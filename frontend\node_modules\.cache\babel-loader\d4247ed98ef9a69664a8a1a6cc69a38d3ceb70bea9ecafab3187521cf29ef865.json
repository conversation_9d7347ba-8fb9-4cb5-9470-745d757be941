{"ast": null, "code": "/**\n *  Addresses are a fundamental part of interacting with Ethereum. They\n *  represent the global identity of Externally Owned Accounts (accounts\n *  backed by a private key) and contracts.\n *\n *  The Ethereum Naming Service (ENS) provides an interconnected ecosystem\n *  of contracts, standards and libraries which enable looking up an\n *  address for an ENS name.\n *\n *  These functions help convert between various formats, validate\n *  addresses and safely resolve ENS names.\n *\n *  @_section: api/address:Addresses  [about-addresses]\n */\nnull;\nexport { getAddress, getIcapAddress } from \"./address.js\";\nexport { getCreateAddress, getCreate2Address } from \"./contract-address.js\";\nexport { isAddressable, isAddress, resolveAddress } from \"./checks.js\";", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "getIcapAddress", "getCreateAddress", "getCreate2Address", "isAddressable", "is<PERSON>dd<PERSON>", "resolve<PERSON>ddress"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\address\\index.ts"], "sourcesContent": ["/**\n *  Addresses are a fundamental part of interacting with Ethereum. They\n *  represent the global identity of Externally Owned Accounts (accounts\n *  backed by a private key) and contracts.\n *\n *  The Ethereum Naming Service (ENS) provides an interconnected ecosystem\n *  of contracts, standards and libraries which enable looking up an\n *  address for an ENS name.\n *\n *  These functions help convert between various formats, validate\n *  addresses and safely resolve ENS names.\n *\n *  @_section: api/address:Addresses  [about-addresses]\n */\n\nnull;\n\n/**\n *  An interface for objects which have an address, and can\n *  resolve it asyncronously.\n *\n *  This allows objects such as [[Signer]] or [[Contract]] to\n *  be used most places an address can be, for example getting\n *  the [balance](Provider-getBalance).\n */\nexport interface Addressable {\n    /**\n     *  Get the object address.\n     */\n    getAddress(): Promise<string>;\n}\n\n/**\n *  Anything that can be used to return or resolve an address.\n */\nexport type AddressLike = string | Promise<string> | Addressable;\n\n/**\n *  An interface for any object which can resolve an ENS name.\n */\nexport interface NameResolver {\n    /**\n     *  Resolve to the address for the ENS %%name%%.\n     *\n     *  Resolves to ``null`` if the name is unconfigued. Use\n     *  [[resolveAddress]] (passing this object as %%resolver%%) to\n     *  throw for names that are unconfigured.\n     */\n    resolveName(name: string): Promise<null | string>;\n}\n\nexport { getAddress, getIcapAddress } from \"./address.js\";\n\nexport { getCreateAddress, getCreate2Address } from \"./contract-address.js\";\n\n\nexport { isAddressable, isAddress, resolveAddress } from \"./checks.js\";\n"], "mappings": "AAAA;;;;;;;;;;;;;;AAeA,IAAI;AAoCJ,SAASA,UAAU,EAAEC,cAAc,QAAQ,cAAc;AAEzD,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,uBAAuB;AAG3E,SAASC,aAAa,EAAEC,SAAS,EAAEC,cAAc,QAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}