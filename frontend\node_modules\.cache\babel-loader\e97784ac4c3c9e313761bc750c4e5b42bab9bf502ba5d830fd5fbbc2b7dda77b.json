{"ast": null, "code": "// Counter Mode\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _CTR_remaining, _CTR_remainingIndex, _CTR_counter;\nimport { ModeOfOperation } from \"./mode.js\";\nexport class CTR extends ModeOfOperation {\n  constructor(key, initialValue) {\n    super(\"CTR\", key, CTR);\n    // Remaining bytes for the one-time pad\n    _CTR_remaining.set(this, void 0);\n    _CTR_remainingIndex.set(this, void 0);\n    // The current counter\n    _CTR_counter.set(this, void 0);\n    __classPrivateFieldSet(this, _CTR_counter, new Uint8Array(16), \"f\");\n    __classPrivateFieldGet(this, _CTR_counter, \"f\").fill(0);\n    __classPrivateFieldSet(this, _CTR_remaining, __classPrivateFieldGet(this, _CTR_counter, \"f\"), \"f\"); // This will be discarded immediately\n    __classPrivateFieldSet(this, _CTR_remainingIndex, 16, \"f\");\n    if (initialValue == null) {\n      initialValue = 1;\n    }\n    if (typeof initialValue === \"number\") {\n      this.setCounterValue(initialValue);\n    } else {\n      this.setCounterBytes(initialValue);\n    }\n  }\n  get counter() {\n    return new Uint8Array(__classPrivateFieldGet(this, _CTR_counter, \"f\"));\n  }\n  setCounterValue(value) {\n    if (!Number.isInteger(value) || value < 0 || value > Number.MAX_SAFE_INTEGER) {\n      throw new TypeError(\"invalid counter initial integer value\");\n    }\n    for (let index = 15; index >= 0; --index) {\n      __classPrivateFieldGet(this, _CTR_counter, \"f\")[index] = value % 256;\n      value = Math.floor(value / 256);\n    }\n  }\n  setCounterBytes(value) {\n    if (value.length !== 16) {\n      throw new TypeError(\"invalid counter initial Uint8Array value length\");\n    }\n    __classPrivateFieldGet(this, _CTR_counter, \"f\").set(value);\n  }\n  increment() {\n    for (let i = 15; i >= 0; i--) {\n      if (__classPrivateFieldGet(this, _CTR_counter, \"f\")[i] === 255) {\n        __classPrivateFieldGet(this, _CTR_counter, \"f\")[i] = 0;\n      } else {\n        __classPrivateFieldGet(this, _CTR_counter, \"f\")[i]++;\n        break;\n      }\n    }\n  }\n  encrypt(plaintext) {\n    var _a, _b;\n    const crypttext = new Uint8Array(plaintext);\n    for (let i = 0; i < crypttext.length; i++) {\n      if (__classPrivateFieldGet(this, _CTR_remainingIndex, \"f\") === 16) {\n        __classPrivateFieldSet(this, _CTR_remaining, this.aes.encrypt(__classPrivateFieldGet(this, _CTR_counter, \"f\")), \"f\");\n        __classPrivateFieldSet(this, _CTR_remainingIndex, 0, \"f\");\n        this.increment();\n      }\n      crypttext[i] ^= __classPrivateFieldGet(this, _CTR_remaining, \"f\")[__classPrivateFieldSet(this, _CTR_remainingIndex, (_b = __classPrivateFieldGet(this, _CTR_remainingIndex, \"f\"), _a = _b++, _b), \"f\"), _a];\n    }\n    return crypttext;\n  }\n  decrypt(ciphertext) {\n    return this.encrypt(ciphertext);\n  }\n}\n_CTR_remaining = new WeakMap(), _CTR_remainingIndex = new WeakMap(), _CTR_counter = new WeakMap();", "map": {"version": 3, "names": ["ModeOfOperation", "CTR", "constructor", "key", "initialValue", "_CTR_remaining", "set", "_CTR_remainingIndex", "_CTR_counter", "__classPrivateFieldSet", "Uint8Array", "__classPrivateFieldGet", "fill", "setCounterValue", "setCounterBytes", "counter", "value", "Number", "isInteger", "MAX_SAFE_INTEGER", "TypeError", "index", "Math", "floor", "length", "increment", "i", "encrypt", "plaintext", "crypttext", "aes", "_b", "_a", "decrypt", "ciphertext"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode-ctr.ts"], "sourcesContent": ["// Counter Mode\n\nimport { ModeOfOperation } from \"./mode.js\";\n\nexport class CTR extends ModeOfOperation {\n\n  // Remaining bytes for the one-time pad\n  #remaining: Uint8Array;\n  #remainingIndex: number;\n\n  // The current counter\n  #counter: Uint8Array;\n\n  constructor(key: Uint8Array, initialValue?: number | Uint8Array) {\n    super(\"CTR\", key, CTR);\n\n    this.#counter = new Uint8Array(16)\n    this.#counter.fill(0);\n\n    this.#remaining = this.#counter;  // This will be discarded immediately\n    this.#remainingIndex = 16;\n\n    if (initialValue == null) { initialValue = 1; }\n\n    if (typeof(initialValue) === \"number\") {\n      this.setCounterValue(initialValue);\n    } else {\n      this.setCounterBytes(initialValue);\n    }\n  }\n\n  get counter(): Uint8Array { return new Uint8Array(this.#counter); }\n\n  setCounterValue(value: number): void {\n    if (!Number.isInteger(value) || value < 0 || value > Number.MAX_SAFE_INTEGER) {\n      throw new TypeError(\"invalid counter initial integer value\");\n    }\n\n    for (let index = 15; index >= 0; --index) {\n      this.#counter[index] = value % 256;\n      value = Math.floor(value / 256);\n    }\n  }\n\n  setCounterBytes(value: Uint8Array): void {\n    if (value.length !== 16) {\n      throw new TypeError(\"invalid counter initial Uint8Array value length\");\n    }\n\n    this.#counter.set(value);\n  }\n\n  increment() {\n    for (let i = 15; i >= 0; i--) {\n      if (this.#counter[i] === 255) {\n        this.#counter[i] = 0;\n      } else {\n        this.#counter[i]++;\n        break;\n      }\n    }\n  }\n\n  encrypt(plaintext: Uint8Array): Uint8Array {\n    const crypttext = new Uint8Array(plaintext);\n\n    for (let i = 0; i < crypttext.length; i++) {\n      if (this.#remainingIndex === 16) {\n        this.#remaining = this.aes.encrypt(this.#counter);\n        this.#remainingIndex = 0;\n        this.increment();\n      }\n      crypttext[i] ^= this.#remaining[this.#remainingIndex++];\n    }\n\n    return crypttext;\n  }\n\n  decrypt(ciphertext: Uint8Array): Uint8Array {\n    return this.encrypt(ciphertext);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAEA,SAASA,eAAe,QAAQ,WAAW;AAE3C,OAAM,MAAOC,GAAI,SAAQD,eAAe;EAStCE,YAAYC,GAAe,EAAEC,YAAkC;IAC7D,KAAK,CAAC,KAAK,EAAED,GAAG,EAAEF,GAAG,CAAC;IARxB;IACAI,cAAA,CAAAC,GAAA;IACAC,mBAAA,CAAAD,GAAA;IAEA;IACAE,YAAA,CAAAF,GAAA;IAKEG,sBAAA,KAAI,EAAAD,YAAA,EAAY,IAAIE,UAAU,CAAC,EAAE,CAAC;IAClCC,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACI,IAAI,CAAC,CAAC,CAAC;IAErBH,sBAAA,KAAI,EAAAJ,cAAA,EAAcM,sBAAA,KAAI,EAAAH,YAAA,MAAS,OAAC,CAAE;IAClCC,sBAAA,KAAI,EAAAF,mBAAA,EAAmB,EAAE;IAEzB,IAAIH,YAAY,IAAI,IAAI,EAAE;MAAEA,YAAY,GAAG,CAAC;;IAE5C,IAAI,OAAOA,YAAa,KAAK,QAAQ,EAAE;MACrC,IAAI,CAACS,eAAe,CAACT,YAAY,CAAC;KACnC,MAAM;MACL,IAAI,CAACU,eAAe,CAACV,YAAY,CAAC;;EAEtC;EAEA,IAAIW,OAAOA,CAAA;IAAiB,OAAO,IAAIL,UAAU,CAACC,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAAC;EAAE;EAElEK,eAAeA,CAACG,KAAa;IAC3B,IAAI,CAACC,MAAM,CAACC,SAAS,CAACF,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGC,MAAM,CAACE,gBAAgB,EAAE;MAC5E,MAAM,IAAIC,SAAS,CAAC,uCAAuC,CAAC;;IAG9D,KAAK,IAAIC,KAAK,GAAG,EAAE,EAAEA,KAAK,IAAI,CAAC,EAAE,EAAEA,KAAK,EAAE;MACxCV,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACa,KAAK,CAAC,GAAGL,KAAK,GAAG,GAAG;MAClCA,KAAK,GAAGM,IAAI,CAACC,KAAK,CAACP,KAAK,GAAG,GAAG,CAAC;;EAEnC;EAEAF,eAAeA,CAACE,KAAiB;IAC/B,IAAIA,KAAK,CAACQ,MAAM,KAAK,EAAE,EAAE;MACvB,MAAM,IAAIJ,SAAS,CAAC,iDAAiD,CAAC;;IAGxET,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACF,GAAG,CAACU,KAAK,CAAC;EAC1B;EAEAS,SAASA,CAAA;IACP,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5B,IAAIf,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACkB,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5Bf,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACkB,CAAC,CAAC,GAAG,CAAC;OACrB,MAAM;QACLf,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAACkB,CAAC,CAAC,EAAE;QAClB;;;EAGN;EAEAC,OAAOA,CAACC,SAAqB;;IAC3B,MAAMC,SAAS,GAAG,IAAInB,UAAU,CAACkB,SAAS,CAAC;IAE3C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,SAAS,CAACL,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzC,IAAIf,sBAAA,KAAI,EAAAJ,mBAAA,MAAgB,KAAK,EAAE,EAAE;QAC/BE,sBAAA,KAAI,EAAAJ,cAAA,EAAc,IAAI,CAACyB,GAAG,CAACH,OAAO,CAAChB,sBAAA,KAAI,EAAAH,YAAA,MAAS,CAAC;QACjDC,sBAAA,KAAI,EAAAF,mBAAA,EAAmB,CAAC;QACxB,IAAI,CAACkB,SAAS,EAAE;;MAElBI,SAAS,CAACH,CAAC,CAAC,IAAIf,sBAAA,KAAI,EAAAN,cAAA,MAAW,CAACI,sBAAA,OAAAF,mBAAA,GAAAwB,EAAA,GAAApB,sBAAA,OAAAJ,mBAAA,MAAoB,EAApByB,EAAA,GAAAD,EAAA,EAAsB,EAAAA,EAAA,SAAAC,EAAA,CAAC;;IAGzD,OAAOH,SAAS;EAClB;EAEAI,OAAOA,CAACC,UAAsB;IAC5B,OAAO,IAAI,CAACP,OAAO,CAACO,UAAU,CAAC;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}