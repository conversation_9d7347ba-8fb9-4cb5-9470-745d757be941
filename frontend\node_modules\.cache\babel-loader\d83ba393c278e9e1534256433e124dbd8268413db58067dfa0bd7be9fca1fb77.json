{"ast": null, "code": "// Use the encode-latin.js script to create the necessary\n// data files to be consumed by this class\nimport { id } from \"../hash/index.js\";\nimport { assertArgument } from \"../utils/index.js\";\nimport { decodeOwl } from \"./decode-owl.js\";\nimport { Wordlist } from \"./wordlist.js\";\n/**\n *  An OWL format Wordlist is an encoding method that exploits\n *  the general locality of alphabetically sorted words to\n *  achieve a simple but effective means of compression.\n *\n *  This class is generally not useful to most developers as\n *  it is used mainly internally to keep Wordlists for languages\n *  based on ASCII-7 small.\n *\n *  If necessary, there are tools within the ``generation/`` folder\n *  to create the necessary data.\n */\nexport class WordlistOwl extends Wordlist {\n  #data;\n  #checksum;\n  /**\n   *  Creates a new Wordlist for %%locale%% using the OWL %%data%%\n   *  and validated against the %%checksum%%.\n   */\n  constructor(locale, data, checksum) {\n    super(locale);\n    this.#data = data;\n    this.#checksum = checksum;\n    this.#words = null;\n  }\n  /**\n   *  The OWL-encoded data.\n   */\n  get _data() {\n    return this.#data;\n  }\n  /**\n   *  Decode all the words for the wordlist.\n   */\n  _decodeWords() {\n    return decodeOwl(this.#data);\n  }\n  #words;\n  #loadWords() {\n    if (this.#words == null) {\n      const words = this._decodeWords();\n      // Verify the computed list matches the official list\n      const checksum = id(words.join(\"\\n\") + \"\\n\");\n      /* c8 ignore start */\n      if (checksum !== this.#checksum) {\n        throw new Error(`BIP39 Wordlist for ${this.locale} FAILED`);\n      }\n      /* c8 ignore stop */\n      this.#words = words;\n    }\n    return this.#words;\n  }\n  getWord(index) {\n    const words = this.#loadWords();\n    assertArgument(index >= 0 && index < words.length, `invalid word index: ${index}`, \"index\", index);\n    return words[index];\n  }\n  getWordIndex(word) {\n    return this.#loadWords().indexOf(word);\n  }\n}", "map": {"version": 3, "names": ["id", "assertArgument", "decodeOwl", "Wordlist", "WordlistOwl", "data", "checksum", "constructor", "locale", "words", "_data", "_decodeWords", "loadWords", "#loadWords", "join", "Error", "getWord", "index", "length", "getWordIndex", "word", "indexOf"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\wordlist-owl.ts"], "sourcesContent": ["\n// Use the encode-latin.js script to create the necessary\n// data files to be consumed by this class\n\nimport { id } from \"../hash/index.js\";\nimport { assertArgument } from \"../utils/index.js\";\n\nimport { decodeOwl } from \"./decode-owl.js\";\nimport { Wordlist } from \"./wordlist.js\";\n\n/**\n *  An OWL format Wordlist is an encoding method that exploits\n *  the general locality of alphabetically sorted words to\n *  achieve a simple but effective means of compression.\n *\n *  This class is generally not useful to most developers as\n *  it is used mainly internally to keep Wordlists for languages\n *  based on ASCII-7 small.\n *\n *  If necessary, there are tools within the ``generation/`` folder\n *  to create the necessary data.\n */\nexport class WordlistOwl extends Wordlist {\n    #data: string;\n    #checksum: string;\n\n    /**\n     *  Creates a new Wordlist for %%locale%% using the OWL %%data%%\n     *  and validated against the %%checksum%%.\n     */\n    constructor(locale: string, data: string, checksum: string) {\n        super(locale);\n        this.#data = data;\n        this.#checksum = checksum;\n        this.#words = null;\n    }\n\n    /**\n     *  The OWL-encoded data.\n     */\n    get _data(): string { return this.#data; }\n\n    /**\n     *  Decode all the words for the wordlist.\n     */\n    _decodeWords(): Array<string> {\n        return decodeOwl(this.#data);\n    }\n\n    #words: null | Array<string>;\n    #loadWords(): Array<string> {\n        if (this.#words == null) {\n            const words = this._decodeWords();\n\n            // Verify the computed list matches the official list\n            const checksum = id(words.join(\"\\n\") + \"\\n\");\n            /* c8 ignore start */\n            if (checksum !== this.#checksum) {\n                throw new Error(`BIP39 Wordlist for ${ this.locale } FAILED`);\n            }\n            /* c8 ignore stop */\n\n            this.#words = words;\n        }\n        return this.#words;\n    }\n\n    getWord(index: number): string {\n        const words = this.#loadWords();\n        assertArgument(index >= 0 && index < words.length, `invalid word index: ${ index }`, \"index\", index);\n        return words[index];\n    }\n\n    getWordIndex(word: string): number {\n        return this.#loadWords().indexOf(word);\n    }\n}\n"], "mappings": "AACA;AACA;AAEA,SAASA,EAAE,QAAQ,kBAAkB;AACrC,SAASC,cAAc,QAAQ,mBAAmB;AAElD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,QAAQ,QAAQ,eAAe;AAExC;;;;;;;;;;;;AAYA,OAAM,MAAOC,WAAY,SAAQD,QAAQ;EACrC,CAAAE,IAAK;EACL,CAAAC,QAAS;EAET;;;;EAIAC,YAAYC,MAAc,EAAEH,IAAY,EAAEC,QAAgB;IACtD,KAAK,CAACE,MAAM,CAAC;IACb,IAAI,CAAC,CAAAH,IAAK,GAAGA,IAAI;IACjB,IAAI,CAAC,CAAAC,QAAS,GAAGA,QAAQ;IACzB,IAAI,CAAC,CAAAG,KAAM,GAAG,IAAI;EACtB;EAEA;;;EAGA,IAAIC,KAAKA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAL,IAAK;EAAE;EAEzC;;;EAGAM,YAAYA,CAAA;IACR,OAAOT,SAAS,CAAC,IAAI,CAAC,CAAAG,IAAK,CAAC;EAChC;EAEA,CAAAI,KAAM;EACN,CAAAG,SAAUC,CAAA;IACN,IAAI,IAAI,CAAC,CAAAJ,KAAM,IAAI,IAAI,EAAE;MACrB,MAAMA,KAAK,GAAG,IAAI,CAACE,YAAY,EAAE;MAEjC;MACA,MAAML,QAAQ,GAAGN,EAAE,CAACS,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;MAC5C;MACA,IAAIR,QAAQ,KAAK,IAAI,CAAC,CAAAA,QAAS,EAAE;QAC7B,MAAM,IAAIS,KAAK,CAAC,sBAAuB,IAAI,CAACP,MAAO,SAAS,CAAC;;MAEjE;MAEA,IAAI,CAAC,CAAAC,KAAM,GAAGA,KAAK;;IAEvB,OAAO,IAAI,CAAC,CAAAA,KAAM;EACtB;EAEAO,OAAOA,CAACC,KAAa;IACjB,MAAMR,KAAK,GAAG,IAAI,CAAC,CAAAG,SAAU,EAAE;IAC/BX,cAAc,CAACgB,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGR,KAAK,CAACS,MAAM,EAAE,uBAAwBD,KAAM,EAAE,EAAE,OAAO,EAAEA,KAAK,CAAC;IACpG,OAAOR,KAAK,CAACQ,KAAK,CAAC;EACvB;EAEAE,YAAYA,CAACC,IAAY;IACrB,OAAO,IAAI,CAAC,CAAAR,SAAU,EAAE,CAACS,OAAO,CAACD,IAAI,CAAC;EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}