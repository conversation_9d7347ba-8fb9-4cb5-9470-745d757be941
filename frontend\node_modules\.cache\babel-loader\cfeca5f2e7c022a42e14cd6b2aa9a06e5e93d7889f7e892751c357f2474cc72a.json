{"ast": null, "code": "import { getAddress, resolveAddress } from \"../address/index.js\";\nimport { hashAuthorization, hashMessage, TypedDataEncoder } from \"../hash/index.js\";\nimport { AbstractSigner, copyRequest } from \"../providers/index.js\";\nimport { computeAddress, Transaction } from \"../transaction/index.js\";\nimport { defineProperties, getBigInt, resolveProperties, assert, assertArgument } from \"../utils/index.js\";\n/**\n *  The **BaseWallet** is a stream-lined implementation of a\n *  [[Signer]] that operates with a private key.\n *\n *  It is preferred to use the [[Wallet]] class, as it offers\n *  additional functionality and simplifies loading a variety\n *  of JSON formats, Mnemonic Phrases, etc.\n *\n *  This class may be of use for those attempting to implement\n *  a minimal Signer.\n */\nexport class BaseWallet extends AbstractSigner {\n  /**\n   *  The wallet address.\n   */\n  address;\n  #signingKey;\n  /**\n   *  Creates a new BaseWallet for %%privateKey%%, optionally\n   *  connected to %%provider%%.\n   *\n   *  If %%provider%% is not specified, only offline methods can\n   *  be used.\n   */\n  constructor(privateKey, provider) {\n    super(provider);\n    assertArgument(privateKey && typeof privateKey.sign === \"function\", \"invalid private key\", \"privateKey\", \"[ REDACTED ]\");\n    this.#signingKey = privateKey;\n    const address = computeAddress(this.signingKey.publicKey);\n    defineProperties(this, {\n      address\n    });\n  }\n  // Store private values behind getters to reduce visibility\n  // in console.log\n  /**\n   *  The [[SigningKey]] used for signing payloads.\n   */\n  get signingKey() {\n    return this.#signingKey;\n  }\n  /**\n   *  The private key for this wallet.\n   */\n  get privateKey() {\n    return this.signingKey.privateKey;\n  }\n  async getAddress() {\n    return this.address;\n  }\n  connect(provider) {\n    return new BaseWallet(this.#signingKey, provider);\n  }\n  async signTransaction(tx) {\n    tx = copyRequest(tx);\n    // Replace any Addressable or ENS name with an address\n    const {\n      to,\n      from\n    } = await resolveProperties({\n      to: tx.to ? resolveAddress(tx.to, this) : undefined,\n      from: tx.from ? resolveAddress(tx.from, this) : undefined\n    });\n    if (to != null) {\n      tx.to = to;\n    }\n    if (from != null) {\n      tx.from = from;\n    }\n    if (tx.from != null) {\n      assertArgument(getAddress(tx.from) === this.address, \"transaction from address mismatch\", \"tx.from\", tx.from);\n      delete tx.from;\n    }\n    // Build the transaction\n    const btx = Transaction.from(tx);\n    btx.signature = this.signingKey.sign(btx.unsignedHash);\n    return btx.serialized;\n  }\n  async signMessage(message) {\n    return this.signMessageSync(message);\n  }\n  // @TODO: Add a secialized signTx and signTyped sync that enforces\n  // all parameters are known?\n  /**\n   *  Returns the signature for %%message%% signed with this wallet.\n   */\n  signMessageSync(message) {\n    return this.signingKey.sign(hashMessage(message)).serialized;\n  }\n  /**\n   *  Returns the Authorization for %%auth%%.\n   */\n  authorizeSync(auth) {\n    assertArgument(typeof auth.address === \"string\", \"invalid address for authorizeSync\", \"auth.address\", auth);\n    const signature = this.signingKey.sign(hashAuthorization(auth));\n    return Object.assign({}, {\n      address: getAddress(auth.address),\n      nonce: getBigInt(auth.nonce || 0),\n      chainId: getBigInt(auth.chainId || 0)\n    }, {\n      signature\n    });\n  }\n  /**\n   *  Resolves to the Authorization for %%auth%%.\n   */\n  async authorize(auth) {\n    auth = Object.assign({}, auth, {\n      address: await resolveAddress(auth.address, this)\n    });\n    return this.authorizeSync(await this.populateAuthorization(auth));\n  }\n  async signTypedData(domain, types, value) {\n    // Populate any ENS names\n    const populated = await TypedDataEncoder.resolveNames(domain, types, value, async name => {\n      // @TODO: this should use resolveName; addresses don't\n      //        need a provider\n      assert(this.provider != null, \"cannot resolve ENS names without a provider\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"resolveName\",\n        info: {\n          name\n        }\n      });\n      const address = await this.provider.resolveName(name);\n      assert(address != null, \"unconfigured ENS name\", \"UNCONFIGURED_NAME\", {\n        value: name\n      });\n      return address;\n    });\n    return this.signingKey.sign(TypedDataEncoder.hash(populated.domain, types, populated.value)).serialized;\n  }\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "resolve<PERSON>ddress", "hashAuthorization", "hashMessage", "TypedDataEncoder", "Abstract<PERSON><PERSON><PERSON>", "copyRequest", "computeAddress", "Transaction", "defineProperties", "getBigInt", "resolveProperties", "assert", "assertArgument", "BaseWallet", "address", "<PERSON><PERSON><PERSON>", "constructor", "privateKey", "provider", "sign", "public<PERSON>ey", "connect", "signTransaction", "tx", "to", "from", "undefined", "btx", "signature", "unsignedHash", "serialized", "signMessage", "message", "signMessageSync", "authorizeSync", "auth", "Object", "assign", "nonce", "chainId", "authorize", "populateAuthorization", "signTypedData", "domain", "types", "value", "populated", "resolveNames", "name", "operation", "info", "resolveName", "hash"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\base-wallet.ts"], "sourcesContent": ["import { getAddress, resolveAddress } from \"../address/index.js\";\nimport {\n    hashAuthorization, hashMessage, TypedDataEncoder\n} from \"../hash/index.js\";\nimport { AbstractSigner, copyRequest } from \"../providers/index.js\";\nimport { computeAddress, Transaction } from \"../transaction/index.js\";\nimport {\n    defineProperties, getBigInt, resolveProperties, assert, assertArgument\n} from \"../utils/index.js\";\n\nimport type { SigningKey } from \"../crypto/index.js\";\nimport type {\n    AuthorizationRequest, TypedDataDomain, TypedDataField\n} from \"../hash/index.js\";\nimport type { Provider, TransactionRequest } from \"../providers/index.js\";\nimport type { Authorization, TransactionLike } from \"../transaction/index.js\";\n\n\n/**\n *  The **BaseWallet** is a stream-lined implementation of a\n *  [[Signer]] that operates with a private key.\n *\n *  It is preferred to use the [[Wallet]] class, as it offers\n *  additional functionality and simplifies loading a variety\n *  of JSON formats, Mnemonic Phrases, etc.\n *\n *  This class may be of use for those attempting to implement\n *  a minimal Signer.\n */\nexport class BaseWallet extends AbstractSigner {\n    /**\n     *  The wallet address.\n     */\n    readonly address!: string;\n\n    readonly #signingKey: SigningKey;\n\n    /**\n     *  Creates a new BaseWallet for %%privateKey%%, optionally\n     *  connected to %%provider%%.\n     *\n     *  If %%provider%% is not specified, only offline methods can\n     *  be used.\n     */\n    constructor(privateKey: SigningKey, provider?: null | Provider) {\n        super(provider);\n\n        assertArgument(privateKey && typeof(privateKey.sign) === \"function\", \"invalid private key\", \"privateKey\", \"[ REDACTED ]\");\n\n        this.#signingKey = privateKey;\n\n        const address = computeAddress(this.signingKey.publicKey);\n        defineProperties<BaseWallet>(this, { address });\n    }\n\n    // Store private values behind getters to reduce visibility\n    // in console.log\n\n    /**\n     *  The [[SigningKey]] used for signing payloads.\n     */\n    get signingKey(): SigningKey { return this.#signingKey; }\n\n    /**\n     *  The private key for this wallet.\n     */\n    get privateKey(): string { return this.signingKey.privateKey; }\n\n    async getAddress(): Promise<string> { return this.address; }\n\n    connect(provider: null | Provider): BaseWallet {\n        return new BaseWallet(this.#signingKey, provider);\n    }\n\n    async signTransaction(tx: TransactionRequest): Promise<string> {\n        tx = copyRequest(tx);\n\n        // Replace any Addressable or ENS name with an address\n        const { to, from } = await resolveProperties({\n            to: (tx.to ? resolveAddress(tx.to, this): undefined),\n            from: (tx.from ? resolveAddress(tx.from, this): undefined)\n        });\n\n        if (to != null) { tx.to = to; }\n        if (from != null) { tx.from = from; }\n\n        if (tx.from != null) {\n            assertArgument(getAddress(<string>(tx.from)) === this.address,\n                \"transaction from address mismatch\", \"tx.from\", tx.from);\n            delete tx.from;\n        }\n\n        // Build the transaction\n        const btx = Transaction.from(<TransactionLike<string>>tx);\n        btx.signature = this.signingKey.sign(btx.unsignedHash);\n\n        return btx.serialized;\n    }\n\n    async signMessage(message: string | Uint8Array): Promise<string> {\n        return this.signMessageSync(message);\n    }\n\n    // @TODO: Add a secialized signTx and signTyped sync that enforces\n    // all parameters are known?\n    /**\n     *  Returns the signature for %%message%% signed with this wallet.\n     */\n    signMessageSync(message: string | Uint8Array): string {\n        return this.signingKey.sign(hashMessage(message)).serialized;\n    }\n\n    /**\n     *  Returns the Authorization for %%auth%%.\n     */\n    authorizeSync(auth: AuthorizationRequest): Authorization {\n        assertArgument(typeof(auth.address) === \"string\",\n          \"invalid address for authorizeSync\", \"auth.address\", auth);\n\n        const signature = this.signingKey.sign(hashAuthorization(auth));\n        return Object.assign({ }, {\n            address: getAddress(auth.address),\n            nonce: getBigInt(auth.nonce || 0),\n            chainId: getBigInt(auth.chainId || 0),\n        }, { signature });\n    }\n\n    /**\n     *  Resolves to the Authorization for %%auth%%.\n     */\n    async authorize(auth: AuthorizationRequest): Promise<Authorization> {\n        auth = Object.assign({ }, auth, {\n            address: await resolveAddress(auth.address, this)\n        });\n        return this.authorizeSync(await this.populateAuthorization(auth));\n    }\n\n    async signTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): Promise<string> {\n\n        // Populate any ENS names\n        const populated = await TypedDataEncoder.resolveNames(domain, types, value, async (name: string) => {\n            // @TODO: this should use resolveName; addresses don't\n            //        need a provider\n\n            assert(this.provider != null, \"cannot resolve ENS names without a provider\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"resolveName\",\n                info: { name }\n            });\n\n            const address = await this.provider.resolveName(name);\n            assert(address != null, \"unconfigured ENS name\", \"UNCONFIGURED_NAME\", {\n                value: name\n            });\n\n            return address;\n        });\n\n        return this.signingKey.sign(TypedDataEncoder.hash(populated.domain, types, populated.value)).serialized;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;AAChE,SACIC,iBAAiB,EAAEC,WAAW,EAAEC,gBAAgB,QAC7C,kBAAkB;AACzB,SAASC,cAAc,EAAEC,WAAW,QAAQ,uBAAuB;AACnE,SAASC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACrE,SACIC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,cAAc,QACnE,mBAAmB;AAU1B;;;;;;;;;;;AAWA,OAAM,MAAOC,UAAW,SAAQT,cAAc;EAC1C;;;EAGSU,OAAO;EAEP,CAAAC,UAAW;EAEpB;;;;;;;EAOAC,YAAYC,UAAsB,EAAEC,QAA0B;IAC1D,KAAK,CAACA,QAAQ,CAAC;IAEfN,cAAc,CAACK,UAAU,IAAI,OAAOA,UAAU,CAACE,IAAK,KAAK,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,cAAc,CAAC;IAEzH,IAAI,CAAC,CAAAJ,UAAW,GAAGE,UAAU;IAE7B,MAAMH,OAAO,GAAGR,cAAc,CAAC,IAAI,CAACS,UAAU,CAACK,SAAS,CAAC;IACzDZ,gBAAgB,CAAa,IAAI,EAAE;MAAEM;IAAO,CAAE,CAAC;EACnD;EAEA;EACA;EAEA;;;EAGA,IAAIC,UAAUA,CAAA;IAAiB,OAAO,IAAI,CAAC,CAAAA,UAAW;EAAE;EAExD;;;EAGA,IAAIE,UAAUA,CAAA;IAAa,OAAO,IAAI,CAACF,UAAU,CAACE,UAAU;EAAE;EAE9D,MAAMlB,UAAUA,CAAA;IAAsB,OAAO,IAAI,CAACe,OAAO;EAAE;EAE3DO,OAAOA,CAACH,QAAyB;IAC7B,OAAO,IAAIL,UAAU,CAAC,IAAI,CAAC,CAAAE,UAAW,EAAEG,QAAQ,CAAC;EACrD;EAEA,MAAMI,eAAeA,CAACC,EAAsB;IACxCA,EAAE,GAAGlB,WAAW,CAACkB,EAAE,CAAC;IAEpB;IACA,MAAM;MAAEC,EAAE;MAAEC;IAAI,CAAE,GAAG,MAAMf,iBAAiB,CAAC;MACzCc,EAAE,EAAGD,EAAE,CAACC,EAAE,GAAGxB,cAAc,CAACuB,EAAE,CAACC,EAAE,EAAE,IAAI,CAAC,GAAEE,SAAU;MACpDD,IAAI,EAAGF,EAAE,CAACE,IAAI,GAAGzB,cAAc,CAACuB,EAAE,CAACE,IAAI,EAAE,IAAI,CAAC,GAAEC;KACnD,CAAC;IAEF,IAAIF,EAAE,IAAI,IAAI,EAAE;MAAED,EAAE,CAACC,EAAE,GAAGA,EAAE;;IAC5B,IAAIC,IAAI,IAAI,IAAI,EAAE;MAAEF,EAAE,CAACE,IAAI,GAAGA,IAAI;;IAElC,IAAIF,EAAE,CAACE,IAAI,IAAI,IAAI,EAAE;MACjBb,cAAc,CAACb,UAAU,CAAUwB,EAAE,CAACE,IAAK,CAAC,KAAK,IAAI,CAACX,OAAO,EACzD,mCAAmC,EAAE,SAAS,EAAES,EAAE,CAACE,IAAI,CAAC;MAC5D,OAAOF,EAAE,CAACE,IAAI;;IAGlB;IACA,MAAME,GAAG,GAAGpB,WAAW,CAACkB,IAAI,CAA0BF,EAAE,CAAC;IACzDI,GAAG,CAACC,SAAS,GAAG,IAAI,CAACb,UAAU,CAACI,IAAI,CAACQ,GAAG,CAACE,YAAY,CAAC;IAEtD,OAAOF,GAAG,CAACG,UAAU;EACzB;EAEA,MAAMC,WAAWA,CAACC,OAA4B;IAC1C,OAAO,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;EACxC;EAEA;EACA;EACA;;;EAGAC,eAAeA,CAACD,OAA4B;IACxC,OAAO,IAAI,CAACjB,UAAU,CAACI,IAAI,CAACjB,WAAW,CAAC8B,OAAO,CAAC,CAAC,CAACF,UAAU;EAChE;EAEA;;;EAGAI,aAAaA,CAACC,IAA0B;IACpCvB,cAAc,CAAC,OAAOuB,IAAI,CAACrB,OAAQ,KAAK,QAAQ,EAC9C,mCAAmC,EAAE,cAAc,EAAEqB,IAAI,CAAC;IAE5D,MAAMP,SAAS,GAAG,IAAI,CAACb,UAAU,CAACI,IAAI,CAAClB,iBAAiB,CAACkC,IAAI,CAAC,CAAC;IAC/D,OAAOC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE;MACtBvB,OAAO,EAAEf,UAAU,CAACoC,IAAI,CAACrB,OAAO,CAAC;MACjCwB,KAAK,EAAE7B,SAAS,CAAC0B,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;MACjCC,OAAO,EAAE9B,SAAS,CAAC0B,IAAI,CAACI,OAAO,IAAI,CAAC;KACvC,EAAE;MAAEX;IAAS,CAAE,CAAC;EACrB;EAEA;;;EAGA,MAAMY,SAASA,CAACL,IAA0B;IACtCA,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEF,IAAI,EAAE;MAC5BrB,OAAO,EAAE,MAAMd,cAAc,CAACmC,IAAI,CAACrB,OAAO,EAAE,IAAI;KACnD,CAAC;IACF,OAAO,IAAI,CAACoB,aAAa,CAAC,MAAM,IAAI,CAACO,qBAAqB,CAACN,IAAI,CAAC,CAAC;EACrE;EAEA,MAAMO,aAAaA,CAACC,MAAuB,EAAEC,KAA4C,EAAEC,KAA0B;IAEjH;IACA,MAAMC,SAAS,GAAG,MAAM3C,gBAAgB,CAAC4C,YAAY,CAACJ,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE,MAAOG,IAAY,IAAI;MAC/F;MACA;MAEArC,MAAM,CAAC,IAAI,CAACO,QAAQ,IAAI,IAAI,EAAE,6CAA6C,EAAE,uBAAuB,EAAE;QAClG+B,SAAS,EAAE,aAAa;QACxBC,IAAI,EAAE;UAAEF;QAAI;OACf,CAAC;MAEF,MAAMlC,OAAO,GAAG,MAAM,IAAI,CAACI,QAAQ,CAACiC,WAAW,CAACH,IAAI,CAAC;MACrDrC,MAAM,CAACG,OAAO,IAAI,IAAI,EAAE,uBAAuB,EAAE,mBAAmB,EAAE;QAClE+B,KAAK,EAAEG;OACV,CAAC;MAEF,OAAOlC,OAAO;IAClB,CAAC,CAAC;IAEF,OAAO,IAAI,CAACC,UAAU,CAACI,IAAI,CAAChB,gBAAgB,CAACiD,IAAI,CAACN,SAAS,CAACH,MAAM,EAAEC,KAAK,EAAEE,SAAS,CAACD,KAAK,CAAC,CAAC,CAACf,UAAU;EAC3G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}