import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { WalletIcon, ShieldCheckIcon, CurrencyDollarIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const Login = () => {
  const { login, isAuthenticated, loading } = useAuth();
  const [isConnecting, setIsConnecting] = useState(false);
  const location = useLocation();

  // Redirect if already authenticated
  const from = location.state?.from?.pathname || '/dashboard';
  
  useEffect(() => {
    if (isAuthenticated) {
      // Small delay to show success message
      setTimeout(() => {
        window.location.href = from;
      }, 1000);
    }
  }, [isAuthenticated, from]);

  const handleConnect = async () => {
    if (!window.ethereum) {
      toast.error('MetaMask is not installed. Please install MetaMask to continue.');
      return;
    }

    setIsConnecting(true);
    try {
      const success = await login();
      if (success) {
        toast.success('Successfully connected! Redirecting...');
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  const features = [
    {
      icon: WalletIcon,
      title: 'Secure Wallet Connection',
      description: 'Connect your MetaMask wallet securely using Web3 authentication.',
    },
    {
      icon: ShieldCheckIcon,
      title: 'Own Your Progress',
      description: 'Your heroes, progress, and earnings are stored on the blockchain.',
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Earn Real Crypto',
      description: 'Battle and earn CQT tokens that you can trade or stake for rewards.',
    },
  ];

  return (
    <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">CQ</span>
          </div>
          <h2 className="text-3xl font-bold text-white mb-2">
            Welcome to CryptoQuest
          </h2>
          <p className="text-dark-300">
            Connect your wallet to start your adventure
          </p>
        </div>

        <div className="game-card p-8">
          <div className="space-y-6">
            {/* MetaMask Connection */}
            <div className="text-center">
              <button
                onClick={handleConnect}
                disabled={isConnecting || loading}
                className="w-full game-button py-4 text-lg flex items-center justify-center space-x-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isConnecting ? (
                  <>
                    <div className="loading-spinner"></div>
                    <span>Connecting...</span>
                  </>
                ) : (
                  <>
                    <WalletIcon className="w-6 h-6" />
                    <span>Connect MetaMask</span>
                  </>
                )}
              </button>
            </div>

            {/* Instructions */}
            <div className="text-sm text-dark-400 space-y-2">
              <p className="font-medium text-white">How to connect:</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Make sure MetaMask is installed</li>
                <li>Click "Connect MetaMask" above</li>
                <li>Approve the connection in MetaMask</li>
                <li>Sign the authentication message</li>
              </ol>
            </div>

            {/* Network Info */}
            <div className="bg-dark-700 rounded-lg p-4">
              <h4 className="font-medium text-white mb-2">Network Information</h4>
              <div className="text-sm text-dark-300 space-y-1">
                <p><span className="text-white">Network:</span> Polygon (MATIC)</p>
                <p><span className="text-white">Token:</span> CQT (CryptoQuest Token)</p>
                <p><span className="text-white">Chain ID:</span> 137</p>
              </div>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-white text-center">Why Connect?</h3>
          <div className="space-y-3">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-dark-800/50 rounded-lg">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <feature.icon className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-white text-sm">{feature.title}</h4>
                  <p className="text-dark-400 text-xs">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Help */}
        <div className="text-center text-sm text-dark-400">
          <p>
            Don't have MetaMask?{' '}
            <a
              href="https://metamask.io/download/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary-400 hover:text-primary-300"
            >
              Download it here
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
