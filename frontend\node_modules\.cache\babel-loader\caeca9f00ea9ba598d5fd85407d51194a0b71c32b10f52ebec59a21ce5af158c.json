{"ast": null, "code": "// Electronic Code Book\nimport { ModeOfOperation } from \"./mode.js\";\nexport class ECB extends ModeOfOperation {\n  constructor(key) {\n    super(\"ECB\", key, ECB);\n  }\n  encrypt(plaintext) {\n    if (plaintext.length % 16) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n    const crypttext = new Uint8Array(plaintext.length);\n    for (let i = 0; i < plaintext.length; i += 16) {\n      crypttext.set(this.aes.encrypt(plaintext.subarray(i, i + 16)), i);\n    }\n    return crypttext;\n  }\n  decrypt(crypttext) {\n    if (crypttext.length % 16) {\n      throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n    const plaintext = new Uint8Array(crypttext.length);\n    for (let i = 0; i < crypttext.length; i += 16) {\n      plaintext.set(this.aes.decrypt(crypttext.subarray(i, i + 16)), i);\n    }\n    return plaintext;\n  }\n}", "map": {"version": 3, "names": ["ModeOfOperation", "ECB", "constructor", "key", "encrypt", "plaintext", "length", "TypeError", "crypttext", "Uint8Array", "i", "set", "aes", "subarray", "decrypt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode-ecb.ts"], "sourcesContent": ["// Electronic Code Book\n\nimport { ModeOfOperation } from \"./mode.js\";\n\nexport class ECB extends ModeOfOperation {\n\n  constructor(key: Uint8Array) {\n    super(\"ECB\", key, ECB);\n  }\n\n  encrypt(plaintext: Uint8Array): Uint8Array {\n    if (plaintext.length % 16) {\n        throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n\n    const crypttext = new Uint8Array(plaintext.length);\n    for (let i = 0; i < plaintext.length; i += 16) {\n        crypttext.set(this.aes.encrypt(plaintext.subarray(i, i + 16)), i);\n    }\n\n    return crypttext;\n  }\n\n  decrypt(crypttext: Uint8Array): Uint8Array {\n    if (crypttext.length % 16) {\n        throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n\n    const plaintext = new Uint8Array(crypttext.length);\n    for (let i = 0; i < crypttext.length; i += 16) {\n        plaintext.set(this.aes.decrypt(crypttext.subarray(i, i + 16)), i);\n    }\n\n    return plaintext;\n  }\n}\n"], "mappings": "AAAA;AAEA,SAASA,eAAe,QAAQ,WAAW;AAE3C,OAAM,MAAOC,GAAI,SAAQD,eAAe;EAEtCE,YAAYC,GAAe;IACzB,KAAK,CAAC,KAAK,EAAEA,GAAG,EAAEF,GAAG,CAAC;EACxB;EAEAG,OAAOA,CAACC,SAAqB;IAC3B,IAAIA,SAAS,CAACC,MAAM,GAAG,EAAE,EAAE;MACvB,MAAM,IAAIC,SAAS,CAAC,uDAAuD,CAAC;;IAGhF,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACJ,SAAS,CAACC,MAAM,CAAC;IAClD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACC,MAAM,EAAEI,CAAC,IAAI,EAAE,EAAE;MAC3CF,SAAS,CAACG,GAAG,CAAC,IAAI,CAACC,GAAG,CAACR,OAAO,CAACC,SAAS,CAACQ,QAAQ,CAACH,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC,EAAEA,CAAC,CAAC;;IAGrE,OAAOF,SAAS;EAClB;EAEAM,OAAOA,CAACN,SAAqB;IAC3B,IAAIA,SAAS,CAACF,MAAM,GAAG,EAAE,EAAE;MACvB,MAAM,IAAIC,SAAS,CAAC,wDAAwD,CAAC;;IAGjF,MAAMF,SAAS,GAAG,IAAII,UAAU,CAACD,SAAS,CAACF,MAAM,CAAC;IAClD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACF,MAAM,EAAEI,CAAC,IAAI,EAAE,EAAE;MAC3CL,SAAS,CAACM,GAAG,CAAC,IAAI,CAACC,GAAG,CAACE,OAAO,CAACN,SAAS,CAACK,QAAQ,CAACH,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC,EAAEA,CAAC,CAAC;;IAGrE,OAAOL,SAAS;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}