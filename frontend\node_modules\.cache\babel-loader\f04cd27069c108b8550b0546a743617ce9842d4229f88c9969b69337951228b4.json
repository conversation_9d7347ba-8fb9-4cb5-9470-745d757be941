{"ast": null, "code": "/**\n *  A **Cryptographically Secure Random Value** is one that has been\n *  generated with additional care take to prevent side-channels\n *  from allowing others to detect it and prevent others from through\n *  coincidence generate the same values.\n *\n *  @_subsection: api/crypto:Random Values  [about-crypto-random]\n */\nimport { randomBytes as crypto_random } from \"./crypto.js\";\nlet locked = false;\nconst _randomBytes = function (length) {\n  return new Uint8Array(crypto_random(length));\n};\nlet __randomBytes = _randomBytes;\n/**\n *  Return %%length%% bytes of cryptographically secure random data.\n *\n *  @example:\n *    randomBytes(8)\n *    //_result:\n */\nexport function randomBytes(length) {\n  return __randomBytes(length);\n}\nrandomBytes._ = _randomBytes;\nrandomBytes.lock = function () {\n  locked = true;\n};\nrandomBytes.register = function (func) {\n  if (locked) {\n    throw new Error(\"randomBytes is locked\");\n  }\n  __randomBytes = func;\n};\nObject.freeze(randomBytes);", "map": {"version": 3, "names": ["randomBytes", "crypto_random", "locked", "_randomBytes", "length", "Uint8Array", "__randomBytes", "_", "lock", "register", "func", "Error", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\random.ts"], "sourcesContent": ["/**\n *  A **Cryptographically Secure Random Value** is one that has been\n *  generated with additional care take to prevent side-channels\n *  from allowing others to detect it and prevent others from through\n *  coincidence generate the same values.\n *\n *  @_subsection: api/crypto:Random Values  [about-crypto-random]\n */\nimport { randomBytes as crypto_random } from \"./crypto.js\";\n\nlet locked = false;\n\nconst _randomBytes = function(length: number): Uint8Array {\n    return new Uint8Array(crypto_random(length));\n}\n\nlet __randomBytes = _randomBytes;\n\n/**\n *  Return %%length%% bytes of cryptographically secure random data.\n *\n *  @example:\n *    randomBytes(8)\n *    //_result:\n */\nexport function randomBytes(length: number): Uint8Array {\n    return __randomBytes(length);\n}\n\nrandomBytes._ = _randomBytes;\nrandomBytes.lock = function(): void { locked = true; }\nrandomBytes.register = function(func: (length: number) => Uint8Array) {\n    if (locked) { throw new Error(\"randomBytes is locked\"); }\n    __randomBytes = func;\n}\nObject.freeze(randomBytes);\n"], "mappings": "AAAA;;;;;;;;AAQA,SAASA,WAAW,IAAIC,aAAa,QAAQ,aAAa;AAE1D,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,YAAY,GAAG,SAAAA,CAASC,MAAc;EACxC,OAAO,IAAIC,UAAU,CAACJ,aAAa,CAACG,MAAM,CAAC,CAAC;AAChD,CAAC;AAED,IAAIE,aAAa,GAAGH,YAAY;AAEhC;;;;;;;AAOA,OAAM,SAAUH,WAAWA,CAACI,MAAc;EACtC,OAAOE,aAAa,CAACF,MAAM,CAAC;AAChC;AAEAJ,WAAW,CAACO,CAAC,GAAGJ,YAAY;AAC5BH,WAAW,CAACQ,IAAI,GAAG;EAAmBN,MAAM,GAAG,IAAI;AAAE,CAAC;AACtDF,WAAW,CAACS,QAAQ,GAAG,UAASC,IAAoC;EAChE,IAAIR,MAAM,EAAE;IAAE,MAAM,IAAIS,KAAK,CAAC,uBAAuB,CAAC;;EACtDL,aAAa,GAAGI,IAAI;AACxB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACb,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}