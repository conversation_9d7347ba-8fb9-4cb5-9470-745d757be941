{"ast": null, "code": "import { AES } from \"./aes.js\";\nexport class ModeOfOperation {\n  constructor(name, key, cls) {\n    if (cls && !(this instanceof cls)) {\n      throw new Error(`${name} must be instantiated with \"new\"`);\n    }\n    Object.defineProperties(this, {\n      aes: {\n        enumerable: true,\n        value: new AES(key)\n      },\n      name: {\n        enumerable: true,\n        value: name\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["AES", "ModeOfOperation", "constructor", "name", "key", "cls", "Error", "Object", "defineProperties", "aes", "enumerable", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode.ts"], "sourcesContent": ["\nimport { AES } from \"./aes.js\";\n\nexport abstract class ModeOfOperation {\n  readonly aes!: AES;\n  readonly name!: string;\n\n  constructor(name: string, key: Uint8Array, cls?: any) {\n    if (cls && !(this instanceof cls)) {\n      throw new Error(`${ name } must be instantiated with \"new\"`);\n    }\n\n    Object.defineProperties(this, {\n      aes: { enumerable: true, value: new AES(key) },\n      name: { enumerable: true, value: name }\n    });\n  }\n\n  abstract encrypt(plaintext: Uint8Array): Uint8Array;\n  abstract decrypt(ciphertext: Uint8Array): Uint8Array;\n}\n"], "mappings": "AACA,SAASA,GAAG,QAAQ,UAAU;AAE9B,OAAM,MAAgBC,eAAe;EAInCC,YAAYC,IAAY,EAAEC,GAAe,EAAEC,GAAS;IAClD,IAAIA,GAAG,IAAI,EAAE,IAAI,YAAYA,GAAG,CAAC,EAAE;MACjC,MAAM,IAAIC,KAAK,CAAC,GAAIH,IAAK,kCAAkC,CAAC;;IAG9DI,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAC5BC,GAAG,EAAE;QAAEC,UAAU,EAAE,IAAI;QAAEC,KAAK,EAAE,IAAIX,GAAG,CAACI,GAAG;MAAC,CAAE;MAC9CD,IAAI,EAAE;QAAEO,UAAU,EAAE,IAAI;QAAEC,KAAK,EAAER;MAAI;KACtC,CAAC;EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}