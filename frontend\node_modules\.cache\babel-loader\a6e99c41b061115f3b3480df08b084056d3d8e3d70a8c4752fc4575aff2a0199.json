{"ast": null, "code": "/**\n *  [[link-quicknode]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base Mainnet (``base``);\n *  - Base Goerli Testnet (``base-goerli``);\n *  - Base Sepolia Testnet (``base-sepolia``);\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:QuickNode  [providers-quicknode]\n */\nimport { defineProperties, FetchRequest, assertArgument } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nconst defaultToken = \"919b412a057b5e9c9b6dce193c5a60242d6efadb\";\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"ethers.quiknode.pro\";\n    case \"goerli\":\n      return \"ethers.ethereum-goerli.quiknode.pro\";\n    case \"sepolia\":\n      return \"ethers.ethereum-sepolia.quiknode.pro\";\n    case \"holesky\":\n      return \"ethers.ethereum-holesky.quiknode.pro\";\n    case \"arbitrum\":\n      return \"ethers.arbitrum-mainnet.quiknode.pro\";\n    case \"arbitrum-goerli\":\n      return \"ethers.arbitrum-goerli.quiknode.pro\";\n    case \"arbitrum-sepolia\":\n      return \"ethers.arbitrum-sepolia.quiknode.pro\";\n    case \"base\":\n      return \"ethers.base-mainnet.quiknode.pro\";\n    case \"base-goerli\":\n      return \"ethers.base-goerli.quiknode.pro\";\n    case \"base-spolia\":\n      return \"ethers.base-sepolia.quiknode.pro\";\n    case \"bnb\":\n      return \"ethers.bsc.quiknode.pro\";\n    case \"bnbt\":\n      return \"ethers.bsc-testnet.quiknode.pro\";\n    case \"matic\":\n      return \"ethers.matic.quiknode.pro\";\n    case \"matic-mumbai\":\n      return \"ethers.matic-testnet.quiknode.pro\";\n    case \"optimism\":\n      return \"ethers.optimism.quiknode.pro\";\n    case \"optimism-goerli\":\n      return \"ethers.optimism-goerli.quiknode.pro\";\n    case \"optimism-sepolia\":\n      return \"ethers.optimism-sepolia.quiknode.pro\";\n    case \"xdai\":\n      return \"ethers.xdai.quiknode.pro\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/*\n@TODO:\n  These networks are not currently present in the Network\n  default included networks. Research them and ensure they\n  are EVM compatible and work with ethers\n\n  http://ethers.matic-amoy.quiknode.pro\n\n  http://ethers.avalanche-mainnet.quiknode.pro\n  http://ethers.avalanche-testnet.quiknode.pro\n  http://ethers.blast-sepolia.quiknode.pro\n  http://ethers.celo-mainnet.quiknode.pro\n  http://ethers.fantom.quiknode.pro\n  http://ethers.imx-demo.quiknode.pro\n  http://ethers.imx-mainnet.quiknode.pro\n  http://ethers.imx-testnet.quiknode.pro\n  http://ethers.near-mainnet.quiknode.pro\n  http://ethers.near-testnet.quiknode.pro\n  http://ethers.nova-mainnet.quiknode.pro\n  http://ethers.scroll-mainnet.quiknode.pro\n  http://ethers.scroll-testnet.quiknode.pro\n  http://ethers.tron-mainnet.quiknode.pro\n  http://ethers.zkevm-mainnet.quiknode.pro\n  http://ethers.zkevm-testnet.quiknode.pro\n  http://ethers.zksync-mainnet.quiknode.pro\n  http://ethers.zksync-testnet.quiknode.pro\n*/\n/**\n *  The **QuickNodeProvider** connects to the [[link-quicknode]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API token is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-quicknode).\n */\nexport class QuickNodeProvider extends JsonRpcProvider {\n  /**\n   *  The API token.\n   */\n  token;\n  /**\n   *  Creates a new **QuickNodeProvider**.\n   */\n  constructor(_network, token) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (token == null) {\n      token = defaultToken;\n    }\n    const request = QuickNodeProvider.getRequest(network, token);\n    super(request, network, {\n      staticNetwork: network\n    });\n    defineProperties(this, {\n      token\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new QuickNodeProvider(chainId, this.token);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  isCommunityResource() {\n    return this.token === defaultToken;\n  }\n  /**\n   *  Returns a new request prepared for %%network%% and the\n   *  %%token%%.\n   */\n  static getRequest(network, token) {\n    if (token == null) {\n      token = defaultToken;\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/${token}`);\n    request.allowGzip = true;\n    //if (projectSecret) { request.setCredentials(\"\", projectSecret); }\n    if (token === defaultToken) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"QuickNodeProvider\");\n        return true;\n      };\n    }\n    return request;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "FetchRequest", "assertArgument", "showThrottleMessage", "Network", "JsonRpcProvider", "defaultToken", "getHost", "name", "QuickNodeProvider", "token", "constructor", "_network", "network", "from", "request", "getRequest", "staticNetwork", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "isCommunityResource", "allowGzip", "retryFunc", "response", "attempt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-quicknode.ts"], "sourcesContent": ["/**\n *  [[link-quicknode]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base Mainnet (``base``);\n *  - Base Goerli Testnet (``base-goerli``);\n *  - Base Sepolia Testnet (``base-sepolia``);\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:QuickNode  [providers-quicknode]\n */\n\nimport {\n    defineProperties, FetchRequest, assertArgument\n} from \"../utils/index.js\";\n\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { AbstractProvider } from \"./abstract-provider.js\";\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\n\n\nconst defaultToken = \"919b412a057b5e9c9b6dce193c5a60242d6efadb\";\n\nfunction getHost(name: string): string {\n    switch(name) {\n        case \"mainnet\":\n            return \"ethers.quiknode.pro\";\n        case \"goerli\":\n            return \"ethers.ethereum-goerli.quiknode.pro\";\n        case \"sepolia\":\n            return \"ethers.ethereum-sepolia.quiknode.pro\";\n        case \"holesky\":\n            return \"ethers.ethereum-holesky.quiknode.pro\";\n\n        case \"arbitrum\":\n            return \"ethers.arbitrum-mainnet.quiknode.pro\";\n        case \"arbitrum-goerli\":\n            return \"ethers.arbitrum-goerli.quiknode.pro\";\n        case \"arbitrum-sepolia\":\n            return \"ethers.arbitrum-sepolia.quiknode.pro\";\n        case \"base\":\n            return \"ethers.base-mainnet.quiknode.pro\";\n        case \"base-goerli\":\n            return \"ethers.base-goerli.quiknode.pro\";\n        case \"base-spolia\":\n            return \"ethers.base-sepolia.quiknode.pro\";\n        case \"bnb\":\n            return \"ethers.bsc.quiknode.pro\";\n        case \"bnbt\":\n            return \"ethers.bsc-testnet.quiknode.pro\";\n        case \"matic\":\n            return \"ethers.matic.quiknode.pro\";\n        case \"matic-mumbai\":\n            return \"ethers.matic-testnet.quiknode.pro\";\n        case \"optimism\":\n            return \"ethers.optimism.quiknode.pro\";\n        case \"optimism-goerli\":\n            return \"ethers.optimism-goerli.quiknode.pro\";\n        case \"optimism-sepolia\":\n            return \"ethers.optimism-sepolia.quiknode.pro\";\n        case \"xdai\":\n            return \"ethers.xdai.quiknode.pro\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n/*\n@TODO:\n  These networks are not currently present in the Network\n  default included networks. Research them and ensure they\n  are EVM compatible and work with ethers\n\n  http://ethers.matic-amoy.quiknode.pro\n\n  http://ethers.avalanche-mainnet.quiknode.pro\n  http://ethers.avalanche-testnet.quiknode.pro\n  http://ethers.blast-sepolia.quiknode.pro\n  http://ethers.celo-mainnet.quiknode.pro\n  http://ethers.fantom.quiknode.pro\n  http://ethers.imx-demo.quiknode.pro\n  http://ethers.imx-mainnet.quiknode.pro\n  http://ethers.imx-testnet.quiknode.pro\n  http://ethers.near-mainnet.quiknode.pro\n  http://ethers.near-testnet.quiknode.pro\n  http://ethers.nova-mainnet.quiknode.pro\n  http://ethers.scroll-mainnet.quiknode.pro\n  http://ethers.scroll-testnet.quiknode.pro\n  http://ethers.tron-mainnet.quiknode.pro\n  http://ethers.zkevm-mainnet.quiknode.pro\n  http://ethers.zkevm-testnet.quiknode.pro\n  http://ethers.zksync-mainnet.quiknode.pro\n  http://ethers.zksync-testnet.quiknode.pro\n*/\n\n\n\n/**\n *  The **QuickNodeProvider** connects to the [[link-quicknode]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API token is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-quicknode).\n */\nexport class QuickNodeProvider extends JsonRpcProvider implements CommunityResourcable {\n    /**\n     *  The API token.\n     */\n    readonly token!: string;\n\n    /**\n     *  Creates a new **QuickNodeProvider**.\n     */\n    constructor(_network?: Networkish, token?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        if (token == null) { token = defaultToken; }\n\n        const request = QuickNodeProvider.getRequest(network, token);\n        super(request, network, { staticNetwork: network });\n\n        defineProperties<QuickNodeProvider>(this, { token });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new QuickNodeProvider(chainId, this.token);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.token === defaultToken);\n    }\n\n    /**\n     *  Returns a new request prepared for %%network%% and the\n     *  %%token%%.\n     */\n    static getRequest(network: Network, token?: null | string): FetchRequest {\n        if (token == null) { token = defaultToken; }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/${ token }`);\n        request.allowGzip = true;\n        //if (projectSecret) { request.setCredentials(\"\", projectSecret); }\n\n        if (token === defaultToken) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"QuickNodeProvider\");\n                return true;\n            };\n        }\n\n        return request;\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,SACIA,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAC3C,mBAAmB;AAE1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAOvD,MAAMC,YAAY,GAAG,0CAA0C;AAE/D,SAASC,OAAOA,CAACC,IAAY;EACzB,QAAOA,IAAI;IACP,KAAK,SAAS;MACV,OAAO,qBAAqB;IAChC,KAAK,QAAQ;MACT,OAAO,qCAAqC;IAChD,KAAK,SAAS;MACV,OAAO,sCAAsC;IACjD,KAAK,SAAS;MACV,OAAO,sCAAsC;IAEjD,KAAK,UAAU;MACX,OAAO,sCAAsC;IACjD,KAAK,iBAAiB;MAClB,OAAO,qCAAqC;IAChD,KAAK,kBAAkB;MACnB,OAAO,sCAAsC;IACjD,KAAK,MAAM;MACP,OAAO,kCAAkC;IAC7C,KAAK,aAAa;MACd,OAAO,iCAAiC;IAC5C,KAAK,aAAa;MACd,OAAO,kCAAkC;IAC7C,KAAK,KAAK;MACN,OAAO,yBAAyB;IACpC,KAAK,MAAM;MACP,OAAO,iCAAiC;IAC5C,KAAK,OAAO;MACR,OAAO,2BAA2B;IACtC,KAAK,cAAc;MACf,OAAO,mCAAmC;IAC9C,KAAK,UAAU;MACX,OAAO,8BAA8B;IACzC,KAAK,iBAAiB;MAClB,OAAO,qCAAqC;IAChD,KAAK,kBAAkB;MACnB,OAAO,sCAAsC;IACjD,KAAK,MAAM;MACP,OAAO,0BAA0B;;EAGzCN,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEM,IAAI,CAAC;AACjE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;;;;;;;;;AASA,OAAM,MAAOC,iBAAkB,SAAQJ,eAAe;EAClD;;;EAGSK,KAAK;EAEd;;;EAGAC,YAAYC,QAAqB,EAAEF,KAAqB;IACpD,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGT,OAAO,CAACU,IAAI,CAACF,QAAQ,CAAC;IACtC,IAAIF,KAAK,IAAI,IAAI,EAAE;MAAEA,KAAK,GAAGJ,YAAY;;IAEzC,MAAMS,OAAO,GAAGN,iBAAiB,CAACO,UAAU,CAACH,OAAO,EAAEH,KAAK,CAAC;IAC5D,KAAK,CAACK,OAAO,EAAEF,OAAO,EAAE;MAAEI,aAAa,EAAEJ;IAAO,CAAE,CAAC;IAEnDb,gBAAgB,CAAoB,IAAI,EAAE;MAAEU;IAAK,CAAE,CAAC;EACxD;EAEAQ,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIV,iBAAiB,CAACU,OAAO,EAAE,IAAI,CAACT,KAAK,CAAC;KACpD,CAAC,OAAOU,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEAE,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACX,KAAK,KAAKJ,YAAY;EACvC;EAEA;;;;EAIA,OAAOU,UAAUA,CAACH,OAAgB,EAAEH,KAAqB;IACrD,IAAIA,KAAK,IAAI,IAAI,EAAE;MAAEA,KAAK,GAAGJ,YAAY;;IAEzC,MAAMS,OAAO,GAAG,IAAId,YAAY,CAAC,YAAaM,OAAO,CAACM,OAAO,CAACL,IAAI,CAAE,IAAKE,KAAM,EAAE,CAAC;IAClFK,OAAO,CAACO,SAAS,GAAG,IAAI;IACxB;IAEA,IAAIZ,KAAK,KAAKJ,YAAY,EAAE;MACxBS,OAAO,CAACQ,SAAS,GAAG,OAAOR,OAAO,EAAES,QAAQ,EAAEC,OAAO,KAAI;QACrDtB,mBAAmB,CAAC,mBAAmB,CAAC;QACxC,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOY,OAAO;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}