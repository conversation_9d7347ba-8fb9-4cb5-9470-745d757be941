{"ast": null, "code": "const IpcSocketProvider = undefined;\nexport { IpcSocketProvider };", "map": {"version": 3, "names": ["IpcSocketProvider", "undefined"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-ipcsocket-browser.ts"], "sourcesContent": ["const IpcSocketProvider = undefined;\n\nexport { IpcSocketProvider };\n"], "mappings": "AAAA,MAAMA,iBAAiB,GAAGC,SAAS;AAEnC,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}