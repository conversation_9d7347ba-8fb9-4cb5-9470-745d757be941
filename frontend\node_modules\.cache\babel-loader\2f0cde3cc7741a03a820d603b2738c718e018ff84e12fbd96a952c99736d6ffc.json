{"ast": null, "code": "import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\nimport { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n  return animations => Promise.all(animations.map(({\n    animation,\n    options\n  }) => animateVisualElement(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n  let animate = animateList(visualElement);\n  const state = createState();\n  let isInitialRender = true;\n  /**\n   * This function will be used to reduce the animation definitions for\n   * each active animation type into an object of resolved values for it.\n   */\n  const buildResolvedTypeValues = (acc, definition) => {\n    const resolved = resolveVariant(visualElement, definition);\n    if (resolved) {\n      const {\n        transition,\n        transitionEnd,\n        ...target\n      } = resolved;\n      acc = {\n        ...acc,\n        ...target,\n        ...transitionEnd\n      };\n    }\n    return acc;\n  };\n  /**\n   * This just allows us to inject mocked animation functions\n   * @internal\n   */\n  function setAnimateFunction(makeAnimator) {\n    animate = makeAnimator(visualElement);\n  }\n  /**\n   * When we receive new props, we need to:\n   * 1. Create a list of protected keys for each type. This is a directory of\n   *    value keys that are currently being \"handled\" by types of a higher priority\n   *    so that whenever an animation is played of a given type, these values are\n   *    protected from being animated.\n   * 2. Determine if an animation type needs animating.\n   * 3. Determine if any values have been removed from a type and figure out\n   *    what to animate those to.\n   */\n  function animateChanges(options, changedActiveType) {\n    const props = visualElement.getProps();\n    const context = visualElement.getVariantContext(true) || {};\n    /**\n     * A list of animations that we'll build into as we iterate through the animation\n     * types. This will get executed at the end of the function.\n     */\n    const animations = [];\n    /**\n     * Keep track of which values have been removed. Then, as we hit lower priority\n     * animation types, we can check if they contain removed values and animate to that.\n     */\n    const removedKeys = new Set();\n    /**\n     * A dictionary of all encountered keys. This is an object to let us build into and\n     * copy it without iteration. Each time we hit an animation type we set its protected\n     * keys - the keys its not allowed to animate - to the latest version of this object.\n     */\n    let encounteredKeys = {};\n    /**\n     * If a variant has been removed at a given index, and this component is controlling\n     * variant animations, we want to ensure lower-priority variants are forced to animate.\n     */\n    let removedVariantIndex = Infinity;\n    /**\n     * Iterate through all animation types in reverse priority order. For each, we want to\n     * detect which values it's handling and whether or not they've changed (and therefore\n     * need to be animated). If any values have been removed, we want to detect those in\n     * lower priority props and flag for animation.\n     */\n    for (let i = 0; i < numAnimationTypes; i++) {\n      const type = reversePriorityOrder[i];\n      const typeState = state[type];\n      const prop = props[type] !== undefined ? props[type] : context[type];\n      const propIsVariant = isVariantLabel(prop);\n      /**\n       * If this type has *just* changed isActive status, set activeDelta\n       * to that status. Otherwise set to null.\n       */\n      const activeDelta = type === changedActiveType ? typeState.isActive : null;\n      if (activeDelta === false) removedVariantIndex = i;\n      /**\n       * If this prop is an inherited variant, rather than been set directly on the\n       * component itself, we want to make sure we allow the parent to trigger animations.\n       *\n       * TODO: Can probably change this to a !isControllingVariants check\n       */\n      let isInherited = prop === context[type] && prop !== props[type] && propIsVariant;\n      /**\n       *\n       */\n      if (isInherited && isInitialRender && visualElement.manuallyAnimateOnMount) {\n        isInherited = false;\n      }\n      /**\n       * Set all encountered keys so far as the protected keys for this type. This will\n       * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n       */\n      typeState.protectedKeys = {\n        ...encounteredKeys\n      };\n      // Check if we can skip analysing this prop early\n      if (\n      // If it isn't active and hasn't *just* been set as inactive\n      !typeState.isActive && activeDelta === null ||\n      // If we didn't and don't have any defined prop for this animation type\n      !prop && !typeState.prevProp ||\n      // Or if the prop doesn't define an animation\n      isAnimationControls(prop) || typeof prop === \"boolean\") {\n        continue;\n      }\n      /**\n       * As we go look through the values defined on this type, if we detect\n       * a changed value or a value that was removed in a higher priority, we set\n       * this to true and add this prop to the animation list.\n       */\n      const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n      let shouldAnimateType = variantDidChange ||\n      // If we're making this variant active, we want to always make it active\n      type === changedActiveType && typeState.isActive && !isInherited && propIsVariant ||\n      // If we removed a higher-priority variant (i is in reverse order)\n      i > removedVariantIndex && propIsVariant;\n      let handledRemovedValues = false;\n      /**\n       * As animations can be set as variant lists, variants or target objects, we\n       * coerce everything to an array if it isn't one already\n       */\n      const definitionList = Array.isArray(prop) ? prop : [prop];\n      /**\n       * Build an object of all the resolved values. We'll use this in the subsequent\n       * animateChanges calls to determine whether a value has changed.\n       */\n      let resolvedValues = definitionList.reduce(buildResolvedTypeValues, {});\n      if (activeDelta === false) resolvedValues = {};\n      /**\n       * Now we need to loop through all the keys in the prev prop and this prop,\n       * and decide:\n       * 1. If the value has changed, and needs animating\n       * 2. If it has been removed, and needs adding to the removedKeys set\n       * 3. If it has been removed in a higher priority type and needs animating\n       * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n       *    needs adding to the type's protectedKeys list.\n       */\n      const {\n        prevResolvedValues = {}\n      } = typeState;\n      const allKeys = {\n        ...prevResolvedValues,\n        ...resolvedValues\n      };\n      const markToAnimate = key => {\n        shouldAnimateType = true;\n        if (removedKeys.has(key)) {\n          handledRemovedValues = true;\n          removedKeys.delete(key);\n        }\n        typeState.needsAnimating[key] = true;\n      };\n      for (const key in allKeys) {\n        const next = resolvedValues[key];\n        const prev = prevResolvedValues[key];\n        // If we've already handled this we can just skip ahead\n        if (encounteredKeys.hasOwnProperty(key)) continue;\n        /**\n         * If the value has changed, we probably want to animate it.\n         */\n        let valueHasChanged = false;\n        if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n          valueHasChanged = !shallowCompare(next, prev);\n        } else {\n          valueHasChanged = next !== prev;\n        }\n        if (valueHasChanged) {\n          if (next !== undefined) {\n            // If next is defined and doesn't equal prev, it needs animating\n            markToAnimate(key);\n          } else {\n            // If it's undefined, it's been removed.\n            removedKeys.add(key);\n          }\n        } else if (next !== undefined && removedKeys.has(key)) {\n          /**\n           * If next hasn't changed and it isn't undefined, we want to check if it's\n           * been removed by a higher priority\n           */\n          markToAnimate(key);\n        } else {\n          /**\n           * If it hasn't changed, we add it to the list of protected values\n           * to ensure it doesn't get animated.\n           */\n          typeState.protectedKeys[key] = true;\n        }\n      }\n      /**\n       * Update the typeState so next time animateChanges is called we can compare the\n       * latest prop and resolvedValues to these.\n       */\n      typeState.prevProp = prop;\n      typeState.prevResolvedValues = resolvedValues;\n      /**\n       *\n       */\n      if (typeState.isActive) {\n        encounteredKeys = {\n          ...encounteredKeys,\n          ...resolvedValues\n        };\n      }\n      if (isInitialRender && visualElement.blockInitialAnimation) {\n        shouldAnimateType = false;\n      }\n      /**\n       * If this is an inherited prop we want to hard-block animations\n       */\n      if (shouldAnimateType && (!isInherited || handledRemovedValues)) {\n        animations.push(...definitionList.map(animation => ({\n          animation: animation,\n          options: {\n            type,\n            ...options\n          }\n        })));\n      }\n    }\n    /**\n     * If there are some removed value that haven't been dealt with,\n     * we need to create a new animation that falls back either to the value\n     * defined in the style prop, or the last read value.\n     */\n    if (removedKeys.size) {\n      const fallbackAnimation = {};\n      removedKeys.forEach(key => {\n        const fallbackTarget = visualElement.getBaseTarget(key);\n        if (fallbackTarget !== undefined) {\n          fallbackAnimation[key] = fallbackTarget;\n        }\n      });\n      animations.push({\n        animation: fallbackAnimation\n      });\n    }\n    let shouldAnimate = Boolean(animations.length);\n    if (isInitialRender && (props.initial === false || props.initial === props.animate) && !visualElement.manuallyAnimateOnMount) {\n      shouldAnimate = false;\n    }\n    isInitialRender = false;\n    return shouldAnimate ? animate(animations) : Promise.resolve();\n  }\n  /**\n   * Change whether a certain animation type is active.\n   */\n  function setActive(type, isActive, options) {\n    var _a;\n    // If the active state hasn't changed, we can safely do nothing here\n    if (state[type].isActive === isActive) return Promise.resolve();\n    // Propagate active change to children\n    (_a = visualElement.variantChildren) === null || _a === void 0 ? void 0 : _a.forEach(child => {\n      var _a;\n      return (_a = child.animationState) === null || _a === void 0 ? void 0 : _a.setActive(type, isActive);\n    });\n    state[type].isActive = isActive;\n    const animations = animateChanges(options, type);\n    for (const key in state) {\n      state[key].protectedKeys = {};\n    }\n    return animations;\n  }\n  return {\n    animateChanges,\n    setActive,\n    setAnimateFunction,\n    getState: () => state\n  };\n}\nfunction checkVariantsDidChange(prev, next) {\n  if (typeof next === \"string\") {\n    return next !== prev;\n  } else if (Array.isArray(next)) {\n    return !shallowCompare(next, prev);\n  }\n  return false;\n}\nfunction createTypeState(isActive = false) {\n  return {\n    isActive,\n    protectedKeys: {},\n    needsAnimating: {},\n    prevResolvedValues: {}\n  };\n}\nfunction createState() {\n  return {\n    animate: createTypeState(true),\n    whileInView: createTypeState(),\n    whileHover: createTypeState(),\n    whileTap: createTypeState(),\n    whileDrag: createTypeState(),\n    whileFocus: createTypeState(),\n    exit: createTypeState()\n  };\n}\nexport { checkVariantsDidChange, createAnimationState };", "map": {"version": 3, "names": ["isAnimationControls", "isKeyframesTarget", "shallowCompare", "isVariantLabel", "resolveV<PERSON>t", "variantPriorityOrder", "animateVisualElement", "reversePriorityOrder", "reverse", "numAnimationTypes", "length", "animateList", "visualElement", "animations", "Promise", "all", "map", "animation", "options", "createAnimationState", "animate", "state", "createState", "isInitialRender", "buildResolvedTypeValues", "acc", "definition", "resolved", "transition", "transitionEnd", "target", "setAnimateFunction", "makeAnimator", "animateChanges", "changedActiveType", "props", "getProps", "context", "getVariantContext", "<PERSON><PERSON><PERSON><PERSON>", "Set", "<PERSON><PERSON><PERSON><PERSON>", "removedVariantIndex", "Infinity", "i", "type", "typeState", "prop", "undefined", "propIsVariant", "activeDelta", "isActive", "isInherited", "manuallyAnimateOnMount", "protected<PERSON><PERSON>s", "prevProp", "variantDidChange", "checkVariantsDidChange", "shouldAnimateType", "handledRemovedValues", "definitionList", "Array", "isArray", "resolvedV<PERSON>ues", "reduce", "prevResolvedValues", "allKeys", "markToAnimate", "key", "has", "delete", "needsAnimating", "next", "prev", "hasOwnProperty", "valueHasChanged", "add", "blockInitialAnimation", "push", "size", "fallbackAnimation", "for<PERSON>ach", "fallback<PERSON><PERSON><PERSON>", "getBase<PERSON>arget", "shouldAnimate", "Boolean", "initial", "resolve", "setActive", "_a", "variant<PERSON><PERSON><PERSON>n", "child", "animationState", "getState", "createTypeState", "whileInView", "whileHover", "whileTap", "whileDrag", "whileFocus", "exit"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs"], "sourcesContent": ["import { isAnimationControls } from '../../animation/utils/is-animation-controls.mjs';\nimport { isKeyframesTarget } from '../../animation/utils/is-keyframes-target.mjs';\nimport { shallowCompare } from '../../utils/shallow-compare.mjs';\nimport { isVariantLabel } from './is-variant-label.mjs';\nimport { resolveVariant } from './resolve-dynamic-variants.mjs';\nimport { variantPriorityOrder } from './variant-props.mjs';\nimport { animateVisualElement } from '../../animation/interfaces/visual-element.mjs';\n\nconst reversePriorityOrder = [...variantPriorityOrder].reverse();\nconst numAnimationTypes = variantPriorityOrder.length;\nfunction animateList(visualElement) {\n    return (animations) => Promise.all(animations.map(({ animation, options }) => animateVisualElement(visualElement, animation, options)));\n}\nfunction createAnimationState(visualElement) {\n    let animate = animateList(visualElement);\n    const state = createState();\n    let isInitialRender = true;\n    /**\n     * This function will be used to reduce the animation definitions for\n     * each active animation type into an object of resolved values for it.\n     */\n    const buildResolvedTypeValues = (acc, definition) => {\n        const resolved = resolveVariant(visualElement, definition);\n        if (resolved) {\n            const { transition, transitionEnd, ...target } = resolved;\n            acc = { ...acc, ...target, ...transitionEnd };\n        }\n        return acc;\n    };\n    /**\n     * This just allows us to inject mocked animation functions\n     * @internal\n     */\n    function setAnimateFunction(makeAnimator) {\n        animate = makeAnimator(visualElement);\n    }\n    /**\n     * When we receive new props, we need to:\n     * 1. Create a list of protected keys for each type. This is a directory of\n     *    value keys that are currently being \"handled\" by types of a higher priority\n     *    so that whenever an animation is played of a given type, these values are\n     *    protected from being animated.\n     * 2. Determine if an animation type needs animating.\n     * 3. Determine if any values have been removed from a type and figure out\n     *    what to animate those to.\n     */\n    function animateChanges(options, changedActiveType) {\n        const props = visualElement.getProps();\n        const context = visualElement.getVariantContext(true) || {};\n        /**\n         * A list of animations that we'll build into as we iterate through the animation\n         * types. This will get executed at the end of the function.\n         */\n        const animations = [];\n        /**\n         * Keep track of which values have been removed. Then, as we hit lower priority\n         * animation types, we can check if they contain removed values and animate to that.\n         */\n        const removedKeys = new Set();\n        /**\n         * A dictionary of all encountered keys. This is an object to let us build into and\n         * copy it without iteration. Each time we hit an animation type we set its protected\n         * keys - the keys its not allowed to animate - to the latest version of this object.\n         */\n        let encounteredKeys = {};\n        /**\n         * If a variant has been removed at a given index, and this component is controlling\n         * variant animations, we want to ensure lower-priority variants are forced to animate.\n         */\n        let removedVariantIndex = Infinity;\n        /**\n         * Iterate through all animation types in reverse priority order. For each, we want to\n         * detect which values it's handling and whether or not they've changed (and therefore\n         * need to be animated). If any values have been removed, we want to detect those in\n         * lower priority props and flag for animation.\n         */\n        for (let i = 0; i < numAnimationTypes; i++) {\n            const type = reversePriorityOrder[i];\n            const typeState = state[type];\n            const prop = props[type] !== undefined ? props[type] : context[type];\n            const propIsVariant = isVariantLabel(prop);\n            /**\n             * If this type has *just* changed isActive status, set activeDelta\n             * to that status. Otherwise set to null.\n             */\n            const activeDelta = type === changedActiveType ? typeState.isActive : null;\n            if (activeDelta === false)\n                removedVariantIndex = i;\n            /**\n             * If this prop is an inherited variant, rather than been set directly on the\n             * component itself, we want to make sure we allow the parent to trigger animations.\n             *\n             * TODO: Can probably change this to a !isControllingVariants check\n             */\n            let isInherited = prop === context[type] && prop !== props[type] && propIsVariant;\n            /**\n             *\n             */\n            if (isInherited &&\n                isInitialRender &&\n                visualElement.manuallyAnimateOnMount) {\n                isInherited = false;\n            }\n            /**\n             * Set all encountered keys so far as the protected keys for this type. This will\n             * be any key that has been animated or otherwise handled by active, higher-priortiy types.\n             */\n            typeState.protectedKeys = { ...encounteredKeys };\n            // Check if we can skip analysing this prop early\n            if (\n            // If it isn't active and hasn't *just* been set as inactive\n            (!typeState.isActive && activeDelta === null) ||\n                // If we didn't and don't have any defined prop for this animation type\n                (!prop && !typeState.prevProp) ||\n                // Or if the prop doesn't define an animation\n                isAnimationControls(prop) ||\n                typeof prop === \"boolean\") {\n                continue;\n            }\n            /**\n             * As we go look through the values defined on this type, if we detect\n             * a changed value or a value that was removed in a higher priority, we set\n             * this to true and add this prop to the animation list.\n             */\n            const variantDidChange = checkVariantsDidChange(typeState.prevProp, prop);\n            let shouldAnimateType = variantDidChange ||\n                // If we're making this variant active, we want to always make it active\n                (type === changedActiveType &&\n                    typeState.isActive &&\n                    !isInherited &&\n                    propIsVariant) ||\n                // If we removed a higher-priority variant (i is in reverse order)\n                (i > removedVariantIndex && propIsVariant);\n            let handledRemovedValues = false;\n            /**\n             * As animations can be set as variant lists, variants or target objects, we\n             * coerce everything to an array if it isn't one already\n             */\n            const definitionList = Array.isArray(prop) ? prop : [prop];\n            /**\n             * Build an object of all the resolved values. We'll use this in the subsequent\n             * animateChanges calls to determine whether a value has changed.\n             */\n            let resolvedValues = definitionList.reduce(buildResolvedTypeValues, {});\n            if (activeDelta === false)\n                resolvedValues = {};\n            /**\n             * Now we need to loop through all the keys in the prev prop and this prop,\n             * and decide:\n             * 1. If the value has changed, and needs animating\n             * 2. If it has been removed, and needs adding to the removedKeys set\n             * 3. If it has been removed in a higher priority type and needs animating\n             * 4. If it hasn't been removed in a higher priority but hasn't changed, and\n             *    needs adding to the type's protectedKeys list.\n             */\n            const { prevResolvedValues = {} } = typeState;\n            const allKeys = {\n                ...prevResolvedValues,\n                ...resolvedValues,\n            };\n            const markToAnimate = (key) => {\n                shouldAnimateType = true;\n                if (removedKeys.has(key)) {\n                    handledRemovedValues = true;\n                    removedKeys.delete(key);\n                }\n                typeState.needsAnimating[key] = true;\n            };\n            for (const key in allKeys) {\n                const next = resolvedValues[key];\n                const prev = prevResolvedValues[key];\n                // If we've already handled this we can just skip ahead\n                if (encounteredKeys.hasOwnProperty(key))\n                    continue;\n                /**\n                 * If the value has changed, we probably want to animate it.\n                 */\n                let valueHasChanged = false;\n                if (isKeyframesTarget(next) && isKeyframesTarget(prev)) {\n                    valueHasChanged = !shallowCompare(next, prev);\n                }\n                else {\n                    valueHasChanged = next !== prev;\n                }\n                if (valueHasChanged) {\n                    if (next !== undefined) {\n                        // If next is defined and doesn't equal prev, it needs animating\n                        markToAnimate(key);\n                    }\n                    else {\n                        // If it's undefined, it's been removed.\n                        removedKeys.add(key);\n                    }\n                }\n                else if (next !== undefined && removedKeys.has(key)) {\n                    /**\n                     * If next hasn't changed and it isn't undefined, we want to check if it's\n                     * been removed by a higher priority\n                     */\n                    markToAnimate(key);\n                }\n                else {\n                    /**\n                     * If it hasn't changed, we add it to the list of protected values\n                     * to ensure it doesn't get animated.\n                     */\n                    typeState.protectedKeys[key] = true;\n                }\n            }\n            /**\n             * Update the typeState so next time animateChanges is called we can compare the\n             * latest prop and resolvedValues to these.\n             */\n            typeState.prevProp = prop;\n            typeState.prevResolvedValues = resolvedValues;\n            /**\n             *\n             */\n            if (typeState.isActive) {\n                encounteredKeys = { ...encounteredKeys, ...resolvedValues };\n            }\n            if (isInitialRender && visualElement.blockInitialAnimation) {\n                shouldAnimateType = false;\n            }\n            /**\n             * If this is an inherited prop we want to hard-block animations\n             */\n            if (shouldAnimateType && (!isInherited || handledRemovedValues)) {\n                animations.push(...definitionList.map((animation) => ({\n                    animation: animation,\n                    options: { type, ...options },\n                })));\n            }\n        }\n        /**\n         * If there are some removed value that haven't been dealt with,\n         * we need to create a new animation that falls back either to the value\n         * defined in the style prop, or the last read value.\n         */\n        if (removedKeys.size) {\n            const fallbackAnimation = {};\n            removedKeys.forEach((key) => {\n                const fallbackTarget = visualElement.getBaseTarget(key);\n                if (fallbackTarget !== undefined) {\n                    fallbackAnimation[key] = fallbackTarget;\n                }\n            });\n            animations.push({ animation: fallbackAnimation });\n        }\n        let shouldAnimate = Boolean(animations.length);\n        if (isInitialRender &&\n            (props.initial === false || props.initial === props.animate) &&\n            !visualElement.manuallyAnimateOnMount) {\n            shouldAnimate = false;\n        }\n        isInitialRender = false;\n        return shouldAnimate ? animate(animations) : Promise.resolve();\n    }\n    /**\n     * Change whether a certain animation type is active.\n     */\n    function setActive(type, isActive, options) {\n        var _a;\n        // If the active state hasn't changed, we can safely do nothing here\n        if (state[type].isActive === isActive)\n            return Promise.resolve();\n        // Propagate active change to children\n        (_a = visualElement.variantChildren) === null || _a === void 0 ? void 0 : _a.forEach((child) => { var _a; return (_a = child.animationState) === null || _a === void 0 ? void 0 : _a.setActive(type, isActive); });\n        state[type].isActive = isActive;\n        const animations = animateChanges(options, type);\n        for (const key in state) {\n            state[key].protectedKeys = {};\n        }\n        return animations;\n    }\n    return {\n        animateChanges,\n        setActive,\n        setAnimateFunction,\n        getState: () => state,\n    };\n}\nfunction checkVariantsDidChange(prev, next) {\n    if (typeof next === \"string\") {\n        return next !== prev;\n    }\n    else if (Array.isArray(next)) {\n        return !shallowCompare(next, prev);\n    }\n    return false;\n}\nfunction createTypeState(isActive = false) {\n    return {\n        isActive,\n        protectedKeys: {},\n        needsAnimating: {},\n        prevResolvedValues: {},\n    };\n}\nfunction createState() {\n    return {\n        animate: createTypeState(true),\n        whileInView: createTypeState(),\n        whileHover: createTypeState(),\n        whileTap: createTypeState(),\n        whileDrag: createTypeState(),\n        whileFocus: createTypeState(),\n        exit: createTypeState(),\n    };\n}\n\nexport { checkVariantsDidChange, createAnimationState };\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,iDAAiD;AACrF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,cAAc,QAAQ,gCAAgC;AAC/D,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,oBAAoB,QAAQ,+CAA+C;AAEpF,MAAMC,oBAAoB,GAAG,CAAC,GAAGF,oBAAoB,CAAC,CAACG,OAAO,CAAC,CAAC;AAChE,MAAMC,iBAAiB,GAAGJ,oBAAoB,CAACK,MAAM;AACrD,SAASC,WAAWA,CAACC,aAAa,EAAE;EAChC,OAAQC,UAAU,IAAKC,OAAO,CAACC,GAAG,CAACF,UAAU,CAACG,GAAG,CAAC,CAAC;IAAEC,SAAS;IAAEC;EAAQ,CAAC,KAAKZ,oBAAoB,CAACM,aAAa,EAAEK,SAAS,EAAEC,OAAO,CAAC,CAAC,CAAC;AAC3I;AACA,SAASC,oBAAoBA,CAACP,aAAa,EAAE;EACzC,IAAIQ,OAAO,GAAGT,WAAW,CAACC,aAAa,CAAC;EACxC,MAAMS,KAAK,GAAGC,WAAW,CAAC,CAAC;EAC3B,IAAIC,eAAe,GAAG,IAAI;EAC1B;AACJ;AACA;AACA;EACI,MAAMC,uBAAuB,GAAGA,CAACC,GAAG,EAAEC,UAAU,KAAK;IACjD,MAAMC,QAAQ,GAAGvB,cAAc,CAACQ,aAAa,EAAEc,UAAU,CAAC;IAC1D,IAAIC,QAAQ,EAAE;MACV,MAAM;QAAEC,UAAU;QAAEC,aAAa;QAAE,GAAGC;MAAO,CAAC,GAAGH,QAAQ;MACzDF,GAAG,GAAG;QAAE,GAAGA,GAAG;QAAE,GAAGK,MAAM;QAAE,GAAGD;MAAc,CAAC;IACjD;IACA,OAAOJ,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI,SAASM,kBAAkBA,CAACC,YAAY,EAAE;IACtCZ,OAAO,GAAGY,YAAY,CAACpB,aAAa,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASqB,cAAcA,CAACf,OAAO,EAAEgB,iBAAiB,EAAE;IAChD,MAAMC,KAAK,GAAGvB,aAAa,CAACwB,QAAQ,CAAC,CAAC;IACtC,MAAMC,OAAO,GAAGzB,aAAa,CAAC0B,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D;AACR;AACA;AACA;IACQ,MAAMzB,UAAU,GAAG,EAAE;IACrB;AACR;AACA;AACA;IACQ,MAAM0B,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB;AACR;AACA;AACA;IACQ,IAAIC,mBAAmB,GAAGC,QAAQ;IAClC;AACR;AACA;AACA;AACA;AACA;IACQ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnC,iBAAiB,EAAEmC,CAAC,EAAE,EAAE;MACxC,MAAMC,IAAI,GAAGtC,oBAAoB,CAACqC,CAAC,CAAC;MACpC,MAAME,SAAS,GAAGzB,KAAK,CAACwB,IAAI,CAAC;MAC7B,MAAME,IAAI,GAAGZ,KAAK,CAACU,IAAI,CAAC,KAAKG,SAAS,GAAGb,KAAK,CAACU,IAAI,CAAC,GAAGR,OAAO,CAACQ,IAAI,CAAC;MACpE,MAAMI,aAAa,GAAG9C,cAAc,CAAC4C,IAAI,CAAC;MAC1C;AACZ;AACA;AACA;MACY,MAAMG,WAAW,GAAGL,IAAI,KAAKX,iBAAiB,GAAGY,SAAS,CAACK,QAAQ,GAAG,IAAI;MAC1E,IAAID,WAAW,KAAK,KAAK,EACrBR,mBAAmB,GAAGE,CAAC;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAIQ,WAAW,GAAGL,IAAI,KAAKV,OAAO,CAACQ,IAAI,CAAC,IAAIE,IAAI,KAAKZ,KAAK,CAACU,IAAI,CAAC,IAAII,aAAa;MACjF;AACZ;AACA;MACY,IAAIG,WAAW,IACX7B,eAAe,IACfX,aAAa,CAACyC,sBAAsB,EAAE;QACtCD,WAAW,GAAG,KAAK;MACvB;MACA;AACZ;AACA;AACA;MACYN,SAAS,CAACQ,aAAa,GAAG;QAAE,GAAGb;MAAgB,CAAC;MAChD;MACA;MACA;MACC,CAACK,SAAS,CAACK,QAAQ,IAAID,WAAW,KAAK,IAAI;MACxC;MACC,CAACH,IAAI,IAAI,CAACD,SAAS,CAACS,QAAS;MAC9B;MACAvD,mBAAmB,CAAC+C,IAAI,CAAC,IACzB,OAAOA,IAAI,KAAK,SAAS,EAAE;QAC3B;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,MAAMS,gBAAgB,GAAGC,sBAAsB,CAACX,SAAS,CAACS,QAAQ,EAAER,IAAI,CAAC;MACzE,IAAIW,iBAAiB,GAAGF,gBAAgB;MACpC;MACCX,IAAI,KAAKX,iBAAiB,IACvBY,SAAS,CAACK,QAAQ,IAClB,CAACC,WAAW,IACZH,aAAc;MAClB;MACCL,CAAC,GAAGF,mBAAmB,IAAIO,aAAc;MAC9C,IAAIU,oBAAoB,GAAG,KAAK;MAChC;AACZ;AACA;AACA;MACY,MAAMC,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACf,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;MAC1D;AACZ;AACA;AACA;MACY,IAAIgB,cAAc,GAAGH,cAAc,CAACI,MAAM,CAACxC,uBAAuB,EAAE,CAAC,CAAC,CAAC;MACvE,IAAI0B,WAAW,KAAK,KAAK,EACrBa,cAAc,GAAG,CAAC,CAAC;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAM;QAAEE,kBAAkB,GAAG,CAAC;MAAE,CAAC,GAAGnB,SAAS;MAC7C,MAAMoB,OAAO,GAAG;QACZ,GAAGD,kBAAkB;QACrB,GAAGF;MACP,CAAC;MACD,MAAMI,aAAa,GAAIC,GAAG,IAAK;QAC3BV,iBAAiB,GAAG,IAAI;QACxB,IAAInB,WAAW,CAAC8B,GAAG,CAACD,GAAG,CAAC,EAAE;UACtBT,oBAAoB,GAAG,IAAI;UAC3BpB,WAAW,CAAC+B,MAAM,CAACF,GAAG,CAAC;QAC3B;QACAtB,SAAS,CAACyB,cAAc,CAACH,GAAG,CAAC,GAAG,IAAI;MACxC,CAAC;MACD,KAAK,MAAMA,GAAG,IAAIF,OAAO,EAAE;QACvB,MAAMM,IAAI,GAAGT,cAAc,CAACK,GAAG,CAAC;QAChC,MAAMK,IAAI,GAAGR,kBAAkB,CAACG,GAAG,CAAC;QACpC;QACA,IAAI3B,eAAe,CAACiC,cAAc,CAACN,GAAG,CAAC,EACnC;QACJ;AAChB;AACA;QACgB,IAAIO,eAAe,GAAG,KAAK;QAC3B,IAAI1E,iBAAiB,CAACuE,IAAI,CAAC,IAAIvE,iBAAiB,CAACwE,IAAI,CAAC,EAAE;UACpDE,eAAe,GAAG,CAACzE,cAAc,CAACsE,IAAI,EAAEC,IAAI,CAAC;QACjD,CAAC,MACI;UACDE,eAAe,GAAGH,IAAI,KAAKC,IAAI;QACnC;QACA,IAAIE,eAAe,EAAE;UACjB,IAAIH,IAAI,KAAKxB,SAAS,EAAE;YACpB;YACAmB,aAAa,CAACC,GAAG,CAAC;UACtB,CAAC,MACI;YACD;YACA7B,WAAW,CAACqC,GAAG,CAACR,GAAG,CAAC;UACxB;QACJ,CAAC,MACI,IAAII,IAAI,KAAKxB,SAAS,IAAIT,WAAW,CAAC8B,GAAG,CAACD,GAAG,CAAC,EAAE;UACjD;AACpB;AACA;AACA;UACoBD,aAAa,CAACC,GAAG,CAAC;QACtB,CAAC,MACI;UACD;AACpB;AACA;AACA;UACoBtB,SAAS,CAACQ,aAAa,CAACc,GAAG,CAAC,GAAG,IAAI;QACvC;MACJ;MACA;AACZ;AACA;AACA;MACYtB,SAAS,CAACS,QAAQ,GAAGR,IAAI;MACzBD,SAAS,CAACmB,kBAAkB,GAAGF,cAAc;MAC7C;AACZ;AACA;MACY,IAAIjB,SAAS,CAACK,QAAQ,EAAE;QACpBV,eAAe,GAAG;UAAE,GAAGA,eAAe;UAAE,GAAGsB;QAAe,CAAC;MAC/D;MACA,IAAIxC,eAAe,IAAIX,aAAa,CAACiE,qBAAqB,EAAE;QACxDnB,iBAAiB,GAAG,KAAK;MAC7B;MACA;AACZ;AACA;MACY,IAAIA,iBAAiB,KAAK,CAACN,WAAW,IAAIO,oBAAoB,CAAC,EAAE;QAC7D9C,UAAU,CAACiE,IAAI,CAAC,GAAGlB,cAAc,CAAC5C,GAAG,CAAEC,SAAS,KAAM;UAClDA,SAAS,EAAEA,SAAS;UACpBC,OAAO,EAAE;YAAE2B,IAAI;YAAE,GAAG3B;UAAQ;QAChC,CAAC,CAAC,CAAC,CAAC;MACR;IACJ;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIqB,WAAW,CAACwC,IAAI,EAAE;MAClB,MAAMC,iBAAiB,GAAG,CAAC,CAAC;MAC5BzC,WAAW,CAAC0C,OAAO,CAAEb,GAAG,IAAK;QACzB,MAAMc,cAAc,GAAGtE,aAAa,CAACuE,aAAa,CAACf,GAAG,CAAC;QACvD,IAAIc,cAAc,KAAKlC,SAAS,EAAE;UAC9BgC,iBAAiB,CAACZ,GAAG,CAAC,GAAGc,cAAc;QAC3C;MACJ,CAAC,CAAC;MACFrE,UAAU,CAACiE,IAAI,CAAC;QAAE7D,SAAS,EAAE+D;MAAkB,CAAC,CAAC;IACrD;IACA,IAAII,aAAa,GAAGC,OAAO,CAACxE,UAAU,CAACH,MAAM,CAAC;IAC9C,IAAIa,eAAe,KACdY,KAAK,CAACmD,OAAO,KAAK,KAAK,IAAInD,KAAK,CAACmD,OAAO,KAAKnD,KAAK,CAACf,OAAO,CAAC,IAC5D,CAACR,aAAa,CAACyC,sBAAsB,EAAE;MACvC+B,aAAa,GAAG,KAAK;IACzB;IACA7D,eAAe,GAAG,KAAK;IACvB,OAAO6D,aAAa,GAAGhE,OAAO,CAACP,UAAU,CAAC,GAAGC,OAAO,CAACyE,OAAO,CAAC,CAAC;EAClE;EACA;AACJ;AACA;EACI,SAASC,SAASA,CAAC3C,IAAI,EAAEM,QAAQ,EAAEjC,OAAO,EAAE;IACxC,IAAIuE,EAAE;IACN;IACA,IAAIpE,KAAK,CAACwB,IAAI,CAAC,CAACM,QAAQ,KAAKA,QAAQ,EACjC,OAAOrC,OAAO,CAACyE,OAAO,CAAC,CAAC;IAC5B;IACA,CAACE,EAAE,GAAG7E,aAAa,CAAC8E,eAAe,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACR,OAAO,CAAEU,KAAK,IAAK;MAAE,IAAIF,EAAE;MAAE,OAAO,CAACA,EAAE,GAAGE,KAAK,CAACC,cAAc,MAAM,IAAI,IAAIH,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACD,SAAS,CAAC3C,IAAI,EAAEM,QAAQ,CAAC;IAAE,CAAC,CAAC;IAClN9B,KAAK,CAACwB,IAAI,CAAC,CAACM,QAAQ,GAAGA,QAAQ;IAC/B,MAAMtC,UAAU,GAAGoB,cAAc,CAACf,OAAO,EAAE2B,IAAI,CAAC;IAChD,KAAK,MAAMuB,GAAG,IAAI/C,KAAK,EAAE;MACrBA,KAAK,CAAC+C,GAAG,CAAC,CAACd,aAAa,GAAG,CAAC,CAAC;IACjC;IACA,OAAOzC,UAAU;EACrB;EACA,OAAO;IACHoB,cAAc;IACduD,SAAS;IACTzD,kBAAkB;IAClB8D,QAAQ,EAAEA,CAAA,KAAMxE;EACpB,CAAC;AACL;AACA,SAASoC,sBAAsBA,CAACgB,IAAI,EAAED,IAAI,EAAE;EACxC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOA,IAAI,KAAKC,IAAI;EACxB,CAAC,MACI,IAAIZ,KAAK,CAACC,OAAO,CAACU,IAAI,CAAC,EAAE;IAC1B,OAAO,CAACtE,cAAc,CAACsE,IAAI,EAAEC,IAAI,CAAC;EACtC;EACA,OAAO,KAAK;AAChB;AACA,SAASqB,eAAeA,CAAC3C,QAAQ,GAAG,KAAK,EAAE;EACvC,OAAO;IACHA,QAAQ;IACRG,aAAa,EAAE,CAAC,CAAC;IACjBiB,cAAc,EAAE,CAAC,CAAC;IAClBN,kBAAkB,EAAE,CAAC;EACzB,CAAC;AACL;AACA,SAAS3C,WAAWA,CAAA,EAAG;EACnB,OAAO;IACHF,OAAO,EAAE0E,eAAe,CAAC,IAAI,CAAC;IAC9BC,WAAW,EAAED,eAAe,CAAC,CAAC;IAC9BE,UAAU,EAAEF,eAAe,CAAC,CAAC;IAC7BG,QAAQ,EAAEH,eAAe,CAAC,CAAC;IAC3BI,SAAS,EAAEJ,eAAe,CAAC,CAAC;IAC5BK,UAAU,EAAEL,eAAe,CAAC,CAAC;IAC7BM,IAAI,EAAEN,eAAe,CAAC;EAC1B,CAAC;AACL;AAEA,SAASrC,sBAAsB,EAAEtC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}