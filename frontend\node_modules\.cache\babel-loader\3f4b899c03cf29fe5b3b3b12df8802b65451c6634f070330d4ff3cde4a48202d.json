{"ast": null, "code": "import { Coder } from \"./abstract-coder.js\";\nconst Empty = new Uint8Array([]);\n/**\n *  @_ignore\n */\nexport class NullCoder extends Coder {\n  constructor(localName) {\n    super(\"null\", \"\", localName, false);\n  }\n  defaultValue() {\n    return null;\n  }\n  encode(writer, value) {\n    if (value != null) {\n      this._throwError(\"not null\", value);\n    }\n    return writer.writeBytes(Empty);\n  }\n  decode(reader) {\n    reader.readBytes(0);\n    return null;\n  }\n}", "map": {"version": 3, "names": ["Coder", "Empty", "Uint8Array", "NullCoder", "constructor", "localName", "defaultValue", "encode", "writer", "value", "_throwError", "writeBytes", "decode", "reader", "readBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\null.ts"], "sourcesContent": ["import { Coder } from \"./abstract-coder.js\";\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\nconst Empty = new Uint8Array([ ]);\n\n/**\n *  @_ignore\n */\nexport class NullCoder extends Coder {\n\n    constructor(localName: string) {\n        super(\"null\", \"\", localName, false);\n    }\n\n    defaultValue(): null {\n        return null;\n    }\n\n    encode(writer: Writer, value: any): number {\n        if (value != null) { this._throwError(\"not null\", value); }\n        return writer.writeBytes(Empty);\n    }\n\n    decode(reader: Reader): any {\n        reader.readBytes(0);\n        return null;\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAG3C,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAG,CAAC;AAEjC;;;AAGA,OAAM,MAAOC,SAAU,SAAQH,KAAK;EAEhCI,YAAYC,SAAiB;IACzB,KAAK,CAAC,MAAM,EAAE,EAAE,EAAEA,SAAS,EAAE,KAAK,CAAC;EACvC;EAEAC,YAAYA,CAAA;IACR,OAAO,IAAI;EACf;EAEAC,MAAMA,CAACC,MAAc,EAAEC,KAAU;IAC7B,IAAIA,KAAK,IAAI,IAAI,EAAE;MAAE,IAAI,CAACC,WAAW,CAAC,UAAU,EAAED,KAAK,CAAC;;IACxD,OAAOD,MAAM,CAACG,UAAU,CAACV,KAAK,CAAC;EACnC;EAEAW,MAAMA,CAACC,MAAc;IACjBA,MAAM,CAACC,SAAS,CAAC,CAAC,CAAC;IACnB,OAAO,IAAI;EACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}