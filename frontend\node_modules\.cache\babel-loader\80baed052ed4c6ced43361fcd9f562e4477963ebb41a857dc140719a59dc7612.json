{"ast": null, "code": "function calcInset(element, container) {\n  const inset = {\n    x: 0,\n    y: 0\n  };\n  let current = element;\n  while (current && current !== container) {\n    if (current instanceof HTMLElement) {\n      inset.x += current.offsetLeft;\n      inset.y += current.offsetTop;\n      current = current.offsetParent;\n    } else if (current.tagName === \"svg\") {\n      /**\n       * This isn't an ideal approach to measuring the offset of <svg /> tags.\n       * It would be preferable, given they behave like HTMLElements in most ways\n       * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n       * can't use .getBBox() like most SVG elements as these provide the offset\n       * relative to the SVG itself, which for <svg /> is usually 0x0.\n       */\n      const svgBoundingBox = current.getBoundingClientRect();\n      current = current.parentElement;\n      const parentBoundingBox = current.getBoundingClientRect();\n      inset.x += svgBoundingBox.left - parentBoundingBox.left;\n      inset.y += svgBoundingBox.top - parentBoundingBox.top;\n    } else if (current instanceof SVGGraphicsElement) {\n      const {\n        x,\n        y\n      } = current.getBBox();\n      inset.x += x;\n      inset.y += y;\n      let svg = null;\n      let parent = current.parentNode;\n      while (!svg) {\n        if (parent.tagName === \"svg\") {\n          svg = parent;\n        }\n        parent = current.parentNode;\n      }\n      current = svg;\n    } else {\n      break;\n    }\n  }\n  return inset;\n}\nexport { calcInset };", "map": {"version": 3, "names": ["calcInset", "element", "container", "inset", "x", "y", "current", "HTMLElement", "offsetLeft", "offsetTop", "offsetParent", "tagName", "svgBoundingBox", "getBoundingClientRect", "parentElement", "parentBoundingBox", "left", "top", "SVGGraphicsElement", "getBBox", "svg", "parent", "parentNode"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/render/dom/scroll/offsets/inset.mjs"], "sourcesContent": ["function calcInset(element, container) {\n    const inset = { x: 0, y: 0 };\n    let current = element;\n    while (current && current !== container) {\n        if (current instanceof HTMLElement) {\n            inset.x += current.offsetLeft;\n            inset.y += current.offsetTop;\n            current = current.offsetParent;\n        }\n        else if (current.tagName === \"svg\") {\n            /**\n             * This isn't an ideal approach to measuring the offset of <svg /> tags.\n             * It would be preferable, given they behave like HTMLElements in most ways\n             * to use offsetLeft/Top. But these don't exist on <svg />. Likewise we\n             * can't use .getBBox() like most SVG elements as these provide the offset\n             * relative to the SVG itself, which for <svg /> is usually 0x0.\n             */\n            const svgBoundingBox = current.getBoundingClientRect();\n            current = current.parentElement;\n            const parentBoundingBox = current.getBoundingClientRect();\n            inset.x += svgBoundingBox.left - parentBoundingBox.left;\n            inset.y += svgBoundingBox.top - parentBoundingBox.top;\n        }\n        else if (current instanceof SVGGraphicsElement) {\n            const { x, y } = current.getBBox();\n            inset.x += x;\n            inset.y += y;\n            let svg = null;\n            let parent = current.parentNode;\n            while (!svg) {\n                if (parent.tagName === \"svg\") {\n                    svg = parent;\n                }\n                parent = current.parentNode;\n            }\n            current = svg;\n        }\n        else {\n            break;\n        }\n    }\n    return inset;\n}\n\nexport { calcInset };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,OAAO,EAAEC,SAAS,EAAE;EACnC,MAAMC,KAAK,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAC5B,IAAIC,OAAO,GAAGL,OAAO;EACrB,OAAOK,OAAO,IAAIA,OAAO,KAAKJ,SAAS,EAAE;IACrC,IAAII,OAAO,YAAYC,WAAW,EAAE;MAChCJ,KAAK,CAACC,CAAC,IAAIE,OAAO,CAACE,UAAU;MAC7BL,KAAK,CAACE,CAAC,IAAIC,OAAO,CAACG,SAAS;MAC5BH,OAAO,GAAGA,OAAO,CAACI,YAAY;IAClC,CAAC,MACI,IAAIJ,OAAO,CAACK,OAAO,KAAK,KAAK,EAAE;MAChC;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,cAAc,GAAGN,OAAO,CAACO,qBAAqB,CAAC,CAAC;MACtDP,OAAO,GAAGA,OAAO,CAACQ,aAAa;MAC/B,MAAMC,iBAAiB,GAAGT,OAAO,CAACO,qBAAqB,CAAC,CAAC;MACzDV,KAAK,CAACC,CAAC,IAAIQ,cAAc,CAACI,IAAI,GAAGD,iBAAiB,CAACC,IAAI;MACvDb,KAAK,CAACE,CAAC,IAAIO,cAAc,CAACK,GAAG,GAAGF,iBAAiB,CAACE,GAAG;IACzD,CAAC,MACI,IAAIX,OAAO,YAAYY,kBAAkB,EAAE;MAC5C,MAAM;QAAEd,CAAC;QAAEC;MAAE,CAAC,GAAGC,OAAO,CAACa,OAAO,CAAC,CAAC;MAClChB,KAAK,CAACC,CAAC,IAAIA,CAAC;MACZD,KAAK,CAACE,CAAC,IAAIA,CAAC;MACZ,IAAIe,GAAG,GAAG,IAAI;MACd,IAAIC,MAAM,GAAGf,OAAO,CAACgB,UAAU;MAC/B,OAAO,CAACF,GAAG,EAAE;QACT,IAAIC,MAAM,CAACV,OAAO,KAAK,KAAK,EAAE;UAC1BS,GAAG,GAAGC,MAAM;QAChB;QACAA,MAAM,GAAGf,OAAO,CAACgB,UAAU;MAC/B;MACAhB,OAAO,GAAGc,GAAG;IACjB,CAAC,MACI;MACD;IACJ;EACJ;EACA,OAAOjB,KAAK;AAChB;AAEA,SAASH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}