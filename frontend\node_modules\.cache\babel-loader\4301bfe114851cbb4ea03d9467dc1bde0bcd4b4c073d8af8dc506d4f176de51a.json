{"ast": null, "code": "import { WordlistOwl } from \"./wordlist-owl.js\";\nimport { decodeOwlA } from \"./decode-owla.js\";\n/**\n *  An OWL-A format Wordlist extends the OWL format to add an\n *  overlay onto an OWL format Wordlist to support diacritic\n *  marks.\n *\n *  This class is generally not useful to most developers as\n *  it is used mainly internally to keep Wordlists for languages\n *  based on latin-1 small.\n *\n *  If necessary, there are tools within the ``generation/`` folder\n *  to create the necessary data.\n */\nexport class WordlistOwlA extends WordlistOwl {\n  #accent;\n  /**\n   *  Creates a new Wordlist for %%locale%% using the OWLA %%data%%\n   *  and %%accent%% data and validated against the %%checksum%%.\n   */\n  constructor(locale, data, accent, checksum) {\n    super(locale, data, checksum);\n    this.#accent = accent;\n  }\n  /**\n   *  The OWLA-encoded accent data.\n   */\n  get _accent() {\n    return this.#accent;\n  }\n  /**\n   *  Decode all the words for the wordlist.\n   */\n  _decodeWords() {\n    return decodeOwlA(this._data, this._accent);\n  }\n}", "map": {"version": 3, "names": ["WordlistOwl", "decodeOwlA", "WordlistOwlA", "accent", "constructor", "locale", "data", "checksum", "_accent", "_decodeWords", "_data"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\wordlist-owla.ts"], "sourcesContent": ["\nimport { WordlistOwl } from \"./wordlist-owl.js\";\nimport { decodeOwlA } from \"./decode-owla.js\";\n\n/**\n *  An OWL-A format Wordlist extends the OWL format to add an\n *  overlay onto an OWL format Wordlist to support diacritic\n *  marks.\n *\n *  This class is generally not useful to most developers as\n *  it is used mainly internally to keep Wordlists for languages\n *  based on latin-1 small.\n *\n *  If necessary, there are tools within the ``generation/`` folder\n *  to create the necessary data.\n */\nexport class WordlistOwlA extends WordlistOwl {\n    #accent: string;\n\n\n    /**\n     *  Creates a new Wordlist for %%locale%% using the OWLA %%data%%\n     *  and %%accent%% data and validated against the %%checksum%%.\n     */\n    constructor(locale: string, data: string, accent: string, checksum: string) {\n        super(locale, data, checksum);\n        this.#accent = accent;\n    }\n\n    /**\n     *  The OWLA-encoded accent data.\n     */\n    get _accent(): string { return this.#accent; }\n\n    /**\n     *  Decode all the words for the wordlist.\n     */\n    _decodeWords(): Array<string> {\n        return decodeOwlA(this._data, this._accent);\n    }\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,UAAU,QAAQ,kBAAkB;AAE7C;;;;;;;;;;;;AAYA,OAAM,MAAOC,YAAa,SAAQF,WAAW;EACzC,CAAAG,MAAO;EAGP;;;;EAIAC,YAAYC,MAAc,EAAEC,IAAY,EAAEH,MAAc,EAAEI,QAAgB;IACtE,KAAK,CAACF,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IAC7B,IAAI,CAAC,CAAAJ,MAAO,GAAGA,MAAM;EACzB;EAEA;;;EAGA,IAAIK,OAAOA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAL,MAAO;EAAE;EAE7C;;;EAGAM,YAAYA,CAAA;IACR,OAAOR,UAAU,CAAC,IAAI,CAACS,KAAK,EAAE,IAAI,CAACF,OAAO,CAAC;EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}