{"ast": null, "code": "/**\n *  Each state-changing operation on Ethereum requires a transaction.\n *\n *  @_section api/transaction:Transactions  [about-transactions]\n */\nnull;\nexport { accessListify } from \"./accesslist.js\";\nexport { authorizationify } from \"./authorization.js\";\nexport { computeAddress, recoverAddress } from \"./address.js\";\nexport { Transaction } from \"./transaction.js\";", "map": {"version": 3, "names": ["accessListify", "authorizationify", "computeAddress", "recoverAddress", "Transaction"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\transaction\\index.ts"], "sourcesContent": ["/**\n *  Each state-changing operation on Ethereum requires a transaction.\n *\n *  @_section api/transaction:Transactions  [about-transactions]\n */\n\nnull;\n\nimport type { BigNumberish } from \"../utils/maths.js\";\nimport type { Signature, SignatureLike } from \"../crypto/index.js\";\n\n/**\n *  A single [[AccessList]] entry of storage keys (slots) for an address.\n */\nexport type AccessListEntry = { address: string, storageKeys: Array<string> };\n\n/**\n *  An ordered collection of [[AccessList]] entries.\n */\nexport type AccessList = Array<AccessListEntry>;\n\n/**\n *  Any ethers-supported access list structure.\n */\nexport type AccessListish = AccessList |\n                            Array<[ string, Array<string> ]> |\n                            Record<string, Array<string>>;\n\n// Keep here?\nexport interface Authorization {\n    address: string;\n    nonce: bigint;\n    chainId: bigint;\n    signature: Signature;\n}\n\nexport type AuthorizationLike = {\n    address: string;\n    nonce: BigNumberish;\n    chainId: BigNumberish;\n    signature: SignatureLike\n};\n\nexport { accessListify } from \"./accesslist.js\";\nexport { authorizationify } from \"./authorization.js\";\nexport { computeAddress, recoverAddress } from \"./address.js\";\nexport { Transaction } from \"./transaction.js\";\n\nexport type {\n    Blob, BlobLike, KzgLibrary, KzgLibraryLike, TransactionLike\n} from \"./transaction.js\";\n"], "mappings": "AAAA;;;;;AAMA,IAAI;AAqCJ,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,EAAEC,cAAc,QAAQ,cAAc;AAC7D,SAASC,WAAW,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}