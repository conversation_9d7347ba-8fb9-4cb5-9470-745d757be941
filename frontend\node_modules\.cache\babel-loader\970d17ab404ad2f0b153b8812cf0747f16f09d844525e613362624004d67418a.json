{"ast": null, "code": "/**\n *  The available providers should suffice for most developers purposes,\n *  but the [[AbstractProvider]] class has many features which enable\n *  sub-classing it for specific purposes.\n *\n *  @_section: api/providers/abstract-provider: Subclassing Provider  [abstract-provider]\n */\n// @TODO\n// Event coalescence\n//   When we register an event with an async value (e.g. address is a Signer\n//   or ENS name), we need to add it immeidately for the Event API, but also\n//   need time to resolve the address. Upon resolving the address, we need to\n//   migrate the listener to the static event. We also need to maintain a map\n//   of Signer/ENS name to address so we can sync respond to listenerCount.\nimport { getAddress, resolveAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { namehash } from \"../hash/index.js\";\nimport { Transaction } from \"../transaction/index.js\";\nimport { concat, dataLength, dataSlice, hexlify, isHexString, getBigInt, getBytes, getNumber, isCallException, isError, makeError, assert, assertArgument, FetchRequest, toBeArray, toQuantity, defineProperties, EventPayload, resolveProperties, toUtf8String } from \"../utils/index.js\";\nimport { EnsResolver } from \"./ens-resolver.js\";\nimport { formatBlock, formatLog, formatTransactionReceipt, formatTransactionResponse } from \"./format.js\";\nimport { Network } from \"./network.js\";\nimport { copyRequest, Block, FeeData, Log, TransactionReceipt, TransactionResponse } from \"./provider.js\";\nimport { PollingBlockSubscriber, PollingBlockTagSubscriber, PollingEventSubscriber, PollingOrphanSubscriber, PollingTransactionSubscriber } from \"./subscriber-polling.js\";\n// Constants\nconst BN_2 = BigInt(2);\nconst MAX_CCIP_REDIRECTS = 10;\nfunction isPromise(value) {\n  return value && typeof value.then === \"function\";\n}\nfunction getTag(prefix, value) {\n  return prefix + \":\" + JSON.stringify(value, (k, v) => {\n    if (v == null) {\n      return \"null\";\n    }\n    if (typeof v === \"bigint\") {\n      return `bigint:${v.toString()}`;\n    }\n    if (typeof v === \"string\") {\n      return v.toLowerCase();\n    }\n    // Sort object keys\n    if (typeof v === \"object\" && !Array.isArray(v)) {\n      const keys = Object.keys(v);\n      keys.sort();\n      return keys.reduce((accum, key) => {\n        accum[key] = v[key];\n        return accum;\n      }, {});\n    }\n    return v;\n  });\n}\n/**\n *  An **UnmanagedSubscriber** is useful for events which do not require\n *  any additional management, such as ``\"debug\"`` which only requires\n *  emit in synchronous event loop triggered calls.\n */\nexport class UnmanagedSubscriber {\n  /**\n   *  The name fof the event.\n   */\n  name;\n  /**\n   *  Create a new UnmanagedSubscriber with %%name%%.\n   */\n  constructor(name) {\n    defineProperties(this, {\n      name\n    });\n  }\n  start() {}\n  stop() {}\n  pause(dropWhilePaused) {}\n  resume() {}\n}\nfunction copy(value) {\n  return JSON.parse(JSON.stringify(value));\n}\nfunction concisify(items) {\n  items = Array.from(new Set(items).values());\n  items.sort();\n  return items;\n}\nasync function getSubscription(_event, provider) {\n  if (_event == null) {\n    throw new Error(\"invalid event\");\n  }\n  // Normalize topic array info an EventFilter\n  if (Array.isArray(_event)) {\n    _event = {\n      topics: _event\n    };\n  }\n  if (typeof _event === \"string\") {\n    switch (_event) {\n      case \"block\":\n      case \"debug\":\n      case \"error\":\n      case \"finalized\":\n      case \"network\":\n      case \"pending\":\n      case \"safe\":\n        {\n          return {\n            type: _event,\n            tag: _event\n          };\n        }\n    }\n  }\n  if (isHexString(_event, 32)) {\n    const hash = _event.toLowerCase();\n    return {\n      type: \"transaction\",\n      tag: getTag(\"tx\", {\n        hash\n      }),\n      hash\n    };\n  }\n  if (_event.orphan) {\n    const event = _event;\n    // @TODO: Should lowercase and whatnot things here instead of copy...\n    return {\n      type: \"orphan\",\n      tag: getTag(\"orphan\", event),\n      filter: copy(event)\n    };\n  }\n  if (_event.address || _event.topics) {\n    const event = _event;\n    const filter = {\n      topics: (event.topics || []).map(t => {\n        if (t == null) {\n          return null;\n        }\n        if (Array.isArray(t)) {\n          return concisify(t.map(t => t.toLowerCase()));\n        }\n        return t.toLowerCase();\n      })\n    };\n    if (event.address) {\n      const addresses = [];\n      const promises = [];\n      const addAddress = addr => {\n        if (isHexString(addr)) {\n          addresses.push(addr);\n        } else {\n          promises.push((async () => {\n            addresses.push(await resolveAddress(addr, provider));\n          })());\n        }\n      };\n      if (Array.isArray(event.address)) {\n        event.address.forEach(addAddress);\n      } else {\n        addAddress(event.address);\n      }\n      if (promises.length) {\n        await Promise.all(promises);\n      }\n      filter.address = concisify(addresses.map(a => a.toLowerCase()));\n    }\n    return {\n      filter,\n      tag: getTag(\"event\", filter),\n      type: \"event\"\n    };\n  }\n  assertArgument(false, \"unknown ProviderEvent\", \"event\", _event);\n}\nfunction getTime() {\n  return new Date().getTime();\n}\nconst defaultOptions = {\n  cacheTimeout: 250,\n  pollingInterval: 4000\n};\n/**\n *  An **AbstractProvider** provides a base class for other sub-classes to\n *  implement the [[Provider]] API by normalizing input arguments and\n *  formatting output results as well as tracking events for consistent\n *  behaviour on an eventually-consistent network.\n */\nexport class AbstractProvider {\n  #subs;\n  #plugins;\n  // null=unpaused, true=paused+dropWhilePaused, false=paused\n  #pausedState;\n  #destroyed;\n  #networkPromise;\n  #anyNetwork;\n  #performCache;\n  // The most recent block number if running an event or -1 if no \"block\" event\n  #lastBlockNumber;\n  #nextTimer;\n  #timers;\n  #disableCcipRead;\n  #options;\n  /**\n   *  Create a new **AbstractProvider** connected to %%network%%, or\n   *  use the various network detection capabilities to discover the\n   *  [[Network]] if necessary.\n   */\n  constructor(_network, options) {\n    this.#options = Object.assign({}, defaultOptions, options || {});\n    if (_network === \"any\") {\n      this.#anyNetwork = true;\n      this.#networkPromise = null;\n    } else if (_network) {\n      const network = Network.from(_network);\n      this.#anyNetwork = false;\n      this.#networkPromise = Promise.resolve(network);\n      setTimeout(() => {\n        this.emit(\"network\", network, null);\n      }, 0);\n    } else {\n      this.#anyNetwork = false;\n      this.#networkPromise = null;\n    }\n    this.#lastBlockNumber = -1;\n    this.#performCache = new Map();\n    this.#subs = new Map();\n    this.#plugins = new Map();\n    this.#pausedState = null;\n    this.#destroyed = false;\n    this.#nextTimer = 1;\n    this.#timers = new Map();\n    this.#disableCcipRead = false;\n  }\n  get pollingInterval() {\n    return this.#options.pollingInterval;\n  }\n  /**\n   *  Returns ``this``, to allow an **AbstractProvider** to implement\n   *  the [[ContractRunner]] interface.\n   */\n  get provider() {\n    return this;\n  }\n  /**\n   *  Returns all the registered plug-ins.\n   */\n  get plugins() {\n    return Array.from(this.#plugins.values());\n  }\n  /**\n   *  Attach a new plug-in.\n   */\n  attachPlugin(plugin) {\n    if (this.#plugins.get(plugin.name)) {\n      throw new Error(`cannot replace existing plugin: ${plugin.name} `);\n    }\n    this.#plugins.set(plugin.name, plugin.connect(this));\n    return this;\n  }\n  /**\n   *  Get a plugin by name.\n   */\n  getPlugin(name) {\n    return this.#plugins.get(name) || null;\n  }\n  /**\n   *  Prevent any CCIP-read operation, regardless of whether requested\n   *  in a [[call]] using ``enableCcipRead``.\n   */\n  get disableCcipRead() {\n    return this.#disableCcipRead;\n  }\n  set disableCcipRead(value) {\n    this.#disableCcipRead = !!value;\n  }\n  // Shares multiple identical requests made during the same 250ms\n  async #perform(req) {\n    const timeout = this.#options.cacheTimeout;\n    // Caching disabled\n    if (timeout < 0) {\n      return await this._perform(req);\n    }\n    // Create a tag\n    const tag = getTag(req.method, req);\n    let perform = this.#performCache.get(tag);\n    if (!perform) {\n      perform = this._perform(req);\n      this.#performCache.set(tag, perform);\n      setTimeout(() => {\n        if (this.#performCache.get(tag) === perform) {\n          this.#performCache.delete(tag);\n        }\n      }, timeout);\n    }\n    return await perform;\n  }\n  /**\n   *  Resolves to the data for executing the CCIP-read operations.\n   */\n  async ccipReadFetch(tx, calldata, urls) {\n    if (this.disableCcipRead || urls.length === 0 || tx.to == null) {\n      return null;\n    }\n    const sender = tx.to.toLowerCase();\n    const data = calldata.toLowerCase();\n    const errorMessages = [];\n    for (let i = 0; i < urls.length; i++) {\n      const url = urls[i];\n      // URL expansion\n      const href = url.replace(\"{sender}\", sender).replace(\"{data}\", data);\n      // If no {data} is present, use POST; otherwise GET\n      //const json: string | null = (url.indexOf(\"{data}\") >= 0) ? null: JSON.stringify({ data, sender });\n      //const result = await fetchJson({ url: href, errorPassThrough: true }, json, (value, response) => {\n      //    value.status = response.statusCode;\n      //    return value;\n      //});\n      const request = new FetchRequest(href);\n      if (url.indexOf(\"{data}\") === -1) {\n        request.body = {\n          data,\n          sender\n        };\n      }\n      this.emit(\"debug\", {\n        action: \"sendCcipReadFetchRequest\",\n        request,\n        index: i,\n        urls\n      });\n      let errorMessage = \"unknown error\";\n      // Fetch the resource...\n      let resp;\n      try {\n        resp = await request.send();\n      } catch (error) {\n        // ...low-level fetch error (missing host, bad SSL, etc.),\n        // so try next URL\n        errorMessages.push(error.message);\n        this.emit(\"debug\", {\n          action: \"receiveCcipReadFetchError\",\n          request,\n          result: {\n            error\n          }\n        });\n        continue;\n      }\n      try {\n        const result = resp.bodyJson;\n        if (result.data) {\n          this.emit(\"debug\", {\n            action: \"receiveCcipReadFetchResult\",\n            request,\n            result\n          });\n          return result.data;\n        }\n        if (result.message) {\n          errorMessage = result.message;\n        }\n        this.emit(\"debug\", {\n          action: \"receiveCcipReadFetchError\",\n          request,\n          result\n        });\n      } catch (error) {}\n      // 4xx indicates the result is not present; stop\n      assert(resp.statusCode < 400 || resp.statusCode >= 500, `response not found during CCIP fetch: ${errorMessage}`, \"OFFCHAIN_FAULT\", {\n        reason: \"404_MISSING_RESOURCE\",\n        transaction: tx,\n        info: {\n          url,\n          errorMessage\n        }\n      });\n      // 5xx indicates server issue; try the next url\n      errorMessages.push(errorMessage);\n    }\n    assert(false, `error encountered during CCIP fetch: ${errorMessages.map(m => JSON.stringify(m)).join(\", \")}`, \"OFFCHAIN_FAULT\", {\n      reason: \"500_SERVER_ERROR\",\n      transaction: tx,\n      info: {\n        urls,\n        errorMessages\n      }\n    });\n  }\n  /**\n   *  Provides the opportunity for a sub-class to wrap a block before\n   *  returning it, to add additional properties or an alternate\n   *  sub-class of [[Block]].\n   */\n  _wrapBlock(value, network) {\n    return new Block(formatBlock(value), this);\n  }\n  /**\n   *  Provides the opportunity for a sub-class to wrap a log before\n   *  returning it, to add additional properties or an alternate\n   *  sub-class of [[Log]].\n   */\n  _wrapLog(value, network) {\n    return new Log(formatLog(value), this);\n  }\n  /**\n   *  Provides the opportunity for a sub-class to wrap a transaction\n   *  receipt before returning it, to add additional properties or an\n   *  alternate sub-class of [[TransactionReceipt]].\n   */\n  _wrapTransactionReceipt(value, network) {\n    return new TransactionReceipt(formatTransactionReceipt(value), this);\n  }\n  /**\n   *  Provides the opportunity for a sub-class to wrap a transaction\n   *  response before returning it, to add additional properties or an\n   *  alternate sub-class of [[TransactionResponse]].\n   */\n  _wrapTransactionResponse(tx, network) {\n    return new TransactionResponse(formatTransactionResponse(tx), this);\n  }\n  /**\n   *  Resolves to the Network, forcing a network detection using whatever\n   *  technique the sub-class requires.\n   *\n   *  Sub-classes **must** override this.\n   */\n  _detectNetwork() {\n    assert(false, \"sub-classes must implement this\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"_detectNetwork\"\n    });\n  }\n  /**\n   *  Sub-classes should use this to perform all built-in operations. All\n   *  methods sanitizes and normalizes the values passed into this.\n   *\n   *  Sub-classes **must** override this.\n   */\n  async _perform(req) {\n    assert(false, `unsupported method: ${req.method}`, \"UNSUPPORTED_OPERATION\", {\n      operation: req.method,\n      info: req\n    });\n  }\n  // State\n  async getBlockNumber() {\n    const blockNumber = getNumber(await this.#perform({\n      method: \"getBlockNumber\"\n    }), \"%response\");\n    if (this.#lastBlockNumber >= 0) {\n      this.#lastBlockNumber = blockNumber;\n    }\n    return blockNumber;\n  }\n  /**\n   *  Returns or resolves to the address for %%address%%, resolving ENS\n   *  names and [[Addressable]] objects and returning if already an\n   *  address.\n   */\n  _getAddress(address) {\n    return resolveAddress(address, this);\n  }\n  /**\n   *  Returns or resolves to a valid block tag for %%blockTag%%, resolving\n   *  negative values and returning if already a valid block tag.\n   */\n  _getBlockTag(blockTag) {\n    if (blockTag == null) {\n      return \"latest\";\n    }\n    switch (blockTag) {\n      case \"earliest\":\n        return \"0x0\";\n      case \"finalized\":\n      case \"latest\":\n      case \"pending\":\n      case \"safe\":\n        return blockTag;\n    }\n    if (isHexString(blockTag)) {\n      if (isHexString(blockTag, 32)) {\n        return blockTag;\n      }\n      return toQuantity(blockTag);\n    }\n    if (typeof blockTag === \"bigint\") {\n      blockTag = getNumber(blockTag, \"blockTag\");\n    }\n    if (typeof blockTag === \"number\") {\n      if (blockTag >= 0) {\n        return toQuantity(blockTag);\n      }\n      if (this.#lastBlockNumber >= 0) {\n        return toQuantity(this.#lastBlockNumber + blockTag);\n      }\n      return this.getBlockNumber().then(b => toQuantity(b + blockTag));\n    }\n    assertArgument(false, \"invalid blockTag\", \"blockTag\", blockTag);\n  }\n  /**\n   *  Returns or resolves to a filter for %%filter%%, resolving any ENS\n   *  names or [[Addressable]] object and returning if already a valid\n   *  filter.\n   */\n  _getFilter(filter) {\n    // Create a canonical representation of the topics\n    const topics = (filter.topics || []).map(t => {\n      if (t == null) {\n        return null;\n      }\n      if (Array.isArray(t)) {\n        return concisify(t.map(t => t.toLowerCase()));\n      }\n      return t.toLowerCase();\n    });\n    const blockHash = \"blockHash\" in filter ? filter.blockHash : undefined;\n    const resolve = (_address, fromBlock, toBlock) => {\n      let address = undefined;\n      switch (_address.length) {\n        case 0:\n          break;\n        case 1:\n          address = _address[0];\n          break;\n        default:\n          _address.sort();\n          address = _address;\n      }\n      if (blockHash) {\n        if (fromBlock != null || toBlock != null) {\n          throw new Error(\"invalid filter\");\n        }\n      }\n      const filter = {};\n      if (address) {\n        filter.address = address;\n      }\n      if (topics.length) {\n        filter.topics = topics;\n      }\n      if (fromBlock) {\n        filter.fromBlock = fromBlock;\n      }\n      if (toBlock) {\n        filter.toBlock = toBlock;\n      }\n      if (blockHash) {\n        filter.blockHash = blockHash;\n      }\n      return filter;\n    };\n    // Addresses could be async (ENS names or Addressables)\n    let address = [];\n    if (filter.address) {\n      if (Array.isArray(filter.address)) {\n        for (const addr of filter.address) {\n          address.push(this._getAddress(addr));\n        }\n      } else {\n        address.push(this._getAddress(filter.address));\n      }\n    }\n    let fromBlock = undefined;\n    if (\"fromBlock\" in filter) {\n      fromBlock = this._getBlockTag(filter.fromBlock);\n    }\n    let toBlock = undefined;\n    if (\"toBlock\" in filter) {\n      toBlock = this._getBlockTag(filter.toBlock);\n    }\n    if (address.filter(a => typeof a !== \"string\").length || fromBlock != null && typeof fromBlock !== \"string\" || toBlock != null && typeof toBlock !== \"string\") {\n      return Promise.all([Promise.all(address), fromBlock, toBlock]).then(result => {\n        return resolve(result[0], result[1], result[2]);\n      });\n    }\n    return resolve(address, fromBlock, toBlock);\n  }\n  /**\n   *  Returns or resolves to a transaction for %%request%%, resolving\n   *  any ENS names or [[Addressable]] and returning if already a valid\n   *  transaction.\n   */\n  _getTransactionRequest(_request) {\n    const request = copyRequest(_request);\n    const promises = [];\n    [\"to\", \"from\"].forEach(key => {\n      if (request[key] == null) {\n        return;\n      }\n      const addr = resolveAddress(request[key], this);\n      if (isPromise(addr)) {\n        promises.push(async function () {\n          request[key] = await addr;\n        }());\n      } else {\n        request[key] = addr;\n      }\n    });\n    if (request.blockTag != null) {\n      const blockTag = this._getBlockTag(request.blockTag);\n      if (isPromise(blockTag)) {\n        promises.push(async function () {\n          request.blockTag = await blockTag;\n        }());\n      } else {\n        request.blockTag = blockTag;\n      }\n    }\n    if (promises.length) {\n      return async function () {\n        await Promise.all(promises);\n        return request;\n      }();\n    }\n    return request;\n  }\n  async getNetwork() {\n    // No explicit network was set and this is our first time\n    if (this.#networkPromise == null) {\n      // Detect the current network (shared with all calls)\n      const detectNetwork = (async () => {\n        try {\n          const network = await this._detectNetwork();\n          this.emit(\"network\", network, null);\n          return network;\n        } catch (error) {\n          if (this.#networkPromise === detectNetwork) {\n            this.#networkPromise = null;\n          }\n          throw error;\n        }\n      })();\n      this.#networkPromise = detectNetwork;\n      return (await detectNetwork).clone();\n    }\n    const networkPromise = this.#networkPromise;\n    const [expected, actual] = await Promise.all([networkPromise, this._detectNetwork() // The actual connected network\n    ]);\n    if (expected.chainId !== actual.chainId) {\n      if (this.#anyNetwork) {\n        // The \"any\" network can change, so notify listeners\n        this.emit(\"network\", actual, expected);\n        // Update the network if something else hasn't already changed it\n        if (this.#networkPromise === networkPromise) {\n          this.#networkPromise = Promise.resolve(actual);\n        }\n      } else {\n        // Otherwise, we do not allow changes to the underlying network\n        assert(false, `network changed: ${expected.chainId} => ${actual.chainId} `, \"NETWORK_ERROR\", {\n          event: \"changed\"\n        });\n      }\n    }\n    return expected.clone();\n  }\n  async getFeeData() {\n    const network = await this.getNetwork();\n    const getFeeDataFunc = async () => {\n      const {\n        _block,\n        gasPrice,\n        priorityFee\n      } = await resolveProperties({\n        _block: this.#getBlock(\"latest\", false),\n        gasPrice: (async () => {\n          try {\n            const value = await this.#perform({\n              method: \"getGasPrice\"\n            });\n            return getBigInt(value, \"%response\");\n          } catch (error) {}\n          return null;\n        })(),\n        priorityFee: (async () => {\n          try {\n            const value = await this.#perform({\n              method: \"getPriorityFee\"\n            });\n            return getBigInt(value, \"%response\");\n          } catch (error) {}\n          return null;\n        })()\n      });\n      let maxFeePerGas = null;\n      let maxPriorityFeePerGas = null;\n      // These are the recommended EIP-1559 heuristics for fee data\n      const block = this._wrapBlock(_block, network);\n      if (block && block.baseFeePerGas) {\n        maxPriorityFeePerGas = priorityFee != null ? priorityFee : BigInt(\"1000000000\");\n        maxFeePerGas = block.baseFeePerGas * BN_2 + maxPriorityFeePerGas;\n      }\n      return new FeeData(gasPrice, maxFeePerGas, maxPriorityFeePerGas);\n    };\n    // Check for a FeeDataNetWorkPlugin\n    const plugin = network.getPlugin(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\");\n    if (plugin) {\n      const req = new FetchRequest(plugin.url);\n      const feeData = await plugin.processFunc(getFeeDataFunc, this, req);\n      return new FeeData(feeData.gasPrice, feeData.maxFeePerGas, feeData.maxPriorityFeePerGas);\n    }\n    return await getFeeDataFunc();\n  }\n  async estimateGas(_tx) {\n    let tx = this._getTransactionRequest(_tx);\n    if (isPromise(tx)) {\n      tx = await tx;\n    }\n    return getBigInt(await this.#perform({\n      method: \"estimateGas\",\n      transaction: tx\n    }), \"%response\");\n  }\n  async #call(tx, blockTag, attempt) {\n    assert(attempt < MAX_CCIP_REDIRECTS, \"CCIP read exceeded maximum redirections\", \"OFFCHAIN_FAULT\", {\n      reason: \"TOO_MANY_REDIRECTS\",\n      transaction: Object.assign({}, tx, {\n        blockTag,\n        enableCcipRead: true\n      })\n    });\n    // This came in as a PerformActionTransaction, so to/from are safe; we can cast\n    const transaction = copyRequest(tx);\n    try {\n      return hexlify(await this._perform({\n        method: \"call\",\n        transaction,\n        blockTag\n      }));\n    } catch (error) {\n      // CCIP Read OffchainLookup\n      if (!this.disableCcipRead && isCallException(error) && error.data && attempt >= 0 && blockTag === \"latest\" && transaction.to != null && dataSlice(error.data, 0, 4) === \"0x556f1830\") {\n        const data = error.data;\n        const txSender = await resolveAddress(transaction.to, this);\n        // Parse the CCIP Read Arguments\n        let ccipArgs;\n        try {\n          ccipArgs = parseOffchainLookup(dataSlice(error.data, 4));\n        } catch (error) {\n          assert(false, error.message, \"OFFCHAIN_FAULT\", {\n            reason: \"BAD_DATA\",\n            transaction,\n            info: {\n              data\n            }\n          });\n        }\n        // Check the sender of the OffchainLookup matches the transaction\n        assert(ccipArgs.sender.toLowerCase() === txSender.toLowerCase(), \"CCIP Read sender mismatch\", \"CALL_EXCEPTION\", {\n          action: \"call\",\n          data,\n          reason: \"OffchainLookup\",\n          transaction: transaction,\n          invocation: null,\n          revert: {\n            signature: \"OffchainLookup(address,string[],bytes,bytes4,bytes)\",\n            name: \"OffchainLookup\",\n            args: ccipArgs.errorArgs\n          }\n        });\n        const ccipResult = await this.ccipReadFetch(transaction, ccipArgs.calldata, ccipArgs.urls);\n        assert(ccipResult != null, \"CCIP Read failed to fetch data\", \"OFFCHAIN_FAULT\", {\n          reason: \"FETCH_FAILED\",\n          transaction,\n          info: {\n            data: error.data,\n            errorArgs: ccipArgs.errorArgs\n          }\n        });\n        const tx = {\n          to: txSender,\n          data: concat([ccipArgs.selector, encodeBytes([ccipResult, ccipArgs.extraData])])\n        };\n        this.emit(\"debug\", {\n          action: \"sendCcipReadCall\",\n          transaction: tx\n        });\n        try {\n          const result = await this.#call(tx, blockTag, attempt + 1);\n          this.emit(\"debug\", {\n            action: \"receiveCcipReadCallResult\",\n            transaction: Object.assign({}, tx),\n            result\n          });\n          return result;\n        } catch (error) {\n          this.emit(\"debug\", {\n            action: \"receiveCcipReadCallError\",\n            transaction: Object.assign({}, tx),\n            error\n          });\n          throw error;\n        }\n      }\n      throw error;\n    }\n  }\n  async #checkNetwork(promise) {\n    const {\n      value\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      value: promise\n    });\n    return value;\n  }\n  async call(_tx) {\n    const {\n      tx,\n      blockTag\n    } = await resolveProperties({\n      tx: this._getTransactionRequest(_tx),\n      blockTag: this._getBlockTag(_tx.blockTag)\n    });\n    return await this.#checkNetwork(this.#call(tx, blockTag, _tx.enableCcipRead ? 0 : -1));\n  }\n  // Account\n  async #getAccountValue(request, _address, _blockTag) {\n    let address = this._getAddress(_address);\n    let blockTag = this._getBlockTag(_blockTag);\n    if (typeof address !== \"string\" || typeof blockTag !== \"string\") {\n      [address, blockTag] = await Promise.all([address, blockTag]);\n    }\n    return await this.#checkNetwork(this.#perform(Object.assign(request, {\n      address,\n      blockTag\n    })));\n  }\n  async getBalance(address, blockTag) {\n    return getBigInt(await this.#getAccountValue({\n      method: \"getBalance\"\n    }, address, blockTag), \"%response\");\n  }\n  async getTransactionCount(address, blockTag) {\n    return getNumber(await this.#getAccountValue({\n      method: \"getTransactionCount\"\n    }, address, blockTag), \"%response\");\n  }\n  async getCode(address, blockTag) {\n    return hexlify(await this.#getAccountValue({\n      method: \"getCode\"\n    }, address, blockTag));\n  }\n  async getStorage(address, _position, blockTag) {\n    const position = getBigInt(_position, \"position\");\n    return hexlify(await this.#getAccountValue({\n      method: \"getStorage\",\n      position\n    }, address, blockTag));\n  }\n  // Write\n  async broadcastTransaction(signedTx) {\n    const {\n      blockNumber,\n      hash,\n      network\n    } = await resolveProperties({\n      blockNumber: this.getBlockNumber(),\n      hash: this._perform({\n        method: \"broadcastTransaction\",\n        signedTransaction: signedTx\n      }),\n      network: this.getNetwork()\n    });\n    const tx = Transaction.from(signedTx);\n    if (tx.hash !== hash) {\n      throw new Error(\"@TODO: the returned hash did not match\");\n    }\n    return this._wrapTransactionResponse(tx, network).replaceableTransaction(blockNumber);\n  }\n  async #getBlock(block, includeTransactions) {\n    // @TODO: Add CustomBlockPlugin check\n    if (isHexString(block, 32)) {\n      return await this.#perform({\n        method: \"getBlock\",\n        blockHash: block,\n        includeTransactions\n      });\n    }\n    let blockTag = this._getBlockTag(block);\n    if (typeof blockTag !== \"string\") {\n      blockTag = await blockTag;\n    }\n    return await this.#perform({\n      method: \"getBlock\",\n      blockTag,\n      includeTransactions\n    });\n  }\n  // Queries\n  async getBlock(block, prefetchTxs) {\n    const {\n      network,\n      params\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      params: this.#getBlock(block, !!prefetchTxs)\n    });\n    if (params == null) {\n      return null;\n    }\n    return this._wrapBlock(params, network);\n  }\n  async getTransaction(hash) {\n    const {\n      network,\n      params\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      params: this.#perform({\n        method: \"getTransaction\",\n        hash\n      })\n    });\n    if (params == null) {\n      return null;\n    }\n    return this._wrapTransactionResponse(params, network);\n  }\n  async getTransactionReceipt(hash) {\n    const {\n      network,\n      params\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      params: this.#perform({\n        method: \"getTransactionReceipt\",\n        hash\n      })\n    });\n    if (params == null) {\n      return null;\n    }\n    // Some backends did not backfill the effectiveGasPrice into old transactions\n    // in the receipt, so we look it up manually and inject it.\n    if (params.gasPrice == null && params.effectiveGasPrice == null) {\n      const tx = await this.#perform({\n        method: \"getTransaction\",\n        hash\n      });\n      if (tx == null) {\n        throw new Error(\"report this; could not find tx or effectiveGasPrice\");\n      }\n      params.effectiveGasPrice = tx.gasPrice;\n    }\n    return this._wrapTransactionReceipt(params, network);\n  }\n  async getTransactionResult(hash) {\n    const {\n      result\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      result: this.#perform({\n        method: \"getTransactionResult\",\n        hash\n      })\n    });\n    if (result == null) {\n      return null;\n    }\n    return hexlify(result);\n  }\n  // Bloom-filter Queries\n  async getLogs(_filter) {\n    let filter = this._getFilter(_filter);\n    if (isPromise(filter)) {\n      filter = await filter;\n    }\n    const {\n      network,\n      params\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      params: this.#perform({\n        method: \"getLogs\",\n        filter\n      })\n    });\n    return params.map(p => this._wrapLog(p, network));\n  }\n  // ENS\n  _getProvider(chainId) {\n    assert(false, \"provider cannot connect to target network\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"_getProvider()\"\n    });\n  }\n  async getResolver(name) {\n    return await EnsResolver.fromName(this, name);\n  }\n  async getAvatar(name) {\n    const resolver = await this.getResolver(name);\n    if (resolver) {\n      return await resolver.getAvatar();\n    }\n    return null;\n  }\n  async resolveName(name) {\n    const resolver = await this.getResolver(name);\n    if (resolver) {\n      return await resolver.getAddress();\n    }\n    return null;\n  }\n  async lookupAddress(address) {\n    address = getAddress(address);\n    const node = namehash(address.substring(2).toLowerCase() + \".addr.reverse\");\n    try {\n      const ensAddr = await EnsResolver.getEnsAddress(this);\n      const ensContract = new Contract(ensAddr, [\"function resolver(bytes32) view returns (address)\"], this);\n      const resolver = await ensContract.resolver(node);\n      if (resolver == null || resolver === ZeroAddress) {\n        return null;\n      }\n      const resolverContract = new Contract(resolver, [\"function name(bytes32) view returns (string)\"], this);\n      const name = await resolverContract.name(node);\n      // Failed forward resolution\n      const check = await this.resolveName(name);\n      if (check !== address) {\n        return null;\n      }\n      return name;\n    } catch (error) {\n      // No data was returned from the resolver\n      if (isError(error, \"BAD_DATA\") && error.value === \"0x\") {\n        return null;\n      }\n      // Something reerted\n      if (isError(error, \"CALL_EXCEPTION\")) {\n        return null;\n      }\n      throw error;\n    }\n    return null;\n  }\n  async waitForTransaction(hash, _confirms, timeout) {\n    const confirms = _confirms != null ? _confirms : 1;\n    if (confirms === 0) {\n      return this.getTransactionReceipt(hash);\n    }\n    return new Promise(async (resolve, reject) => {\n      let timer = null;\n      const listener = async blockNumber => {\n        try {\n          const receipt = await this.getTransactionReceipt(hash);\n          if (receipt != null) {\n            if (blockNumber - receipt.blockNumber + 1 >= confirms) {\n              resolve(receipt);\n              //this.off(\"block\", listener);\n              if (timer) {\n                clearTimeout(timer);\n                timer = null;\n              }\n              return;\n            }\n          }\n        } catch (error) {\n          console.log(\"EEE\", error);\n        }\n        this.once(\"block\", listener);\n      };\n      if (timeout != null) {\n        timer = setTimeout(() => {\n          if (timer == null) {\n            return;\n          }\n          timer = null;\n          this.off(\"block\", listener);\n          reject(makeError(\"timeout\", \"TIMEOUT\", {\n            reason: \"timeout\"\n          }));\n        }, timeout);\n      }\n      listener(await this.getBlockNumber());\n    });\n  }\n  async waitForBlock(blockTag) {\n    assert(false, \"not implemented yet\", \"NOT_IMPLEMENTED\", {\n      operation: \"waitForBlock\"\n    });\n  }\n  /**\n   *  Clear a timer created using the [[_setTimeout]] method.\n   */\n  _clearTimeout(timerId) {\n    const timer = this.#timers.get(timerId);\n    if (!timer) {\n      return;\n    }\n    if (timer.timer) {\n      clearTimeout(timer.timer);\n    }\n    this.#timers.delete(timerId);\n  }\n  /**\n   *  Create a timer that will execute %%func%% after at least %%timeout%%\n   *  (in ms). If %%timeout%% is unspecified, then %%func%% will execute\n   *  in the next event loop.\n   *\n   *  [Pausing](AbstractProvider-paused) the provider will pause any\n   *  associated timers.\n   */\n  _setTimeout(_func, timeout) {\n    if (timeout == null) {\n      timeout = 0;\n    }\n    const timerId = this.#nextTimer++;\n    const func = () => {\n      this.#timers.delete(timerId);\n      _func();\n    };\n    if (this.paused) {\n      this.#timers.set(timerId, {\n        timer: null,\n        func,\n        time: timeout\n      });\n    } else {\n      const timer = setTimeout(func, timeout);\n      this.#timers.set(timerId, {\n        timer,\n        func,\n        time: getTime()\n      });\n    }\n    return timerId;\n  }\n  /**\n   *  Perform %%func%% on each subscriber.\n   */\n  _forEachSubscriber(func) {\n    for (const sub of this.#subs.values()) {\n      func(sub.subscriber);\n    }\n  }\n  /**\n   *  Sub-classes may override this to customize subscription\n   *  implementations.\n   */\n  _getSubscriber(sub) {\n    switch (sub.type) {\n      case \"debug\":\n      case \"error\":\n      case \"network\":\n        return new UnmanagedSubscriber(sub.type);\n      case \"block\":\n        {\n          const subscriber = new PollingBlockSubscriber(this);\n          subscriber.pollingInterval = this.pollingInterval;\n          return subscriber;\n        }\n      case \"safe\":\n      case \"finalized\":\n        return new PollingBlockTagSubscriber(this, sub.type);\n      case \"event\":\n        return new PollingEventSubscriber(this, sub.filter);\n      case \"transaction\":\n        return new PollingTransactionSubscriber(this, sub.hash);\n      case \"orphan\":\n        return new PollingOrphanSubscriber(this, sub.filter);\n    }\n    throw new Error(`unsupported event: ${sub.type}`);\n  }\n  /**\n   *  If a [[Subscriber]] fails and needs to replace itself, this\n   *  method may be used.\n   *\n   *  For example, this is used for providers when using the\n   *  ``eth_getFilterChanges`` method, which can return null if state\n   *  filters are not supported by the backend, allowing the Subscriber\n   *  to swap in a [[PollingEventSubscriber]].\n   */\n  _recoverSubscriber(oldSub, newSub) {\n    for (const sub of this.#subs.values()) {\n      if (sub.subscriber === oldSub) {\n        if (sub.started) {\n          sub.subscriber.stop();\n        }\n        sub.subscriber = newSub;\n        if (sub.started) {\n          newSub.start();\n        }\n        if (this.#pausedState != null) {\n          newSub.pause(this.#pausedState);\n        }\n        break;\n      }\n    }\n  }\n  async #hasSub(event, emitArgs) {\n    let sub = await getSubscription(event, this);\n    // This is a log that is removing an existing log; we actually want\n    // to emit an orphan event for the removed log\n    if (sub.type === \"event\" && emitArgs && emitArgs.length > 0 && emitArgs[0].removed === true) {\n      sub = await getSubscription({\n        orphan: \"drop-log\",\n        log: emitArgs[0]\n      }, this);\n    }\n    return this.#subs.get(sub.tag) || null;\n  }\n  async #getSub(event) {\n    const subscription = await getSubscription(event, this);\n    // Prevent tampering with our tag in any subclass' _getSubscriber\n    const tag = subscription.tag;\n    let sub = this.#subs.get(tag);\n    if (!sub) {\n      const subscriber = this._getSubscriber(subscription);\n      const addressableMap = new WeakMap();\n      const nameMap = new Map();\n      sub = {\n        subscriber,\n        tag,\n        addressableMap,\n        nameMap,\n        started: false,\n        listeners: []\n      };\n      this.#subs.set(tag, sub);\n    }\n    return sub;\n  }\n  async on(event, listener) {\n    const sub = await this.#getSub(event);\n    sub.listeners.push({\n      listener,\n      once: false\n    });\n    if (!sub.started) {\n      sub.subscriber.start();\n      sub.started = true;\n      if (this.#pausedState != null) {\n        sub.subscriber.pause(this.#pausedState);\n      }\n    }\n    return this;\n  }\n  async once(event, listener) {\n    const sub = await this.#getSub(event);\n    sub.listeners.push({\n      listener,\n      once: true\n    });\n    if (!sub.started) {\n      sub.subscriber.start();\n      sub.started = true;\n      if (this.#pausedState != null) {\n        sub.subscriber.pause(this.#pausedState);\n      }\n    }\n    return this;\n  }\n  async emit(event, ...args) {\n    const sub = await this.#hasSub(event, args);\n    // If there is not subscription or if a recent emit removed\n    // the last of them (which also deleted the sub) do nothing\n    if (!sub || sub.listeners.length === 0) {\n      return false;\n    }\n    ;\n    const count = sub.listeners.length;\n    sub.listeners = sub.listeners.filter(({\n      listener,\n      once\n    }) => {\n      const payload = new EventPayload(this, once ? null : listener, event);\n      try {\n        listener.call(this, ...args, payload);\n      } catch (error) {}\n      return !once;\n    });\n    if (sub.listeners.length === 0) {\n      if (sub.started) {\n        sub.subscriber.stop();\n      }\n      this.#subs.delete(sub.tag);\n    }\n    return count > 0;\n  }\n  async listenerCount(event) {\n    if (event) {\n      const sub = await this.#hasSub(event);\n      if (!sub) {\n        return 0;\n      }\n      return sub.listeners.length;\n    }\n    let total = 0;\n    for (const {\n      listeners\n    } of this.#subs.values()) {\n      total += listeners.length;\n    }\n    return total;\n  }\n  async listeners(event) {\n    if (event) {\n      const sub = await this.#hasSub(event);\n      if (!sub) {\n        return [];\n      }\n      return sub.listeners.map(({\n        listener\n      }) => listener);\n    }\n    let result = [];\n    for (const {\n      listeners\n    } of this.#subs.values()) {\n      result = result.concat(listeners.map(({\n        listener\n      }) => listener));\n    }\n    return result;\n  }\n  async off(event, listener) {\n    const sub = await this.#hasSub(event);\n    if (!sub) {\n      return this;\n    }\n    if (listener) {\n      const index = sub.listeners.map(({\n        listener\n      }) => listener).indexOf(listener);\n      if (index >= 0) {\n        sub.listeners.splice(index, 1);\n      }\n    }\n    if (!listener || sub.listeners.length === 0) {\n      if (sub.started) {\n        sub.subscriber.stop();\n      }\n      this.#subs.delete(sub.tag);\n    }\n    return this;\n  }\n  async removeAllListeners(event) {\n    if (event) {\n      const {\n        tag,\n        started,\n        subscriber\n      } = await this.#getSub(event);\n      if (started) {\n        subscriber.stop();\n      }\n      this.#subs.delete(tag);\n    } else {\n      for (const [tag, {\n        started,\n        subscriber\n      }] of this.#subs) {\n        if (started) {\n          subscriber.stop();\n        }\n        this.#subs.delete(tag);\n      }\n    }\n    return this;\n  }\n  // Alias for \"on\"\n  async addListener(event, listener) {\n    return await this.on(event, listener);\n  }\n  // Alias for \"off\"\n  async removeListener(event, listener) {\n    return this.off(event, listener);\n  }\n  /**\n   *  If this provider has been destroyed using the [[destroy]] method.\n   *\n   *  Once destroyed, all resources are reclaimed, internal event loops\n   *  and timers are cleaned up and no further requests may be sent to\n   *  the provider.\n   */\n  get destroyed() {\n    return this.#destroyed;\n  }\n  /**\n   *  Sub-classes may use this to shutdown any sockets or release their\n   *  resources and reject any pending requests.\n   *\n   *  Sub-classes **must** call ``super.destroy()``.\n   */\n  destroy() {\n    // Stop all listeners\n    this.removeAllListeners();\n    // Shut down all tiemrs\n    for (const timerId of this.#timers.keys()) {\n      this._clearTimeout(timerId);\n    }\n    this.#destroyed = true;\n  }\n  /**\n   *  Whether the provider is currently paused.\n   *\n   *  A paused provider will not emit any events, and generally should\n   *  not make any requests to the network, but that is up to sub-classes\n   *  to manage.\n   *\n   *  Setting ``paused = true`` is identical to calling ``.pause(false)``,\n   *  which will buffer any events that occur while paused until the\n   *  provider is unpaused.\n   */\n  get paused() {\n    return this.#pausedState != null;\n  }\n  set paused(pause) {\n    if (!!pause === this.paused) {\n      return;\n    }\n    if (this.paused) {\n      this.resume();\n    } else {\n      this.pause(false);\n    }\n  }\n  /**\n   *  Pause the provider. If %%dropWhilePaused%%, any events that occur\n   *  while paused are dropped, otherwise all events will be emitted once\n   *  the provider is unpaused.\n   */\n  pause(dropWhilePaused) {\n    this.#lastBlockNumber = -1;\n    if (this.#pausedState != null) {\n      if (this.#pausedState == !!dropWhilePaused) {\n        return;\n      }\n      assert(false, \"cannot change pause type; resume first\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"pause\"\n      });\n    }\n    this._forEachSubscriber(s => s.pause(dropWhilePaused));\n    this.#pausedState = !!dropWhilePaused;\n    for (const timer of this.#timers.values()) {\n      // Clear the timer\n      if (timer.timer) {\n        clearTimeout(timer.timer);\n      }\n      // Remaining time needed for when we become unpaused\n      timer.time = getTime() - timer.time;\n    }\n  }\n  /**\n   *  Resume the provider.\n   */\n  resume() {\n    if (this.#pausedState == null) {\n      return;\n    }\n    this._forEachSubscriber(s => s.resume());\n    this.#pausedState = null;\n    for (const timer of this.#timers.values()) {\n      // Remaining time when we were paused\n      let timeout = timer.time;\n      if (timeout < 0) {\n        timeout = 0;\n      }\n      // Start time (in cause paused, so we con compute remaininf time)\n      timer.time = getTime();\n      // Start the timer\n      setTimeout(timer.func, timeout);\n    }\n  }\n}\nfunction _parseString(result, start) {\n  try {\n    const bytes = _parseBytes(result, start);\n    if (bytes) {\n      return toUtf8String(bytes);\n    }\n  } catch (error) {}\n  return null;\n}\nfunction _parseBytes(result, start) {\n  if (result === \"0x\") {\n    return null;\n  }\n  try {\n    const offset = getNumber(dataSlice(result, start, start + 32));\n    const length = getNumber(dataSlice(result, offset, offset + 32));\n    return dataSlice(result, offset + 32, offset + 32 + length);\n  } catch (error) {}\n  return null;\n}\nfunction numPad(value) {\n  const result = toBeArray(value);\n  if (result.length > 32) {\n    throw new Error(\"internal; should not happen\");\n  }\n  const padded = new Uint8Array(32);\n  padded.set(result, 32 - result.length);\n  return padded;\n}\nfunction bytesPad(value) {\n  if (value.length % 32 === 0) {\n    return value;\n  }\n  const result = new Uint8Array(Math.ceil(value.length / 32) * 32);\n  result.set(value);\n  return result;\n}\nconst empty = new Uint8Array([]);\n// ABI Encodes a series of (bytes, bytes, ...)\nfunction encodeBytes(datas) {\n  const result = [];\n  let byteCount = 0;\n  // Add place-holders for pointers as we add items\n  for (let i = 0; i < datas.length; i++) {\n    result.push(empty);\n    byteCount += 32;\n  }\n  for (let i = 0; i < datas.length; i++) {\n    const data = getBytes(datas[i]);\n    // Update the bytes offset\n    result[i] = numPad(byteCount);\n    // The length and padded value of data\n    result.push(numPad(data.length));\n    result.push(bytesPad(data));\n    byteCount += 32 + Math.ceil(data.length / 32) * 32;\n  }\n  return concat(result);\n}\nconst zeros = \"0x0000000000000000000000000000000000000000000000000000000000000000\";\nfunction parseOffchainLookup(data) {\n  const result = {\n    sender: \"\",\n    urls: [],\n    calldata: \"\",\n    selector: \"\",\n    extraData: \"\",\n    errorArgs: []\n  };\n  assert(dataLength(data) >= 5 * 32, \"insufficient OffchainLookup data\", \"OFFCHAIN_FAULT\", {\n    reason: \"insufficient OffchainLookup data\"\n  });\n  const sender = dataSlice(data, 0, 32);\n  assert(dataSlice(sender, 0, 12) === dataSlice(zeros, 0, 12), \"corrupt OffchainLookup sender\", \"OFFCHAIN_FAULT\", {\n    reason: \"corrupt OffchainLookup sender\"\n  });\n  result.sender = dataSlice(sender, 12);\n  // Read the URLs from the response\n  try {\n    const urls = [];\n    const urlsOffset = getNumber(dataSlice(data, 32, 64));\n    const urlsLength = getNumber(dataSlice(data, urlsOffset, urlsOffset + 32));\n    const urlsData = dataSlice(data, urlsOffset + 32);\n    for (let u = 0; u < urlsLength; u++) {\n      const url = _parseString(urlsData, u * 32);\n      if (url == null) {\n        throw new Error(\"abort\");\n      }\n      urls.push(url);\n    }\n    result.urls = urls;\n  } catch (error) {\n    assert(false, \"corrupt OffchainLookup urls\", \"OFFCHAIN_FAULT\", {\n      reason: \"corrupt OffchainLookup urls\"\n    });\n  }\n  // Get the CCIP calldata to forward\n  try {\n    const calldata = _parseBytes(data, 64);\n    if (calldata == null) {\n      throw new Error(\"abort\");\n    }\n    result.calldata = calldata;\n  } catch (error) {\n    assert(false, \"corrupt OffchainLookup calldata\", \"OFFCHAIN_FAULT\", {\n      reason: \"corrupt OffchainLookup calldata\"\n    });\n  }\n  // Get the callbackSelector (bytes4)\n  assert(dataSlice(data, 100, 128) === dataSlice(zeros, 0, 28), \"corrupt OffchainLookup callbaackSelector\", \"OFFCHAIN_FAULT\", {\n    reason: \"corrupt OffchainLookup callbaackSelector\"\n  });\n  result.selector = dataSlice(data, 96, 100);\n  // Get the extra data to send back to the contract as context\n  try {\n    const extraData = _parseBytes(data, 128);\n    if (extraData == null) {\n      throw new Error(\"abort\");\n    }\n    result.extraData = extraData;\n  } catch (error) {\n    assert(false, \"corrupt OffchainLookup extraData\", \"OFFCHAIN_FAULT\", {\n      reason: \"corrupt OffchainLookup extraData\"\n    });\n  }\n  result.errorArgs = \"sender,urls,calldata,selector,extraData\".split(/,/).map(k => result[k]);\n  return result;\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "resolve<PERSON>ddress", "ZeroAddress", "Contract", "<PERSON><PERSON><PERSON>", "Transaction", "concat", "dataLength", "dataSlice", "hexlify", "isHexString", "getBigInt", "getBytes", "getNumber", "isCallException", "isError", "makeError", "assert", "assertArgument", "FetchRequest", "toBeArray", "toQuantity", "defineProperties", "EventPayload", "resolveProperties", "toUtf8String", "EnsResolver", "formatBlock", "formatLog", "formatTransactionReceipt", "formatTransactionResponse", "Network", "copyRequest", "Block", "FeeData", "Log", "TransactionReceipt", "TransactionResponse", "PollingBlockSubscriber", "PollingBlockTagSubscriber", "PollingEventSubscriber", "PollingOrphanSubscriber", "PollingTransactionSubscriber", "BN_2", "BigInt", "MAX_CCIP_REDIRECTS", "isPromise", "value", "then", "getTag", "prefix", "JSON", "stringify", "k", "v", "toString", "toLowerCase", "Array", "isArray", "keys", "Object", "sort", "reduce", "accum", "key", "UnmanagedSubscriber", "name", "constructor", "start", "stop", "pause", "dropWhilePaused", "resume", "copy", "parse", "concisify", "items", "from", "Set", "values", "getSubscription", "_event", "provider", "Error", "topics", "type", "tag", "hash", "orphan", "event", "filter", "address", "map", "t", "addresses", "promises", "addAddress", "addr", "push", "for<PERSON>ach", "length", "Promise", "all", "a", "getTime", "Date", "defaultOptions", "cacheTimeout", "pollingInterval", "AbstractProvider", "subs", "plugins", "pausedState", "destroyed", "networkPromise", "anyNetwork", "performCache", "lastBlockNumber", "nextTimer", "timers", "disableCcipRead", "options", "_network", "assign", "network", "resolve", "setTimeout", "emit", "Map", "attachPlugin", "plugin", "get", "set", "connect", "getPlugin", "perform", "#perform", "req", "timeout", "_perform", "method", "delete", "ccipReadFetch", "tx", "calldata", "urls", "to", "sender", "data", "errorMessages", "i", "url", "href", "replace", "request", "indexOf", "body", "action", "index", "errorMessage", "resp", "send", "error", "message", "result", "bodyJson", "statusCode", "reason", "transaction", "info", "m", "join", "_wrapBlock", "_wrapLog", "_wrapTransactionReceipt", "_wrapTransactionResponse", "_detectNetwork", "operation", "getBlockNumber", "blockNumber", "_getAddress", "_getBlockTag", "blockTag", "b", "_getFilter", "blockHash", "undefined", "_address", "fromBlock", "toBlock", "_getTransactionRequest", "_request", "getNetwork", "detectNetwork", "clone", "expected", "actual", "chainId", "getFeeData", "getFeeDataFunc", "_block", "gasPrice", "priorityFee", "getBlock", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "block", "baseFeePerGas", "feeData", "processFunc", "estimateGas", "_tx", "call", "#call", "attempt", "enableCcipRead", "txSender", "ccipArgs", "parseOffchainLookup", "invocation", "revert", "signature", "args", "errorArgs", "ccipResult", "selector", "encodeBytes", "extraData", "checkNetwork", "#checkNetwork", "promise", "getAccountValue", "#getAccountValue", "_blockTag", "getBalance", "getTransactionCount", "getCode", "getStorage", "_position", "position", "broadcastTransaction", "signedTx", "signedTransaction", "replaceableTransaction", "#getBlock", "includeTransactions", "prefetchTxs", "params", "getTransaction", "getTransactionReceipt", "effectiveGasPrice", "getTransactionResult", "getLogs", "_filter", "p", "_get<PERSON><PERSON><PERSON>", "getResolver", "fromName", "get<PERSON><PERSON><PERSON>", "resolver", "resolveName", "lookup<PERSON><PERSON><PERSON>", "node", "substring", "ensAddr", "getEnsAddress", "ensContract", "resolverContract", "check", "waitForTransaction", "_confirms", "confirms", "reject", "timer", "listener", "receipt", "clearTimeout", "console", "log", "once", "off", "waitForBlock", "_clearTimeout", "timerId", "_setTimeout", "_func", "func", "paused", "time", "_forEachSubscriber", "sub", "subscriber", "_getSubscriber", "_recoverSubscriber", "oldSub", "newSub", "started", "hasSub", "#hasSub", "emitArgs", "removed", "getSub", "#getSub", "subscription", "addressableMap", "WeakMap", "nameMap", "listeners", "on", "count", "payload", "listenerCount", "total", "splice", "removeAllListeners", "addListener", "removeListener", "destroy", "s", "_parseString", "bytes", "_parseBytes", "offset", "numPad", "padded", "Uint8Array", "bytesPad", "Math", "ceil", "empty", "datas", "byteCount", "zeros", "urlsOffset", "u<PERSON><PERSON><PERSON><PERSON>", "urlsData", "u", "split"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\abstract-provider.ts"], "sourcesContent": ["/**\n *  The available providers should suffice for most developers purposes,\n *  but the [[AbstractProvider]] class has many features which enable\n *  sub-classing it for specific purposes.\n *\n *  @_section: api/providers/abstract-provider: Subclassing Provider  [abstract-provider]\n */\n\n// @TODO\n// Event coalescence\n//   When we register an event with an async value (e.g. address is a Signer\n//   or ENS name), we need to add it immeidately for the Event API, but also\n//   need time to resolve the address. Upon resolving the address, we need to\n//   migrate the listener to the static event. We also need to maintain a map\n//   of Signer/ENS name to address so we can sync respond to listenerCount.\n\nimport { getAddress, resolveAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { namehash } from \"../hash/index.js\";\nimport { Transaction } from \"../transaction/index.js\";\nimport {\n    concat, dataLength, dataSlice, hexlify, isHexString,\n    getBigInt, getBytes, getNumber,\n    isCallException, isError, makeError, assert, assertArgument,\n    FetchRequest,\n    toBeArray, toQuantity,\n    defineProperties, EventPayload, resolveProperties,\n    toUtf8String\n} from \"../utils/index.js\";\n\nimport { EnsResolver } from \"./ens-resolver.js\";\nimport {\n    formatBlock, formatLog, formatTransactionReceipt, formatTransactionResponse\n} from \"./format.js\";\nimport { Network } from \"./network.js\";\nimport { copyRequest, Block, FeeData, Log, TransactionReceipt, TransactionResponse } from \"./provider.js\";\nimport {\n    PollingBlockSubscriber, PollingBlockTagSubscriber, PollingEventSubscriber,\n    PollingOrphanSubscriber, PollingTransactionSubscriber\n} from \"./subscriber-polling.js\";\n\nimport type { Addressable, AddressLike } from \"../address/index.js\";\nimport type { BigNumberish, BytesLike } from \"../utils/index.js\";\nimport type { FetchResponse, Listener } from \"../utils/index.js\";\n\nimport type { Networkish } from \"./network.js\";\nimport type { FetchUrlFeeDataNetworkPlugin } from \"./plugins-network.js\";\n//import type { MaxPriorityFeePlugin } from \"./plugins-network.js\";\nimport type {\n    BlockParams, LogParams, TransactionReceiptParams,\n    TransactionResponseParams\n} from \"./formatting.js\";\n\nimport type {\n    BlockTag, EventFilter, Filter, FilterByBlockHash, OrphanFilter,\n    PreparedTransactionRequest, Provider, ProviderEvent,\n    TransactionRequest\n} from \"./provider.js\";\n\ntype Timer = ReturnType<typeof setTimeout>;\n\n\n// Constants\nconst BN_2 = BigInt(2);\n\nconst MAX_CCIP_REDIRECTS = 10;\n\nfunction isPromise<T = any>(value: any): value is Promise<T> {\n    return (value && typeof(value.then) === \"function\");\n}\n\nfunction getTag(prefix: string, value: any): string {\n    return prefix + \":\" + JSON.stringify(value, (k, v) => {\n        if (v == null) { return \"null\"; }\n        if (typeof(v) === \"bigint\") { return `bigint:${ v.toString() }`}\n        if (typeof(v) === \"string\") { return v.toLowerCase(); }\n\n        // Sort object keys\n        if (typeof(v) === \"object\" && !Array.isArray(v)) {\n            const keys = Object.keys(v);\n            keys.sort();\n            return keys.reduce((accum, key) => {\n                accum[key] = v[key];\n                return accum;\n            }, <any>{ });\n        }\n\n        return v;\n    });\n}\n\n/**\n *  The types of additional event values that can be emitted for the\n *  ``\"debug\"`` event.\n */\nexport type DebugEventAbstractProvider = {\n    action: \"sendCcipReadFetchRequest\",\n    request: FetchRequest\n    index: number\n    urls: Array<string>\n} | {\n    action: \"receiveCcipReadFetchResult\",\n    request: FetchRequest,\n    result: any\n} | {\n    action: \"receiveCcipReadFetchError\",\n    request: FetchRequest,\n    result: any\n} | {\n    action: \"sendCcipReadCall\",\n    transaction: { to: string, data: string }\n} | {\n    action: \"receiveCcipReadCallResult\",\n    transaction: { to: string, data: string }\n    result: string\n} | {\n    action: \"receiveCcipReadCallError\",\n    transaction: { to: string, data: string }\n    error: Error\n};\n\n\n/**\n *  The value passed to the [[AbstractProvider-_getSubscriber]] method.\n *\n *  Only developers sub-classing [[AbstractProvider[[ will care about this,\n *  if they are modifying a low-level feature of how subscriptions operate.\n */\nexport type Subscription = {\n    type: \"block\" | \"close\" | \"debug\" | \"error\" | \"finalized\" | \"network\" | \"pending\" | \"safe\",\n    tag: string\n} | {\n    type: \"transaction\",\n    tag: string,\n    hash: string\n} | {\n    type: \"event\",\n    tag: string,\n    filter: EventFilter\n} | {\n    type: \"orphan\",\n    tag: string,\n    filter: OrphanFilter\n};\n\n/**\n *  A **Subscriber** manages a subscription.\n *\n *  Only developers sub-classing [[AbstractProvider[[ will care about this,\n *  if they are modifying a low-level feature of how subscriptions operate.\n */\nexport interface Subscriber {\n    /**\n     *  Called initially when a subscriber is added the first time.\n     */\n    start(): void;\n\n    /**\n     *  Called when there are no more subscribers to the event.\n     */\n    stop(): void;\n\n    /**\n     *  Called when the subscription should pause.\n     *\n     *  If %%dropWhilePaused%%, events that occur while paused should not\n     *  be emitted [[resume]].\n     */\n    pause(dropWhilePaused?: boolean): void;\n\n    /**\n     *  Resume a paused subscriber.\n     */\n    resume(): void;\n\n    /**\n     *  The frequency (in ms) to poll for events, if polling is used by\n     *  the subscriber.\n     *\n     *  For non-polling subscribers, this must return ``undefined``.\n     */\n    pollingInterval?: number;\n}\n\n/**\n *  An **UnmanagedSubscriber** is useful for events which do not require\n *  any additional management, such as ``\"debug\"`` which only requires\n *  emit in synchronous event loop triggered calls.\n */\nexport class UnmanagedSubscriber implements Subscriber {\n    /**\n     *  The name fof the event.\n     */\n    name!: string;\n\n    /**\n     *  Create a new UnmanagedSubscriber with %%name%%.\n     */\n    constructor(name: string) { defineProperties<UnmanagedSubscriber>(this, { name }); }\n\n    start(): void { }\n    stop(): void { }\n\n    pause(dropWhilePaused?: boolean): void { }\n    resume(): void { }\n}\n\ntype Sub = {\n    tag: string;\n    nameMap: Map<string, string>\n    addressableMap: WeakMap<Addressable, string>;\n    listeners: Array<{ listener: Listener, once: boolean }>;\n    // @TODO: get rid of this, as it is (and has to be)\n    // tracked in subscriber\n    started: boolean;\n    subscriber: Subscriber;\n};\n\nfunction copy<T = any>(value: T): T {\n    return JSON.parse(JSON.stringify(value));\n}\n\nfunction concisify(items: Array<string>): Array<string> {\n    items = Array.from((new Set(items)).values())\n    items.sort();\n    return items;\n}\n\n\nasync function getSubscription(_event: ProviderEvent, provider: AbstractProvider): Promise<Subscription> {\n    if (_event == null) { throw new Error(\"invalid event\"); }\n\n    // Normalize topic array info an EventFilter\n    if (Array.isArray(_event)) { _event = { topics: _event }; }\n\n    if (typeof(_event) === \"string\") {\n        switch (_event) {\n            case \"block\":\n            case \"debug\":\n            case \"error\":\n            case \"finalized\":\n            case \"network\":\n            case \"pending\":\n            case \"safe\": {\n                return { type: _event, tag: _event };\n            }\n        }\n    }\n\n    if (isHexString(_event, 32)) {\n        const hash = _event.toLowerCase();\n        return { type: \"transaction\", tag: getTag(\"tx\", { hash }), hash };\n    }\n\n    if ((<any>_event).orphan) {\n        const event = <OrphanFilter>_event;\n        // @TODO: Should lowercase and whatnot things here instead of copy...\n        return { type: \"orphan\", tag: getTag(\"orphan\", event), filter: copy(event) };\n    }\n\n    if (((<any>_event).address || (<any>_event).topics)) {\n        const event = <EventFilter>_event;\n\n        const filter: any = {\n            topics: ((event.topics || []).map((t) => {\n                if (t == null) { return null; }\n                if (Array.isArray(t)) {\n                    return concisify(t.map((t) => t.toLowerCase()));\n                }\n                return t.toLowerCase();\n            }))\n        };\n\n        if (event.address) {\n            const addresses: Array<string> = [ ];\n            const promises: Array<Promise<void>> = [ ];\n\n            const addAddress = (addr: AddressLike) => {\n                if (isHexString(addr)) {\n                    addresses.push(addr);\n                } else {\n                    promises.push((async () => {\n                        addresses.push(await resolveAddress(addr, provider));\n                    })());\n                }\n            }\n\n            if (Array.isArray(event.address)) {\n                event.address.forEach(addAddress);\n            } else {\n                addAddress(event.address);\n            }\n            if (promises.length) { await Promise.all(promises); }\n            filter.address = concisify(addresses.map((a) => a.toLowerCase()));\n        }\n\n        return { filter, tag: getTag(\"event\", filter), type: \"event\" };\n    }\n\n    assertArgument(false, \"unknown ProviderEvent\", \"event\", _event);\n}\n\nfunction getTime(): number { return (new Date()).getTime(); }\n\n/**\n *  An **AbstractPlugin** is used to provide additional internal services\n *  to an [[AbstractProvider]] without adding backwards-incompatible changes\n *  to method signatures or other internal and complex logic.\n */\nexport interface AbstractProviderPlugin {\n    /**\n     *  The reverse domain notation of the plugin.\n     */\n    readonly name: string;\n\n    /**\n     *  Creates a new instance of the plugin, connected to %%provider%%.\n     */\n    connect(provider: AbstractProvider): AbstractProviderPlugin;\n}\n\n/**\n *  A normalized filter used for [[PerformActionRequest]] objects.\n */\nexport type PerformActionFilter = {\n    address?: string | Array<string>;\n    topics?: Array<null | string | Array<string>>;\n    fromBlock?: BlockTag;\n    toBlock?: BlockTag;\n} | {\n    address?: string | Array<string>;\n    topics?: Array<null | string | Array<string>>;\n    blockHash?: string;\n};\n\n/**\n *  A normalized transactions used for [[PerformActionRequest]] objects.\n */\nexport interface PerformActionTransaction extends PreparedTransactionRequest {\n    /**\n     *  The ``to`` address of the transaction.\n     */\n    to?: string;\n\n    /**\n     *  The sender of the transaction.\n     */\n    from?: string;\n}\n\n/**\n *  The [[AbstractProvider]] methods will normalize all values and pass this\n *  type to [[AbstractProvider-_perform]].\n */\nexport type PerformActionRequest = {\n    method: \"broadcastTransaction\",\n    signedTransaction: string\n} | {\n    method: \"call\",\n    transaction: PerformActionTransaction, blockTag: BlockTag\n} | {\n    method: \"chainId\"\n} | {\n    method: \"estimateGas\",\n    transaction: PerformActionTransaction\n} | {\n    method: \"getBalance\",\n    address: string, blockTag: BlockTag\n} | {\n    method: \"getBlock\",\n    blockTag: BlockTag, includeTransactions: boolean\n} | {\n    method: \"getBlock\",\n    blockHash: string, includeTransactions: boolean\n} | {\n    method: \"getBlockNumber\"\n} | {\n    method: \"getCode\",\n    address: string, blockTag: BlockTag\n} | {\n    method: \"getGasPrice\"\n} | {\n    method: \"getLogs\",\n    filter: PerformActionFilter\n} | {\n    method: \"getPriorityFee\"\n} | {\n    method: \"getStorage\",\n    address: string, position: bigint, blockTag: BlockTag\n} | {\n    method: \"getTransaction\",\n    hash: string\n} | {\n    method: \"getTransactionCount\",\n    address: string, blockTag: BlockTag\n} | {\n    method: \"getTransactionReceipt\",\n    hash: string\n} | {\n    method: \"getTransactionResult\",\n    hash: string\n};\n\ntype _PerformAccountRequest = {\n    method: \"getBalance\" | \"getTransactionCount\" | \"getCode\"\n} | {\n    method: \"getStorage\", position: bigint\n}\n\n/**\n *  Options for configuring some internal aspects of an [[AbstractProvider]].\n *\n *  **``cacheTimeout``** - how long to cache a low-level ``_perform``\n *  for, based on input parameters. This reduces the number of calls\n *  to getChainId and getBlockNumber, but may break test chains which\n *  can perform operations (internally) synchronously. Use ``-1`` to\n *  disable, ``0`` will only buffer within the same event loop and\n *  any other value is in ms. (default: ``250``)\n */\nexport type AbstractProviderOptions = {\n    cacheTimeout?: number;\n    pollingInterval?: number;\n};\n\nconst defaultOptions = {\n    cacheTimeout: 250,\n    pollingInterval: 4000\n};\n\ntype CcipArgs = {\n    sender: string;\n    urls: Array<string>;\n    calldata: string;\n    selector: string;\n    extraData: string;\n    errorArgs: Array<any>\n};\n\n/**\n *  An **AbstractProvider** provides a base class for other sub-classes to\n *  implement the [[Provider]] API by normalizing input arguments and\n *  formatting output results as well as tracking events for consistent\n *  behaviour on an eventually-consistent network.\n */\nexport class AbstractProvider implements Provider {\n\n    #subs: Map<string, Sub>;\n    #plugins: Map<string, AbstractProviderPlugin>;\n\n    // null=unpaused, true=paused+dropWhilePaused, false=paused\n    #pausedState: null | boolean;\n\n    #destroyed: boolean;\n\n    #networkPromise: null | Promise<Network>;\n    readonly #anyNetwork: boolean;\n\n    #performCache: Map<string, Promise<any>>;\n\n    // The most recent block number if running an event or -1 if no \"block\" event\n    #lastBlockNumber: number;\n\n    #nextTimer: number;\n    #timers: Map<number, { timer: null | Timer, func: () => void, time: number }>;\n\n    #disableCcipRead: boolean;\n\n    #options: Required<AbstractProviderOptions>;\n\n    /**\n     *  Create a new **AbstractProvider** connected to %%network%%, or\n     *  use the various network detection capabilities to discover the\n     *  [[Network]] if necessary.\n     */\n    constructor(_network?: \"any\" | Networkish, options?: AbstractProviderOptions) {\n        this.#options = Object.assign({ }, defaultOptions, options || { });\n\n        if (_network === \"any\") {\n            this.#anyNetwork = true;\n            this.#networkPromise = null;\n        } else if (_network) {\n            const network = Network.from(_network);\n            this.#anyNetwork = false;\n            this.#networkPromise = Promise.resolve(network);\n            setTimeout(() => { this.emit(\"network\", network, null); }, 0);\n        } else {\n            this.#anyNetwork = false;\n            this.#networkPromise = null;\n        }\n\n        this.#lastBlockNumber = -1;\n\n        this.#performCache = new Map();\n\n        this.#subs = new Map();\n        this.#plugins = new Map();\n        this.#pausedState = null;\n\n        this.#destroyed = false;\n\n        this.#nextTimer = 1;\n        this.#timers = new Map();\n\n        this.#disableCcipRead = false;\n    }\n\n    get pollingInterval(): number { return this.#options.pollingInterval; }\n\n    /**\n     *  Returns ``this``, to allow an **AbstractProvider** to implement\n     *  the [[ContractRunner]] interface.\n     */\n    get provider(): this { return this; }\n\n    /**\n     *  Returns all the registered plug-ins.\n     */\n    get plugins(): Array<AbstractProviderPlugin> {\n        return Array.from(this.#plugins.values());\n    }\n\n    /**\n     *  Attach a new plug-in.\n     */\n    attachPlugin(plugin: AbstractProviderPlugin): this {\n        if (this.#plugins.get(plugin.name)) {\n            throw new Error(`cannot replace existing plugin: ${ plugin.name } `);\n        }\n        this.#plugins.set(plugin.name,  plugin.connect(this));\n        return this;\n    }\n\n    /**\n     *  Get a plugin by name.\n     */\n    getPlugin<T extends AbstractProviderPlugin = AbstractProviderPlugin>(name: string): null | T {\n        return <T>(this.#plugins.get(name)) || null;\n    }\n\n    /**\n     *  Prevent any CCIP-read operation, regardless of whether requested\n     *  in a [[call]] using ``enableCcipRead``.\n     */\n    get disableCcipRead(): boolean { return this.#disableCcipRead; }\n    set disableCcipRead(value: boolean) { this.#disableCcipRead = !!value; }\n\n    // Shares multiple identical requests made during the same 250ms\n    async #perform<T = any>(req: PerformActionRequest): Promise<T> {\n        const timeout = this.#options.cacheTimeout;\n\n        // Caching disabled\n        if (timeout < 0) { return await this._perform(req); }\n\n        // Create a tag\n        const tag = getTag(req.method, req);\n\n        let perform = this.#performCache.get(tag);\n        if (!perform) {\n            perform = this._perform(req);\n\n            this.#performCache.set(tag, perform);\n\n            setTimeout(() => {\n                if (this.#performCache.get(tag) === perform) {\n                    this.#performCache.delete(tag);\n                }\n            }, timeout);\n        }\n\n        return await perform;\n    }\n\n    /**\n     *  Resolves to the data for executing the CCIP-read operations.\n     */\n    async ccipReadFetch(tx: PerformActionTransaction, calldata: string, urls: Array<string>): Promise<null | string> {\n        if (this.disableCcipRead || urls.length === 0 || tx.to == null) { return null; }\n\n        const sender = tx.to.toLowerCase();\n        const data = calldata.toLowerCase();\n\n        const errorMessages: Array<string> = [ ];\n\n        for (let i = 0; i < urls.length; i++) {\n            const url = urls[i];\n\n            // URL expansion\n            const href = url.replace(\"{sender}\", sender).replace(\"{data}\", data);\n\n            // If no {data} is present, use POST; otherwise GET\n            //const json: string | null = (url.indexOf(\"{data}\") >= 0) ? null: JSON.stringify({ data, sender });\n\n            //const result = await fetchJson({ url: href, errorPassThrough: true }, json, (value, response) => {\n            //    value.status = response.statusCode;\n            //    return value;\n            //});\n            const request = new FetchRequest(href);\n            if (url.indexOf(\"{data}\") === -1) {\n                request.body = { data, sender };\n            }\n\n            this.emit(\"debug\", { action: \"sendCcipReadFetchRequest\", request, index: i, urls });\n\n            let errorMessage = \"unknown error\";\n\n            // Fetch the resource...\n            let resp: FetchResponse;\n            try {\n                resp = await request.send();\n            } catch (error: any) {\n                // ...low-level fetch error (missing host, bad SSL, etc.),\n                // so try next URL\n                errorMessages.push(error.message);\n                this.emit(\"debug\", { action: \"receiveCcipReadFetchError\", request, result: { error } });\n                continue;\n            }\n\n            try {\n                const result = resp.bodyJson;\n                if (result.data) {\n                    this.emit(\"debug\", { action: \"receiveCcipReadFetchResult\", request, result });\n                    return result.data;\n                }\n                if (result.message) { errorMessage = result.message; }\n                this.emit(\"debug\", { action: \"receiveCcipReadFetchError\", request, result });\n            } catch (error) { }\n\n            // 4xx indicates the result is not present; stop\n            assert(resp.statusCode < 400 || resp.statusCode >= 500, `response not found during CCIP fetch: ${ errorMessage }`,\n                \"OFFCHAIN_FAULT\", { reason: \"404_MISSING_RESOURCE\", transaction: tx, info: { url, errorMessage } });\n\n            // 5xx indicates server issue; try the next url\n            errorMessages.push(errorMessage);\n        }\n\n        assert(false, `error encountered during CCIP fetch: ${ errorMessages.map((m) => JSON.stringify(m)).join(\", \") }`, \"OFFCHAIN_FAULT\", {\n            reason: \"500_SERVER_ERROR\",\n            transaction: tx, info: { urls, errorMessages }\n        });\n    }\n\n    /**\n     *  Provides the opportunity for a sub-class to wrap a block before\n     *  returning it, to add additional properties or an alternate\n     *  sub-class of [[Block]].\n     */\n    _wrapBlock(value: BlockParams, network: Network): Block {\n        return new Block(formatBlock(value), this);\n    }\n\n    /**\n     *  Provides the opportunity for a sub-class to wrap a log before\n     *  returning it, to add additional properties or an alternate\n     *  sub-class of [[Log]].\n     */\n    _wrapLog(value: LogParams, network: Network): Log {\n        return new Log(formatLog(value), this);\n    }\n\n    /**\n     *  Provides the opportunity for a sub-class to wrap a transaction\n     *  receipt before returning it, to add additional properties or an\n     *  alternate sub-class of [[TransactionReceipt]].\n     */\n    _wrapTransactionReceipt(value: TransactionReceiptParams, network: Network): TransactionReceipt {\n        return new TransactionReceipt(formatTransactionReceipt(value), this);\n    }\n\n    /**\n     *  Provides the opportunity for a sub-class to wrap a transaction\n     *  response before returning it, to add additional properties or an\n     *  alternate sub-class of [[TransactionResponse]].\n     */\n    _wrapTransactionResponse(tx: TransactionResponseParams, network: Network): TransactionResponse {\n        return new TransactionResponse(formatTransactionResponse(tx), this);\n    }\n\n    /**\n     *  Resolves to the Network, forcing a network detection using whatever\n     *  technique the sub-class requires.\n     *\n     *  Sub-classes **must** override this.\n     */\n    _detectNetwork(): Promise<Network> {\n        assert(false, \"sub-classes must implement this\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"_detectNetwork\"\n        });\n    }\n\n    /**\n     *  Sub-classes should use this to perform all built-in operations. All\n     *  methods sanitizes and normalizes the values passed into this.\n     *\n     *  Sub-classes **must** override this.\n     */\n    async _perform<T = any>(req: PerformActionRequest): Promise<T> {\n        assert(false, `unsupported method: ${ req.method }`, \"UNSUPPORTED_OPERATION\", {\n            operation: req.method,\n            info: req\n        });\n    }\n\n    // State\n\n    async getBlockNumber(): Promise<number> {\n        const blockNumber = getNumber(await this.#perform({ method: \"getBlockNumber\" }), \"%response\");\n        if (this.#lastBlockNumber >= 0) { this.#lastBlockNumber = blockNumber; }\n        return blockNumber;\n    }\n\n    /**\n     *  Returns or resolves to the address for %%address%%, resolving ENS\n     *  names and [[Addressable]] objects and returning if already an\n     *  address.\n     */\n    _getAddress(address: AddressLike): string | Promise<string> {\n        return resolveAddress(address, this);\n    }\n\n    /**\n     *  Returns or resolves to a valid block tag for %%blockTag%%, resolving\n     *  negative values and returning if already a valid block tag.\n     */\n    _getBlockTag(blockTag?: BlockTag): string | Promise<string> {\n        if (blockTag == null) { return \"latest\"; }\n\n        switch (blockTag) {\n            case \"earliest\":\n                return \"0x0\";\n            case \"finalized\":\n            case \"latest\":\n            case \"pending\":\n            case \"safe\":\n                return blockTag;\n        }\n\n\n        if (isHexString(blockTag)) {\n            if (isHexString(blockTag, 32)) { return blockTag; }\n            return toQuantity(blockTag);\n        }\n\n        if (typeof(blockTag) === \"bigint\") {\n            blockTag = getNumber(blockTag, \"blockTag\");\n        }\n\n        if (typeof(blockTag) === \"number\") {\n            if (blockTag >= 0) { return toQuantity(blockTag); }\n            if (this.#lastBlockNumber >= 0) { return toQuantity(this.#lastBlockNumber + blockTag); }\n            return this.getBlockNumber().then((b) => toQuantity(b + <number>blockTag));\n        }\n\n        assertArgument(false, \"invalid blockTag\", \"blockTag\", blockTag);\n    }\n\n    /**\n     *  Returns or resolves to a filter for %%filter%%, resolving any ENS\n     *  names or [[Addressable]] object and returning if already a valid\n     *  filter.\n     */\n    _getFilter(filter: Filter | FilterByBlockHash): PerformActionFilter | Promise<PerformActionFilter> {\n\n        // Create a canonical representation of the topics\n        const topics = (filter.topics || [ ]).map((t) => {\n            if (t == null) { return null; }\n            if (Array.isArray(t)) {\n                return concisify(t.map((t) => t.toLowerCase()));\n            }\n            return t.toLowerCase();\n        });\n\n        const blockHash = (\"blockHash\" in filter) ? filter.blockHash: undefined;\n\n        const resolve = (_address: Array<string>, fromBlock?: string, toBlock?: string) => {\n            let address: undefined | string | Array<string> = undefined;\n            switch (_address.length) {\n                case 0: break;\n                case 1:\n                    address = _address[0];\n                    break;\n                default:\n                    _address.sort();\n                    address = _address;\n            }\n\n            if (blockHash) {\n                if (fromBlock != null || toBlock != null) {\n                    throw new Error(\"invalid filter\");\n                }\n            }\n\n            const filter = <any>{ };\n            if (address) { filter.address = address; }\n            if (topics.length) { filter.topics = topics; }\n            if (fromBlock) { filter.fromBlock = fromBlock; }\n            if (toBlock) { filter.toBlock = toBlock; }\n            if (blockHash) { filter.blockHash = blockHash; }\n\n            return filter;\n        };\n\n        // Addresses could be async (ENS names or Addressables)\n        let address: Array<string | Promise<string>> = [ ];\n        if (filter.address) {\n            if (Array.isArray(filter.address)) {\n                for (const addr of filter.address) { address.push(this._getAddress(addr)); }\n            } else {\n                address.push(this._getAddress(filter.address));\n            }\n        }\n\n        let fromBlock: undefined | string | Promise<string> = undefined;\n        if (\"fromBlock\" in filter) { fromBlock = this._getBlockTag(filter.fromBlock); }\n\n        let toBlock: undefined | string | Promise<string> = undefined;\n        if (\"toBlock\" in filter) { toBlock = this._getBlockTag(filter.toBlock); }\n\n        if (address.filter((a) => (typeof(a) !== \"string\")).length ||\n            (fromBlock != null && typeof(fromBlock) !== \"string\") ||\n            (toBlock != null && typeof(toBlock) !== \"string\")) {\n\n            return Promise.all([ Promise.all(address), fromBlock, toBlock ]).then((result) => {\n                return resolve(result[0], result[1], result[2]);\n            });\n        }\n\n        return resolve(<Array<string>>address, fromBlock, toBlock);\n    }\n\n    /**\n     *  Returns or resolves to a transaction for %%request%%, resolving\n     *  any ENS names or [[Addressable]] and returning if already a valid\n     *  transaction.\n     */\n    _getTransactionRequest(_request: TransactionRequest): PerformActionTransaction | Promise<PerformActionTransaction> {\n        const request = <PerformActionTransaction>copyRequest(_request);\n\n        const promises: Array<Promise<void>> = [ ];\n        [ \"to\", \"from\" ].forEach((key) => {\n            if ((<any>request)[key] == null) { return; }\n\n            const addr = resolveAddress((<any>request)[key], this);\n            if (isPromise(addr)) {\n                promises.push((async function() { (<any>request)[key] = await addr; })());\n            } else {\n                (<any>request)[key] = addr;\n            }\n        });\n\n        if (request.blockTag != null) {\n            const blockTag = this._getBlockTag(request.blockTag);\n            if (isPromise(blockTag)) {\n                promises.push((async function() { request.blockTag = await blockTag; })());\n            } else {\n                request.blockTag = blockTag;\n            }\n        }\n\n        if (promises.length) {\n            return (async function() {\n                await Promise.all(promises);\n                return request;\n            })();\n        }\n\n        return request;\n    }\n\n    async getNetwork(): Promise<Network> {\n\n        // No explicit network was set and this is our first time\n        if (this.#networkPromise == null) {\n\n            // Detect the current network (shared with all calls)\n            const detectNetwork = (async () => {\n                try {\n                    const network = await this._detectNetwork();\n                    this.emit(\"network\", network, null);\n                    return network;\n                } catch (error) {\n                    if (this.#networkPromise === detectNetwork!) {\n                        this.#networkPromise = null;\n                    }\n                    throw error;\n                }\n            })();\n\n            this.#networkPromise = detectNetwork;\n            return (await detectNetwork).clone();\n        }\n\n        const networkPromise = this.#networkPromise;\n\n        const [ expected, actual ] = await Promise.all([\n            networkPromise,          // Possibly an explicit Network\n            this._detectNetwork()    // The actual connected network\n        ]);\n\n        if (expected.chainId !== actual.chainId) {\n            if (this.#anyNetwork) {\n                // The \"any\" network can change, so notify listeners\n                this.emit(\"network\", actual, expected);\n\n                // Update the network if something else hasn't already changed it\n                if (this.#networkPromise === networkPromise) {\n                    this.#networkPromise = Promise.resolve(actual);\n                }\n            } else {\n                // Otherwise, we do not allow changes to the underlying network\n                assert(false, `network changed: ${ expected.chainId } => ${ actual.chainId } `, \"NETWORK_ERROR\", {\n                    event: \"changed\"\n                });\n            }\n        }\n\n        return expected.clone();\n    }\n\n    async getFeeData(): Promise<FeeData> {\n        const network = await this.getNetwork();\n\n        const getFeeDataFunc = async () => {\n            const { _block, gasPrice, priorityFee } = await resolveProperties({\n                _block: this.#getBlock(\"latest\", false),\n                gasPrice: ((async () => {\n                    try {\n                        const value = await this.#perform({ method: \"getGasPrice\" });\n                        return getBigInt(value, \"%response\");\n                    } catch (error) { }\n                    return null\n                })()),\n                priorityFee: ((async () => {\n                    try {\n                        const value = await this.#perform({ method: \"getPriorityFee\" });\n                        return getBigInt(value, \"%response\");\n                    } catch (error) { }\n                    return null;\n                })())\n            });\n\n            let maxFeePerGas: null | bigint = null;\n            let maxPriorityFeePerGas: null | bigint = null;\n\n            // These are the recommended EIP-1559 heuristics for fee data\n            const block = this._wrapBlock(_block, network);\n            if (block && block.baseFeePerGas) {\n                maxPriorityFeePerGas = (priorityFee != null) ? priorityFee: BigInt(\"1000000000\");\n                maxFeePerGas = (block.baseFeePerGas * BN_2) + maxPriorityFeePerGas;\n            }\n\n            return new FeeData(gasPrice, maxFeePerGas, maxPriorityFeePerGas);\n        };\n\n        // Check for a FeeDataNetWorkPlugin\n        const plugin = <FetchUrlFeeDataNetworkPlugin>network.getPlugin(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\");\n        if (plugin) {\n            const req = new FetchRequest(plugin.url);\n            const feeData = await plugin.processFunc(getFeeDataFunc, this, req);\n            return new FeeData(feeData.gasPrice, feeData.maxFeePerGas, feeData.maxPriorityFeePerGas);\n        }\n\n        return await getFeeDataFunc();\n    }\n\n\n    async estimateGas(_tx: TransactionRequest): Promise<bigint> {\n        let tx = this._getTransactionRequest(_tx);\n        if (isPromise(tx)) { tx = await tx; }\n        return getBigInt(await this.#perform({\n            method: \"estimateGas\", transaction: tx\n        }), \"%response\");\n    }\n\n    async #call(tx: PerformActionTransaction, blockTag: string, attempt: number): Promise<string> {\n        assert (attempt < MAX_CCIP_REDIRECTS, \"CCIP read exceeded maximum redirections\", \"OFFCHAIN_FAULT\", {\n             reason: \"TOO_MANY_REDIRECTS\",\n             transaction: Object.assign({ }, tx, { blockTag, enableCcipRead: true })\n         });\n\n         // This came in as a PerformActionTransaction, so to/from are safe; we can cast\n         const transaction = <PerformActionTransaction>copyRequest(tx);\n\n         try {\n             return hexlify(await this._perform({ method: \"call\", transaction, blockTag }));\n\n         } catch (error: any) {\n             // CCIP Read OffchainLookup\n             if (!this.disableCcipRead && isCallException(error) && error.data && attempt >= 0 && blockTag === \"latest\" && transaction.to != null && dataSlice(error.data, 0, 4) === \"0x556f1830\") {\n                 const data = error.data;\n\n                 const txSender = await resolveAddress(transaction.to, this);\n\n                 // Parse the CCIP Read Arguments\n                 let ccipArgs: CcipArgs;\n                 try {\n                     ccipArgs = parseOffchainLookup(dataSlice(error.data, 4));\n                 } catch (error: any) {\n                     assert(false, error.message, \"OFFCHAIN_FAULT\", {\n                         reason: \"BAD_DATA\", transaction, info: { data } });\n                 }\n\n                 // Check the sender of the OffchainLookup matches the transaction\n                 assert(ccipArgs.sender.toLowerCase() === txSender.toLowerCase(),\n                     \"CCIP Read sender mismatch\", \"CALL_EXCEPTION\", {\n                         action: \"call\",\n                         data,\n                         reason: \"OffchainLookup\",\n                         transaction: <any>transaction, // @TODO: populate data?\n                         invocation: null,\n                         revert: {\n                             signature: \"OffchainLookup(address,string[],bytes,bytes4,bytes)\",\n                             name: \"OffchainLookup\",\n                             args: ccipArgs.errorArgs\n                         }\n                     });\n\n                 const ccipResult = await this.ccipReadFetch(transaction, ccipArgs.calldata, ccipArgs.urls);\n                 assert(ccipResult != null, \"CCIP Read failed to fetch data\", \"OFFCHAIN_FAULT\", {\n                     reason: \"FETCH_FAILED\", transaction, info: { data: error.data, errorArgs: ccipArgs.errorArgs } });\n\n                 const tx = {\n                     to: txSender,\n                     data: concat([ ccipArgs.selector, encodeBytes([ ccipResult, ccipArgs.extraData ]) ])\n                 };\n\n                 this.emit(\"debug\", { action: \"sendCcipReadCall\", transaction: tx });\n                 try {\n                     const result = await this.#call(tx, blockTag, attempt + 1);\n                     this.emit(\"debug\", { action: \"receiveCcipReadCallResult\", transaction: Object.assign({ }, tx), result });\n                     return result;\n                 } catch (error) {\n                     this.emit(\"debug\", { action: \"receiveCcipReadCallError\", transaction: Object.assign({ }, tx), error });\n                     throw error;\n                 }\n             }\n\n             throw error;\n         }\n    }\n\n    async #checkNetwork<T>(promise: Promise<T>): Promise<T> {\n        const { value } = await resolveProperties({\n            network: this.getNetwork(),\n            value: promise\n        });\n        return value;\n    }\n\n    async call(_tx: TransactionRequest): Promise<string> {\n        const { tx, blockTag } = await resolveProperties({\n            tx: this._getTransactionRequest(_tx),\n            blockTag: this._getBlockTag(_tx.blockTag)\n        });\n\n        return await this.#checkNetwork(this.#call(tx, blockTag, _tx.enableCcipRead ? 0: -1));\n    }\n\n    // Account\n    async #getAccountValue(request: _PerformAccountRequest, _address: AddressLike, _blockTag?: BlockTag): Promise<any> {\n        let address: string | Promise<string> = this._getAddress(_address);\n        let blockTag: string | Promise<string> = this._getBlockTag(_blockTag);\n\n        if (typeof(address) !== \"string\" || typeof(blockTag) !== \"string\") {\n            [ address, blockTag ] = await Promise.all([ address, blockTag ]);\n        }\n\n        return await this.#checkNetwork(this.#perform(Object.assign(request, { address, blockTag })));\n    }\n\n    async getBalance(address: AddressLike, blockTag?: BlockTag): Promise<bigint> {\n        return getBigInt(await this.#getAccountValue({ method: \"getBalance\" }, address, blockTag), \"%response\");\n    }\n\n    async getTransactionCount(address: AddressLike, blockTag?: BlockTag): Promise<number> {\n        return getNumber(await this.#getAccountValue({ method: \"getTransactionCount\" }, address, blockTag), \"%response\");\n    }\n\n    async getCode(address: AddressLike, blockTag?: BlockTag): Promise<string> {\n        return hexlify(await this.#getAccountValue({ method: \"getCode\" }, address, blockTag));\n    }\n\n    async getStorage(address: AddressLike, _position: BigNumberish, blockTag?: BlockTag): Promise<string> {\n        const position = getBigInt(_position, \"position\");\n        return hexlify(await this.#getAccountValue({ method: \"getStorage\", position }, address, blockTag));\n    }\n\n    // Write\n    async broadcastTransaction(signedTx: string): Promise<TransactionResponse> {\n        const { blockNumber, hash, network } = await resolveProperties({\n             blockNumber: this.getBlockNumber(),\n             hash: this._perform({\n                 method: \"broadcastTransaction\",\n                 signedTransaction: signedTx\n             }),\n             network: this.getNetwork()\n        });\n\n        const tx = Transaction.from(signedTx);\n        if (tx.hash !== hash) {\n            throw new Error(\"@TODO: the returned hash did not match\");\n        }\n\n        return this._wrapTransactionResponse(<any>tx, network).replaceableTransaction(blockNumber);\n    }\n\n    async #getBlock(block: BlockTag | string, includeTransactions: boolean): Promise<any> {\n        // @TODO: Add CustomBlockPlugin check\n\n        if (isHexString(block, 32)) {\n            return await this.#perform({\n                method: \"getBlock\", blockHash: block, includeTransactions\n            });\n        }\n\n        let blockTag = this._getBlockTag(block);\n        if (typeof(blockTag) !== \"string\") { blockTag = await blockTag; }\n\n        return await this.#perform({\n            method: \"getBlock\", blockTag, includeTransactions\n        });\n    }\n\n    // Queries\n    async getBlock(block: BlockTag | string, prefetchTxs?: boolean): Promise<null | Block> {\n        const { network, params } = await resolveProperties({\n            network: this.getNetwork(),\n            params: this.#getBlock(block, !!prefetchTxs)\n        });\n        if (params == null) { return null; }\n\n        return this._wrapBlock(params, network);\n    }\n\n    async getTransaction(hash: string): Promise<null | TransactionResponse> {\n        const { network, params } = await resolveProperties({\n            network: this.getNetwork(),\n            params: this.#perform({ method: \"getTransaction\", hash })\n        });\n        if (params == null) { return null; }\n\n        return this._wrapTransactionResponse(params, network);\n    }\n\n    async getTransactionReceipt(hash: string): Promise<null | TransactionReceipt> {\n        const { network, params } = await resolveProperties({\n            network: this.getNetwork(),\n            params: this.#perform({ method: \"getTransactionReceipt\", hash })\n        });\n        if (params == null) { return null; }\n\n        // Some backends did not backfill the effectiveGasPrice into old transactions\n        // in the receipt, so we look it up manually and inject it.\n        if (params.gasPrice == null && params.effectiveGasPrice == null) {\n            const tx = await this.#perform({ method: \"getTransaction\", hash });\n            if (tx == null) { throw new Error(\"report this; could not find tx or effectiveGasPrice\"); }\n            params.effectiveGasPrice = tx.gasPrice;\n        }\n\n        return this._wrapTransactionReceipt(params, network);\n    }\n\n    async getTransactionResult(hash: string): Promise<null | string> {\n        const { result } = await resolveProperties({\n            network: this.getNetwork(),\n            result: this.#perform({ method: \"getTransactionResult\", hash })\n        });\n        if (result == null) { return null; }\n        return hexlify(result);\n    }\n\n    // Bloom-filter Queries\n    async getLogs(_filter: Filter | FilterByBlockHash): Promise<Array<Log>> {\n        let filter = this._getFilter(_filter);\n        if (isPromise(filter)) { filter = await filter; }\n\n        const { network, params } = await resolveProperties({\n            network: this.getNetwork(),\n            params: this.#perform<Array<LogParams>>({ method: \"getLogs\", filter })\n        });\n\n        return params.map((p) => this._wrapLog(p, network));\n    }\n\n    // ENS\n    _getProvider(chainId: number): AbstractProvider {\n        assert(false, \"provider cannot connect to target network\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"_getProvider()\"\n        });\n    }\n\n    async getResolver(name: string): Promise<null | EnsResolver> {\n        return await EnsResolver.fromName(this, name);\n    }\n\n    async getAvatar(name: string): Promise<null | string> {\n        const resolver = await this.getResolver(name);\n        if (resolver) { return await resolver.getAvatar(); }\n        return null;\n    }\n\n    async resolveName(name: string): Promise<null | string>{\n        const resolver = await this.getResolver(name);\n        if (resolver) { return await resolver.getAddress(); }\n        return null;\n    }\n\n    async lookupAddress(address: string): Promise<null | string> {\n        address = getAddress(address);\n        const node = namehash(address.substring(2).toLowerCase() + \".addr.reverse\");\n\n        try {\n\n            const ensAddr = await EnsResolver.getEnsAddress(this);\n            const ensContract = new Contract(ensAddr, [\n                \"function resolver(bytes32) view returns (address)\"\n            ], this);\n\n            const resolver = await ensContract.resolver(node);\n            if (resolver == null || resolver === ZeroAddress) { return null; }\n\n            const resolverContract = new Contract(resolver, [\n                \"function name(bytes32) view returns (string)\"\n            ], this);\n            const name = await resolverContract.name(node);\n\n            // Failed forward resolution\n            const check = await this.resolveName(name);\n            if (check !== address) { return null; }\n\n            return name;\n\n        } catch (error) {\n            // No data was returned from the resolver\n            if (isError(error, \"BAD_DATA\") && error.value === \"0x\") {\n                return null;\n            }\n\n            // Something reerted\n            if (isError(error, \"CALL_EXCEPTION\")) { return null; }\n\n            throw error;\n        }\n\n        return null;\n    }\n\n    async waitForTransaction(hash: string, _confirms?: null | number, timeout?: null | number): Promise<null | TransactionReceipt> {\n        const confirms = (_confirms != null) ? _confirms: 1;\n        if (confirms === 0) { return this.getTransactionReceipt(hash); }\n\n        return new Promise(async (resolve, reject) => {\n            let timer: null | Timer = null;\n\n            const listener = (async (blockNumber: number) => {\n                try {\n                    const receipt = await this.getTransactionReceipt(hash);\n                    if (receipt != null) {\n                        if (blockNumber - receipt.blockNumber + 1 >= confirms) {\n                            resolve(receipt);\n                            //this.off(\"block\", listener);\n                            if (timer) {\n                                clearTimeout(timer);\n                                timer = null;\n                            }\n                            return;\n                        }\n                    }\n                } catch (error) {\n                    console.log(\"EEE\", error);\n                }\n                this.once(\"block\", listener);\n            });\n\n            if (timeout != null) {\n                timer = setTimeout(() => {\n                    if (timer == null) { return; }\n                    timer = null;\n                    this.off(\"block\", listener);\n                    reject(makeError(\"timeout\", \"TIMEOUT\", { reason: \"timeout\" }));\n                }, timeout);\n            }\n\n            listener(await this.getBlockNumber());\n        });\n    }\n\n    async waitForBlock(blockTag?: BlockTag): Promise<Block> {\n        assert(false, \"not implemented yet\", \"NOT_IMPLEMENTED\", {\n            operation: \"waitForBlock\"\n        });\n    }\n\n    /**\n     *  Clear a timer created using the [[_setTimeout]] method.\n     */\n    _clearTimeout(timerId: number): void {\n        const timer = this.#timers.get(timerId);\n        if (!timer) { return; }\n        if (timer.timer) { clearTimeout(timer.timer); }\n        this.#timers.delete(timerId);\n    }\n\n    /**\n     *  Create a timer that will execute %%func%% after at least %%timeout%%\n     *  (in ms). If %%timeout%% is unspecified, then %%func%% will execute\n     *  in the next event loop.\n     *\n     *  [Pausing](AbstractProvider-paused) the provider will pause any\n     *  associated timers.\n     */\n    _setTimeout(_func: () => void, timeout?: number): number {\n        if (timeout == null) { timeout = 0; }\n        const timerId = this.#nextTimer++;\n        const func = () => {\n            this.#timers.delete(timerId);\n            _func();\n        };\n\n        if (this.paused) {\n            this.#timers.set(timerId, { timer: null, func, time: timeout });\n        } else {\n            const timer = setTimeout(func, timeout);\n            this.#timers.set(timerId, { timer, func, time: getTime() });\n        }\n\n        return timerId;\n    }\n\n    /**\n     *  Perform %%func%% on each subscriber.\n     */\n    _forEachSubscriber(func: (s: Subscriber) => void): void {\n        for (const sub of this.#subs.values()) {\n            func(sub.subscriber);\n        }\n    }\n\n    /**\n     *  Sub-classes may override this to customize subscription\n     *  implementations.\n     */\n    _getSubscriber(sub: Subscription): Subscriber {\n        switch (sub.type) {\n            case \"debug\":\n            case \"error\":\n            case \"network\":\n                return new UnmanagedSubscriber(sub.type);\n            case \"block\": {\n                const subscriber = new PollingBlockSubscriber(this);\n                subscriber.pollingInterval = this.pollingInterval;\n                return subscriber;\n            }\n            case \"safe\": case \"finalized\":\n                return new PollingBlockTagSubscriber(this, sub.type);\n            case \"event\":\n                return new PollingEventSubscriber(this, sub.filter);\n            case \"transaction\":\n                return new PollingTransactionSubscriber(this, sub.hash);\n            case \"orphan\":\n                return new PollingOrphanSubscriber(this, sub.filter);\n        }\n\n        throw new Error(`unsupported event: ${ sub.type }`);\n    }\n\n    /**\n     *  If a [[Subscriber]] fails and needs to replace itself, this\n     *  method may be used.\n     *\n     *  For example, this is used for providers when using the\n     *  ``eth_getFilterChanges`` method, which can return null if state\n     *  filters are not supported by the backend, allowing the Subscriber\n     *  to swap in a [[PollingEventSubscriber]].\n     */\n    _recoverSubscriber(oldSub: Subscriber, newSub: Subscriber): void {\n        for (const sub of this.#subs.values()) {\n            if (sub.subscriber === oldSub) {\n                if (sub.started) { sub.subscriber.stop(); }\n                sub.subscriber = newSub;\n                if (sub.started) { newSub.start(); }\n                if (this.#pausedState != null) { newSub.pause(this.#pausedState); }\n                break;\n            }\n        }\n    }\n\n    async #hasSub(event: ProviderEvent, emitArgs?: Array<any>): Promise<null | Sub> {\n        let sub = await getSubscription(event, this);\n        // This is a log that is removing an existing log; we actually want\n        // to emit an orphan event for the removed log\n        if (sub.type === \"event\" && emitArgs && emitArgs.length > 0 && emitArgs[0].removed === true) {\n            sub = await getSubscription({ orphan: \"drop-log\", log: emitArgs[0] }, this);\n        }\n        return this.#subs.get(sub.tag) || null;\n    }\n\n    async #getSub(event: ProviderEvent): Promise<Sub> {\n        const subscription = await getSubscription(event, this);\n\n        // Prevent tampering with our tag in any subclass' _getSubscriber\n        const tag = subscription.tag;\n\n        let sub = this.#subs.get(tag);\n        if (!sub) {\n            const subscriber = this._getSubscriber(subscription);\n\n            const addressableMap = new WeakMap();\n            const nameMap = new Map();\n            sub = { subscriber, tag, addressableMap, nameMap, started: false, listeners: [ ] };\n            this.#subs.set(tag, sub);\n        }\n\n        return sub;\n    }\n\n    async on(event: ProviderEvent, listener: Listener): Promise<this> {\n        const sub = await this.#getSub(event);\n        sub.listeners.push({ listener, once: false });\n        if (!sub.started) {\n            sub.subscriber.start();\n            sub.started = true;\n            if (this.#pausedState != null) { sub.subscriber.pause(this.#pausedState); }\n        }\n        return this;\n    }\n\n    async once(event: ProviderEvent, listener: Listener): Promise<this> {\n        const sub = await this.#getSub(event);\n        sub.listeners.push({ listener, once: true });\n        if (!sub.started) {\n            sub.subscriber.start();\n            sub.started = true;\n            if (this.#pausedState != null) { sub.subscriber.pause(this.#pausedState); }\n        }\n        return this;\n    }\n\n    async emit(event: ProviderEvent, ...args: Array<any>): Promise<boolean> {\n        const sub = await this.#hasSub(event, args);\n        // If there is not subscription or if a recent emit removed\n        // the last of them (which also deleted the sub) do nothing\n        if (!sub || sub.listeners.length === 0) { return false; };\n\n        const count = sub.listeners.length;\n        sub.listeners = sub.listeners.filter(({ listener, once }) => {\n            const payload = new EventPayload(this, (once ? null: listener), event);\n            try {\n                listener.call(this, ...args, payload);\n            } catch(error) { }\n            return !once;\n        });\n\n        if (sub.listeners.length === 0) {\n            if (sub.started) { sub.subscriber.stop(); }\n            this.#subs.delete(sub.tag);\n        }\n\n        return (count > 0);\n    }\n\n    async listenerCount(event?: ProviderEvent): Promise<number> {\n        if (event) {\n            const sub = await this.#hasSub(event);\n            if (!sub) { return 0; }\n            return sub.listeners.length;\n        }\n\n        let total = 0;\n        for (const { listeners } of this.#subs.values()) {\n            total += listeners.length;\n        }\n        return total;\n    }\n\n    async listeners(event?: ProviderEvent): Promise<Array<Listener>> {\n        if (event) {\n            const sub = await this.#hasSub(event);\n            if (!sub) { return  [ ]; }\n            return sub.listeners.map(({ listener }) => listener);\n        }\n        let result: Array<Listener> = [ ];\n        for (const { listeners } of this.#subs.values()) {\n            result = result.concat(listeners.map(({ listener }) => listener));\n        }\n        return result;\n    }\n\n    async off(event: ProviderEvent, listener?: Listener): Promise<this> {\n        const sub = await this.#hasSub(event);\n        if (!sub) { return this; }\n\n        if (listener) {\n            const index = sub.listeners.map(({ listener }) => listener).indexOf(listener);\n            if (index >= 0) { sub.listeners.splice(index, 1); }\n        }\n\n        if (!listener || sub.listeners.length === 0) {\n            if (sub.started) { sub.subscriber.stop(); }\n            this.#subs.delete(sub.tag);\n        }\n\n        return this;\n    }\n\n    async removeAllListeners(event?: ProviderEvent): Promise<this> {\n        if (event) {\n            const { tag, started, subscriber } = await this.#getSub(event);\n            if (started) { subscriber.stop(); }\n            this.#subs.delete(tag);\n        } else {\n            for (const [ tag, { started, subscriber } ] of this.#subs) {\n                if (started) { subscriber.stop(); }\n                this.#subs.delete(tag);\n            }\n        }\n        return this;\n    }\n\n    // Alias for \"on\"\n    async addListener(event: ProviderEvent, listener: Listener): Promise<this> {\n       return await this.on(event, listener);\n    }\n\n    // Alias for \"off\"\n    async removeListener(event: ProviderEvent, listener: Listener): Promise<this> {\n       return this.off(event, listener);\n    }\n\n    /**\n     *  If this provider has been destroyed using the [[destroy]] method.\n     *\n     *  Once destroyed, all resources are reclaimed, internal event loops\n     *  and timers are cleaned up and no further requests may be sent to\n     *  the provider.\n     */\n    get destroyed(): boolean {\n        return this.#destroyed;\n    }\n\n    /**\n     *  Sub-classes may use this to shutdown any sockets or release their\n     *  resources and reject any pending requests.\n     *\n     *  Sub-classes **must** call ``super.destroy()``.\n     */\n    destroy(): void {\n        // Stop all listeners\n        this.removeAllListeners();\n\n        // Shut down all tiemrs\n        for (const timerId of this.#timers.keys()) {\n            this._clearTimeout(timerId);\n        }\n\n        this.#destroyed = true;\n    }\n\n    /**\n     *  Whether the provider is currently paused.\n     *\n     *  A paused provider will not emit any events, and generally should\n     *  not make any requests to the network, but that is up to sub-classes\n     *  to manage.\n     *\n     *  Setting ``paused = true`` is identical to calling ``.pause(false)``,\n     *  which will buffer any events that occur while paused until the\n     *  provider is unpaused.\n     */\n    get paused(): boolean { return (this.#pausedState != null); }\n    set paused(pause: boolean) {\n        if (!!pause === this.paused) { return; }\n\n        if (this.paused) {\n            this.resume();\n        } else {\n            this.pause(false);\n        }\n    }\n\n    /**\n     *  Pause the provider. If %%dropWhilePaused%%, any events that occur\n     *  while paused are dropped, otherwise all events will be emitted once\n     *  the provider is unpaused.\n     */\n    pause(dropWhilePaused?: boolean): void {\n        this.#lastBlockNumber = -1;\n\n        if (this.#pausedState != null) {\n            if (this.#pausedState == !!dropWhilePaused) { return; }\n            assert(false, \"cannot change pause type; resume first\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"pause\"\n            });\n        }\n\n        this._forEachSubscriber((s) => s.pause(dropWhilePaused));\n        this.#pausedState = !!dropWhilePaused;\n\n        for (const timer of this.#timers.values()) {\n            // Clear the timer\n            if (timer.timer) { clearTimeout(timer.timer); }\n\n            // Remaining time needed for when we become unpaused\n            timer.time = getTime() - timer.time;\n        }\n    }\n\n    /**\n     *  Resume the provider.\n     */\n    resume(): void {\n        if (this.#pausedState == null) { return; }\n\n        this._forEachSubscriber((s) => s.resume());\n        this.#pausedState = null;\n        for (const timer of this.#timers.values()) {\n            // Remaining time when we were paused\n            let timeout = timer.time;\n            if (timeout < 0) { timeout = 0; }\n\n            // Start time (in cause paused, so we con compute remaininf time)\n            timer.time = getTime();\n\n            // Start the timer\n            setTimeout(timer.func, timeout);\n        }\n    }\n}\n\n\nfunction _parseString(result: string, start: number): null | string {\n    try {\n        const bytes = _parseBytes(result, start);\n        if (bytes) { return toUtf8String(bytes); }\n    } catch(error) { }\n    return null;\n}\n\nfunction _parseBytes(result: string, start: number): null | string {\n    if (result === \"0x\") { return null; }\n    try {\n        const offset = getNumber(dataSlice(result, start, start + 32));\n        const length = getNumber(dataSlice(result, offset, offset + 32));\n\n        return dataSlice(result, offset + 32, offset + 32 + length);\n    } catch (error) { }\n    return null;\n}\n\nfunction numPad(value: number): Uint8Array {\n    const result = toBeArray(value);\n    if (result.length > 32) { throw new Error(\"internal; should not happen\"); }\n\n    const padded = new Uint8Array(32);\n    padded.set(result, 32 - result.length);\n    return padded;\n}\n\nfunction bytesPad(value: Uint8Array): Uint8Array {\n    if ((value.length % 32) === 0) { return value; }\n\n    const result = new Uint8Array(Math.ceil(value.length / 32) * 32);\n    result.set(value);\n    return result;\n}\n\nconst empty: Uint8Array = new Uint8Array([ ]);\n\n// ABI Encodes a series of (bytes, bytes, ...)\nfunction encodeBytes(datas: Array<BytesLike>): string {\n    const result: Array<Uint8Array> = [ ];\n\n    let byteCount = 0;\n\n    // Add place-holders for pointers as we add items\n    for (let i = 0; i < datas.length; i++) {\n        result.push(empty);\n        byteCount += 32;\n    }\n\n    for (let i = 0; i < datas.length; i++) {\n        const data = getBytes(datas[i]);\n\n        // Update the bytes offset\n        result[i] = numPad(byteCount);\n\n        // The length and padded value of data\n        result.push(numPad(data.length));\n        result.push(bytesPad(data));\n        byteCount += 32 + Math.ceil(data.length / 32) * 32;\n    }\n\n    return concat(result);\n}\n\nconst zeros = \"0x0000000000000000000000000000000000000000000000000000000000000000\"\nfunction parseOffchainLookup(data: string): CcipArgs {\n    const result: CcipArgs = {\n        sender: \"\", urls: [ ], calldata: \"\", selector: \"\", extraData: \"\", errorArgs: [ ]\n    };\n\n    assert(dataLength(data) >= 5 * 32, \"insufficient OffchainLookup data\", \"OFFCHAIN_FAULT\", {\n        reason: \"insufficient OffchainLookup data\"\n    });\n\n    const sender = dataSlice(data, 0, 32);\n    assert(dataSlice(sender, 0, 12) === dataSlice(zeros, 0, 12), \"corrupt OffchainLookup sender\", \"OFFCHAIN_FAULT\", {\n        reason: \"corrupt OffchainLookup sender\"\n    });\n    result.sender = dataSlice(sender, 12);\n\n    // Read the URLs from the response\n    try {\n        const urls: Array<string> = [];\n        const urlsOffset = getNumber(dataSlice(data, 32, 64));\n        const urlsLength = getNumber(dataSlice(data, urlsOffset, urlsOffset + 32));\n        const urlsData = dataSlice(data, urlsOffset + 32);\n        for (let u = 0; u < urlsLength; u++) {\n            const url = _parseString(urlsData, u * 32);\n            if (url == null) { throw new Error(\"abort\"); }\n            urls.push(url);\n        }\n        result.urls = urls;\n    } catch (error) {\n        assert(false, \"corrupt OffchainLookup urls\", \"OFFCHAIN_FAULT\", {\n            reason: \"corrupt OffchainLookup urls\"\n        });\n    }\n\n    // Get the CCIP calldata to forward\n    try {\n        const calldata = _parseBytes(data, 64);\n        if (calldata == null) { throw new Error(\"abort\"); }\n        result.calldata = calldata;\n    } catch (error) {\n        assert(false, \"corrupt OffchainLookup calldata\", \"OFFCHAIN_FAULT\", {\n            reason: \"corrupt OffchainLookup calldata\"\n        });\n    }\n\n    // Get the callbackSelector (bytes4)\n    assert(dataSlice(data, 100, 128) === dataSlice(zeros, 0, 28), \"corrupt OffchainLookup callbaackSelector\", \"OFFCHAIN_FAULT\", {\n        reason: \"corrupt OffchainLookup callbaackSelector\"\n    });\n    result.selector = dataSlice(data, 96, 100);\n\n    // Get the extra data to send back to the contract as context\n    try {\n        const extraData = _parseBytes(data, 128);\n        if (extraData == null) { throw new Error(\"abort\"); }\n        result.extraData = extraData;\n    } catch (error) {\n        assert(false, \"corrupt OffchainLookup extraData\", \"OFFCHAIN_FAULT\", {\n            reason: \"corrupt OffchainLookup extraData\"\n        });\n    }\n\n    result.errorArgs = \"sender,urls,calldata,selector,extraData\".split(/,/).map((k) => (<any>result)[k])\n\n    return result;\n}\n"], "mappings": "AAAA;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASA,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SACIC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EACnDC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAC9BC,eAAe,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAC3DC,YAAY,EACZC,SAAS,EAAEC,UAAU,EACrBC,gBAAgB,EAAEC,YAAY,EAAEC,iBAAiB,EACjDC,YAAY,QACT,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SACIC,WAAW,EAAEC,SAAS,EAAEC,wBAAwB,EAAEC,yBAAyB,QACxE,aAAa;AACpB,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,WAAW,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,EAAEC,kBAAkB,EAAEC,mBAAmB,QAAQ,eAAe;AACzG,SACIC,sBAAsB,EAAEC,yBAAyB,EAAEC,sBAAsB,EACzEC,uBAAuB,EAAEC,4BAA4B,QAClD,yBAAyB;AAuBhC;AACA,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AAEtB,MAAMC,kBAAkB,GAAG,EAAE;AAE7B,SAASC,SAASA,CAAUC,KAAU;EAClC,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAK,KAAK,UAAU;AACtD;AAEA,SAASC,MAAMA,CAACC,MAAc,EAAEH,KAAU;EACtC,OAAOG,MAAM,GAAG,GAAG,GAAGC,IAAI,CAACC,SAAS,CAACL,KAAK,EAAE,CAACM,CAAC,EAAEC,CAAC,KAAI;IACjD,IAAIA,CAAC,IAAI,IAAI,EAAE;MAAE,OAAO,MAAM;;IAC9B,IAAI,OAAOA,CAAE,KAAK,QAAQ,EAAE;MAAE,OAAO,UAAWA,CAAC,CAACC,QAAQ,EAAG,EAAE;;IAC/D,IAAI,OAAOD,CAAE,KAAK,QAAQ,EAAE;MAAE,OAAOA,CAAC,CAACE,WAAW,EAAE;;IAEpD;IACA,IAAI,OAAOF,CAAE,KAAK,QAAQ,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,EAAE;MAC7C,MAAMK,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACL,CAAC,CAAC;MAC3BK,IAAI,CAACE,IAAI,EAAE;MACX,OAAOF,IAAI,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;QAC9BD,KAAK,CAACC,GAAG,CAAC,GAAGV,CAAC,CAACU,GAAG,CAAC;QACnB,OAAOD,KAAK;MAChB,CAAC,EAAO,EAAG,CAAC;;IAGhB,OAAOT,CAAC;EACZ,CAAC,CAAC;AACN;AA+FA;;;;;AAKA,OAAM,MAAOW,mBAAmB;EAC5B;;;EAGAC,IAAI;EAEJ;;;EAGAC,YAAYD,IAAY;IAAI5C,gBAAgB,CAAsB,IAAI,EAAE;MAAE4C;IAAI,CAAE,CAAC;EAAE;EAEnFE,KAAKA,CAAA,GAAW;EAChBC,IAAIA,CAAA,GAAW;EAEfC,KAAKA,CAACC,eAAyB,GAAU;EACzCC,MAAMA,CAAA,GAAW;;AAcrB,SAASC,IAAIA,CAAU1B,KAAQ;EAC3B,OAAOI,IAAI,CAACuB,KAAK,CAACvB,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC,CAAC;AAC5C;AAEA,SAAS4B,SAASA,CAACC,KAAoB;EACnCA,KAAK,GAAGnB,KAAK,CAACoB,IAAI,CAAE,IAAIC,GAAG,CAACF,KAAK,CAAC,CAAEG,MAAM,EAAE,CAAC;EAC7CH,KAAK,CAACf,IAAI,EAAE;EACZ,OAAOe,KAAK;AAChB;AAGA,eAAeI,eAAeA,CAACC,MAAqB,EAAEC,QAA0B;EAC5E,IAAID,MAAM,IAAI,IAAI,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,eAAe,CAAC;;EAEtD;EACA,IAAI1B,KAAK,CAACC,OAAO,CAACuB,MAAM,CAAC,EAAE;IAAEA,MAAM,GAAG;MAAEG,MAAM,EAAEH;IAAM,CAAE;;EAExD,IAAI,OAAOA,MAAO,KAAK,QAAQ,EAAE;IAC7B,QAAQA,MAAM;MACV,KAAK,OAAO;MACZ,KAAK,OAAO;MACZ,KAAK,OAAO;MACZ,KAAK,WAAW;MAChB,KAAK,SAAS;MACd,KAAK,SAAS;MACd,KAAK,MAAM;QAAE;UACT,OAAO;YAAEI,IAAI,EAAEJ,MAAM;YAAEK,GAAG,EAAEL;UAAM,CAAE;;;;EAKhD,IAAIvE,WAAW,CAACuE,MAAM,EAAE,EAAE,CAAC,EAAE;IACzB,MAAMM,IAAI,GAAGN,MAAM,CAACzB,WAAW,EAAE;IACjC,OAAO;MAAE6B,IAAI,EAAE,aAAa;MAAEC,GAAG,EAAErC,MAAM,CAAC,IAAI,EAAE;QAAEsC;MAAI,CAAE,CAAC;MAAEA;IAAI,CAAE;;EAGrE,IAAUN,MAAO,CAACO,MAAM,EAAE;IACtB,MAAMC,KAAK,GAAiBR,MAAM;IAClC;IACA,OAAO;MAAEI,IAAI,EAAE,QAAQ;MAAEC,GAAG,EAAErC,MAAM,CAAC,QAAQ,EAAEwC,KAAK,CAAC;MAAEC,MAAM,EAAEjB,IAAI,CAACgB,KAAK;IAAC,CAAE;;EAGhF,IAAWR,MAAO,CAACU,OAAO,IAAUV,MAAO,CAACG,MAAM,EAAG;IACjD,MAAMK,KAAK,GAAgBR,MAAM;IAEjC,MAAMS,MAAM,GAAQ;MAChBN,MAAM,EAAG,CAACK,KAAK,CAACL,MAAM,IAAI,EAAE,EAAEQ,GAAG,CAAEC,CAAC,IAAI;QACpC,IAAIA,CAAC,IAAI,IAAI,EAAE;UAAE,OAAO,IAAI;;QAC5B,IAAIpC,KAAK,CAACC,OAAO,CAACmC,CAAC,CAAC,EAAE;UAClB,OAAOlB,SAAS,CAACkB,CAAC,CAACD,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrC,WAAW,EAAE,CAAC,CAAC;;QAEnD,OAAOqC,CAAC,CAACrC,WAAW,EAAE;MAC1B,CAAC;KACJ;IAED,IAAIiC,KAAK,CAACE,OAAO,EAAE;MACf,MAAMG,SAAS,GAAkB,EAAG;MACpC,MAAMC,QAAQ,GAAyB,EAAG;MAE1C,MAAMC,UAAU,GAAIC,IAAiB,IAAI;QACrC,IAAIvF,WAAW,CAACuF,IAAI,CAAC,EAAE;UACnBH,SAAS,CAACI,IAAI,CAACD,IAAI,CAAC;SACvB,MAAM;UACHF,QAAQ,CAACG,IAAI,CAAC,CAAC,YAAW;YACtBJ,SAAS,CAACI,IAAI,CAAC,MAAMjG,cAAc,CAACgG,IAAI,EAAEf,QAAQ,CAAC,CAAC;UACxD,CAAC,EAAC,CAAE,CAAC;;MAEb,CAAC;MAED,IAAIzB,KAAK,CAACC,OAAO,CAAC+B,KAAK,CAACE,OAAO,CAAC,EAAE;QAC9BF,KAAK,CAACE,OAAO,CAACQ,OAAO,CAACH,UAAU,CAAC;OACpC,MAAM;QACHA,UAAU,CAACP,KAAK,CAACE,OAAO,CAAC;;MAE7B,IAAII,QAAQ,CAACK,MAAM,EAAE;QAAE,MAAMC,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;;MAClDL,MAAM,CAACC,OAAO,GAAGhB,SAAS,CAACmB,SAAS,CAACF,GAAG,CAAEW,CAAC,IAAKA,CAAC,CAAC/C,WAAW,EAAE,CAAC,CAAC;;IAGrE,OAAO;MAAEkC,MAAM;MAAEJ,GAAG,EAAErC,MAAM,CAAC,OAAO,EAAEyC,MAAM,CAAC;MAAEL,IAAI,EAAE;IAAO,CAAE;;EAGlEnE,cAAc,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE+D,MAAM,CAAC;AACnE;AAEA,SAASuB,OAAOA,CAAA;EAAa,OAAQ,IAAIC,IAAI,EAAE,CAAED,OAAO,EAAE;AAAE;AA0H5D,MAAME,cAAc,GAAG;EACnBC,YAAY,EAAE,GAAG;EACjBC,eAAe,EAAE;CACpB;AAWD;;;;;;AAMA,OAAM,MAAOC,gBAAgB;EAEzB,CAAAC,IAAK;EACL,CAAAC,OAAQ;EAER;EACA,CAAAC,WAAY;EAEZ,CAAAC,SAAU;EAEV,CAAAC,cAAe;EACN,CAAAC,UAAW;EAEpB,CAAAC,YAAa;EAEb;EACA,CAAAC,eAAgB;EAEhB,CAAAC,SAAU;EACV,CAAAC,MAAO;EAEP,CAAAC,eAAgB;EAEhB,CAAAC,OAAQ;EAER;;;;;EAKAtD,YAAYuD,QAA6B,EAAED,OAAiC;IACxE,IAAI,CAAC,CAAAA,OAAQ,GAAG7D,MAAM,CAAC+D,MAAM,CAAC,EAAG,EAAEjB,cAAc,EAAEe,OAAO,IAAI,EAAG,CAAC;IAElE,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACpB,IAAI,CAAC,CAAAP,UAAW,GAAG,IAAI;MACvB,IAAI,CAAC,CAAAD,cAAe,GAAG,IAAI;KAC9B,MAAM,IAAIQ,QAAQ,EAAE;MACjB,MAAME,OAAO,GAAG7F,OAAO,CAAC8C,IAAI,CAAC6C,QAAQ,CAAC;MACtC,IAAI,CAAC,CAAAP,UAAW,GAAG,KAAK;MACxB,IAAI,CAAC,CAAAD,cAAe,GAAGb,OAAO,CAACwB,OAAO,CAACD,OAAO,CAAC;MAC/CE,UAAU,CAAC,MAAK;QAAG,IAAI,CAACC,IAAI,CAAC,SAAS,EAAEH,OAAO,EAAE,IAAI,CAAC;MAAE,CAAC,EAAE,CAAC,CAAC;KAChE,MAAM;MACH,IAAI,CAAC,CAAAT,UAAW,GAAG,KAAK;MACxB,IAAI,CAAC,CAAAD,cAAe,GAAG,IAAI;;IAG/B,IAAI,CAAC,CAAAG,eAAgB,GAAG,CAAC,CAAC;IAE1B,IAAI,CAAC,CAAAD,YAAa,GAAG,IAAIY,GAAG,EAAE;IAE9B,IAAI,CAAC,CAAAlB,IAAK,GAAG,IAAIkB,GAAG,EAAE;IACtB,IAAI,CAAC,CAAAjB,OAAQ,GAAG,IAAIiB,GAAG,EAAE;IACzB,IAAI,CAAC,CAAAhB,WAAY,GAAG,IAAI;IAExB,IAAI,CAAC,CAAAC,SAAU,GAAG,KAAK;IAEvB,IAAI,CAAC,CAAAK,SAAU,GAAG,CAAC;IACnB,IAAI,CAAC,CAAAC,MAAO,GAAG,IAAIS,GAAG,EAAE;IAExB,IAAI,CAAC,CAAAR,eAAgB,GAAG,KAAK;EACjC;EAEA,IAAIZ,eAAeA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAa,OAAQ,CAACb,eAAe;EAAE;EAEtE;;;;EAIA,IAAI1B,QAAQA,CAAA;IAAW,OAAO,IAAI;EAAE;EAEpC;;;EAGA,IAAI6B,OAAOA,CAAA;IACP,OAAOtD,KAAK,CAACoB,IAAI,CAAC,IAAI,CAAC,CAAAkC,OAAQ,CAAChC,MAAM,EAAE,CAAC;EAC7C;EAEA;;;EAGAkD,YAAYA,CAACC,MAA8B;IACvC,IAAI,IAAI,CAAC,CAAAnB,OAAQ,CAACoB,GAAG,CAACD,MAAM,CAAChE,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIiB,KAAK,CAAC,mCAAoC+C,MAAM,CAAChE,IAAK,GAAG,CAAC;;IAExE,IAAI,CAAC,CAAA6C,OAAQ,CAACqB,GAAG,CAACF,MAAM,CAAChE,IAAI,EAAGgE,MAAM,CAACG,OAAO,CAAC,IAAI,CAAC,CAAC;IACrD,OAAO,IAAI;EACf;EAEA;;;EAGAC,SAASA,CAA4DpE,IAAY;IAC7E,OAAW,IAAI,CAAC,CAAA6C,OAAQ,CAACoB,GAAG,CAACjE,IAAI,CAAC,IAAK,IAAI;EAC/C;EAEA;;;;EAIA,IAAIsD,eAAeA,CAAA;IAAc,OAAO,IAAI,CAAC,CAAAA,eAAgB;EAAE;EAC/D,IAAIA,eAAeA,CAACzE,KAAc;IAAI,IAAI,CAAC,CAAAyE,eAAgB,GAAG,CAAC,CAACzE,KAAK;EAAE;EAEvE;EACA,MAAM,CAAAwF,OAAQC,CAAUC,GAAyB;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAAC,CAAAjB,OAAQ,CAACd,YAAY;IAE1C;IACA,IAAI+B,OAAO,GAAG,CAAC,EAAE;MAAE,OAAO,MAAM,IAAI,CAACC,QAAQ,CAACF,GAAG,CAAC;;IAElD;IACA,MAAMnD,GAAG,GAAGrC,MAAM,CAACwF,GAAG,CAACG,MAAM,EAAEH,GAAG,CAAC;IAEnC,IAAIF,OAAO,GAAG,IAAI,CAAC,CAAAnB,YAAa,CAACe,GAAG,CAAC7C,GAAG,CAAC;IACzC,IAAI,CAACiD,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI,CAACI,QAAQ,CAACF,GAAG,CAAC;MAE5B,IAAI,CAAC,CAAArB,YAAa,CAACgB,GAAG,CAAC9C,GAAG,EAAEiD,OAAO,CAAC;MAEpCT,UAAU,CAAC,MAAK;QACZ,IAAI,IAAI,CAAC,CAAAV,YAAa,CAACe,GAAG,CAAC7C,GAAG,CAAC,KAAKiD,OAAO,EAAE;UACzC,IAAI,CAAC,CAAAnB,YAAa,CAACyB,MAAM,CAACvD,GAAG,CAAC;;MAEtC,CAAC,EAAEoD,OAAO,CAAC;;IAGf,OAAO,MAAMH,OAAO;EACxB;EAEA;;;EAGA,MAAMO,aAAaA,CAACC,EAA4B,EAAEC,QAAgB,EAAEC,IAAmB;IACnF,IAAI,IAAI,CAACzB,eAAe,IAAIyB,IAAI,CAAC7C,MAAM,KAAK,CAAC,IAAI2C,EAAE,CAACG,EAAE,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAE7E,MAAMC,MAAM,GAAGJ,EAAE,CAACG,EAAE,CAAC1F,WAAW,EAAE;IAClC,MAAM4F,IAAI,GAAGJ,QAAQ,CAACxF,WAAW,EAAE;IAEnC,MAAM6F,aAAa,GAAkB,EAAG;IAExC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAAC7C,MAAM,EAAEkD,CAAC,EAAE,EAAE;MAClC,MAAMC,GAAG,GAAGN,IAAI,CAACK,CAAC,CAAC;MAEnB;MACA,MAAME,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,UAAU,EAAEN,MAAM,CAAC,CAACM,OAAO,CAAC,QAAQ,EAAEL,IAAI,CAAC;MAEpE;MACA;MAEA;MACA;MACA;MACA;MACA,MAAMM,OAAO,GAAG,IAAIvI,YAAY,CAACqI,IAAI,CAAC;MACtC,IAAID,GAAG,CAACI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9BD,OAAO,CAACE,IAAI,GAAG;UAAER,IAAI;UAAED;QAAM,CAAE;;MAGnC,IAAI,CAACpB,IAAI,CAAC,OAAO,EAAE;QAAE8B,MAAM,EAAE,0BAA0B;QAAEH,OAAO;QAAEI,KAAK,EAAER,CAAC;QAAEL;MAAI,CAAE,CAAC;MAEnF,IAAIc,YAAY,GAAG,eAAe;MAElC;MACA,IAAIC,IAAmB;MACvB,IAAI;QACAA,IAAI,GAAG,MAAMN,OAAO,CAACO,IAAI,EAAE;OAC9B,CAAC,OAAOC,KAAU,EAAE;QACjB;QACA;QACAb,aAAa,CAACnD,IAAI,CAACgE,KAAK,CAACC,OAAO,CAAC;QACjC,IAAI,CAACpC,IAAI,CAAC,OAAO,EAAE;UAAE8B,MAAM,EAAE,2BAA2B;UAAEH,OAAO;UAAEU,MAAM,EAAE;YAAEF;UAAK;QAAE,CAAE,CAAC;QACvF;;MAGJ,IAAI;QACA,MAAME,MAAM,GAAGJ,IAAI,CAACK,QAAQ;QAC5B,IAAID,MAAM,CAAChB,IAAI,EAAE;UACb,IAAI,CAACrB,IAAI,CAAC,OAAO,EAAE;YAAE8B,MAAM,EAAE,4BAA4B;YAAEH,OAAO;YAAEU;UAAM,CAAE,CAAC;UAC7E,OAAOA,MAAM,CAAChB,IAAI;;QAEtB,IAAIgB,MAAM,CAACD,OAAO,EAAE;UAAEJ,YAAY,GAAGK,MAAM,CAACD,OAAO;;QACnD,IAAI,CAACpC,IAAI,CAAC,OAAO,EAAE;UAAE8B,MAAM,EAAE,2BAA2B;UAAEH,OAAO;UAAEU;QAAM,CAAE,CAAC;OAC/E,CAAC,OAAOF,KAAK,EAAE;MAEhB;MACAjJ,MAAM,CAAC+I,IAAI,CAACM,UAAU,GAAG,GAAG,IAAIN,IAAI,CAACM,UAAU,IAAI,GAAG,EAAE,yCAA0CP,YAAa,EAAE,EAC7G,gBAAgB,EAAE;QAAEQ,MAAM,EAAE,sBAAsB;QAAEC,WAAW,EAAEzB,EAAE;QAAE0B,IAAI,EAAE;UAAElB,GAAG;UAAEQ;QAAY;MAAE,CAAE,CAAC;MAEvG;MACAV,aAAa,CAACnD,IAAI,CAAC6D,YAAY,CAAC;;IAGpC9I,MAAM,CAAC,KAAK,EAAE,wCAAyCoI,aAAa,CAACzD,GAAG,CAAE8E,CAAC,IAAKvH,IAAI,CAACC,SAAS,CAACsH,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAE,EAAE,EAAE,gBAAgB,EAAE;MAChIJ,MAAM,EAAE,kBAAkB;MAC1BC,WAAW,EAAEzB,EAAE;MAAE0B,IAAI,EAAE;QAAExB,IAAI;QAAEI;MAAa;KAC/C,CAAC;EACN;EAEA;;;;;EAKAuB,UAAUA,CAAC7H,KAAkB,EAAE6E,OAAgB;IAC3C,OAAO,IAAI3F,KAAK,CAACN,WAAW,CAACoB,KAAK,CAAC,EAAE,IAAI,CAAC;EAC9C;EAEA;;;;;EAKA8H,QAAQA,CAAC9H,KAAgB,EAAE6E,OAAgB;IACvC,OAAO,IAAIzF,GAAG,CAACP,SAAS,CAACmB,KAAK,CAAC,EAAE,IAAI,CAAC;EAC1C;EAEA;;;;;EAKA+H,uBAAuBA,CAAC/H,KAA+B,EAAE6E,OAAgB;IACrE,OAAO,IAAIxF,kBAAkB,CAACP,wBAAwB,CAACkB,KAAK,CAAC,EAAE,IAAI,CAAC;EACxE;EAEA;;;;;EAKAgI,wBAAwBA,CAAChC,EAA6B,EAAEnB,OAAgB;IACpE,OAAO,IAAIvF,mBAAmB,CAACP,yBAAyB,CAACiH,EAAE,CAAC,EAAE,IAAI,CAAC;EACvE;EAEA;;;;;;EAMAiC,cAAcA,CAAA;IACV/J,MAAM,CAAC,KAAK,EAAE,iCAAiC,EAAE,uBAAuB,EAAE;MACtEgK,SAAS,EAAE;KACd,CAAC;EACN;EAEA;;;;;;EAMA,MAAMtC,QAAQA,CAAUF,GAAyB;IAC7CxH,MAAM,CAAC,KAAK,EAAE,uBAAwBwH,GAAG,CAACG,MAAO,EAAE,EAAE,uBAAuB,EAAE;MAC1EqC,SAAS,EAAExC,GAAG,CAACG,MAAM;MACrB6B,IAAI,EAAEhC;KACT,CAAC;EACN;EAEA;EAEA,MAAMyC,cAAcA,CAAA;IAChB,MAAMC,WAAW,GAAGtK,SAAS,CAAC,MAAM,IAAI,CAAC,CAAA0H,OAAQ,CAAC;MAAEK,MAAM,EAAE;IAAgB,CAAE,CAAC,EAAE,WAAW,CAAC;IAC7F,IAAI,IAAI,CAAC,CAAAvB,eAAgB,IAAI,CAAC,EAAE;MAAE,IAAI,CAAC,CAAAA,eAAgB,GAAG8D,WAAW;;IACrE,OAAOA,WAAW;EACtB;EAEA;;;;;EAKAC,WAAWA,CAACzF,OAAoB;IAC5B,OAAO1F,cAAc,CAAC0F,OAAO,EAAE,IAAI,CAAC;EACxC;EAEA;;;;EAIA0F,YAAYA,CAACC,QAAmB;IAC5B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAAE,OAAO,QAAQ;;IAEvC,QAAQA,QAAQ;MACZ,KAAK,UAAU;QACX,OAAO,KAAK;MAChB,KAAK,WAAW;MAChB,KAAK,QAAQ;MACb,KAAK,SAAS;MACd,KAAK,MAAM;QACP,OAAOA,QAAQ;;IAIvB,IAAI5K,WAAW,CAAC4K,QAAQ,CAAC,EAAE;MACvB,IAAI5K,WAAW,CAAC4K,QAAQ,EAAE,EAAE,CAAC,EAAE;QAAE,OAAOA,QAAQ;;MAChD,OAAOjK,UAAU,CAACiK,QAAQ,CAAC;;IAG/B,IAAI,OAAOA,QAAS,KAAK,QAAQ,EAAE;MAC/BA,QAAQ,GAAGzK,SAAS,CAACyK,QAAQ,EAAE,UAAU,CAAC;;IAG9C,IAAI,OAAOA,QAAS,KAAK,QAAQ,EAAE;MAC/B,IAAIA,QAAQ,IAAI,CAAC,EAAE;QAAE,OAAOjK,UAAU,CAACiK,QAAQ,CAAC;;MAChD,IAAI,IAAI,CAAC,CAAAjE,eAAgB,IAAI,CAAC,EAAE;QAAE,OAAOhG,UAAU,CAAC,IAAI,CAAC,CAAAgG,eAAgB,GAAGiE,QAAQ,CAAC;;MACrF,OAAO,IAAI,CAACJ,cAAc,EAAE,CAAClI,IAAI,CAAEuI,CAAC,IAAKlK,UAAU,CAACkK,CAAC,GAAWD,QAAQ,CAAC,CAAC;;IAG9EpK,cAAc,CAAC,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAEoK,QAAQ,CAAC;EACnE;EAEA;;;;;EAKAE,UAAUA,CAAC9F,MAAkC;IAEzC;IACA,MAAMN,MAAM,GAAG,CAACM,MAAM,CAACN,MAAM,IAAI,EAAG,EAAEQ,GAAG,CAAEC,CAAC,IAAI;MAC5C,IAAIA,CAAC,IAAI,IAAI,EAAE;QAAE,OAAO,IAAI;;MAC5B,IAAIpC,KAAK,CAACC,OAAO,CAACmC,CAAC,CAAC,EAAE;QAClB,OAAOlB,SAAS,CAACkB,CAAC,CAACD,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACrC,WAAW,EAAE,CAAC,CAAC;;MAEnD,OAAOqC,CAAC,CAACrC,WAAW,EAAE;IAC1B,CAAC,CAAC;IAEF,MAAMiI,SAAS,GAAI,WAAW,IAAI/F,MAAM,GAAIA,MAAM,CAAC+F,SAAS,GAAEC,SAAS;IAEvE,MAAM7D,OAAO,GAAGA,CAAC8D,QAAuB,EAAEC,SAAkB,EAAEC,OAAgB,KAAI;MAC9E,IAAIlG,OAAO,GAAuC+F,SAAS;MAC3D,QAAQC,QAAQ,CAACvF,MAAM;QACnB,KAAK,CAAC;UAAE;QACR,KAAK,CAAC;UACFT,OAAO,GAAGgG,QAAQ,CAAC,CAAC,CAAC;UACrB;QACJ;UACIA,QAAQ,CAAC9H,IAAI,EAAE;UACf8B,OAAO,GAAGgG,QAAQ;;MAG1B,IAAIF,SAAS,EAAE;QACX,IAAIG,SAAS,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,EAAE;UACtC,MAAM,IAAI1G,KAAK,CAAC,gBAAgB,CAAC;;;MAIzC,MAAMO,MAAM,GAAQ,EAAG;MACvB,IAAIC,OAAO,EAAE;QAAED,MAAM,CAACC,OAAO,GAAGA,OAAO;;MACvC,IAAIP,MAAM,CAACgB,MAAM,EAAE;QAAEV,MAAM,CAACN,MAAM,GAAGA,MAAM;;MAC3C,IAAIwG,SAAS,EAAE;QAAElG,MAAM,CAACkG,SAAS,GAAGA,SAAS;;MAC7C,IAAIC,OAAO,EAAE;QAAEnG,MAAM,CAACmG,OAAO,GAAGA,OAAO;;MACvC,IAAIJ,SAAS,EAAE;QAAE/F,MAAM,CAAC+F,SAAS,GAAGA,SAAS;;MAE7C,OAAO/F,MAAM;IACjB,CAAC;IAED;IACA,IAAIC,OAAO,GAAoC,EAAG;IAClD,IAAID,MAAM,CAACC,OAAO,EAAE;MAChB,IAAIlC,KAAK,CAACC,OAAO,CAACgC,MAAM,CAACC,OAAO,CAAC,EAAE;QAC/B,KAAK,MAAMM,IAAI,IAAIP,MAAM,CAACC,OAAO,EAAE;UAAEA,OAAO,CAACO,IAAI,CAAC,IAAI,CAACkF,WAAW,CAACnF,IAAI,CAAC,CAAC;;OAC5E,MAAM;QACHN,OAAO,CAACO,IAAI,CAAC,IAAI,CAACkF,WAAW,CAAC1F,MAAM,CAACC,OAAO,CAAC,CAAC;;;IAItD,IAAIiG,SAAS,GAAyCF,SAAS;IAC/D,IAAI,WAAW,IAAIhG,MAAM,EAAE;MAAEkG,SAAS,GAAG,IAAI,CAACP,YAAY,CAAC3F,MAAM,CAACkG,SAAS,CAAC;;IAE5E,IAAIC,OAAO,GAAyCH,SAAS;IAC7D,IAAI,SAAS,IAAIhG,MAAM,EAAE;MAAEmG,OAAO,GAAG,IAAI,CAACR,YAAY,CAAC3F,MAAM,CAACmG,OAAO,CAAC;;IAEtE,IAAIlG,OAAO,CAACD,MAAM,CAAEa,CAAC,IAAM,OAAOA,CAAE,KAAK,QAAS,CAAC,CAACH,MAAM,IACrDwF,SAAS,IAAI,IAAI,IAAI,OAAOA,SAAU,KAAK,QAAS,IACpDC,OAAO,IAAI,IAAI,IAAI,OAAOA,OAAQ,KAAK,QAAS,EAAE;MAEnD,OAAOxF,OAAO,CAACC,GAAG,CAAC,CAAED,OAAO,CAACC,GAAG,CAACX,OAAO,CAAC,EAAEiG,SAAS,EAAEC,OAAO,CAAE,CAAC,CAAC7I,IAAI,CAAEoH,MAAM,IAAI;QAC7E,OAAOvC,OAAO,CAACuC,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,EAAEA,MAAM,CAAC,CAAC,CAAC,CAAC;MACnD,CAAC,CAAC;;IAGN,OAAOvC,OAAO,CAAgBlC,OAAO,EAAEiG,SAAS,EAAEC,OAAO,CAAC;EAC9D;EAEA;;;;;EAKAC,sBAAsBA,CAACC,QAA4B;IAC/C,MAAMrC,OAAO,GAA6B1H,WAAW,CAAC+J,QAAQ,CAAC;IAE/D,MAAMhG,QAAQ,GAAyB,EAAG;IAC1C,CAAE,IAAI,EAAE,MAAM,CAAE,CAACI,OAAO,CAAEnC,GAAG,IAAI;MAC7B,IAAU0F,OAAQ,CAAC1F,GAAG,CAAC,IAAI,IAAI,EAAE;QAAE;;MAEnC,MAAMiC,IAAI,GAAGhG,cAAc,CAAOyJ,OAAQ,CAAC1F,GAAG,CAAC,EAAE,IAAI,CAAC;MACtD,IAAIlB,SAAS,CAACmD,IAAI,CAAC,EAAE;QACjBF,QAAQ,CAACG,IAAI,CAAE,kBAAK;UAAoBwD,OAAQ,CAAC1F,GAAG,CAAC,GAAG,MAAMiC,IAAI;QAAE,CAAC,CAAC,CAAE,CAAC;OAC5E,MAAM;QACGyD,OAAQ,CAAC1F,GAAG,CAAC,GAAGiC,IAAI;;IAElC,CAAC,CAAC;IAEF,IAAIyD,OAAO,CAAC4B,QAAQ,IAAI,IAAI,EAAE;MAC1B,MAAMA,QAAQ,GAAG,IAAI,CAACD,YAAY,CAAC3B,OAAO,CAAC4B,QAAQ,CAAC;MACpD,IAAIxI,SAAS,CAACwI,QAAQ,CAAC,EAAE;QACrBvF,QAAQ,CAACG,IAAI,CAAE,kBAAK;UAAcwD,OAAO,CAAC4B,QAAQ,GAAG,MAAMA,QAAQ;QAAE,CAAC,CAAC,CAAE,CAAC;OAC7E,MAAM;QACH5B,OAAO,CAAC4B,QAAQ,GAAGA,QAAQ;;;IAInC,IAAIvF,QAAQ,CAACK,MAAM,EAAE;MACjB,OAAQ,kBAAK;QACT,MAAMC,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;QAC3B,OAAO2D,OAAO;MAClB,CAAC,CAAC,CAAE;;IAGR,OAAOA,OAAO;EAClB;EAEA,MAAMsC,UAAUA,CAAA;IAEZ;IACA,IAAI,IAAI,CAAC,CAAA9E,cAAe,IAAI,IAAI,EAAE;MAE9B;MACA,MAAM+E,aAAa,GAAG,CAAC,YAAW;QAC9B,IAAI;UACA,MAAMrE,OAAO,GAAG,MAAM,IAAI,CAACoD,cAAc,EAAE;UAC3C,IAAI,CAACjD,IAAI,CAAC,SAAS,EAAEH,OAAO,EAAE,IAAI,CAAC;UACnC,OAAOA,OAAO;SACjB,CAAC,OAAOsC,KAAK,EAAE;UACZ,IAAI,IAAI,CAAC,CAAAhD,cAAe,KAAK+E,aAAc,EAAE;YACzC,IAAI,CAAC,CAAA/E,cAAe,GAAG,IAAI;;UAE/B,MAAMgD,KAAK;;MAEnB,CAAC,EAAC,CAAE;MAEJ,IAAI,CAAC,CAAAhD,cAAe,GAAG+E,aAAa;MACpC,OAAO,CAAC,MAAMA,aAAa,EAAEC,KAAK,EAAE;;IAGxC,MAAMhF,cAAc,GAAG,IAAI,CAAC,CAAAA,cAAe;IAE3C,MAAM,CAAEiF,QAAQ,EAAEC,MAAM,CAAE,GAAG,MAAM/F,OAAO,CAACC,GAAG,CAAC,CAC3CY,cAAc,EACd,IAAI,CAAC8D,cAAc,EAAE,CAAI;IAAA,CAC5B,CAAC;IAEF,IAAImB,QAAQ,CAACE,OAAO,KAAKD,MAAM,CAACC,OAAO,EAAE;MACrC,IAAI,IAAI,CAAC,CAAAlF,UAAW,EAAE;QAClB;QACA,IAAI,CAACY,IAAI,CAAC,SAAS,EAAEqE,MAAM,EAAED,QAAQ,CAAC;QAEtC;QACA,IAAI,IAAI,CAAC,CAAAjF,cAAe,KAAKA,cAAc,EAAE;UACzC,IAAI,CAAC,CAAAA,cAAe,GAAGb,OAAO,CAACwB,OAAO,CAACuE,MAAM,CAAC;;OAErD,MAAM;QACH;QACAnL,MAAM,CAAC,KAAK,EAAE,oBAAqBkL,QAAQ,CAACE,OAAQ,OAAQD,MAAM,CAACC,OAAQ,GAAG,EAAE,eAAe,EAAE;UAC7F5G,KAAK,EAAE;SACV,CAAC;;;IAIV,OAAO0G,QAAQ,CAACD,KAAK,EAAE;EAC3B;EAEA,MAAMI,UAAUA,CAAA;IACZ,MAAM1E,OAAO,GAAG,MAAM,IAAI,CAACoE,UAAU,EAAE;IAEvC,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAW;MAC9B,MAAM;QAAEC,MAAM;QAAEC,QAAQ;QAAEC;MAAW,CAAE,GAAG,MAAMlL,iBAAiB,CAAC;QAC9DgL,MAAM,EAAE,IAAI,CAAC,CAAAG,QAAS,CAAC,QAAQ,EAAE,KAAK,CAAC;QACvCF,QAAQ,EAAG,CAAC,YAAW;UACnB,IAAI;YACA,MAAM1J,KAAK,GAAG,MAAM,IAAI,CAAC,CAAAwF,OAAQ,CAAC;cAAEK,MAAM,EAAE;YAAa,CAAE,CAAC;YAC5D,OAAOjI,SAAS,CAACoC,KAAK,EAAE,WAAW,CAAC;WACvC,CAAC,OAAOmH,KAAK,EAAE;UAChB,OAAO,IAAI;QACf,CAAC,EAAC,CAAG;QACLwC,WAAW,EAAG,CAAC,YAAW;UACtB,IAAI;YACA,MAAM3J,KAAK,GAAG,MAAM,IAAI,CAAC,CAAAwF,OAAQ,CAAC;cAAEK,MAAM,EAAE;YAAgB,CAAE,CAAC;YAC/D,OAAOjI,SAAS,CAACoC,KAAK,EAAE,WAAW,CAAC;WACvC,CAAC,OAAOmH,KAAK,EAAE;UAChB,OAAO,IAAI;QACf,CAAC,EAAC;OACL,CAAC;MAEF,IAAI0C,YAAY,GAAkB,IAAI;MACtC,IAAIC,oBAAoB,GAAkB,IAAI;MAE9C;MACA,MAAMC,KAAK,GAAG,IAAI,CAAClC,UAAU,CAAC4B,MAAM,EAAE5E,OAAO,CAAC;MAC9C,IAAIkF,KAAK,IAAIA,KAAK,CAACC,aAAa,EAAE;QAC9BF,oBAAoB,GAAIH,WAAW,IAAI,IAAI,GAAIA,WAAW,GAAE9J,MAAM,CAAC,YAAY,CAAC;QAChFgK,YAAY,GAAIE,KAAK,CAACC,aAAa,GAAGpK,IAAI,GAAIkK,oBAAoB;;MAGtE,OAAO,IAAI3K,OAAO,CAACuK,QAAQ,EAAEG,YAAY,EAAEC,oBAAoB,CAAC;IACpE,CAAC;IAED;IACA,MAAM3E,MAAM,GAAiCN,OAAO,CAACU,SAAS,CAAC,kDAAkD,CAAC;IAClH,IAAIJ,MAAM,EAAE;MACR,MAAMO,GAAG,GAAG,IAAItH,YAAY,CAAC+G,MAAM,CAACqB,GAAG,CAAC;MACxC,MAAMyD,OAAO,GAAG,MAAM9E,MAAM,CAAC+E,WAAW,CAACV,cAAc,EAAE,IAAI,EAAE9D,GAAG,CAAC;MACnE,OAAO,IAAIvG,OAAO,CAAC8K,OAAO,CAACP,QAAQ,EAAEO,OAAO,CAACJ,YAAY,EAAEI,OAAO,CAACH,oBAAoB,CAAC;;IAG5F,OAAO,MAAMN,cAAc,EAAE;EACjC;EAGA,MAAMW,WAAWA,CAACC,GAAuB;IACrC,IAAIpE,EAAE,GAAG,IAAI,CAAC+C,sBAAsB,CAACqB,GAAG,CAAC;IACzC,IAAIrK,SAAS,CAACiG,EAAE,CAAC,EAAE;MAAEA,EAAE,GAAG,MAAMA,EAAE;;IAClC,OAAOpI,SAAS,CAAC,MAAM,IAAI,CAAC,CAAA4H,OAAQ,CAAC;MACjCK,MAAM,EAAE,aAAa;MAAE4B,WAAW,EAAEzB;KACvC,CAAC,EAAE,WAAW,CAAC;EACpB;EAEA,MAAM,CAAAqE,IAAKC,CAACtE,EAA4B,EAAEuC,QAAgB,EAAEgC,OAAe;IACvErM,MAAM,CAAEqM,OAAO,GAAGzK,kBAAkB,EAAE,yCAAyC,EAAE,gBAAgB,EAAE;MAC9F0H,MAAM,EAAE,oBAAoB;MAC5BC,WAAW,EAAE5G,MAAM,CAAC+D,MAAM,CAAC,EAAG,EAAEoB,EAAE,EAAE;QAAEuC,QAAQ;QAAEiC,cAAc,EAAE;MAAI,CAAE;KACzE,CAAC;IAEF;IACA,MAAM/C,WAAW,GAA6BxI,WAAW,CAAC+G,EAAE,CAAC;IAE7D,IAAI;MACA,OAAOtI,OAAO,CAAC,MAAM,IAAI,CAACkI,QAAQ,CAAC;QAAEC,MAAM,EAAE,MAAM;QAAE4B,WAAW;QAAEc;MAAQ,CAAE,CAAC,CAAC;KAEjF,CAAC,OAAOpB,KAAU,EAAE;MACjB;MACA,IAAI,CAAC,IAAI,CAAC1C,eAAe,IAAI1G,eAAe,CAACoJ,KAAK,CAAC,IAAIA,KAAK,CAACd,IAAI,IAAIkE,OAAO,IAAI,CAAC,IAAIhC,QAAQ,KAAK,QAAQ,IAAId,WAAW,CAACtB,EAAE,IAAI,IAAI,IAAI1I,SAAS,CAAC0J,KAAK,CAACd,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,YAAY,EAAE;QAClL,MAAMA,IAAI,GAAGc,KAAK,CAACd,IAAI;QAEvB,MAAMoE,QAAQ,GAAG,MAAMvN,cAAc,CAACuK,WAAW,CAACtB,EAAE,EAAE,IAAI,CAAC;QAE3D;QACA,IAAIuE,QAAkB;QACtB,IAAI;UACAA,QAAQ,GAAGC,mBAAmB,CAAClN,SAAS,CAAC0J,KAAK,CAACd,IAAI,EAAE,CAAC,CAAC,CAAC;SAC3D,CAAC,OAAOc,KAAU,EAAE;UACjBjJ,MAAM,CAAC,KAAK,EAAEiJ,KAAK,CAACC,OAAO,EAAE,gBAAgB,EAAE;YAC3CI,MAAM,EAAE,UAAU;YAAEC,WAAW;YAAEC,IAAI,EAAE;cAAErB;YAAI;WAAI,CAAC;;QAG1D;QACAnI,MAAM,CAACwM,QAAQ,CAACtE,MAAM,CAAC3F,WAAW,EAAE,KAAKgK,QAAQ,CAAChK,WAAW,EAAE,EAC3D,2BAA2B,EAAE,gBAAgB,EAAE;UAC3CqG,MAAM,EAAE,MAAM;UACdT,IAAI;UACJmB,MAAM,EAAE,gBAAgB;UACxBC,WAAW,EAAOA,WAAW;UAC7BmD,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE;YACJC,SAAS,EAAE,qDAAqD;YAChE3J,IAAI,EAAE,gBAAgB;YACtB4J,IAAI,EAAEL,QAAQ,CAACM;;SAEtB,CAAC;QAEN,MAAMC,UAAU,GAAG,MAAM,IAAI,CAAClF,aAAa,CAAC0B,WAAW,EAAEiD,QAAQ,CAACzE,QAAQ,EAAEyE,QAAQ,CAACxE,IAAI,CAAC;QAC1FhI,MAAM,CAAC+M,UAAU,IAAI,IAAI,EAAE,gCAAgC,EAAE,gBAAgB,EAAE;UAC3EzD,MAAM,EAAE,cAAc;UAAEC,WAAW;UAAEC,IAAI,EAAE;YAAErB,IAAI,EAAEc,KAAK,CAACd,IAAI;YAAE2E,SAAS,EAAEN,QAAQ,CAACM;UAAS;SAAI,CAAC;QAErG,MAAMhF,EAAE,GAAG;UACPG,EAAE,EAAEsE,QAAQ;UACZpE,IAAI,EAAE9I,MAAM,CAAC,CAAEmN,QAAQ,CAACQ,QAAQ,EAAEC,WAAW,CAAC,CAAEF,UAAU,EAAEP,QAAQ,CAACU,SAAS,CAAE,CAAC,CAAE;SACtF;QAED,IAAI,CAACpG,IAAI,CAAC,OAAO,EAAE;UAAE8B,MAAM,EAAE,kBAAkB;UAAEW,WAAW,EAAEzB;QAAE,CAAE,CAAC;QACnE,IAAI;UACA,MAAMqB,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAgD,IAAK,CAACrE,EAAE,EAAEuC,QAAQ,EAAEgC,OAAO,GAAG,CAAC,CAAC;UAC1D,IAAI,CAACvF,IAAI,CAAC,OAAO,EAAE;YAAE8B,MAAM,EAAE,2BAA2B;YAAEW,WAAW,EAAE5G,MAAM,CAAC+D,MAAM,CAAC,EAAG,EAAEoB,EAAE,CAAC;YAAEqB;UAAM,CAAE,CAAC;UACxG,OAAOA,MAAM;SAChB,CAAC,OAAOF,KAAK,EAAE;UACZ,IAAI,CAACnC,IAAI,CAAC,OAAO,EAAE;YAAE8B,MAAM,EAAE,0BAA0B;YAAEW,WAAW,EAAE5G,MAAM,CAAC+D,MAAM,CAAC,EAAG,EAAEoB,EAAE,CAAC;YAAEmB;UAAK,CAAE,CAAC;UACtG,MAAMA,KAAK;;;MAInB,MAAMA,KAAK;;EAEpB;EAEA,MAAM,CAAAkE,YAAaC,CAAIC,OAAmB;IACtC,MAAM;MAAEvL;IAAK,CAAE,GAAG,MAAMvB,iBAAiB,CAAC;MACtCoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1BjJ,KAAK,EAAEuL;KACV,CAAC;IACF,OAAOvL,KAAK;EAChB;EAEA,MAAMqK,IAAIA,CAACD,GAAuB;IAC9B,MAAM;MAAEpE,EAAE;MAAEuC;IAAQ,CAAE,GAAG,MAAM9J,iBAAiB,CAAC;MAC7CuH,EAAE,EAAE,IAAI,CAAC+C,sBAAsB,CAACqB,GAAG,CAAC;MACpC7B,QAAQ,EAAE,IAAI,CAACD,YAAY,CAAC8B,GAAG,CAAC7B,QAAQ;KAC3C,CAAC;IAEF,OAAO,MAAM,IAAI,CAAC,CAAA8C,YAAa,CAAC,IAAI,CAAC,CAAAhB,IAAK,CAACrE,EAAE,EAAEuC,QAAQ,EAAE6B,GAAG,CAACI,cAAc,GAAG,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC;EACzF;EAEA;EACA,MAAM,CAAAgB,eAAgBC,CAAC9E,OAA+B,EAAEiC,QAAqB,EAAE8C,SAAoB;IAC/F,IAAI9I,OAAO,GAA6B,IAAI,CAACyF,WAAW,CAACO,QAAQ,CAAC;IAClE,IAAIL,QAAQ,GAA6B,IAAI,CAACD,YAAY,CAACoD,SAAS,CAAC;IAErE,IAAI,OAAO9I,OAAQ,KAAK,QAAQ,IAAI,OAAO2F,QAAS,KAAK,QAAQ,EAAE;MAC/D,CAAE3F,OAAO,EAAE2F,QAAQ,CAAE,GAAG,MAAMjF,OAAO,CAACC,GAAG,CAAC,CAAEX,OAAO,EAAE2F,QAAQ,CAAE,CAAC;;IAGpE,OAAO,MAAM,IAAI,CAAC,CAAA8C,YAAa,CAAC,IAAI,CAAC,CAAA7F,OAAQ,CAAC3E,MAAM,CAAC+D,MAAM,CAAC+B,OAAO,EAAE;MAAE/D,OAAO;MAAE2F;IAAQ,CAAE,CAAC,CAAC,CAAC;EACjG;EAEA,MAAMoD,UAAUA,CAAC/I,OAAoB,EAAE2F,QAAmB;IACtD,OAAO3K,SAAS,CAAC,MAAM,IAAI,CAAC,CAAA4N,eAAgB,CAAC;MAAE3F,MAAM,EAAE;IAAY,CAAE,EAAEjD,OAAO,EAAE2F,QAAQ,CAAC,EAAE,WAAW,CAAC;EAC3G;EAEA,MAAMqD,mBAAmBA,CAAChJ,OAAoB,EAAE2F,QAAmB;IAC/D,OAAOzK,SAAS,CAAC,MAAM,IAAI,CAAC,CAAA0N,eAAgB,CAAC;MAAE3F,MAAM,EAAE;IAAqB,CAAE,EAAEjD,OAAO,EAAE2F,QAAQ,CAAC,EAAE,WAAW,CAAC;EACpH;EAEA,MAAMsD,OAAOA,CAACjJ,OAAoB,EAAE2F,QAAmB;IACnD,OAAO7K,OAAO,CAAC,MAAM,IAAI,CAAC,CAAA8N,eAAgB,CAAC;MAAE3F,MAAM,EAAE;IAAS,CAAE,EAAEjD,OAAO,EAAE2F,QAAQ,CAAC,CAAC;EACzF;EAEA,MAAMuD,UAAUA,CAAClJ,OAAoB,EAAEmJ,SAAuB,EAAExD,QAAmB;IAC/E,MAAMyD,QAAQ,GAAGpO,SAAS,CAACmO,SAAS,EAAE,UAAU,CAAC;IACjD,OAAOrO,OAAO,CAAC,MAAM,IAAI,CAAC,CAAA8N,eAAgB,CAAC;MAAE3F,MAAM,EAAE,YAAY;MAAEmG;IAAQ,CAAE,EAAEpJ,OAAO,EAAE2F,QAAQ,CAAC,CAAC;EACtG;EAEA;EACA,MAAM0D,oBAAoBA,CAACC,QAAgB;IACvC,MAAM;MAAE9D,WAAW;MAAE5F,IAAI;MAAEqC;IAAO,CAAE,GAAG,MAAMpG,iBAAiB,CAAC;MAC1D2J,WAAW,EAAE,IAAI,CAACD,cAAc,EAAE;MAClC3F,IAAI,EAAE,IAAI,CAACoD,QAAQ,CAAC;QAChBC,MAAM,EAAE,sBAAsB;QAC9BsG,iBAAiB,EAAED;OACtB,CAAC;MACFrH,OAAO,EAAE,IAAI,CAACoE,UAAU;KAC5B,CAAC;IAEF,MAAMjD,EAAE,GAAG1I,WAAW,CAACwE,IAAI,CAACoK,QAAQ,CAAC;IACrC,IAAIlG,EAAE,CAACxD,IAAI,KAAKA,IAAI,EAAE;MAClB,MAAM,IAAIJ,KAAK,CAAC,wCAAwC,CAAC;;IAG7D,OAAO,IAAI,CAAC4F,wBAAwB,CAAMhC,EAAE,EAAEnB,OAAO,CAAC,CAACuH,sBAAsB,CAAChE,WAAW,CAAC;EAC9F;EAEA,MAAM,CAAAwB,QAASyC,CAACtC,KAAwB,EAAEuC,mBAA4B;IAClE;IAEA,IAAI3O,WAAW,CAACoM,KAAK,EAAE,EAAE,CAAC,EAAE;MACxB,OAAO,MAAM,IAAI,CAAC,CAAAvE,OAAQ,CAAC;QACvBK,MAAM,EAAE,UAAU;QAAE6C,SAAS,EAAEqB,KAAK;QAAEuC;OACzC,CAAC;;IAGN,IAAI/D,QAAQ,GAAG,IAAI,CAACD,YAAY,CAACyB,KAAK,CAAC;IACvC,IAAI,OAAOxB,QAAS,KAAK,QAAQ,EAAE;MAAEA,QAAQ,GAAG,MAAMA,QAAQ;;IAE9D,OAAO,MAAM,IAAI,CAAC,CAAA/C,OAAQ,CAAC;MACvBK,MAAM,EAAE,UAAU;MAAE0C,QAAQ;MAAE+D;KACjC,CAAC;EACN;EAEA;EACA,MAAM1C,QAAQA,CAACG,KAAwB,EAAEwC,WAAqB;IAC1D,MAAM;MAAE1H,OAAO;MAAE2H;IAAM,CAAE,GAAG,MAAM/N,iBAAiB,CAAC;MAChDoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1BuD,MAAM,EAAE,IAAI,CAAC,CAAA5C,QAAS,CAACG,KAAK,EAAE,CAAC,CAACwC,WAAW;KAC9C,CAAC;IACF,IAAIC,MAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEjC,OAAO,IAAI,CAAC3E,UAAU,CAAC2E,MAAM,EAAE3H,OAAO,CAAC;EAC3C;EAEA,MAAM4H,cAAcA,CAACjK,IAAY;IAC7B,MAAM;MAAEqC,OAAO;MAAE2H;IAAM,CAAE,GAAG,MAAM/N,iBAAiB,CAAC;MAChDoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1BuD,MAAM,EAAE,IAAI,CAAC,CAAAhH,OAAQ,CAAC;QAAEK,MAAM,EAAE,gBAAgB;QAAErD;MAAI,CAAE;KAC3D,CAAC;IACF,IAAIgK,MAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEjC,OAAO,IAAI,CAACxE,wBAAwB,CAACwE,MAAM,EAAE3H,OAAO,CAAC;EACzD;EAEA,MAAM6H,qBAAqBA,CAAClK,IAAY;IACpC,MAAM;MAAEqC,OAAO;MAAE2H;IAAM,CAAE,GAAG,MAAM/N,iBAAiB,CAAC;MAChDoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1BuD,MAAM,EAAE,IAAI,CAAC,CAAAhH,OAAQ,CAAC;QAAEK,MAAM,EAAE,uBAAuB;QAAErD;MAAI,CAAE;KAClE,CAAC;IACF,IAAIgK,MAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEjC;IACA;IACA,IAAIA,MAAM,CAAC9C,QAAQ,IAAI,IAAI,IAAI8C,MAAM,CAACG,iBAAiB,IAAI,IAAI,EAAE;MAC7D,MAAM3G,EAAE,GAAG,MAAM,IAAI,CAAC,CAAAR,OAAQ,CAAC;QAAEK,MAAM,EAAE,gBAAgB;QAAErD;MAAI,CAAE,CAAC;MAClE,IAAIwD,EAAE,IAAI,IAAI,EAAE;QAAE,MAAM,IAAI5D,KAAK,CAAC,qDAAqD,CAAC;;MACxFoK,MAAM,CAACG,iBAAiB,GAAG3G,EAAE,CAAC0D,QAAQ;;IAG1C,OAAO,IAAI,CAAC3B,uBAAuB,CAACyE,MAAM,EAAE3H,OAAO,CAAC;EACxD;EAEA,MAAM+H,oBAAoBA,CAACpK,IAAY;IACnC,MAAM;MAAE6E;IAAM,CAAE,GAAG,MAAM5I,iBAAiB,CAAC;MACvCoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1B5B,MAAM,EAAE,IAAI,CAAC,CAAA7B,OAAQ,CAAC;QAAEK,MAAM,EAAE,sBAAsB;QAAErD;MAAI,CAAE;KACjE,CAAC;IACF,IAAI6E,MAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACjC,OAAO3J,OAAO,CAAC2J,MAAM,CAAC;EAC1B;EAEA;EACA,MAAMwF,OAAOA,CAACC,OAAmC;IAC7C,IAAInK,MAAM,GAAG,IAAI,CAAC8F,UAAU,CAACqE,OAAO,CAAC;IACrC,IAAI/M,SAAS,CAAC4C,MAAM,CAAC,EAAE;MAAEA,MAAM,GAAG,MAAMA,MAAM;;IAE9C,MAAM;MAAEkC,OAAO;MAAE2H;IAAM,CAAE,GAAG,MAAM/N,iBAAiB,CAAC;MAChDoG,OAAO,EAAE,IAAI,CAACoE,UAAU,EAAE;MAC1BuD,MAAM,EAAE,IAAI,CAAC,CAAAhH,OAAQ,CAAmB;QAAEK,MAAM,EAAE,SAAS;QAAElD;MAAM,CAAE;KACxE,CAAC;IAEF,OAAO6J,MAAM,CAAC3J,GAAG,CAAEkK,CAAC,IAAK,IAAI,CAACjF,QAAQ,CAACiF,CAAC,EAAElI,OAAO,CAAC,CAAC;EACvD;EAEA;EACAmI,YAAYA,CAAC1D,OAAe;IACxBpL,MAAM,CAAC,KAAK,EAAE,2CAA2C,EAAE,uBAAuB,EAAE;MAChFgK,SAAS,EAAE;KACd,CAAC;EACN;EAEA,MAAM+E,WAAWA,CAAC9L,IAAY;IAC1B,OAAO,MAAMxC,WAAW,CAACuO,QAAQ,CAAC,IAAI,EAAE/L,IAAI,CAAC;EACjD;EAEA,MAAMgM,SAASA,CAAChM,IAAY;IACxB,MAAMiM,QAAQ,GAAG,MAAM,IAAI,CAACH,WAAW,CAAC9L,IAAI,CAAC;IAC7C,IAAIiM,QAAQ,EAAE;MAAE,OAAO,MAAMA,QAAQ,CAACD,SAAS,EAAE;;IACjD,OAAO,IAAI;EACf;EAEA,MAAME,WAAWA,CAAClM,IAAY;IAC1B,MAAMiM,QAAQ,GAAG,MAAM,IAAI,CAACH,WAAW,CAAC9L,IAAI,CAAC;IAC7C,IAAIiM,QAAQ,EAAE;MAAE,OAAO,MAAMA,QAAQ,CAACnQ,UAAU,EAAE;;IAClD,OAAO,IAAI;EACf;EAEA,MAAMqQ,aAAaA,CAAC1K,OAAe;IAC/BA,OAAO,GAAG3F,UAAU,CAAC2F,OAAO,CAAC;IAC7B,MAAM2K,IAAI,GAAGlQ,QAAQ,CAACuF,OAAO,CAAC4K,SAAS,CAAC,CAAC,CAAC,CAAC/M,WAAW,EAAE,GAAG,eAAe,CAAC;IAE3E,IAAI;MAEA,MAAMgN,OAAO,GAAG,MAAM9O,WAAW,CAAC+O,aAAa,CAAC,IAAI,CAAC;MACrD,MAAMC,WAAW,GAAG,IAAIvQ,QAAQ,CAACqQ,OAAO,EAAE,CACtC,mDAAmD,CACtD,EAAE,IAAI,CAAC;MAER,MAAML,QAAQ,GAAG,MAAMO,WAAW,CAACP,QAAQ,CAACG,IAAI,CAAC;MACjD,IAAIH,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAKjQ,WAAW,EAAE;QAAE,OAAO,IAAI;;MAE/D,MAAMyQ,gBAAgB,GAAG,IAAIxQ,QAAQ,CAACgQ,QAAQ,EAAE,CAC5C,8CAA8C,CACjD,EAAE,IAAI,CAAC;MACR,MAAMjM,IAAI,GAAG,MAAMyM,gBAAgB,CAACzM,IAAI,CAACoM,IAAI,CAAC;MAE9C;MACA,MAAMM,KAAK,GAAG,MAAM,IAAI,CAACR,WAAW,CAAClM,IAAI,CAAC;MAC1C,IAAI0M,KAAK,KAAKjL,OAAO,EAAE;QAAE,OAAO,IAAI;;MAEpC,OAAOzB,IAAI;KAEd,CAAC,OAAOgG,KAAK,EAAE;MACZ;MACA,IAAInJ,OAAO,CAACmJ,KAAK,EAAE,UAAU,CAAC,IAAIA,KAAK,CAACnH,KAAK,KAAK,IAAI,EAAE;QACpD,OAAO,IAAI;;MAGf;MACA,IAAIhC,OAAO,CAACmJ,KAAK,EAAE,gBAAgB,CAAC,EAAE;QAAE,OAAO,IAAI;;MAEnD,MAAMA,KAAK;;IAGf,OAAO,IAAI;EACf;EAEA,MAAM2G,kBAAkBA,CAACtL,IAAY,EAAEuL,SAAyB,EAAEpI,OAAuB;IACrF,MAAMqI,QAAQ,GAAID,SAAS,IAAI,IAAI,GAAIA,SAAS,GAAE,CAAC;IACnD,IAAIC,QAAQ,KAAK,CAAC,EAAE;MAAE,OAAO,IAAI,CAACtB,qBAAqB,CAAClK,IAAI,CAAC;;IAE7D,OAAO,IAAIc,OAAO,CAAC,OAAOwB,OAAO,EAAEmJ,MAAM,KAAI;MACzC,IAAIC,KAAK,GAAiB,IAAI;MAE9B,MAAMC,QAAQ,GAAI,MAAO/F,WAAmB,IAAI;QAC5C,IAAI;UACA,MAAMgG,OAAO,GAAG,MAAM,IAAI,CAAC1B,qBAAqB,CAAClK,IAAI,CAAC;UACtD,IAAI4L,OAAO,IAAI,IAAI,EAAE;YACjB,IAAIhG,WAAW,GAAGgG,OAAO,CAAChG,WAAW,GAAG,CAAC,IAAI4F,QAAQ,EAAE;cACnDlJ,OAAO,CAACsJ,OAAO,CAAC;cAChB;cACA,IAAIF,KAAK,EAAE;gBACPG,YAAY,CAACH,KAAK,CAAC;gBACnBA,KAAK,GAAG,IAAI;;cAEhB;;;SAGX,CAAC,OAAO/G,KAAK,EAAE;UACZmH,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEpH,KAAK,CAAC;;QAE7B,IAAI,CAACqH,IAAI,CAAC,OAAO,EAAEL,QAAQ,CAAC;MAChC,CAAE;MAEF,IAAIxI,OAAO,IAAI,IAAI,EAAE;QACjBuI,KAAK,GAAGnJ,UAAU,CAAC,MAAK;UACpB,IAAImJ,KAAK,IAAI,IAAI,EAAE;YAAE;;UACrBA,KAAK,GAAG,IAAI;UACZ,IAAI,CAACO,GAAG,CAAC,OAAO,EAAEN,QAAQ,CAAC;UAC3BF,MAAM,CAAChQ,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE;YAAEuJ,MAAM,EAAE;UAAS,CAAE,CAAC,CAAC;QAClE,CAAC,EAAE7B,OAAO,CAAC;;MAGfwI,QAAQ,CAAC,MAAM,IAAI,CAAChG,cAAc,EAAE,CAAC;IACzC,CAAC,CAAC;EACN;EAEA,MAAMuG,YAAYA,CAACnG,QAAmB;IAClCrK,MAAM,CAAC,KAAK,EAAE,qBAAqB,EAAE,iBAAiB,EAAE;MACpDgK,SAAS,EAAE;KACd,CAAC;EACN;EAEA;;;EAGAyG,aAAaA,CAACC,OAAe;IACzB,MAAMV,KAAK,GAAG,IAAI,CAAC,CAAA1J,MAAO,CAACY,GAAG,CAACwJ,OAAO,CAAC;IACvC,IAAI,CAACV,KAAK,EAAE;MAAE;;IACd,IAAIA,KAAK,CAACA,KAAK,EAAE;MAAEG,YAAY,CAACH,KAAK,CAACA,KAAK,CAAC;;IAC5C,IAAI,CAAC,CAAA1J,MAAO,CAACsB,MAAM,CAAC8I,OAAO,CAAC;EAChC;EAEA;;;;;;;;EAQAC,WAAWA,CAACC,KAAiB,EAAEnJ,OAAgB;IAC3C,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,CAAC;;IAClC,MAAMiJ,OAAO,GAAG,IAAI,CAAC,CAAArK,SAAU,EAAE;IACjC,MAAMwK,IAAI,GAAGA,CAAA,KAAK;MACd,IAAI,CAAC,CAAAvK,MAAO,CAACsB,MAAM,CAAC8I,OAAO,CAAC;MAC5BE,KAAK,EAAE;IACX,CAAC;IAED,IAAI,IAAI,CAACE,MAAM,EAAE;MACb,IAAI,CAAC,CAAAxK,MAAO,CAACa,GAAG,CAACuJ,OAAO,EAAE;QAAEV,KAAK,EAAE,IAAI;QAAEa,IAAI;QAAEE,IAAI,EAAEtJ;MAAO,CAAE,CAAC;KAClE,MAAM;MACH,MAAMuI,KAAK,GAAGnJ,UAAU,CAACgK,IAAI,EAAEpJ,OAAO,CAAC;MACvC,IAAI,CAAC,CAAAnB,MAAO,CAACa,GAAG,CAACuJ,OAAO,EAAE;QAAEV,KAAK;QAAEa,IAAI;QAAEE,IAAI,EAAExL,OAAO;MAAE,CAAE,CAAC;;IAG/D,OAAOmL,OAAO;EAClB;EAEA;;;EAGAM,kBAAkBA,CAACH,IAA6B;IAC5C,KAAK,MAAMI,GAAG,IAAI,IAAI,CAAC,CAAApL,IAAK,CAAC/B,MAAM,EAAE,EAAE;MACnC+M,IAAI,CAACI,GAAG,CAACC,UAAU,CAAC;;EAE5B;EAEA;;;;EAIAC,cAAcA,CAACF,GAAiB;IAC5B,QAAQA,GAAG,CAAC7M,IAAI;MACZ,KAAK,OAAO;MACZ,KAAK,OAAO;MACZ,KAAK,SAAS;QACV,OAAO,IAAIpB,mBAAmB,CAACiO,GAAG,CAAC7M,IAAI,CAAC;MAC5C,KAAK,OAAO;QAAE;UACV,MAAM8M,UAAU,GAAG,IAAI7P,sBAAsB,CAAC,IAAI,CAAC;UACnD6P,UAAU,CAACvL,eAAe,GAAG,IAAI,CAACA,eAAe;UACjD,OAAOuL,UAAU;;MAErB,KAAK,MAAM;MAAE,KAAK,WAAW;QACzB,OAAO,IAAI5P,yBAAyB,CAAC,IAAI,EAAE2P,GAAG,CAAC7M,IAAI,CAAC;MACxD,KAAK,OAAO;QACR,OAAO,IAAI7C,sBAAsB,CAAC,IAAI,EAAE0P,GAAG,CAACxM,MAAM,CAAC;MACvD,KAAK,aAAa;QACd,OAAO,IAAIhD,4BAA4B,CAAC,IAAI,EAAEwP,GAAG,CAAC3M,IAAI,CAAC;MAC3D,KAAK,QAAQ;QACT,OAAO,IAAI9C,uBAAuB,CAAC,IAAI,EAAEyP,GAAG,CAACxM,MAAM,CAAC;;IAG5D,MAAM,IAAIP,KAAK,CAAC,sBAAuB+M,GAAG,CAAC7M,IAAK,EAAE,CAAC;EACvD;EAEA;;;;;;;;;EASAgN,kBAAkBA,CAACC,MAAkB,EAAEC,MAAkB;IACrD,KAAK,MAAML,GAAG,IAAI,IAAI,CAAC,CAAApL,IAAK,CAAC/B,MAAM,EAAE,EAAE;MACnC,IAAImN,GAAG,CAACC,UAAU,KAAKG,MAAM,EAAE;QAC3B,IAAIJ,GAAG,CAACM,OAAO,EAAE;UAAEN,GAAG,CAACC,UAAU,CAAC9N,IAAI,EAAE;;QACxC6N,GAAG,CAACC,UAAU,GAAGI,MAAM;QACvB,IAAIL,GAAG,CAACM,OAAO,EAAE;UAAED,MAAM,CAACnO,KAAK,EAAE;;QACjC,IAAI,IAAI,CAAC,CAAA4C,WAAY,IAAI,IAAI,EAAE;UAAEuL,MAAM,CAACjO,KAAK,CAAC,IAAI,CAAC,CAAA0C,WAAY,CAAC;;QAChE;;;EAGZ;EAEA,MAAM,CAAAyL,MAAOC,CAACjN,KAAoB,EAAEkN,QAAqB;IACrD,IAAIT,GAAG,GAAG,MAAMlN,eAAe,CAACS,KAAK,EAAE,IAAI,CAAC;IAC5C;IACA;IACA,IAAIyM,GAAG,CAAC7M,IAAI,KAAK,OAAO,IAAIsN,QAAQ,IAAIA,QAAQ,CAACvM,MAAM,GAAG,CAAC,IAAIuM,QAAQ,CAAC,CAAC,CAAC,CAACC,OAAO,KAAK,IAAI,EAAE;MACzFV,GAAG,GAAG,MAAMlN,eAAe,CAAC;QAAEQ,MAAM,EAAE,UAAU;QAAE8L,GAAG,EAAEqB,QAAQ,CAAC,CAAC;MAAC,CAAE,EAAE,IAAI,CAAC;;IAE/E,OAAO,IAAI,CAAC,CAAA7L,IAAK,CAACqB,GAAG,CAAC+J,GAAG,CAAC5M,GAAG,CAAC,IAAI,IAAI;EAC1C;EAEA,MAAM,CAAAuN,MAAOC,CAACrN,KAAoB;IAC9B,MAAMsN,YAAY,GAAG,MAAM/N,eAAe,CAACS,KAAK,EAAE,IAAI,CAAC;IAEvD;IACA,MAAMH,GAAG,GAAGyN,YAAY,CAACzN,GAAG;IAE5B,IAAI4M,GAAG,GAAG,IAAI,CAAC,CAAApL,IAAK,CAACqB,GAAG,CAAC7C,GAAG,CAAC;IAC7B,IAAI,CAAC4M,GAAG,EAAE;MACN,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACW,YAAY,CAAC;MAEpD,MAAMC,cAAc,GAAG,IAAIC,OAAO,EAAE;MACpC,MAAMC,OAAO,GAAG,IAAIlL,GAAG,EAAE;MACzBkK,GAAG,GAAG;QAAEC,UAAU;QAAE7M,GAAG;QAAE0N,cAAc;QAAEE,OAAO;QAAEV,OAAO,EAAE,KAAK;QAAEW,SAAS,EAAE;MAAG,CAAE;MAClF,IAAI,CAAC,CAAArM,IAAK,CAACsB,GAAG,CAAC9C,GAAG,EAAE4M,GAAG,CAAC;;IAG5B,OAAOA,GAAG;EACd;EAEA,MAAMkB,EAAEA,CAAC3N,KAAoB,EAAEyL,QAAkB;IAC7C,MAAMgB,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAW,MAAO,CAACpN,KAAK,CAAC;IACrCyM,GAAG,CAACiB,SAAS,CAACjN,IAAI,CAAC;MAAEgL,QAAQ;MAAEK,IAAI,EAAE;IAAK,CAAE,CAAC;IAC7C,IAAI,CAACW,GAAG,CAACM,OAAO,EAAE;MACdN,GAAG,CAACC,UAAU,CAAC/N,KAAK,EAAE;MACtB8N,GAAG,CAACM,OAAO,GAAG,IAAI;MAClB,IAAI,IAAI,CAAC,CAAAxL,WAAY,IAAI,IAAI,EAAE;QAAEkL,GAAG,CAACC,UAAU,CAAC7N,KAAK,CAAC,IAAI,CAAC,CAAA0C,WAAY,CAAC;;;IAE5E,OAAO,IAAI;EACf;EAEA,MAAMuK,IAAIA,CAAC9L,KAAoB,EAAEyL,QAAkB;IAC/C,MAAMgB,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAW,MAAO,CAACpN,KAAK,CAAC;IACrCyM,GAAG,CAACiB,SAAS,CAACjN,IAAI,CAAC;MAAEgL,QAAQ;MAAEK,IAAI,EAAE;IAAI,CAAE,CAAC;IAC5C,IAAI,CAACW,GAAG,CAACM,OAAO,EAAE;MACdN,GAAG,CAACC,UAAU,CAAC/N,KAAK,EAAE;MACtB8N,GAAG,CAACM,OAAO,GAAG,IAAI;MAClB,IAAI,IAAI,CAAC,CAAAxL,WAAY,IAAI,IAAI,EAAE;QAAEkL,GAAG,CAACC,UAAU,CAAC7N,KAAK,CAAC,IAAI,CAAC,CAAA0C,WAAY,CAAC;;;IAE5E,OAAO,IAAI;EACf;EAEA,MAAMe,IAAIA,CAACtC,KAAoB,EAAE,GAAGqI,IAAgB;IAChD,MAAMoE,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAO,MAAO,CAAChN,KAAK,EAAEqI,IAAI,CAAC;IAC3C;IACA;IACA,IAAI,CAACoE,GAAG,IAAIA,GAAG,CAACiB,SAAS,CAAC/M,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO,KAAK;;IAAG;IAEzD,MAAMiN,KAAK,GAAGnB,GAAG,CAACiB,SAAS,CAAC/M,MAAM;IAClC8L,GAAG,CAACiB,SAAS,GAAGjB,GAAG,CAACiB,SAAS,CAACzN,MAAM,CAAC,CAAC;MAAEwL,QAAQ;MAAEK;IAAI,CAAE,KAAI;MACxD,MAAM+B,OAAO,GAAG,IAAI/R,YAAY,CAAC,IAAI,EAAGgQ,IAAI,GAAG,IAAI,GAAEL,QAAQ,EAAGzL,KAAK,CAAC;MACtE,IAAI;QACAyL,QAAQ,CAAC9D,IAAI,CAAC,IAAI,EAAE,GAAGU,IAAI,EAAEwF,OAAO,CAAC;OACxC,CAAC,OAAMpJ,KAAK,EAAE;MACf,OAAO,CAACqH,IAAI;IAChB,CAAC,CAAC;IAEF,IAAIW,GAAG,CAACiB,SAAS,CAAC/M,MAAM,KAAK,CAAC,EAAE;MAC5B,IAAI8L,GAAG,CAACM,OAAO,EAAE;QAAEN,GAAG,CAACC,UAAU,CAAC9N,IAAI,EAAE;;MACxC,IAAI,CAAC,CAAAyC,IAAK,CAAC+B,MAAM,CAACqJ,GAAG,CAAC5M,GAAG,CAAC;;IAG9B,OAAQ+N,KAAK,GAAG,CAAC;EACrB;EAEA,MAAME,aAAaA,CAAC9N,KAAqB;IACrC,IAAIA,KAAK,EAAE;MACP,MAAMyM,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAO,MAAO,CAAChN,KAAK,CAAC;MACrC,IAAI,CAACyM,GAAG,EAAE;QAAE,OAAO,CAAC;;MACpB,OAAOA,GAAG,CAACiB,SAAS,CAAC/M,MAAM;;IAG/B,IAAIoN,KAAK,GAAG,CAAC;IACb,KAAK,MAAM;MAAEL;IAAS,CAAE,IAAI,IAAI,CAAC,CAAArM,IAAK,CAAC/B,MAAM,EAAE,EAAE;MAC7CyO,KAAK,IAAIL,SAAS,CAAC/M,MAAM;;IAE7B,OAAOoN,KAAK;EAChB;EAEA,MAAML,SAASA,CAAC1N,KAAqB;IACjC,IAAIA,KAAK,EAAE;MACP,MAAMyM,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAO,MAAO,CAAChN,KAAK,CAAC;MACrC,IAAI,CAACyM,GAAG,EAAE;QAAE,OAAQ,EAAG;;MACvB,OAAOA,GAAG,CAACiB,SAAS,CAACvN,GAAG,CAAC,CAAC;QAAEsL;MAAQ,CAAE,KAAKA,QAAQ,CAAC;;IAExD,IAAI9G,MAAM,GAAoB,EAAG;IACjC,KAAK,MAAM;MAAE+I;IAAS,CAAE,IAAI,IAAI,CAAC,CAAArM,IAAK,CAAC/B,MAAM,EAAE,EAAE;MAC7CqF,MAAM,GAAGA,MAAM,CAAC9J,MAAM,CAAC6S,SAAS,CAACvN,GAAG,CAAC,CAAC;QAAEsL;MAAQ,CAAE,KAAKA,QAAQ,CAAC,CAAC;;IAErE,OAAO9G,MAAM;EACjB;EAEA,MAAMoH,GAAGA,CAAC/L,KAAoB,EAAEyL,QAAmB;IAC/C,MAAMgB,GAAG,GAAG,MAAM,IAAI,CAAC,CAAAO,MAAO,CAAChN,KAAK,CAAC;IACrC,IAAI,CAACyM,GAAG,EAAE;MAAE,OAAO,IAAI;;IAEvB,IAAIhB,QAAQ,EAAE;MACV,MAAMpH,KAAK,GAAGoI,GAAG,CAACiB,SAAS,CAACvN,GAAG,CAAC,CAAC;QAAEsL;MAAQ,CAAE,KAAKA,QAAQ,CAAC,CAACvH,OAAO,CAACuH,QAAQ,CAAC;MAC7E,IAAIpH,KAAK,IAAI,CAAC,EAAE;QAAEoI,GAAG,CAACiB,SAAS,CAACM,MAAM,CAAC3J,KAAK,EAAE,CAAC,CAAC;;;IAGpD,IAAI,CAACoH,QAAQ,IAAIgB,GAAG,CAACiB,SAAS,CAAC/M,MAAM,KAAK,CAAC,EAAE;MACzC,IAAI8L,GAAG,CAACM,OAAO,EAAE;QAAEN,GAAG,CAACC,UAAU,CAAC9N,IAAI,EAAE;;MACxC,IAAI,CAAC,CAAAyC,IAAK,CAAC+B,MAAM,CAACqJ,GAAG,CAAC5M,GAAG,CAAC;;IAG9B,OAAO,IAAI;EACf;EAEA,MAAMoO,kBAAkBA,CAACjO,KAAqB;IAC1C,IAAIA,KAAK,EAAE;MACP,MAAM;QAAEH,GAAG;QAAEkN,OAAO;QAAEL;MAAU,CAAE,GAAG,MAAM,IAAI,CAAC,CAAAU,MAAO,CAACpN,KAAK,CAAC;MAC9D,IAAI+M,OAAO,EAAE;QAAEL,UAAU,CAAC9N,IAAI,EAAE;;MAChC,IAAI,CAAC,CAAAyC,IAAK,CAAC+B,MAAM,CAACvD,GAAG,CAAC;KACzB,MAAM;MACH,KAAK,MAAM,CAAEA,GAAG,EAAE;QAAEkN,OAAO;QAAEL;MAAU,CAAE,CAAE,IAAI,IAAI,CAAC,CAAArL,IAAK,EAAE;QACvD,IAAI0L,OAAO,EAAE;UAAEL,UAAU,CAAC9N,IAAI,EAAE;;QAChC,IAAI,CAAC,CAAAyC,IAAK,CAAC+B,MAAM,CAACvD,GAAG,CAAC;;;IAG9B,OAAO,IAAI;EACf;EAEA;EACA,MAAMqO,WAAWA,CAAClO,KAAoB,EAAEyL,QAAkB;IACvD,OAAO,MAAM,IAAI,CAACkC,EAAE,CAAC3N,KAAK,EAAEyL,QAAQ,CAAC;EACxC;EAEA;EACA,MAAM0C,cAAcA,CAACnO,KAAoB,EAAEyL,QAAkB;IAC1D,OAAO,IAAI,CAACM,GAAG,CAAC/L,KAAK,EAAEyL,QAAQ,CAAC;EACnC;EAEA;;;;;;;EAOA,IAAIjK,SAASA,CAAA;IACT,OAAO,IAAI,CAAC,CAAAA,SAAU;EAC1B;EAEA;;;;;;EAMA4M,OAAOA,CAAA;IACH;IACA,IAAI,CAACH,kBAAkB,EAAE;IAEzB;IACA,KAAK,MAAM/B,OAAO,IAAI,IAAI,CAAC,CAAApK,MAAO,CAAC5D,IAAI,EAAE,EAAE;MACvC,IAAI,CAAC+N,aAAa,CAACC,OAAO,CAAC;;IAG/B,IAAI,CAAC,CAAA1K,SAAU,GAAG,IAAI;EAC1B;EAEA;;;;;;;;;;;EAWA,IAAI8K,MAAMA,CAAA;IAAc,OAAQ,IAAI,CAAC,CAAA/K,WAAY,IAAI,IAAI;EAAG;EAC5D,IAAI+K,MAAMA,CAACzN,KAAc;IACrB,IAAI,CAAC,CAACA,KAAK,KAAK,IAAI,CAACyN,MAAM,EAAE;MAAE;;IAE/B,IAAI,IAAI,CAACA,MAAM,EAAE;MACb,IAAI,CAACvN,MAAM,EAAE;KAChB,MAAM;MACH,IAAI,CAACF,KAAK,CAAC,KAAK,CAAC;;EAEzB;EAEA;;;;;EAKAA,KAAKA,CAACC,eAAyB;IAC3B,IAAI,CAAC,CAAA8C,eAAgB,GAAG,CAAC,CAAC;IAE1B,IAAI,IAAI,CAAC,CAAAL,WAAY,IAAI,IAAI,EAAE;MAC3B,IAAI,IAAI,CAAC,CAAAA,WAAY,IAAI,CAAC,CAACzC,eAAe,EAAE;QAAE;;MAC9CtD,MAAM,CAAC,KAAK,EAAE,wCAAwC,EAAE,uBAAuB,EAAE;QAC7EgK,SAAS,EAAE;OACd,CAAC;;IAGN,IAAI,CAACgH,kBAAkB,CAAE6B,CAAC,IAAKA,CAAC,CAACxP,KAAK,CAACC,eAAe,CAAC,CAAC;IACxD,IAAI,CAAC,CAAAyC,WAAY,GAAG,CAAC,CAACzC,eAAe;IAErC,KAAK,MAAM0M,KAAK,IAAI,IAAI,CAAC,CAAA1J,MAAO,CAACxC,MAAM,EAAE,EAAE;MACvC;MACA,IAAIkM,KAAK,CAACA,KAAK,EAAE;QAAEG,YAAY,CAACH,KAAK,CAACA,KAAK,CAAC;;MAE5C;MACAA,KAAK,CAACe,IAAI,GAAGxL,OAAO,EAAE,GAAGyK,KAAK,CAACe,IAAI;;EAE3C;EAEA;;;EAGAxN,MAAMA,CAAA;IACF,IAAI,IAAI,CAAC,CAAAwC,WAAY,IAAI,IAAI,EAAE;MAAE;;IAEjC,IAAI,CAACiL,kBAAkB,CAAE6B,CAAC,IAAKA,CAAC,CAACtP,MAAM,EAAE,CAAC;IAC1C,IAAI,CAAC,CAAAwC,WAAY,GAAG,IAAI;IACxB,KAAK,MAAMiK,KAAK,IAAI,IAAI,CAAC,CAAA1J,MAAO,CAACxC,MAAM,EAAE,EAAE;MACvC;MACA,IAAI2D,OAAO,GAAGuI,KAAK,CAACe,IAAI;MACxB,IAAItJ,OAAO,GAAG,CAAC,EAAE;QAAEA,OAAO,GAAG,CAAC;;MAE9B;MACAuI,KAAK,CAACe,IAAI,GAAGxL,OAAO,EAAE;MAEtB;MACAsB,UAAU,CAACmJ,KAAK,CAACa,IAAI,EAAEpJ,OAAO,CAAC;;EAEvC;;AAIJ,SAASqL,YAAYA,CAAC3J,MAAc,EAAEhG,KAAa;EAC/C,IAAI;IACA,MAAM4P,KAAK,GAAGC,WAAW,CAAC7J,MAAM,EAAEhG,KAAK,CAAC;IACxC,IAAI4P,KAAK,EAAE;MAAE,OAAOvS,YAAY,CAACuS,KAAK,CAAC;;GAC1C,CAAC,OAAM9J,KAAK,EAAE;EACf,OAAO,IAAI;AACf;AAEA,SAAS+J,WAAWA,CAAC7J,MAAc,EAAEhG,KAAa;EAC9C,IAAIgG,MAAM,KAAK,IAAI,EAAE;IAAE,OAAO,IAAI;;EAClC,IAAI;IACA,MAAM8J,MAAM,GAAGrT,SAAS,CAACL,SAAS,CAAC4J,MAAM,EAAEhG,KAAK,EAAEA,KAAK,GAAG,EAAE,CAAC,CAAC;IAC9D,MAAMgC,MAAM,GAAGvF,SAAS,CAACL,SAAS,CAAC4J,MAAM,EAAE8J,MAAM,EAAEA,MAAM,GAAG,EAAE,CAAC,CAAC;IAEhE,OAAO1T,SAAS,CAAC4J,MAAM,EAAE8J,MAAM,GAAG,EAAE,EAAEA,MAAM,GAAG,EAAE,GAAG9N,MAAM,CAAC;GAC9D,CAAC,OAAO8D,KAAK,EAAE;EAChB,OAAO,IAAI;AACf;AAEA,SAASiK,MAAMA,CAACpR,KAAa;EACzB,MAAMqH,MAAM,GAAGhJ,SAAS,CAAC2B,KAAK,CAAC;EAC/B,IAAIqH,MAAM,CAAChE,MAAM,GAAG,EAAE,EAAE;IAAE,MAAM,IAAIjB,KAAK,CAAC,6BAA6B,CAAC;;EAExE,MAAMiP,MAAM,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;EACjCD,MAAM,CAAChM,GAAG,CAACgC,MAAM,EAAE,EAAE,GAAGA,MAAM,CAAChE,MAAM,CAAC;EACtC,OAAOgO,MAAM;AACjB;AAEA,SAASE,QAAQA,CAACvR,KAAiB;EAC/B,IAAKA,KAAK,CAACqD,MAAM,GAAG,EAAE,KAAM,CAAC,EAAE;IAAE,OAAOrD,KAAK;;EAE7C,MAAMqH,MAAM,GAAG,IAAIiK,UAAU,CAACE,IAAI,CAACC,IAAI,CAACzR,KAAK,CAACqD,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;EAChEgE,MAAM,CAAChC,GAAG,CAACrF,KAAK,CAAC;EACjB,OAAOqH,MAAM;AACjB;AAEA,MAAMqK,KAAK,GAAe,IAAIJ,UAAU,CAAC,EAAG,CAAC;AAE7C;AACA,SAASnG,WAAWA,CAACwG,KAAuB;EACxC,MAAMtK,MAAM,GAAsB,EAAG;EAErC,IAAIuK,SAAS,GAAG,CAAC;EAEjB;EACA,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,KAAK,CAACtO,MAAM,EAAEkD,CAAC,EAAE,EAAE;IACnCc,MAAM,CAAClE,IAAI,CAACuO,KAAK,CAAC;IAClBE,SAAS,IAAI,EAAE;;EAGnB,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,KAAK,CAACtO,MAAM,EAAEkD,CAAC,EAAE,EAAE;IACnC,MAAMF,IAAI,GAAGxI,QAAQ,CAAC8T,KAAK,CAACpL,CAAC,CAAC,CAAC;IAE/B;IACAc,MAAM,CAACd,CAAC,CAAC,GAAG6K,MAAM,CAACQ,SAAS,CAAC;IAE7B;IACAvK,MAAM,CAAClE,IAAI,CAACiO,MAAM,CAAC/K,IAAI,CAAChD,MAAM,CAAC,CAAC;IAChCgE,MAAM,CAAClE,IAAI,CAACoO,QAAQ,CAAClL,IAAI,CAAC,CAAC;IAC3BuL,SAAS,IAAI,EAAE,GAAGJ,IAAI,CAACC,IAAI,CAACpL,IAAI,CAAChD,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE;;EAGtD,OAAO9F,MAAM,CAAC8J,MAAM,CAAC;AACzB;AAEA,MAAMwK,KAAK,GAAG,oEAAoE;AAClF,SAASlH,mBAAmBA,CAACtE,IAAY;EACrC,MAAMgB,MAAM,GAAa;IACrBjB,MAAM,EAAE,EAAE;IAAEF,IAAI,EAAE,EAAG;IAAED,QAAQ,EAAE,EAAE;IAAEiF,QAAQ,EAAE,EAAE;IAAEE,SAAS,EAAE,EAAE;IAAEJ,SAAS,EAAE;GAChF;EAED9M,MAAM,CAACV,UAAU,CAAC6I,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,EAAE,kCAAkC,EAAE,gBAAgB,EAAE;IACrFmB,MAAM,EAAE;GACX,CAAC;EAEF,MAAMpB,MAAM,GAAG3I,SAAS,CAAC4I,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;EACrCnI,MAAM,CAACT,SAAS,CAAC2I,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK3I,SAAS,CAACoU,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,+BAA+B,EAAE,gBAAgB,EAAE;IAC5GrK,MAAM,EAAE;GACX,CAAC;EACFH,MAAM,CAACjB,MAAM,GAAG3I,SAAS,CAAC2I,MAAM,EAAE,EAAE,CAAC;EAErC;EACA,IAAI;IACA,MAAMF,IAAI,GAAkB,EAAE;IAC9B,MAAM4L,UAAU,GAAGhU,SAAS,CAACL,SAAS,CAAC4I,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACrD,MAAM0L,UAAU,GAAGjU,SAAS,CAACL,SAAS,CAAC4I,IAAI,EAAEyL,UAAU,EAAEA,UAAU,GAAG,EAAE,CAAC,CAAC;IAC1E,MAAME,QAAQ,GAAGvU,SAAS,CAAC4I,IAAI,EAAEyL,UAAU,GAAG,EAAE,CAAC;IACjD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;MACjC,MAAMzL,GAAG,GAAGwK,YAAY,CAACgB,QAAQ,EAAEC,CAAC,GAAG,EAAE,CAAC;MAC1C,IAAIzL,GAAG,IAAI,IAAI,EAAE;QAAE,MAAM,IAAIpE,KAAK,CAAC,OAAO,CAAC;;MAC3C8D,IAAI,CAAC/C,IAAI,CAACqD,GAAG,CAAC;;IAElBa,MAAM,CAACnB,IAAI,GAAGA,IAAI;GACrB,CAAC,OAAOiB,KAAK,EAAE;IACZjJ,MAAM,CAAC,KAAK,EAAE,6BAA6B,EAAE,gBAAgB,EAAE;MAC3DsJ,MAAM,EAAE;KACX,CAAC;;EAGN;EACA,IAAI;IACA,MAAMvB,QAAQ,GAAGiL,WAAW,CAAC7K,IAAI,EAAE,EAAE,CAAC;IACtC,IAAIJ,QAAQ,IAAI,IAAI,EAAE;MAAE,MAAM,IAAI7D,KAAK,CAAC,OAAO,CAAC;;IAChDiF,MAAM,CAACpB,QAAQ,GAAGA,QAAQ;GAC7B,CAAC,OAAOkB,KAAK,EAAE;IACZjJ,MAAM,CAAC,KAAK,EAAE,iCAAiC,EAAE,gBAAgB,EAAE;MAC/DsJ,MAAM,EAAE;KACX,CAAC;;EAGN;EACAtJ,MAAM,CAACT,SAAS,CAAC4I,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK5I,SAAS,CAACoU,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,0CAA0C,EAAE,gBAAgB,EAAE;IACxHrK,MAAM,EAAE;GACX,CAAC;EACFH,MAAM,CAAC6D,QAAQ,GAAGzN,SAAS,CAAC4I,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC;EAE1C;EACA,IAAI;IACA,MAAM+E,SAAS,GAAG8F,WAAW,CAAC7K,IAAI,EAAE,GAAG,CAAC;IACxC,IAAI+E,SAAS,IAAI,IAAI,EAAE;MAAE,MAAM,IAAIhJ,KAAK,CAAC,OAAO,CAAC;;IACjDiF,MAAM,CAAC+D,SAAS,GAAGA,SAAS;GAC/B,CAAC,OAAOjE,KAAK,EAAE;IACZjJ,MAAM,CAAC,KAAK,EAAE,kCAAkC,EAAE,gBAAgB,EAAE;MAChEsJ,MAAM,EAAE;KACX,CAAC;;EAGNH,MAAM,CAAC2D,SAAS,GAAG,yCAAyC,CAACkH,KAAK,CAAC,GAAG,CAAC,CAACrP,GAAG,CAAEvC,CAAC,IAAW+G,MAAO,CAAC/G,CAAC,CAAC,CAAC;EAEpG,OAAO+G,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}