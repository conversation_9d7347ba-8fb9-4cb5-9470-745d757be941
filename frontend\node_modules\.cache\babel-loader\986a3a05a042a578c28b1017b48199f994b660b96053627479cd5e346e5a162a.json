{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { Signature } from \"../crypto/index.js\";\nimport { getBigInt } from \"../utils/index.js\";\nexport function authorizationify(auth) {\n  return {\n    address: getAddress(auth.address),\n    nonce: getBigInt(auth.nonce != null ? auth.nonce : 0),\n    chainId: getBigInt(auth.chainId != null ? auth.chainId : 0),\n    signature: Signature.from(auth.signature)\n  };\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "Signature", "getBigInt", "authorizationify", "auth", "address", "nonce", "chainId", "signature", "from"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\transaction\\authorization.ts"], "sourcesContent": ["import { getAddress } from \"../address/index.js\";\nimport { Signature } from \"../crypto/index.js\";\nimport { getBigInt } from \"../utils/index.js\";\n\nimport type { Authorization, AuthorizationLike } from \"./index.js\";\n\nexport function authorizationify(auth: AuthorizationLike): Authorization {\n    return {\n        address: getAddress(auth.address),\n        nonce: getBigInt((auth.nonce != null) ? auth.nonce: 0),\n        chainId: getBigInt((auth.chainId != null)? auth.chainId: 0),\n        signature: Signature.from(auth.signature)\n    };\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,mBAAmB;AAI7C,OAAM,SAAUC,gBAAgBA,CAACC,IAAuB;EACpD,OAAO;IACHC,OAAO,EAAEL,UAAU,CAACI,IAAI,CAACC,OAAO,CAAC;IACjCC,KAAK,EAAEJ,SAAS,CAAEE,IAAI,CAACE,KAAK,IAAI,IAAI,GAAIF,IAAI,CAACE,KAAK,GAAE,CAAC,CAAC;IACtDC,OAAO,EAAEL,SAAS,CAAEE,IAAI,CAACG,OAAO,IAAI,IAAI,GAAGH,IAAI,CAACG,OAAO,GAAE,CAAC,CAAC;IAC3DC,SAAS,EAAEP,SAAS,CAACQ,IAAI,CAACL,IAAI,CAACI,SAAS;GAC3C;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}