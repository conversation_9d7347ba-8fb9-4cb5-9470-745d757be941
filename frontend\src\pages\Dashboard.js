import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useGame } from '../contexts/GameContext';
import { useWeb3 } from '../contexts/Web3Context';
import TokenBalance from '../components/TokenBalance';
import QuestCard from '../components/QuestCard';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  ShieldCheckIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PlayIcon,
  GiftIcon
} from '@heroicons/react/24/outline';

const Dashboard = () => {
  const { user } = useAuth();
  const { heroes, userStats, battleHistory, dailyQuests, loading, claimQuestReward } = useGame();
  const [claimingQuest, setClaimingQuest] = useState(null);

  const handleClaimQuest = async (questId) => {
    try {
      setClaimingQuest(questId);
      await claimQuestReward(questId);
    } catch (error) {
      console.error('Failed to claim quest:', error);
    } finally {
      setClaimingQuest(null);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  const quickStats = [
    {
      label: 'Heroes',
      value: heroes?.length || 0,
      icon: ShieldCheckIcon,
      color: 'text-blue-400',
    },
    {
      label: 'Level',
      value: user?.level || 1,
      icon: ChartBarIcon,
      color: 'text-green-400',
    },
    {
      label: 'Wins',
      value: user?.wins || 0,
      icon: TrophyIcon,
      color: 'text-purple-400',
    },
  ];

  const quickActions = [
    {
      title: 'Start Battle',
      description: 'Fight monsters and earn CQT',
      href: '/battle',
      icon: PlayIcon,
      color: 'from-red-500 to-red-600',
    },
    {
      title: 'Manage Heroes',
      description: 'Upgrade and customize your team',
      href: '/heroes',
      icon: ShieldCheckIcon,
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'Stake Tokens',
      description: 'Earn passive rewards',
      href: '/staking',
      icon: CurrencyDollarIcon,
      color: 'from-green-500 to-green-600',
    },
    {
      title: 'Join Tournament',
      description: 'Compete for big prizes',
      href: '/tournaments',
      icon: TrophyIcon,
      color: 'from-purple-500 to-purple-600',
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">
          Welcome back, {user?.username || 'Warrior'}!
        </h1>
        <p className="text-dark-300">
          Ready for your next adventure?
        </p>
      </div>

      {/* Token Balance */}
      <div className="flex justify-center mb-8">
        <TokenBalance showRefresh={true} className="scale-110" />
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {quickStats.map((stat, index) => (
          <div key={index} className="game-card p-6 text-center">
            <stat.icon className={`w-10 h-10 ${stat.color} mx-auto mb-3`} />
            <div className="text-3xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-sm text-dark-400">{stat.label}</div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <Link
              key={index}
              to={action.href}
              className="game-card p-6 hover:glow-primary transition-all duration-300 group"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                <action.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{action.title}</h3>
              <p className="text-dark-400 text-sm">{action.description}</p>
            </Link>
          ))}
        </div>
      </div>

      {/* Daily Quests */}
      {dailyQuests && dailyQuests.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
            <GiftIcon className="w-6 h-6 mr-2" />
            Daily Quests
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {dailyQuests.slice(0, 3).map((quest, index) => (
              <QuestCard
                key={index}
                quest={quest}
                onClaim={handleClaimQuest}
                claiming={claimingQuest === quest.id}
              />
            ))}
          </div>
        </div>
      )}

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Battles */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Recent Battles</h2>
          <div className="game-card p-4">
            {battleHistory && battleHistory.length > 0 ? (
              <div className="space-y-3">
                {battleHistory.slice(0, 5).map((battle, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0">
                    <div>
                      <span className="text-white font-medium capitalize">{battle.type}</span>
                      <span className="text-dark-400 text-sm ml-2">
                        vs {battle.opponent === 'AI' ? 'Monster' : 'Player'}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${battle.result === 'win' ? 'text-green-400' : 'text-red-400'}`}>
                        {battle.result === 'win' ? 'Victory' : 'Defeat'}
                      </div>
                      {battle.reward > 0 && (
                        <div className="text-yellow-400 text-sm">+{battle.reward} CQT</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-dark-400">
                <TrophyIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No battles yet</p>
                <Link to="/battle" className="text-primary-400 hover:text-primary-300 text-sm">
                  Start your first battle
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Hero Overview */}
        <div>
          <h2 className="text-2xl font-bold text-white mb-4">Your Heroes</h2>
          <div className="game-card p-4">
            {heroes && heroes.length > 0 ? (
              <div className="space-y-3">
                {heroes.slice(0, 3).map((hero, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0">
                    <div>
                      <div className="text-white font-medium">{hero.name}</div>
                      <div className="text-dark-400 text-sm capitalize">{hero.type} • Level {hero.level}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-primary-400 font-medium">
                        {hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk} Power
                      </div>
                    </div>
                  </div>
                ))}
                {heroes.length > 3 && (
                  <div className="text-center pt-2">
                    <Link to="/heroes" className="text-primary-400 hover:text-primary-300 text-sm">
                      View all {heroes.length} heroes
                    </Link>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-dark-400">
                <ShieldCheckIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No heroes found</p>
                <Link to="/heroes" className="text-primary-400 hover:text-primary-300 text-sm">
                  Manage your heroes
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
