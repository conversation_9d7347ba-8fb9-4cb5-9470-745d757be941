{"ast": null, "code": "import { ZeroHash } from \"../constants/index.js\";\nimport { concat, dataLength, getBigInt, getBytes, getNumber, hexlify, toBeArray, isHexString, zeroPadValue, assertArgument, assertPrivate } from \"../utils/index.js\";\n// Constants\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_2 = BigInt(2);\nconst BN_27 = BigInt(27);\nconst BN_28 = BigInt(28);\nconst BN_35 = BigInt(35);\nconst _guard = {};\nfunction toUint256(value) {\n  return zeroPadValue(toBeArray(value), 32);\n}\n/**\n *  A Signature  @TODO\n *\n *\n *  @_docloc: api/crypto:Signing\n */\nexport class Signature {\n  #r;\n  #s;\n  #v;\n  #networkV;\n  /**\n   *  The ``r`` value for a signature.\n   *\n   *  This represents the ``x`` coordinate of a \"reference\" or\n   *  challenge point, from which the ``y`` can be computed.\n   */\n  get r() {\n    return this.#r;\n  }\n  set r(value) {\n    assertArgument(dataLength(value) === 32, \"invalid r\", \"value\", value);\n    this.#r = hexlify(value);\n  }\n  /**\n   *  The ``s`` value for a signature.\n   */\n  get s() {\n    return this.#s;\n  }\n  set s(_value) {\n    assertArgument(dataLength(_value) === 32, \"invalid s\", \"value\", _value);\n    const value = hexlify(_value);\n    assertArgument(parseInt(value.substring(0, 3)) < 8, \"non-canonical s\", \"value\", value);\n    this.#s = value;\n  }\n  /**\n   *  The ``v`` value for a signature.\n   *\n   *  Since a given ``x`` value for ``r`` has two possible values for\n   *  its correspondin ``y``, the ``v`` indicates which of the two ``y``\n   *  values to use.\n   *\n   *  It is normalized to the values ``27`` or ``28`` for legacy\n   *  purposes.\n   */\n  get v() {\n    return this.#v;\n  }\n  set v(value) {\n    const v = getNumber(value, \"value\");\n    assertArgument(v === 27 || v === 28, \"invalid v\", \"v\", value);\n    this.#v = v;\n  }\n  /**\n   *  The EIP-155 ``v`` for legacy transactions. For non-legacy\n   *  transactions, this value is ``null``.\n   */\n  get networkV() {\n    return this.#networkV;\n  }\n  /**\n   *  The chain ID for EIP-155 legacy transactions. For non-legacy\n   *  transactions, this value is ``null``.\n   */\n  get legacyChainId() {\n    const v = this.networkV;\n    if (v == null) {\n      return null;\n    }\n    return Signature.getChainId(v);\n  }\n  /**\n   *  The ``yParity`` for the signature.\n   *\n   *  See ``v`` for more details on how this value is used.\n   */\n  get yParity() {\n    return this.v === 27 ? 0 : 1;\n  }\n  /**\n   *  The [[link-eip-2098]] compact representation of the ``yParity``\n   *  and ``s`` compacted into a single ``bytes32``.\n   */\n  get yParityAndS() {\n    // The EIP-2098 compact representation\n    const yParityAndS = getBytes(this.s);\n    if (this.yParity) {\n      yParityAndS[0] |= 0x80;\n    }\n    return hexlify(yParityAndS);\n  }\n  /**\n   *  The [[link-eip-2098]] compact representation.\n   */\n  get compactSerialized() {\n    return concat([this.r, this.yParityAndS]);\n  }\n  /**\n   *  The serialized representation.\n   */\n  get serialized() {\n    return concat([this.r, this.s, this.yParity ? \"0x1c\" : \"0x1b\"]);\n  }\n  /**\n   *  @private\n   */\n  constructor(guard, r, s, v) {\n    assertPrivate(guard, _guard, \"Signature\");\n    this.#r = r;\n    this.#s = s;\n    this.#v = v;\n    this.#networkV = null;\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return `Signature { r: \"${this.r}\", s: \"${this.s}\", yParity: ${this.yParity}, networkV: ${this.networkV} }`;\n  }\n  /**\n   *  Returns a new identical [[Signature]].\n   */\n  clone() {\n    const clone = new Signature(_guard, this.r, this.s, this.v);\n    if (this.networkV) {\n      clone.#networkV = this.networkV;\n    }\n    return clone;\n  }\n  /**\n   *  Returns a representation that is compatible with ``JSON.stringify``.\n   */\n  toJSON() {\n    const networkV = this.networkV;\n    return {\n      _type: \"signature\",\n      networkV: networkV != null ? networkV.toString() : null,\n      r: this.r,\n      s: this.s,\n      v: this.v\n    };\n  }\n  /**\n   *  Compute the chain ID from the ``v`` in a legacy EIP-155 transactions.\n   *\n   *  @example:\n   *    Signature.getChainId(45)\n   *    //_result:\n   *\n   *    Signature.getChainId(46)\n   *    //_result:\n   */\n  static getChainId(v) {\n    const bv = getBigInt(v, \"v\");\n    // The v is not an EIP-155 v, so it is the unspecified chain ID\n    if (bv == BN_27 || bv == BN_28) {\n      return BN_0;\n    }\n    // Bad value for an EIP-155 v\n    assertArgument(bv >= BN_35, \"invalid EIP-155 v\", \"v\", v);\n    return (bv - BN_35) / BN_2;\n  }\n  /**\n   *  Compute the ``v`` for a chain ID for a legacy EIP-155 transactions.\n   *\n   *  Legacy transactions which use [[link-eip-155]] hijack the ``v``\n   *  property to include the chain ID.\n   *\n   *  @example:\n   *    Signature.getChainIdV(5, 27)\n   *    //_result:\n   *\n   *    Signature.getChainIdV(5, 28)\n   *    //_result:\n   *\n   */\n  static getChainIdV(chainId, v) {\n    return getBigInt(chainId) * BN_2 + BigInt(35 + v - 27);\n  }\n  /**\n   *  Compute the normalized legacy transaction ``v`` from a ``yParirty``,\n   *  a legacy transaction ``v`` or a legacy [[link-eip-155]] transaction.\n   *\n   *  @example:\n   *    // The values 0 and 1 imply v is actually yParity\n   *    Signature.getNormalizedV(0)\n   *    //_result:\n   *\n   *    // Legacy non-EIP-1559 transaction (i.e. 27 or 28)\n   *    Signature.getNormalizedV(27)\n   *    //_result:\n   *\n   *    // Legacy EIP-155 transaction (i.e. >= 35)\n   *    Signature.getNormalizedV(46)\n   *    //_result:\n   *\n   *    // Invalid values throw\n   *    Signature.getNormalizedV(5)\n   *    //_error:\n   */\n  static getNormalizedV(v) {\n    const bv = getBigInt(v);\n    if (bv === BN_0 || bv === BN_27) {\n      return 27;\n    }\n    if (bv === BN_1 || bv === BN_28) {\n      return 28;\n    }\n    assertArgument(bv >= BN_35, \"invalid v\", \"v\", v);\n    // Otherwise, EIP-155 v means odd is 27 and even is 28\n    return bv & BN_1 ? 27 : 28;\n  }\n  /**\n   *  Creates a new [[Signature]].\n   *\n   *  If no %%sig%% is provided, a new [[Signature]] is created\n   *  with default values.\n   *\n   *  If %%sig%% is a string, it is parsed.\n   */\n  static from(sig) {\n    function assertError(check, message) {\n      assertArgument(check, message, \"signature\", sig);\n    }\n    ;\n    if (sig == null) {\n      return new Signature(_guard, ZeroHash, ZeroHash, 27);\n    }\n    if (typeof sig === \"string\") {\n      const bytes = getBytes(sig, \"signature\");\n      if (bytes.length === 64) {\n        const r = hexlify(bytes.slice(0, 32));\n        const s = bytes.slice(32, 64);\n        const v = s[0] & 0x80 ? 28 : 27;\n        s[0] &= 0x7f;\n        return new Signature(_guard, r, hexlify(s), v);\n      }\n      if (bytes.length === 65) {\n        const r = hexlify(bytes.slice(0, 32));\n        const s = bytes.slice(32, 64);\n        assertError((s[0] & 0x80) === 0, \"non-canonical s\");\n        const v = Signature.getNormalizedV(bytes[64]);\n        return new Signature(_guard, r, hexlify(s), v);\n      }\n      assertError(false, \"invalid raw signature length\");\n    }\n    if (sig instanceof Signature) {\n      return sig.clone();\n    }\n    // Get r\n    const _r = sig.r;\n    assertError(_r != null, \"missing r\");\n    const r = toUint256(_r);\n    // Get s; by any means necessary (we check consistency below)\n    const s = function (s, yParityAndS) {\n      if (s != null) {\n        return toUint256(s);\n      }\n      if (yParityAndS != null) {\n        assertError(isHexString(yParityAndS, 32), \"invalid yParityAndS\");\n        const bytes = getBytes(yParityAndS);\n        bytes[0] &= 0x7f;\n        return hexlify(bytes);\n      }\n      assertError(false, \"missing s\");\n    }(sig.s, sig.yParityAndS);\n    assertError((getBytes(s)[0] & 0x80) == 0, \"non-canonical s\");\n    // Get v; by any means necessary (we check consistency below)\n    const {\n      networkV,\n      v\n    } = function (_v, yParityAndS, yParity) {\n      if (_v != null) {\n        const v = getBigInt(_v);\n        return {\n          networkV: v >= BN_35 ? v : undefined,\n          v: Signature.getNormalizedV(v)\n        };\n      }\n      if (yParityAndS != null) {\n        assertError(isHexString(yParityAndS, 32), \"invalid yParityAndS\");\n        return {\n          v: getBytes(yParityAndS)[0] & 0x80 ? 28 : 27\n        };\n      }\n      if (yParity != null) {\n        switch (getNumber(yParity, \"sig.yParity\")) {\n          case 0:\n            return {\n              v: 27\n            };\n          case 1:\n            return {\n              v: 28\n            };\n        }\n        assertError(false, \"invalid yParity\");\n      }\n      assertError(false, \"missing v\");\n    }(sig.v, sig.yParityAndS, sig.yParity);\n    const result = new Signature(_guard, r, s, v);\n    if (networkV) {\n      result.#networkV = networkV;\n    }\n    // If multiple of v, yParity, yParityAndS we given, check they match\n    assertError(sig.yParity == null || getNumber(sig.yParity, \"sig.yParity\") === result.yParity, \"yParity mismatch\");\n    assertError(sig.yParityAndS == null || sig.yParityAndS === result.yParityAndS, \"yParityAndS mismatch\");\n    return result;\n  }\n}", "map": {"version": 3, "names": ["ZeroHash", "concat", "dataLength", "getBigInt", "getBytes", "getNumber", "hexlify", "toBeArray", "isHexString", "zeroPadValue", "assertArgument", "assertPrivate", "BN_0", "BigInt", "BN_1", "BN_2", "BN_27", "BN_28", "BN_35", "_guard", "toUint256", "value", "Signature", "r", "s", "v", "networkV", "_value", "parseInt", "substring", "legacyChainId", "get<PERSON>hainId", "yParity", "yParityAndS", "compactSerialized", "serialized", "constructor", "guard", "Symbol", "for", "clone", "toJSON", "_type", "toString", "bv", "getChainIdV", "chainId", "getNormalizedV", "from", "sig", "assertError", "check", "message", "bytes", "length", "slice", "_r", "_v", "undefined", "result"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\signature.ts"], "sourcesContent": ["\nimport { ZeroHash } from \"../constants/index.js\";\nimport {\n    concat, dataLength, getBigInt, getBytes, getNumber, hexlify,\n    toBeArray, isHexString, zeroPadValue,\n    assertArgument, assertPrivate\n} from \"../utils/index.js\";\n\nimport type {\n    BigNumberish, BytesLike, Numeric\n} from \"../utils/index.js\";\n\n\n// Constants\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_2 = BigInt(2);\nconst BN_27 = BigInt(27);\nconst BN_28 = BigInt(28);\nconst BN_35 = BigInt(35);\n\n\nconst _guard = { };\n\n// @TODO: Allow Uint8Array\n\n/**\n *  A SignatureLike\n *\n *  @_docloc: api/crypto:Signing\n */\nexport type SignatureLike = Signature | string | {\n    r: string;\n    s: string;\n    v: BigNumberish;\n    yParity?: 0 | 1;\n    yParityAndS?: string;\n} | {\n    r: string;\n    yParityAndS: string;\n    yParity?: 0 | 1;\n    s?: string;\n    v?: number;\n} | {\n    r: string;\n    s: string;\n    yParity: 0 | 1;\n    v?: BigNumberish;\n    yParityAndS?: string;\n};\n\nfunction toUint256(value: BigNumberish): string {\n    return zeroPadValue(toBeArray(value), 32);\n}\n\n/**\n *  A Signature  @TODO\n *\n *\n *  @_docloc: api/crypto:Signing\n */\nexport class Signature {\n    #r: string;\n    #s: string;\n    #v: 27 | 28;\n    #networkV: null | bigint;\n\n    /**\n     *  The ``r`` value for a signature.\n     *\n     *  This represents the ``x`` coordinate of a \"reference\" or\n     *  challenge point, from which the ``y`` can be computed.\n     */\n    get r(): string { return this.#r; }\n    set r(value: BytesLike) {\n        assertArgument(dataLength(value) === 32, \"invalid r\", \"value\", value);\n        this.#r = hexlify(value);\n    }\n\n    /**\n     *  The ``s`` value for a signature.\n     */\n    get s(): string { return this.#s; }\n    set s(_value: BytesLike) {\n        assertArgument(dataLength(_value) === 32, \"invalid s\", \"value\", _value);\n        const value = hexlify(_value);\n        assertArgument(parseInt(value.substring(0, 3)) < 8, \"non-canonical s\", \"value\", value);\n        this.#s = value;\n    }\n\n    /**\n     *  The ``v`` value for a signature.\n     *\n     *  Since a given ``x`` value for ``r`` has two possible values for\n     *  its correspondin ``y``, the ``v`` indicates which of the two ``y``\n     *  values to use.\n     *\n     *  It is normalized to the values ``27`` or ``28`` for legacy\n     *  purposes.\n     */\n    get v(): 27 | 28 { return this.#v; }\n    set v(value: BigNumberish) {\n        const v = getNumber(value, \"value\");\n        assertArgument(v === 27 || v === 28, \"invalid v\", \"v\", value);\n        this.#v = v;\n    }\n\n    /**\n     *  The EIP-155 ``v`` for legacy transactions. For non-legacy\n     *  transactions, this value is ``null``.\n     */\n    get networkV(): null | bigint { return this.#networkV; }\n\n    /**\n     *  The chain ID for EIP-155 legacy transactions. For non-legacy\n     *  transactions, this value is ``null``.\n     */\n    get legacyChainId(): null | bigint {\n        const v = this.networkV;\n        if (v == null) { return null; }\n        return Signature.getChainId(v);\n    }\n\n    /**\n     *  The ``yParity`` for the signature.\n     *\n     *  See ``v`` for more details on how this value is used.\n     */\n    get yParity(): 0 | 1 {\n        return (this.v === 27) ? 0: 1;\n    }\n\n    /**\n     *  The [[link-eip-2098]] compact representation of the ``yParity``\n     *  and ``s`` compacted into a single ``bytes32``.\n     */\n    get yParityAndS(): string {\n        // The EIP-2098 compact representation\n        const yParityAndS = getBytes(this.s);\n        if (this.yParity) { yParityAndS[0] |= 0x80; }\n        return hexlify(yParityAndS);\n    }\n\n    /**\n     *  The [[link-eip-2098]] compact representation.\n     */\n    get compactSerialized(): string {\n        return concat([ this.r, this.yParityAndS ]);\n    }\n\n    /**\n     *  The serialized representation.\n     */\n    get serialized(): string {\n        return concat([ this.r, this.s, (this.yParity ? \"0x1c\": \"0x1b\") ]);\n    }\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, r: string, s: string, v: 27 | 28) {\n        assertPrivate(guard, _guard, \"Signature\");\n        this.#r = r;\n        this.#s = s;\n        this.#v = v;\n        this.#networkV = null;\n    }\n\n    [Symbol.for('nodejs.util.inspect.custom')](): string {\n        return `Signature { r: \"${ this.r }\", s: \"${ this.s }\", yParity: ${ this.yParity }, networkV: ${ this.networkV } }`;\n    }\n\n    /**\n     *  Returns a new identical [[Signature]].\n     */\n    clone(): Signature {\n        const clone = new Signature(_guard, this.r, this.s, this.v);\n        if (this.networkV) { clone.#networkV = this.networkV; }\n        return clone;\n    }\n\n    /**\n     *  Returns a representation that is compatible with ``JSON.stringify``.\n     */\n    toJSON(): any {\n        const networkV = this.networkV;\n        return {\n            _type: \"signature\",\n            networkV: ((networkV != null) ? networkV.toString(): null),\n            r: this.r, s: this.s, v: this.v,\n        };\n    }\n\n    /**\n     *  Compute the chain ID from the ``v`` in a legacy EIP-155 transactions.\n     *\n     *  @example:\n     *    Signature.getChainId(45)\n     *    //_result:\n     *\n     *    Signature.getChainId(46)\n     *    //_result:\n     */\n    static getChainId(v: BigNumberish): bigint {\n        const bv = getBigInt(v, \"v\");\n\n        // The v is not an EIP-155 v, so it is the unspecified chain ID\n        if ((bv == BN_27) || (bv == BN_28)) { return BN_0; }\n\n        // Bad value for an EIP-155 v\n        assertArgument(bv >= BN_35, \"invalid EIP-155 v\", \"v\", v);\n\n        return (bv - BN_35) / BN_2;\n    }\n\n    /**\n     *  Compute the ``v`` for a chain ID for a legacy EIP-155 transactions.\n     *\n     *  Legacy transactions which use [[link-eip-155]] hijack the ``v``\n     *  property to include the chain ID.\n     *\n     *  @example:\n     *    Signature.getChainIdV(5, 27)\n     *    //_result:\n     *\n     *    Signature.getChainIdV(5, 28)\n     *    //_result:\n     *\n     */\n    static getChainIdV(chainId: BigNumberish, v: 27 | 28): bigint {\n        return (getBigInt(chainId) * BN_2) + BigInt(35 + v - 27);\n    }\n\n    /**\n     *  Compute the normalized legacy transaction ``v`` from a ``yParirty``,\n     *  a legacy transaction ``v`` or a legacy [[link-eip-155]] transaction.\n     *\n     *  @example:\n     *    // The values 0 and 1 imply v is actually yParity\n     *    Signature.getNormalizedV(0)\n     *    //_result:\n     *\n     *    // Legacy non-EIP-1559 transaction (i.e. 27 or 28)\n     *    Signature.getNormalizedV(27)\n     *    //_result:\n     *\n     *    // Legacy EIP-155 transaction (i.e. >= 35)\n     *    Signature.getNormalizedV(46)\n     *    //_result:\n     *\n     *    // Invalid values throw\n     *    Signature.getNormalizedV(5)\n     *    //_error:\n     */\n    static getNormalizedV(v: BigNumberish): 27 | 28 {\n        const bv = getBigInt(v);\n\n        if (bv === BN_0 || bv === BN_27) { return 27; }\n        if (bv === BN_1 || bv === BN_28) { return 28; }\n\n        assertArgument(bv >= BN_35, \"invalid v\", \"v\", v);\n\n        // Otherwise, EIP-155 v means odd is 27 and even is 28\n        return (bv & BN_1) ? 27: 28;\n    }\n\n    /**\n     *  Creates a new [[Signature]].\n     *\n     *  If no %%sig%% is provided, a new [[Signature]] is created\n     *  with default values.\n     *\n     *  If %%sig%% is a string, it is parsed.\n     */\n    static from(sig?: SignatureLike): Signature {\n        function assertError(check: unknown, message: string): asserts check {\n            assertArgument(check, message, \"signature\", sig);\n        };\n\n        if (sig == null) {\n            return new Signature(_guard, ZeroHash, ZeroHash, 27);\n        }\n\n        if (typeof(sig) === \"string\") {\n            const bytes = getBytes(sig, \"signature\");\n            if (bytes.length === 64) {\n                const r = hexlify(bytes.slice(0, 32));\n                const s = bytes.slice(32, 64);\n                const v = (s[0] & 0x80) ? 28: 27;\n                s[0] &= 0x7f;\n                return new Signature(_guard, r, hexlify(s), v);\n            }\n\n            if (bytes.length === 65) {\n                const r = hexlify(bytes.slice(0, 32));\n                const s = bytes.slice(32, 64);\n                assertError((s[0] & 0x80) === 0, \"non-canonical s\");\n                const v = Signature.getNormalizedV(bytes[64]);\n                return new Signature(_guard, r, hexlify(s), v);\n            }\n\n            assertError(false, \"invalid raw signature length\");\n        }\n\n        if (sig instanceof Signature) { return sig.clone(); }\n\n        // Get r\n        const _r = sig.r;\n        assertError(_r != null, \"missing r\");\n        const r = toUint256(_r);\n\n        // Get s; by any means necessary (we check consistency below)\n        const s = (function(s?: string, yParityAndS?: string) {\n            if (s != null) { return toUint256(s); }\n\n            if (yParityAndS != null) {\n                assertError(isHexString(yParityAndS, 32), \"invalid yParityAndS\");\n                const bytes = getBytes(yParityAndS);\n                bytes[0] &= 0x7f;\n                return hexlify(bytes);\n            }\n\n            assertError(false, \"missing s\");\n        })(sig.s, sig.yParityAndS);\n        assertError((getBytes(s)[0] & 0x80) == 0, \"non-canonical s\");\n\n        // Get v; by any means necessary (we check consistency below)\n        const { networkV, v } = (function(_v?: BigNumberish, yParityAndS?: string, yParity?: Numeric): { networkV?: bigint, v: 27 | 28 } {\n            if (_v != null) {\n                const v = getBigInt(_v);\n                return {\n                    networkV: ((v >= BN_35) ? v: undefined),\n                    v: Signature.getNormalizedV(v)\n                };\n            }\n\n            if (yParityAndS != null) {\n                assertError(isHexString(yParityAndS, 32), \"invalid yParityAndS\");\n                return { v: ((getBytes(yParityAndS)[0] & 0x80) ? 28: 27) };\n            }\n\n            if (yParity != null) {\n                switch (getNumber(yParity, \"sig.yParity\")) {\n                    case 0: return { v: 27 };\n                    case 1: return { v: 28 };\n                }\n                assertError(false, \"invalid yParity\");\n            }\n\n            assertError(false, \"missing v\");\n        })(sig.v, sig.yParityAndS, sig.yParity);\n\n        const result = new Signature(_guard, r, s, v);\n        if (networkV) { result.#networkV =  networkV; }\n\n        // If multiple of v, yParity, yParityAndS we given, check they match\n        assertError(sig.yParity == null || getNumber(sig.yParity, \"sig.yParity\") === result.yParity, \"yParity mismatch\");\n        assertError(sig.yParityAndS == null || sig.yParityAndS === result.yParityAndS, \"yParityAndS mismatch\");\n\n        return result;\n    }\n}\n\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,SACIC,MAAM,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAC3DC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EACpCC,cAAc,EAAEC,aAAa,QAC1B,mBAAmB;AAO1B;AACA,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AACtB,MAAME,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMG,KAAK,GAAGH,MAAM,CAAC,EAAE,CAAC;AACxB,MAAMI,KAAK,GAAGJ,MAAM,CAAC,EAAE,CAAC;AACxB,MAAMK,KAAK,GAAGL,MAAM,CAAC,EAAE,CAAC;AAGxB,MAAMM,MAAM,GAAG,EAAG;AA6BlB,SAASC,SAASA,CAACC,KAAmB;EAClC,OAAOZ,YAAY,CAACF,SAAS,CAACc,KAAK,CAAC,EAAE,EAAE,CAAC;AAC7C;AAEA;;;;;;AAMA,OAAM,MAAOC,SAAS;EAClB,CAAAC,CAAE;EACF,CAAAC,CAAE;EACF,CAAAC,CAAE;EACF,CAAAC,QAAS;EAET;;;;;;EAMA,IAAIH,CAACA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,CAAE;EAAE;EAClC,IAAIA,CAACA,CAACF,KAAgB;IAClBX,cAAc,CAACR,UAAU,CAACmB,KAAK,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,EAAEA,KAAK,CAAC;IACrE,IAAI,CAAC,CAAAE,CAAE,GAAGjB,OAAO,CAACe,KAAK,CAAC;EAC5B;EAEA;;;EAGA,IAAIG,CAACA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,CAAE;EAAE;EAClC,IAAIA,CAACA,CAACG,MAAiB;IACnBjB,cAAc,CAACR,UAAU,CAACyB,MAAM,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,OAAO,EAAEA,MAAM,CAAC;IACvE,MAAMN,KAAK,GAAGf,OAAO,CAACqB,MAAM,CAAC;IAC7BjB,cAAc,CAACkB,QAAQ,CAACP,KAAK,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,iBAAiB,EAAE,OAAO,EAAER,KAAK,CAAC;IACtF,IAAI,CAAC,CAAAG,CAAE,GAAGH,KAAK;EACnB;EAEA;;;;;;;;;;EAUA,IAAII,CAACA,CAAA;IAAc,OAAO,IAAI,CAAC,CAAAA,CAAE;EAAE;EACnC,IAAIA,CAACA,CAACJ,KAAmB;IACrB,MAAMI,CAAC,GAAGpB,SAAS,CAACgB,KAAK,EAAE,OAAO,CAAC;IACnCX,cAAc,CAACe,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,EAAE,EAAE,WAAW,EAAE,GAAG,EAAEJ,KAAK,CAAC;IAC7D,IAAI,CAAC,CAAAI,CAAE,GAAGA,CAAC;EACf;EAEA;;;;EAIA,IAAIC,QAAQA,CAAA;IAAoB,OAAO,IAAI,CAAC,CAAAA,QAAS;EAAE;EAEvD;;;;EAIA,IAAII,aAAaA,CAAA;IACb,MAAML,CAAC,GAAG,IAAI,CAACC,QAAQ;IACvB,IAAID,CAAC,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAC5B,OAAOH,SAAS,CAACS,UAAU,CAACN,CAAC,CAAC;EAClC;EAEA;;;;;EAKA,IAAIO,OAAOA,CAAA;IACP,OAAQ,IAAI,CAACP,CAAC,KAAK,EAAE,GAAI,CAAC,GAAE,CAAC;EACjC;EAEA;;;;EAIA,IAAIQ,WAAWA,CAAA;IACX;IACA,MAAMA,WAAW,GAAG7B,QAAQ,CAAC,IAAI,CAACoB,CAAC,CAAC;IACpC,IAAI,IAAI,CAACQ,OAAO,EAAE;MAAEC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;;IAC1C,OAAO3B,OAAO,CAAC2B,WAAW,CAAC;EAC/B;EAEA;;;EAGA,IAAIC,iBAAiBA,CAAA;IACjB,OAAOjC,MAAM,CAAC,CAAE,IAAI,CAACsB,CAAC,EAAE,IAAI,CAACU,WAAW,CAAE,CAAC;EAC/C;EAEA;;;EAGA,IAAIE,UAAUA,CAAA;IACV,OAAOlC,MAAM,CAAC,CAAE,IAAI,CAACsB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAG,IAAI,CAACQ,OAAO,GAAG,MAAM,GAAE,MAAM,CAAG,CAAC;EACtE;EAEA;;;EAGAI,YAAYC,KAAU,EAAEd,CAAS,EAAEC,CAAS,EAAEC,CAAU;IACpDd,aAAa,CAAC0B,KAAK,EAAElB,MAAM,EAAE,WAAW,CAAC;IACzC,IAAI,CAAC,CAAAI,CAAE,GAAGA,CAAC;IACX,IAAI,CAAC,CAAAC,CAAE,GAAGA,CAAC;IACX,IAAI,CAAC,CAAAC,CAAE,GAAGA,CAAC;IACX,IAAI,CAAC,CAAAC,QAAS,GAAG,IAAI;EACzB;EAEA,CAACY,MAAM,CAACC,GAAG,CAAC,4BAA4B,CAAC,IAAC;IACtC,OAAO,mBAAoB,IAAI,CAAChB,CAAE,UAAW,IAAI,CAACC,CAAE,eAAgB,IAAI,CAACQ,OAAQ,eAAgB,IAAI,CAACN,QAAS,IAAI;EACvH;EAEA;;;EAGAc,KAAKA,CAAA;IACD,MAAMA,KAAK,GAAG,IAAIlB,SAAS,CAACH,MAAM,EAAE,IAAI,CAACI,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC3D,IAAI,IAAI,CAACC,QAAQ,EAAE;MAAEc,KAAK,CAAC,CAAAd,QAAS,GAAG,IAAI,CAACA,QAAQ;;IACpD,OAAOc,KAAK;EAChB;EAEA;;;EAGAC,MAAMA,CAAA;IACF,MAAMf,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,OAAO;MACHgB,KAAK,EAAE,WAAW;MAClBhB,QAAQ,EAAIA,QAAQ,IAAI,IAAI,GAAIA,QAAQ,CAACiB,QAAQ,EAAE,GAAE,IAAK;MAC1DpB,CAAC,EAAE,IAAI,CAACA,CAAC;MAAEC,CAAC,EAAE,IAAI,CAACA,CAAC;MAAEC,CAAC,EAAE,IAAI,CAACA;KACjC;EACL;EAEA;;;;;;;;;;EAUA,OAAOM,UAAUA,CAACN,CAAe;IAC7B,MAAMmB,EAAE,GAAGzC,SAAS,CAACsB,CAAC,EAAE,GAAG,CAAC;IAE5B;IACA,IAAKmB,EAAE,IAAI5B,KAAK,IAAM4B,EAAE,IAAI3B,KAAM,EAAE;MAAE,OAAOL,IAAI;;IAEjD;IACAF,cAAc,CAACkC,EAAE,IAAI1B,KAAK,EAAE,mBAAmB,EAAE,GAAG,EAAEO,CAAC,CAAC;IAExD,OAAO,CAACmB,EAAE,GAAG1B,KAAK,IAAIH,IAAI;EAC9B;EAEA;;;;;;;;;;;;;;EAcA,OAAO8B,WAAWA,CAACC,OAAqB,EAAErB,CAAU;IAChD,OAAQtB,SAAS,CAAC2C,OAAO,CAAC,GAAG/B,IAAI,GAAIF,MAAM,CAAC,EAAE,GAAGY,CAAC,GAAG,EAAE,CAAC;EAC5D;EAEA;;;;;;;;;;;;;;;;;;;;;EAqBA,OAAOsB,cAAcA,CAACtB,CAAe;IACjC,MAAMmB,EAAE,GAAGzC,SAAS,CAACsB,CAAC,CAAC;IAEvB,IAAImB,EAAE,KAAKhC,IAAI,IAAIgC,EAAE,KAAK5B,KAAK,EAAE;MAAE,OAAO,EAAE;;IAC5C,IAAI4B,EAAE,KAAK9B,IAAI,IAAI8B,EAAE,KAAK3B,KAAK,EAAE;MAAE,OAAO,EAAE;;IAE5CP,cAAc,CAACkC,EAAE,IAAI1B,KAAK,EAAE,WAAW,EAAE,GAAG,EAAEO,CAAC,CAAC;IAEhD;IACA,OAAQmB,EAAE,GAAG9B,IAAI,GAAI,EAAE,GAAE,EAAE;EAC/B;EAEA;;;;;;;;EAQA,OAAOkC,IAAIA,CAACC,GAAmB;IAC3B,SAASC,WAAWA,CAACC,KAAc,EAAEC,OAAe;MAChD1C,cAAc,CAACyC,KAAK,EAAEC,OAAO,EAAE,WAAW,EAAEH,GAAG,CAAC;IACpD;IAAC;IAED,IAAIA,GAAG,IAAI,IAAI,EAAE;MACb,OAAO,IAAI3B,SAAS,CAACH,MAAM,EAAEnB,QAAQ,EAAEA,QAAQ,EAAE,EAAE,CAAC;;IAGxD,IAAI,OAAOiD,GAAI,KAAK,QAAQ,EAAE;MAC1B,MAAMI,KAAK,GAAGjD,QAAQ,CAAC6C,GAAG,EAAE,WAAW,CAAC;MACxC,IAAII,KAAK,CAACC,MAAM,KAAK,EAAE,EAAE;QACrB,MAAM/B,CAAC,GAAGjB,OAAO,CAAC+C,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM/B,CAAC,GAAG6B,KAAK,CAACE,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;QAC7B,MAAM9B,CAAC,GAAID,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,EAAE,GAAE,EAAE;QAChCA,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;QACZ,OAAO,IAAIF,SAAS,CAACH,MAAM,EAAEI,CAAC,EAAEjB,OAAO,CAACkB,CAAC,CAAC,EAAEC,CAAC,CAAC;;MAGlD,IAAI4B,KAAK,CAACC,MAAM,KAAK,EAAE,EAAE;QACrB,MAAM/B,CAAC,GAAGjB,OAAO,CAAC+C,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM/B,CAAC,GAAG6B,KAAK,CAACE,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;QAC7BL,WAAW,CAAC,CAAC1B,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE,iBAAiB,CAAC;QACnD,MAAMC,CAAC,GAAGH,SAAS,CAACyB,cAAc,CAACM,KAAK,CAAC,EAAE,CAAC,CAAC;QAC7C,OAAO,IAAI/B,SAAS,CAACH,MAAM,EAAEI,CAAC,EAAEjB,OAAO,CAACkB,CAAC,CAAC,EAAEC,CAAC,CAAC;;MAGlDyB,WAAW,CAAC,KAAK,EAAE,8BAA8B,CAAC;;IAGtD,IAAID,GAAG,YAAY3B,SAAS,EAAE;MAAE,OAAO2B,GAAG,CAACT,KAAK,EAAE;;IAElD;IACA,MAAMgB,EAAE,GAAGP,GAAG,CAAC1B,CAAC;IAChB2B,WAAW,CAACM,EAAE,IAAI,IAAI,EAAE,WAAW,CAAC;IACpC,MAAMjC,CAAC,GAAGH,SAAS,CAACoC,EAAE,CAAC;IAEvB;IACA,MAAMhC,CAAC,GAAI,UAASA,CAAU,EAAES,WAAoB;MAChD,IAAIT,CAAC,IAAI,IAAI,EAAE;QAAE,OAAOJ,SAAS,CAACI,CAAC,CAAC;;MAEpC,IAAIS,WAAW,IAAI,IAAI,EAAE;QACrBiB,WAAW,CAAC1C,WAAW,CAACyB,WAAW,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC;QAChE,MAAMoB,KAAK,GAAGjD,QAAQ,CAAC6B,WAAW,CAAC;QACnCoB,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;QAChB,OAAO/C,OAAO,CAAC+C,KAAK,CAAC;;MAGzBH,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC;IACnC,CAAC,CAAED,GAAG,CAACzB,CAAC,EAAEyB,GAAG,CAAChB,WAAW,CAAC;IAC1BiB,WAAW,CAAC,CAAC9C,QAAQ,CAACoB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE,iBAAiB,CAAC;IAE5D;IACA,MAAM;MAAEE,QAAQ;MAAED;IAAC,CAAE,GAAI,UAASgC,EAAiB,EAAExB,WAAoB,EAAED,OAAiB;MACxF,IAAIyB,EAAE,IAAI,IAAI,EAAE;QACZ,MAAMhC,CAAC,GAAGtB,SAAS,CAACsD,EAAE,CAAC;QACvB,OAAO;UACH/B,QAAQ,EAAID,CAAC,IAAIP,KAAK,GAAIO,CAAC,GAAEiC,SAAU;UACvCjC,CAAC,EAAEH,SAAS,CAACyB,cAAc,CAACtB,CAAC;SAChC;;MAGL,IAAIQ,WAAW,IAAI,IAAI,EAAE;QACrBiB,WAAW,CAAC1C,WAAW,CAACyB,WAAW,EAAE,EAAE,CAAC,EAAE,qBAAqB,CAAC;QAChE,OAAO;UAAER,CAAC,EAAIrB,QAAQ,CAAC6B,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,EAAE,GAAE;QAAG,CAAE;;MAG9D,IAAID,OAAO,IAAI,IAAI,EAAE;QACjB,QAAQ3B,SAAS,CAAC2B,OAAO,EAAE,aAAa,CAAC;UACrC,KAAK,CAAC;YAAE,OAAO;cAAEP,CAAC,EAAE;YAAE,CAAE;UACxB,KAAK,CAAC;YAAE,OAAO;cAAEA,CAAC,EAAE;YAAE,CAAE;;QAE5ByB,WAAW,CAAC,KAAK,EAAE,iBAAiB,CAAC;;MAGzCA,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC;IACnC,CAAC,CAAED,GAAG,CAACxB,CAAC,EAAEwB,GAAG,CAAChB,WAAW,EAAEgB,GAAG,CAACjB,OAAO,CAAC;IAEvC,MAAM2B,MAAM,GAAG,IAAIrC,SAAS,CAACH,MAAM,EAAEI,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;IAC7C,IAAIC,QAAQ,EAAE;MAAEiC,MAAM,CAAC,CAAAjC,QAAS,GAAIA,QAAQ;;IAE5C;IACAwB,WAAW,CAACD,GAAG,CAACjB,OAAO,IAAI,IAAI,IAAI3B,SAAS,CAAC4C,GAAG,CAACjB,OAAO,EAAE,aAAa,CAAC,KAAK2B,MAAM,CAAC3B,OAAO,EAAE,kBAAkB,CAAC;IAChHkB,WAAW,CAACD,GAAG,CAAChB,WAAW,IAAI,IAAI,IAAIgB,GAAG,CAAChB,WAAW,KAAK0B,MAAM,CAAC1B,WAAW,EAAE,sBAAsB,CAAC;IAEtG,OAAO0B,MAAM;EACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}