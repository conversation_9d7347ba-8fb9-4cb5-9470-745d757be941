{"ast": null, "code": "import { keccak256 } from \"../crypto/index.js\";\nimport { concat, hexlify, assertArgument, toUtf8Bytes } from \"../utils/index.js\";\nimport { ens_normalize } from \"@adraffy/ens-normalize\";\nconst Zeros = new Uint8Array(32);\nZeros.fill(0);\nfunction checkComponent(comp) {\n  assertArgument(comp.length !== 0, \"invalid ENS name; empty component\", \"comp\", comp);\n  return comp;\n}\nfunction ensNameSplit(name) {\n  const bytes = toUtf8Bytes(ensNormalize(name));\n  const comps = [];\n  if (name.length === 0) {\n    return comps;\n  }\n  let last = 0;\n  for (let i = 0; i < bytes.length; i++) {\n    const d = bytes[i];\n    // A separator (i.e. \".\"); copy this component\n    if (d === 0x2e) {\n      comps.push(checkComponent(bytes.slice(last, i)));\n      last = i + 1;\n    }\n  }\n  // There was a stray separator at the end of the name\n  assertArgument(last < bytes.length, \"invalid ENS name; empty component\", \"name\", name);\n  comps.push(checkComponent(bytes.slice(last)));\n  return comps;\n}\n/**\n *  Returns the ENS %%name%% normalized.\n */\nexport function ensNormalize(name) {\n  try {\n    if (name.length === 0) {\n      throw new Error(\"empty label\");\n    }\n    return ens_normalize(name);\n  } catch (error) {\n    assertArgument(false, `invalid ENS name (${error.message})`, \"name\", name);\n  }\n}\n/**\n *  Returns ``true`` if %%name%% is a valid ENS name.\n */\nexport function isValidName(name) {\n  try {\n    return ensNameSplit(name).length !== 0;\n  } catch (error) {}\n  return false;\n}\n/**\n *  Returns the [[link-namehash]] for %%name%%.\n */\nexport function namehash(name) {\n  assertArgument(typeof name === \"string\", \"invalid ENS name; not a string\", \"name\", name);\n  assertArgument(name.length, `invalid ENS name (empty label)`, \"name\", name);\n  let result = Zeros;\n  const comps = ensNameSplit(name);\n  while (comps.length) {\n    result = keccak256(concat([result, keccak256(comps.pop())]));\n  }\n  return hexlify(result);\n}\n/**\n *  Returns the DNS encoded %%name%%.\n *\n *  This is used for various parts of ENS name resolution, such\n *  as the wildcard resolution.\n */\nexport function dnsEncode(name, _maxLength) {\n  const length = _maxLength != null ? _maxLength : 63;\n  assertArgument(length <= 255, \"DNS encoded label cannot exceed 255\", \"length\", length);\n  return hexlify(concat(ensNameSplit(name).map(comp => {\n    assertArgument(comp.length <= length, `label ${JSON.stringify(name)} exceeds ${length} bytes`, \"name\", name);\n    const bytes = new Uint8Array(comp.length + 1);\n    bytes.set(comp, 1);\n    bytes[0] = bytes.length - 1;\n    return bytes;\n  }))) + \"00\";\n}", "map": {"version": 3, "names": ["keccak256", "concat", "hexlify", "assertArgument", "toUtf8Bytes", "ens_normalize", "Zeros", "Uint8Array", "fill", "checkComponent", "comp", "length", "ensNameSplit", "name", "bytes", "ensNormalize", "comps", "last", "i", "d", "push", "slice", "Error", "error", "message", "isValidName", "<PERSON><PERSON><PERSON>", "result", "pop", "dnsEncode", "_maxLength", "map", "JSON", "stringify", "set"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\namehash.ts"], "sourcesContent": ["\nimport { keccak256 } from \"../crypto/index.js\";\nimport {\n    concat, hexlify, assertArgument, toUtf8Bytes\n} from \"../utils/index.js\";\n\n\nimport { ens_normalize } from \"@adraffy/ens-normalize\";\n\nconst Zeros = new Uint8Array(32);\nZeros.fill(0);\n\nfunction checkComponent(comp: Uint8Array): Uint8Array {\n    assertArgument(comp.length !== 0, \"invalid ENS name; empty component\", \"comp\", comp)\n    return comp;\n}\n\nfunction ensNameSplit(name: string): Array<Uint8Array> {\n    const bytes = toUtf8Bytes(ensNormalize(name));\n    const comps: Array<Uint8Array> = [ ];\n\n    if (name.length === 0) { return comps; }\n\n    let last = 0;\n    for (let i = 0; i < bytes.length; i++) {\n        const d = bytes[i];\n\n        // A separator (i.e. \".\"); copy this component\n        if (d === 0x2e) {\n            comps.push(checkComponent(bytes.slice(last, i)));\n            last = i + 1;\n        }\n    }\n\n    // There was a stray separator at the end of the name\n    assertArgument(last < bytes.length, \"invalid ENS name; empty component\", \"name\", name);\n\n    comps.push(checkComponent(bytes.slice(last)));\n    return comps;\n}\n\n/**\n *  Returns the ENS %%name%% normalized.\n */\nexport function ensNormalize(name: string): string {\n    try {\n        if (name.length === 0) { throw new Error(\"empty label\"); }\n        return ens_normalize(name);\n    } catch (error: any) {\n        assertArgument(false, `invalid ENS name (${ error.message })`, \"name\", name);\n    }\n}\n\n/**\n *  Returns ``true`` if %%name%% is a valid ENS name.\n */\nexport function isValidName(name: string): name is string {\n    try {\n        return (ensNameSplit(name).length !== 0);\n    } catch (error) { }\n    return false;\n}\n\n/**\n *  Returns the [[link-namehash]] for %%name%%.\n */\nexport function namehash(name: string): string {\n    assertArgument(typeof(name) === \"string\", \"invalid ENS name; not a string\", \"name\", name);\n\n    assertArgument(name.length, `invalid ENS name (empty label)`, \"name\", name);\n\n    let result: string | Uint8Array = Zeros;\n\n    const comps = ensNameSplit(name);\n    while (comps.length) {\n        result = keccak256(concat([ result, keccak256(<Uint8Array>(comps.pop()))] ));\n    }\n\n    return hexlify(result);\n}\n\n/**\n *  Returns the DNS encoded %%name%%.\n *\n *  This is used for various parts of ENS name resolution, such\n *  as the wildcard resolution.\n */\nexport function dnsEncode(name: string, _maxLength?: number): string {\n    const length = (_maxLength != null) ? _maxLength: 63;\n\n    assertArgument(length <= 255, \"DNS encoded label cannot exceed 255\", \"length\", length);\n\n    return hexlify(concat(ensNameSplit(name).map((comp) => {\n        assertArgument(comp.length <= length, `label ${ JSON.stringify(name) } exceeds ${ length } bytes`, \"name\", name);\n\n        const bytes = new Uint8Array(comp.length + 1);\n        bytes.set(comp, 1);\n        bytes[0] = bytes.length - 1;\n        return bytes;\n    }))) + \"00\";\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SACIC,MAAM,EAAEC,OAAO,EAAEC,cAAc,EAAEC,WAAW,QACzC,mBAAmB;AAG1B,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;AAChCD,KAAK,CAACE,IAAI,CAAC,CAAC,CAAC;AAEb,SAASC,cAAcA,CAACC,IAAgB;EACpCP,cAAc,CAACO,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE,mCAAmC,EAAE,MAAM,EAAED,IAAI,CAAC;EACpF,OAAOA,IAAI;AACf;AAEA,SAASE,YAAYA,CAACC,IAAY;EAC9B,MAAMC,KAAK,GAAGV,WAAW,CAACW,YAAY,CAACF,IAAI,CAAC,CAAC;EAC7C,MAAMG,KAAK,GAAsB,EAAG;EAEpC,IAAIH,IAAI,CAACF,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOK,KAAK;;EAErC,IAAIC,IAAI,GAAG,CAAC;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACH,MAAM,EAAEO,CAAC,EAAE,EAAE;IACnC,MAAMC,CAAC,GAAGL,KAAK,CAACI,CAAC,CAAC;IAElB;IACA,IAAIC,CAAC,KAAK,IAAI,EAAE;MACZH,KAAK,CAACI,IAAI,CAACX,cAAc,CAACK,KAAK,CAACO,KAAK,CAACJ,IAAI,EAAEC,CAAC,CAAC,CAAC,CAAC;MAChDD,IAAI,GAAGC,CAAC,GAAG,CAAC;;;EAIpB;EACAf,cAAc,CAACc,IAAI,GAAGH,KAAK,CAACH,MAAM,EAAE,mCAAmC,EAAE,MAAM,EAAEE,IAAI,CAAC;EAEtFG,KAAK,CAACI,IAAI,CAACX,cAAc,CAACK,KAAK,CAACO,KAAK,CAACJ,IAAI,CAAC,CAAC,CAAC;EAC7C,OAAOD,KAAK;AAChB;AAEA;;;AAGA,OAAM,SAAUD,YAAYA,CAACF,IAAY;EACrC,IAAI;IACA,IAAIA,IAAI,CAACF,MAAM,KAAK,CAAC,EAAE;MAAE,MAAM,IAAIW,KAAK,CAAC,aAAa,CAAC;;IACvD,OAAOjB,aAAa,CAACQ,IAAI,CAAC;GAC7B,CAAC,OAAOU,KAAU,EAAE;IACjBpB,cAAc,CAAC,KAAK,EAAE,qBAAsBoB,KAAK,CAACC,OAAQ,GAAG,EAAE,MAAM,EAAEX,IAAI,CAAC;;AAEpF;AAEA;;;AAGA,OAAM,SAAUY,WAAWA,CAACZ,IAAY;EACpC,IAAI;IACA,OAAQD,YAAY,CAACC,IAAI,CAAC,CAACF,MAAM,KAAK,CAAC;GAC1C,CAAC,OAAOY,KAAK,EAAE;EAChB,OAAO,KAAK;AAChB;AAEA;;;AAGA,OAAM,SAAUG,QAAQA,CAACb,IAAY;EACjCV,cAAc,CAAC,OAAOU,IAAK,KAAK,QAAQ,EAAE,gCAAgC,EAAE,MAAM,EAAEA,IAAI,CAAC;EAEzFV,cAAc,CAACU,IAAI,CAACF,MAAM,EAAE,gCAAgC,EAAE,MAAM,EAAEE,IAAI,CAAC;EAE3E,IAAIc,MAAM,GAAwBrB,KAAK;EAEvC,MAAMU,KAAK,GAAGJ,YAAY,CAACC,IAAI,CAAC;EAChC,OAAOG,KAAK,CAACL,MAAM,EAAE;IACjBgB,MAAM,GAAG3B,SAAS,CAACC,MAAM,CAAC,CAAE0B,MAAM,EAAE3B,SAAS,CAAcgB,KAAK,CAACY,GAAG,EAAG,CAAC,CAAC,CAAE,CAAC;;EAGhF,OAAO1B,OAAO,CAACyB,MAAM,CAAC;AAC1B;AAEA;;;;;;AAMA,OAAM,SAAUE,SAASA,CAAChB,IAAY,EAAEiB,UAAmB;EACvD,MAAMnB,MAAM,GAAImB,UAAU,IAAI,IAAI,GAAIA,UAAU,GAAE,EAAE;EAEpD3B,cAAc,CAACQ,MAAM,IAAI,GAAG,EAAE,qCAAqC,EAAE,QAAQ,EAAEA,MAAM,CAAC;EAEtF,OAAOT,OAAO,CAACD,MAAM,CAACW,YAAY,CAACC,IAAI,CAAC,CAACkB,GAAG,CAAErB,IAAI,IAAI;IAClDP,cAAc,CAACO,IAAI,CAACC,MAAM,IAAIA,MAAM,EAAE,SAAUqB,IAAI,CAACC,SAAS,CAACpB,IAAI,CAAE,YAAaF,MAAO,QAAQ,EAAE,MAAM,EAAEE,IAAI,CAAC;IAEhH,MAAMC,KAAK,GAAG,IAAIP,UAAU,CAACG,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;IAC7CG,KAAK,CAACoB,GAAG,CAACxB,IAAI,EAAE,CAAC,CAAC;IAClBI,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACH,MAAM,GAAG,CAAC;IAC3B,OAAOG,KAAK;EAChB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}