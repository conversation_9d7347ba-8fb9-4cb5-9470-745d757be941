{"ast": null, "code": "import { defineProperties } from \"../utils/index.js\";\nimport { AbstractSigner } from \"./abstract-signer.js\";\n/**\n *  A **NonceManager** wraps another [[Signer]] and automatically manages\n *  the nonce, ensuring serialized and sequential nonces are used during\n *  transaction.\n */\nexport class NonceManager extends AbstractSigner {\n  /**\n   *  The Signer being managed.\n   */\n  signer;\n  #noncePromise;\n  #delta;\n  /**\n   *  Creates a new **NonceManager** to manage %%signer%%.\n   */\n  constructor(signer) {\n    super(signer.provider);\n    defineProperties(this, {\n      signer\n    });\n    this.#noncePromise = null;\n    this.#delta = 0;\n  }\n  async getAddress() {\n    return this.signer.getAddress();\n  }\n  connect(provider) {\n    return new NonceManager(this.signer.connect(provider));\n  }\n  async getNonce(blockTag) {\n    if (blockTag === \"pending\") {\n      if (this.#noncePromise == null) {\n        this.#noncePromise = super.getNonce(\"pending\");\n      }\n      const delta = this.#delta;\n      return (await this.#noncePromise) + delta;\n    }\n    return super.getNonce(blockTag);\n  }\n  /**\n   *  Manually increment the nonce. This may be useful when managng\n   *  offline transactions.\n   */\n  increment() {\n    this.#delta++;\n  }\n  /**\n   *  Resets the nonce, causing the **NonceManager** to reload the current\n   *  nonce from the blockchain on the next transaction.\n   */\n  reset() {\n    this.#delta = 0;\n    this.#noncePromise = null;\n  }\n  async sendTransaction(tx) {\n    const noncePromise = this.getNonce(\"pending\");\n    this.increment();\n    tx = await this.signer.populateTransaction(tx);\n    tx.nonce = await noncePromise;\n    // @TODO: Maybe handle interesting/recoverable errors?\n    // Like don't increment if the tx was certainly not sent\n    return await this.signer.sendTransaction(tx);\n  }\n  signTransaction(tx) {\n    return this.signer.signTransaction(tx);\n  }\n  signMessage(message) {\n    return this.signer.signMessage(message);\n  }\n  signTypedData(domain, types, value) {\n    return this.signer.signTypedData(domain, types, value);\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "Abstract<PERSON><PERSON><PERSON>", "<PERSON>ceManager", "signer", "noncePromise", "delta", "constructor", "provider", "get<PERSON><PERSON><PERSON>", "connect", "getNonce", "blockTag", "increment", "reset", "sendTransaction", "tx", "populateTransaction", "nonce", "signTransaction", "signMessage", "message", "signTypedData", "domain", "types", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\signer-noncemanager.ts"], "sourcesContent": ["import { defineProperties } from \"../utils/index.js\";\nimport { AbstractSigner } from \"./abstract-signer.js\";\n\nimport type { TypedDataDomain, TypedDataField } from \"../hash/index.js\";\n\nimport type {\n    BlockTag, Provider, TransactionRequest, TransactionResponse\n} from \"./provider.js\";\nimport type { Signer } from \"./signer.js\";\n\n\n/**\n *  A **NonceManager** wraps another [[Signer]] and automatically manages\n *  the nonce, ensuring serialized and sequential nonces are used during\n *  transaction.\n */\nexport class NonceManager extends AbstractSigner {\n    /**\n     *  The Signer being managed.\n     */\n    signer!: Signer;\n\n    #noncePromise: null | Promise<number>;\n    #delta: number;\n\n    /**\n     *  Creates a new **NonceManager** to manage %%signer%%.\n     */\n    constructor(signer: Signer) {\n        super(signer.provider);\n        defineProperties<NonceManager>(this, { signer });\n\n        this.#noncePromise = null;\n        this.#delta = 0;\n    }\n\n    async getAddress(): Promise<string> {\n        return this.signer.getAddress();\n    }\n\n    connect(provider: null | Provider): NonceManager {\n        return new NonceManager(this.signer.connect(provider));\n    }\n\n    async getNonce(blockTag?: BlockTag): Promise<number> {\n        if (blockTag === \"pending\") {\n            if (this.#noncePromise == null) {\n                this.#noncePromise = super.getNonce(\"pending\");\n            }\n\n            const delta = this.#delta;\n            return (await this.#noncePromise) + delta;\n        }\n\n        return super.getNonce(blockTag);\n    }\n\n    /**\n     *  Manually increment the nonce. This may be useful when managng\n     *  offline transactions.\n     */\n    increment(): void {\n        this.#delta++;\n    }\n\n    /**\n     *  Resets the nonce, causing the **NonceManager** to reload the current\n     *  nonce from the blockchain on the next transaction.\n     */\n    reset(): void {\n        this.#delta = 0;\n        this.#noncePromise = null;\n    }\n\n    async sendTransaction(tx: TransactionRequest): Promise<TransactionResponse> {\n        const noncePromise = this.getNonce(\"pending\");\n        this.increment();\n\n        tx = await this.signer.populateTransaction(tx);\n        tx.nonce = await noncePromise;\n\n        // @TODO: Maybe handle interesting/recoverable errors?\n        // Like don't increment if the tx was certainly not sent\n        return await this.signer.sendTransaction(tx);\n    }\n\n    signTransaction(tx: TransactionRequest): Promise<string> {\n        return this.signer.signTransaction(tx);\n    }\n\n    signMessage(message: string | Uint8Array): Promise<string> {\n        return this.signer.signMessage(message);\n    }\n\n    signTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): Promise<string> {\n        return this.signer.signTypedData(domain, types, value);\n    }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mBAAmB;AACpD,SAASC,cAAc,QAAQ,sBAAsB;AAUrD;;;;;AAKA,OAAM,MAAOC,YAAa,SAAQD,cAAc;EAC5C;;;EAGAE,MAAM;EAEN,CAAAC,YAAa;EACb,CAAAC,KAAM;EAEN;;;EAGAC,YAAYH,MAAc;IACtB,KAAK,CAACA,MAAM,CAACI,QAAQ,CAAC;IACtBP,gBAAgB,CAAe,IAAI,EAAE;MAAEG;IAAM,CAAE,CAAC;IAEhD,IAAI,CAAC,CAAAC,YAAa,GAAG,IAAI;IACzB,IAAI,CAAC,CAAAC,KAAM,GAAG,CAAC;EACnB;EAEA,MAAMG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACL,MAAM,CAACK,UAAU,EAAE;EACnC;EAEAC,OAAOA,CAACF,QAAyB;IAC7B,OAAO,IAAIL,YAAY,CAAC,IAAI,CAACC,MAAM,CAACM,OAAO,CAACF,QAAQ,CAAC,CAAC;EAC1D;EAEA,MAAMG,QAAQA,CAACC,QAAmB;IAC9B,IAAIA,QAAQ,KAAK,SAAS,EAAE;MACxB,IAAI,IAAI,CAAC,CAAAP,YAAa,IAAI,IAAI,EAAE;QAC5B,IAAI,CAAC,CAAAA,YAAa,GAAG,KAAK,CAACM,QAAQ,CAAC,SAAS,CAAC;;MAGlD,MAAML,KAAK,GAAG,IAAI,CAAC,CAAAA,KAAM;MACzB,OAAO,CAAC,MAAM,IAAI,CAAC,CAAAD,YAAa,IAAIC,KAAK;;IAG7C,OAAO,KAAK,CAACK,QAAQ,CAACC,QAAQ,CAAC;EACnC;EAEA;;;;EAIAC,SAASA,CAAA;IACL,IAAI,CAAC,CAAAP,KAAM,EAAE;EACjB;EAEA;;;;EAIAQ,KAAKA,CAAA;IACD,IAAI,CAAC,CAAAR,KAAM,GAAG,CAAC;IACf,IAAI,CAAC,CAAAD,YAAa,GAAG,IAAI;EAC7B;EAEA,MAAMU,eAAeA,CAACC,EAAsB;IACxC,MAAMX,YAAY,GAAG,IAAI,CAACM,QAAQ,CAAC,SAAS,CAAC;IAC7C,IAAI,CAACE,SAAS,EAAE;IAEhBG,EAAE,GAAG,MAAM,IAAI,CAACZ,MAAM,CAACa,mBAAmB,CAACD,EAAE,CAAC;IAC9CA,EAAE,CAACE,KAAK,GAAG,MAAMb,YAAY;IAE7B;IACA;IACA,OAAO,MAAM,IAAI,CAACD,MAAM,CAACW,eAAe,CAACC,EAAE,CAAC;EAChD;EAEAG,eAAeA,CAACH,EAAsB;IAClC,OAAO,IAAI,CAACZ,MAAM,CAACe,eAAe,CAACH,EAAE,CAAC;EAC1C;EAEAI,WAAWA,CAACC,OAA4B;IACpC,OAAO,IAAI,CAACjB,MAAM,CAACgB,WAAW,CAACC,OAAO,CAAC;EAC3C;EAEAC,aAAaA,CAACC,MAAuB,EAAEC,KAA4C,EAAEC,KAA0B;IAC3G,OAAO,IAAI,CAACrB,MAAM,CAACkB,aAAa,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,CAAC;EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}