{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\components\\\\TokenBalance.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useWeb3 } from '../contexts/Web3Context';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CurrencyDollarIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TokenBalance = ({\n  showRefresh = true,\n  className = ''\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    getTokenBalance\n  } = useWeb3();\n  const [balance, setBalance] = useState('0');\n  const [loading, setLoading] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  useEffect(() => {\n    loadBalance();\n  }, [user === null || user === void 0 ? void 0 : user.walletAddress]);\n  const loadBalance = async () => {\n    if (!(user !== null && user !== void 0 && user.walletAddress)) return;\n    try {\n      setLoading(true);\n      const tokenBalance = await getTokenBalance(user.walletAddress);\n      setBalance(parseFloat(tokenBalance).toFixed(2));\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Failed to load token balance:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRefresh = () => {\n    loadBalance();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex items-center space-x-2 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-5 h-5 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(CurrencyDollarIcon, {\n          className: \"w-3 h-3 text-yellow-900\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"text-white font-bold text-sm\",\n          initial: {\n            scale: 1.2,\n            color: '#fbbf24'\n          },\n          animate: {\n            scale: 1,\n            color: '#ffffff'\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: [loading ? '...' : balance, \" CQT\"]\n        }, balance, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-dark-400\",\n          children: [\"Updated \", lastUpdated.toLocaleTimeString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), showRefresh && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleRefresh,\n      disabled: loading,\n      className: \"p-2 text-dark-400 hover:text-white transition-colors disabled:opacity-50\",\n      title: \"Refresh balance\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        animate: loading ? {\n          rotate: 360\n        } : {},\n        transition: loading ? {\n          duration: 1,\n          repeat: Infinity,\n          ease: 'linear'\n        } : {},\n        children: /*#__PURE__*/_jsxDEV(ArrowPathIcon, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(TokenBalance, \"ts7V5Sz5++FRJk663TS0r0rX0Io=\", false, function () {\n  return [useAuth, useWeb3];\n});\n_c = TokenBalance;\nexport default TokenBalance;\nvar _c;\n$RefreshReg$(_c, \"TokenBalance\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "useWeb3", "useAuth", "CurrencyDollarIcon", "ArrowPathIcon", "jsxDEV", "_jsxDEV", "TokenBalance", "showRefresh", "className", "_s", "user", "getTokenBalance", "balance", "setBalance", "loading", "setLoading", "lastUpdated", "setLastUpdated", "loadBalance", "wallet<PERSON>ddress", "tokenBalance", "parseFloat", "toFixed", "Date", "error", "console", "handleRefresh", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "span", "initial", "scale", "color", "animate", "transition", "duration", "toLocaleTimeString", "onClick", "disabled", "title", "div", "rotate", "repeat", "Infinity", "ease", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/components/TokenBalance.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { useWeb3 } from '../contexts/Web3Context';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CurrencyDollarIcon, ArrowPathIcon } from '@heroicons/react/24/outline';\n\nconst TokenBalance = ({ showRefresh = true, className = '' }) => {\n  const { user } = useAuth();\n  const { getTokenBalance } = useWeb3();\n  const [balance, setBalance] = useState('0');\n  const [loading, setLoading] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n\n  useEffect(() => {\n    loadBalance();\n  }, [user?.walletAddress]);\n\n  const loadBalance = async () => {\n    if (!user?.walletAddress) return;\n    \n    try {\n      setLoading(true);\n      const tokenBalance = await getTokenBalance(user.walletAddress);\n      setBalance(parseFloat(tokenBalance).toFixed(2));\n      setLastUpdated(new Date());\n    } catch (error) {\n      console.error('Failed to load token balance:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefresh = () => {\n    loadBalance();\n  };\n\n  return (\n    <div className={`flex items-center space-x-2 ${className}`}>\n      <div className=\"flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600\">\n        <div className=\"w-5 h-5 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center\">\n          <CurrencyDollarIcon className=\"w-3 h-3 text-yellow-900\" />\n        </div>\n        \n        <div className=\"flex flex-col\">\n          <motion.span\n            key={balance}\n            className=\"text-white font-bold text-sm\"\n            initial={{ scale: 1.2, color: '#fbbf24' }}\n            animate={{ scale: 1, color: '#ffffff' }}\n            transition={{ duration: 0.3 }}\n          >\n            {loading ? '...' : balance} CQT\n          </motion.span>\n          {lastUpdated && (\n            <span className=\"text-xs text-dark-400\">\n              Updated {lastUpdated.toLocaleTimeString()}\n            </span>\n          )}\n        </div>\n      </div>\n\n      {showRefresh && (\n        <button\n          onClick={handleRefresh}\n          disabled={loading}\n          className=\"p-2 text-dark-400 hover:text-white transition-colors disabled:opacity-50\"\n          title=\"Refresh balance\"\n        >\n          <motion.div\n            animate={loading ? { rotate: 360 } : {}}\n            transition={loading ? { duration: 1, repeat: Infinity, ease: 'linear' } : {}}\n          >\n            <ArrowPathIcon className=\"w-4 h-4\" />\n          </motion.div>\n        </button>\n      )}\n    </div>\n  );\n};\n\nexport default TokenBalance;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,kBAAkB,EAAEC,aAAa,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhF,MAAMC,YAAY,GAAGA,CAAC;EAAEC,WAAW,GAAG,IAAI;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC/D,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEU;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EACrC,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmB,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdoB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,aAAa,CAAC,CAAC;EAEzB,MAAMD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,EAACR,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAES,aAAa,GAAE;IAE1B,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMK,YAAY,GAAG,MAAMT,eAAe,CAACD,IAAI,CAACS,aAAa,CAAC;MAC9DN,UAAU,CAACQ,UAAU,CAACD,YAAY,CAAC,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;MAC/CL,cAAc,CAAC,IAAIM,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,aAAa,GAAGA,CAAA,KAAM;IAC1BR,WAAW,CAAC,CAAC;EACf,CAAC;EAED,oBACEb,OAAA;IAAKG,SAAS,EAAE,+BAA+BA,SAAS,EAAG;IAAAmB,QAAA,gBACzDtB,OAAA;MAAKG,SAAS,EAAC,qFAAqF;MAAAmB,QAAA,gBAClGtB,OAAA;QAAKG,SAAS,EAAC,sGAAsG;QAAAmB,QAAA,eACnHtB,OAAA,CAACH,kBAAkB;UAACM,SAAS,EAAC;QAAyB;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAEN1B,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAmB,QAAA,gBAC5BtB,OAAA,CAACN,MAAM,CAACiC,IAAI;UAEVxB,SAAS,EAAC,8BAA8B;UACxCyB,OAAO,EAAE;YAAEC,KAAK,EAAE,GAAG;YAAEC,KAAK,EAAE;UAAU,CAAE;UAC1CC,OAAO,EAAE;YAAEF,KAAK,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAE;UACxCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAX,QAAA,GAE7Bb,OAAO,GAAG,KAAK,GAAGF,OAAO,EAAC,MAC7B;QAAA,GAPOA,OAAO;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOD,CAAC,EACbf,WAAW,iBACVX,OAAA;UAAMG,SAAS,EAAC,uBAAuB;UAAAmB,QAAA,GAAC,UAC9B,EAACX,WAAW,CAACuB,kBAAkB,CAAC,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELxB,WAAW,iBACVF,OAAA;MACEmC,OAAO,EAAEd,aAAc;MACvBe,QAAQ,EAAE3B,OAAQ;MAClBN,SAAS,EAAC,0EAA0E;MACpFkC,KAAK,EAAC,iBAAiB;MAAAf,QAAA,eAEvBtB,OAAA,CAACN,MAAM,CAAC4C,GAAG;QACTP,OAAO,EAAEtB,OAAO,GAAG;UAAE8B,MAAM,EAAE;QAAI,CAAC,GAAG,CAAC,CAAE;QACxCP,UAAU,EAAEvB,OAAO,GAAG;UAAEwB,QAAQ,EAAE,CAAC;UAAEO,MAAM,EAAEC,QAAQ;UAAEC,IAAI,EAAE;QAAS,CAAC,GAAG,CAAC,CAAE;QAAApB,QAAA,eAE7EtB,OAAA,CAACF,aAAa;UAACK,SAAS,EAAC;QAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACtB,EAAA,CAxEIH,YAAY;EAAA,QACCL,OAAO,EACID,OAAO;AAAA;AAAAgD,EAAA,GAF/B1C,YAAY;AA0ElB,eAAeA,YAAY;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}