{"ast": null, "code": "/**\n *  [[link-pocket]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Polygon (``matic``)\n *  - Arbitrum (``arbitrum``)\n *\n *  @_subsection: api/providers/thirdparty:Pocket  [providers-pocket]\n */\nimport { defineProperties, FetchRequest, assertArgument } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nconst defaultApplicationId = \"62e1ad51b37b8e00394bda3b\";\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"eth-mainnet.gateway.pokt.network\";\n    case \"goerli\":\n      return \"eth-goerli.gateway.pokt.network\";\n    case \"matic\":\n      return \"poly-mainnet.gateway.pokt.network\";\n    case \"matic-mumbai\":\n      return \"polygon-mumbai-rpc.gateway.pokt.network\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **PocketProvider** connects to the [[link-pocket]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-pocket-signup).\n */\nexport class PocketProvider extends JsonRpcProvider {\n  /**\n   *  The Application ID for the Pocket connection.\n   */\n  applicationId;\n  /**\n   *  The Application Secret for making authenticated requests\n   *  to the Pocket connection.\n   */\n  applicationSecret;\n  /**\n   *  Create a new **PocketProvider**.\n   *\n   *  By default connecting to ``mainnet`` with a highly throttled\n   *  API key.\n   */\n  constructor(_network, applicationId, applicationSecret) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (applicationId == null) {\n      applicationId = defaultApplicationId;\n    }\n    if (applicationSecret == null) {\n      applicationSecret = null;\n    }\n    const options = {\n      staticNetwork: network\n    };\n    const request = PocketProvider.getRequest(network, applicationId, applicationSecret);\n    super(request, network, options);\n    defineProperties(this, {\n      applicationId,\n      applicationSecret\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new PocketProvider(chainId, this.applicationId, this.applicationSecret);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  /**\n   *  Returns a prepared request for connecting to %%network%% with\n   *  %%applicationId%%.\n   */\n  static getRequest(network, applicationId, applicationSecret) {\n    if (applicationId == null) {\n      applicationId = defaultApplicationId;\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/v1/lb/${applicationId}`);\n    request.allowGzip = true;\n    if (applicationSecret) {\n      request.setCredentials(\"\", applicationSecret);\n    }\n    if (applicationId === defaultApplicationId) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"PocketProvider\");\n        return true;\n      };\n    }\n    return request;\n  }\n  isCommunityResource() {\n    return this.applicationId === defaultApplicationId;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "FetchRequest", "assertArgument", "showThrottleMessage", "Network", "JsonRpcProvider", "defaultApplicationId", "getHost", "name", "PocketProvider", "applicationId", "applicationSecret", "constructor", "_network", "network", "from", "options", "staticNetwork", "request", "getRequest", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "allowGzip", "setCredentials", "retryFunc", "response", "attempt", "isCommunityResource"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-pocket.ts"], "sourcesContent": ["/**\n *  [[link-pocket]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Polygon (``matic``)\n *  - Arbitrum (``arbitrum``)\n *\n *  @_subsection: api/providers/thirdparty:Pocket  [providers-pocket]\n */\nimport {\n    defineProperties, FetchRequest, assertArgument\n} from \"../utils/index.js\";\n\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\n\nconst defaultApplicationId = \"62e1ad51b37b8e00394bda3b\";\n\nfunction getHost(name: string): string {\n    switch (name) {\n        case \"mainnet\":\n            return  \"eth-mainnet.gateway.pokt.network\";\n        case \"goerli\":\n            return \"eth-goerli.gateway.pokt.network\";\n\n        case \"matic\":\n            return \"poly-mainnet.gateway.pokt.network\";\n        case \"matic-mumbai\":\n            return \"polygon-mumbai-rpc.gateway.pokt.network\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n\n/**\n *  The **PocketProvider** connects to the [[link-pocket]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-pocket-signup).\n */\nexport class PocketProvider extends JsonRpcProvider implements CommunityResourcable {\n\n    /**\n     *  The Application ID for the Pocket connection.\n     */\n    readonly applicationId!: string;\n\n    /**\n     *  The Application Secret for making authenticated requests\n     *  to the Pocket connection.\n     */\n    readonly applicationSecret!: null | string;\n\n    /**\n     *  Create a new **PocketProvider**.\n     *\n     *  By default connecting to ``mainnet`` with a highly throttled\n     *  API key.\n     */\n    constructor(_network?: Networkish, applicationId?: null | string, applicationSecret?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        if (applicationId == null) { applicationId = defaultApplicationId; }\n        if (applicationSecret == null) { applicationSecret = null; }\n\n        const options = { staticNetwork: network };\n\n        const request = PocketProvider.getRequest(network, applicationId, applicationSecret);\n        super(request, network, options);\n\n        defineProperties<PocketProvider>(this, { applicationId, applicationSecret });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new PocketProvider(chainId, this.applicationId, this.applicationSecret);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    /**\n     *  Returns a prepared request for connecting to %%network%% with\n     *  %%applicationId%%.\n     */\n    static getRequest(network: Network, applicationId?: null | string, applicationSecret?: null | string): FetchRequest {\n        if (applicationId == null) { applicationId = defaultApplicationId; }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/v1/lb/${ applicationId }`);\n        request.allowGzip = true;\n\n        if (applicationSecret) {\n            request.setCredentials(\"\", applicationSecret);\n        }\n\n        if (applicationId === defaultApplicationId) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"PocketProvider\");\n                return true;\n            };\n        }\n\n        return request;\n    }\n\n    isCommunityResource(): boolean {\n        return (this.applicationId === defaultApplicationId);\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAaA,SACIA,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAC3C,mBAAmB;AAG1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAKvD,MAAMC,oBAAoB,GAAG,0BAA0B;AAEvD,SAASC,OAAOA,CAACC,IAAY;EACzB,QAAQA,IAAI;IACR,KAAK,SAAS;MACV,OAAQ,kCAAkC;IAC9C,KAAK,QAAQ;MACT,OAAO,iCAAiC;IAE5C,KAAK,OAAO;MACR,OAAO,mCAAmC;IAC9C,KAAK,cAAc;MACf,OAAO,yCAAyC;;EAGxDN,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEM,IAAI,CAAC;AACjE;AAGA;;;;;;;;;AASA,OAAM,MAAOC,cAAe,SAAQJ,eAAe;EAE/C;;;EAGSK,aAAa;EAEtB;;;;EAISC,iBAAiB;EAE1B;;;;;;EAMAC,YAAYC,QAAqB,EAAEH,aAA6B,EAAEC,iBAAiC;IAC/F,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGV,OAAO,CAACW,IAAI,CAACF,QAAQ,CAAC;IACtC,IAAIH,aAAa,IAAI,IAAI,EAAE;MAAEA,aAAa,GAAGJ,oBAAoB;;IACjE,IAAIK,iBAAiB,IAAI,IAAI,EAAE;MAAEA,iBAAiB,GAAG,IAAI;;IAEzD,MAAMK,OAAO,GAAG;MAAEC,aAAa,EAAEH;IAAO,CAAE;IAE1C,MAAMI,OAAO,GAAGT,cAAc,CAACU,UAAU,CAACL,OAAO,EAAEJ,aAAa,EAAEC,iBAAiB,CAAC;IACpF,KAAK,CAACO,OAAO,EAAEJ,OAAO,EAAEE,OAAO,CAAC;IAEhChB,gBAAgB,CAAiB,IAAI,EAAE;MAAEU,aAAa;MAAEC;IAAiB,CAAE,CAAC;EAChF;EAEAS,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIZ,cAAc,CAACY,OAAO,EAAE,IAAI,CAACX,aAAa,EAAE,IAAI,CAACC,iBAAiB,CAAC;KACjF,CAAC,OAAOW,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEA;;;;EAIA,OAAOF,UAAUA,CAACL,OAAgB,EAAEJ,aAA6B,EAAEC,iBAAiC;IAChG,IAAID,aAAa,IAAI,IAAI,EAAE;MAAEA,aAAa,GAAGJ,oBAAoB;;IAEjE,MAAMY,OAAO,GAAG,IAAIjB,YAAY,CAAC,YAAaM,OAAO,CAACO,OAAO,CAACN,IAAI,CAAE,UAAWE,aAAc,EAAE,CAAC;IAChGQ,OAAO,CAACK,SAAS,GAAG,IAAI;IAExB,IAAIZ,iBAAiB,EAAE;MACnBO,OAAO,CAACM,cAAc,CAAC,EAAE,EAAEb,iBAAiB,CAAC;;IAGjD,IAAID,aAAa,KAAKJ,oBAAoB,EAAE;MACxCY,OAAO,CAACO,SAAS,GAAG,OAAOP,OAAO,EAAEQ,QAAQ,EAAEC,OAAO,KAAI;QACrDxB,mBAAmB,CAAC,gBAAgB,CAAC;QACrC,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOe,OAAO;EAClB;EAEAU,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAAClB,aAAa,KAAKJ,oBAAoB;EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}