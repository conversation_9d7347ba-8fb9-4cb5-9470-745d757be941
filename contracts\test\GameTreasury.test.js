const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("GameTreasury", function () {
  let cqtToken, gameTreasury;
  let owner, playToEarn, ecosystem, team, marketing, staking, liquidity;
  let gameServer, player1, player2;

  beforeEach(async function () {
    [owner, playToEarn, ecosystem, team, marketing, staking, liquidity, gameServer, player1, player2] = await ethers.getSigners();

    // Deploy CQTToken
    const CQTToken = await ethers.getContractFactory("CQTToken");
    cqtToken = await CQTToken.deploy(
      playToEarn.address,
      ecosystem.address,
      team.address,
      marketing.address,
      staking.address,
      liquidity.address
    );
    await cqtToken.waitForDeployment();

    // Deploy GameTreasury
    const GameTreasury = await ethers.getContractFactory("GameTreasury");
    gameTreasury = await GameTreasury.deploy(await cqtToken.getAddress());
    await gameTreasury.waitForDeployment();

    // Add GameTreasury as authorized game contract
    await cqtToken.addGameContract(await gameTreasury.getAddress());

    // Authorize game server
    await gameTreasury.authorizeServer(gameServer.address);
  });

  describe("Server Authorization", function () {
    it("Should allow owner to authorize servers", async function () {
      await gameTreasury.authorizeServer(player1.address);
      expect(await gameTreasury.authorizedServers(player1.address)).to.be.true;
    });

    it("Should not allow non-owner to authorize servers", async function () {
      await expect(
        gameTreasury.connect(player1).authorizeServer(player2.address)
      ).to.be.revertedWithCustomError(gameTreasury, "OwnableUnauthorizedAccount");
    });

    it("Should allow owner to deauthorize servers", async function () {
      await gameTreasury.authorizeServer(player1.address);
      await gameTreasury.deauthorizeServer(player1.address);
      expect(await gameTreasury.authorizedServers(player1.address)).to.be.false;
    });
  });

  describe("PvE Rewards", function () {
    it("Should distribute PvE rewards correctly", async function () {
      const initialBalance = await cqtToken.balanceOf(player1.address);
      
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      const expectedReward = await gameTreasury.pveWinReward();
      
      expect(finalBalance - initialBalance).to.equal(expectedReward);
      expect(await gameTreasury.pveWins(player1.address)).to.equal(1);
      expect(await gameTreasury.totalEarned(player1.address)).to.equal(expectedReward);
    });

    it("Should not allow unauthorized servers to distribute rewards", async function () {
      await expect(
        gameTreasury.connect(player1).distributePvEReward(player2.address)
      ).to.be.revertedWith("Not authorized server");
    });

    it("Should emit RewardDistributed event", async function () {
      const expectedReward = await gameTreasury.pveWinReward();
      
      await expect(
        gameTreasury.connect(gameServer).distributePvEReward(player1.address)
      ).to.emit(gameTreasury, "RewardDistributed")
       .withArgs(player1.address, expectedReward, "PvE");
    });
  });

  describe("PvP Rewards", function () {
    it("Should distribute PvP rewards correctly", async function () {
      const initialBalance = await cqtToken.balanceOf(player1.address);
      
      await gameTreasury.connect(gameServer).distributePvPReward(player1.address);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      const expectedReward = await gameTreasury.pvpWinReward();
      
      expect(finalBalance - initialBalance).to.equal(expectedReward);
      expect(await gameTreasury.pvpWins(player1.address)).to.equal(1);
      expect(await gameTreasury.totalEarned(player1.address)).to.equal(expectedReward);
    });
  });

  describe("Hero Upgrades", function () {
    beforeEach(async function () {
      // Give player1 some tokens first
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
    });

    it("Should allow hero upgrades when player has enough tokens", async function () {
      const initialBalance = await cqtToken.balanceOf(player1.address);
      const upgradeCost = await gameTreasury.heroUpgradeCost();
      
      // Player needs to approve the treasury to spend tokens
      await cqtToken.connect(player1).approve(await gameTreasury.getAddress(), upgradeCost);
      
      await gameTreasury.connect(gameServer).upgradeHero(player1.address);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      expect(initialBalance - finalBalance).to.equal(upgradeCost);
      expect(await gameTreasury.totalSpent(player1.address)).to.equal(upgradeCost);
    });

    it("Should not allow upgrades without sufficient balance", async function () {
      // Try to upgrade without enough tokens
      await expect(
        gameTreasury.connect(gameServer).upgradeHero(player2.address)
      ).to.be.revertedWith("Insufficient CQT balance");
    });
  });

  describe("Tournament System", function () {
    beforeEach(async function () {
      // Give players tokens for tournament entry
      for (let i = 0; i < 10; i++) {
        await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
        await gameTreasury.connect(gameServer).distributePvEReward(player2.address);
      }
    });

    it("Should allow tournament entry with sufficient tokens", async function () {
      const entryFee = await gameTreasury.tournamentEntryFee();
      const initialPrizePool = await gameTreasury.tournamentPrizePool();
      
      await cqtToken.connect(player1).approve(await gameTreasury.getAddress(), entryFee);
      
      await gameTreasury.connect(gameServer).enterTournament(player1.address);
      
      const finalPrizePool = await gameTreasury.tournamentPrizePool();
      expect(finalPrizePool - initialPrizePool).to.equal(entryFee);
      expect(await gameTreasury.totalSpent(player1.address)).to.equal(entryFee);
    });

    it("Should distribute tournament prizes correctly", async function () {
      const entryFee = await gameTreasury.tournamentEntryFee();
      
      // Multiple players enter tournament
      await cqtToken.connect(player1).approve(await gameTreasury.getAddress(), entryFee);
      await cqtToken.connect(player2).approve(await gameTreasury.getAddress(), entryFee);
      
      await gameTreasury.connect(gameServer).enterTournament(player1.address);
      await gameTreasury.connect(gameServer).enterTournament(player2.address);
      
      const prizeAmount = entryFee; // Winner gets entry fee as prize
      const initialBalance = await cqtToken.balanceOf(player1.address);
      
      await gameTreasury.connect(gameServer).distributeTournamentPrize(player1.address, prizeAmount);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      expect(finalBalance - initialBalance).to.equal(prizeAmount);
    });
  });

  describe("Player Statistics", function () {
    it("Should track player statistics correctly", async function () {
      // Distribute some rewards
      await gameTreasury.connect(gameServer).distributePvEReward(player1.address);
      await gameTreasury.connect(gameServer).distributePvPReward(player1.address);
      
      const stats = await gameTreasury.getPlayerStats(player1.address);
      
      const expectedPvEReward = await gameTreasury.pveWinReward();
      const expectedPvPReward = await gameTreasury.pvpWinReward();
      const expectedTotal = expectedPvEReward + expectedPvPReward;
      
      expect(stats[0]).to.equal(expectedTotal); // totalEarned
      expect(stats[1]).to.equal(0); // totalSpent
      expect(stats[2]).to.equal(1); // pveWins
      expect(stats[3]).to.equal(1); // pvpWins
      expect(stats[4]).to.equal(expectedTotal); // balance
    });
  });

  describe("Configuration Updates", function () {
    it("Should allow owner to update reward amounts", async function () {
      const newPvEReward = ethers.parseEther("20");
      const newPvPReward = ethers.parseEther("50");
      const newDailyReward = ethers.parseEther("10");
      
      await gameTreasury.updateRewards(newPvEReward, newPvPReward, newDailyReward);
      
      expect(await gameTreasury.pveWinReward()).to.equal(newPvEReward);
      expect(await gameTreasury.pvpWinReward()).to.equal(newPvPReward);
      expect(await gameTreasury.dailyQuestReward()).to.equal(newDailyReward);
    });

    it("Should allow owner to update costs", async function () {
      const newUpgradeCost = ethers.parseEther("100");
      const newSkillCost = ethers.parseEther("200");
      const newTournamentFee = ethers.parseEther("150");
      
      await gameTreasury.updateCosts(newUpgradeCost, newSkillCost, newTournamentFee);
      
      expect(await gameTreasury.heroUpgradeCost()).to.equal(newUpgradeCost);
      expect(await gameTreasury.skillUnlockCost()).to.equal(newSkillCost);
      expect(await gameTreasury.tournamentEntryFee()).to.equal(newTournamentFee);
    });

    it("Should not allow non-owner to update configuration", async function () {
      await expect(
        gameTreasury.connect(player1).updateRewards(100, 200, 50)
      ).to.be.revertedWithCustomError(gameTreasury, "OwnableUnauthorizedAccount");
    });
  });
});
