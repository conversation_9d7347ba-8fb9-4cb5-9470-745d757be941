{"ast": null, "code": "import { assert } from \"../utils/index.js\";\nimport { AnkrProvider } from \"./provider-ankr.js\";\nimport { AlchemyProvider } from \"./provider-alchemy.js\";\n//import { BlockscoutProvider } from \"./provider-blockscout.js\";\nimport { ChainstackProvider } from \"./provider-chainstack.js\";\nimport { CloudflareProvider } from \"./provider-cloudflare.js\";\nimport { EtherscanProvider } from \"./provider-etherscan.js\";\nimport { InfuraProvider } from \"./provider-infura.js\";\n//import { PocketProvider } from \"./provider-pocket.js\";\nimport { QuickNodeProvider } from \"./provider-quicknode.js\";\nimport { FallbackProvider } from \"./provider-fallback.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nimport { Network } from \"./network.js\";\nimport { WebSocketProvider } from \"./provider-websocket.js\";\nfunction isWebSocketLike(value) {\n  return value && typeof value.send === \"function\" && typeof value.close === \"function\";\n}\nconst Testnets = \"goerli kovan sepolia classicKotti optimism-goerli arbitrum-goerli matic-mumbai bnbt\".split(\" \");\n/**\n *  Returns a default provider for %%network%%.\n *\n *  If %%network%% is a [[WebSocketLike]] or string that begins with\n *  ``\"ws:\"`` or ``\"wss:\"``, a [[WebSocketProvider]] is returned backed\n *  by that WebSocket or URL.\n *\n *  If %%network%% is a string that begins with ``\"HTTP:\"`` or ``\"HTTPS:\"``,\n *  a [[JsonRpcProvider]] is returned connected to that URL.\n *\n *  Otherwise, a default provider is created backed by well-known public\n *  Web3 backends (such as [[link-infura]]) using community-provided API\n *  keys.\n *\n *  The %%options%% allows specifying custom API keys per backend (setting\n *  an API key to ``\"-\"`` will omit that provider) and ``options.exclusive``\n *  can be set to either a backend name or and array of backend names, which\n *  will whitelist **only** those backends.\n *\n *  Current backend strings supported are:\n *  - ``\"alchemy\"``\n *  - ``\"ankr\"``\n *  - ``\"cloudflare\"``\n *  - ``\"chainstack\"``\n *  - ``\"etherscan\"``\n *  - ``\"infura\"``\n *  - ``\"publicPolygon\"``\n *  - ``\"quicknode\"``\n *\n *  @example:\n *    // Connect to a local Geth node\n *    provider = getDefaultProvider(\"http://localhost:8545/\");\n *\n *    // Connect to Ethereum mainnet with any current and future\n *    // third-party services available\n *    provider = getDefaultProvider(\"mainnet\");\n *\n *    // Connect to Polygon, but only allow Etherscan and\n *    // INFURA and use \"MY_API_KEY\" in calls to Etherscan.\n *    provider = getDefaultProvider(\"matic\", {\n *      etherscan: \"MY_API_KEY\",\n *      exclusive: [ \"etherscan\", \"infura\" ]\n *    });\n */\nexport function getDefaultProvider(network, options) {\n  if (options == null) {\n    options = {};\n  }\n  const allowService = name => {\n    if (options[name] === \"-\") {\n      return false;\n    }\n    if (typeof options.exclusive === \"string\") {\n      return name === options.exclusive;\n    }\n    if (Array.isArray(options.exclusive)) {\n      return options.exclusive.indexOf(name) !== -1;\n    }\n    return true;\n  };\n  if (typeof network === \"string\" && network.match(/^https?:/)) {\n    return new JsonRpcProvider(network);\n  }\n  if (typeof network === \"string\" && network.match(/^wss?:/) || isWebSocketLike(network)) {\n    return new WebSocketProvider(network);\n  }\n  // Get the network and name, if possible\n  let staticNetwork = null;\n  try {\n    staticNetwork = Network.from(network);\n  } catch (error) {}\n  const providers = [];\n  if (allowService(\"publicPolygon\") && staticNetwork) {\n    if (staticNetwork.name === \"matic\") {\n      providers.push(new JsonRpcProvider(\"https:/\\/polygon-rpc.com/\", staticNetwork, {\n        staticNetwork\n      }));\n    } else if (staticNetwork.name === \"matic-amoy\") {\n      providers.push(new JsonRpcProvider(\"https:/\\/rpc-amoy.polygon.technology/\", staticNetwork, {\n        staticNetwork\n      }));\n    }\n  }\n  if (allowService(\"alchemy\")) {\n    try {\n      providers.push(new AlchemyProvider(network, options.alchemy));\n    } catch (error) {}\n  }\n  if (allowService(\"ankr\") && options.ankr != null) {\n    try {\n      providers.push(new AnkrProvider(network, options.ankr));\n    } catch (error) {}\n  }\n  /* Temporarily remove until custom error issue is fixed\n      if (allowService(\"blockscout\")) {\n          try {\n              providers.push(new BlockscoutProvider(network, options.blockscout));\n          } catch (error) { }\n      }\n  */\n  if (allowService(\"chainstack\")) {\n    try {\n      providers.push(new ChainstackProvider(network, options.chainstack));\n    } catch (error) {}\n  }\n  if (allowService(\"cloudflare\")) {\n    try {\n      providers.push(new CloudflareProvider(network));\n    } catch (error) {}\n  }\n  if (allowService(\"etherscan\")) {\n    try {\n      providers.push(new EtherscanProvider(network, options.etherscan));\n    } catch (error) {}\n  }\n  if (allowService(\"infura\")) {\n    try {\n      let projectId = options.infura;\n      let projectSecret = undefined;\n      if (typeof projectId === \"object\") {\n        projectSecret = projectId.projectSecret;\n        projectId = projectId.projectId;\n      }\n      providers.push(new InfuraProvider(network, projectId, projectSecret));\n    } catch (error) {}\n  }\n  /*\n      if (options.pocket !== \"-\") {\n          try {\n              let appId = options.pocket;\n              let secretKey: undefined | string = undefined;\n              let loadBalancer: undefined | boolean = undefined;\n              if (typeof(appId) === \"object\") {\n                  loadBalancer = !!appId.loadBalancer;\n                  secretKey = appId.secretKey;\n                  appId = appId.appId;\n              }\n              providers.push(new PocketProvider(network, appId, secretKey, loadBalancer));\n          } catch (error) { console.log(error); }\n      }\n  */\n  if (allowService(\"quicknode\")) {\n    try {\n      let token = options.quicknode;\n      providers.push(new QuickNodeProvider(network, token));\n    } catch (error) {}\n  }\n  assert(providers.length, \"unsupported default network\", \"UNSUPPORTED_OPERATION\", {\n    operation: \"getDefaultProvider\"\n  });\n  // No need for a FallbackProvider\n  if (providers.length === 1) {\n    return providers[0];\n  }\n  // We use the floor because public third-party providers can be unreliable,\n  // so a low number of providers with a large quorum will fail too often\n  let quorum = Math.floor(providers.length / 2);\n  if (quorum > 2) {\n    quorum = 2;\n  }\n  // Testnets don't need as strong a security gaurantee and speed is\n  // more useful during testing\n  if (staticNetwork && Testnets.indexOf(staticNetwork.name) !== -1) {\n    quorum = 1;\n  }\n  // Provided override qorum takes priority\n  if (options && options.quorum) {\n    quorum = options.quorum;\n  }\n  return new FallbackProvider(providers, undefined, {\n    quorum\n  });\n}", "map": {"version": 3, "names": ["assert", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlchemyProvider", "ChainstackProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EtherscanProvider", "InfuraProvider", "QuickNodeProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JsonRpcProvider", "Network", "WebSocketProvider", "isWebSocketLike", "value", "send", "close", "Testnets", "split", "getDefaultProvider", "network", "options", "allowService", "name", "exclusive", "Array", "isArray", "indexOf", "match", "staticNetwork", "from", "error", "providers", "push", "alchemy", "ankr", "chainstack", "etherscan", "projectId", "infura", "projectSecret", "undefined", "token", "quicknode", "length", "operation", "quorum", "Math", "floor"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\default-provider.ts"], "sourcesContent": ["\nimport { assert } from \"../utils/index.js\";\n\nimport { AnkrProvider } from \"./provider-ankr.js\";\nimport { AlchemyProvider } from \"./provider-alchemy.js\";\n//import { BlockscoutProvider } from \"./provider-blockscout.js\";\nimport { ChainstackProvider } from \"./provider-chainstack.js\";\nimport { CloudflareProvider } from \"./provider-cloudflare.js\";\nimport { EtherscanProvider } from \"./provider-etherscan.js\";\nimport { InfuraProvider } from \"./provider-infura.js\";\n//import { PocketProvider } from \"./provider-pocket.js\";\nimport { QuickNodeProvider } from \"./provider-quicknode.js\";\n\nimport { FallbackProvider } from \"./provider-fallback.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nimport { Network } from \"./network.js\";\nimport { WebSocketProvider } from \"./provider-websocket.js\";\n\nimport type { AbstractProvider } from \"./abstract-provider.js\";\nimport type { Networkish } from \"./network.js\";\nimport { WebSocketLike } from \"./provider-websocket.js\";\n\nfunction isWebSocketLike(value: any): value is WebSocketLike {\n    return (value && typeof(value.send) === \"function\" &&\n        typeof(value.close) === \"function\");\n}\n\nconst Testnets = \"goerli kovan sepolia classicKotti optimism-goerli arbitrum-goerli matic-mumbai bnbt\".split(\" \");\n\n/**\n *  Returns a default provider for %%network%%.\n *\n *  If %%network%% is a [[WebSocketLike]] or string that begins with\n *  ``\"ws:\"`` or ``\"wss:\"``, a [[WebSocketProvider]] is returned backed\n *  by that WebSocket or URL.\n *\n *  If %%network%% is a string that begins with ``\"HTTP:\"`` or ``\"HTTPS:\"``,\n *  a [[JsonRpcProvider]] is returned connected to that URL.\n *\n *  Otherwise, a default provider is created backed by well-known public\n *  Web3 backends (such as [[link-infura]]) using community-provided API\n *  keys.\n *\n *  The %%options%% allows specifying custom API keys per backend (setting\n *  an API key to ``\"-\"`` will omit that provider) and ``options.exclusive``\n *  can be set to either a backend name or and array of backend names, which\n *  will whitelist **only** those backends.\n *\n *  Current backend strings supported are:\n *  - ``\"alchemy\"``\n *  - ``\"ankr\"``\n *  - ``\"cloudflare\"``\n *  - ``\"chainstack\"``\n *  - ``\"etherscan\"``\n *  - ``\"infura\"``\n *  - ``\"publicPolygon\"``\n *  - ``\"quicknode\"``\n *\n *  @example:\n *    // Connect to a local Geth node\n *    provider = getDefaultProvider(\"http://localhost:8545/\");\n *\n *    // Connect to Ethereum mainnet with any current and future\n *    // third-party services available\n *    provider = getDefaultProvider(\"mainnet\");\n *\n *    // Connect to Polygon, but only allow Etherscan and\n *    // INFURA and use \"MY_API_KEY\" in calls to Etherscan.\n *    provider = getDefaultProvider(\"matic\", {\n *      etherscan: \"MY_API_KEY\",\n *      exclusive: [ \"etherscan\", \"infura\" ]\n *    });\n */\nexport function getDefaultProvider(network?: string | Networkish | WebSocketLike, options?: any): AbstractProvider {\n    if (options == null) { options = { }; }\n\n    const allowService = (name: string) => {\n        if (options[name] === \"-\") { return false; }\n        if (typeof(options.exclusive) === \"string\") {\n            return (name === options.exclusive);\n        }\n        if (Array.isArray(options.exclusive)) {\n            return (options.exclusive.indexOf(name) !== -1);\n        }\n        return true;\n    };\n\n    if (typeof(network) === \"string\" && network.match(/^https?:/)) {\n        return new JsonRpcProvider(network);\n    }\n\n    if (typeof(network) === \"string\" && network.match(/^wss?:/) || isWebSocketLike(network)) {\n        return new WebSocketProvider(network);\n    }\n\n    // Get the network and name, if possible\n    let staticNetwork: null | Network = null;\n    try {\n        staticNetwork = Network.from(network);\n    } catch (error) { }\n\n\n    const providers: Array<AbstractProvider> = [ ];\n\n    if (allowService(\"publicPolygon\") && staticNetwork) {\n        if (staticNetwork.name === \"matic\") {\n            providers.push(new JsonRpcProvider(\"https:/\\/polygon-rpc.com/\", staticNetwork, { staticNetwork }));\n        } else if (staticNetwork.name === \"matic-amoy\") {\n            providers.push(new JsonRpcProvider(\"https:/\\/rpc-amoy.polygon.technology/\", staticNetwork, { staticNetwork }));\n        }\n    }\n\n    if (allowService(\"alchemy\")) {\n        try {\n            providers.push(new AlchemyProvider(network, options.alchemy));\n        } catch (error) { }\n    }\n\n    if (allowService(\"ankr\") && options.ankr != null) {\n        try {\n            providers.push(new AnkrProvider(network, options.ankr));\n        } catch (error) { }\n    }\n/* Temporarily remove until custom error issue is fixed\n    if (allowService(\"blockscout\")) {\n        try {\n            providers.push(new BlockscoutProvider(network, options.blockscout));\n        } catch (error) { }\n    }\n*/\n    if (allowService(\"chainstack\")) {\n        try {\n            providers.push(new ChainstackProvider(network, options.chainstack));\n        } catch (error) { }\n    }\n\n    if (allowService(\"cloudflare\")) {\n        try {\n            providers.push(new CloudflareProvider(network));\n        } catch (error) { }\n    }\n\n    if (allowService(\"etherscan\")) {\n        try {\n            providers.push(new EtherscanProvider(network, options.etherscan));\n        } catch (error) { }\n    }\n\n    if (allowService(\"infura\")) {\n        try {\n            let projectId = options.infura;\n            let projectSecret: undefined | string = undefined;\n            if (typeof(projectId) === \"object\") {\n                projectSecret = projectId.projectSecret;\n                projectId = projectId.projectId;\n            }\n            providers.push(new InfuraProvider(network, projectId, projectSecret));\n        } catch (error) { }\n    }\n/*\n    if (options.pocket !== \"-\") {\n        try {\n            let appId = options.pocket;\n            let secretKey: undefined | string = undefined;\n            let loadBalancer: undefined | boolean = undefined;\n            if (typeof(appId) === \"object\") {\n                loadBalancer = !!appId.loadBalancer;\n                secretKey = appId.secretKey;\n                appId = appId.appId;\n            }\n            providers.push(new PocketProvider(network, appId, secretKey, loadBalancer));\n        } catch (error) { console.log(error); }\n    }\n*/\n    if (allowService(\"quicknode\")) {\n        try {\n            let token = options.quicknode;\n            providers.push(new QuickNodeProvider(network, token));\n        } catch (error) { }\n    }\n\n    assert(providers.length, \"unsupported default network\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"getDefaultProvider\"\n    });\n\n    // No need for a FallbackProvider\n    if (providers.length === 1) { return providers[0]; }\n\n    // We use the floor because public third-party providers can be unreliable,\n    // so a low number of providers with a large quorum will fail too often\n    let quorum = Math.floor(providers.length / 2);\n    if (quorum > 2) { quorum = 2; }\n\n    // Testnets don't need as strong a security gaurantee and speed is\n    // more useful during testing\n    if (staticNetwork && Testnets.indexOf(staticNetwork.name) !== -1) { quorum = 1; }\n\n    // Provided override qorum takes priority\n    if (options && options.quorum) { quorum = options.quorum; }\n\n    return new FallbackProvider(providers, undefined, { quorum });\n}\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,mBAAmB;AAE1C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,cAAc,QAAQ,sBAAsB;AACrD;AACA,SAASC,iBAAiB,QAAQ,yBAAyB;AAE3D,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,iBAAiB,QAAQ,yBAAyB;AAM3D,SAASC,eAAeA,CAACC,KAAU;EAC/B,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAK,KAAK,UAAU,IAC9C,OAAOD,KAAK,CAACE,KAAM,KAAK,UAAU;AAC1C;AAEA,MAAMC,QAAQ,GAAG,qFAAqF,CAACC,KAAK,CAAC,GAAG,CAAC;AAEjH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CA,OAAM,SAAUC,kBAAkBA,CAACC,OAA6C,EAAEC,OAAa;EAC3F,IAAIA,OAAO,IAAI,IAAI,EAAE;IAAEA,OAAO,GAAG,EAAG;;EAEpC,MAAMC,YAAY,GAAIC,IAAY,IAAI;IAClC,IAAIF,OAAO,CAACE,IAAI,CAAC,KAAK,GAAG,EAAE;MAAE,OAAO,KAAK;;IACzC,IAAI,OAAOF,OAAO,CAACG,SAAU,KAAK,QAAQ,EAAE;MACxC,OAAQD,IAAI,KAAKF,OAAO,CAACG,SAAS;;IAEtC,IAAIC,KAAK,CAACC,OAAO,CAACL,OAAO,CAACG,SAAS,CAAC,EAAE;MAClC,OAAQH,OAAO,CAACG,SAAS,CAACG,OAAO,CAACJ,IAAI,CAAC,KAAK,CAAC,CAAC;;IAElD,OAAO,IAAI;EACf,CAAC;EAED,IAAI,OAAOH,OAAQ,KAAK,QAAQ,IAAIA,OAAO,CAACQ,KAAK,CAAC,UAAU,CAAC,EAAE;IAC3D,OAAO,IAAIlB,eAAe,CAACU,OAAO,CAAC;;EAGvC,IAAI,OAAOA,OAAQ,KAAK,QAAQ,IAAIA,OAAO,CAACQ,KAAK,CAAC,QAAQ,CAAC,IAAIf,eAAe,CAACO,OAAO,CAAC,EAAE;IACrF,OAAO,IAAIR,iBAAiB,CAACQ,OAAO,CAAC;;EAGzC;EACA,IAAIS,aAAa,GAAmB,IAAI;EACxC,IAAI;IACAA,aAAa,GAAGlB,OAAO,CAACmB,IAAI,CAACV,OAAO,CAAC;GACxC,CAAC,OAAOW,KAAK,EAAE;EAGhB,MAAMC,SAAS,GAA4B,EAAG;EAE9C,IAAIV,YAAY,CAAC,eAAe,CAAC,IAAIO,aAAa,EAAE;IAChD,IAAIA,aAAa,CAACN,IAAI,KAAK,OAAO,EAAE;MAChCS,SAAS,CAACC,IAAI,CAAC,IAAIvB,eAAe,CAAC,2BAA2B,EAAEmB,aAAa,EAAE;QAAEA;MAAa,CAAE,CAAC,CAAC;KACrG,MAAM,IAAIA,aAAa,CAACN,IAAI,KAAK,YAAY,EAAE;MAC5CS,SAAS,CAACC,IAAI,CAAC,IAAIvB,eAAe,CAAC,uCAAuC,EAAEmB,aAAa,EAAE;QAAEA;MAAa,CAAE,CAAC,CAAC;;;EAItH,IAAIP,YAAY,CAAC,SAAS,CAAC,EAAE;IACzB,IAAI;MACAU,SAAS,CAACC,IAAI,CAAC,IAAI9B,eAAe,CAACiB,OAAO,EAAEC,OAAO,CAACa,OAAO,CAAC,CAAC;KAChE,CAAC,OAAOH,KAAK,EAAE;;EAGpB,IAAIT,YAAY,CAAC,MAAM,CAAC,IAAID,OAAO,CAACc,IAAI,IAAI,IAAI,EAAE;IAC9C,IAAI;MACAH,SAAS,CAACC,IAAI,CAAC,IAAI/B,YAAY,CAACkB,OAAO,EAAEC,OAAO,CAACc,IAAI,CAAC,CAAC;KAC1D,CAAC,OAAOJ,KAAK,EAAE;;EAExB;;;;;;;EAOI,IAAIT,YAAY,CAAC,YAAY,CAAC,EAAE;IAC5B,IAAI;MACAU,SAAS,CAACC,IAAI,CAAC,IAAI7B,kBAAkB,CAACgB,OAAO,EAAEC,OAAO,CAACe,UAAU,CAAC,CAAC;KACtE,CAAC,OAAOL,KAAK,EAAE;;EAGpB,IAAIT,YAAY,CAAC,YAAY,CAAC,EAAE;IAC5B,IAAI;MACAU,SAAS,CAACC,IAAI,CAAC,IAAI5B,kBAAkB,CAACe,OAAO,CAAC,CAAC;KAClD,CAAC,OAAOW,KAAK,EAAE;;EAGpB,IAAIT,YAAY,CAAC,WAAW,CAAC,EAAE;IAC3B,IAAI;MACAU,SAAS,CAACC,IAAI,CAAC,IAAI3B,iBAAiB,CAACc,OAAO,EAAEC,OAAO,CAACgB,SAAS,CAAC,CAAC;KACpE,CAAC,OAAON,KAAK,EAAE;;EAGpB,IAAIT,YAAY,CAAC,QAAQ,CAAC,EAAE;IACxB,IAAI;MACA,IAAIgB,SAAS,GAAGjB,OAAO,CAACkB,MAAM;MAC9B,IAAIC,aAAa,GAAuBC,SAAS;MACjD,IAAI,OAAOH,SAAU,KAAK,QAAQ,EAAE;QAChCE,aAAa,GAAGF,SAAS,CAACE,aAAa;QACvCF,SAAS,GAAGA,SAAS,CAACA,SAAS;;MAEnCN,SAAS,CAACC,IAAI,CAAC,IAAI1B,cAAc,CAACa,OAAO,EAAEkB,SAAS,EAAEE,aAAa,CAAC,CAAC;KACxE,CAAC,OAAOT,KAAK,EAAE;;EAExB;;;;;;;;;;;;;;;EAeI,IAAIT,YAAY,CAAC,WAAW,CAAC,EAAE;IAC3B,IAAI;MACA,IAAIoB,KAAK,GAAGrB,OAAO,CAACsB,SAAS;MAC7BX,SAAS,CAACC,IAAI,CAAC,IAAIzB,iBAAiB,CAACY,OAAO,EAAEsB,KAAK,CAAC,CAAC;KACxD,CAAC,OAAOX,KAAK,EAAE;;EAGpB9B,MAAM,CAAC+B,SAAS,CAACY,MAAM,EAAE,6BAA6B,EAAE,uBAAuB,EAAE;IAC7EC,SAAS,EAAE;GACd,CAAC;EAEF;EACA,IAAIb,SAAS,CAACY,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOZ,SAAS,CAAC,CAAC,CAAC;;EAEjD;EACA;EACA,IAAIc,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAChB,SAAS,CAACY,MAAM,GAAG,CAAC,CAAC;EAC7C,IAAIE,MAAM,GAAG,CAAC,EAAE;IAAEA,MAAM,GAAG,CAAC;;EAE5B;EACA;EACA,IAAIjB,aAAa,IAAIZ,QAAQ,CAACU,OAAO,CAACE,aAAa,CAACN,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;IAAEuB,MAAM,GAAG,CAAC;;EAE9E;EACA,IAAIzB,OAAO,IAAIA,OAAO,CAACyB,MAAM,EAAE;IAAEA,MAAM,GAAGzB,OAAO,CAACyB,MAAM;;EAExD,OAAO,IAAIrC,gBAAgB,CAACuB,SAAS,EAAES,SAAS,EAAE;IAAEK;EAAM,CAAE,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}