const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const {
  verifyWalletSignature,
  generateAuthMessage,
  isValidWalletAddress,
  generateToken
} = require('../middleware/auth');

const router = express.Router();

// Generate nonce for wallet authentication
router.post('/nonce', [
  body('walletAddress')
    .isString()
    .custom((value) => {
      if (!isValidWalletAddress(value)) {
        throw new Error('Invalid wallet address format');
      }
      return true;
    })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { walletAddress } = req.body;
    const nonce = Math.floor(Math.random() * 1000000).toString();
    const message = generateAuthMessage(walletAddress, nonce);

    // Store nonce temporarily (in production, use Redis or similar)
    // For now, we'll include it in the message and verify it during login
    
    res.json({
      message,
      nonce
    });
  } catch (error) {
    console.error('Nonce generation error:', error);
    res.status(500).json({ error: 'Failed to generate nonce' });
  }
});

// Authenticate wallet and login
router.post('/login', [
  body('walletAddress')
    .isString()
    .custom((value) => {
      if (!isValidWalletAddress(value)) {
        throw new Error('Invalid wallet address format');
      }
      return true;
    }),
  body('signature').isString().notEmpty(),
  body('message').isString().notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { walletAddress, signature, message } = req.body;

    // Verify signature
    const isValidSignature = verifyWalletSignature(message, signature, walletAddress);
    if (!isValidSignature) {
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Check if user exists, if not create new user
    let users = await executeQuery(
      'SELECT * FROM users WHERE wallet_address = ?',
      [walletAddress.toLowerCase()]
    );

    let user;
    if (users.length === 0) {
      // Create new user
      await executeQuery(
        'INSERT INTO users (wallet_address) VALUES (?)',
        [walletAddress.toLowerCase()]
      );
      
      // Create starter heroes for new user
      await createStarterHeroes(walletAddress.toLowerCase());
      
      // Get the newly created user
      users = await executeQuery(
        'SELECT * FROM users WHERE wallet_address = ?',
        [walletAddress.toLowerCase()]
      );
    }

    user = users[0];

    // Generate JWT token
    const token = generateToken(walletAddress.toLowerCase());

    // Update last login
    await executeQuery(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE wallet_address = ?',
      [walletAddress.toLowerCase()]
    );

    res.json({
      token,
      user: {
        walletAddress: user.wallet_address,
        username: user.username,
        level: user.level,
        xp: user.xp,
        rankPoints: user.rank_points,
        totalBattles: user.total_battles,
        wins: user.wins,
        losses: user.losses,
        createdAt: user.created_at
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Create starter heroes for new users
async function createStarterHeroes(walletAddress) {
  const starterHeroes = [
    {
      hero_type: 'warrior',
      name: 'Brave Knight',
      hp: 120,
      atk: 25,
      def: 20,
      spd: 12,
      luk: 8
    },
    {
      hero_type: 'mage',
      name: 'Fire Wizard',
      hp: 80,
      atk: 35,
      def: 10,
      spd: 15,
      luk: 12
    },
    {
      hero_type: 'archer',
      name: 'Swift Hunter',
      hp: 100,
      atk: 30,
      def: 15,
      spd: 20,
      luk: 15
    }
  ];

  for (const hero of starterHeroes) {
    await executeQuery(
      `INSERT INTO heroes (owner_address, hero_type, name, hp, atk, def, spd, luk) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        walletAddress,
        hero.hero_type,
        hero.name,
        hero.hp,
        hero.atk,
        hero.def,
        hero.spd,
        hero.luk
      ]
    );
  }

  console.log(`Created starter heroes for user: ${walletAddress}`);
}

// Refresh token
router.post('/refresh', [
  body('token').isString().notEmpty()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { token } = req.body;
    
    // Verify the old token (even if expired)
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });
    
    // Check if user still exists
    const users = await executeQuery(
      'SELECT wallet_address FROM users WHERE wallet_address = ?',
      [decoded.walletAddress]
    );
    
    if (users.length === 0) {
      return res.status(401).json({ error: 'User not found' });
    }
    
    // Generate new token
    const newToken = generateToken(decoded.walletAddress);
    
    res.json({ token: newToken });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
});

// Logout (client-side token removal, but we can log it)
router.post('/logout', (req, res) => {
  // In a more complex setup, you might want to blacklist the token
  res.json({ message: 'Logged out successfully' });
});

module.exports = router;
