{"ast": null, "code": "/**\n *  Explain UUID and link to RFC here.\n *\n *  @_subsection: api/utils:UUID  [about-uuid]\n */\nimport { getBytes, hexlify } from \"./data.js\";\n/**\n *  Returns the version 4 [[link-uuid]] for the %%randomBytes%%.\n *\n *  @see: https://www.ietf.org/rfc/rfc4122.txt (Section 4.4)\n */\nexport function uuidV4(randomBytes) {\n  const bytes = getBytes(randomBytes, \"randomBytes\");\n  // Section: 4.1.3:\n  // - time_hi_and_version[12:16] = 0b0100\n  bytes[6] = bytes[6] & 0x0f | 0x40;\n  // Section 4.4\n  // - clock_seq_hi_and_reserved[6] = 0b0\n  // - clock_seq_hi_and_reserved[7] = 0b1\n  bytes[8] = bytes[8] & 0x3f | 0x80;\n  const value = hexlify(bytes);\n  return [value.substring(2, 10), value.substring(10, 14), value.substring(14, 18), value.substring(18, 22), value.substring(22, 34)].join(\"-\");\n}", "map": {"version": 3, "names": ["getBytes", "hexlify", "uuidV4", "randomBytes", "bytes", "value", "substring", "join"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\uuid.ts"], "sourcesContent": ["/**\n *  Explain UUID and link to RFC here.\n *\n *  @_subsection: api/utils:UUID  [about-uuid]\n */\nimport { getBytes, hexlify } from \"./data.js\";\n\nimport type { BytesLike } from \"./index.js\";\n\n/**\n *  Returns the version 4 [[link-uuid]] for the %%randomBytes%%.\n *\n *  @see: https://www.ietf.org/rfc/rfc4122.txt (Section 4.4)\n */\nexport function uuidV4(randomBytes: BytesLike): string {\n    const bytes = getBytes(randomBytes, \"randomBytes\");\n\n    // Section: 4.1.3:\n    // - time_hi_and_version[12:16] = 0b0100\n    bytes[6] = (bytes[6] & 0x0f) | 0x40;\n\n    // Section 4.4\n    // - clock_seq_hi_and_reserved[6] = 0b0\n    // - clock_seq_hi_and_reserved[7] = 0b1\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n\n    const value = hexlify(bytes);\n\n    return [\n       value.substring(2, 10),\n       value.substring(10, 14),\n       value.substring(14, 18),\n       value.substring(18, 22),\n       value.substring(22, 34),\n    ].join(\"-\");\n}\n"], "mappings": "AAAA;;;;;AAKA,SAASA,QAAQ,EAAEC,OAAO,QAAQ,WAAW;AAI7C;;;;;AAKA,OAAM,SAAUC,MAAMA,CAACC,WAAsB;EACzC,MAAMC,KAAK,GAAGJ,QAAQ,CAACG,WAAW,EAAE,aAAa,CAAC;EAElD;EACA;EACAC,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EAEnC;EACA;EACA;EACAA,KAAK,CAAC,CAAC,CAAC,GAAIA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EAEnC,MAAMC,KAAK,GAAGJ,OAAO,CAACG,KAAK,CAAC;EAE5B,OAAO,CACJC,KAAK,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EACtBD,KAAK,CAACC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EACvBD,KAAK,CAACC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EACvBD,KAAK,CAACC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,EACvBD,KAAK,CAACC,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CACzB,CAACC,IAAI,CAAC,GAAG,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}