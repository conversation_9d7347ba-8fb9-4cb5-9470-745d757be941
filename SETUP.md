# CryptoQuest Setup Guide

This guide will help you set up and run the complete CryptoQuest Web3 Play-to-Earn game.

## 🛠️ Prerequisites

Before starting, make sure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **MySQL** (v8.0 or higher) - [Download here](https://dev.mysql.com/downloads/)
- **Git** - [Download here](https://git-scm.com/)
- **MetaMask** browser extension - [Install here](https://metamask.io/)

## 📦 Installation Steps

### 1. Install Dependencies

```bash
# Install root dependencies
npm install

# Install contract dependencies
cd contracts
npm install
cd ..

# Install backend dependencies
cd backend
npm install
cd ..

# Install frontend dependencies
cd frontend
npm install
cd ..
```

### 2. Database Setup

1. **Start MySQL server** and create the database:

```sql
CREATE DATABASE cq_db;
```

2. **Run database migration**:

```bash
cd backend
npm run migrate
```

This will create all necessary tables and insert default data.

### 3. Smart Contract Deployment

1. **Start local blockchain** (in a new terminal):

```bash
cd contracts
npx hardhat node
```

This will start a local Ethereum node on `http://127.0.0.1:8545` and provide test accounts.

2. **Deploy contracts** (in another terminal):

```bash
cd contracts
npx hardhat run scripts/deploy.js --network localhost
```

3. **Copy contract addresses** from the deployment output and update the backend `.env` file:

```bash
# Update backend/.env with the deployed contract addresses
CQT_TOKEN_ADDRESS=0x...
GAME_TREASURY_ADDRESS=0x...
STAKING_POOL_ADDRESS=0x...
```

4. **Add a private key** to the backend `.env` file (use one from the hardhat node output):

```bash
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
```

### 4. MetaMask Configuration

1. **Add Local Network** to MetaMask:
   - Network Name: `Localhost 8545`
   - RPC URL: `http://127.0.0.1:8545`
   - Chain ID: `31337`
   - Currency Symbol: `ETH`

2. **Import Test Account** using one of the private keys from hardhat node output

3. **Add CQT Token** to MetaMask:
   - Token Contract Address: (use the CQT_TOKEN_ADDRESS from deployment)
   - Token Symbol: `CQT`
   - Token Decimals: `18`

## 🚀 Running the Application

### Option 1: Run All Services Together

```bash
# From the root directory
npm run dev
```

This will start:
- Backend server on `http://localhost:3001`
- Frontend application on `http://localhost:3000`

### Option 2: Run Services Separately

**Terminal 1 - Blockchain:**
```bash
cd contracts
npx hardhat node
```

**Terminal 2 - Backend:**
```bash
cd backend
npm run dev
```

**Terminal 3 - Frontend:**
```bash
cd frontend
npm start
```

## 🎮 Using the Application

1. **Open your browser** and go to `http://localhost:3000`

2. **Connect your wallet** by clicking "Connect Wallet" and selecting MetaMask

3. **Sign the authentication message** to log in

4. **Start playing!** You'll automatically receive 3 starter heroes

5. **Battle monsters** in Adventure Mode to earn CQT tokens

6. **Upgrade your heroes** using earned tokens

7. **Stake tokens** to earn passive rewards

## 🔧 Configuration

### Backend Configuration (`backend/.env`)

```bash
# Server
PORT=3001
NODE_ENV=development

# Database
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=cq_db

# Blockchain
RPC_URL=http://127.0.0.1:8545
PRIVATE_KEY=your-private-key-here
CQT_TOKEN_ADDRESS=deployed-token-address
GAME_TREASURY_ADDRESS=deployed-treasury-address
STAKING_POOL_ADDRESS=deployed-staking-address

# Game Settings
PVE_WIN_REWARD=10
PVP_WIN_REWARD=25
HERO_UPGRADE_COST=50
SKILL_UNLOCK_COST=100
```

### Frontend Configuration

Create `frontend/.env` if you need custom settings:

```bash
REACT_APP_CQT_TOKEN_ADDRESS=your-token-address
REACT_APP_STAKING_POOL_ADDRESS=your-staking-address
REACT_APP_GAME_TREASURY_ADDRESS=your-treasury-address
```

## 🧪 Testing

### Smart Contract Tests

```bash
cd contracts
npm test
```

### Backend API Tests

```bash
cd backend
npm test
```

## 📝 Game Features

### ✅ Implemented Features

- **Wallet Authentication** - MetaMask integration with signature verification
- **Hero Management** - View, upgrade heroes with stats and skills
- **PvE Battles** - Fight AI monsters with difficulty levels
- **Token Economy** - Earn and spend CQT tokens
- **Staking System** - Stake tokens for passive rewards
- **User Dashboard** - Profile, stats, and game overview
- **Leaderboard** - Player rankings and statistics
- **Database Integration** - MySQL with complete schema

### 🚧 Coming Soon Features

- **PvP Arena** - Player vs Player battles
- **Tournaments** - Competitive events with entry fees
- **Advanced Battle System** - More complex combat mechanics
- **NFT Integration** - Hero NFTs and marketplace
- **Mobile App** - React Native mobile version

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Make sure MySQL is running
   - Check database credentials in `.env`
   - Run the migration script again

2. **Contract Deployment Failed**
   - Make sure Hardhat node is running
   - Check if you have enough test ETH
   - Restart the Hardhat node and redeploy

3. **MetaMask Connection Issues**
   - Make sure you're on the correct network (Localhost 8545)
   - Try refreshing the page
   - Reset MetaMask account if needed

4. **Frontend Build Errors**
   - Delete `node_modules` and run `npm install` again
   - Make sure all dependencies are installed
   - Check for any missing environment variables

### Getting Help

If you encounter issues:

1. Check the console logs in both browser and terminal
2. Verify all environment variables are set correctly
3. Make sure all services are running
4. Try restarting all services

## 🌐 Production Deployment

For production deployment:

1. **Deploy contracts** to a real network (Polygon, Ethereum, etc.)
2. **Set up production database** (MySQL, PostgreSQL)
3. **Configure environment variables** for production
4. **Deploy backend** to a cloud service (AWS, Heroku, etc.)
5. **Deploy frontend** to a static hosting service (Vercel, Netlify)
6. **Set up monitoring** and logging

## 📚 Additional Resources

- [Hardhat Documentation](https://hardhat.org/docs)
- [React Documentation](https://reactjs.org/docs)
- [Ethers.js Documentation](https://docs.ethers.io/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [MySQL Documentation](https://dev.mysql.com/doc/)

## 🎉 Congratulations!

You now have a fully functional Web3 Play-to-Earn game running locally. Start battling, earning tokens, and building your hero collection!

Happy gaming! 🎮⚔️💰
