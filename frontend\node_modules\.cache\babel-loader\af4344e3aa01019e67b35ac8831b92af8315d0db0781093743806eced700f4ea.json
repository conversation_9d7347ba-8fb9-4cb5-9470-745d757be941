{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities for modular arithmetics and finite fields\nimport { bitMask, numberToBytesBE, numberToBytesLE, bytesToNumberBE, bytesToNumberLE, ensureBytes, validateObject } from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0),\n  _1n = BigInt(1),\n  _2n = BigInt(2),\n  _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4),\n  _5n = BigInt(5),\n  _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9),\n  _16n = BigInt(16);\n// Calculates a modulo b\nexport function mod(a, b) {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\n// TODO: use field version && remove\nexport function pow(num, power, modulo) {\n  if (modulo <= _0n || power < _0n) throw new Error('Expected power/modulo > 0');\n  if (modulo === _1n) return _0n;\n  let res = _1n;\n  while (power > _0n) {\n    if (power & _1n) res = res * num % modulo;\n    num = num * num % modulo;\n    power >>= _1n;\n  }\n  return res;\n}\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nexport function pow2(x, power, modulo) {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n// Inverses number over modulo\nexport function invert(number, modulo) {\n  if (number === _0n || modulo <= _0n) {\n    throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n  }\n  // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n,\n    y = _1n,\n    u = _1n,\n    v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P) {\n  // Legendre constant: used to calculate Legendre symbol (a | p),\n  // which denotes the value of a^((p-1)/2) (mod p).\n  // (a | p) ≡ 1    if a is a square (mod p)\n  // (a | p) ≡ -1   if a is not a square (mod p)\n  // (a | p) ≡ 0    if a ≡ 0 (mod p)\n  const legendreC = (P - _1n) / _2n;\n  let Q, S, Z;\n  // Step 1: By factoring out powers of 2 from p - 1,\n  // find q and s such that p - 1 = q*(2^s) with q odd\n  for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++);\n  // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n  for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++);\n  // Fast-path\n  if (S === 1) {\n    const p1div4 = (P + _1n) / _4n;\n    return function tonelliFast(Fp, n) {\n      const root = Fp.pow(n, p1div4);\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n  // Slow-path\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow(Fp, n) {\n    // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n    if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE)) throw new Error('Cannot find square root');\n    let r = S;\n    // TODO: will fail at Fp2/etc\n    let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n    let x = Fp.pow(n, Q1div2); // first guess at the square root\n    let b = Fp.pow(n, Q); // first guess at the fudge factor\n    while (!Fp.eql(b, Fp.ONE)) {\n      if (Fp.eql(b, Fp.ZERO)) return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n      // Find m such b^(2^m)==1\n      let m = 1;\n      for (let t2 = Fp.sqr(b); m < r; m++) {\n        if (Fp.eql(t2, Fp.ONE)) break;\n        t2 = Fp.sqr(t2); // t2 *= t2\n      }\n      // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n      const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n      g = Fp.sqr(ge); // g = ge * ge\n      x = Fp.mul(x, ge); // x *= ge\n      b = Fp.mul(b, g); // b *= g\n      r = m;\n    }\n    return x;\n  };\n}\nexport function FpSqrt(P) {\n  // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n  // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n  // P ≡ 3 (mod 4)\n  // √n = n^((P+1)/4)\n  if (P % _4n === _3n) {\n    // Not all roots possible!\n    // const ORDER =\n    //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n    // const NUM = 72057594037927816n;\n    const p1div4 = (P + _1n) / _4n;\n    return function sqrt3mod4(Fp, n) {\n      const root = Fp.pow(n, p1div4);\n      // Throw if root**2 != n\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n  // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n  if (P % _8n === _5n) {\n    const c1 = (P - _5n) / _8n;\n    return function sqrt5mod8(Fp, n) {\n      const n2 = Fp.mul(n, _2n);\n      const v = Fp.pow(n2, c1);\n      const nv = Fp.mul(n, v);\n      const i = Fp.mul(Fp.mul(nv, _2n), v);\n      const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n  // P ≡ 9 (mod 16)\n  if (P % _16n === _9n) {\n    // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n    // Means we cannot use sqrt for constants at all!\n    //\n    // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n    // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n    // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n    // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n    // sqrt = (x) => {\n    //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n    //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n    //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n    //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n    //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n    //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n    //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n    //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n    // }\n  }\n  // Other cases: Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num, modulo) => (mod(num, modulo) & _1n) === _1n;\n// prettier-ignore\nconst FIELD_FIELDS = ['create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr', 'eql', 'add', 'sub', 'mul', 'pow', 'div', 'addN', 'subN', 'mulN', 'sqrN'];\nexport function validateField(field) {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'isSafeInteger',\n    BITS: 'isSafeInteger'\n  };\n  const opts = FIELD_FIELDS.reduce((map, val) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  return validateObject(field, opts);\n}\n// Generic field functions\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow(f, num, power) {\n  // Should have same speed as pow for bigints\n  // TODO: benchmark!\n  if (power < _0n) throw new Error('Expected power > 0');\n  if (power === _0n) return f.ONE;\n  if (power === _1n) return num;\n  let p = f.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = f.mul(p, d);\n    d = f.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch(f, nums) {\n  const tmp = new Array(nums.length);\n  // Walk from first to last, multiply them by each other MOD p\n  const lastMultiplied = nums.reduce((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = acc;\n    return f.mul(acc, num);\n  }, f.ONE);\n  // Invert last element\n  const inverted = f.inv(lastMultiplied);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = f.mul(acc, tmp[i]);\n    return f.mul(acc, num);\n  }, inverted);\n  return tmp;\n}\nexport function FpDiv(f, lhs, rhs) {\n  return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare(f) {\n  const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n  return x => {\n    const p = f.pow(x, legendreConst);\n    return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n  };\n}\n// CURVE.n lengths\nexport function nLength(n, nBitLength) {\n  // Bit size, byte size of CURVE.n\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return {\n    nBitLength: _nBitLength,\n    nByteLength\n  };\n}\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(ORDER, bitLen, isLE = false, redef = {}) {\n  if (ORDER <= _0n) throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n  const {\n    nBitLength: BITS,\n    nByteLength: BYTES\n  } = nLength(ORDER, bitLen);\n  if (BYTES > 2048) throw new Error('Field lengths over 2048 bytes are not supported');\n  const sqrtP = FpSqrt(ORDER);\n  const f = Object.freeze({\n    ORDER,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    create: num => mod(num, ORDER),\n    isValid: num => {\n      if (typeof num !== 'bigint') throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: num => num === _0n,\n    isOdd: num => (num & _1n) === _1n,\n    neg: num => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n    sqr: num => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n    // Same as above, but doesn't normalize\n    sqrN: num => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n    inv: num => invert(num, ORDER),\n    sqrt: redef.sqrt || (n => sqrtP(f, n)),\n    invertBatch: lst => FpInvertBatch(f, lst),\n    // TODO: do we really need constant cmov?\n    // We don't have const-time bigints anyway, so probably will be not very useful\n    cmov: (a, b, c) => c ? b : a,\n    toBytes: num => isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES),\n    fromBytes: bytes => {\n      if (bytes.length !== BYTES) throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n      return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n    }\n  });\n  return Object.freeze(f);\n}\nexport function FpSqrtOdd(Fp, elm) {\n  if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\nexport function FpSqrtEven(Fp, elm) {\n  if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */\nexport function hashToPrivateScalar(hash, groupOrder, isLE = false) {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024) throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder) {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder) {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key, fieldOrder, isLE = false) {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024) throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n  const num = isLE ? bytesToNumberBE(key) : bytesToNumberLE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}", "map": {"version": 3, "names": ["bitMask", "numberToBytesBE", "numberToBytesLE", "bytesToNumberBE", "bytesToNumberLE", "ensureBytes", "validateObject", "_0n", "BigInt", "_1n", "_2n", "_3n", "_4n", "_5n", "_8n", "_9n", "_16n", "mod", "a", "b", "result", "pow", "num", "power", "modulo", "Error", "res", "pow2", "x", "invert", "number", "y", "u", "v", "q", "r", "m", "n", "gcd", "tonelliShanks", "P", "legendreC", "Q", "S", "Z", "p1div4", "tonelliFast", "Fp", "root", "eql", "sqr", "Q1div2", "<PERSON><PERSON><PERSON><PERSON>", "neg", "ONE", "g", "mul", "ZERO", "t2", "ge", "FpSqrt", "sqrt3mod4", "c1", "sqrt5mod8", "n2", "nv", "i", "sub", "isNegativeLE", "FIELD_FIELDS", "validateField", "field", "initial", "ORDER", "MASK", "BYTES", "BITS", "opts", "reduce", "map", "val", "FpPow", "f", "p", "d", "FpInvertBatch", "nums", "tmp", "Array", "length", "lastMultiplied", "acc", "is0", "inverted", "inv", "reduceRight", "FpDiv", "lhs", "rhs", "FpIsSquare", "legendreConst", "nLength", "nBitLength", "_nBitLength", "undefined", "toString", "nByteLength", "Math", "ceil", "Field", "bitLen", "isLE", "redef", "sqrtP", "Object", "freeze", "create", "<PERSON><PERSON><PERSON><PERSON>", "isOdd", "add", "div", "sqrN", "addN", "subN", "mulN", "sqrt", "invertBatch", "lst", "cmov", "c", "toBytes", "fromBytes", "bytes", "FpSqrtOdd", "elm", "FpSqrtEven", "hashToPrivateScalar", "hash", "groupOrder", "hashLen", "minLen", "getFieldBytesLength", "fieldOrder", "bitLength", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "mapHashToField", "key", "len", "fieldLen", "reduced"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\abstract\\modular.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Utilities for modular arithmetics and finite fields\nimport {\n  bitMask,\n  numberToBytesBE,\n  numberToBytesLE,\n  bytesToNumberBE,\n  bytesToNumberLE,\n  ensureBytes,\n  validateObject,\n} from './utils.js';\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3);\n// prettier-ignore\nconst _4n = BigInt(4), _5n = BigInt(5), _8n = BigInt(8);\n// prettier-ignore\nconst _9n = BigInt(9), _16n = BigInt(16);\n\n// Calculates a modulo b\nexport function mod(a: bigint, b: bigint): bigint {\n  const result = a % b;\n  return result >= _0n ? result : b + result;\n}\n/**\n * Efficiently raise num to power and do modular division.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n * @example\n * pow(2n, 6n, 11n) // 64n % 11n == 9n\n */\n// TODO: use field version && remove\nexport function pow(num: bigint, power: bigint, modulo: bigint): bigint {\n  if (modulo <= _0n || power < _0n) throw new Error('Expected power/modulo > 0');\n  if (modulo === _1n) return _0n;\n  let res = _1n;\n  while (power > _0n) {\n    if (power & _1n) res = (res * num) % modulo;\n    num = (num * num) % modulo;\n    power >>= _1n;\n  }\n  return res;\n}\n\n// Does x ^ (2 ^ power) mod p. pow2(30, 4) == 30 ^ (2 ^ 4)\nexport function pow2(x: bigint, power: bigint, modulo: bigint): bigint {\n  let res = x;\n  while (power-- > _0n) {\n    res *= res;\n    res %= modulo;\n  }\n  return res;\n}\n\n// Inverses number over modulo\nexport function invert(number: bigint, modulo: bigint): bigint {\n  if (number === _0n || modulo <= _0n) {\n    throw new Error(`invert: expected positive integers, got n=${number} mod=${modulo}`);\n  }\n  // Euclidean GCD https://brilliant.org/wiki/extended-euclidean-algorithm/\n  // Fermat's little theorem \"CT-like\" version inv(n) = n^(m-2) mod m is 30x slower.\n  let a = mod(number, modulo);\n  let b = modulo;\n  // prettier-ignore\n  let x = _0n, y = _1n, u = _1n, v = _0n;\n  while (a !== _0n) {\n    // JIT applies optimization if those two lines follow each other\n    const q = b / a;\n    const r = b % a;\n    const m = x - u * q;\n    const n = y - v * q;\n    // prettier-ignore\n    b = a, a = r, x = u, y = v, u = m, v = n;\n  }\n  const gcd = b;\n  if (gcd !== _1n) throw new Error('invert: does not exist');\n  return mod(x, modulo);\n}\n\n/**\n * Tonelli-Shanks square root search algorithm.\n * 1. https://eprint.iacr.org/2012/685.pdf (page 12)\n * 2. Square Roots from 1; 24, 51, 10 to Dan Shanks\n * Will start an infinite loop if field order P is not prime.\n * @param P field order\n * @returns function that takes field Fp (created from P) and number n\n */\nexport function tonelliShanks(P: bigint) {\n  // Legendre constant: used to calculate Legendre symbol (a | p),\n  // which denotes the value of a^((p-1)/2) (mod p).\n  // (a | p) ≡ 1    if a is a square (mod p)\n  // (a | p) ≡ -1   if a is not a square (mod p)\n  // (a | p) ≡ 0    if a ≡ 0 (mod p)\n  const legendreC = (P - _1n) / _2n;\n\n  let Q: bigint, S: number, Z: bigint;\n  // Step 1: By factoring out powers of 2 from p - 1,\n  // find q and s such that p - 1 = q*(2^s) with q odd\n  for (Q = P - _1n, S = 0; Q % _2n === _0n; Q /= _2n, S++);\n\n  // Step 2: Select a non-square z such that (z | p) ≡ -1 and set c ≡ zq\n  for (Z = _2n; Z < P && pow(Z, legendreC, P) !== P - _1n; Z++);\n\n  // Fast-path\n  if (S === 1) {\n    const p1div4 = (P + _1n) / _4n;\n    return function tonelliFast<T>(Fp: IField<T>, n: T) {\n      const root = Fp.pow(n, p1div4);\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // Slow-path\n  const Q1div2 = (Q + _1n) / _2n;\n  return function tonelliSlow<T>(Fp: IField<T>, n: T): T {\n    // Step 0: Check that n is indeed a square: (n | p) should not be ≡ -1\n    if (Fp.pow(n, legendreC) === Fp.neg(Fp.ONE)) throw new Error('Cannot find square root');\n    let r = S;\n    // TODO: will fail at Fp2/etc\n    let g = Fp.pow(Fp.mul(Fp.ONE, Z), Q); // will update both x and b\n    let x = Fp.pow(n, Q1div2); // first guess at the square root\n    let b = Fp.pow(n, Q); // first guess at the fudge factor\n\n    while (!Fp.eql(b, Fp.ONE)) {\n      if (Fp.eql(b, Fp.ZERO)) return Fp.ZERO; // https://en.wikipedia.org/wiki/Tonelli%E2%80%93Shanks_algorithm (4. If t = 0, return r = 0)\n      // Find m such b^(2^m)==1\n      let m = 1;\n      for (let t2 = Fp.sqr(b); m < r; m++) {\n        if (Fp.eql(t2, Fp.ONE)) break;\n        t2 = Fp.sqr(t2); // t2 *= t2\n      }\n      // NOTE: r-m-1 can be bigger than 32, need to convert to bigint before shift, otherwise there will be overflow\n      const ge = Fp.pow(g, _1n << BigInt(r - m - 1)); // ge = 2^(r-m-1)\n      g = Fp.sqr(ge); // g = ge * ge\n      x = Fp.mul(x, ge); // x *= ge\n      b = Fp.mul(b, g); // b *= g\n      r = m;\n    }\n    return x;\n  };\n}\n\nexport function FpSqrt(P: bigint) {\n  // NOTE: different algorithms can give different roots, it is up to user to decide which one they want.\n  // For example there is FpSqrtOdd/FpSqrtEven to choice root based on oddness (used for hash-to-curve).\n\n  // P ≡ 3 (mod 4)\n  // √n = n^((P+1)/4)\n  if (P % _4n === _3n) {\n    // Not all roots possible!\n    // const ORDER =\n    //   0x1a0111ea397fe69a4b1ba7b6434bacd764774b84f38512bf6730d2a0f6b0f6241eabfffeb153ffffb9feffffffffaaabn;\n    // const NUM = 72057594037927816n;\n    const p1div4 = (P + _1n) / _4n;\n    return function sqrt3mod4<T>(Fp: IField<T>, n: T) {\n      const root = Fp.pow(n, p1div4);\n      // Throw if root**2 != n\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // Atkin algorithm for q ≡ 5 (mod 8), https://eprint.iacr.org/2012/685.pdf (page 10)\n  if (P % _8n === _5n) {\n    const c1 = (P - _5n) / _8n;\n    return function sqrt5mod8<T>(Fp: IField<T>, n: T) {\n      const n2 = Fp.mul(n, _2n);\n      const v = Fp.pow(n2, c1);\n      const nv = Fp.mul(n, v);\n      const i = Fp.mul(Fp.mul(nv, _2n), v);\n      const root = Fp.mul(nv, Fp.sub(i, Fp.ONE));\n      if (!Fp.eql(Fp.sqr(root), n)) throw new Error('Cannot find square root');\n      return root;\n    };\n  }\n\n  // P ≡ 9 (mod 16)\n  if (P % _16n === _9n) {\n    // NOTE: tonelli is too slow for bls-Fp2 calculations even on start\n    // Means we cannot use sqrt for constants at all!\n    //\n    // const c1 = Fp.sqrt(Fp.negate(Fp.ONE)); //  1. c1 = sqrt(-1) in F, i.e., (c1^2) == -1 in F\n    // const c2 = Fp.sqrt(c1);                //  2. c2 = sqrt(c1) in F, i.e., (c2^2) == c1 in F\n    // const c3 = Fp.sqrt(Fp.negate(c1));     //  3. c3 = sqrt(-c1) in F, i.e., (c3^2) == -c1 in F\n    // const c4 = (P + _7n) / _16n;           //  4. c4 = (q + 7) / 16        # Integer arithmetic\n    // sqrt = (x) => {\n    //   let tv1 = Fp.pow(x, c4);             //  1. tv1 = x^c4\n    //   let tv2 = Fp.mul(c1, tv1);           //  2. tv2 = c1 * tv1\n    //   const tv3 = Fp.mul(c2, tv1);         //  3. tv3 = c2 * tv1\n    //   let tv4 = Fp.mul(c3, tv1);           //  4. tv4 = c3 * tv1\n    //   const e1 = Fp.equals(Fp.square(tv2), x); //  5.  e1 = (tv2^2) == x\n    //   const e2 = Fp.equals(Fp.square(tv3), x); //  6.  e2 = (tv3^2) == x\n    //   tv1 = Fp.cmov(tv1, tv2, e1); //  7. tv1 = CMOV(tv1, tv2, e1)  # Select tv2 if (tv2^2) == x\n    //   tv2 = Fp.cmov(tv4, tv3, e2); //  8. tv2 = CMOV(tv4, tv3, e2)  # Select tv3 if (tv3^2) == x\n    //   const e3 = Fp.equals(Fp.square(tv2), x); //  9.  e3 = (tv2^2) == x\n    //   return Fp.cmov(tv1, tv2, e3); //  10.  z = CMOV(tv1, tv2, e3)  # Select the sqrt from tv1 and tv2\n    // }\n  }\n\n  // Other cases: Tonelli-Shanks algorithm\n  return tonelliShanks(P);\n}\n\n// Little-endian check for first LE bit (last BE bit);\nexport const isNegativeLE = (num: bigint, modulo: bigint) => (mod(num, modulo) & _1n) === _1n;\n\n// Field is not always over prime: for example, Fp2 has ORDER(q)=p^m\nexport interface IField<T> {\n  ORDER: bigint;\n  BYTES: number;\n  BITS: number;\n  MASK: bigint;\n  ZERO: T;\n  ONE: T;\n  // 1-arg\n  create: (num: T) => T;\n  isValid: (num: T) => boolean;\n  is0: (num: T) => boolean;\n  neg(num: T): T;\n  inv(num: T): T;\n  sqrt(num: T): T;\n  sqr(num: T): T;\n  // 2-args\n  eql(lhs: T, rhs: T): boolean;\n  add(lhs: T, rhs: T): T;\n  sub(lhs: T, rhs: T): T;\n  mul(lhs: T, rhs: T | bigint): T;\n  pow(lhs: T, power: bigint): T;\n  div(lhs: T, rhs: T | bigint): T;\n  // N for NonNormalized (for now)\n  addN(lhs: T, rhs: T): T;\n  subN(lhs: T, rhs: T): T;\n  mulN(lhs: T, rhs: T | bigint): T;\n  sqrN(num: T): T;\n\n  // Optional\n  // Should be same as sgn0 function in\n  // [RFC9380](https://www.rfc-editor.org/rfc/rfc9380#section-4.1).\n  // NOTE: sgn0 is 'negative in LE', which is same as odd. And negative in LE is kinda strange definition anyway.\n  isOdd?(num: T): boolean; // Odd instead of even since we have it for Fp2\n  // legendre?(num: T): T;\n  pow(lhs: T, power: bigint): T;\n  invertBatch: (lst: T[]) => T[];\n  toBytes(num: T): Uint8Array;\n  fromBytes(bytes: Uint8Array): T;\n  // If c is False, CMOV returns a, otherwise it returns b.\n  cmov(a: T, b: T, c: boolean): T;\n}\n// prettier-ignore\nconst FIELD_FIELDS = [\n  'create', 'isValid', 'is0', 'neg', 'inv', 'sqrt', 'sqr',\n  'eql', 'add', 'sub', 'mul', 'pow', 'div',\n  'addN', 'subN', 'mulN', 'sqrN'\n] as const;\nexport function validateField<T>(field: IField<T>) {\n  const initial = {\n    ORDER: 'bigint',\n    MASK: 'bigint',\n    BYTES: 'isSafeInteger',\n    BITS: 'isSafeInteger',\n  } as Record<string, string>;\n  const opts = FIELD_FIELDS.reduce((map, val: string) => {\n    map[val] = 'function';\n    return map;\n  }, initial);\n  return validateObject(field, opts);\n}\n\n// Generic field functions\n\n/**\n * Same as `pow` but for Fp: non-constant-time.\n * Unsafe in some contexts: uses ladder, so can expose bigint bits.\n */\nexport function FpPow<T>(f: IField<T>, num: T, power: bigint): T {\n  // Should have same speed as pow for bigints\n  // TODO: benchmark!\n  if (power < _0n) throw new Error('Expected power > 0');\n  if (power === _0n) return f.ONE;\n  if (power === _1n) return num;\n  let p = f.ONE;\n  let d = num;\n  while (power > _0n) {\n    if (power & _1n) p = f.mul(p, d);\n    d = f.sqr(d);\n    power >>= _1n;\n  }\n  return p;\n}\n\n/**\n * Efficiently invert an array of Field elements.\n * `inv(0)` will return `undefined` here: make sure to throw an error.\n */\nexport function FpInvertBatch<T>(f: IField<T>, nums: T[]): T[] {\n  const tmp = new Array(nums.length);\n  // Walk from first to last, multiply them by each other MOD p\n  const lastMultiplied = nums.reduce((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = acc;\n    return f.mul(acc, num);\n  }, f.ONE);\n  // Invert last element\n  const inverted = f.inv(lastMultiplied);\n  // Walk from last to first, multiply them by inverted each other MOD p\n  nums.reduceRight((acc, num, i) => {\n    if (f.is0(num)) return acc;\n    tmp[i] = f.mul(acc, tmp[i]);\n    return f.mul(acc, num);\n  }, inverted);\n  return tmp;\n}\n\nexport function FpDiv<T>(f: IField<T>, lhs: T, rhs: T | bigint): T {\n  return f.mul(lhs, typeof rhs === 'bigint' ? invert(rhs, f.ORDER) : f.inv(rhs));\n}\n\n// This function returns True whenever the value x is a square in the field F.\nexport function FpIsSquare<T>(f: IField<T>) {\n  const legendreConst = (f.ORDER - _1n) / _2n; // Integer arithmetic\n  return (x: T): boolean => {\n    const p = f.pow(x, legendreConst);\n    return f.eql(p, f.ZERO) || f.eql(p, f.ONE);\n  };\n}\n\n// CURVE.n lengths\nexport function nLength(n: bigint, nBitLength?: number) {\n  // Bit size, byte size of CURVE.n\n  const _nBitLength = nBitLength !== undefined ? nBitLength : n.toString(2).length;\n  const nByteLength = Math.ceil(_nBitLength / 8);\n  return { nBitLength: _nBitLength, nByteLength };\n}\n\ntype FpField = IField<bigint> & Required<Pick<IField<bigint>, 'isOdd'>>;\n/**\n * Initializes a finite field over prime. **Non-primes are not supported.**\n * Do not init in loop: slow. Very fragile: always run a benchmark on a change.\n * Major performance optimizations:\n * * a) denormalized operations like mulN instead of mul\n * * b) same object shape: never add or remove keys\n * * c) Object.freeze\n * @param ORDER prime positive bigint\n * @param bitLen how many bits the field consumes\n * @param isLE (def: false) if encoding / decoding should be in little-endian\n * @param redef optional faster redefinitions of sqrt and other methods\n */\nexport function Field(\n  ORDER: bigint,\n  bitLen?: number,\n  isLE = false,\n  redef: Partial<IField<bigint>> = {}\n): Readonly<FpField> {\n  if (ORDER <= _0n) throw new Error(`Expected Field ORDER > 0, got ${ORDER}`);\n  const { nBitLength: BITS, nByteLength: BYTES } = nLength(ORDER, bitLen);\n  if (BYTES > 2048) throw new Error('Field lengths over 2048 bytes are not supported');\n  const sqrtP = FpSqrt(ORDER);\n  const f: Readonly<FpField> = Object.freeze({\n    ORDER,\n    BITS,\n    BYTES,\n    MASK: bitMask(BITS),\n    ZERO: _0n,\n    ONE: _1n,\n    create: (num) => mod(num, ORDER),\n    isValid: (num) => {\n      if (typeof num !== 'bigint')\n        throw new Error(`Invalid field element: expected bigint, got ${typeof num}`);\n      return _0n <= num && num < ORDER; // 0 is valid element, but it's not invertible\n    },\n    is0: (num) => num === _0n,\n    isOdd: (num) => (num & _1n) === _1n,\n    neg: (num) => mod(-num, ORDER),\n    eql: (lhs, rhs) => lhs === rhs,\n\n    sqr: (num) => mod(num * num, ORDER),\n    add: (lhs, rhs) => mod(lhs + rhs, ORDER),\n    sub: (lhs, rhs) => mod(lhs - rhs, ORDER),\n    mul: (lhs, rhs) => mod(lhs * rhs, ORDER),\n    pow: (num, power) => FpPow(f, num, power),\n    div: (lhs, rhs) => mod(lhs * invert(rhs, ORDER), ORDER),\n\n    // Same as above, but doesn't normalize\n    sqrN: (num) => num * num,\n    addN: (lhs, rhs) => lhs + rhs,\n    subN: (lhs, rhs) => lhs - rhs,\n    mulN: (lhs, rhs) => lhs * rhs,\n\n    inv: (num) => invert(num, ORDER),\n    sqrt: redef.sqrt || ((n) => sqrtP(f, n)),\n    invertBatch: (lst) => FpInvertBatch(f, lst),\n    // TODO: do we really need constant cmov?\n    // We don't have const-time bigints anyway, so probably will be not very useful\n    cmov: (a, b, c) => (c ? b : a),\n    toBytes: (num) => (isLE ? numberToBytesLE(num, BYTES) : numberToBytesBE(num, BYTES)),\n    fromBytes: (bytes) => {\n      if (bytes.length !== BYTES)\n        throw new Error(`Fp.fromBytes: expected ${BYTES}, got ${bytes.length}`);\n      return isLE ? bytesToNumberLE(bytes) : bytesToNumberBE(bytes);\n    },\n  } as FpField);\n  return Object.freeze(f);\n}\n\nexport function FpSqrtOdd<T>(Fp: IField<T>, elm: T) {\n  if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? root : Fp.neg(root);\n}\n\nexport function FpSqrtEven<T>(Fp: IField<T>, elm: T) {\n  if (!Fp.isOdd) throw new Error(`Field doesn't have isOdd`);\n  const root = Fp.sqrt(elm);\n  return Fp.isOdd(root) ? Fp.neg(root) : root;\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Same as mapKeyToField, but accepts less bytes (40 instead of 48 for 32-byte field).\n * Which makes it slightly more biased, less secure.\n * @deprecated use mapKeyToField instead\n */\nexport function hashToPrivateScalar(\n  hash: string | Uint8Array,\n  groupOrder: bigint,\n  isLE = false\n): bigint {\n  hash = ensureBytes('privateHash', hash);\n  const hashLen = hash.length;\n  const minLen = nLength(groupOrder).nByteLength + 8;\n  if (minLen < 24 || hashLen < minLen || hashLen > 1024)\n    throw new Error(`hashToPrivateScalar: expected ${minLen}-1024 bytes of input, got ${hashLen}`);\n  const num = isLE ? bytesToNumberLE(hash) : bytesToNumberBE(hash);\n  return mod(num, groupOrder - _1n) + _1n;\n}\n\n/**\n * Returns total number of bytes consumed by the field element.\n * For example, 32 bytes for usual 256-bit weierstrass curve.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of field\n */\nexport function getFieldBytesLength(fieldOrder: bigint): number {\n  if (typeof fieldOrder !== 'bigint') throw new Error('field order must be bigint');\n  const bitLength = fieldOrder.toString(2).length;\n  return Math.ceil(bitLength / 8);\n}\n\n/**\n * Returns minimal amount of bytes that can be safely reduced\n * by field order.\n * Should be 2^-128 for 128-bit curve such as P256.\n * @param fieldOrder number of field elements, usually CURVE.n\n * @returns byte length of target hash\n */\nexport function getMinHashLength(fieldOrder: bigint): number {\n  const length = getFieldBytesLength(fieldOrder);\n  return length + Math.ceil(length / 2);\n}\n\n/**\n * \"Constant-time\" private key generation utility.\n * Can take (n + n/2) or more bytes of uniform input e.g. from CSPRNG or KDF\n * and convert them into private scalar, with the modulo bias being negligible.\n * Needs at least 48 bytes of input for 32-byte private key.\n * https://research.kudelskisecurity.com/2020/07/28/the-definitive-guide-to-modulo-bias-and-how-to-avoid-it/\n * FIPS 186-5, A.2 https://csrc.nist.gov/publications/detail/fips/186/5/final\n * RFC 9380, https://www.rfc-editor.org/rfc/rfc9380#section-5\n * @param hash hash output from SHA3 or a similar function\n * @param groupOrder size of subgroup - (e.g. secp256k1.CURVE.n)\n * @param isLE interpret hash bytes as LE num\n * @returns valid private scalar\n */\nexport function mapHashToField(key: Uint8Array, fieldOrder: bigint, isLE = false): Uint8Array {\n  const len = key.length;\n  const fieldLen = getFieldBytesLength(fieldOrder);\n  const minLen = getMinHashLength(fieldOrder);\n  // No small numbers: need to understand bias story. No huge numbers: easier to detect JS timings.\n  if (len < 16 || len < minLen || len > 1024)\n    throw new Error(`expected ${minLen}-1024 bytes of input, got ${len}`);\n  const num = isLE ? bytesToNumberBE(key) : bytesToNumberLE(key);\n  // `mod(x, 11)` can sometimes produce 0. `mod(x, 10) + 1` is the same, but no 0\n  const reduced = mod(num, fieldOrder - _1n) + _1n;\n  return isLE ? numberToBytesLE(reduced, fieldLen) : numberToBytesBE(reduced, fieldLen);\n}\n"], "mappings": "AAAA;AACA;AACA,SACEA,OAAO,EACPC,eAAe,EACfC,eAAe,EACfC,eAAe,EACfC,eAAe,EACfC,WAAW,EACXC,cAAc,QACT,YAAY;AACnB;AACA,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;EAAEG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;AACxE;AACA,MAAMI,GAAG,GAAGJ,MAAM,CAAC,CAAC,CAAC;EAAEK,GAAG,GAAGL,MAAM,CAAC,CAAC,CAAC;EAAEM,GAAG,GAAGN,MAAM,CAAC,CAAC,CAAC;AACvD;AACA,MAAMO,GAAG,GAAGP,MAAM,CAAC,CAAC,CAAC;EAAEQ,IAAI,GAAGR,MAAM,CAAC,EAAE,CAAC;AAExC;AACA,OAAM,SAAUS,GAAGA,CAACC,CAAS,EAAEC,CAAS;EACtC,MAAMC,MAAM,GAAGF,CAAC,GAAGC,CAAC;EACpB,OAAOC,MAAM,IAAIb,GAAG,GAAGa,MAAM,GAAGD,CAAC,GAAGC,MAAM;AAC5C;AACA;;;;;;AAMA;AACA,OAAM,SAAUC,GAAGA,CAACC,GAAW,EAAEC,KAAa,EAAEC,MAAc;EAC5D,IAAIA,MAAM,IAAIjB,GAAG,IAAIgB,KAAK,GAAGhB,GAAG,EAAE,MAAM,IAAIkB,KAAK,CAAC,2BAA2B,CAAC;EAC9E,IAAID,MAAM,KAAKf,GAAG,EAAE,OAAOF,GAAG;EAC9B,IAAImB,GAAG,GAAGjB,GAAG;EACb,OAAOc,KAAK,GAAGhB,GAAG,EAAE;IAClB,IAAIgB,KAAK,GAAGd,GAAG,EAAEiB,GAAG,GAAIA,GAAG,GAAGJ,GAAG,GAAIE,MAAM;IAC3CF,GAAG,GAAIA,GAAG,GAAGA,GAAG,GAAIE,MAAM;IAC1BD,KAAK,KAAKd,GAAG;;EAEf,OAAOiB,GAAG;AACZ;AAEA;AACA,OAAM,SAAUC,IAAIA,CAACC,CAAS,EAAEL,KAAa,EAAEC,MAAc;EAC3D,IAAIE,GAAG,GAAGE,CAAC;EACX,OAAOL,KAAK,EAAE,GAAGhB,GAAG,EAAE;IACpBmB,GAAG,IAAIA,GAAG;IACVA,GAAG,IAAIF,MAAM;;EAEf,OAAOE,GAAG;AACZ;AAEA;AACA,OAAM,SAAUG,MAAMA,CAACC,MAAc,EAAEN,MAAc;EACnD,IAAIM,MAAM,KAAKvB,GAAG,IAAIiB,MAAM,IAAIjB,GAAG,EAAE;IACnC,MAAM,IAAIkB,KAAK,CAAC,6CAA6CK,MAAM,QAAQN,MAAM,EAAE,CAAC;;EAEtF;EACA;EACA,IAAIN,CAAC,GAAGD,GAAG,CAACa,MAAM,EAAEN,MAAM,CAAC;EAC3B,IAAIL,CAAC,GAAGK,MAAM;EACd;EACA,IAAII,CAAC,GAAGrB,GAAG;IAAEwB,CAAC,GAAGtB,GAAG;IAAEuB,CAAC,GAAGvB,GAAG;IAAEwB,CAAC,GAAG1B,GAAG;EACtC,OAAOW,CAAC,KAAKX,GAAG,EAAE;IAChB;IACA,MAAM2B,CAAC,GAAGf,CAAC,GAAGD,CAAC;IACf,MAAMiB,CAAC,GAAGhB,CAAC,GAAGD,CAAC;IACf,MAAMkB,CAAC,GAAGR,CAAC,GAAGI,CAAC,GAAGE,CAAC;IACnB,MAAMG,CAAC,GAAGN,CAAC,GAAGE,CAAC,GAAGC,CAAC;IACnB;IACAf,CAAC,GAAGD,CAAC,EAAEA,CAAC,GAAGiB,CAAC,EAAEP,CAAC,GAAGI,CAAC,EAAED,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGI,CAAC,EAAEH,CAAC,GAAGI,CAAC;;EAE1C,MAAMC,GAAG,GAAGnB,CAAC;EACb,IAAImB,GAAG,KAAK7B,GAAG,EAAE,MAAM,IAAIgB,KAAK,CAAC,wBAAwB,CAAC;EAC1D,OAAOR,GAAG,CAACW,CAAC,EAAEJ,MAAM,CAAC;AACvB;AAEA;;;;;;;;AAQA,OAAM,SAAUe,aAAaA,CAACC,CAAS;EACrC;EACA;EACA;EACA;EACA;EACA,MAAMC,SAAS,GAAG,CAACD,CAAC,GAAG/B,GAAG,IAAIC,GAAG;EAEjC,IAAIgC,CAAS,EAAEC,CAAS,EAAEC,CAAS;EACnC;EACA;EACA,KAAKF,CAAC,GAAGF,CAAC,GAAG/B,GAAG,EAAEkC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGhC,GAAG,KAAKH,GAAG,EAAEmC,CAAC,IAAIhC,GAAG,EAAEiC,CAAC,EAAE,CAAC;EAExD;EACA,KAAKC,CAAC,GAAGlC,GAAG,EAAEkC,CAAC,GAAGJ,CAAC,IAAInB,GAAG,CAACuB,CAAC,EAAEH,SAAS,EAAED,CAAC,CAAC,KAAKA,CAAC,GAAG/B,GAAG,EAAEmC,CAAC,EAAE,CAAC;EAE7D;EACA,IAAID,CAAC,KAAK,CAAC,EAAE;IACX,MAAME,MAAM,GAAG,CAACL,CAAC,GAAG/B,GAAG,IAAIG,GAAG;IAC9B,OAAO,SAASkC,WAAWA,CAAIC,EAAa,EAAEV,CAAI;MAChD,MAAMW,IAAI,GAAGD,EAAE,CAAC1B,GAAG,CAACgB,CAAC,EAAEQ,MAAM,CAAC;MAC9B,IAAI,CAACE,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACF,IAAI,CAAC,EAAEX,CAAC,CAAC,EAAE,MAAM,IAAIZ,KAAK,CAAC,yBAAyB,CAAC;MACxE,OAAOuB,IAAI;IACb,CAAC;;EAGH;EACA,MAAMG,MAAM,GAAG,CAACT,CAAC,GAAGjC,GAAG,IAAIC,GAAG;EAC9B,OAAO,SAAS0C,WAAWA,CAAIL,EAAa,EAAEV,CAAI;IAChD;IACA,IAAIU,EAAE,CAAC1B,GAAG,CAACgB,CAAC,EAAEI,SAAS,CAAC,KAAKM,EAAE,CAACM,GAAG,CAACN,EAAE,CAACO,GAAG,CAAC,EAAE,MAAM,IAAI7B,KAAK,CAAC,yBAAyB,CAAC;IACvF,IAAIU,CAAC,GAAGQ,CAAC;IACT;IACA,IAAIY,CAAC,GAAGR,EAAE,CAAC1B,GAAG,CAAC0B,EAAE,CAACS,GAAG,CAACT,EAAE,CAACO,GAAG,EAAEV,CAAC,CAAC,EAAEF,CAAC,CAAC,CAAC,CAAC;IACtC,IAAId,CAAC,GAAGmB,EAAE,CAAC1B,GAAG,CAACgB,CAAC,EAAEc,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIhC,CAAC,GAAG4B,EAAE,CAAC1B,GAAG,CAACgB,CAAC,EAAEK,CAAC,CAAC,CAAC,CAAC;IAEtB,OAAO,CAACK,EAAE,CAACE,GAAG,CAAC9B,CAAC,EAAE4B,EAAE,CAACO,GAAG,CAAC,EAAE;MACzB,IAAIP,EAAE,CAACE,GAAG,CAAC9B,CAAC,EAAE4B,EAAE,CAACU,IAAI,CAAC,EAAE,OAAOV,EAAE,CAACU,IAAI,CAAC,CAAC;MACxC;MACA,IAAIrB,CAAC,GAAG,CAAC;MACT,KAAK,IAAIsB,EAAE,GAAGX,EAAE,CAACG,GAAG,CAAC/B,CAAC,CAAC,EAAEiB,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;QACnC,IAAIW,EAAE,CAACE,GAAG,CAACS,EAAE,EAAEX,EAAE,CAACO,GAAG,CAAC,EAAE;QACxBI,EAAE,GAAGX,EAAE,CAACG,GAAG,CAACQ,EAAE,CAAC,CAAC,CAAC;;MAEnB;MACA,MAAMC,EAAE,GAAGZ,EAAE,CAAC1B,GAAG,CAACkC,CAAC,EAAE9C,GAAG,IAAID,MAAM,CAAC2B,CAAC,GAAGC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChDmB,CAAC,GAAGR,EAAE,CAACG,GAAG,CAACS,EAAE,CAAC,CAAC,CAAC;MAChB/B,CAAC,GAAGmB,EAAE,CAACS,GAAG,CAAC5B,CAAC,EAAE+B,EAAE,CAAC,CAAC,CAAC;MACnBxC,CAAC,GAAG4B,EAAE,CAACS,GAAG,CAACrC,CAAC,EAAEoC,CAAC,CAAC,CAAC,CAAC;MAClBpB,CAAC,GAAGC,CAAC;;IAEP,OAAOR,CAAC;EACV,CAAC;AACH;AAEA,OAAM,SAAUgC,MAAMA,CAACpB,CAAS;EAC9B;EACA;EAEA;EACA;EACA,IAAIA,CAAC,GAAG5B,GAAG,KAAKD,GAAG,EAAE;IACnB;IACA;IACA;IACA;IACA,MAAMkC,MAAM,GAAG,CAACL,CAAC,GAAG/B,GAAG,IAAIG,GAAG;IAC9B,OAAO,SAASiD,SAASA,CAAId,EAAa,EAAEV,CAAI;MAC9C,MAAMW,IAAI,GAAGD,EAAE,CAAC1B,GAAG,CAACgB,CAAC,EAAEQ,MAAM,CAAC;MAC9B;MACA,IAAI,CAACE,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACF,IAAI,CAAC,EAAEX,CAAC,CAAC,EAAE,MAAM,IAAIZ,KAAK,CAAC,yBAAyB,CAAC;MACxE,OAAOuB,IAAI;IACb,CAAC;;EAGH;EACA,IAAIR,CAAC,GAAG1B,GAAG,KAAKD,GAAG,EAAE;IACnB,MAAMiD,EAAE,GAAG,CAACtB,CAAC,GAAG3B,GAAG,IAAIC,GAAG;IAC1B,OAAO,SAASiD,SAASA,CAAIhB,EAAa,EAAEV,CAAI;MAC9C,MAAM2B,EAAE,GAAGjB,EAAE,CAACS,GAAG,CAACnB,CAAC,EAAE3B,GAAG,CAAC;MACzB,MAAMuB,CAAC,GAAGc,EAAE,CAAC1B,GAAG,CAAC2C,EAAE,EAAEF,EAAE,CAAC;MACxB,MAAMG,EAAE,GAAGlB,EAAE,CAACS,GAAG,CAACnB,CAAC,EAAEJ,CAAC,CAAC;MACvB,MAAMiC,CAAC,GAAGnB,EAAE,CAACS,GAAG,CAACT,EAAE,CAACS,GAAG,CAACS,EAAE,EAAEvD,GAAG,CAAC,EAAEuB,CAAC,CAAC;MACpC,MAAMe,IAAI,GAAGD,EAAE,CAACS,GAAG,CAACS,EAAE,EAAElB,EAAE,CAACoB,GAAG,CAACD,CAAC,EAAEnB,EAAE,CAACO,GAAG,CAAC,CAAC;MAC1C,IAAI,CAACP,EAAE,CAACE,GAAG,CAACF,EAAE,CAACG,GAAG,CAACF,IAAI,CAAC,EAAEX,CAAC,CAAC,EAAE,MAAM,IAAIZ,KAAK,CAAC,yBAAyB,CAAC;MACxE,OAAOuB,IAAI;IACb,CAAC;;EAGH;EACA,IAAIR,CAAC,GAAGxB,IAAI,KAAKD,GAAG,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGF;EACA,OAAOwB,aAAa,CAACC,CAAC,CAAC;AACzB;AAEA;AACA,OAAO,MAAM4B,YAAY,GAAGA,CAAC9C,GAAW,EAAEE,MAAc,KAAK,CAACP,GAAG,CAACK,GAAG,EAAEE,MAAM,CAAC,GAAGf,GAAG,MAAMA,GAAG;AA4C7F;AACA,MAAM4D,YAAY,GAAG,CACnB,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EACvD,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CACtB;AACV,OAAM,SAAUC,aAAaA,CAAIC,KAAgB;EAC/C,MAAMC,OAAO,GAAG;IACdC,KAAK,EAAE,QAAQ;IACfC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE,eAAe;IACtBC,IAAI,EAAE;GACmB;EAC3B,MAAMC,IAAI,GAAGR,YAAY,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAW,KAAI;IACpDD,GAAG,CAACC,GAAG,CAAC,GAAG,UAAU;IACrB,OAAOD,GAAG;EACZ,CAAC,EAAEP,OAAO,CAAC;EACX,OAAOlE,cAAc,CAACiE,KAAK,EAAEM,IAAI,CAAC;AACpC;AAEA;AAEA;;;;AAIA,OAAM,SAAUI,KAAKA,CAAIC,CAAY,EAAE5D,GAAM,EAAEC,KAAa;EAC1D;EACA;EACA,IAAIA,KAAK,GAAGhB,GAAG,EAAE,MAAM,IAAIkB,KAAK,CAAC,oBAAoB,CAAC;EACtD,IAAIF,KAAK,KAAKhB,GAAG,EAAE,OAAO2E,CAAC,CAAC5B,GAAG;EAC/B,IAAI/B,KAAK,KAAKd,GAAG,EAAE,OAAOa,GAAG;EAC7B,IAAI6D,CAAC,GAAGD,CAAC,CAAC5B,GAAG;EACb,IAAI8B,CAAC,GAAG9D,GAAG;EACX,OAAOC,KAAK,GAAGhB,GAAG,EAAE;IAClB,IAAIgB,KAAK,GAAGd,GAAG,EAAE0E,CAAC,GAAGD,CAAC,CAAC1B,GAAG,CAAC2B,CAAC,EAAEC,CAAC,CAAC;IAChCA,CAAC,GAAGF,CAAC,CAAChC,GAAG,CAACkC,CAAC,CAAC;IACZ7D,KAAK,KAAKd,GAAG;;EAEf,OAAO0E,CAAC;AACV;AAEA;;;;AAIA,OAAM,SAAUE,aAAaA,CAAIH,CAAY,EAAEI,IAAS;EACtD,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAACF,IAAI,CAACG,MAAM,CAAC;EAClC;EACA,MAAMC,cAAc,GAAGJ,IAAI,CAACR,MAAM,CAAC,CAACa,GAAG,EAAErE,GAAG,EAAE4C,CAAC,KAAI;IACjD,IAAIgB,CAAC,CAACU,GAAG,CAACtE,GAAG,CAAC,EAAE,OAAOqE,GAAG;IAC1BJ,GAAG,CAACrB,CAAC,CAAC,GAAGyB,GAAG;IACZ,OAAOT,CAAC,CAAC1B,GAAG,CAACmC,GAAG,EAAErE,GAAG,CAAC;EACxB,CAAC,EAAE4D,CAAC,CAAC5B,GAAG,CAAC;EACT;EACA,MAAMuC,QAAQ,GAAGX,CAAC,CAACY,GAAG,CAACJ,cAAc,CAAC;EACtC;EACAJ,IAAI,CAACS,WAAW,CAAC,CAACJ,GAAG,EAAErE,GAAG,EAAE4C,CAAC,KAAI;IAC/B,IAAIgB,CAAC,CAACU,GAAG,CAACtE,GAAG,CAAC,EAAE,OAAOqE,GAAG;IAC1BJ,GAAG,CAACrB,CAAC,CAAC,GAAGgB,CAAC,CAAC1B,GAAG,CAACmC,GAAG,EAAEJ,GAAG,CAACrB,CAAC,CAAC,CAAC;IAC3B,OAAOgB,CAAC,CAAC1B,GAAG,CAACmC,GAAG,EAAErE,GAAG,CAAC;EACxB,CAAC,EAAEuE,QAAQ,CAAC;EACZ,OAAON,GAAG;AACZ;AAEA,OAAM,SAAUS,KAAKA,CAAId,CAAY,EAAEe,GAAM,EAAEC,GAAe;EAC5D,OAAOhB,CAAC,CAAC1B,GAAG,CAACyC,GAAG,EAAE,OAAOC,GAAG,KAAK,QAAQ,GAAGrE,MAAM,CAACqE,GAAG,EAAEhB,CAAC,CAACT,KAAK,CAAC,GAAGS,CAAC,CAACY,GAAG,CAACI,GAAG,CAAC,CAAC;AAChF;AAEA;AACA,OAAM,SAAUC,UAAUA,CAAIjB,CAAY;EACxC,MAAMkB,aAAa,GAAG,CAAClB,CAAC,CAACT,KAAK,GAAGhE,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7C,OAAQkB,CAAI,IAAa;IACvB,MAAMuD,CAAC,GAAGD,CAAC,CAAC7D,GAAG,CAACO,CAAC,EAAEwE,aAAa,CAAC;IACjC,OAAOlB,CAAC,CAACjC,GAAG,CAACkC,CAAC,EAAED,CAAC,CAACzB,IAAI,CAAC,IAAIyB,CAAC,CAACjC,GAAG,CAACkC,CAAC,EAAED,CAAC,CAAC5B,GAAG,CAAC;EAC5C,CAAC;AACH;AAEA;AACA,OAAM,SAAU+C,OAAOA,CAAChE,CAAS,EAAEiE,UAAmB;EACpD;EACA,MAAMC,WAAW,GAAGD,UAAU,KAAKE,SAAS,GAAGF,UAAU,GAAGjE,CAAC,CAACoE,QAAQ,CAAC,CAAC,CAAC,CAAChB,MAAM;EAChF,MAAMiB,WAAW,GAAGC,IAAI,CAACC,IAAI,CAACL,WAAW,GAAG,CAAC,CAAC;EAC9C,OAAO;IAAED,UAAU,EAAEC,WAAW;IAAEG;EAAW,CAAE;AACjD;AAGA;;;;;;;;;;;;AAYA,OAAM,SAAUG,KAAKA,CACnBpC,KAAa,EACbqC,MAAe,EACfC,IAAI,GAAG,KAAK,EACZC,KAAA,GAAiC,EAAE;EAEnC,IAAIvC,KAAK,IAAIlE,GAAG,EAAE,MAAM,IAAIkB,KAAK,CAAC,iCAAiCgD,KAAK,EAAE,CAAC;EAC3E,MAAM;IAAE6B,UAAU,EAAE1B,IAAI;IAAE8B,WAAW,EAAE/B;EAAK,CAAE,GAAG0B,OAAO,CAAC5B,KAAK,EAAEqC,MAAM,CAAC;EACvE,IAAInC,KAAK,GAAG,IAAI,EAAE,MAAM,IAAIlD,KAAK,CAAC,iDAAiD,CAAC;EACpF,MAAMwF,KAAK,GAAGrD,MAAM,CAACa,KAAK,CAAC;EAC3B,MAAMS,CAAC,GAAsBgC,MAAM,CAACC,MAAM,CAAC;IACzC1C,KAAK;IACLG,IAAI;IACJD,KAAK;IACLD,IAAI,EAAE1E,OAAO,CAAC4E,IAAI,CAAC;IACnBnB,IAAI,EAAElD,GAAG;IACT+C,GAAG,EAAE7C,GAAG;IACR2G,MAAM,EAAG9F,GAAG,IAAKL,GAAG,CAACK,GAAG,EAAEmD,KAAK,CAAC;IAChC4C,OAAO,EAAG/F,GAAG,IAAI;MACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,EACzB,MAAM,IAAIG,KAAK,CAAC,+CAA+C,OAAOH,GAAG,EAAE,CAAC;MAC9E,OAAOf,GAAG,IAAIe,GAAG,IAAIA,GAAG,GAAGmD,KAAK,CAAC,CAAC;IACpC,CAAC;IACDmB,GAAG,EAAGtE,GAAG,IAAKA,GAAG,KAAKf,GAAG;IACzB+G,KAAK,EAAGhG,GAAG,IAAK,CAACA,GAAG,GAAGb,GAAG,MAAMA,GAAG;IACnC4C,GAAG,EAAG/B,GAAG,IAAKL,GAAG,CAAC,CAACK,GAAG,EAAEmD,KAAK,CAAC;IAC9BxB,GAAG,EAAEA,CAACgD,GAAG,EAAEC,GAAG,KAAKD,GAAG,KAAKC,GAAG;IAE9BhD,GAAG,EAAG5B,GAAG,IAAKL,GAAG,CAACK,GAAG,GAAGA,GAAG,EAAEmD,KAAK,CAAC;IACnC8C,GAAG,EAAEA,CAACtB,GAAG,EAAEC,GAAG,KAAKjF,GAAG,CAACgF,GAAG,GAAGC,GAAG,EAAEzB,KAAK,CAAC;IACxCN,GAAG,EAAEA,CAAC8B,GAAG,EAAEC,GAAG,KAAKjF,GAAG,CAACgF,GAAG,GAAGC,GAAG,EAAEzB,KAAK,CAAC;IACxCjB,GAAG,EAAEA,CAACyC,GAAG,EAAEC,GAAG,KAAKjF,GAAG,CAACgF,GAAG,GAAGC,GAAG,EAAEzB,KAAK,CAAC;IACxCpD,GAAG,EAAEA,CAACC,GAAG,EAAEC,KAAK,KAAK0D,KAAK,CAACC,CAAC,EAAE5D,GAAG,EAAEC,KAAK,CAAC;IACzCiG,GAAG,EAAEA,CAACvB,GAAG,EAAEC,GAAG,KAAKjF,GAAG,CAACgF,GAAG,GAAGpE,MAAM,CAACqE,GAAG,EAAEzB,KAAK,CAAC,EAAEA,KAAK,CAAC;IAEvD;IACAgD,IAAI,EAAGnG,GAAG,IAAKA,GAAG,GAAGA,GAAG;IACxBoG,IAAI,EAAEA,CAACzB,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAC7ByB,IAAI,EAAEA,CAAC1B,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAC7B0B,IAAI,EAAEA,CAAC3B,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG;IAE7BJ,GAAG,EAAGxE,GAAG,IAAKO,MAAM,CAACP,GAAG,EAAEmD,KAAK,CAAC;IAChCoD,IAAI,EAAEb,KAAK,CAACa,IAAI,KAAMxF,CAAC,IAAK4E,KAAK,CAAC/B,CAAC,EAAE7C,CAAC,CAAC,CAAC;IACxCyF,WAAW,EAAGC,GAAG,IAAK1C,aAAa,CAACH,CAAC,EAAE6C,GAAG,CAAC;IAC3C;IACA;IACAC,IAAI,EAAEA,CAAC9G,CAAC,EAAEC,CAAC,EAAE8G,CAAC,KAAMA,CAAC,GAAG9G,CAAC,GAAGD,CAAE;IAC9BgH,OAAO,EAAG5G,GAAG,IAAMyF,IAAI,GAAG7G,eAAe,CAACoB,GAAG,EAAEqD,KAAK,CAAC,GAAG1E,eAAe,CAACqB,GAAG,EAAEqD,KAAK,CAAE;IACpFwD,SAAS,EAAGC,KAAK,IAAI;MACnB,IAAIA,KAAK,CAAC3C,MAAM,KAAKd,KAAK,EACxB,MAAM,IAAIlD,KAAK,CAAC,0BAA0BkD,KAAK,SAASyD,KAAK,CAAC3C,MAAM,EAAE,CAAC;MACzE,OAAOsB,IAAI,GAAG3G,eAAe,CAACgI,KAAK,CAAC,GAAGjI,eAAe,CAACiI,KAAK,CAAC;IAC/D;GACU,CAAC;EACb,OAAOlB,MAAM,CAACC,MAAM,CAACjC,CAAC,CAAC;AACzB;AAEA,OAAM,SAAUmD,SAASA,CAAItF,EAAa,EAAEuF,GAAM;EAChD,IAAI,CAACvF,EAAE,CAACuE,KAAK,EAAE,MAAM,IAAI7F,KAAK,CAAC,0BAA0B,CAAC;EAC1D,MAAMuB,IAAI,GAAGD,EAAE,CAAC8E,IAAI,CAACS,GAAG,CAAC;EACzB,OAAOvF,EAAE,CAACuE,KAAK,CAACtE,IAAI,CAAC,GAAGA,IAAI,GAAGD,EAAE,CAACM,GAAG,CAACL,IAAI,CAAC;AAC7C;AAEA,OAAM,SAAUuF,UAAUA,CAAIxF,EAAa,EAAEuF,GAAM;EACjD,IAAI,CAACvF,EAAE,CAACuE,KAAK,EAAE,MAAM,IAAI7F,KAAK,CAAC,0BAA0B,CAAC;EAC1D,MAAMuB,IAAI,GAAGD,EAAE,CAAC8E,IAAI,CAACS,GAAG,CAAC;EACzB,OAAOvF,EAAE,CAACuE,KAAK,CAACtE,IAAI,CAAC,GAAGD,EAAE,CAACM,GAAG,CAACL,IAAI,CAAC,GAAGA,IAAI;AAC7C;AAEA;;;;;;AAMA,OAAM,SAAUwF,mBAAmBA,CACjCC,IAAyB,EACzBC,UAAkB,EAClB3B,IAAI,GAAG,KAAK;EAEZ0B,IAAI,GAAGpI,WAAW,CAAC,aAAa,EAAEoI,IAAI,CAAC;EACvC,MAAME,OAAO,GAAGF,IAAI,CAAChD,MAAM;EAC3B,MAAMmD,MAAM,GAAGvC,OAAO,CAACqC,UAAU,CAAC,CAAChC,WAAW,GAAG,CAAC;EAClD,IAAIkC,MAAM,GAAG,EAAE,IAAID,OAAO,GAAGC,MAAM,IAAID,OAAO,GAAG,IAAI,EACnD,MAAM,IAAIlH,KAAK,CAAC,iCAAiCmH,MAAM,6BAA6BD,OAAO,EAAE,CAAC;EAChG,MAAMrH,GAAG,GAAGyF,IAAI,GAAG3G,eAAe,CAACqI,IAAI,CAAC,GAAGtI,eAAe,CAACsI,IAAI,CAAC;EAChE,OAAOxH,GAAG,CAACK,GAAG,EAAEoH,UAAU,GAAGjI,GAAG,CAAC,GAAGA,GAAG;AACzC;AAEA;;;;;;AAMA,OAAM,SAAUoI,mBAAmBA,CAACC,UAAkB;EACpD,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE,MAAM,IAAIrH,KAAK,CAAC,4BAA4B,CAAC;EACjF,MAAMsH,SAAS,GAAGD,UAAU,CAACrC,QAAQ,CAAC,CAAC,CAAC,CAAChB,MAAM;EAC/C,OAAOkB,IAAI,CAACC,IAAI,CAACmC,SAAS,GAAG,CAAC,CAAC;AACjC;AAEA;;;;;;;AAOA,OAAM,SAAUC,gBAAgBA,CAACF,UAAkB;EACjD,MAAMrD,MAAM,GAAGoD,mBAAmB,CAACC,UAAU,CAAC;EAC9C,OAAOrD,MAAM,GAAGkB,IAAI,CAACC,IAAI,CAACnB,MAAM,GAAG,CAAC,CAAC;AACvC;AAEA;;;;;;;;;;;;;AAaA,OAAM,SAAUwD,cAAcA,CAACC,GAAe,EAAEJ,UAAkB,EAAE/B,IAAI,GAAG,KAAK;EAC9E,MAAMoC,GAAG,GAAGD,GAAG,CAACzD,MAAM;EACtB,MAAM2D,QAAQ,GAAGP,mBAAmB,CAACC,UAAU,CAAC;EAChD,MAAMF,MAAM,GAAGI,gBAAgB,CAACF,UAAU,CAAC;EAC3C;EACA,IAAIK,GAAG,GAAG,EAAE,IAAIA,GAAG,GAAGP,MAAM,IAAIO,GAAG,GAAG,IAAI,EACxC,MAAM,IAAI1H,KAAK,CAAC,YAAYmH,MAAM,6BAA6BO,GAAG,EAAE,CAAC;EACvE,MAAM7H,GAAG,GAAGyF,IAAI,GAAG5G,eAAe,CAAC+I,GAAG,CAAC,GAAG9I,eAAe,CAAC8I,GAAG,CAAC;EAC9D;EACA,MAAMG,OAAO,GAAGpI,GAAG,CAACK,GAAG,EAAEwH,UAAU,GAAGrI,GAAG,CAAC,GAAGA,GAAG;EAChD,OAAOsG,IAAI,GAAG7G,eAAe,CAACmJ,OAAO,EAAED,QAAQ,CAAC,GAAGnJ,eAAe,CAACoJ,OAAO,EAAED,QAAQ,CAAC;AACvF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}