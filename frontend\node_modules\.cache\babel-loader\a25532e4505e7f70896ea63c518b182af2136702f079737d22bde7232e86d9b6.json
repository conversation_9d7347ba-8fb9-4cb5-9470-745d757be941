{"ast": null, "code": "/**\n *  Events allow for applications to use the observer pattern, which\n *  allows subscribing and publishing events, outside the normal\n *  execution paths.\n *\n *  @_section api/utils/events:Events  [about-events]\n */\nimport { defineProperties } from \"./properties.js\";\n/**\n *  When an [[EventEmitterable]] triggers a [[Listener]], the\n *  callback always ahas one additional argument passed, which is\n *  an **EventPayload**.\n */\nexport class EventPayload {\n  /**\n   *  The event filter.\n   */\n  filter;\n  /**\n   *  The **EventEmitterable**.\n   */\n  emitter;\n  #listener;\n  /**\n   *  Create a new **EventPayload** for %%emitter%% with\n   *  the %%listener%% and for %%filter%%.\n   */\n  constructor(emitter, listener, filter) {\n    this.#listener = listener;\n    defineProperties(this, {\n      emitter,\n      filter\n    });\n  }\n  /**\n   *  Unregister the triggered listener for future events.\n   */\n  async removeListener() {\n    if (this.#listener == null) {\n      return;\n    }\n    await this.emitter.off(this.filter, this.#listener);\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "EventPayload", "filter", "emitter", "listener", "constructor", "removeListener", "off"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\events.ts"], "sourcesContent": ["/**\n *  Events allow for applications to use the observer pattern, which\n *  allows subscribing and publishing events, outside the normal\n *  execution paths.\n *\n *  @_section api/utils/events:Events  [about-events]\n */\nimport { defineProperties } from \"./properties.js\";\n\n/**\n *  A callback function called when a an event is triggered.\n */\nexport type Listener = (...args: Array<any>) => void;\n\n/**\n *  An **EventEmitterable** behaves similar to an EventEmitter\n *  except provides async access to its methods.\n *\n *  An EventEmitter implements the observer pattern.\n */\nexport interface EventEmitterable<T> {\n    /**\n     *  Registers a %%listener%% that is called whenever the\n     *  %%event%% occurs until unregistered.\n     */\n    on(event: T, listener: Listener): Promise<this>;\n\n    /**\n     *  Registers a %%listener%% that is called the next time\n     *  %%event%% occurs.\n     */\n    once(event: T, listener: Listener): Promise<this>;\n\n    /**\n     *  Triggers each listener for %%event%% with the %%args%%.\n     */\n    emit(event: T, ...args: Array<any>): Promise<boolean>;\n\n    /**\n     *  Resolves to the number of listeners for %%event%%.\n     */\n    listenerCount(event?: T): Promise<number>;\n\n    /**\n     *  Resolves to the listeners for %%event%%.\n     */\n    listeners(event?: T): Promise<Array<Listener>>;\n\n    /**\n     *  Unregister the %%listener%% for %%event%%. If %%listener%%\n     *  is unspecified, all listeners are unregistered.\n     */\n    off(event: T, listener?: Listener): Promise<this>;\n\n    /**\n     *  Unregister all listeners for %%event%%.\n     */\n    removeAllListeners(event?: T): Promise<this>;\n\n    /**\n     *  Alias for [[on]].\n     */\n    addListener(event: T, listener: Listener): Promise<this>;\n\n    /**\n     *  Alias for [[off]].\n     */\n    removeListener(event: T, listener: Listener): Promise<this>;\n}\n\n/**\n *  When an [[EventEmitterable]] triggers a [[Listener]], the\n *  callback always ahas one additional argument passed, which is\n *  an **EventPayload**.\n */\nexport class EventPayload<T> {\n    /**\n     *  The event filter.\n     */\n    readonly filter!: T;\n\n    /**\n     *  The **EventEmitterable**.\n     */\n    readonly emitter!: EventEmitterable<T>;\n\n    readonly #listener: null | Listener;\n\n    /**\n     *  Create a new **EventPayload** for %%emitter%% with\n     *  the %%listener%% and for %%filter%%.\n     */\n    constructor(emitter: EventEmitterable<T>, listener: null | Listener, filter: T) {\n        this.#listener = listener;\n        defineProperties<EventPayload<any>>(this, { emitter, filter });\n    }\n\n    /**\n     *  Unregister the triggered listener for future events.\n     */\n    async removeListener(): Promise<void> {\n        if (this.#listener == null) { return; }\n        await this.emitter.off(this.filter, this.#listener);\n    }\n}\n"], "mappings": "AAAA;;;;;;;AAOA,SAASA,gBAAgB,QAAQ,iBAAiB;AA+DlD;;;;;AAKA,OAAM,MAAOC,YAAY;EACrB;;;EAGSC,MAAM;EAEf;;;EAGSC,OAAO;EAEP,CAAAC,QAAS;EAElB;;;;EAIAC,YAAYF,OAA4B,EAAEC,QAAyB,EAAEF,MAAS;IAC1E,IAAI,CAAC,CAAAE,QAAS,GAAGA,QAAQ;IACzBJ,gBAAgB,CAAoB,IAAI,EAAE;MAAEG,OAAO;MAAED;IAAM,CAAE,CAAC;EAClE;EAEA;;;EAGA,MAAMI,cAAcA,CAAA;IAChB,IAAI,IAAI,CAAC,CAAAF,QAAS,IAAI,IAAI,EAAE;MAAE;;IAC9B,MAAM,IAAI,CAACD,OAAO,CAACI,GAAG,CAAC,IAAI,CAACL,MAAM,EAAE,IAAI,CAAC,CAAAE,QAAS,CAAC;EACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}