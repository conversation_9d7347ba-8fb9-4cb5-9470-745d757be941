{"ast": null, "code": "/**\n *  Using strings in Ethereum (or any security-basd system) requires\n *  additional care. These utilities attempt to mitigate some of the\n *  safety issues as well as provide the ability to recover and analyse\n *  strings.\n *\n *  @_subsection api/utils:Strings and UTF-8  [about-strings]\n */\nimport { getBytes } from \"./data.js\";\nimport { assertArgument, assertNormalize } from \"./errors.js\";\nfunction errorFunc(reason, offset, bytes, output, badCodepoint) {\n  assertArgument(false, `invalid codepoint at offset ${offset}; ${reason}`, \"bytes\", bytes);\n}\nfunction ignoreFunc(reason, offset, bytes, output, badCodepoint) {\n  // If there is an invalid prefix (including stray continuation), skip any additional continuation bytes\n  if (reason === \"BAD_PREFIX\" || reason === \"UNEXPECTED_CONTINUE\") {\n    let i = 0;\n    for (let o = offset + 1; o < bytes.length; o++) {\n      if (bytes[o] >> 6 !== 0x02) {\n        break;\n      }\n      i++;\n    }\n    return i;\n  }\n  // This byte runs us past the end of the string, so just jump to the end\n  // (but the first byte was read already read and therefore skipped)\n  if (reason === \"OVERRUN\") {\n    return bytes.length - offset - 1;\n  }\n  // Nothing to skip\n  return 0;\n}\nfunction replaceFunc(reason, offset, bytes, output, badCodepoint) {\n  // Overlong representations are otherwise \"valid\" code points; just non-deistingtished\n  if (reason === \"OVERLONG\") {\n    assertArgument(typeof badCodepoint === \"number\", \"invalid bad code point for replacement\", \"badCodepoint\", badCodepoint);\n    output.push(badCodepoint);\n    return 0;\n  }\n  // Put the replacement character into the output\n  output.push(0xfffd);\n  // Otherwise, process as if ignoring errors\n  return ignoreFunc(reason, offset, bytes, output, badCodepoint);\n}\n/**\n *  A handful of popular, built-in UTF-8 error handling strategies.\n *\n *  **``\"error\"``** - throws on ANY illegal UTF-8 sequence or\n *  non-canonical (overlong) codepoints (this is the default)\n *\n *  **``\"ignore\"``** - silently drops any illegal UTF-8 sequence\n *  and accepts non-canonical (overlong) codepoints\n *\n *  **``\"replace\"``** - replace any illegal UTF-8 sequence with the\n *  UTF-8 replacement character (i.e. ``\"\\\\ufffd\"``) and accepts\n *  non-canonical (overlong) codepoints\n *\n *  @returns: Record<\"error\" | \"ignore\" | \"replace\", Utf8ErrorFunc>\n */\nexport const Utf8ErrorFuncs = Object.freeze({\n  error: errorFunc,\n  ignore: ignoreFunc,\n  replace: replaceFunc\n});\n// http://stackoverflow.com/questions/13356493/decode-utf-8-with-javascript#13691499\nfunction getUtf8CodePoints(_bytes, onError) {\n  if (onError == null) {\n    onError = Utf8ErrorFuncs.error;\n  }\n  const bytes = getBytes(_bytes, \"bytes\");\n  const result = [];\n  let i = 0;\n  // Invalid bytes are ignored\n  while (i < bytes.length) {\n    const c = bytes[i++];\n    // 0xxx xxxx\n    if (c >> 7 === 0) {\n      result.push(c);\n      continue;\n    }\n    // Multibyte; how many bytes left for this character?\n    let extraLength = null;\n    let overlongMask = null;\n    // 110x xxxx 10xx xxxx\n    if ((c & 0xe0) === 0xc0) {\n      extraLength = 1;\n      overlongMask = 0x7f;\n      // 1110 xxxx 10xx xxxx 10xx xxxx\n    } else if ((c & 0xf0) === 0xe0) {\n      extraLength = 2;\n      overlongMask = 0x7ff;\n      // 1111 0xxx 10xx xxxx 10xx xxxx 10xx xxxx\n    } else if ((c & 0xf8) === 0xf0) {\n      extraLength = 3;\n      overlongMask = 0xffff;\n    } else {\n      if ((c & 0xc0) === 0x80) {\n        i += onError(\"UNEXPECTED_CONTINUE\", i - 1, bytes, result);\n      } else {\n        i += onError(\"BAD_PREFIX\", i - 1, bytes, result);\n      }\n      continue;\n    }\n    // Do we have enough bytes in our data?\n    if (i - 1 + extraLength >= bytes.length) {\n      i += onError(\"OVERRUN\", i - 1, bytes, result);\n      continue;\n    }\n    // Remove the length prefix from the char\n    let res = c & (1 << 8 - extraLength - 1) - 1;\n    for (let j = 0; j < extraLength; j++) {\n      let nextChar = bytes[i];\n      // Invalid continuation byte\n      if ((nextChar & 0xc0) != 0x80) {\n        i += onError(\"MISSING_CONTINUE\", i, bytes, result);\n        res = null;\n        break;\n      }\n      ;\n      res = res << 6 | nextChar & 0x3f;\n      i++;\n    }\n    // See above loop for invalid continuation byte\n    if (res === null) {\n      continue;\n    }\n    // Maximum code point\n    if (res > 0x10ffff) {\n      i += onError(\"OUT_OF_RANGE\", i - 1 - extraLength, bytes, result, res);\n      continue;\n    }\n    // Reserved for UTF-16 surrogate halves\n    if (res >= 0xd800 && res <= 0xdfff) {\n      i += onError(\"UTF16_SURROGATE\", i - 1 - extraLength, bytes, result, res);\n      continue;\n    }\n    // Check for overlong sequences (more bytes than needed)\n    if (res <= overlongMask) {\n      i += onError(\"OVERLONG\", i - 1 - extraLength, bytes, result, res);\n      continue;\n    }\n    result.push(res);\n  }\n  return result;\n}\n// http://stackoverflow.com/questions/18729405/how-to-convert-utf8-string-to-byte-array\n/**\n *  Returns the UTF-8 byte representation of %%str%%.\n *\n *  If %%form%% is specified, the string is normalized.\n */\nexport function toUtf8Bytes(str, form) {\n  assertArgument(typeof str === \"string\", \"invalid string value\", \"str\", str);\n  if (form != null) {\n    assertNormalize(form);\n    str = str.normalize(form);\n  }\n  let result = [];\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 0x80) {\n      result.push(c);\n    } else if (c < 0x800) {\n      result.push(c >> 6 | 0xc0);\n      result.push(c & 0x3f | 0x80);\n    } else if ((c & 0xfc00) == 0xd800) {\n      i++;\n      const c2 = str.charCodeAt(i);\n      assertArgument(i < str.length && (c2 & 0xfc00) === 0xdc00, \"invalid surrogate pair\", \"str\", str);\n      // Surrogate Pair\n      const pair = 0x10000 + ((c & 0x03ff) << 10) + (c2 & 0x03ff);\n      result.push(pair >> 18 | 0xf0);\n      result.push(pair >> 12 & 0x3f | 0x80);\n      result.push(pair >> 6 & 0x3f | 0x80);\n      result.push(pair & 0x3f | 0x80);\n    } else {\n      result.push(c >> 12 | 0xe0);\n      result.push(c >> 6 & 0x3f | 0x80);\n      result.push(c & 0x3f | 0x80);\n    }\n  }\n  return new Uint8Array(result);\n}\n;\n//export \nfunction _toUtf8String(codePoints) {\n  return codePoints.map(codePoint => {\n    if (codePoint <= 0xffff) {\n      return String.fromCharCode(codePoint);\n    }\n    codePoint -= 0x10000;\n    return String.fromCharCode((codePoint >> 10 & 0x3ff) + 0xd800, (codePoint & 0x3ff) + 0xdc00);\n  }).join(\"\");\n}\n/**\n *  Returns the string represented by the UTF-8 data %%bytes%%.\n *\n *  When %%onError%% function is specified, it is called on UTF-8\n *  errors allowing recovery using the [[Utf8ErrorFunc]] API.\n *  (default: [error](Utf8ErrorFuncs))\n */\nexport function toUtf8String(bytes, onError) {\n  return _toUtf8String(getUtf8CodePoints(bytes, onError));\n}\n/**\n *  Returns the UTF-8 code-points for %%str%%.\n *\n *  If %%form%% is specified, the string is normalized.\n */\nexport function toUtf8CodePoints(str, form) {\n  return getUtf8CodePoints(toUtf8Bytes(str, form));\n}", "map": {"version": 3, "names": ["getBytes", "assertArgument", "assertNormalize", "errorFunc", "reason", "offset", "bytes", "output", "badCodepoint", "ignoreFunc", "i", "o", "length", "replaceFunc", "push", "Utf8ErrorFuncs", "Object", "freeze", "error", "ignore", "replace", "getUtf8CodePoints", "_bytes", "onError", "result", "c", "extraLength", "overlongMask", "res", "j", "nextChar", "toUtf8Bytes", "str", "form", "normalize", "charCodeAt", "c2", "pair", "Uint8Array", "_toUtf8String", "codePoints", "map", "codePoint", "String", "fromCharCode", "join", "toUtf8String", "toUtf8CodePoints"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\utf8.ts"], "sourcesContent": ["/**\n *  Using strings in Ethereum (or any security-basd system) requires\n *  additional care. These utilities attempt to mitigate some of the\n *  safety issues as well as provide the ability to recover and analyse\n *  strings.\n *\n *  @_subsection api/utils:Strings and UTF-8  [about-strings]\n */\nimport { getBytes } from \"./data.js\";\nimport { assertArgument, assertNormalize } from \"./errors.js\";\n\nimport type { BytesLike } from \"./index.js\";\n\n\n///////////////////////////////\n\n/**\n *  The stanard normalization forms.\n */\nexport type UnicodeNormalizationForm = \"NFC\" | \"NFD\" | \"NFKC\" | \"NFKD\";\n\n/**\n *  When using the UTF-8 error API the following errors can be intercepted\n *  and processed as the %%reason%% passed to the [[Utf8ErrorFunc]].\n *\n *  **``\"UNEXPECTED_CONTINUE\"``** - a continuation byte was present where there\n *  was nothing to continue.\n *\n *  **``\"BAD_PREFIX\"``** - an invalid (non-continuation) byte to start a\n *  UTF-8 codepoint was found.\n *\n *  **``\"OVERRUN\"``** - the string is too short to process the expected\n *  codepoint length.\n *\n *  **``\"MISSING_CONTINUE\"``** - a missing continuation byte was expected but\n *  not found. The %%offset%% indicates the index the continuation byte\n *  was expected at.\n *\n *  **``\"OUT_OF_RANGE\"``** - the computed code point is outside the range\n *  for UTF-8. The %%badCodepoint%% indicates the computed codepoint, which was\n *  outside the valid UTF-8 range.\n *\n *  **``\"UTF16_SURROGATE\"``** - the UTF-8 strings contained a UTF-16 surrogate\n *  pair. The %%badCodepoint%% is the computed codepoint, which was inside the\n *  UTF-16 surrogate range.\n *\n *  **``\"OVERLONG\"``** - the string is an overlong representation. The\n *  %%badCodepoint%% indicates the computed codepoint, which has already\n *  been bounds checked.\n *\n *\n *  @returns string\n */\nexport type Utf8ErrorReason = \"UNEXPECTED_CONTINUE\" | \"BAD_PREFIX\" | \"OVERRUN\" |\n    \"MISSING_CONTINUE\" | \"OUT_OF_RANGE\" | \"UTF16_SURROGATE\" | \"OVERLONG\";\n\n\n/**\n *  A callback that can be used with [[toUtf8String]] to analysis or\n *  recovery from invalid UTF-8 data.\n *\n *  Parsing UTF-8 data is done through a simple Finite-State Machine (FSM)\n *  which calls the ``Utf8ErrorFunc`` if a fault is detected.\n *\n *  The %%reason%% indicates where in the FSM execution the fault\n *  occurred and the %%offset%% indicates where the input failed.\n *\n *  The %%bytes%% represents the raw UTF-8 data that was provided and\n *  %%output%% is the current array of UTF-8 code-points, which may\n *  be updated by the ``Utf8ErrorFunc``.\n *\n *  The value of the %%badCodepoint%% depends on the %%reason%%. See\n *  [[Utf8ErrorReason]] for details.\n *\n *  The function should return the number of bytes that should be skipped\n *  when control resumes to the FSM.\n */\nexport type Utf8ErrorFunc = (reason: Utf8ErrorReason, offset: number, bytes: Uint8Array, output: Array<number>, badCodepoint?: number) => number;\n\n\nfunction errorFunc(reason: Utf8ErrorReason, offset: number, bytes: Uint8Array, output: Array<number>, badCodepoint?: number): number {\n    assertArgument(false, `invalid codepoint at offset ${ offset }; ${ reason }`, \"bytes\", bytes);\n}\n\nfunction ignoreFunc(reason: Utf8ErrorReason, offset: number, bytes: Uint8Array, output: Array<number>, badCodepoint?: number): number {\n\n    // If there is an invalid prefix (including stray continuation), skip any additional continuation bytes\n    if (reason === \"BAD_PREFIX\" || reason === \"UNEXPECTED_CONTINUE\") {\n        let i = 0;\n        for (let o = offset + 1; o < bytes.length; o++) {\n            if (bytes[o] >> 6 !== 0x02) { break; }\n            i++;\n        }\n        return i;\n    }\n\n    // This byte runs us past the end of the string, so just jump to the end\n    // (but the first byte was read already read and therefore skipped)\n    if (reason === \"OVERRUN\") {\n        return bytes.length - offset - 1;\n    }\n\n    // Nothing to skip\n    return 0;\n}\n\nfunction replaceFunc(reason: Utf8ErrorReason, offset: number, bytes: Uint8Array, output: Array<number>, badCodepoint?: number): number {\n\n    // Overlong representations are otherwise \"valid\" code points; just non-deistingtished\n    if (reason === \"OVERLONG\") {\n        assertArgument(typeof(badCodepoint) === \"number\", \"invalid bad code point for replacement\", \"badCodepoint\", badCodepoint);\n        output.push(badCodepoint);\n        return 0;\n    }\n\n    // Put the replacement character into the output\n    output.push(0xfffd);\n\n    // Otherwise, process as if ignoring errors\n    return ignoreFunc(reason, offset, bytes, output, badCodepoint);\n}\n\n/**\n *  A handful of popular, built-in UTF-8 error handling strategies.\n *\n *  **``\"error\"``** - throws on ANY illegal UTF-8 sequence or\n *  non-canonical (overlong) codepoints (this is the default)\n *\n *  **``\"ignore\"``** - silently drops any illegal UTF-8 sequence\n *  and accepts non-canonical (overlong) codepoints\n *\n *  **``\"replace\"``** - replace any illegal UTF-8 sequence with the\n *  UTF-8 replacement character (i.e. ``\"\\\\ufffd\"``) and accepts\n *  non-canonical (overlong) codepoints\n *\n *  @returns: Record<\"error\" | \"ignore\" | \"replace\", Utf8ErrorFunc>\n */\nexport const Utf8ErrorFuncs: Readonly<Record<\"error\" | \"ignore\" | \"replace\", Utf8ErrorFunc>> = Object.freeze({\n    error: errorFunc,\n    ignore: ignoreFunc,\n    replace: replaceFunc\n});\n\n// http://stackoverflow.com/questions/13356493/decode-utf-8-with-javascript#13691499\nfunction getUtf8CodePoints(_bytes: BytesLike, onError?: Utf8ErrorFunc): Array<number> {\n    if (onError == null) { onError = Utf8ErrorFuncs.error; }\n\n    const bytes = getBytes(_bytes, \"bytes\");\n\n    const result: Array<number> = [];\n    let i = 0;\n\n    // Invalid bytes are ignored\n    while(i < bytes.length) {\n\n        const c = bytes[i++];\n\n        // 0xxx xxxx\n        if (c >> 7 === 0) {\n            result.push(c);\n            continue;\n        }\n\n        // Multibyte; how many bytes left for this character?\n        let extraLength: null | number = null;\n        let overlongMask: null | number = null;\n\n        // 110x xxxx 10xx xxxx\n        if ((c & 0xe0) === 0xc0) {\n            extraLength = 1;\n            overlongMask = 0x7f;\n\n        // 1110 xxxx 10xx xxxx 10xx xxxx\n        } else if ((c & 0xf0) === 0xe0) {\n            extraLength = 2;\n            overlongMask = 0x7ff;\n\n        // 1111 0xxx 10xx xxxx 10xx xxxx 10xx xxxx\n        } else if ((c & 0xf8) === 0xf0) {\n            extraLength = 3;\n            overlongMask = 0xffff;\n\n        } else {\n            if ((c & 0xc0) === 0x80) {\n                i += onError(\"UNEXPECTED_CONTINUE\", i - 1, bytes, result);\n            } else {\n                i += onError(\"BAD_PREFIX\", i - 1, bytes, result);\n            }\n            continue;\n        }\n\n        // Do we have enough bytes in our data?\n        if (i - 1 + extraLength >= bytes.length) {\n            i += onError(\"OVERRUN\", i - 1, bytes, result);\n            continue;\n        }\n\n        // Remove the length prefix from the char\n        let res: null | number = c & ((1 << (8 - extraLength - 1)) - 1);\n\n        for (let j = 0; j < extraLength; j++) {\n            let nextChar = bytes[i];\n\n            // Invalid continuation byte\n            if ((nextChar & 0xc0) != 0x80) {\n                i += onError(\"MISSING_CONTINUE\", i, bytes, result);\n                res = null;\n                break;\n            };\n\n            res = (res << 6) | (nextChar & 0x3f);\n            i++;\n        }\n\n        // See above loop for invalid continuation byte\n        if (res === null) { continue; }\n\n        // Maximum code point\n        if (res > 0x10ffff) {\n            i += onError(\"OUT_OF_RANGE\", i - 1 - extraLength, bytes, result, res);\n            continue;\n        }\n\n        // Reserved for UTF-16 surrogate halves\n        if (res >= 0xd800 && res <= 0xdfff) {\n            i += onError(\"UTF16_SURROGATE\", i - 1 - extraLength, bytes, result, res);\n            continue;\n        }\n\n        // Check for overlong sequences (more bytes than needed)\n        if (res <= overlongMask) {\n            i += onError(\"OVERLONG\", i - 1 - extraLength, bytes, result, res);\n            continue;\n        }\n\n        result.push(res);\n    }\n\n    return result;\n}\n\n// http://stackoverflow.com/questions/18729405/how-to-convert-utf8-string-to-byte-array\n\n/**\n *  Returns the UTF-8 byte representation of %%str%%.\n *\n *  If %%form%% is specified, the string is normalized.\n */\nexport function toUtf8Bytes(str: string, form?: UnicodeNormalizationForm): Uint8Array {\n    assertArgument(typeof(str) === \"string\", \"invalid string value\", \"str\", str);\n\n    if (form != null) {\n        assertNormalize(form);\n        str = str.normalize(form);\n    }\n\n    let result: Array<number> = [];\n    for (let i = 0; i < str.length; i++) {\n        const c = str.charCodeAt(i);\n\n        if (c < 0x80) {\n            result.push(c);\n\n        } else if (c < 0x800) {\n            result.push((c >> 6) | 0xc0);\n            result.push((c & 0x3f) | 0x80);\n\n        } else if ((c & 0xfc00) == 0xd800) {\n            i++;\n            const c2 = str.charCodeAt(i);\n\n            assertArgument(i < str.length && ((c2 & 0xfc00) === 0xdc00),\n                \"invalid surrogate pair\", \"str\", str);\n\n            // Surrogate Pair\n            const pair = 0x10000 + ((c & 0x03ff) << 10) + (c2 & 0x03ff);\n            result.push((pair >> 18) | 0xf0);\n            result.push(((pair >> 12) & 0x3f) | 0x80);\n            result.push(((pair >> 6) & 0x3f) | 0x80);\n            result.push((pair & 0x3f) | 0x80);\n\n        } else {\n            result.push((c >> 12) | 0xe0);\n            result.push(((c >> 6) & 0x3f) | 0x80);\n            result.push((c & 0x3f) | 0x80);\n        }\n    }\n\n    return new Uint8Array(result);\n};\n\n//export \nfunction _toUtf8String(codePoints: Array<number>): string {\n    return codePoints.map((codePoint) => {\n        if (codePoint <= 0xffff) {\n            return String.fromCharCode(codePoint);\n        }\n        codePoint -= 0x10000;\n        return String.fromCharCode(\n            (((codePoint >> 10) & 0x3ff) + 0xd800),\n            ((codePoint & 0x3ff) + 0xdc00)\n        );\n    }).join(\"\");\n}\n\n/**\n *  Returns the string represented by the UTF-8 data %%bytes%%.\n *\n *  When %%onError%% function is specified, it is called on UTF-8\n *  errors allowing recovery using the [[Utf8ErrorFunc]] API.\n *  (default: [error](Utf8ErrorFuncs))\n */\nexport function toUtf8String(bytes: BytesLike, onError?: Utf8ErrorFunc): string {\n    return _toUtf8String(getUtf8CodePoints(bytes, onError));\n}\n\n/**\n *  Returns the UTF-8 code-points for %%str%%.\n *\n *  If %%form%% is specified, the string is normalized.\n */\nexport function toUtf8CodePoints(str: string, form?: UnicodeNormalizationForm): Array<number> {\n    return getUtf8CodePoints(toUtf8Bytes(str, form));\n}\n\n"], "mappings": "AAAA;;;;;;;;AAQA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,EAAEC,eAAe,QAAQ,aAAa;AAuE7D,SAASC,SAASA,CAACC,MAAuB,EAAEC,MAAc,EAAEC,KAAiB,EAAEC,MAAqB,EAAEC,YAAqB;EACvHP,cAAc,CAAC,KAAK,EAAE,+BAAgCI,MAAO,KAAMD,MAAO,EAAE,EAAE,OAAO,EAAEE,KAAK,CAAC;AACjG;AAEA,SAASG,UAAUA,CAACL,MAAuB,EAAEC,MAAc,EAAEC,KAAiB,EAAEC,MAAqB,EAAEC,YAAqB;EAExH;EACA,IAAIJ,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,qBAAqB,EAAE;IAC7D,IAAIM,CAAC,GAAG,CAAC;IACT,KAAK,IAAIC,CAAC,GAAGN,MAAM,GAAG,CAAC,EAAEM,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIL,KAAK,CAACK,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;QAAE;;MAC9BD,CAAC,EAAE;;IAEP,OAAOA,CAAC;;EAGZ;EACA;EACA,IAAIN,MAAM,KAAK,SAAS,EAAE;IACtB,OAAOE,KAAK,CAACM,MAAM,GAAGP,MAAM,GAAG,CAAC;;EAGpC;EACA,OAAO,CAAC;AACZ;AAEA,SAASQ,WAAWA,CAACT,MAAuB,EAAEC,MAAc,EAAEC,KAAiB,EAAEC,MAAqB,EAAEC,YAAqB;EAEzH;EACA,IAAIJ,MAAM,KAAK,UAAU,EAAE;IACvBH,cAAc,CAAC,OAAOO,YAAa,KAAK,QAAQ,EAAE,wCAAwC,EAAE,cAAc,EAAEA,YAAY,CAAC;IACzHD,MAAM,CAACO,IAAI,CAACN,YAAY,CAAC;IACzB,OAAO,CAAC;;EAGZ;EACAD,MAAM,CAACO,IAAI,CAAC,MAAM,CAAC;EAEnB;EACA,OAAOL,UAAU,CAACL,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,CAAC;AAClE;AAEA;;;;;;;;;;;;;;;AAeA,OAAO,MAAMO,cAAc,GAAoEC,MAAM,CAACC,MAAM,CAAC;EACzGC,KAAK,EAAEf,SAAS;EAChBgB,MAAM,EAAEV,UAAU;EAClBW,OAAO,EAAEP;CACZ,CAAC;AAEF;AACA,SAASQ,iBAAiBA,CAACC,MAAiB,EAAEC,OAAuB;EACjE,IAAIA,OAAO,IAAI,IAAI,EAAE;IAAEA,OAAO,GAAGR,cAAc,CAACG,KAAK;;EAErD,MAAMZ,KAAK,GAAGN,QAAQ,CAACsB,MAAM,EAAE,OAAO,CAAC;EAEvC,MAAME,MAAM,GAAkB,EAAE;EAChC,IAAId,CAAC,GAAG,CAAC;EAET;EACA,OAAMA,CAAC,GAAGJ,KAAK,CAACM,MAAM,EAAE;IAEpB,MAAMa,CAAC,GAAGnB,KAAK,CAACI,CAAC,EAAE,CAAC;IAEpB;IACA,IAAIe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;MACdD,MAAM,CAACV,IAAI,CAACW,CAAC,CAAC;MACd;;IAGJ;IACA,IAAIC,WAAW,GAAkB,IAAI;IACrC,IAAIC,YAAY,GAAkB,IAAI;IAEtC;IACA,IAAI,CAACF,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;MACrBC,WAAW,GAAG,CAAC;MACfC,YAAY,GAAG,IAAI;MAEvB;KACC,MAAM,IAAI,CAACF,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;MAC5BC,WAAW,GAAG,CAAC;MACfC,YAAY,GAAG,KAAK;MAExB;KACC,MAAM,IAAI,CAACF,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;MAC5BC,WAAW,GAAG,CAAC;MACfC,YAAY,GAAG,MAAM;KAExB,MAAM;MACH,IAAI,CAACF,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;QACrBf,CAAC,IAAIa,OAAO,CAAC,qBAAqB,EAAEb,CAAC,GAAG,CAAC,EAAEJ,KAAK,EAAEkB,MAAM,CAAC;OAC5D,MAAM;QACHd,CAAC,IAAIa,OAAO,CAAC,YAAY,EAAEb,CAAC,GAAG,CAAC,EAAEJ,KAAK,EAAEkB,MAAM,CAAC;;MAEpD;;IAGJ;IACA,IAAId,CAAC,GAAG,CAAC,GAAGgB,WAAW,IAAIpB,KAAK,CAACM,MAAM,EAAE;MACrCF,CAAC,IAAIa,OAAO,CAAC,SAAS,EAAEb,CAAC,GAAG,CAAC,EAAEJ,KAAK,EAAEkB,MAAM,CAAC;MAC7C;;IAGJ;IACA,IAAII,GAAG,GAAkBH,CAAC,GAAI,CAAC,CAAC,IAAK,CAAC,GAAGC,WAAW,GAAG,CAAE,IAAI,CAAE;IAE/D,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,WAAW,EAAEG,CAAC,EAAE,EAAE;MAClC,IAAIC,QAAQ,GAAGxB,KAAK,CAACI,CAAC,CAAC;MAEvB;MACA,IAAI,CAACoB,QAAQ,GAAG,IAAI,KAAK,IAAI,EAAE;QAC3BpB,CAAC,IAAIa,OAAO,CAAC,kBAAkB,EAAEb,CAAC,EAAEJ,KAAK,EAAEkB,MAAM,CAAC;QAClDI,GAAG,GAAG,IAAI;QACV;;MACH;MAEDA,GAAG,GAAIA,GAAG,IAAI,CAAC,GAAKE,QAAQ,GAAG,IAAK;MACpCpB,CAAC,EAAE;;IAGP;IACA,IAAIkB,GAAG,KAAK,IAAI,EAAE;MAAE;;IAEpB;IACA,IAAIA,GAAG,GAAG,QAAQ,EAAE;MAChBlB,CAAC,IAAIa,OAAO,CAAC,cAAc,EAAEb,CAAC,GAAG,CAAC,GAAGgB,WAAW,EAAEpB,KAAK,EAAEkB,MAAM,EAAEI,GAAG,CAAC;MACrE;;IAGJ;IACA,IAAIA,GAAG,IAAI,MAAM,IAAIA,GAAG,IAAI,MAAM,EAAE;MAChClB,CAAC,IAAIa,OAAO,CAAC,iBAAiB,EAAEb,CAAC,GAAG,CAAC,GAAGgB,WAAW,EAAEpB,KAAK,EAAEkB,MAAM,EAAEI,GAAG,CAAC;MACxE;;IAGJ;IACA,IAAIA,GAAG,IAAID,YAAY,EAAE;MACrBjB,CAAC,IAAIa,OAAO,CAAC,UAAU,EAAEb,CAAC,GAAG,CAAC,GAAGgB,WAAW,EAAEpB,KAAK,EAAEkB,MAAM,EAAEI,GAAG,CAAC;MACjE;;IAGJJ,MAAM,CAACV,IAAI,CAACc,GAAG,CAAC;;EAGpB,OAAOJ,MAAM;AACjB;AAEA;AAEA;;;;;AAKA,OAAM,SAAUO,WAAWA,CAACC,GAAW,EAAEC,IAA+B;EACpEhC,cAAc,CAAC,OAAO+B,GAAI,KAAK,QAAQ,EAAE,sBAAsB,EAAE,KAAK,EAAEA,GAAG,CAAC;EAE5E,IAAIC,IAAI,IAAI,IAAI,EAAE;IACd/B,eAAe,CAAC+B,IAAI,CAAC;IACrBD,GAAG,GAAGA,GAAG,CAACE,SAAS,CAACD,IAAI,CAAC;;EAG7B,IAAIT,MAAM,GAAkB,EAAE;EAC9B,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,GAAG,CAACpB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACjC,MAAMe,CAAC,GAAGO,GAAG,CAACG,UAAU,CAACzB,CAAC,CAAC;IAE3B,IAAIe,CAAC,GAAG,IAAI,EAAE;MACVD,MAAM,CAACV,IAAI,CAACW,CAAC,CAAC;KAEjB,MAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MAClBD,MAAM,CAACV,IAAI,CAAEW,CAAC,IAAI,CAAC,GAAI,IAAI,CAAC;MAC5BD,MAAM,CAACV,IAAI,CAAEW,CAAC,GAAG,IAAI,GAAI,IAAI,CAAC;KAEjC,MAAM,IAAI,CAACA,CAAC,GAAG,MAAM,KAAK,MAAM,EAAE;MAC/Bf,CAAC,EAAE;MACH,MAAM0B,EAAE,GAAGJ,GAAG,CAACG,UAAU,CAACzB,CAAC,CAAC;MAE5BT,cAAc,CAACS,CAAC,GAAGsB,GAAG,CAACpB,MAAM,IAAK,CAACwB,EAAE,GAAG,MAAM,MAAM,MAAO,EACvD,wBAAwB,EAAE,KAAK,EAAEJ,GAAG,CAAC;MAEzC;MACA,MAAMK,IAAI,GAAG,OAAO,IAAI,CAACZ,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAIW,EAAE,GAAG,MAAM,CAAC;MAC3DZ,MAAM,CAACV,IAAI,CAAEuB,IAAI,IAAI,EAAE,GAAI,IAAI,CAAC;MAChCb,MAAM,CAACV,IAAI,CAAGuB,IAAI,IAAI,EAAE,GAAI,IAAI,GAAI,IAAI,CAAC;MACzCb,MAAM,CAACV,IAAI,CAAGuB,IAAI,IAAI,CAAC,GAAI,IAAI,GAAI,IAAI,CAAC;MACxCb,MAAM,CAACV,IAAI,CAAEuB,IAAI,GAAG,IAAI,GAAI,IAAI,CAAC;KAEpC,MAAM;MACHb,MAAM,CAACV,IAAI,CAAEW,CAAC,IAAI,EAAE,GAAI,IAAI,CAAC;MAC7BD,MAAM,CAACV,IAAI,CAAGW,CAAC,IAAI,CAAC,GAAI,IAAI,GAAI,IAAI,CAAC;MACrCD,MAAM,CAACV,IAAI,CAAEW,CAAC,GAAG,IAAI,GAAI,IAAI,CAAC;;;EAItC,OAAO,IAAIa,UAAU,CAACd,MAAM,CAAC;AACjC;AAAC;AAED;AACA,SAASe,aAAaA,CAACC,UAAyB;EAC5C,OAAOA,UAAU,CAACC,GAAG,CAAEC,SAAS,IAAI;IAChC,IAAIA,SAAS,IAAI,MAAM,EAAE;MACrB,OAAOC,MAAM,CAACC,YAAY,CAACF,SAAS,CAAC;;IAEzCA,SAAS,IAAI,OAAO;IACpB,OAAOC,MAAM,CAACC,YAAY,CACrB,CAAEF,SAAS,IAAI,EAAE,GAAI,KAAK,IAAI,MAAM,EACpC,CAACA,SAAS,GAAG,KAAK,IAAI,MAAO,CACjC;EACL,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC;AACf;AAEA;;;;;;;AAOA,OAAM,SAAUC,YAAYA,CAACxC,KAAgB,EAAEiB,OAAuB;EAClE,OAAOgB,aAAa,CAAClB,iBAAiB,CAACf,KAAK,EAAEiB,OAAO,CAAC,CAAC;AAC3D;AAEA;;;;;AAKA,OAAM,SAAUwB,gBAAgBA,CAACf,GAAW,EAAEC,IAA+B;EACzE,OAAOZ,iBAAiB,CAACU,WAAW,CAACC,GAAG,EAAEC,IAAI,CAAC,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}