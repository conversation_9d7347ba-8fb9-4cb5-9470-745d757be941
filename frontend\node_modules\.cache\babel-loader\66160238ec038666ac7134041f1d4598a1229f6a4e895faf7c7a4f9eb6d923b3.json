{"ast": null, "code": "/**\n *  The Interface class is a low-level class that accepts an\n *  ABI and provides all the necessary functionality to encode\n *  and decode paramaters to and results from methods, events\n *  and errors.\n *\n *  It also provides several convenience methods to automatically\n *  search and find matching transactions and events to parse them.\n *\n *  @_subsection api/abi:Interfaces  [interfaces]\n */\nimport { keccak256 } from \"../crypto/index.js\";\nimport { id } from \"../hash/index.js\";\nimport { concat, dataSlice, getBigInt, getBytes, getBytesCopy, hexlify, zeroPadBytes, zeroPadValue, isHexString, defineProperties, assertArgument, toBeHex, assert } from \"../utils/index.js\";\nimport { AbiCoder } from \"./abi-coder.js\";\nimport { checkResultErrors, Result } from \"./coders/abstract-coder.js\";\nimport { ConstructorFragment, ErrorFragment, EventFragment, Fragment, FunctionFragment, ParamType } from \"./fragments.js\";\nimport { Typed } from \"./typed.js\";\nexport { checkResultErrors, Result };\n/**\n *  When using the [[Interface-parseLog]] to automatically match a Log to its event\n *  for parsing, a **LogDescription** is returned.\n */\nexport class LogDescription {\n  /**\n   *  The matching fragment for the ``topic0``.\n   */\n  fragment;\n  /**\n   *  The name of the Event.\n   */\n  name;\n  /**\n   *  The full Event signature.\n   */\n  signature;\n  /**\n   *  The topic hash for the Event.\n   */\n  topic;\n  /**\n   *  The arguments passed into the Event with ``emit``.\n   */\n  args;\n  /**\n   *  @_ignore:\n   */\n  constructor(fragment, topic, args) {\n    const name = fragment.name,\n      signature = fragment.format();\n    defineProperties(this, {\n      fragment,\n      name,\n      signature,\n      topic,\n      args\n    });\n  }\n}\n/**\n *  When using the [[Interface-parseTransaction]] to automatically match\n *  a transaction data to its function for parsing,\n *  a **TransactionDescription** is returned.\n */\nexport class TransactionDescription {\n  /**\n   *  The matching fragment from the transaction ``data``.\n   */\n  fragment;\n  /**\n   *  The name of the Function from the transaction ``data``.\n   */\n  name;\n  /**\n   *  The arguments passed to the Function from the transaction ``data``.\n   */\n  args;\n  /**\n   *  The full Function signature from the transaction ``data``.\n   */\n  signature;\n  /**\n   *  The selector for the Function from the transaction ``data``.\n   */\n  selector;\n  /**\n   *  The ``value`` (in wei) from the transaction.\n   */\n  value;\n  /**\n   *  @_ignore:\n   */\n  constructor(fragment, selector, args, value) {\n    const name = fragment.name,\n      signature = fragment.format();\n    defineProperties(this, {\n      fragment,\n      name,\n      args,\n      signature,\n      selector,\n      value\n    });\n  }\n}\n/**\n *  When using the [[Interface-parseError]] to automatically match an\n *  error for a call result for parsing, an **ErrorDescription** is returned.\n */\nexport class ErrorDescription {\n  /**\n   *  The matching fragment.\n   */\n  fragment;\n  /**\n   *  The name of the Error.\n   */\n  name;\n  /**\n   *  The arguments passed to the Error with ``revert``.\n   */\n  args;\n  /**\n   *  The full Error signature.\n   */\n  signature;\n  /**\n   *  The selector for the Error.\n   */\n  selector;\n  /**\n   *  @_ignore:\n   */\n  constructor(fragment, selector, args) {\n    const name = fragment.name,\n      signature = fragment.format();\n    defineProperties(this, {\n      fragment,\n      name,\n      args,\n      signature,\n      selector\n    });\n  }\n}\n/**\n *  An **Indexed** is used as a value when a value that does not\n *  fit within a topic (i.e. not a fixed-length, 32-byte type). It\n *  is the ``keccak256`` of the value, and used for types such as\n *  arrays, tuples, bytes and strings.\n */\nexport class Indexed {\n  /**\n   *  The ``keccak256`` of the value logged.\n   */\n  hash;\n  /**\n   *  @_ignore:\n   */\n  _isIndexed;\n  /**\n   *  Returns ``true`` if %%value%% is an **Indexed**.\n   *\n   *  This provides a Type Guard for property access.\n   */\n  static isIndexed(value) {\n    return !!(value && value._isIndexed);\n  }\n  /**\n   *  @_ignore:\n   */\n  constructor(hash) {\n    defineProperties(this, {\n      hash,\n      _isIndexed: true\n    });\n  }\n}\n// https://docs.soliditylang.org/en/v0.8.13/control-structures.html?highlight=panic#panic-via-assert-and-error-via-require\nconst PanicReasons = {\n  \"0\": \"generic panic\",\n  \"1\": \"assert(false)\",\n  \"17\": \"arithmetic overflow\",\n  \"18\": \"division or modulo by zero\",\n  \"33\": \"enum overflow\",\n  \"34\": \"invalid encoded storage byte array accessed\",\n  \"49\": \"out-of-bounds array access; popping on an empty array\",\n  \"50\": \"out-of-bounds access of an array or bytesN\",\n  \"65\": \"out of memory\",\n  \"81\": \"uninitialized function\"\n};\nconst BuiltinErrors = {\n  \"0x08c379a0\": {\n    signature: \"Error(string)\",\n    name: \"Error\",\n    inputs: [\"string\"],\n    reason: message => {\n      return `reverted with reason string ${JSON.stringify(message)}`;\n    }\n  },\n  \"0x4e487b71\": {\n    signature: \"Panic(uint256)\",\n    name: \"Panic\",\n    inputs: [\"uint256\"],\n    reason: code => {\n      let reason = \"unknown panic code\";\n      if (code >= 0 && code <= 0xff && PanicReasons[code.toString()]) {\n        reason = PanicReasons[code.toString()];\n      }\n      return `reverted with panic code 0x${code.toString(16)} (${reason})`;\n    }\n  }\n};\n/**\n *  An Interface abstracts many of the low-level details for\n *  encoding and decoding the data on the blockchain.\n *\n *  An ABI provides information on how to encode data to send to\n *  a Contract, how to decode the results and events and how to\n *  interpret revert errors.\n *\n *  The ABI can be specified by [any supported format](InterfaceAbi).\n */\nexport class Interface {\n  /**\n   *  All the Contract ABI members (i.e. methods, events, errors, etc).\n   */\n  fragments;\n  /**\n   *  The Contract constructor.\n   */\n  deploy;\n  /**\n   *  The Fallback method, if any.\n   */\n  fallback;\n  /**\n   *  If receiving ether is supported.\n   */\n  receive;\n  #errors;\n  #events;\n  #functions;\n  //    #structs: Map<string, StructFragment>;\n  #abiCoder;\n  /**\n   *  Create a new Interface for the %%fragments%%.\n   */\n  constructor(fragments) {\n    let abi = [];\n    if (typeof fragments === \"string\") {\n      abi = JSON.parse(fragments);\n    } else {\n      abi = fragments;\n    }\n    this.#functions = new Map();\n    this.#errors = new Map();\n    this.#events = new Map();\n    //        this.#structs = new Map();\n    const frags = [];\n    for (const a of abi) {\n      try {\n        frags.push(Fragment.from(a));\n      } catch (error) {\n        console.log(`[Warning] Invalid Fragment ${JSON.stringify(a)}:`, error.message);\n      }\n    }\n    defineProperties(this, {\n      fragments: Object.freeze(frags)\n    });\n    let fallback = null;\n    let receive = false;\n    this.#abiCoder = this.getAbiCoder();\n    // Add all fragments by their signature\n    this.fragments.forEach((fragment, index) => {\n      let bucket;\n      switch (fragment.type) {\n        case \"constructor\":\n          if (this.deploy) {\n            console.log(\"duplicate definition - constructor\");\n            return;\n          }\n          //checkNames(fragment, \"input\", fragment.inputs);\n          defineProperties(this, {\n            deploy: fragment\n          });\n          return;\n        case \"fallback\":\n          if (fragment.inputs.length === 0) {\n            receive = true;\n          } else {\n            assertArgument(!fallback || fragment.payable !== fallback.payable, \"conflicting fallback fragments\", `fragments[${index}]`, fragment);\n            fallback = fragment;\n            receive = fallback.payable;\n          }\n          return;\n        case \"function\":\n          //checkNames(fragment, \"input\", fragment.inputs);\n          //checkNames(fragment, \"output\", (<FunctionFragment>fragment).outputs);\n          bucket = this.#functions;\n          break;\n        case \"event\":\n          //checkNames(fragment, \"input\", fragment.inputs);\n          bucket = this.#events;\n          break;\n        case \"error\":\n          bucket = this.#errors;\n          break;\n        default:\n          return;\n      }\n      // Two identical entries; ignore it\n      const signature = fragment.format();\n      if (bucket.has(signature)) {\n        return;\n      }\n      bucket.set(signature, fragment);\n    });\n    // If we do not have a constructor add a default\n    if (!this.deploy) {\n      defineProperties(this, {\n        deploy: ConstructorFragment.from(\"constructor()\")\n      });\n    }\n    defineProperties(this, {\n      fallback,\n      receive\n    });\n  }\n  /**\n   *  Returns the entire Human-Readable ABI, as an array of\n   *  signatures, optionally as %%minimal%% strings, which\n   *  removes parameter names and unneceesary spaces.\n   */\n  format(minimal) {\n    const format = minimal ? \"minimal\" : \"full\";\n    const abi = this.fragments.map(f => f.format(format));\n    return abi;\n  }\n  /**\n   *  Return the JSON-encoded ABI. This is the format Solidiy\n   *  returns.\n   */\n  formatJson() {\n    const abi = this.fragments.map(f => f.format(\"json\"));\n    // We need to re-bundle the JSON fragments a bit\n    return JSON.stringify(abi.map(j => JSON.parse(j)));\n  }\n  /**\n   *  The ABI coder that will be used to encode and decode binary\n   *  data.\n   */\n  getAbiCoder() {\n    return AbiCoder.defaultAbiCoder();\n  }\n  // Find a function definition by any means necessary (unless it is ambiguous)\n  #getFunction(key, values, forceUnique) {\n    // Selector\n    if (isHexString(key)) {\n      const selector = key.toLowerCase();\n      for (const fragment of this.#functions.values()) {\n        if (selector === fragment.selector) {\n          return fragment;\n        }\n      }\n      return null;\n    }\n    // It is a bare name, look up the function (will return null if ambiguous)\n    if (key.indexOf(\"(\") === -1) {\n      const matching = [];\n      for (const [name, fragment] of this.#functions) {\n        if (name.split(\"(\" /* fix:) */)[0] === key) {\n          matching.push(fragment);\n        }\n      }\n      if (values) {\n        const lastValue = values.length > 0 ? values[values.length - 1] : null;\n        let valueLength = values.length;\n        let allowOptions = true;\n        if (Typed.isTyped(lastValue) && lastValue.type === \"overrides\") {\n          allowOptions = false;\n          valueLength--;\n        }\n        // Remove all matches that don't have a compatible length. The args\n        // may contain an overrides, so the match may have n or n - 1 parameters\n        for (let i = matching.length - 1; i >= 0; i--) {\n          const inputs = matching[i].inputs.length;\n          if (inputs !== valueLength && (!allowOptions || inputs !== valueLength - 1)) {\n            matching.splice(i, 1);\n          }\n        }\n        // Remove all matches that don't match the Typed signature\n        for (let i = matching.length - 1; i >= 0; i--) {\n          const inputs = matching[i].inputs;\n          for (let j = 0; j < values.length; j++) {\n            // Not a typed value\n            if (!Typed.isTyped(values[j])) {\n              continue;\n            }\n            // We are past the inputs\n            if (j >= inputs.length) {\n              if (values[j].type === \"overrides\") {\n                continue;\n              }\n              matching.splice(i, 1);\n              break;\n            }\n            // Make sure the value type matches the input type\n            if (values[j].type !== inputs[j].baseType) {\n              matching.splice(i, 1);\n              break;\n            }\n          }\n        }\n      }\n      // We found a single matching signature with an overrides, but the\n      // last value is something that cannot possibly be an options\n      if (matching.length === 1 && values && values.length !== matching[0].inputs.length) {\n        const lastArg = values[values.length - 1];\n        if (lastArg == null || Array.isArray(lastArg) || typeof lastArg !== \"object\") {\n          matching.splice(0, 1);\n        }\n      }\n      if (matching.length === 0) {\n        return null;\n      }\n      if (matching.length > 1 && forceUnique) {\n        const matchStr = matching.map(m => JSON.stringify(m.format())).join(\", \");\n        assertArgument(false, `ambiguous function description (i.e. matches ${matchStr})`, \"key\", key);\n      }\n      return matching[0];\n    }\n    // Normalize the signature and lookup the function\n    const result = this.#functions.get(FunctionFragment.from(key).format());\n    if (result) {\n      return result;\n    }\n    return null;\n  }\n  /**\n   *  Get the function name for %%key%%, which may be a function selector,\n   *  function name or function signature that belongs to the ABI.\n   */\n  getFunctionName(key) {\n    const fragment = this.#getFunction(key, null, false);\n    assertArgument(fragment, \"no matching function\", \"key\", key);\n    return fragment.name;\n  }\n  /**\n   *  Returns true if %%key%% (a function selector, function name or\n   *  function signature) is present in the ABI.\n   *\n   *  In the case of a function name, the name may be ambiguous, so\n   *  accessing the [[FunctionFragment]] may require refinement.\n   */\n  hasFunction(key) {\n    return !!this.#getFunction(key, null, false);\n  }\n  /**\n   *  Get the [[FunctionFragment]] for %%key%%, which may be a function\n   *  selector, function name or function signature that belongs to the ABI.\n   *\n   *  If %%values%% is provided, it will use the Typed API to handle\n   *  ambiguous cases where multiple functions match by name.\n   *\n   *  If the %%key%% and %%values%% do not refine to a single function in\n   *  the ABI, this will throw.\n   */\n  getFunction(key, values) {\n    return this.#getFunction(key, values || null, true);\n  }\n  /**\n   *  Iterate over all functions, calling %%callback%%, sorted by their name.\n   */\n  forEachFunction(callback) {\n    const names = Array.from(this.#functions.keys());\n    names.sort((a, b) => a.localeCompare(b));\n    for (let i = 0; i < names.length; i++) {\n      const name = names[i];\n      callback(this.#functions.get(name), i);\n    }\n  }\n  // Find an event definition by any means necessary (unless it is ambiguous)\n  #getEvent(key, values, forceUnique) {\n    // EventTopic\n    if (isHexString(key)) {\n      const eventTopic = key.toLowerCase();\n      for (const fragment of this.#events.values()) {\n        if (eventTopic === fragment.topicHash) {\n          return fragment;\n        }\n      }\n      return null;\n    }\n    // It is a bare name, look up the function (will return null if ambiguous)\n    if (key.indexOf(\"(\") === -1) {\n      const matching = [];\n      for (const [name, fragment] of this.#events) {\n        if (name.split(\"(\" /* fix:) */)[0] === key) {\n          matching.push(fragment);\n        }\n      }\n      if (values) {\n        // Remove all matches that don't have a compatible length.\n        for (let i = matching.length - 1; i >= 0; i--) {\n          if (matching[i].inputs.length < values.length) {\n            matching.splice(i, 1);\n          }\n        }\n        // Remove all matches that don't match the Typed signature\n        for (let i = matching.length - 1; i >= 0; i--) {\n          const inputs = matching[i].inputs;\n          for (let j = 0; j < values.length; j++) {\n            // Not a typed value\n            if (!Typed.isTyped(values[j])) {\n              continue;\n            }\n            // Make sure the value type matches the input type\n            if (values[j].type !== inputs[j].baseType) {\n              matching.splice(i, 1);\n              break;\n            }\n          }\n        }\n      }\n      if (matching.length === 0) {\n        return null;\n      }\n      if (matching.length > 1 && forceUnique) {\n        const matchStr = matching.map(m => JSON.stringify(m.format())).join(\", \");\n        assertArgument(false, `ambiguous event description (i.e. matches ${matchStr})`, \"key\", key);\n      }\n      return matching[0];\n    }\n    // Normalize the signature and lookup the function\n    const result = this.#events.get(EventFragment.from(key).format());\n    if (result) {\n      return result;\n    }\n    return null;\n  }\n  /**\n   *  Get the event name for %%key%%, which may be a topic hash,\n   *  event name or event signature that belongs to the ABI.\n   */\n  getEventName(key) {\n    const fragment = this.#getEvent(key, null, false);\n    assertArgument(fragment, \"no matching event\", \"key\", key);\n    return fragment.name;\n  }\n  /**\n   *  Returns true if %%key%% (an event topic hash, event name or\n   *  event signature) is present in the ABI.\n   *\n   *  In the case of an event name, the name may be ambiguous, so\n   *  accessing the [[EventFragment]] may require refinement.\n   */\n  hasEvent(key) {\n    return !!this.#getEvent(key, null, false);\n  }\n  /**\n   *  Get the [[EventFragment]] for %%key%%, which may be a topic hash,\n   *  event name or event signature that belongs to the ABI.\n   *\n   *  If %%values%% is provided, it will use the Typed API to handle\n   *  ambiguous cases where multiple events match by name.\n   *\n   *  If the %%key%% and %%values%% do not refine to a single event in\n   *  the ABI, this will throw.\n   */\n  getEvent(key, values) {\n    return this.#getEvent(key, values || null, true);\n  }\n  /**\n   *  Iterate over all events, calling %%callback%%, sorted by their name.\n   */\n  forEachEvent(callback) {\n    const names = Array.from(this.#events.keys());\n    names.sort((a, b) => a.localeCompare(b));\n    for (let i = 0; i < names.length; i++) {\n      const name = names[i];\n      callback(this.#events.get(name), i);\n    }\n  }\n  /**\n   *  Get the [[ErrorFragment]] for %%key%%, which may be an error\n   *  selector, error name or error signature that belongs to the ABI.\n   *\n   *  If %%values%% is provided, it will use the Typed API to handle\n   *  ambiguous cases where multiple errors match by name.\n   *\n   *  If the %%key%% and %%values%% do not refine to a single error in\n   *  the ABI, this will throw.\n   */\n  getError(key, values) {\n    if (isHexString(key)) {\n      const selector = key.toLowerCase();\n      if (BuiltinErrors[selector]) {\n        return ErrorFragment.from(BuiltinErrors[selector].signature);\n      }\n      for (const fragment of this.#errors.values()) {\n        if (selector === fragment.selector) {\n          return fragment;\n        }\n      }\n      return null;\n    }\n    // It is a bare name, look up the function (will return null if ambiguous)\n    if (key.indexOf(\"(\") === -1) {\n      const matching = [];\n      for (const [name, fragment] of this.#errors) {\n        if (name.split(\"(\" /* fix:) */)[0] === key) {\n          matching.push(fragment);\n        }\n      }\n      if (matching.length === 0) {\n        if (key === \"Error\") {\n          return ErrorFragment.from(\"error Error(string)\");\n        }\n        if (key === \"Panic\") {\n          return ErrorFragment.from(\"error Panic(uint256)\");\n        }\n        return null;\n      } else if (matching.length > 1) {\n        const matchStr = matching.map(m => JSON.stringify(m.format())).join(\", \");\n        assertArgument(false, `ambiguous error description (i.e. ${matchStr})`, \"name\", key);\n      }\n      return matching[0];\n    }\n    // Normalize the signature and lookup the function\n    key = ErrorFragment.from(key).format();\n    if (key === \"Error(string)\") {\n      return ErrorFragment.from(\"error Error(string)\");\n    }\n    if (key === \"Panic(uint256)\") {\n      return ErrorFragment.from(\"error Panic(uint256)\");\n    }\n    const result = this.#errors.get(key);\n    if (result) {\n      return result;\n    }\n    return null;\n  }\n  /**\n   *  Iterate over all errors, calling %%callback%%, sorted by their name.\n   */\n  forEachError(callback) {\n    const names = Array.from(this.#errors.keys());\n    names.sort((a, b) => a.localeCompare(b));\n    for (let i = 0; i < names.length; i++) {\n      const name = names[i];\n      callback(this.#errors.get(name), i);\n    }\n  }\n  // Get the 4-byte selector used by Solidity to identify a function\n  /*\n  getSelector(fragment: ErrorFragment | FunctionFragment): string {\n  if (typeof(fragment) === \"string\") {\n      const matches: Array<Fragment> = [ ];\n       try { matches.push(this.getFunction(fragment)); } catch (error) { }\n      try { matches.push(this.getError(<string>fragment)); } catch (_) { }\n       if (matches.length === 0) {\n          logger.throwArgumentError(\"unknown fragment\", \"key\", fragment);\n      } else if (matches.length > 1) {\n          logger.throwArgumentError(\"ambiguous fragment matches function and error\", \"key\", fragment);\n      }\n       fragment = matches[0];\n  }\n   return dataSlice(id(fragment.format()), 0, 4);\n  }\n  */\n  // Get the 32-byte topic hash used by Solidity to identify an event\n  /*\n  getEventTopic(fragment: EventFragment): string {\n      //if (typeof(fragment) === \"string\") { fragment = this.getEvent(eventFragment); }\n      return id(fragment.format());\n  }\n  */\n  _decodeParams(params, data) {\n    return this.#abiCoder.decode(params, data);\n  }\n  _encodeParams(params, values) {\n    return this.#abiCoder.encode(params, values);\n  }\n  /**\n   *  Encodes a ``tx.data`` object for deploying the Contract with\n   *  the %%values%% as the constructor arguments.\n   */\n  encodeDeploy(values) {\n    return this._encodeParams(this.deploy.inputs, values || []);\n  }\n  /**\n   *  Decodes the result %%data%% (e.g. from an ``eth_call``) for the\n   *  specified error (see [[getError]] for valid values for\n   *  %%key%%).\n   *\n   *  Most developers should prefer the [[parseCallResult]] method instead,\n   *  which will automatically detect a ``CALL_EXCEPTION`` and throw the\n   *  corresponding error.\n   */\n  decodeErrorResult(fragment, data) {\n    if (typeof fragment === \"string\") {\n      const f = this.getError(fragment);\n      assertArgument(f, \"unknown error\", \"fragment\", fragment);\n      fragment = f;\n    }\n    assertArgument(dataSlice(data, 0, 4) === fragment.selector, `data signature does not match error ${fragment.name}.`, \"data\", data);\n    return this._decodeParams(fragment.inputs, dataSlice(data, 4));\n  }\n  /**\n   *  Encodes the transaction revert data for a call result that\n   *  reverted from the the Contract with the sepcified %%error%%\n   *  (see [[getError]] for valid values for %%fragment%%) with the %%values%%.\n   *\n   *  This is generally not used by most developers, unless trying to mock\n   *  a result from a Contract.\n   */\n  encodeErrorResult(fragment, values) {\n    if (typeof fragment === \"string\") {\n      const f = this.getError(fragment);\n      assertArgument(f, \"unknown error\", \"fragment\", fragment);\n      fragment = f;\n    }\n    return concat([fragment.selector, this._encodeParams(fragment.inputs, values || [])]);\n  }\n  /**\n   *  Decodes the %%data%% from a transaction ``tx.data`` for\n   *  the function specified (see [[getFunction]] for valid values\n   *  for %%fragment%%).\n   *\n   *  Most developers should prefer the [[parseTransaction]] method\n   *  instead, which will automatically detect the fragment.\n   */\n  decodeFunctionData(fragment, data) {\n    if (typeof fragment === \"string\") {\n      const f = this.getFunction(fragment);\n      assertArgument(f, \"unknown function\", \"fragment\", fragment);\n      fragment = f;\n    }\n    assertArgument(dataSlice(data, 0, 4) === fragment.selector, `data signature does not match function ${fragment.name}.`, \"data\", data);\n    return this._decodeParams(fragment.inputs, dataSlice(data, 4));\n  }\n  /**\n   *  Encodes the ``tx.data`` for a transaction that calls the function\n   *  specified (see [[getFunction]] for valid values for %%fragment%%) with\n   *  the %%values%%.\n   */\n  encodeFunctionData(fragment, values) {\n    if (typeof fragment === \"string\") {\n      const f = this.getFunction(fragment);\n      assertArgument(f, \"unknown function\", \"fragment\", fragment);\n      fragment = f;\n    }\n    return concat([fragment.selector, this._encodeParams(fragment.inputs, values || [])]);\n  }\n  /**\n   *  Decodes the result %%data%% (e.g. from an ``eth_call``) for the\n   *  specified function (see [[getFunction]] for valid values for\n   *  %%key%%).\n   *\n   *  Most developers should prefer the [[parseCallResult]] method instead,\n   *  which will automatically detect a ``CALL_EXCEPTION`` and throw the\n   *  corresponding error.\n   */\n  decodeFunctionResult(fragment, data) {\n    if (typeof fragment === \"string\") {\n      const f = this.getFunction(fragment);\n      assertArgument(f, \"unknown function\", \"fragment\", fragment);\n      fragment = f;\n    }\n    let message = \"invalid length for result data\";\n    const bytes = getBytesCopy(data);\n    if (bytes.length % 32 === 0) {\n      try {\n        return this.#abiCoder.decode(fragment.outputs, bytes);\n      } catch (error) {\n        message = \"could not decode result data\";\n      }\n    }\n    // Call returned data with no error, but the data is junk\n    assert(false, message, \"BAD_DATA\", {\n      value: hexlify(bytes),\n      info: {\n        method: fragment.name,\n        signature: fragment.format()\n      }\n    });\n  }\n  makeError(_data, tx) {\n    const data = getBytes(_data, \"data\");\n    const error = AbiCoder.getBuiltinCallException(\"call\", tx, data);\n    // Not a built-in error; try finding a custom error\n    const customPrefix = \"execution reverted (unknown custom error)\";\n    if (error.message.startsWith(customPrefix)) {\n      const selector = hexlify(data.slice(0, 4));\n      const ef = this.getError(selector);\n      if (ef) {\n        try {\n          const args = this.#abiCoder.decode(ef.inputs, data.slice(4));\n          error.revert = {\n            name: ef.name,\n            signature: ef.format(),\n            args\n          };\n          error.reason = error.revert.signature;\n          error.message = `execution reverted: ${error.reason}`;\n        } catch (e) {\n          error.message = `execution reverted (coult not decode custom error)`;\n        }\n      }\n    }\n    // Add the invocation, if available\n    const parsed = this.parseTransaction(tx);\n    if (parsed) {\n      error.invocation = {\n        method: parsed.name,\n        signature: parsed.signature,\n        args: parsed.args\n      };\n    }\n    return error;\n  }\n  /**\n   *  Encodes the result data (e.g. from an ``eth_call``) for the\n   *  specified function (see [[getFunction]] for valid values\n   *  for %%fragment%%) with %%values%%.\n   *\n   *  This is generally not used by most developers, unless trying to mock\n   *  a result from a Contract.\n   */\n  encodeFunctionResult(fragment, values) {\n    if (typeof fragment === \"string\") {\n      const f = this.getFunction(fragment);\n      assertArgument(f, \"unknown function\", \"fragment\", fragment);\n      fragment = f;\n    }\n    return hexlify(this.#abiCoder.encode(fragment.outputs, values || []));\n  }\n  /*\n      spelunk(inputs: Array<ParamType>, values: ReadonlyArray<any>, processfunc: (type: string, value: any) => Promise<any>): Promise<Array<any>> {\n          const promises: Array<Promise<>> = [ ];\n          const process = function(type: ParamType, value: any): any {\n              if (type.baseType === \"array\") {\n                  return descend(type.child\n              }\n              if (type. === \"address\") {\n              }\n          };\n  \n          const descend = function (inputs: Array<ParamType>, values: ReadonlyArray<any>) {\n              if (inputs.length !== values.length) { throw new Error(\"length mismatch\"); }\n              \n          };\n  \n          const result: Array<any> = [ ];\n          values.forEach((value, index) => {\n              if (value == null) {\n                  topics.push(null);\n              } else if (param.baseType === \"array\" || param.baseType === \"tuple\") {\n                  logger.throwArgumentError(\"filtering with tuples or arrays not supported\", (\"contract.\" + param.name), value);\n              } else if (Array.isArray(value)) {\n                  topics.push(value.map((value) => encodeTopic(param, value)));\n              } else {\n                  topics.push(encodeTopic(param, value));\n              }\n          });\n      }\n  */\n  // Create the filter for the event with search criteria (e.g. for eth_filterLog)\n  encodeFilterTopics(fragment, values) {\n    if (typeof fragment === \"string\") {\n      const f = this.getEvent(fragment);\n      assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n      fragment = f;\n    }\n    assert(values.length <= fragment.inputs.length, `too many arguments for ${fragment.format()}`, \"UNEXPECTED_ARGUMENT\", {\n      count: values.length,\n      expectedCount: fragment.inputs.length\n    });\n    const topics = [];\n    if (!fragment.anonymous) {\n      topics.push(fragment.topicHash);\n    }\n    // @TODO: Use the coders for this; to properly support tuples, etc.\n    const encodeTopic = (param, value) => {\n      if (param.type === \"string\") {\n        return id(value);\n      } else if (param.type === \"bytes\") {\n        return keccak256(hexlify(value));\n      }\n      if (param.type === \"bool\" && typeof value === \"boolean\") {\n        value = value ? \"0x01\" : \"0x00\";\n      } else if (param.type.match(/^u?int/)) {\n        value = toBeHex(value); // @TODO: Should this toTwos??\n      } else if (param.type.match(/^bytes/)) {\n        value = zeroPadBytes(value, 32);\n      } else if (param.type === \"address\") {\n        // Check addresses are valid\n        this.#abiCoder.encode([\"address\"], [value]);\n      }\n      return zeroPadValue(hexlify(value), 32);\n    };\n    values.forEach((value, index) => {\n      const param = fragment.inputs[index];\n      if (!param.indexed) {\n        assertArgument(value == null, \"cannot filter non-indexed parameters; must be null\", \"contract.\" + param.name, value);\n        return;\n      }\n      if (value == null) {\n        topics.push(null);\n      } else if (param.baseType === \"array\" || param.baseType === \"tuple\") {\n        assertArgument(false, \"filtering with tuples or arrays not supported\", \"contract.\" + param.name, value);\n      } else if (Array.isArray(value)) {\n        topics.push(value.map(value => encodeTopic(param, value)));\n      } else {\n        topics.push(encodeTopic(param, value));\n      }\n    });\n    // Trim off trailing nulls\n    while (topics.length && topics[topics.length - 1] === null) {\n      topics.pop();\n    }\n    return topics;\n  }\n  encodeEventLog(fragment, values) {\n    if (typeof fragment === \"string\") {\n      const f = this.getEvent(fragment);\n      assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n      fragment = f;\n    }\n    const topics = [];\n    const dataTypes = [];\n    const dataValues = [];\n    if (!fragment.anonymous) {\n      topics.push(fragment.topicHash);\n    }\n    assertArgument(values.length === fragment.inputs.length, \"event arguments/values mismatch\", \"values\", values);\n    fragment.inputs.forEach((param, index) => {\n      const value = values[index];\n      if (param.indexed) {\n        if (param.type === \"string\") {\n          topics.push(id(value));\n        } else if (param.type === \"bytes\") {\n          topics.push(keccak256(value));\n        } else if (param.baseType === \"tuple\" || param.baseType === \"array\") {\n          // @TODO\n          throw new Error(\"not implemented\");\n        } else {\n          topics.push(this.#abiCoder.encode([param.type], [value]));\n        }\n      } else {\n        dataTypes.push(param);\n        dataValues.push(value);\n      }\n    });\n    return {\n      data: this.#abiCoder.encode(dataTypes, dataValues),\n      topics: topics\n    };\n  }\n  // Decode a filter for the event and the search criteria\n  decodeEventLog(fragment, data, topics) {\n    if (typeof fragment === \"string\") {\n      const f = this.getEvent(fragment);\n      assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n      fragment = f;\n    }\n    if (topics != null && !fragment.anonymous) {\n      const eventTopic = fragment.topicHash;\n      assertArgument(isHexString(topics[0], 32) && topics[0].toLowerCase() === eventTopic, \"fragment/topic mismatch\", \"topics[0]\", topics[0]);\n      topics = topics.slice(1);\n    }\n    const indexed = [];\n    const nonIndexed = [];\n    const dynamic = [];\n    fragment.inputs.forEach((param, index) => {\n      if (param.indexed) {\n        if (param.type === \"string\" || param.type === \"bytes\" || param.baseType === \"tuple\" || param.baseType === \"array\") {\n          indexed.push(ParamType.from({\n            type: \"bytes32\",\n            name: param.name\n          }));\n          dynamic.push(true);\n        } else {\n          indexed.push(param);\n          dynamic.push(false);\n        }\n      } else {\n        nonIndexed.push(param);\n        dynamic.push(false);\n      }\n    });\n    const resultIndexed = topics != null ? this.#abiCoder.decode(indexed, concat(topics)) : null;\n    const resultNonIndexed = this.#abiCoder.decode(nonIndexed, data, true);\n    //const result: (Array<any> & { [ key: string ]: any }) = [ ];\n    const values = [];\n    const keys = [];\n    let nonIndexedIndex = 0,\n      indexedIndex = 0;\n    fragment.inputs.forEach((param, index) => {\n      let value = null;\n      if (param.indexed) {\n        if (resultIndexed == null) {\n          value = new Indexed(null);\n        } else if (dynamic[index]) {\n          value = new Indexed(resultIndexed[indexedIndex++]);\n        } else {\n          try {\n            value = resultIndexed[indexedIndex++];\n          } catch (error) {\n            value = error;\n          }\n        }\n      } else {\n        try {\n          value = resultNonIndexed[nonIndexedIndex++];\n        } catch (error) {\n          value = error;\n        }\n      }\n      values.push(value);\n      keys.push(param.name || null);\n    });\n    return Result.fromItems(values, keys);\n  }\n  /**\n   *  Parses a transaction, finding the matching function and extracts\n   *  the parameter values along with other useful function details.\n   *\n   *  If the matching function cannot be found, return null.\n   */\n  parseTransaction(tx) {\n    const data = getBytes(tx.data, \"tx.data\");\n    const value = getBigInt(tx.value != null ? tx.value : 0, \"tx.value\");\n    const fragment = this.getFunction(hexlify(data.slice(0, 4)));\n    if (!fragment) {\n      return null;\n    }\n    const args = this.#abiCoder.decode(fragment.inputs, data.slice(4));\n    return new TransactionDescription(fragment, fragment.selector, args, value);\n  }\n  parseCallResult(data) {\n    throw new Error(\"@TODO\");\n  }\n  /**\n   *  Parses a receipt log, finding the matching event and extracts\n   *  the parameter values along with other useful event details.\n   *\n   *  If the matching event cannot be found, returns null.\n   */\n  parseLog(log) {\n    const fragment = this.getEvent(log.topics[0]);\n    if (!fragment || fragment.anonymous) {\n      return null;\n    }\n    // @TODO: If anonymous, and the only method, and the input count matches, should we parse?\n    //        Probably not, because just because it is the only event in the ABI does\n    //        not mean we have the full ABI; maybe just a fragment?\n    return new LogDescription(fragment, fragment.topicHash, this.decodeEventLog(fragment, log.data, log.topics));\n  }\n  /**\n   *  Parses a revert data, finding the matching error and extracts\n   *  the parameter values along with other useful error details.\n   *\n   *  If the matching error cannot be found, returns null.\n   */\n  parseError(data) {\n    const hexData = hexlify(data);\n    const fragment = this.getError(dataSlice(hexData, 0, 4));\n    if (!fragment) {\n      return null;\n    }\n    const args = this.#abiCoder.decode(fragment.inputs, dataSlice(hexData, 4));\n    return new ErrorDescription(fragment, fragment.selector, args);\n  }\n  /**\n   *  Creates a new [[Interface]] from the ABI %%value%%.\n   *\n   *  The %%value%% may be provided as an existing [[Interface]] object,\n   *  a JSON-encoded ABI or any Human-Readable ABI format.\n   */\n  static from(value) {\n    // Already an Interface, which is immutable\n    if (value instanceof Interface) {\n      return value;\n    }\n    // JSON\n    if (typeof value === \"string\") {\n      return new Interface(JSON.parse(value));\n    }\n    // An Interface; possibly from another v6 instance\n    if (typeof value.formatJson === \"function\") {\n      return new Interface(value.formatJson());\n    }\n    // A legacy Interface; from an older version\n    if (typeof value.format === \"function\") {\n      return new Interface(value.format(\"json\"));\n    }\n    // Array of fragments\n    return new Interface(value);\n  }\n}", "map": {"version": 3, "names": ["keccak256", "id", "concat", "dataSlice", "getBigInt", "getBytes", "getBytesCopy", "hexlify", "zeroPadBytes", "zeroPadValue", "isHexString", "defineProperties", "assertArgument", "toBeHex", "assert", "AbiCoder", "checkResultErrors", "Result", "ConstructorFragment", "ErrorFragment", "EventFragment", "Fragment", "FunctionFragment", "ParamType", "Typed", "LogDescription", "fragment", "name", "signature", "topic", "args", "constructor", "format", "TransactionDescription", "selector", "value", "ErrorDescription", "Indexed", "hash", "_isIndexed", "isIndexed", "PanicReasons", "BuiltinErrors", "inputs", "reason", "message", "JSON", "stringify", "code", "toString", "Interface", "fragments", "deploy", "fallback", "receive", "errors", "events", "functions", "abiCoder", "abi", "parse", "Map", "frags", "a", "push", "from", "error", "console", "log", "Object", "freeze", "getAbiCoder", "for<PERSON>ach", "index", "bucket", "type", "length", "payable", "has", "set", "minimal", "map", "f", "formatJson", "j", "defaultAbiCoder", "getFunction", "#getFunction", "key", "values", "forceUnique", "toLowerCase", "indexOf", "matching", "split", "lastValue", "valueLength", "allowOptions", "isTyped", "i", "splice", "baseType", "lastArg", "Array", "isArray", "matchStr", "m", "join", "result", "get", "getFunctionName", "hasFunction", "forEachFunction", "callback", "names", "keys", "sort", "b", "localeCompare", "getEvent", "#getEvent", "eventTopic", "topicHash", "getEventName", "hasEvent", "forEachEvent", "getError", "forEachError", "_decodeParams", "params", "data", "decode", "_encodeParams", "encode", "encodeDeploy", "decodeErrorResult", "encodeErrorResult", "decodeFunctionData", "encodeFunctionData", "decodeFunctionResult", "bytes", "outputs", "info", "method", "makeError", "_data", "tx", "getBuiltinCallException", "customPrefix", "startsWith", "slice", "ef", "revert", "e", "parsed", "parseTransaction", "invocation", "encodeFunctionResult", "encodeFilterTopics", "count", "expectedCount", "topics", "anonymous", "encodeTopic", "param", "match", "indexed", "pop", "encodeEventLog", "dataTypes", "dataValues", "Error", "decodeEventLog", "nonIndexed", "dynamic", "resultIndexed", "resultNonIndexed", "nonIndexedIndex", "indexedIndex", "fromItems", "parseCallResult", "parseLog", "parseError", "hexData"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\interface.ts"], "sourcesContent": ["/**\n *  The Interface class is a low-level class that accepts an\n *  ABI and provides all the necessary functionality to encode\n *  and decode paramaters to and results from methods, events\n *  and errors.\n *\n *  It also provides several convenience methods to automatically\n *  search and find matching transactions and events to parse them.\n *\n *  @_subsection api/abi:Interfaces  [interfaces]\n */\n\nimport { keccak256 } from \"../crypto/index.js\"\nimport { id } from \"../hash/index.js\"\nimport {\n    concat, dataSlice, getBigInt, getBytes, getBytesCopy,\n    hexlify, zeroPadBytes, zeroPadValue, isHexString, defineProperties,\n    assertArgument, toBeHex, assert\n} from \"../utils/index.js\";\n\nimport { AbiCoder } from \"./abi-coder.js\";\nimport { checkResultErrors, Result } from \"./coders/abstract-coder.js\";\nimport {\n    ConstructorFragment, ErrorFragment, EventFragment, FallbackFragment,\n    Fragment, FunctionFragment, ParamType\n} from \"./fragments.js\";\nimport { Typed } from \"./typed.js\";\n\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON>, By<PERSON><PERSON>ike, CallExceptionError, CallExceptionTransaction } from \"../utils/index.js\";\n\nimport type { JsonFragment } from \"./fragments.js\";\n\n\nexport { checkResultErrors, Result };\n\n/**\n *  When using the [[Interface-parseLog]] to automatically match a Log to its event\n *  for parsing, a **LogDescription** is returned.\n */\nexport class LogDescription {\n    /**\n     *  The matching fragment for the ``topic0``.\n     */\n    readonly fragment!: EventFragment;\n\n    /**\n     *  The name of the Event.\n     */\n    readonly name!: string;\n\n    /**\n     *  The full Event signature.\n     */\n    readonly signature!: string;\n\n    /**\n     *  The topic hash for the Event.\n     */\n    readonly topic!: string;\n\n    /**\n     *  The arguments passed into the Event with ``emit``.\n     */\n    readonly args!: Result\n\n    /**\n     *  @_ignore:\n     */\n    constructor(fragment: EventFragment, topic: string, args: Result) {\n        const name = fragment.name, signature = fragment.format();\n        defineProperties<LogDescription>(this, {\n            fragment, name, signature, topic, args\n        });\n    }\n}\n\n/**\n *  When using the [[Interface-parseTransaction]] to automatically match\n *  a transaction data to its function for parsing,\n *  a **TransactionDescription** is returned.\n */\nexport class TransactionDescription {\n    /**\n     *  The matching fragment from the transaction ``data``.\n     */\n    readonly fragment!: FunctionFragment;\n\n    /**\n     *  The name of the Function from the transaction ``data``.\n     */\n    readonly name!: string;\n\n    /**\n     *  The arguments passed to the Function from the transaction ``data``.\n     */\n    readonly args!: Result;\n\n    /**\n     *  The full Function signature from the transaction ``data``.\n     */\n    readonly signature!: string;\n\n    /**\n     *  The selector for the Function from the transaction ``data``.\n     */\n    readonly selector!: string;\n\n    /**\n     *  The ``value`` (in wei) from the transaction.\n     */\n    readonly value!: bigint;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(fragment: FunctionFragment, selector: string, args: Result, value: bigint) {\n        const name = fragment.name, signature = fragment.format();\n        defineProperties<TransactionDescription>(this, {\n            fragment, name, args, signature, selector, value\n        });\n    }\n}\n\n/**\n *  When using the [[Interface-parseError]] to automatically match an\n *  error for a call result for parsing, an **ErrorDescription** is returned.\n */\nexport class ErrorDescription {\n    /**\n     *  The matching fragment.\n     */\n    readonly fragment!: ErrorFragment;\n\n    /**\n     *  The name of the Error.\n     */\n    readonly name!: string;\n\n    /**\n     *  The arguments passed to the Error with ``revert``.\n     */\n    readonly args!: Result;\n\n    /**\n     *  The full Error signature.\n     */\n    readonly signature!: string;\n\n    /**\n     *  The selector for the Error.\n     */\n    readonly selector!: string;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(fragment: ErrorFragment, selector: string, args: Result) {\n        const name = fragment.name, signature = fragment.format();\n        defineProperties<ErrorDescription>(this, {\n            fragment, name, args, signature, selector\n        });\n    }\n}\n\n/**\n *  An **Indexed** is used as a value when a value that does not\n *  fit within a topic (i.e. not a fixed-length, 32-byte type). It\n *  is the ``keccak256`` of the value, and used for types such as\n *  arrays, tuples, bytes and strings.\n */\nexport class Indexed {\n    /**\n     *  The ``keccak256`` of the value logged.\n     */\n    readonly hash!: null | string;\n\n    /**\n     *  @_ignore:\n     */\n    readonly _isIndexed!: boolean;\n\n    /**\n     *  Returns ``true`` if %%value%% is an **Indexed**.\n     *\n     *  This provides a Type Guard for property access.\n     */\n    static isIndexed(value: any): value is Indexed {\n        return !!(value && value._isIndexed);\n    }\n\n    /**\n     *  @_ignore:\n     */\n    constructor(hash: null | string) {\n        defineProperties<Indexed>(this, { hash, _isIndexed: true })\n    }\n}\n\ntype ErrorInfo = {\n    signature: string,\n    inputs: Array<string>,\n    name: string,\n    reason: (...args: Array<any>) => string;\n};\n\n// https://docs.soliditylang.org/en/v0.8.13/control-structures.html?highlight=panic#panic-via-assert-and-error-via-require\nconst PanicReasons: Record<string, string> = {\n    \"0\": \"generic panic\",\n    \"1\": \"assert(false)\",\n    \"17\": \"arithmetic overflow\",\n    \"18\": \"division or modulo by zero\",\n    \"33\": \"enum overflow\",\n    \"34\": \"invalid encoded storage byte array accessed\",\n    \"49\": \"out-of-bounds array access; popping on an empty array\",\n    \"50\": \"out-of-bounds access of an array or bytesN\",\n    \"65\": \"out of memory\",\n    \"81\": \"uninitialized function\",\n}\n\nconst BuiltinErrors: Record<string, ErrorInfo> = {\n    \"0x08c379a0\": {\n        signature: \"Error(string)\",\n        name: \"Error\",\n        inputs: [ \"string\" ],\n        reason: (message: string) => {\n            return `reverted with reason string ${ JSON.stringify(message) }`;\n        }\n    },\n    \"0x4e487b71\": {\n        signature: \"Panic(uint256)\",\n        name: \"Panic\",\n        inputs: [ \"uint256\" ],\n        reason: (code: bigint) => {\n            let reason = \"unknown panic code\";\n            if (code >= 0 && code <= 0xff && PanicReasons[code.toString()]) {\n                reason = PanicReasons[code.toString()];\n            }\n            return `reverted with panic code 0x${ code.toString(16) } (${ reason })`;\n        }\n    }\n}\n\n/*\nfunction wrapAccessError(property: string, error: Error): Error {\n    const wrap = new Error(`deferred error during ABI decoding triggered accessing ${ property }`);\n    (<any>wrap).error = error;\n    return wrap;\n}\n*/\n/*\nfunction checkNames(fragment: Fragment, type: \"input\" | \"output\", params: Array<ParamType>): void {\n    params.reduce((accum, param) => {\n        if (param.name) {\n            if (accum[param.name]) {\n                logger.throwArgumentError(`duplicate ${ type } parameter ${ JSON.stringify(param.name) } in ${ fragment.format(\"full\") }`, \"fragment\", fragment);\n            }\n            accum[param.name] = true;\n        }\n        return accum;\n    }, <{ [ name: string ]: boolean }>{ });\n}\n*/\n\n/**\n *  An **InterfaceAbi** may be any supported ABI format.\n *\n *  A string is expected to be a JSON string, which will be parsed\n *  using ``JSON.parse``. This means that the value **must** be a valid\n *  JSON string, with no stray commas, etc.\n *\n *  An array may contain any combination of:\n *  - Human-Readable fragments\n *  - Parsed JSON fragment\n *  - [[Fragment]] instances\n *\n *  A **Human-Readable Fragment** is a string which resembles a Solidity\n *  signature and is introduced in [this blog entry](link-ricmoo-humanreadableabi).\n *  For example, ``function balanceOf(address) view returns (uint)``.\n *\n *  A **Parsed JSON Fragment** is a JavaScript Object desribed in the\n *  [Solidity documentation](link-solc-jsonabi).\n */\nexport type InterfaceAbi = string | ReadonlyArray<Fragment | JsonFragment | string>;\n\n/**\n *  An Interface abstracts many of the low-level details for\n *  encoding and decoding the data on the blockchain.\n *\n *  An ABI provides information on how to encode data to send to\n *  a Contract, how to decode the results and events and how to\n *  interpret revert errors.\n *\n *  The ABI can be specified by [any supported format](InterfaceAbi).\n */\nexport class Interface {\n\n    /**\n     *  All the Contract ABI members (i.e. methods, events, errors, etc).\n     */\n    readonly fragments!: ReadonlyArray<Fragment>;\n\n    /**\n     *  The Contract constructor.\n     */\n    readonly deploy!: ConstructorFragment;\n\n    /**\n     *  The Fallback method, if any.\n     */\n    readonly fallback!: null | FallbackFragment;\n\n    /**\n     *  If receiving ether is supported.\n     */\n    readonly receive!: boolean;\n\n    #errors: Map<string, ErrorFragment>;\n    #events: Map<string, EventFragment>;\n    #functions: Map<string, FunctionFragment>;\n//    #structs: Map<string, StructFragment>;\n\n    #abiCoder: AbiCoder;\n\n    /**\n     *  Create a new Interface for the %%fragments%%.\n     */\n    constructor(fragments: InterfaceAbi) {\n        let abi: ReadonlyArray<Fragment | JsonFragment | string> = [ ];\n        if (typeof(fragments) === \"string\") {\n            abi = JSON.parse(fragments);\n        } else {\n            abi = fragments;\n        }\n\n        this.#functions = new Map();\n        this.#errors = new Map();\n        this.#events = new Map();\n//        this.#structs = new Map();\n\n\n        const frags: Array<Fragment> = [ ];\n        for (const a of abi) {\n            try {\n                frags.push(Fragment.from(a));\n            } catch (error: any) {\n                console.log(`[Warning] Invalid Fragment ${ JSON.stringify(a) }:`, error.message);\n            }\n        }\n\n        defineProperties<Interface>(this, {\n            fragments: Object.freeze(frags)\n        });\n\n        let fallback: null | FallbackFragment = null;\n        let receive = false;\n\n        this.#abiCoder = this.getAbiCoder();\n\n        // Add all fragments by their signature\n        this.fragments.forEach((fragment, index) => {\n            let bucket: Map<string, Fragment>;\n            switch (fragment.type) {\n                case \"constructor\":\n                    if (this.deploy) {\n                        console.log(\"duplicate definition - constructor\");\n                        return;\n                    }\n                    //checkNames(fragment, \"input\", fragment.inputs);\n                    defineProperties<Interface>(this, { deploy: <ConstructorFragment>fragment });\n                    return;\n\n                case \"fallback\":\n                    if (fragment.inputs.length === 0) {\n                        receive = true;\n                    } else {\n                        assertArgument(!fallback || (<FallbackFragment>fragment).payable !== fallback.payable,\n                            \"conflicting fallback fragments\", `fragments[${ index }]`, fragment);\n                        fallback = <FallbackFragment>fragment;\n                        receive = fallback.payable;\n                    }\n                    return;\n\n                case \"function\":\n                    //checkNames(fragment, \"input\", fragment.inputs);\n                    //checkNames(fragment, \"output\", (<FunctionFragment>fragment).outputs);\n                    bucket = this.#functions;\n                    break;\n\n                case \"event\":\n                    //checkNames(fragment, \"input\", fragment.inputs);\n                    bucket = this.#events;\n                    break;\n\n                case \"error\":\n                    bucket = this.#errors;\n                    break;\n\n                default:\n                    return;\n            }\n\n            // Two identical entries; ignore it\n            const signature = fragment.format();\n            if (bucket.has(signature)) { return; }\n\n            bucket.set(signature, fragment);\n        });\n\n        // If we do not have a constructor add a default\n        if (!this.deploy) {\n            defineProperties<Interface>(this, {\n                deploy: ConstructorFragment.from(\"constructor()\")\n            });\n        }\n\n        defineProperties<Interface>(this, { fallback, receive });\n    }\n\n    /**\n     *  Returns the entire Human-Readable ABI, as an array of\n     *  signatures, optionally as %%minimal%% strings, which\n     *  removes parameter names and unneceesary spaces.\n     */\n    format(minimal?: boolean): Array<string> {\n        const format = (minimal ? \"minimal\": \"full\");\n        const abi = this.fragments.map((f) => f.format(format));\n        return abi;\n    }\n\n    /**\n     *  Return the JSON-encoded ABI. This is the format Solidiy\n     *  returns.\n     */\n    formatJson(): string {\n        const abi = this.fragments.map((f) => f.format(\"json\"));\n\n        // We need to re-bundle the JSON fragments a bit\n        return JSON.stringify(abi.map((j) => JSON.parse(j)));\n    }\n\n    /**\n     *  The ABI coder that will be used to encode and decode binary\n     *  data.\n     */\n    getAbiCoder(): AbiCoder {\n        return AbiCoder.defaultAbiCoder();\n    }\n\n    // Find a function definition by any means necessary (unless it is ambiguous)\n    #getFunction(key: string, values: null | Array<any | Typed>, forceUnique: boolean): null | FunctionFragment {\n\n        // Selector\n        if (isHexString(key)) {\n            const selector = key.toLowerCase();\n            for (const fragment of this.#functions.values()) {\n                if (selector === fragment.selector) { return fragment; }\n            }\n            return null;\n        }\n\n        // It is a bare name, look up the function (will return null if ambiguous)\n        if (key.indexOf(\"(\") === -1) {\n            const matching: Array<FunctionFragment> = [ ];\n            for (const [ name, fragment ] of this.#functions) {\n                if (name.split(\"(\"/* fix:) */)[0] === key) { matching.push(fragment); }\n            }\n\n            if (values) {\n                const lastValue = (values.length > 0) ? values[values.length - 1]: null;\n\n                let valueLength = values.length;\n                let allowOptions = true;\n                if (Typed.isTyped(lastValue) && lastValue.type === \"overrides\") {\n                    allowOptions = false;\n                    valueLength--;\n                }\n\n                // Remove all matches that don't have a compatible length. The args\n                // may contain an overrides, so the match may have n or n - 1 parameters\n                for (let i = matching.length - 1; i >= 0; i--) {\n                    const inputs = matching[i].inputs.length;\n                    if (inputs !== valueLength && (!allowOptions || inputs !== valueLength - 1)) {\n                        matching.splice(i, 1);\n                    }\n                }\n\n                // Remove all matches that don't match the Typed signature\n                for (let i = matching.length - 1; i >= 0; i--) {\n                    const inputs = matching[i].inputs;\n                    for (let j = 0; j < values.length; j++) {\n                        // Not a typed value\n                        if (!Typed.isTyped(values[j])) { continue; }\n\n                        // We are past the inputs\n                        if (j >= inputs.length) {\n                            if (values[j].type === \"overrides\") { continue; }\n                            matching.splice(i, 1);\n                            break;\n                        }\n\n                        // Make sure the value type matches the input type\n                        if (values[j].type !== inputs[j].baseType) {\n                            matching.splice(i, 1);\n                            break;\n                        }\n                    }\n                }\n            }\n\n            // We found a single matching signature with an overrides, but the\n            // last value is something that cannot possibly be an options\n            if (matching.length === 1 && values && values.length !== matching[0].inputs.length) {\n                const lastArg = values[values.length - 1];\n                if (lastArg == null || Array.isArray(lastArg) || typeof(lastArg) !== \"object\") {\n                    matching.splice(0, 1);\n                }\n            }\n\n            if (matching.length === 0) { return null; }\n\n            if (matching.length > 1 && forceUnique) {\n                const matchStr = matching.map((m) => JSON.stringify(m.format())).join(\", \");\n                assertArgument(false, `ambiguous function description (i.e. matches ${ matchStr })`, \"key\", key);\n            }\n\n            return matching[0];\n        }\n\n        // Normalize the signature and lookup the function\n        const result = this.#functions.get(FunctionFragment.from(key).format());\n        if (result) { return result; }\n\n        return null;\n    }\n\n    /**\n     *  Get the function name for %%key%%, which may be a function selector,\n     *  function name or function signature that belongs to the ABI.\n     */\n    getFunctionName(key: string): string {\n        const fragment = this.#getFunction(key, null, false);\n        assertArgument(fragment, \"no matching function\", \"key\", key);\n        return fragment.name;\n    }\n\n    /**\n     *  Returns true if %%key%% (a function selector, function name or\n     *  function signature) is present in the ABI.\n     *\n     *  In the case of a function name, the name may be ambiguous, so\n     *  accessing the [[FunctionFragment]] may require refinement.\n     */\n    hasFunction(key: string): boolean {\n        return !!this.#getFunction(key, null, false);\n    }\n\n    /**\n     *  Get the [[FunctionFragment]] for %%key%%, which may be a function\n     *  selector, function name or function signature that belongs to the ABI.\n     *\n     *  If %%values%% is provided, it will use the Typed API to handle\n     *  ambiguous cases where multiple functions match by name.\n     *\n     *  If the %%key%% and %%values%% do not refine to a single function in\n     *  the ABI, this will throw.\n     */\n    getFunction(key: string, values?: Array<any | Typed>): null | FunctionFragment {\n        return this.#getFunction(key, values || null, true);\n    }\n\n    /**\n     *  Iterate over all functions, calling %%callback%%, sorted by their name.\n     */\n    forEachFunction(callback: (func: FunctionFragment, index: number) => void): void {\n        const names = Array.from(this.#functions.keys());\n        names.sort((a, b) => a.localeCompare(b));\n        for (let i = 0; i < names.length; i++) {\n            const name = names[i];\n            callback(<FunctionFragment>(this.#functions.get(name)), i);\n        }\n    }\n\n\n    // Find an event definition by any means necessary (unless it is ambiguous)\n    #getEvent(key: string, values: null | Array<null | any | Typed>, forceUnique: boolean): null | EventFragment {\n\n        // EventTopic\n        if (isHexString(key)) {\n            const eventTopic = key.toLowerCase();\n            for (const fragment of this.#events.values()) {\n                if (eventTopic === fragment.topicHash) { return fragment; }\n            }\n            return null;\n        }\n\n        // It is a bare name, look up the function (will return null if ambiguous)\n        if (key.indexOf(\"(\") === -1) {\n            const matching: Array<EventFragment> = [ ];\n            for (const [ name, fragment ] of this.#events) {\n                if (name.split(\"(\"/* fix:) */)[0] === key) { matching.push(fragment); }\n            }\n\n            if (values) {\n                // Remove all matches that don't have a compatible length.\n                for (let i = matching.length - 1; i >= 0; i--) {\n                    if (matching[i].inputs.length < values.length) {\n                        matching.splice(i, 1);\n                    }\n                }\n\n                // Remove all matches that don't match the Typed signature\n                for (let i = matching.length - 1; i >= 0; i--) {\n                    const inputs = matching[i].inputs;\n                    for (let j = 0; j < values.length; j++) {\n                        // Not a typed value\n                        if (!Typed.isTyped(values[j])) { continue; }\n\n                        // Make sure the value type matches the input type\n                        if (values[j].type !== inputs[j].baseType) {\n                            matching.splice(i, 1);\n                            break;\n                        }\n                    }\n                }\n            }\n\n            if (matching.length === 0) { return null; }\n\n            if (matching.length > 1 && forceUnique) {\n                const matchStr = matching.map((m) => JSON.stringify(m.format())).join(\", \");\n                assertArgument(false, `ambiguous event description (i.e. matches ${ matchStr })`, \"key\", key);\n            }\n\n            return matching[0];\n        }\n\n        // Normalize the signature and lookup the function\n        const result = this.#events.get(EventFragment.from(key).format());\n        if (result) { return result; }\n\n        return null;\n    }\n\n    /**\n     *  Get the event name for %%key%%, which may be a topic hash,\n     *  event name or event signature that belongs to the ABI.\n     */\n    getEventName(key: string): string {\n        const fragment = this.#getEvent(key, null, false);\n        assertArgument(fragment, \"no matching event\", \"key\", key);\n\n        return fragment.name;\n    }\n\n    /**\n     *  Returns true if %%key%% (an event topic hash, event name or\n     *  event signature) is present in the ABI.\n     *\n     *  In the case of an event name, the name may be ambiguous, so\n     *  accessing the [[EventFragment]] may require refinement.\n     */\n    hasEvent(key: string): boolean {\n        return !!this.#getEvent(key, null, false);\n    }\n\n    /**\n     *  Get the [[EventFragment]] for %%key%%, which may be a topic hash,\n     *  event name or event signature that belongs to the ABI.\n     *\n     *  If %%values%% is provided, it will use the Typed API to handle\n     *  ambiguous cases where multiple events match by name.\n     *\n     *  If the %%key%% and %%values%% do not refine to a single event in\n     *  the ABI, this will throw.\n     */\n    getEvent(key: string, values?: Array<any | Typed>): null | EventFragment {\n        return this.#getEvent(key, values || null, true)\n    }\n\n    /**\n     *  Iterate over all events, calling %%callback%%, sorted by their name.\n     */\n    forEachEvent(callback: (func: EventFragment, index: number) => void): void {\n        const names = Array.from(this.#events.keys());\n        names.sort((a, b) => a.localeCompare(b));\n        for (let i = 0; i < names.length; i++) {\n            const name = names[i];\n            callback(<EventFragment>(this.#events.get(name)), i);\n        }\n    }\n\n    /**\n     *  Get the [[ErrorFragment]] for %%key%%, which may be an error\n     *  selector, error name or error signature that belongs to the ABI.\n     *\n     *  If %%values%% is provided, it will use the Typed API to handle\n     *  ambiguous cases where multiple errors match by name.\n     *\n     *  If the %%key%% and %%values%% do not refine to a single error in\n     *  the ABI, this will throw.\n     */\n    getError(key: string, values?: Array<any | Typed>): null | ErrorFragment {\n        if (isHexString(key)) {\n            const selector = key.toLowerCase();\n\n            if (BuiltinErrors[selector]) {\n                return ErrorFragment.from(BuiltinErrors[selector].signature);\n            }\n\n            for (const fragment of this.#errors.values()) {\n                if (selector === fragment.selector) { return fragment; }\n            }\n\n            return null;\n        }\n\n        // It is a bare name, look up the function (will return null if ambiguous)\n        if (key.indexOf(\"(\") === -1) {\n            const matching: Array<ErrorFragment> = [ ];\n            for (const [ name, fragment ] of this.#errors) {\n                if (name.split(\"(\"/* fix:) */)[0] === key) { matching.push(fragment); }\n            }\n\n            if (matching.length === 0) {\n                if (key === \"Error\") { return ErrorFragment.from(\"error Error(string)\"); }\n                if (key === \"Panic\") { return ErrorFragment.from(\"error Panic(uint256)\"); }\n                return null;\n            } else if (matching.length > 1) {\n                const matchStr = matching.map((m) => JSON.stringify(m.format())).join(\", \");\n                assertArgument(false, `ambiguous error description (i.e. ${ matchStr })`, \"name\", key);\n            }\n\n            return matching[0];\n        }\n\n        // Normalize the signature and lookup the function\n        key = ErrorFragment.from(key).format()\n        if (key === \"Error(string)\") { return ErrorFragment.from(\"error Error(string)\"); }\n        if (key === \"Panic(uint256)\") { return ErrorFragment.from(\"error Panic(uint256)\"); }\n\n        const result = this.#errors.get(key);\n        if (result) { return result; }\n\n        return null;\n    }\n\n    /**\n     *  Iterate over all errors, calling %%callback%%, sorted by their name.\n     */\n    forEachError(callback: (func: ErrorFragment, index: number) => void): void {\n        const names = Array.from(this.#errors.keys());\n        names.sort((a, b) => a.localeCompare(b));\n        for (let i = 0; i < names.length; i++) {\n            const name = names[i];\n            callback(<ErrorFragment>(this.#errors.get(name)), i);\n        }\n    }\n\n    // Get the 4-byte selector used by Solidity to identify a function\n        /*\n    getSelector(fragment: ErrorFragment | FunctionFragment): string {\n        if (typeof(fragment) === \"string\") {\n            const matches: Array<Fragment> = [ ];\n\n            try { matches.push(this.getFunction(fragment)); } catch (error) { }\n            try { matches.push(this.getError(<string>fragment)); } catch (_) { }\n\n            if (matches.length === 0) {\n                logger.throwArgumentError(\"unknown fragment\", \"key\", fragment);\n            } else if (matches.length > 1) {\n                logger.throwArgumentError(\"ambiguous fragment matches function and error\", \"key\", fragment);\n            }\n\n            fragment = matches[0];\n        }\n\n        return dataSlice(id(fragment.format()), 0, 4);\n    }\n        */\n\n    // Get the 32-byte topic hash used by Solidity to identify an event\n    /*\n    getEventTopic(fragment: EventFragment): string {\n        //if (typeof(fragment) === \"string\") { fragment = this.getEvent(eventFragment); }\n        return id(fragment.format());\n    }\n    */\n\n\n    _decodeParams(params: ReadonlyArray<ParamType>, data: BytesLike): Result {\n        return this.#abiCoder.decode(params, data)\n    }\n\n    _encodeParams(params: ReadonlyArray<ParamType>, values: ReadonlyArray<any>): string {\n        return this.#abiCoder.encode(params, values)\n    }\n\n    /**\n     *  Encodes a ``tx.data`` object for deploying the Contract with\n     *  the %%values%% as the constructor arguments.\n     */\n    encodeDeploy(values?: ReadonlyArray<any>): string {\n        return this._encodeParams(this.deploy.inputs, values || [ ]);\n    }\n\n    /**\n     *  Decodes the result %%data%% (e.g. from an ``eth_call``) for the\n     *  specified error (see [[getError]] for valid values for\n     *  %%key%%).\n     *\n     *  Most developers should prefer the [[parseCallResult]] method instead,\n     *  which will automatically detect a ``CALL_EXCEPTION`` and throw the\n     *  corresponding error.\n     */\n    decodeErrorResult(fragment: ErrorFragment | string, data: BytesLike): Result {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getError(fragment);\n            assertArgument(f, \"unknown error\", \"fragment\", fragment);\n            fragment = f;\n        }\n\n        assertArgument(dataSlice(data, 0, 4) === fragment.selector,\n            `data signature does not match error ${ fragment.name }.`, \"data\", data);\n\n        return this._decodeParams(fragment.inputs, dataSlice(data, 4));\n    }\n\n    /**\n     *  Encodes the transaction revert data for a call result that\n     *  reverted from the the Contract with the sepcified %%error%%\n     *  (see [[getError]] for valid values for %%fragment%%) with the %%values%%.\n     *\n     *  This is generally not used by most developers, unless trying to mock\n     *  a result from a Contract.\n     */\n    encodeErrorResult(fragment: ErrorFragment | string, values?: ReadonlyArray<any>): string {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getError(fragment);\n            assertArgument(f, \"unknown error\", \"fragment\", fragment);\n            fragment = f;\n        }\n\n        return concat([\n            fragment.selector,\n            this._encodeParams(fragment.inputs, values || [ ])\n        ]);\n    }\n\n    /**\n     *  Decodes the %%data%% from a transaction ``tx.data`` for\n     *  the function specified (see [[getFunction]] for valid values\n     *  for %%fragment%%).\n     *\n     *  Most developers should prefer the [[parseTransaction]] method\n     *  instead, which will automatically detect the fragment.\n     */\n    decodeFunctionData(fragment: FunctionFragment | string, data: BytesLike): Result {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getFunction(fragment);\n            assertArgument(f, \"unknown function\", \"fragment\", fragment);\n            fragment = f;\n        }\n\n        assertArgument(dataSlice(data, 0, 4) === fragment.selector,\n            `data signature does not match function ${ fragment.name }.`, \"data\", data);\n\n        return this._decodeParams(fragment.inputs, dataSlice(data, 4));\n    }\n\n    /**\n     *  Encodes the ``tx.data`` for a transaction that calls the function\n     *  specified (see [[getFunction]] for valid values for %%fragment%%) with\n     *  the %%values%%.\n     */\n    encodeFunctionData(fragment: FunctionFragment | string, values?: ReadonlyArray<any>): string {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getFunction(fragment);\n            assertArgument(f, \"unknown function\", \"fragment\", fragment);\n            fragment = f;\n        }\n\n        return concat([\n            fragment.selector,\n            this._encodeParams(fragment.inputs, values || [ ])\n        ]);\n    }\n\n    /**\n     *  Decodes the result %%data%% (e.g. from an ``eth_call``) for the\n     *  specified function (see [[getFunction]] for valid values for\n     *  %%key%%).\n     *\n     *  Most developers should prefer the [[parseCallResult]] method instead,\n     *  which will automatically detect a ``CALL_EXCEPTION`` and throw the\n     *  corresponding error.\n     */\n    decodeFunctionResult(fragment: FunctionFragment | string, data: BytesLike): Result {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getFunction(fragment);\n            assertArgument(f, \"unknown function\", \"fragment\", fragment);\n            fragment = f;\n        }\n\n        let message = \"invalid length for result data\";\n\n        const bytes = getBytesCopy(data);\n        if ((bytes.length % 32) === 0) {\n            try {\n                return this.#abiCoder.decode(fragment.outputs, bytes);\n            } catch (error) {\n                message = \"could not decode result data\";\n            }\n        }\n\n        // Call returned data with no error, but the data is junk\n        assert(false, message, \"BAD_DATA\", {\n            value: hexlify(bytes),\n            info: { method: fragment.name, signature: fragment.format() }\n        });\n    }\n\n    makeError(_data: BytesLike, tx: CallExceptionTransaction): CallExceptionError {\n        const data = getBytes(_data, \"data\");\n\n        const error = AbiCoder.getBuiltinCallException(\"call\", tx, data);\n\n        // Not a built-in error; try finding a custom error\n        const customPrefix = \"execution reverted (unknown custom error)\";\n        if (error.message.startsWith(customPrefix)) {\n            const selector = hexlify(data.slice(0, 4));\n\n            const ef = this.getError(selector);\n            if (ef) {\n                try {\n                    const args = this.#abiCoder.decode(ef.inputs, data.slice(4));\n                    error.revert = {\n                        name: ef.name, signature: ef.format(), args\n                    };\n                    error.reason = error.revert.signature;\n                    error.message = `execution reverted: ${ error.reason }`\n                 } catch (e) {\n                    error.message = `execution reverted (coult not decode custom error)`\n                }\n            }\n        }\n\n        // Add the invocation, if available\n        const parsed = this.parseTransaction(tx);\n        if (parsed) {\n            error.invocation = {\n                method: parsed.name,\n                signature: parsed.signature,\n                args: parsed.args\n            };\n        }\n\n        return error;\n    }\n\n    /**\n     *  Encodes the result data (e.g. from an ``eth_call``) for the\n     *  specified function (see [[getFunction]] for valid values\n     *  for %%fragment%%) with %%values%%.\n     *\n     *  This is generally not used by most developers, unless trying to mock\n     *  a result from a Contract.\n     */\n    encodeFunctionResult(fragment: FunctionFragment | string, values?: ReadonlyArray<any>): string {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getFunction(fragment);\n            assertArgument(f, \"unknown function\", \"fragment\", fragment);\n            fragment = f;\n        }\n        return hexlify(this.#abiCoder.encode(fragment.outputs, values || [ ]));\n    }\n/*\n    spelunk(inputs: Array<ParamType>, values: ReadonlyArray<any>, processfunc: (type: string, value: any) => Promise<any>): Promise<Array<any>> {\n        const promises: Array<Promise<>> = [ ];\n        const process = function(type: ParamType, value: any): any {\n            if (type.baseType === \"array\") {\n                return descend(type.child\n            }\n            if (type. === \"address\") {\n            }\n        };\n\n        const descend = function (inputs: Array<ParamType>, values: ReadonlyArray<any>) {\n            if (inputs.length !== values.length) { throw new Error(\"length mismatch\"); }\n            \n        };\n\n        const result: Array<any> = [ ];\n        values.forEach((value, index) => {\n            if (value == null) {\n                topics.push(null);\n            } else if (param.baseType === \"array\" || param.baseType === \"tuple\") {\n                logger.throwArgumentError(\"filtering with tuples or arrays not supported\", (\"contract.\" + param.name), value);\n            } else if (Array.isArray(value)) {\n                topics.push(value.map((value) => encodeTopic(param, value)));\n            } else {\n                topics.push(encodeTopic(param, value));\n            }\n        });\n    }\n*/\n    // Create the filter for the event with search criteria (e.g. for eth_filterLog)\n    encodeFilterTopics(fragment: EventFragment | string, values: ReadonlyArray<any>): Array<null | string | Array<string>> {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getEvent(fragment);\n            assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n            fragment = f;\n        }\n\n        assert(values.length <= fragment.inputs.length, `too many arguments for ${ fragment.format() }`,\n            \"UNEXPECTED_ARGUMENT\", { count: values.length, expectedCount: fragment.inputs.length })\n\n        const topics: Array<null | string | Array<string>> = [];\n        if (!fragment.anonymous) { topics.push(fragment.topicHash); }\n\n        // @TODO: Use the coders for this; to properly support tuples, etc.\n        const encodeTopic = (param: ParamType, value: any): string => {\n            if (param.type === \"string\") {\n                 return id(value);\n            } else if (param.type === \"bytes\") {\n                 return keccak256(hexlify(value));\n            }\n\n            if (param.type === \"bool\" && typeof(value) === \"boolean\") {\n                value = (value ? \"0x01\": \"0x00\");\n            } else if (param.type.match(/^u?int/)) {\n                value = toBeHex(value);  // @TODO: Should this toTwos??\n            } else if (param.type.match(/^bytes/)) {\n                value = zeroPadBytes(value, 32);\n            } else if (param.type === \"address\") {\n                // Check addresses are valid\n                this.#abiCoder.encode( [ \"address\" ], [ value ]);\n            }\n\n            return zeroPadValue(hexlify(value), 32);\n        };\n\n        values.forEach((value, index) => {\n\n            const param = (<EventFragment>fragment).inputs[index];\n\n            if (!param.indexed) {\n                assertArgument(value == null,\n                    \"cannot filter non-indexed parameters; must be null\", (\"contract.\" + param.name), value);\n                return;\n            }\n\n            if (value == null) {\n                topics.push(null);\n            } else if (param.baseType === \"array\" || param.baseType === \"tuple\") {\n                assertArgument(false, \"filtering with tuples or arrays not supported\", (\"contract.\" + param.name), value);\n            } else if (Array.isArray(value)) {\n                topics.push(value.map((value) => encodeTopic(param, value)));\n            } else {\n                topics.push(encodeTopic(param, value));\n            }\n        });\n\n        // Trim off trailing nulls\n        while (topics.length && topics[topics.length - 1] === null) {\n            topics.pop();\n        }\n\n        return topics;\n    }\n\n    encodeEventLog(fragment: EventFragment | string, values: ReadonlyArray<any>): { data: string, topics: Array<string> } {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getEvent(fragment);\n            assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n            fragment = f;\n        }\n\n        const topics: Array<string> = [ ];\n\n        const dataTypes: Array<ParamType> = [ ];\n        const dataValues: Array<string> = [ ];\n\n        if (!fragment.anonymous) {\n            topics.push(fragment.topicHash);\n        }\n\n        assertArgument(values.length === fragment.inputs.length,\n            \"event arguments/values mismatch\", \"values\", values);\n\n        fragment.inputs.forEach((param, index) => {\n            const value = values[index];\n            if (param.indexed) {\n                if (param.type === \"string\") {\n                    topics.push(id(value))\n                } else if (param.type === \"bytes\") {\n                    topics.push(keccak256(value))\n                } else if (param.baseType === \"tuple\" || param.baseType === \"array\") {\n                    // @TODO\n                    throw new Error(\"not implemented\");\n                } else {\n                    topics.push(this.#abiCoder.encode([ param.type] , [ value ]));\n                }\n            } else {\n                dataTypes.push(param);\n                dataValues.push(value);\n            }\n        });\n\n        return {\n            data: this.#abiCoder.encode(dataTypes , dataValues),\n            topics: topics\n        };\n    }\n\n    // Decode a filter for the event and the search criteria\n    decodeEventLog(fragment: EventFragment | string, data: BytesLike, topics?: ReadonlyArray<string>): Result {\n        if (typeof(fragment) === \"string\") {\n            const f = this.getEvent(fragment);\n            assertArgument(f, \"unknown event\", \"eventFragment\", fragment);\n            fragment = f;\n        }\n\n        if (topics != null && !fragment.anonymous) {\n            const eventTopic = fragment.topicHash;\n            assertArgument(isHexString(topics[0], 32) && topics[0].toLowerCase() === eventTopic,\n                \"fragment/topic mismatch\", \"topics[0]\", topics[0]);\n            topics = topics.slice(1);\n        }\n\n        const indexed: Array<ParamType> = [];\n        const nonIndexed: Array<ParamType> = [];\n        const dynamic: Array<boolean> = [];\n\n        fragment.inputs.forEach((param, index) => {\n            if (param.indexed) {\n                if (param.type === \"string\" || param.type === \"bytes\" || param.baseType === \"tuple\" || param.baseType === \"array\") {\n                    indexed.push(ParamType.from({ type: \"bytes32\", name: param.name }));\n                    dynamic.push(true);\n                } else {\n                    indexed.push(param);\n                    dynamic.push(false);\n                }\n            } else {\n                nonIndexed.push(param);\n                dynamic.push(false);\n            }\n        });\n\n        const resultIndexed = (topics != null) ? this.#abiCoder.decode(indexed, concat(topics)): null;\n        const resultNonIndexed = this.#abiCoder.decode(nonIndexed, data, true);\n\n        //const result: (Array<any> & { [ key: string ]: any }) = [ ];\n        const values: Array<any> = [ ];\n        const keys: Array<null | string> = [ ];\n        let nonIndexedIndex = 0, indexedIndex = 0;\n        fragment.inputs.forEach((param, index) => {\n            let value: null | Indexed | Error = null;\n            if (param.indexed) {\n                if (resultIndexed == null) {\n                    value = new Indexed(null);\n\n                } else if (dynamic[index]) {\n                    value = new Indexed(resultIndexed[indexedIndex++]);\n\n                } else {\n                    try {\n                        value = resultIndexed[indexedIndex++];\n                    } catch (error: any) {\n                        value = error;\n                    }\n                }\n            } else {\n                try {\n                    value = resultNonIndexed[nonIndexedIndex++];\n                } catch (error: any) {\n                    value = error;\n                }\n            }\n\n            values.push(value);\n            keys.push(param.name || null);\n        });\n\n        return Result.fromItems(values, keys);\n    }\n\n    /**\n     *  Parses a transaction, finding the matching function and extracts\n     *  the parameter values along with other useful function details.\n     *\n     *  If the matching function cannot be found, return null.\n     */\n    parseTransaction(tx: { data: string, value?: BigNumberish }): null | TransactionDescription {\n        const data = getBytes(tx.data, \"tx.data\");\n        const value = getBigInt((tx.value != null) ? tx.value: 0, \"tx.value\");\n\n        const fragment = this.getFunction(hexlify(data.slice(0, 4)));\n\n        if (!fragment) { return null; }\n\n        const args = this.#abiCoder.decode(fragment.inputs, data.slice(4));\n        return new TransactionDescription(fragment, fragment.selector, args, value);\n    }\n\n    parseCallResult(data: BytesLike): Result {\n        throw new Error(\"@TODO\");\n    }\n\n    /**\n     *  Parses a receipt log, finding the matching event and extracts\n     *  the parameter values along with other useful event details.\n     *\n     *  If the matching event cannot be found, returns null.\n     */\n    parseLog(log: { topics: ReadonlyArray<string>, data: string}): null | LogDescription {\n        const fragment = this.getEvent(log.topics[0]);\n\n        if (!fragment || fragment.anonymous) { return null; }\n\n        // @TODO: If anonymous, and the only method, and the input count matches, should we parse?\n        //        Probably not, because just because it is the only event in the ABI does\n        //        not mean we have the full ABI; maybe just a fragment?\n\n\n       return new LogDescription(fragment, fragment.topicHash, this.decodeEventLog(fragment, log.data, log.topics));\n    }\n\n    /**\n     *  Parses a revert data, finding the matching error and extracts\n     *  the parameter values along with other useful error details.\n     *\n     *  If the matching error cannot be found, returns null.\n     */\n    parseError(data: BytesLike): null | ErrorDescription {\n        const hexData = hexlify(data);\n\n        const fragment = this.getError(dataSlice(hexData, 0, 4));\n\n        if (!fragment) { return null; }\n\n        const args = this.#abiCoder.decode(fragment.inputs, dataSlice(hexData, 4));\n        return new ErrorDescription(fragment, fragment.selector, args);\n    }\n\n    /**\n     *  Creates a new [[Interface]] from the ABI %%value%%.\n     *\n     *  The %%value%% may be provided as an existing [[Interface]] object,\n     *  a JSON-encoded ABI or any Human-Readable ABI format.\n     */\n    static from(value: InterfaceAbi | Interface): Interface {\n        // Already an Interface, which is immutable\n        if (value instanceof Interface) { return value; }\n\n        // JSON\n        if (typeof(value) === \"string\") { return new Interface(JSON.parse(value)); }\n\n        // An Interface; possibly from another v6 instance\n        if (typeof((<any>value).formatJson) === \"function\") {\n            return new Interface((<any>value).formatJson());\n        }\n\n        // A legacy Interface; from an older version\n        if (typeof((<any>value).format) === \"function\") {\n            return new Interface((<any>value).format(\"json\"));\n        }\n\n        // Array of fragments\n        return new Interface(value);\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;AAYA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SACIC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,YAAY,EACpDC,OAAO,EAAEC,YAAY,EAAEC,YAAY,EAAEC,WAAW,EAAEC,gBAAgB,EAClEC,cAAc,EAAEC,OAAO,EAAEC,MAAM,QAC5B,mBAAmB;AAE1B,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,iBAAiB,EAAEC,MAAM,QAAQ,4BAA4B;AACtE,SACIC,mBAAmB,EAAEC,aAAa,EAAEC,aAAa,EACjDC,QAAQ,EAAEC,gBAAgB,EAAEC,SAAS,QAClC,gBAAgB;AACvB,SAASC,KAAK,QAAQ,YAAY;AAOlC,SAASR,iBAAiB,EAAEC,MAAM;AAElC;;;;AAIA,OAAM,MAAOQ,cAAc;EACvB;;;EAGSC,QAAQ;EAEjB;;;EAGSC,IAAI;EAEb;;;EAGSC,SAAS;EAElB;;;EAGSC,KAAK;EAEd;;;EAGSC,IAAI;EAEb;;;EAGAC,YAAYL,QAAuB,EAAEG,KAAa,EAAEC,IAAY;IAC5D,MAAMH,IAAI,GAAGD,QAAQ,CAACC,IAAI;MAAEC,SAAS,GAAGF,QAAQ,CAACM,MAAM,EAAE;IACzDrB,gBAAgB,CAAiB,IAAI,EAAE;MACnCe,QAAQ;MAAEC,IAAI;MAAEC,SAAS;MAAEC,KAAK;MAAEC;KACrC,CAAC;EACN;;AAGJ;;;;;AAKA,OAAM,MAAOG,sBAAsB;EAC/B;;;EAGSP,QAAQ;EAEjB;;;EAGSC,IAAI;EAEb;;;EAGSG,IAAI;EAEb;;;EAGSF,SAAS;EAElB;;;EAGSM,QAAQ;EAEjB;;;EAGSC,KAAK;EAEd;;;EAGAJ,YAAYL,QAA0B,EAAEQ,QAAgB,EAAEJ,IAAY,EAAEK,KAAa;IACjF,MAAMR,IAAI,GAAGD,QAAQ,CAACC,IAAI;MAAEC,SAAS,GAAGF,QAAQ,CAACM,MAAM,EAAE;IACzDrB,gBAAgB,CAAyB,IAAI,EAAE;MAC3Ce,QAAQ;MAAEC,IAAI;MAAEG,IAAI;MAAEF,SAAS;MAAEM,QAAQ;MAAEC;KAC9C,CAAC;EACN;;AAGJ;;;;AAIA,OAAM,MAAOC,gBAAgB;EACzB;;;EAGSV,QAAQ;EAEjB;;;EAGSC,IAAI;EAEb;;;EAGSG,IAAI;EAEb;;;EAGSF,SAAS;EAElB;;;EAGSM,QAAQ;EAEjB;;;EAGAH,YAAYL,QAAuB,EAAEQ,QAAgB,EAAEJ,IAAY;IAC/D,MAAMH,IAAI,GAAGD,QAAQ,CAACC,IAAI;MAAEC,SAAS,GAAGF,QAAQ,CAACM,MAAM,EAAE;IACzDrB,gBAAgB,CAAmB,IAAI,EAAE;MACrCe,QAAQ;MAAEC,IAAI;MAAEG,IAAI;MAAEF,SAAS;MAAEM;KACpC,CAAC;EACN;;AAGJ;;;;;;AAMA,OAAM,MAAOG,OAAO;EAChB;;;EAGSC,IAAI;EAEb;;;EAGSC,UAAU;EAEnB;;;;;EAKA,OAAOC,SAASA,CAACL,KAAU;IACvB,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACI,UAAU,CAAC;EACxC;EAEA;;;EAGAR,YAAYO,IAAmB;IAC3B3B,gBAAgB,CAAU,IAAI,EAAE;MAAE2B,IAAI;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EAC/D;;AAUJ;AACA,MAAME,YAAY,GAA2B;EACzC,GAAG,EAAE,eAAe;EACpB,GAAG,EAAE,eAAe;EACpB,IAAI,EAAE,qBAAqB;EAC3B,IAAI,EAAE,4BAA4B;EAClC,IAAI,EAAE,eAAe;EACrB,IAAI,EAAE,6CAA6C;EACnD,IAAI,EAAE,uDAAuD;EAC7D,IAAI,EAAE,4CAA4C;EAClD,IAAI,EAAE,eAAe;EACrB,IAAI,EAAE;CACT;AAED,MAAMC,aAAa,GAA8B;EAC7C,YAAY,EAAE;IACVd,SAAS,EAAE,eAAe;IAC1BD,IAAI,EAAE,OAAO;IACbgB,MAAM,EAAE,CAAE,QAAQ,CAAE;IACpBC,MAAM,EAAGC,OAAe,IAAI;MACxB,OAAO,+BAAgCC,IAAI,CAACC,SAAS,CAACF,OAAO,CAAE,EAAE;IACrE;GACH;EACD,YAAY,EAAE;IACVjB,SAAS,EAAE,gBAAgB;IAC3BD,IAAI,EAAE,OAAO;IACbgB,MAAM,EAAE,CAAE,SAAS,CAAE;IACrBC,MAAM,EAAGI,IAAY,IAAI;MACrB,IAAIJ,MAAM,GAAG,oBAAoB;MACjC,IAAII,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,IAAIP,YAAY,CAACO,IAAI,CAACC,QAAQ,EAAE,CAAC,EAAE;QAC5DL,MAAM,GAAGH,YAAY,CAACO,IAAI,CAACC,QAAQ,EAAE,CAAC;;MAE1C,OAAO,8BAA+BD,IAAI,CAACC,QAAQ,CAAC,EAAE,CAAE,KAAML,MAAO,GAAG;IAC5E;;CAEP;AA4CD;;;;;;;;;;AAUA,OAAM,MAAOM,SAAS;EAElB;;;EAGSC,SAAS;EAElB;;;EAGSC,MAAM;EAEf;;;EAGSC,QAAQ;EAEjB;;;EAGSC,OAAO;EAEhB,CAAAC,MAAO;EACP,CAAAC,MAAO;EACP,CAAAC,SAAU;EACd;EAEI,CAAAC,QAAS;EAET;;;EAGA3B,YAAYoB,SAAuB;IAC/B,IAAIQ,GAAG,GAAoD,EAAG;IAC9D,IAAI,OAAOR,SAAU,KAAK,QAAQ,EAAE;MAChCQ,GAAG,GAAGb,IAAI,CAACc,KAAK,CAACT,SAAS,CAAC;KAC9B,MAAM;MACHQ,GAAG,GAAGR,SAAS;;IAGnB,IAAI,CAAC,CAAAM,SAAU,GAAG,IAAII,GAAG,EAAE;IAC3B,IAAI,CAAC,CAAAN,MAAO,GAAG,IAAIM,GAAG,EAAE;IACxB,IAAI,CAAC,CAAAL,MAAO,GAAG,IAAIK,GAAG,EAAE;IAChC;IAGQ,MAAMC,KAAK,GAAoB,EAAG;IAClC,KAAK,MAAMC,CAAC,IAAIJ,GAAG,EAAE;MACjB,IAAI;QACAG,KAAK,CAACE,IAAI,CAAC3C,QAAQ,CAAC4C,IAAI,CAACF,CAAC,CAAC,CAAC;OAC/B,CAAC,OAAOG,KAAU,EAAE;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA+BtB,IAAI,CAACC,SAAS,CAACgB,CAAC,CAAE,GAAG,EAAEG,KAAK,CAACrB,OAAO,CAAC;;;IAIxFlC,gBAAgB,CAAY,IAAI,EAAE;MAC9BwC,SAAS,EAAEkB,MAAM,CAACC,MAAM,CAACR,KAAK;KACjC,CAAC;IAEF,IAAIT,QAAQ,GAA4B,IAAI;IAC5C,IAAIC,OAAO,GAAG,KAAK;IAEnB,IAAI,CAAC,CAAAI,QAAS,GAAG,IAAI,CAACa,WAAW,EAAE;IAEnC;IACA,IAAI,CAACpB,SAAS,CAACqB,OAAO,CAAC,CAAC9C,QAAQ,EAAE+C,KAAK,KAAI;MACvC,IAAIC,MAA6B;MACjC,QAAQhD,QAAQ,CAACiD,IAAI;QACjB,KAAK,aAAa;UACd,IAAI,IAAI,CAACvB,MAAM,EAAE;YACbe,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;YACjD;;UAEJ;UACAzD,gBAAgB,CAAY,IAAI,EAAE;YAAEyC,MAAM,EAAuB1B;UAAQ,CAAE,CAAC;UAC5E;QAEJ,KAAK,UAAU;UACX,IAAIA,QAAQ,CAACiB,MAAM,CAACiC,MAAM,KAAK,CAAC,EAAE;YAC9BtB,OAAO,GAAG,IAAI;WACjB,MAAM;YACH1C,cAAc,CAAC,CAACyC,QAAQ,IAAuB3B,QAAS,CAACmD,OAAO,KAAKxB,QAAQ,CAACwB,OAAO,EACjF,gCAAgC,EAAE,aAAcJ,KAAM,GAAG,EAAE/C,QAAQ,CAAC;YACxE2B,QAAQ,GAAqB3B,QAAQ;YACrC4B,OAAO,GAAGD,QAAQ,CAACwB,OAAO;;UAE9B;QAEJ,KAAK,UAAU;UACX;UACA;UACAH,MAAM,GAAG,IAAI,CAAC,CAAAjB,SAAU;UACxB;QAEJ,KAAK,OAAO;UACR;UACAiB,MAAM,GAAG,IAAI,CAAC,CAAAlB,MAAO;UACrB;QAEJ,KAAK,OAAO;UACRkB,MAAM,GAAG,IAAI,CAAC,CAAAnB,MAAO;UACrB;QAEJ;UACI;;MAGR;MACA,MAAM3B,SAAS,GAAGF,QAAQ,CAACM,MAAM,EAAE;MACnC,IAAI0C,MAAM,CAACI,GAAG,CAAClD,SAAS,CAAC,EAAE;QAAE;;MAE7B8C,MAAM,CAACK,GAAG,CAACnD,SAAS,EAAEF,QAAQ,CAAC;IACnC,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAAC0B,MAAM,EAAE;MACdzC,gBAAgB,CAAY,IAAI,EAAE;QAC9ByC,MAAM,EAAElC,mBAAmB,CAAC+C,IAAI,CAAC,eAAe;OACnD,CAAC;;IAGNtD,gBAAgB,CAAY,IAAI,EAAE;MAAE0C,QAAQ;MAAEC;IAAO,CAAE,CAAC;EAC5D;EAEA;;;;;EAKAtB,MAAMA,CAACgD,OAAiB;IACpB,MAAMhD,MAAM,GAAIgD,OAAO,GAAG,SAAS,GAAE,MAAO;IAC5C,MAAMrB,GAAG,GAAG,IAAI,CAACR,SAAS,CAAC8B,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAClD,MAAM,CAACA,MAAM,CAAC,CAAC;IACvD,OAAO2B,GAAG;EACd;EAEA;;;;EAIAwB,UAAUA,CAAA;IACN,MAAMxB,GAAG,GAAG,IAAI,CAACR,SAAS,CAAC8B,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAClD,MAAM,CAAC,MAAM,CAAC,CAAC;IAEvD;IACA,OAAOc,IAAI,CAACC,SAAS,CAACY,GAAG,CAACsB,GAAG,CAAEG,CAAC,IAAKtC,IAAI,CAACc,KAAK,CAACwB,CAAC,CAAC,CAAC,CAAC;EACxD;EAEA;;;;EAIAb,WAAWA,CAAA;IACP,OAAOxD,QAAQ,CAACsE,eAAe,EAAE;EACrC;EAEA;EACA,CAAAC,WAAYC,CAACC,GAAW,EAAEC,MAAiC,EAAEC,WAAoB;IAE7E;IACA,IAAIhF,WAAW,CAAC8E,GAAG,CAAC,EAAE;MAClB,MAAMtD,QAAQ,GAAGsD,GAAG,CAACG,WAAW,EAAE;MAClC,KAAK,MAAMjE,QAAQ,IAAI,IAAI,CAAC,CAAA+B,SAAU,CAACgC,MAAM,EAAE,EAAE;QAC7C,IAAIvD,QAAQ,KAAKR,QAAQ,CAACQ,QAAQ,EAAE;UAAE,OAAOR,QAAQ;;;MAEzD,OAAO,IAAI;;IAGf;IACA,IAAI8D,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACzB,MAAMC,QAAQ,GAA4B,EAAG;MAC7C,KAAK,MAAM,CAAElE,IAAI,EAAED,QAAQ,CAAE,IAAI,IAAI,CAAC,CAAA+B,SAAU,EAAE;QAC9C,IAAI9B,IAAI,CAACmE,KAAK,CAAC,GAAG,YAAW,CAAC,CAAC,CAAC,CAAC,KAAKN,GAAG,EAAE;UAAEK,QAAQ,CAAC7B,IAAI,CAACtC,QAAQ,CAAC;;;MAGxE,IAAI+D,MAAM,EAAE;QACR,MAAMM,SAAS,GAAIN,MAAM,CAACb,MAAM,GAAG,CAAC,GAAIa,MAAM,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC,GAAE,IAAI;QAEvE,IAAIoB,WAAW,GAAGP,MAAM,CAACb,MAAM;QAC/B,IAAIqB,YAAY,GAAG,IAAI;QACvB,IAAIzE,KAAK,CAAC0E,OAAO,CAACH,SAAS,CAAC,IAAIA,SAAS,CAACpB,IAAI,KAAK,WAAW,EAAE;UAC5DsB,YAAY,GAAG,KAAK;UACpBD,WAAW,EAAE;;QAGjB;QACA;QACA,KAAK,IAAIG,CAAC,GAAGN,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,MAAMxD,MAAM,GAAGkD,QAAQ,CAACM,CAAC,CAAC,CAACxD,MAAM,CAACiC,MAAM;UACxC,IAAIjC,MAAM,KAAKqD,WAAW,KAAK,CAACC,YAAY,IAAItD,MAAM,KAAKqD,WAAW,GAAG,CAAC,CAAC,EAAE;YACzEH,QAAQ,CAACO,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;;;QAI7B;QACA,KAAK,IAAIA,CAAC,GAAGN,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,MAAMxD,MAAM,GAAGkD,QAAQ,CAACM,CAAC,CAAC,CAACxD,MAAM;UACjC,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAACb,MAAM,EAAEQ,CAAC,EAAE,EAAE;YACpC;YACA,IAAI,CAAC5D,KAAK,CAAC0E,OAAO,CAACT,MAAM,CAACL,CAAC,CAAC,CAAC,EAAE;cAAE;;YAEjC;YACA,IAAIA,CAAC,IAAIzC,MAAM,CAACiC,MAAM,EAAE;cACpB,IAAIa,MAAM,CAACL,CAAC,CAAC,CAACT,IAAI,KAAK,WAAW,EAAE;gBAAE;;cACtCkB,QAAQ,CAACO,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;cACrB;;YAGJ;YACA,IAAIV,MAAM,CAACL,CAAC,CAAC,CAACT,IAAI,KAAKhC,MAAM,CAACyC,CAAC,CAAC,CAACiB,QAAQ,EAAE;cACvCR,QAAQ,CAACO,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;cACrB;;;;;MAMhB;MACA;MACA,IAAIN,QAAQ,CAACjB,MAAM,KAAK,CAAC,IAAIa,MAAM,IAAIA,MAAM,CAACb,MAAM,KAAKiB,QAAQ,CAAC,CAAC,CAAC,CAAClD,MAAM,CAACiC,MAAM,EAAE;QAChF,MAAM0B,OAAO,GAAGb,MAAM,CAACA,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC;QACzC,IAAI0B,OAAO,IAAI,IAAI,IAAIC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,IAAI,OAAOA,OAAQ,KAAK,QAAQ,EAAE;UAC3ET,QAAQ,CAACO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;;;MAI7B,IAAIP,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;;MAExC,IAAIiB,QAAQ,CAACjB,MAAM,GAAG,CAAC,IAAIc,WAAW,EAAE;QACpC,MAAMe,QAAQ,GAAGZ,QAAQ,CAACZ,GAAG,CAAEyB,CAAC,IAAK5D,IAAI,CAACC,SAAS,CAAC2D,CAAC,CAAC1E,MAAM,EAAE,CAAC,CAAC,CAAC2E,IAAI,CAAC,IAAI,CAAC;QAC3E/F,cAAc,CAAC,KAAK,EAAE,gDAAiD6F,QAAS,GAAG,EAAE,KAAK,EAAEjB,GAAG,CAAC;;MAGpG,OAAOK,QAAQ,CAAC,CAAC,CAAC;;IAGtB;IACA,MAAMe,MAAM,GAAG,IAAI,CAAC,CAAAnD,SAAU,CAACoD,GAAG,CAACvF,gBAAgB,CAAC2C,IAAI,CAACuB,GAAG,CAAC,CAACxD,MAAM,EAAE,CAAC;IACvE,IAAI4E,MAAM,EAAE;MAAE,OAAOA,MAAM;;IAE3B,OAAO,IAAI;EACf;EAEA;;;;EAIAE,eAAeA,CAACtB,GAAW;IACvB,MAAM9D,QAAQ,GAAG,IAAI,CAAC,CAAA4D,WAAY,CAACE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;IACpD5E,cAAc,CAACc,QAAQ,EAAE,sBAAsB,EAAE,KAAK,EAAE8D,GAAG,CAAC;IAC5D,OAAO9D,QAAQ,CAACC,IAAI;EACxB;EAEA;;;;;;;EAOAoF,WAAWA,CAACvB,GAAW;IACnB,OAAO,CAAC,CAAC,IAAI,CAAC,CAAAF,WAAY,CAACE,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;EAChD;EAEA;;;;;;;;;;EAUAF,WAAWA,CAACE,GAAW,EAAEC,MAA2B;IAChD,OAAO,IAAI,CAAC,CAAAH,WAAY,CAACE,GAAG,EAAEC,MAAM,IAAI,IAAI,EAAE,IAAI,CAAC;EACvD;EAEA;;;EAGAuB,eAAeA,CAACC,QAAyD;IACrE,MAAMC,KAAK,GAAGX,KAAK,CAACtC,IAAI,CAAC,IAAI,CAAC,CAAAR,SAAU,CAAC0D,IAAI,EAAE,CAAC;IAChDD,KAAK,CAACE,IAAI,CAAC,CAACrD,CAAC,EAAEsD,CAAC,KAAKtD,CAAC,CAACuD,aAAa,CAACD,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACtC,MAAM,EAAEuB,CAAC,EAAE,EAAE;MACnC,MAAMxE,IAAI,GAAGuF,KAAK,CAACf,CAAC,CAAC;MACrBc,QAAQ,CAAoB,IAAI,CAAC,CAAAxD,SAAU,CAACoD,GAAG,CAAClF,IAAI,CAAC,EAAGwE,CAAC,CAAC;;EAElE;EAGA;EACA,CAAAoB,QAASC,CAAChC,GAAW,EAAEC,MAAwC,EAAEC,WAAoB;IAEjF;IACA,IAAIhF,WAAW,CAAC8E,GAAG,CAAC,EAAE;MAClB,MAAMiC,UAAU,GAAGjC,GAAG,CAACG,WAAW,EAAE;MACpC,KAAK,MAAMjE,QAAQ,IAAI,IAAI,CAAC,CAAA8B,MAAO,CAACiC,MAAM,EAAE,EAAE;QAC1C,IAAIgC,UAAU,KAAK/F,QAAQ,CAACgG,SAAS,EAAE;UAAE,OAAOhG,QAAQ;;;MAE5D,OAAO,IAAI;;IAGf;IACA,IAAI8D,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACzB,MAAMC,QAAQ,GAAyB,EAAG;MAC1C,KAAK,MAAM,CAAElE,IAAI,EAAED,QAAQ,CAAE,IAAI,IAAI,CAAC,CAAA8B,MAAO,EAAE;QAC3C,IAAI7B,IAAI,CAACmE,KAAK,CAAC,GAAG,YAAW,CAAC,CAAC,CAAC,CAAC,KAAKN,GAAG,EAAE;UAAEK,QAAQ,CAAC7B,IAAI,CAACtC,QAAQ,CAAC;;;MAGxE,IAAI+D,MAAM,EAAE;QACR;QACA,KAAK,IAAIU,CAAC,GAAGN,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,IAAIN,QAAQ,CAACM,CAAC,CAAC,CAACxD,MAAM,CAACiC,MAAM,GAAGa,MAAM,CAACb,MAAM,EAAE;YAC3CiB,QAAQ,CAACO,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;;;QAI7B;QACA,KAAK,IAAIA,CAAC,GAAGN,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAEuB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC3C,MAAMxD,MAAM,GAAGkD,QAAQ,CAACM,CAAC,CAAC,CAACxD,MAAM;UACjC,KAAK,IAAIyC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,CAACb,MAAM,EAAEQ,CAAC,EAAE,EAAE;YACpC;YACA,IAAI,CAAC5D,KAAK,CAAC0E,OAAO,CAACT,MAAM,CAACL,CAAC,CAAC,CAAC,EAAE;cAAE;;YAEjC;YACA,IAAIK,MAAM,CAACL,CAAC,CAAC,CAACT,IAAI,KAAKhC,MAAM,CAACyC,CAAC,CAAC,CAACiB,QAAQ,EAAE;cACvCR,QAAQ,CAACO,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;cACrB;;;;;MAMhB,IAAIN,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;;MAExC,IAAIiB,QAAQ,CAACjB,MAAM,GAAG,CAAC,IAAIc,WAAW,EAAE;QACpC,MAAMe,QAAQ,GAAGZ,QAAQ,CAACZ,GAAG,CAAEyB,CAAC,IAAK5D,IAAI,CAACC,SAAS,CAAC2D,CAAC,CAAC1E,MAAM,EAAE,CAAC,CAAC,CAAC2E,IAAI,CAAC,IAAI,CAAC;QAC3E/F,cAAc,CAAC,KAAK,EAAE,6CAA8C6F,QAAS,GAAG,EAAE,KAAK,EAAEjB,GAAG,CAAC;;MAGjG,OAAOK,QAAQ,CAAC,CAAC,CAAC;;IAGtB;IACA,MAAMe,MAAM,GAAG,IAAI,CAAC,CAAApD,MAAO,CAACqD,GAAG,CAACzF,aAAa,CAAC6C,IAAI,CAACuB,GAAG,CAAC,CAACxD,MAAM,EAAE,CAAC;IACjE,IAAI4E,MAAM,EAAE;MAAE,OAAOA,MAAM;;IAE3B,OAAO,IAAI;EACf;EAEA;;;;EAIAe,YAAYA,CAACnC,GAAW;IACpB,MAAM9D,QAAQ,GAAG,IAAI,CAAC,CAAA6F,QAAS,CAAC/B,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;IACjD5E,cAAc,CAACc,QAAQ,EAAE,mBAAmB,EAAE,KAAK,EAAE8D,GAAG,CAAC;IAEzD,OAAO9D,QAAQ,CAACC,IAAI;EACxB;EAEA;;;;;;;EAOAiG,QAAQA,CAACpC,GAAW;IAChB,OAAO,CAAC,CAAC,IAAI,CAAC,CAAA+B,QAAS,CAAC/B,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC;EAC7C;EAEA;;;;;;;;;;EAUA+B,QAAQA,CAAC/B,GAAW,EAAEC,MAA2B;IAC7C,OAAO,IAAI,CAAC,CAAA8B,QAAS,CAAC/B,GAAG,EAAEC,MAAM,IAAI,IAAI,EAAE,IAAI,CAAC;EACpD;EAEA;;;EAGAoC,YAAYA,CAACZ,QAAsD;IAC/D,MAAMC,KAAK,GAAGX,KAAK,CAACtC,IAAI,CAAC,IAAI,CAAC,CAAAT,MAAO,CAAC2D,IAAI,EAAE,CAAC;IAC7CD,KAAK,CAACE,IAAI,CAAC,CAACrD,CAAC,EAAEsD,CAAC,KAAKtD,CAAC,CAACuD,aAAa,CAACD,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACtC,MAAM,EAAEuB,CAAC,EAAE,EAAE;MACnC,MAAMxE,IAAI,GAAGuF,KAAK,CAACf,CAAC,CAAC;MACrBc,QAAQ,CAAiB,IAAI,CAAC,CAAAzD,MAAO,CAACqD,GAAG,CAAClF,IAAI,CAAC,EAAGwE,CAAC,CAAC;;EAE5D;EAEA;;;;;;;;;;EAUA2B,QAAQA,CAACtC,GAAW,EAAEC,MAA2B;IAC7C,IAAI/E,WAAW,CAAC8E,GAAG,CAAC,EAAE;MAClB,MAAMtD,QAAQ,GAAGsD,GAAG,CAACG,WAAW,EAAE;MAElC,IAAIjD,aAAa,CAACR,QAAQ,CAAC,EAAE;QACzB,OAAOf,aAAa,CAAC8C,IAAI,CAACvB,aAAa,CAACR,QAAQ,CAAC,CAACN,SAAS,CAAC;;MAGhE,KAAK,MAAMF,QAAQ,IAAI,IAAI,CAAC,CAAA6B,MAAO,CAACkC,MAAM,EAAE,EAAE;QAC1C,IAAIvD,QAAQ,KAAKR,QAAQ,CAACQ,QAAQ,EAAE;UAAE,OAAOR,QAAQ;;;MAGzD,OAAO,IAAI;;IAGf;IACA,IAAI8D,GAAG,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACzB,MAAMC,QAAQ,GAAyB,EAAG;MAC1C,KAAK,MAAM,CAAElE,IAAI,EAAED,QAAQ,CAAE,IAAI,IAAI,CAAC,CAAA6B,MAAO,EAAE;QAC3C,IAAI5B,IAAI,CAACmE,KAAK,CAAC,GAAG,YAAW,CAAC,CAAC,CAAC,CAAC,KAAKN,GAAG,EAAE;UAAEK,QAAQ,CAAC7B,IAAI,CAACtC,QAAQ,CAAC;;;MAGxE,IAAImE,QAAQ,CAACjB,MAAM,KAAK,CAAC,EAAE;QACvB,IAAIY,GAAG,KAAK,OAAO,EAAE;UAAE,OAAOrE,aAAa,CAAC8C,IAAI,CAAC,qBAAqB,CAAC;;QACvE,IAAIuB,GAAG,KAAK,OAAO,EAAE;UAAE,OAAOrE,aAAa,CAAC8C,IAAI,CAAC,sBAAsB,CAAC;;QACxE,OAAO,IAAI;OACd,MAAM,IAAI4B,QAAQ,CAACjB,MAAM,GAAG,CAAC,EAAE;QAC5B,MAAM6B,QAAQ,GAAGZ,QAAQ,CAACZ,GAAG,CAAEyB,CAAC,IAAK5D,IAAI,CAACC,SAAS,CAAC2D,CAAC,CAAC1E,MAAM,EAAE,CAAC,CAAC,CAAC2E,IAAI,CAAC,IAAI,CAAC;QAC3E/F,cAAc,CAAC,KAAK,EAAE,qCAAsC6F,QAAS,GAAG,EAAE,MAAM,EAAEjB,GAAG,CAAC;;MAG1F,OAAOK,QAAQ,CAAC,CAAC,CAAC;;IAGtB;IACAL,GAAG,GAAGrE,aAAa,CAAC8C,IAAI,CAACuB,GAAG,CAAC,CAACxD,MAAM,EAAE;IACtC,IAAIwD,GAAG,KAAK,eAAe,EAAE;MAAE,OAAOrE,aAAa,CAAC8C,IAAI,CAAC,qBAAqB,CAAC;;IAC/E,IAAIuB,GAAG,KAAK,gBAAgB,EAAE;MAAE,OAAOrE,aAAa,CAAC8C,IAAI,CAAC,sBAAsB,CAAC;;IAEjF,MAAM2C,MAAM,GAAG,IAAI,CAAC,CAAArD,MAAO,CAACsD,GAAG,CAACrB,GAAG,CAAC;IACpC,IAAIoB,MAAM,EAAE;MAAE,OAAOA,MAAM;;IAE3B,OAAO,IAAI;EACf;EAEA;;;EAGAmB,YAAYA,CAACd,QAAsD;IAC/D,MAAMC,KAAK,GAAGX,KAAK,CAACtC,IAAI,CAAC,IAAI,CAAC,CAAAV,MAAO,CAAC4D,IAAI,EAAE,CAAC;IAC7CD,KAAK,CAACE,IAAI,CAAC,CAACrD,CAAC,EAAEsD,CAAC,KAAKtD,CAAC,CAACuD,aAAa,CAACD,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACtC,MAAM,EAAEuB,CAAC,EAAE,EAAE;MACnC,MAAMxE,IAAI,GAAGuF,KAAK,CAACf,CAAC,CAAC;MACrBc,QAAQ,CAAiB,IAAI,CAAC,CAAA1D,MAAO,CAACsD,GAAG,CAAClF,IAAI,CAAC,EAAGwE,CAAC,CAAC;;EAE5D;EAEA;EACI;;;;;;;;;;;;;;;;EAqBJ;EACA;;;;;;EAQA6B,aAAaA,CAACC,MAAgC,EAAEC,IAAe;IAC3D,OAAO,IAAI,CAAC,CAAAxE,QAAS,CAACyE,MAAM,CAACF,MAAM,EAAEC,IAAI,CAAC;EAC9C;EAEAE,aAAaA,CAACH,MAAgC,EAAExC,MAA0B;IACtE,OAAO,IAAI,CAAC,CAAA/B,QAAS,CAAC2E,MAAM,CAACJ,MAAM,EAAExC,MAAM,CAAC;EAChD;EAEA;;;;EAIA6C,YAAYA,CAAC7C,MAA2B;IACpC,OAAO,IAAI,CAAC2C,aAAa,CAAC,IAAI,CAAChF,MAAM,CAACT,MAAM,EAAE8C,MAAM,IAAI,EAAG,CAAC;EAChE;EAEA;;;;;;;;;EASA8C,iBAAiBA,CAAC7G,QAAgC,EAAEwG,IAAe;IAC/D,IAAI,OAAOxG,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAAC4C,QAAQ,CAACpG,QAAQ,CAAC;MACjCd,cAAc,CAACsE,CAAC,EAAE,eAAe,EAAE,UAAU,EAAExD,QAAQ,CAAC;MACxDA,QAAQ,GAAGwD,CAAC;;IAGhBtE,cAAc,CAACT,SAAS,CAAC+H,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,KAAKxG,QAAQ,CAACQ,QAAQ,EACtD,uCAAwCR,QAAQ,CAACC,IAAK,GAAG,EAAE,MAAM,EAAEuG,IAAI,CAAC;IAE5E,OAAO,IAAI,CAACF,aAAa,CAACtG,QAAQ,CAACiB,MAAM,EAAExC,SAAS,CAAC+H,IAAI,EAAE,CAAC,CAAC,CAAC;EAClE;EAEA;;;;;;;;EAQAM,iBAAiBA,CAAC9G,QAAgC,EAAE+D,MAA2B;IAC3E,IAAI,OAAO/D,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAAC4C,QAAQ,CAACpG,QAAQ,CAAC;MACjCd,cAAc,CAACsE,CAAC,EAAE,eAAe,EAAE,UAAU,EAAExD,QAAQ,CAAC;MACxDA,QAAQ,GAAGwD,CAAC;;IAGhB,OAAOhF,MAAM,CAAC,CACVwB,QAAQ,CAACQ,QAAQ,EACjB,IAAI,CAACkG,aAAa,CAAC1G,QAAQ,CAACiB,MAAM,EAAE8C,MAAM,IAAI,EAAG,CAAC,CACrD,CAAC;EACN;EAEA;;;;;;;;EAQAgD,kBAAkBA,CAAC/G,QAAmC,EAAEwG,IAAe;IACnE,IAAI,OAAOxG,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACI,WAAW,CAAC5D,QAAQ,CAAC;MACpCd,cAAc,CAACsE,CAAC,EAAE,kBAAkB,EAAE,UAAU,EAAExD,QAAQ,CAAC;MAC3DA,QAAQ,GAAGwD,CAAC;;IAGhBtE,cAAc,CAACT,SAAS,CAAC+H,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,KAAKxG,QAAQ,CAACQ,QAAQ,EACtD,0CAA2CR,QAAQ,CAACC,IAAK,GAAG,EAAE,MAAM,EAAEuG,IAAI,CAAC;IAE/E,OAAO,IAAI,CAACF,aAAa,CAACtG,QAAQ,CAACiB,MAAM,EAAExC,SAAS,CAAC+H,IAAI,EAAE,CAAC,CAAC,CAAC;EAClE;EAEA;;;;;EAKAQ,kBAAkBA,CAAChH,QAAmC,EAAE+D,MAA2B;IAC/E,IAAI,OAAO/D,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACI,WAAW,CAAC5D,QAAQ,CAAC;MACpCd,cAAc,CAACsE,CAAC,EAAE,kBAAkB,EAAE,UAAU,EAAExD,QAAQ,CAAC;MAC3DA,QAAQ,GAAGwD,CAAC;;IAGhB,OAAOhF,MAAM,CAAC,CACVwB,QAAQ,CAACQ,QAAQ,EACjB,IAAI,CAACkG,aAAa,CAAC1G,QAAQ,CAACiB,MAAM,EAAE8C,MAAM,IAAI,EAAG,CAAC,CACrD,CAAC;EACN;EAEA;;;;;;;;;EASAkD,oBAAoBA,CAACjH,QAAmC,EAAEwG,IAAe;IACrE,IAAI,OAAOxG,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACI,WAAW,CAAC5D,QAAQ,CAAC;MACpCd,cAAc,CAACsE,CAAC,EAAE,kBAAkB,EAAE,UAAU,EAAExD,QAAQ,CAAC;MAC3DA,QAAQ,GAAGwD,CAAC;;IAGhB,IAAIrC,OAAO,GAAG,gCAAgC;IAE9C,MAAM+F,KAAK,GAAGtI,YAAY,CAAC4H,IAAI,CAAC;IAChC,IAAKU,KAAK,CAAChE,MAAM,GAAG,EAAE,KAAM,CAAC,EAAE;MAC3B,IAAI;QACA,OAAO,IAAI,CAAC,CAAAlB,QAAS,CAACyE,MAAM,CAACzG,QAAQ,CAACmH,OAAO,EAAED,KAAK,CAAC;OACxD,CAAC,OAAO1E,KAAK,EAAE;QACZrB,OAAO,GAAG,8BAA8B;;;IAIhD;IACA/B,MAAM,CAAC,KAAK,EAAE+B,OAAO,EAAE,UAAU,EAAE;MAC/BV,KAAK,EAAE5B,OAAO,CAACqI,KAAK,CAAC;MACrBE,IAAI,EAAE;QAAEC,MAAM,EAAErH,QAAQ,CAACC,IAAI;QAAEC,SAAS,EAAEF,QAAQ,CAACM,MAAM;MAAE;KAC9D,CAAC;EACN;EAEAgH,SAASA,CAACC,KAAgB,EAAEC,EAA4B;IACpD,MAAMhB,IAAI,GAAG7H,QAAQ,CAAC4I,KAAK,EAAE,MAAM,CAAC;IAEpC,MAAM/E,KAAK,GAAGnD,QAAQ,CAACoI,uBAAuB,CAAC,MAAM,EAAED,EAAE,EAAEhB,IAAI,CAAC;IAEhE;IACA,MAAMkB,YAAY,GAAG,2CAA2C;IAChE,IAAIlF,KAAK,CAACrB,OAAO,CAACwG,UAAU,CAACD,YAAY,CAAC,EAAE;MACxC,MAAMlH,QAAQ,GAAG3B,OAAO,CAAC2H,IAAI,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAE1C,MAAMC,EAAE,GAAG,IAAI,CAACzB,QAAQ,CAAC5F,QAAQ,CAAC;MAClC,IAAIqH,EAAE,EAAE;QACJ,IAAI;UACA,MAAMzH,IAAI,GAAG,IAAI,CAAC,CAAA4B,QAAS,CAACyE,MAAM,CAACoB,EAAE,CAAC5G,MAAM,EAAEuF,IAAI,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5DpF,KAAK,CAACsF,MAAM,GAAG;YACX7H,IAAI,EAAE4H,EAAE,CAAC5H,IAAI;YAAEC,SAAS,EAAE2H,EAAE,CAACvH,MAAM,EAAE;YAAEF;WAC1C;UACDoC,KAAK,CAACtB,MAAM,GAAGsB,KAAK,CAACsF,MAAM,CAAC5H,SAAS;UACrCsC,KAAK,CAACrB,OAAO,GAAG,uBAAwBqB,KAAK,CAACtB,MAAO,EAAE;SACzD,CAAC,OAAO6G,CAAC,EAAE;UACTvF,KAAK,CAACrB,OAAO,GAAG,oDAAoD;;;;IAKhF;IACA,MAAM6G,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACT,EAAE,CAAC;IACxC,IAAIQ,MAAM,EAAE;MACRxF,KAAK,CAAC0F,UAAU,GAAG;QACfb,MAAM,EAAEW,MAAM,CAAC/H,IAAI;QACnBC,SAAS,EAAE8H,MAAM,CAAC9H,SAAS;QAC3BE,IAAI,EAAE4H,MAAM,CAAC5H;OAChB;;IAGL,OAAOoC,KAAK;EAChB;EAEA;;;;;;;;EAQA2F,oBAAoBA,CAACnI,QAAmC,EAAE+D,MAA2B;IACjF,IAAI,OAAO/D,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACI,WAAW,CAAC5D,QAAQ,CAAC;MACpCd,cAAc,CAACsE,CAAC,EAAE,kBAAkB,EAAE,UAAU,EAAExD,QAAQ,CAAC;MAC3DA,QAAQ,GAAGwD,CAAC;;IAEhB,OAAO3E,OAAO,CAAC,IAAI,CAAC,CAAAmD,QAAS,CAAC2E,MAAM,CAAC3G,QAAQ,CAACmH,OAAO,EAAEpD,MAAM,IAAI,EAAG,CAAC,CAAC;EAC1E;EACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BI;EACAqE,kBAAkBA,CAACpI,QAAgC,EAAE+D,MAA0B;IAC3E,IAAI,OAAO/D,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACqC,QAAQ,CAAC7F,QAAQ,CAAC;MACjCd,cAAc,CAACsE,CAAC,EAAE,eAAe,EAAE,eAAe,EAAExD,QAAQ,CAAC;MAC7DA,QAAQ,GAAGwD,CAAC;;IAGhBpE,MAAM,CAAC2E,MAAM,CAACb,MAAM,IAAIlD,QAAQ,CAACiB,MAAM,CAACiC,MAAM,EAAE,0BAA2BlD,QAAQ,CAACM,MAAM,EAAG,EAAE,EAC3F,qBAAqB,EAAE;MAAE+H,KAAK,EAAEtE,MAAM,CAACb,MAAM;MAAEoF,aAAa,EAAEtI,QAAQ,CAACiB,MAAM,CAACiC;IAAM,CAAE,CAAC;IAE3F,MAAMqF,MAAM,GAAyC,EAAE;IACvD,IAAI,CAACvI,QAAQ,CAACwI,SAAS,EAAE;MAAED,MAAM,CAACjG,IAAI,CAACtC,QAAQ,CAACgG,SAAS,CAAC;;IAE1D;IACA,MAAMyC,WAAW,GAAGA,CAACC,KAAgB,EAAEjI,KAAU,KAAY;MACzD,IAAIiI,KAAK,CAACzF,IAAI,KAAK,QAAQ,EAAE;QACxB,OAAO1E,EAAE,CAACkC,KAAK,CAAC;OACpB,MAAM,IAAIiI,KAAK,CAACzF,IAAI,KAAK,OAAO,EAAE;QAC9B,OAAO3E,SAAS,CAACO,OAAO,CAAC4B,KAAK,CAAC,CAAC;;MAGrC,IAAIiI,KAAK,CAACzF,IAAI,KAAK,MAAM,IAAI,OAAOxC,KAAM,KAAK,SAAS,EAAE;QACtDA,KAAK,GAAIA,KAAK,GAAG,MAAM,GAAE,MAAO;OACnC,MAAM,IAAIiI,KAAK,CAACzF,IAAI,CAAC0F,KAAK,CAAC,QAAQ,CAAC,EAAE;QACnClI,KAAK,GAAGtB,OAAO,CAACsB,KAAK,CAAC,CAAC,CAAE;OAC5B,MAAM,IAAIiI,KAAK,CAACzF,IAAI,CAAC0F,KAAK,CAAC,QAAQ,CAAC,EAAE;QACnClI,KAAK,GAAG3B,YAAY,CAAC2B,KAAK,EAAE,EAAE,CAAC;OAClC,MAAM,IAAIiI,KAAK,CAACzF,IAAI,KAAK,SAAS,EAAE;QACjC;QACA,IAAI,CAAC,CAAAjB,QAAS,CAAC2E,MAAM,CAAE,CAAE,SAAS,CAAE,EAAE,CAAElG,KAAK,CAAE,CAAC;;MAGpD,OAAO1B,YAAY,CAACF,OAAO,CAAC4B,KAAK,CAAC,EAAE,EAAE,CAAC;IAC3C,CAAC;IAEDsD,MAAM,CAACjB,OAAO,CAAC,CAACrC,KAAK,EAAEsC,KAAK,KAAI;MAE5B,MAAM2F,KAAK,GAAmB1I,QAAS,CAACiB,MAAM,CAAC8B,KAAK,CAAC;MAErD,IAAI,CAAC2F,KAAK,CAACE,OAAO,EAAE;QAChB1J,cAAc,CAACuB,KAAK,IAAI,IAAI,EACxB,oDAAoD,EAAG,WAAW,GAAGiI,KAAK,CAACzI,IAAI,EAAGQ,KAAK,CAAC;QAC5F;;MAGJ,IAAIA,KAAK,IAAI,IAAI,EAAE;QACf8H,MAAM,CAACjG,IAAI,CAAC,IAAI,CAAC;OACpB,MAAM,IAAIoG,KAAK,CAAC/D,QAAQ,KAAK,OAAO,IAAI+D,KAAK,CAAC/D,QAAQ,KAAK,OAAO,EAAE;QACjEzF,cAAc,CAAC,KAAK,EAAE,+CAA+C,EAAG,WAAW,GAAGwJ,KAAK,CAACzI,IAAI,EAAGQ,KAAK,CAAC;OAC5G,MAAM,IAAIoE,KAAK,CAACC,OAAO,CAACrE,KAAK,CAAC,EAAE;QAC7B8H,MAAM,CAACjG,IAAI,CAAC7B,KAAK,CAAC8C,GAAG,CAAE9C,KAAK,IAAKgI,WAAW,CAACC,KAAK,EAAEjI,KAAK,CAAC,CAAC,CAAC;OAC/D,MAAM;QACH8H,MAAM,CAACjG,IAAI,CAACmG,WAAW,CAACC,KAAK,EAAEjI,KAAK,CAAC,CAAC;;IAE9C,CAAC,CAAC;IAEF;IACA,OAAO8H,MAAM,CAACrF,MAAM,IAAIqF,MAAM,CAACA,MAAM,CAACrF,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;MACxDqF,MAAM,CAACM,GAAG,EAAE;;IAGhB,OAAON,MAAM;EACjB;EAEAO,cAAcA,CAAC9I,QAAgC,EAAE+D,MAA0B;IACvE,IAAI,OAAO/D,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACqC,QAAQ,CAAC7F,QAAQ,CAAC;MACjCd,cAAc,CAACsE,CAAC,EAAE,eAAe,EAAE,eAAe,EAAExD,QAAQ,CAAC;MAC7DA,QAAQ,GAAGwD,CAAC;;IAGhB,MAAM+E,MAAM,GAAkB,EAAG;IAEjC,MAAMQ,SAAS,GAAqB,EAAG;IACvC,MAAMC,UAAU,GAAkB,EAAG;IAErC,IAAI,CAAChJ,QAAQ,CAACwI,SAAS,EAAE;MACrBD,MAAM,CAACjG,IAAI,CAACtC,QAAQ,CAACgG,SAAS,CAAC;;IAGnC9G,cAAc,CAAC6E,MAAM,CAACb,MAAM,KAAKlD,QAAQ,CAACiB,MAAM,CAACiC,MAAM,EACnD,iCAAiC,EAAE,QAAQ,EAAEa,MAAM,CAAC;IAExD/D,QAAQ,CAACiB,MAAM,CAAC6B,OAAO,CAAC,CAAC4F,KAAK,EAAE3F,KAAK,KAAI;MACrC,MAAMtC,KAAK,GAAGsD,MAAM,CAAChB,KAAK,CAAC;MAC3B,IAAI2F,KAAK,CAACE,OAAO,EAAE;QACf,IAAIF,KAAK,CAACzF,IAAI,KAAK,QAAQ,EAAE;UACzBsF,MAAM,CAACjG,IAAI,CAAC/D,EAAE,CAACkC,KAAK,CAAC,CAAC;SACzB,MAAM,IAAIiI,KAAK,CAACzF,IAAI,KAAK,OAAO,EAAE;UAC/BsF,MAAM,CAACjG,IAAI,CAAChE,SAAS,CAACmC,KAAK,CAAC,CAAC;SAChC,MAAM,IAAIiI,KAAK,CAAC/D,QAAQ,KAAK,OAAO,IAAI+D,KAAK,CAAC/D,QAAQ,KAAK,OAAO,EAAE;UACjE;UACA,MAAM,IAAIsE,KAAK,CAAC,iBAAiB,CAAC;SACrC,MAAM;UACHV,MAAM,CAACjG,IAAI,CAAC,IAAI,CAAC,CAAAN,QAAS,CAAC2E,MAAM,CAAC,CAAE+B,KAAK,CAACzF,IAAI,CAAC,EAAG,CAAExC,KAAK,CAAE,CAAC,CAAC;;OAEpE,MAAM;QACHsI,SAAS,CAACzG,IAAI,CAACoG,KAAK,CAAC;QACrBM,UAAU,CAAC1G,IAAI,CAAC7B,KAAK,CAAC;;IAE9B,CAAC,CAAC;IAEF,OAAO;MACH+F,IAAI,EAAE,IAAI,CAAC,CAAAxE,QAAS,CAAC2E,MAAM,CAACoC,SAAS,EAAGC,UAAU,CAAC;MACnDT,MAAM,EAAEA;KACX;EACL;EAEA;EACAW,cAAcA,CAAClJ,QAAgC,EAAEwG,IAAe,EAAE+B,MAA8B;IAC5F,IAAI,OAAOvI,QAAS,KAAK,QAAQ,EAAE;MAC/B,MAAMwD,CAAC,GAAG,IAAI,CAACqC,QAAQ,CAAC7F,QAAQ,CAAC;MACjCd,cAAc,CAACsE,CAAC,EAAE,eAAe,EAAE,eAAe,EAAExD,QAAQ,CAAC;MAC7DA,QAAQ,GAAGwD,CAAC;;IAGhB,IAAI+E,MAAM,IAAI,IAAI,IAAI,CAACvI,QAAQ,CAACwI,SAAS,EAAE;MACvC,MAAMzC,UAAU,GAAG/F,QAAQ,CAACgG,SAAS;MACrC9G,cAAc,CAACF,WAAW,CAACuJ,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAACtE,WAAW,EAAE,KAAK8B,UAAU,EAC/E,yBAAyB,EAAE,WAAW,EAAEwC,MAAM,CAAC,CAAC,CAAC,CAAC;MACtDA,MAAM,GAAGA,MAAM,CAACX,KAAK,CAAC,CAAC,CAAC;;IAG5B,MAAMgB,OAAO,GAAqB,EAAE;IACpC,MAAMO,UAAU,GAAqB,EAAE;IACvC,MAAMC,OAAO,GAAmB,EAAE;IAElCpJ,QAAQ,CAACiB,MAAM,CAAC6B,OAAO,CAAC,CAAC4F,KAAK,EAAE3F,KAAK,KAAI;MACrC,IAAI2F,KAAK,CAACE,OAAO,EAAE;QACf,IAAIF,KAAK,CAACzF,IAAI,KAAK,QAAQ,IAAIyF,KAAK,CAACzF,IAAI,KAAK,OAAO,IAAIyF,KAAK,CAAC/D,QAAQ,KAAK,OAAO,IAAI+D,KAAK,CAAC/D,QAAQ,KAAK,OAAO,EAAE;UAC/GiE,OAAO,CAACtG,IAAI,CAACzC,SAAS,CAAC0C,IAAI,CAAC;YAAEU,IAAI,EAAE,SAAS;YAAEhD,IAAI,EAAEyI,KAAK,CAACzI;UAAI,CAAE,CAAC,CAAC;UACnEmJ,OAAO,CAAC9G,IAAI,CAAC,IAAI,CAAC;SACrB,MAAM;UACHsG,OAAO,CAACtG,IAAI,CAACoG,KAAK,CAAC;UACnBU,OAAO,CAAC9G,IAAI,CAAC,KAAK,CAAC;;OAE1B,MAAM;QACH6G,UAAU,CAAC7G,IAAI,CAACoG,KAAK,CAAC;QACtBU,OAAO,CAAC9G,IAAI,CAAC,KAAK,CAAC;;IAE3B,CAAC,CAAC;IAEF,MAAM+G,aAAa,GAAId,MAAM,IAAI,IAAI,GAAI,IAAI,CAAC,CAAAvG,QAAS,CAACyE,MAAM,CAACmC,OAAO,EAAEpK,MAAM,CAAC+J,MAAM,CAAC,CAAC,GAAE,IAAI;IAC7F,MAAMe,gBAAgB,GAAG,IAAI,CAAC,CAAAtH,QAAS,CAACyE,MAAM,CAAC0C,UAAU,EAAE3C,IAAI,EAAE,IAAI,CAAC;IAEtE;IACA,MAAMzC,MAAM,GAAe,EAAG;IAC9B,MAAM0B,IAAI,GAAyB,EAAG;IACtC,IAAI8D,eAAe,GAAG,CAAC;MAAEC,YAAY,GAAG,CAAC;IACzCxJ,QAAQ,CAACiB,MAAM,CAAC6B,OAAO,CAAC,CAAC4F,KAAK,EAAE3F,KAAK,KAAI;MACrC,IAAItC,KAAK,GAA2B,IAAI;MACxC,IAAIiI,KAAK,CAACE,OAAO,EAAE;QACf,IAAIS,aAAa,IAAI,IAAI,EAAE;UACvB5I,KAAK,GAAG,IAAIE,OAAO,CAAC,IAAI,CAAC;SAE5B,MAAM,IAAIyI,OAAO,CAACrG,KAAK,CAAC,EAAE;UACvBtC,KAAK,GAAG,IAAIE,OAAO,CAAC0I,aAAa,CAACG,YAAY,EAAE,CAAC,CAAC;SAErD,MAAM;UACH,IAAI;YACA/I,KAAK,GAAG4I,aAAa,CAACG,YAAY,EAAE,CAAC;WACxC,CAAC,OAAOhH,KAAU,EAAE;YACjB/B,KAAK,GAAG+B,KAAK;;;OAGxB,MAAM;QACH,IAAI;UACA/B,KAAK,GAAG6I,gBAAgB,CAACC,eAAe,EAAE,CAAC;SAC9C,CAAC,OAAO/G,KAAU,EAAE;UACjB/B,KAAK,GAAG+B,KAAK;;;MAIrBuB,MAAM,CAACzB,IAAI,CAAC7B,KAAK,CAAC;MAClBgF,IAAI,CAACnD,IAAI,CAACoG,KAAK,CAACzI,IAAI,IAAI,IAAI,CAAC;IACjC,CAAC,CAAC;IAEF,OAAOV,MAAM,CAACkK,SAAS,CAAC1F,MAAM,EAAE0B,IAAI,CAAC;EACzC;EAEA;;;;;;EAMAwC,gBAAgBA,CAACT,EAA0C;IACvD,MAAMhB,IAAI,GAAG7H,QAAQ,CAAC6I,EAAE,CAAChB,IAAI,EAAE,SAAS,CAAC;IACzC,MAAM/F,KAAK,GAAG/B,SAAS,CAAE8I,EAAE,CAAC/G,KAAK,IAAI,IAAI,GAAI+G,EAAE,CAAC/G,KAAK,GAAE,CAAC,EAAE,UAAU,CAAC;IAErE,MAAMT,QAAQ,GAAG,IAAI,CAAC4D,WAAW,CAAC/E,OAAO,CAAC2H,IAAI,CAACoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAE5D,IAAI,CAAC5H,QAAQ,EAAE;MAAE,OAAO,IAAI;;IAE5B,MAAMI,IAAI,GAAG,IAAI,CAAC,CAAA4B,QAAS,CAACyE,MAAM,CAACzG,QAAQ,CAACiB,MAAM,EAAEuF,IAAI,CAACoB,KAAK,CAAC,CAAC,CAAC,CAAC;IAClE,OAAO,IAAIrH,sBAAsB,CAACP,QAAQ,EAAEA,QAAQ,CAACQ,QAAQ,EAAEJ,IAAI,EAAEK,KAAK,CAAC;EAC/E;EAEAiJ,eAAeA,CAAClD,IAAe;IAC3B,MAAM,IAAIyC,KAAK,CAAC,OAAO,CAAC;EAC5B;EAEA;;;;;;EAMAU,QAAQA,CAACjH,GAAmD;IACxD,MAAM1C,QAAQ,GAAG,IAAI,CAAC6F,QAAQ,CAACnD,GAAG,CAAC6F,MAAM,CAAC,CAAC,CAAC,CAAC;IAE7C,IAAI,CAACvI,QAAQ,IAAIA,QAAQ,CAACwI,SAAS,EAAE;MAAE,OAAO,IAAI;;IAElD;IACA;IACA;IAGD,OAAO,IAAIzI,cAAc,CAACC,QAAQ,EAAEA,QAAQ,CAACgG,SAAS,EAAE,IAAI,CAACkD,cAAc,CAAClJ,QAAQ,EAAE0C,GAAG,CAAC8D,IAAI,EAAE9D,GAAG,CAAC6F,MAAM,CAAC,CAAC;EAC/G;EAEA;;;;;;EAMAqB,UAAUA,CAACpD,IAAe;IACtB,MAAMqD,OAAO,GAAGhL,OAAO,CAAC2H,IAAI,CAAC;IAE7B,MAAMxG,QAAQ,GAAG,IAAI,CAACoG,QAAQ,CAAC3H,SAAS,CAACoL,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAExD,IAAI,CAAC7J,QAAQ,EAAE;MAAE,OAAO,IAAI;;IAE5B,MAAMI,IAAI,GAAG,IAAI,CAAC,CAAA4B,QAAS,CAACyE,MAAM,CAACzG,QAAQ,CAACiB,MAAM,EAAExC,SAAS,CAACoL,OAAO,EAAE,CAAC,CAAC,CAAC;IAC1E,OAAO,IAAInJ,gBAAgB,CAACV,QAAQ,EAAEA,QAAQ,CAACQ,QAAQ,EAAEJ,IAAI,CAAC;EAClE;EAEA;;;;;;EAMA,OAAOmC,IAAIA,CAAC9B,KAA+B;IACvC;IACA,IAAIA,KAAK,YAAYe,SAAS,EAAE;MAAE,OAAOf,KAAK;;IAE9C;IACA,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;MAAE,OAAO,IAAIe,SAAS,CAACJ,IAAI,CAACc,KAAK,CAACzB,KAAK,CAAC,CAAC;;IAEzE;IACA,IAAI,OAAaA,KAAM,CAACgD,UAAW,KAAK,UAAU,EAAE;MAChD,OAAO,IAAIjC,SAAS,CAAOf,KAAM,CAACgD,UAAU,EAAE,CAAC;;IAGnD;IACA,IAAI,OAAahD,KAAM,CAACH,MAAO,KAAK,UAAU,EAAE;MAC5C,OAAO,IAAIkB,SAAS,CAAOf,KAAM,CAACH,MAAM,CAAC,MAAM,CAAC,CAAC;;IAGrD;IACA,OAAO,IAAIkB,SAAS,CAACf,KAAK,CAAC;EAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}