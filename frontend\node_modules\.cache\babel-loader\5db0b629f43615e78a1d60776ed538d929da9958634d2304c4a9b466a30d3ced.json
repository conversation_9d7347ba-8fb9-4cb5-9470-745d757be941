{"ast": null, "code": "/* Browser Crypto Shims */\nimport { hmac } from \"@noble/hashes/hmac\";\nimport { pbkdf2 } from \"@noble/hashes/pbkdf2\";\nimport { sha256 } from \"@noble/hashes/sha256\";\nimport { sha512 } from \"@noble/hashes/sha512\";\nimport { assert, assertArgument } from \"../utils/index.js\";\nfunction getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('unable to locate global object');\n}\n;\nconst anyGlobal = getGlobal();\nconst crypto = anyGlobal.crypto || anyGlobal.msCrypto;\nexport function createHash(algo) {\n  switch (algo) {\n    case \"sha256\":\n      return sha256.create();\n    case \"sha512\":\n      return sha512.create();\n  }\n  assertArgument(false, \"invalid hashing algorithm name\", \"algorithm\", algo);\n}\nexport function createHmac(_algo, key) {\n  const algo = {\n    sha256,\n    sha512\n  }[_algo];\n  assertArgument(algo != null, \"invalid hmac algorithm\", \"algorithm\", _algo);\n  return hmac.create(algo, key);\n}\nexport function pbkdf2Sync(password, salt, iterations, keylen, _algo) {\n  const algo = {\n    sha256,\n    sha512\n  }[_algo];\n  assertArgument(algo != null, \"invalid pbkdf2 algorithm\", \"algorithm\", _algo);\n  return pbkdf2(algo, password, salt, {\n    c: iterations,\n    dkLen: keylen\n  });\n}\nexport function randomBytes(length) {\n  assert(crypto != null, \"platform does not support secure random numbers\", \"UNSUPPORTED_OPERATION\", {\n    operation: \"randomBytes\"\n  });\n  assertArgument(Number.isInteger(length) && length > 0 && length <= 1024, \"invalid length\", \"length\", length);\n  const result = new Uint8Array(length);\n  crypto.getRandomValues(result);\n  return result;\n}", "map": {"version": 3, "names": ["hmac", "pbkdf2", "sha256", "sha512", "assert", "assertArgument", "getGlobal", "self", "window", "global", "Error", "anyGlobal", "crypto", "msCrypto", "createHash", "algo", "create", "createHmac", "_algo", "key", "pbkdf2Sync", "password", "salt", "iterations", "keylen", "c", "dkLen", "randomBytes", "length", "operation", "Number", "isInteger", "result", "Uint8Array", "getRandomValues"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\crypto-browser.ts"], "sourcesContent": ["/* Browser Crypto Shims */\n\nimport { hmac } from \"@noble/hashes/hmac\";\nimport { pbkdf2 } from \"@noble/hashes/pbkdf2\";\nimport { sha256 } from \"@noble/hashes/sha256\";\nimport { sha512 } from \"@noble/hashes/sha512\";\n\nimport { assert, assertArgument } from \"../utils/index.js\";\n\n\ndeclare global {\n    interface Window { }\n\n    const window: Window;\n    const self: Window;\n}\n\n\nfunction getGlobal(): any {\n  if (typeof self !== 'undefined') { return self; }\n  if (typeof window !== 'undefined') { return window; }\n  if (typeof global !== 'undefined') { return global; }\n  throw new Error('unable to locate global object');\n};\n\nconst anyGlobal = getGlobal();\nconst crypto: any = anyGlobal.crypto || anyGlobal.msCrypto;\n\n\nexport interface CryptoHasher {\n    update(data: Uint8Array): CryptoHasher;\n    digest(): Uint8Array;\n}\n\nexport function createHash(algo: string): CryptoHasher {\n    switch (algo) {\n        case \"sha256\": return sha256.create();\n        case \"sha512\": return sha512.create();\n    }\n    assertArgument(false, \"invalid hashing algorithm name\", \"algorithm\", algo);\n}\n\nexport function createHmac(_algo: string, key: Uint8Array): CryptoHasher {\n    const algo = ({ sha256, sha512 }[_algo]);\n    assertArgument(algo != null, \"invalid hmac algorithm\", \"algorithm\", _algo);\n    return hmac.create(algo, key);\n}\n\nexport function pbkdf2Sync(password: Uint8Array, salt: Uint8Array, iterations: number, keylen: number, _algo: \"sha256\" | \"sha512\"): Uint8Array {\n    const algo = ({ sha256, sha512 }[_algo]);\n    assertArgument(algo != null, \"invalid pbkdf2 algorithm\", \"algorithm\", _algo);\n    return pbkdf2(algo, password, salt, { c: iterations, dkLen: keylen });\n}\n\nexport function randomBytes(length: number): Uint8Array {\n    assert(crypto != null, \"platform does not support secure random numbers\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"randomBytes\" });\n\n    assertArgument(Number.isInteger(length) && length > 0 && length <= 1024, \"invalid length\", \"length\", length);\n\n    const result = new Uint8Array(length);\n    crypto.getRandomValues(result);\n    return result;\n}\n"], "mappings": "AAAA;AAEA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,MAAM,QAAQ,sBAAsB;AAE7C,SAASC,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AAW1D,SAASC,SAASA,CAAA;EAChB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAAE,OAAOA,IAAI;;EAC9C,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;;EAClD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;;EAClD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;AACnD;AAAC;AAED,MAAMC,SAAS,GAAGL,SAAS,EAAE;AAC7B,MAAMM,MAAM,GAAQD,SAAS,CAACC,MAAM,IAAID,SAAS,CAACE,QAAQ;AAQ1D,OAAM,SAAUC,UAAUA,CAACC,IAAY;EACnC,QAAQA,IAAI;IACR,KAAK,QAAQ;MAAE,OAAOb,MAAM,CAACc,MAAM,EAAE;IACrC,KAAK,QAAQ;MAAE,OAAOb,MAAM,CAACa,MAAM,EAAE;;EAEzCX,cAAc,CAAC,KAAK,EAAE,gCAAgC,EAAE,WAAW,EAAEU,IAAI,CAAC;AAC9E;AAEA,OAAM,SAAUE,UAAUA,CAACC,KAAa,EAAEC,GAAe;EACrD,MAAMJ,IAAI,GAAI;IAAEb,MAAM;IAAEC;EAAM,CAAE,CAACe,KAAK,CAAE;EACxCb,cAAc,CAACU,IAAI,IAAI,IAAI,EAAE,wBAAwB,EAAE,WAAW,EAAEG,KAAK,CAAC;EAC1E,OAAOlB,IAAI,CAACgB,MAAM,CAACD,IAAI,EAAEI,GAAG,CAAC;AACjC;AAEA,OAAM,SAAUC,UAAUA,CAACC,QAAoB,EAAEC,IAAgB,EAAEC,UAAkB,EAAEC,MAAc,EAAEN,KAA0B;EAC7H,MAAMH,IAAI,GAAI;IAAEb,MAAM;IAAEC;EAAM,CAAE,CAACe,KAAK,CAAE;EACxCb,cAAc,CAACU,IAAI,IAAI,IAAI,EAAE,0BAA0B,EAAE,WAAW,EAAEG,KAAK,CAAC;EAC5E,OAAOjB,MAAM,CAACc,IAAI,EAAEM,QAAQ,EAAEC,IAAI,EAAE;IAAEG,CAAC,EAAEF,UAAU;IAAEG,KAAK,EAAEF;EAAM,CAAE,CAAC;AACzE;AAEA,OAAM,SAAUG,WAAWA,CAACC,MAAc;EACtCxB,MAAM,CAACQ,MAAM,IAAI,IAAI,EAAE,iDAAiD,EAAE,uBAAuB,EAAE;IAC/FiB,SAAS,EAAE;GAAe,CAAC;EAE/BxB,cAAc,CAACyB,MAAM,CAACC,SAAS,CAACH,MAAM,CAAC,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,IAAI,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAEA,MAAM,CAAC;EAE5G,MAAMI,MAAM,GAAG,IAAIC,UAAU,CAACL,MAAM,CAAC;EACrChB,MAAM,CAACsB,eAAe,CAACF,MAAM,CAAC;EAC9B,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}