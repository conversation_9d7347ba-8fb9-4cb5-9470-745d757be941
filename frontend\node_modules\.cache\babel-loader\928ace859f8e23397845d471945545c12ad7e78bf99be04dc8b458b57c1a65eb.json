{"ast": null, "code": "import { Coder } from \"./abstract-coder.js\";\n/**\n *  Clones the functionality of an existing Coder, but without a localName\n *\n *  @_ignore\n */\nexport class AnonymousCoder extends Coder {\n  coder;\n  constructor(coder) {\n    super(coder.name, coder.type, \"_\", coder.dynamic);\n    this.coder = coder;\n  }\n  defaultValue() {\n    return this.coder.defaultValue();\n  }\n  encode(writer, value) {\n    return this.coder.encode(writer, value);\n  }\n  decode(reader) {\n    return this.coder.decode(reader);\n  }\n}", "map": {"version": 3, "names": ["Coder", "AnonymousCoder", "coder", "constructor", "name", "type", "dynamic", "defaultValue", "encode", "writer", "value", "decode", "reader"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\anonymous.ts"], "sourcesContent": ["import { Coder } from \"./abstract-coder.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n/**\n *  Clones the functionality of an existing Coder, but without a localName\n *\n *  @_ignore\n */\nexport class AnonymousCoder extends Coder {\n    private coder: Coder;\n\n    constructor(coder: Coder) {\n        super(coder.name, coder.type, \"_\", coder.dynamic);\n        this.coder = coder;\n    }\n\n    defaultValue(): any {\n        return this.coder.defaultValue();\n    }\n\n    encode(writer: Writer, value: any): number {\n        return this.coder.encode(writer, value);\n    }\n\n    decode(reader: Reader): any {\n        return this.coder.decode(reader);\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAI3C;;;;;AAKA,OAAM,MAAOC,cAAe,SAAQD,KAAK;EAC7BE,KAAK;EAEbC,YAAYD,KAAY;IACpB,KAAK,CAACA,KAAK,CAACE,IAAI,EAAEF,KAAK,CAACG,IAAI,EAAE,GAAG,EAAEH,KAAK,CAACI,OAAO,CAAC;IACjD,IAAI,CAACJ,KAAK,GAAGA,KAAK;EACtB;EAEAK,YAAYA,CAAA;IACR,OAAO,IAAI,CAACL,KAAK,CAACK,YAAY,EAAE;EACpC;EAEAC,MAAMA,CAACC,MAAc,EAAEC,KAAU;IAC7B,OAAO,IAAI,CAACR,KAAK,CAACM,MAAM,CAACC,MAAM,EAAEC,KAAK,CAAC;EAC3C;EAEAC,MAAMA,CAACC,MAAc;IACjB,OAAO,IAAI,CAACV,KAAK,CAACS,MAAM,CAACC,MAAM,CAAC;EACpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}