{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\contexts\\\\GameContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport api from '../utils/api';\nimport { useAuth } from './AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst GameContext = /*#__PURE__*/createContext();\nexport const useGame = () => {\n  _s();\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n};\n_s(useGame, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const GameProvider = ({\n  children\n}) => {\n  _s2();\n  const {\n    isAuthenticated\n  } = useAuth();\n  const [heroes, setHeroes] = useState([]);\n  const [userStats, setUserStats] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [battleHistory, setBattleHistory] = useState([]);\n  const [dailyQuests, setDailyQuests] = useState([]);\n\n  // Load game data when user is authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadGameData();\n    } else {\n      // Clear data when user logs out\n      setHeroes([]);\n      setUserStats(null);\n      setBattleHistory([]);\n      setDailyQuests([]);\n    }\n  }, [isAuthenticated]);\n  const loadGameData = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([loadHeroes(), loadUserStats(), loadBattleHistory(), loadDailyQuests()]);\n    } catch (error) {\n      console.error('Failed to load game data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadHeroes = async () => {\n    try {\n      const response = await axios.get('/api/heroes');\n      setHeroes(response.data.heroes);\n      return response.data.heroes;\n    } catch (error) {\n      console.error('Failed to load heroes:', error);\n      return [];\n    }\n  };\n  const loadUserStats = async () => {\n    try {\n      const response = await axios.get('/api/users/profile');\n      setUserStats(response.data.stats);\n      return response.data.stats;\n    } catch (error) {\n      console.error('Failed to load user stats:', error);\n      return null;\n    }\n  };\n  const loadBattleHistory = async () => {\n    try {\n      const response = await axios.get('/api/battles/history?limit=10');\n      setBattleHistory(response.data.battles);\n      return response.data.battles;\n    } catch (error) {\n      console.error('Failed to load battle history:', error);\n      return [];\n    }\n  };\n  const loadDailyQuests = async () => {\n    try {\n      const response = await axios.get('/api/quests/daily');\n      setDailyQuests(response.data.quests);\n      return response.data.quests;\n    } catch (error) {\n      console.error('Failed to load daily quests:', error);\n      return [];\n    }\n  };\n  const upgradeHero = async heroId => {\n    try {\n      const response = await axios.post(`/api/heroes/${heroId}/upgrade`);\n\n      // Reload heroes to get updated stats\n      await loadHeroes();\n      await loadUserStats();\n      return response.data;\n    } catch (error) {\n      console.error('Failed to upgrade hero:', error);\n      throw error;\n    }\n  };\n  const unlockSkill = async (heroId, skillId) => {\n    try {\n      const response = await axios.post(`/api/heroes/${heroId}/unlock-skill/${skillId}`);\n\n      // Reload heroes to get updated skills\n      await loadHeroes();\n      await loadUserStats();\n      return response.data;\n    } catch (error) {\n      console.error('Failed to unlock skill:', error);\n      throw error;\n    }\n  };\n  const startPvEBattle = async (heroIds, difficulty) => {\n    try {\n      const response = await axios.post('/api/battles/pve/start', {\n        heroIds,\n        difficulty\n      });\n\n      // Reload battle history and user stats\n      await loadBattleHistory();\n      await loadUserStats();\n      return response.data;\n    } catch (error) {\n      console.error('Failed to start PvE battle:', error);\n      throw error;\n    }\n  };\n  const claimQuestReward = async questId => {\n    try {\n      const response = await axios.post(`/api/quests/${questId}/claim`);\n\n      // Reload quests and user stats\n      await loadDailyQuests();\n      await loadUserStats();\n      return response.data;\n    } catch (error) {\n      console.error('Failed to claim quest reward:', error);\n      throw error;\n    }\n  };\n  const getHeroById = heroId => {\n    return heroes.find(hero => hero.id === heroId);\n  };\n  const getActiveHeroes = () => {\n    return heroes.filter(hero => hero.isActive !== false);\n  };\n  const getTotalHeroPower = heroIds => {\n    return heroIds.reduce((total, heroId) => {\n      const hero = getHeroById(heroId);\n      if (hero) {\n        const power = hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk;\n        return total + power;\n      }\n      return total;\n    }, 0);\n  };\n  const value = {\n    heroes,\n    userStats,\n    battleHistory,\n    dailyQuests,\n    loading,\n    loadGameData,\n    loadHeroes,\n    loadUserStats,\n    loadBattleHistory,\n    loadDailyQuests,\n    upgradeHero,\n    unlockSkill,\n    startPvEBattle,\n    claimQuestReward,\n    getHeroById,\n    getActiveHeroes,\n    getTotalHeroPower\n  };\n  return /*#__PURE__*/_jsxDEV(GameContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s2(GameProvider, \"9ULNuoYqD2zip1xLZT0pdZBvZ/s=\", false, function () {\n  return [useAuth];\n});\n_c = GameProvider;\nvar _c;\n$RefreshReg$(_c, \"GameProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "api", "useAuth", "jsxDEV", "_jsxDEV", "GameContext", "useGame", "_s", "context", "Error", "GameProvider", "children", "_s2", "isAuthenticated", "heroes", "setHeroes", "userStats", "setUserStats", "loading", "setLoading", "battleHistory", "setBattleHistory", "dailyQuests", "setDailyQuests", "loadGameData", "Promise", "all", "loadHeroes", "loadUserStats", "loadBattleHistory", "loadDailyQuests", "error", "console", "response", "axios", "get", "data", "stats", "battles", "quests", "upgradeHero", "heroId", "post", "unlockSkill", "skillId", "startPvEBattle", "heroIds", "difficulty", "claimQuestReward", "questId", "getHeroById", "find", "hero", "id", "getActiveHeroes", "filter", "isActive", "getTotalHeroPower", "reduce", "total", "power", "hp", "atk", "def", "spd", "luk", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/contexts/GameContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport api from '../utils/api';\nimport { useAuth } from './AuthContext';\n\nconst GameContext = createContext();\n\nexport const useGame = () => {\n  const context = useContext(GameContext);\n  if (!context) {\n    throw new Error('useGame must be used within a GameProvider');\n  }\n  return context;\n};\n\nexport const GameProvider = ({ children }) => {\n  const { isAuthenticated } = useAuth();\n  const [heroes, setHeroes] = useState([]);\n  const [userStats, setUserStats] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [battleHistory, setBattleHistory] = useState([]);\n  const [dailyQuests, setDailyQuests] = useState([]);\n\n  // Load game data when user is authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadGameData();\n    } else {\n      // Clear data when user logs out\n      setHeroes([]);\n      setUserStats(null);\n      setBattleHistory([]);\n      setDailyQuests([]);\n    }\n  }, [isAuthenticated]);\n\n  const loadGameData = async () => {\n    setLoading(true);\n    try {\n      await Promise.all([\n        loadHeroes(),\n        loadUserStats(),\n        loadBattleHistory(),\n        loadDailyQuests(),\n      ]);\n    } catch (error) {\n      console.error('Failed to load game data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadHeroes = async () => {\n    try {\n      const response = await axios.get('/api/heroes');\n      setHeroes(response.data.heroes);\n      return response.data.heroes;\n    } catch (error) {\n      console.error('Failed to load heroes:', error);\n      return [];\n    }\n  };\n\n  const loadUserStats = async () => {\n    try {\n      const response = await axios.get('/api/users/profile');\n      setUserStats(response.data.stats);\n      return response.data.stats;\n    } catch (error) {\n      console.error('Failed to load user stats:', error);\n      return null;\n    }\n  };\n\n  const loadBattleHistory = async () => {\n    try {\n      const response = await axios.get('/api/battles/history?limit=10');\n      setBattleHistory(response.data.battles);\n      return response.data.battles;\n    } catch (error) {\n      console.error('Failed to load battle history:', error);\n      return [];\n    }\n  };\n\n  const loadDailyQuests = async () => {\n    try {\n      const response = await axios.get('/api/quests/daily');\n      setDailyQuests(response.data.quests);\n      return response.data.quests;\n    } catch (error) {\n      console.error('Failed to load daily quests:', error);\n      return [];\n    }\n  };\n\n  const upgradeHero = async (heroId) => {\n    try {\n      const response = await axios.post(`/api/heroes/${heroId}/upgrade`);\n\n      // Reload heroes to get updated stats\n      await loadHeroes();\n      await loadUserStats();\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to upgrade hero:', error);\n      throw error;\n    }\n  };\n\n  const unlockSkill = async (heroId, skillId) => {\n    try {\n      const response = await axios.post(`/api/heroes/${heroId}/unlock-skill/${skillId}`);\n\n      // Reload heroes to get updated skills\n      await loadHeroes();\n      await loadUserStats();\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to unlock skill:', error);\n      throw error;\n    }\n  };\n\n  const startPvEBattle = async (heroIds, difficulty) => {\n    try {\n      const response = await axios.post('/api/battles/pve/start', {\n        heroIds,\n        difficulty,\n      });\n\n      // Reload battle history and user stats\n      await loadBattleHistory();\n      await loadUserStats();\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to start PvE battle:', error);\n      throw error;\n    }\n  };\n\n  const claimQuestReward = async (questId) => {\n    try {\n      const response = await axios.post(`/api/quests/${questId}/claim`);\n\n      // Reload quests and user stats\n      await loadDailyQuests();\n      await loadUserStats();\n\n      return response.data;\n    } catch (error) {\n      console.error('Failed to claim quest reward:', error);\n      throw error;\n    }\n  };\n\n  const getHeroById = (heroId) => {\n    return heroes.find(hero => hero.id === heroId);\n  };\n\n  const getActiveHeroes = () => {\n    return heroes.filter(hero => hero.isActive !== false);\n  };\n\n  const getTotalHeroPower = (heroIds) => {\n    return heroIds.reduce((total, heroId) => {\n      const hero = getHeroById(heroId);\n      if (hero) {\n        const power = hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk;\n        return total + power;\n      }\n      return total;\n    }, 0);\n  };\n\n  const value = {\n    heroes,\n    userStats,\n    battleHistory,\n    dailyQuests,\n    loading,\n    loadGameData,\n    loadHeroes,\n    loadUserStats,\n    loadBattleHistory,\n    loadDailyQuests,\n    upgradeHero,\n    unlockSkill,\n    startPvEBattle,\n    claimQuestReward,\n    getHeroById,\n    getActiveHeroes,\n    getTotalHeroPower,\n  };\n\n  return (\n    <GameContext.Provider value={value}>\n      {children}\n    </GameContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,GAAG,MAAM,cAAc;AAC9B,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMS,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACO,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM;IAAEC;EAAgB,CAAC,GAAGX,OAAO,CAAC,CAAC;EACrC,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIa,eAAe,EAAE;MACnBW,YAAY,CAAC,CAAC;IAChB,CAAC,MAAM;MACL;MACAT,SAAS,CAAC,EAAE,CAAC;MACbE,YAAY,CAAC,IAAI,CAAC;MAClBI,gBAAgB,CAAC,EAAE,CAAC;MACpBE,cAAc,CAAC,EAAE,CAAC;IACpB;EACF,CAAC,EAAE,CAACV,eAAe,CAAC,CAAC;EAErB,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BL,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMM,OAAO,CAACC,GAAG,CAAC,CAChBC,UAAU,CAAC,CAAC,EACZC,aAAa,CAAC,CAAC,EACfC,iBAAiB,CAAC,CAAC,EACnBC,eAAe,CAAC,CAAC,CAClB,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,aAAa,CAAC;MAC/CpB,SAAS,CAACkB,QAAQ,CAACG,IAAI,CAACtB,MAAM,CAAC;MAC/B,OAAOmB,QAAQ,CAACG,IAAI,CAACtB,MAAM;IAC7B,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMH,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,oBAAoB,CAAC;MACtDlB,YAAY,CAACgB,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;MACjC,OAAOJ,QAAQ,CAACG,IAAI,CAACC,KAAK;IAC5B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAMF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,+BAA+B,CAAC;MACjEd,gBAAgB,CAACY,QAAQ,CAACG,IAAI,CAACE,OAAO,CAAC;MACvC,OAAOL,QAAQ,CAACG,IAAI,CAACE,OAAO;IAC9B,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,mBAAmB,CAAC;MACrDZ,cAAc,CAACU,QAAQ,CAACG,IAAI,CAACG,MAAM,CAAC;MACpC,OAAON,QAAQ,CAACG,IAAI,CAACG,MAAM;IAC7B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMS,WAAW,GAAG,MAAOC,MAAM,IAAK;IACpC,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAACQ,IAAI,CAAC,eAAeD,MAAM,UAAU,CAAC;;MAElE;MACA,MAAMd,UAAU,CAAC,CAAC;MAClB,MAAMC,aAAa,CAAC,CAAC;MAErB,OAAOK,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMY,WAAW,GAAG,MAAAA,CAAOF,MAAM,EAAEG,OAAO,KAAK;IAC7C,IAAI;MACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAACQ,IAAI,CAAC,eAAeD,MAAM,iBAAiBG,OAAO,EAAE,CAAC;;MAElF;MACA,MAAMjB,UAAU,CAAC,CAAC;MAClB,MAAMC,aAAa,CAAC,CAAC;MAErB,OAAOK,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMc,cAAc,GAAG,MAAAA,CAAOC,OAAO,EAAEC,UAAU,KAAK;IACpD,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAACQ,IAAI,CAAC,wBAAwB,EAAE;QAC1DI,OAAO;QACPC;MACF,CAAC,CAAC;;MAEF;MACA,MAAMlB,iBAAiB,CAAC,CAAC;MACzB,MAAMD,aAAa,CAAC,CAAC;MAErB,OAAOK,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMiB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMC,KAAK,CAACQ,IAAI,CAAC,eAAeO,OAAO,QAAQ,CAAC;;MAEjE;MACA,MAAMnB,eAAe,CAAC,CAAC;MACvB,MAAMF,aAAa,CAAC,CAAC;MAErB,OAAOK,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMmB,WAAW,GAAIT,MAAM,IAAK;IAC9B,OAAO3B,MAAM,CAACqC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKZ,MAAM,CAAC;EAChD,CAAC;EAED,MAAMa,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOxC,MAAM,CAACyC,MAAM,CAACH,IAAI,IAAIA,IAAI,CAACI,QAAQ,KAAK,KAAK,CAAC;EACvD,CAAC;EAED,MAAMC,iBAAiB,GAAIX,OAAO,IAAK;IACrC,OAAOA,OAAO,CAACY,MAAM,CAAC,CAACC,KAAK,EAAElB,MAAM,KAAK;MACvC,MAAMW,IAAI,GAAGF,WAAW,CAACT,MAAM,CAAC;MAChC,IAAIW,IAAI,EAAE;QACR,MAAMQ,KAAK,GAAGR,IAAI,CAACf,KAAK,CAACwB,EAAE,GAAGT,IAAI,CAACf,KAAK,CAACyB,GAAG,GAAGV,IAAI,CAACf,KAAK,CAAC0B,GAAG,GAAGX,IAAI,CAACf,KAAK,CAAC2B,GAAG,GAAGZ,IAAI,CAACf,KAAK,CAAC4B,GAAG;QAC/F,OAAON,KAAK,GAAGC,KAAK;MACtB;MACA,OAAOD,KAAK;IACd,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,MAAMO,KAAK,GAAG;IACZpD,MAAM;IACNE,SAAS;IACTI,aAAa;IACbE,WAAW;IACXJ,OAAO;IACPM,YAAY;IACZG,UAAU;IACVC,aAAa;IACbC,iBAAiB;IACjBC,eAAe;IACfU,WAAW;IACXG,WAAW;IACXE,cAAc;IACdG,gBAAgB;IAChBE,WAAW;IACXI,eAAe;IACfG;EACF,CAAC;EAED,oBACErD,OAAA,CAACC,WAAW,CAAC8D,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvD,QAAA,EAChCA;EAAQ;IAAAyD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3D,GAAA,CA5LWF,YAAY;EAAA,QACKR,OAAO;AAAA;AAAAsE,EAAA,GADxB9D,YAAY;AAAA,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}