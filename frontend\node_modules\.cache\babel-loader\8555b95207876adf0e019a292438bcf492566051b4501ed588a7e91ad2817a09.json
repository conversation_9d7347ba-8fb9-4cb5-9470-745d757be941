{"ast": null, "code": "import { Interface, Typed } from \"../abi/index.js\";\nimport { isAddressable, resolveAddress } from \"../address/index.js\";\n// import from provider.ts instead of index.ts to prevent circular dep\n// from EtherscanProvider\nimport { copyRequest, Log } from \"../providers/provider.js\";\nimport { defineProperties, getBigInt, isCallException, isHexString, resolveProperties, isError, makeError, assert, assertArgument } from \"../utils/index.js\";\nimport { ContractEventPayload, ContractUnknownEventPayload, ContractTransactionResponse, EventLog, UndecodedEventLog } from \"./wrappers.js\";\nconst BN_0 = BigInt(0);\nfunction canCall(value) {\n  return value && typeof value.call === \"function\";\n}\nfunction canEstimate(value) {\n  return value && typeof value.estimateGas === \"function\";\n}\nfunction canResolve(value) {\n  return value && typeof value.resolveName === \"function\";\n}\nfunction canSend(value) {\n  return value && typeof value.sendTransaction === \"function\";\n}\nfunction getResolver(value) {\n  if (value != null) {\n    if (canResolve(value)) {\n      return value;\n    }\n    if (value.provider) {\n      return value.provider;\n    }\n  }\n  return undefined;\n}\nclass PreparedTopicFilter {\n  #filter;\n  fragment;\n  constructor(contract, fragment, args) {\n    defineProperties(this, {\n      fragment\n    });\n    if (fragment.inputs.length < args.length) {\n      throw new Error(\"too many arguments\");\n    }\n    // Recursively descend into args and resolve any addresses\n    const runner = getRunner(contract.runner, \"resolveName\");\n    const resolver = canResolve(runner) ? runner : null;\n    this.#filter = async function () {\n      const resolvedArgs = await Promise.all(fragment.inputs.map((param, index) => {\n        const arg = args[index];\n        if (arg == null) {\n          return null;\n        }\n        return param.walkAsync(args[index], (type, value) => {\n          if (type === \"address\") {\n            if (Array.isArray(value)) {\n              return Promise.all(value.map(v => resolveAddress(v, resolver)));\n            }\n            return resolveAddress(value, resolver);\n          }\n          return value;\n        });\n      }));\n      return contract.interface.encodeFilterTopics(fragment, resolvedArgs);\n    }();\n  }\n  getTopicFilter() {\n    return this.#filter;\n  }\n}\n// A = Arguments passed in as a tuple\n// R = The result type of the call (i.e. if only one return type,\n//     the qualified type, otherwise Result)\n// D = The type the default call will return (i.e. R for view/pure,\n//     TransactionResponse otherwise)\n//export interface ContractMethod<A extends Array<any> = Array<any>, R = any, D extends R | ContractTransactionResponse = ContractTransactionResponse> {\nfunction getRunner(value, feature) {\n  if (value == null) {\n    return null;\n  }\n  if (typeof value[feature] === \"function\") {\n    return value;\n  }\n  if (value.provider && typeof value.provider[feature] === \"function\") {\n    return value.provider;\n  }\n  return null;\n}\nfunction getProvider(value) {\n  if (value == null) {\n    return null;\n  }\n  return value.provider || null;\n}\n/**\n *  @_ignore:\n */\nexport async function copyOverrides(arg, allowed) {\n  // Make sure the overrides passed in are a valid overrides object\n  const _overrides = Typed.dereference(arg, \"overrides\");\n  assertArgument(typeof _overrides === \"object\", \"invalid overrides parameter\", \"overrides\", arg);\n  // Create a shallow copy (we'll deep-ify anything needed during normalizing)\n  const overrides = copyRequest(_overrides);\n  assertArgument(overrides.to == null || (allowed || []).indexOf(\"to\") >= 0, \"cannot override to\", \"overrides.to\", overrides.to);\n  assertArgument(overrides.data == null || (allowed || []).indexOf(\"data\") >= 0, \"cannot override data\", \"overrides.data\", overrides.data);\n  // Resolve any from\n  if (overrides.from) {\n    overrides.from = overrides.from;\n  }\n  return overrides;\n}\n/**\n *  @_ignore:\n */\nexport async function resolveArgs(_runner, inputs, args) {\n  // Recursively descend into args and resolve any addresses\n  const runner = getRunner(_runner, \"resolveName\");\n  const resolver = canResolve(runner) ? runner : null;\n  return await Promise.all(inputs.map((param, index) => {\n    return param.walkAsync(args[index], (type, value) => {\n      value = Typed.dereference(value, type);\n      if (type === \"address\") {\n        return resolveAddress(value, resolver);\n      }\n      return value;\n    });\n  }));\n}\nfunction buildWrappedFallback(contract) {\n  const populateTransaction = async function (overrides) {\n    // If an overrides was passed in, copy it and normalize the values\n    const tx = await copyOverrides(overrides, [\"data\"]);\n    tx.to = await contract.getAddress();\n    if (tx.from) {\n      tx.from = await resolveAddress(tx.from, getResolver(contract.runner));\n    }\n    const iface = contract.interface;\n    const noValue = getBigInt(tx.value || BN_0, \"overrides.value\") === BN_0;\n    const noData = (tx.data || \"0x\") === \"0x\";\n    if (iface.fallback && !iface.fallback.payable && iface.receive && !noData && !noValue) {\n      assertArgument(false, \"cannot send data to receive or send value to non-payable fallback\", \"overrides\", overrides);\n    }\n    assertArgument(iface.fallback || noData, \"cannot send data to receive-only contract\", \"overrides.data\", tx.data);\n    // Only allow payable contracts to set non-zero value\n    const payable = iface.receive || iface.fallback && iface.fallback.payable;\n    assertArgument(payable || noValue, \"cannot send value to non-payable fallback\", \"overrides.value\", tx.value);\n    // Only allow fallback contracts to set non-empty data\n    assertArgument(iface.fallback || noData, \"cannot send data to receive-only contract\", \"overrides.data\", tx.data);\n    return tx;\n  };\n  const staticCall = async function (overrides) {\n    const runner = getRunner(contract.runner, \"call\");\n    assert(canCall(runner), \"contract runner does not support calling\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"call\"\n    });\n    const tx = await populateTransaction(overrides);\n    try {\n      return await runner.call(tx);\n    } catch (error) {\n      if (isCallException(error) && error.data) {\n        throw contract.interface.makeError(error.data, tx);\n      }\n      throw error;\n    }\n  };\n  const send = async function (overrides) {\n    const runner = contract.runner;\n    assert(canSend(runner), \"contract runner does not support sending transactions\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"sendTransaction\"\n    });\n    const tx = await runner.sendTransaction(await populateTransaction(overrides));\n    const provider = getProvider(contract.runner);\n    // @TODO: the provider can be null; make a custom dummy provider that will throw a\n    // meaningful error\n    return new ContractTransactionResponse(contract.interface, provider, tx);\n  };\n  const estimateGas = async function (overrides) {\n    const runner = getRunner(contract.runner, \"estimateGas\");\n    assert(canEstimate(runner), \"contract runner does not support gas estimation\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"estimateGas\"\n    });\n    return await runner.estimateGas(await populateTransaction(overrides));\n  };\n  const method = async overrides => {\n    return await send(overrides);\n  };\n  defineProperties(method, {\n    _contract: contract,\n    estimateGas,\n    populateTransaction,\n    send,\n    staticCall\n  });\n  return method;\n}\nfunction buildWrappedMethod(contract, key) {\n  const getFragment = function (...args) {\n    const fragment = contract.interface.getFunction(key, args);\n    assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"fragment\",\n      info: {\n        key,\n        args\n      }\n    });\n    return fragment;\n  };\n  const populateTransaction = async function (...args) {\n    const fragment = getFragment(...args);\n    // If an overrides was passed in, copy it and normalize the values\n    let overrides = {};\n    if (fragment.inputs.length + 1 === args.length) {\n      overrides = await copyOverrides(args.pop());\n      if (overrides.from) {\n        overrides.from = await resolveAddress(overrides.from, getResolver(contract.runner));\n      }\n    }\n    if (fragment.inputs.length !== args.length) {\n      throw new Error(\"internal error: fragment inputs doesn't match arguments; should not happen\");\n    }\n    const resolvedArgs = await resolveArgs(contract.runner, fragment.inputs, args);\n    return Object.assign({}, overrides, await resolveProperties({\n      to: contract.getAddress(),\n      data: contract.interface.encodeFunctionData(fragment, resolvedArgs)\n    }));\n  };\n  const staticCall = async function (...args) {\n    const result = await staticCallResult(...args);\n    if (result.length === 1) {\n      return result[0];\n    }\n    return result;\n  };\n  const send = async function (...args) {\n    const runner = contract.runner;\n    assert(canSend(runner), \"contract runner does not support sending transactions\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"sendTransaction\"\n    });\n    const tx = await runner.sendTransaction(await populateTransaction(...args));\n    const provider = getProvider(contract.runner);\n    // @TODO: the provider can be null; make a custom dummy provider that will throw a\n    // meaningful error\n    return new ContractTransactionResponse(contract.interface, provider, tx);\n  };\n  const estimateGas = async function (...args) {\n    const runner = getRunner(contract.runner, \"estimateGas\");\n    assert(canEstimate(runner), \"contract runner does not support gas estimation\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"estimateGas\"\n    });\n    return await runner.estimateGas(await populateTransaction(...args));\n  };\n  const staticCallResult = async function (...args) {\n    const runner = getRunner(contract.runner, \"call\");\n    assert(canCall(runner), \"contract runner does not support calling\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"call\"\n    });\n    const tx = await populateTransaction(...args);\n    let result = \"0x\";\n    try {\n      result = await runner.call(tx);\n    } catch (error) {\n      if (isCallException(error) && error.data) {\n        throw contract.interface.makeError(error.data, tx);\n      }\n      throw error;\n    }\n    const fragment = getFragment(...args);\n    return contract.interface.decodeFunctionResult(fragment, result);\n  };\n  const method = async (...args) => {\n    const fragment = getFragment(...args);\n    if (fragment.constant) {\n      return await staticCall(...args);\n    }\n    return await send(...args);\n  };\n  defineProperties(method, {\n    name: contract.interface.getFunctionName(key),\n    _contract: contract,\n    _key: key,\n    getFragment,\n    estimateGas,\n    populateTransaction,\n    send,\n    staticCall,\n    staticCallResult\n  });\n  // Only works on non-ambiguous keys (refined fragment is always non-ambiguous)\n  Object.defineProperty(method, \"fragment\", {\n    configurable: false,\n    enumerable: true,\n    get: () => {\n      const fragment = contract.interface.getFunction(key);\n      assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"fragment\",\n        info: {\n          key\n        }\n      });\n      return fragment;\n    }\n  });\n  return method;\n}\nfunction buildWrappedEvent(contract, key) {\n  const getFragment = function (...args) {\n    const fragment = contract.interface.getEvent(key, args);\n    assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"fragment\",\n      info: {\n        key,\n        args\n      }\n    });\n    return fragment;\n  };\n  const method = function (...args) {\n    return new PreparedTopicFilter(contract, getFragment(...args), args);\n  };\n  defineProperties(method, {\n    name: contract.interface.getEventName(key),\n    _contract: contract,\n    _key: key,\n    getFragment\n  });\n  // Only works on non-ambiguous keys (refined fragment is always non-ambiguous)\n  Object.defineProperty(method, \"fragment\", {\n    configurable: false,\n    enumerable: true,\n    get: () => {\n      const fragment = contract.interface.getEvent(key);\n      assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"fragment\",\n        info: {\n          key\n        }\n      });\n      return fragment;\n    }\n  });\n  return method;\n}\n// The combination of TypeScrype, Private Fields and Proxies makes\n// the world go boom; so we hide variables with some trickery keeping\n// a symbol attached to each BaseContract which its sub-class (even\n// via a Proxy) can reach and use to look up its internal values.\nconst internal = Symbol.for(\"_ethersInternal_contract\");\nconst internalValues = new WeakMap();\nfunction setInternal(contract, values) {\n  internalValues.set(contract[internal], values);\n}\nfunction getInternal(contract) {\n  return internalValues.get(contract[internal]);\n}\nfunction isDeferred(value) {\n  return value && typeof value === \"object\" && \"getTopicFilter\" in value && typeof value.getTopicFilter === \"function\" && value.fragment;\n}\nasync function getSubInfo(contract, event) {\n  let topics;\n  let fragment = null;\n  // Convert named events to topicHash and get the fragment for\n  // events which need deconstructing.\n  if (Array.isArray(event)) {\n    const topicHashify = function (name) {\n      if (isHexString(name, 32)) {\n        return name;\n      }\n      const fragment = contract.interface.getEvent(name);\n      assertArgument(fragment, \"unknown fragment\", \"name\", name);\n      return fragment.topicHash;\n    };\n    // Array of Topics and Names; e.g. `[ \"0x1234...89ab\", \"Transfer(address)\" ]`\n    topics = event.map(e => {\n      if (e == null) {\n        return null;\n      }\n      if (Array.isArray(e)) {\n        return e.map(topicHashify);\n      }\n      return topicHashify(e);\n    });\n  } else if (event === \"*\") {\n    topics = [null];\n  } else if (typeof event === \"string\") {\n    if (isHexString(event, 32)) {\n      // Topic Hash\n      topics = [event];\n    } else {\n      // Name or Signature; e.g. `\"Transfer\", `\"Transfer(address)\"`\n      fragment = contract.interface.getEvent(event);\n      assertArgument(fragment, \"unknown fragment\", \"event\", event);\n      topics = [fragment.topicHash];\n    }\n  } else if (isDeferred(event)) {\n    // Deferred Topic Filter; e.g. `contract.filter.Transfer(from)`\n    topics = await event.getTopicFilter();\n  } else if (\"fragment\" in event) {\n    // ContractEvent; e.g. `contract.filter.Transfer`\n    fragment = event.fragment;\n    topics = [fragment.topicHash];\n  } else {\n    assertArgument(false, \"unknown event name\", \"event\", event);\n  }\n  // Normalize topics and sort TopicSets\n  topics = topics.map(t => {\n    if (t == null) {\n      return null;\n    }\n    if (Array.isArray(t)) {\n      const items = Array.from(new Set(t.map(t => t.toLowerCase())).values());\n      if (items.length === 1) {\n        return items[0];\n      }\n      items.sort();\n      return items;\n    }\n    return t.toLowerCase();\n  });\n  const tag = topics.map(t => {\n    if (t == null) {\n      return \"null\";\n    }\n    if (Array.isArray(t)) {\n      return t.join(\"|\");\n    }\n    return t;\n  }).join(\"&\");\n  return {\n    fragment,\n    tag,\n    topics\n  };\n}\nasync function hasSub(contract, event) {\n  const {\n    subs\n  } = getInternal(contract);\n  return subs.get((await getSubInfo(contract, event)).tag) || null;\n}\nasync function getSub(contract, operation, event) {\n  // Make sure our runner can actually subscribe to events\n  const provider = getProvider(contract.runner);\n  assert(provider, \"contract runner does not support subscribing\", \"UNSUPPORTED_OPERATION\", {\n    operation\n  });\n  const {\n    fragment,\n    tag,\n    topics\n  } = await getSubInfo(contract, event);\n  const {\n    addr,\n    subs\n  } = getInternal(contract);\n  let sub = subs.get(tag);\n  if (!sub) {\n    const address = addr ? addr : contract;\n    const filter = {\n      address,\n      topics\n    };\n    const listener = log => {\n      let foundFragment = fragment;\n      if (foundFragment == null) {\n        try {\n          foundFragment = contract.interface.getEvent(log.topics[0]);\n        } catch (error) {}\n      }\n      // If fragment is null, we do not deconstruct the args to emit\n      if (foundFragment) {\n        const _foundFragment = foundFragment;\n        const args = fragment ? contract.interface.decodeEventLog(fragment, log.data, log.topics) : [];\n        emit(contract, event, args, listener => {\n          return new ContractEventPayload(contract, listener, event, _foundFragment, log);\n        });\n      } else {\n        emit(contract, event, [], listener => {\n          return new ContractUnknownEventPayload(contract, listener, event, log);\n        });\n      }\n    };\n    let starting = [];\n    const start = () => {\n      if (starting.length) {\n        return;\n      }\n      starting.push(provider.on(filter, listener));\n    };\n    const stop = async () => {\n      if (starting.length == 0) {\n        return;\n      }\n      let started = starting;\n      starting = [];\n      await Promise.all(started);\n      provider.off(filter, listener);\n    };\n    sub = {\n      tag,\n      listeners: [],\n      start,\n      stop\n    };\n    subs.set(tag, sub);\n  }\n  return sub;\n}\n// We use this to ensure one emit resolves before firing the next to\n// ensure correct ordering (note this cannot throw and just adds the\n// notice to the event queu using setTimeout).\nlet lastEmit = Promise.resolve();\nasync function _emit(contract, event, args, payloadFunc) {\n  await lastEmit;\n  const sub = await hasSub(contract, event);\n  if (!sub) {\n    return false;\n  }\n  const count = sub.listeners.length;\n  sub.listeners = sub.listeners.filter(({\n    listener,\n    once\n  }) => {\n    const passArgs = Array.from(args);\n    if (payloadFunc) {\n      passArgs.push(payloadFunc(once ? null : listener));\n    }\n    try {\n      listener.call(contract, ...passArgs);\n    } catch (error) {}\n    return !once;\n  });\n  if (sub.listeners.length === 0) {\n    sub.stop();\n    getInternal(contract).subs.delete(sub.tag);\n  }\n  return count > 0;\n}\nasync function emit(contract, event, args, payloadFunc) {\n  try {\n    await lastEmit;\n  } catch (error) {}\n  const resultPromise = _emit(contract, event, args, payloadFunc);\n  lastEmit = resultPromise;\n  return await resultPromise;\n}\nconst passProperties = [\"then\"];\nexport class BaseContract {\n  /**\n   *  The target to connect to.\n   *\n   *  This can be an address, ENS name or any [[Addressable]], such as\n   *  another contract. To get the resovled address, use the ``getAddress``\n   *  method.\n   */\n  target;\n  /**\n   *  The contract Interface.\n   */\n  interface;\n  /**\n   *  The connected runner. This is generally a [[Provider]] or a\n   *  [[Signer]], which dictates what operations are supported.\n   *\n   *  For example, a **Contract** connected to a [[Provider]] may\n   *  only execute read-only operations.\n   */\n  runner;\n  /**\n   *  All the Events available on this contract.\n   */\n  filters;\n  /**\n   *  @_ignore:\n   */\n  [internal];\n  /**\n   *  The fallback or receive function if any.\n   */\n  fallback;\n  /**\n   *  Creates a new contract connected to %%target%% with the %%abi%% and\n   *  optionally connected to a %%runner%% to perform operations on behalf\n   *  of.\n   */\n  constructor(target, abi, runner, _deployTx) {\n    assertArgument(typeof target === \"string\" || isAddressable(target), \"invalid value for Contract target\", \"target\", target);\n    if (runner == null) {\n      runner = null;\n    }\n    const iface = Interface.from(abi);\n    defineProperties(this, {\n      target,\n      runner,\n      interface: iface\n    });\n    Object.defineProperty(this, internal, {\n      value: {}\n    });\n    let addrPromise;\n    let addr = null;\n    let deployTx = null;\n    if (_deployTx) {\n      const provider = getProvider(runner);\n      // @TODO: the provider can be null; make a custom dummy provider that will throw a\n      // meaningful error\n      deployTx = new ContractTransactionResponse(this.interface, provider, _deployTx);\n    }\n    let subs = new Map();\n    // Resolve the target as the address\n    if (typeof target === \"string\") {\n      if (isHexString(target)) {\n        addr = target;\n        addrPromise = Promise.resolve(target);\n      } else {\n        const resolver = getRunner(runner, \"resolveName\");\n        if (!canResolve(resolver)) {\n          throw makeError(\"contract runner does not support name resolution\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"resolveName\"\n          });\n        }\n        addrPromise = resolver.resolveName(target).then(addr => {\n          if (addr == null) {\n            throw makeError(\"an ENS name used for a contract target must be correctly configured\", \"UNCONFIGURED_NAME\", {\n              value: target\n            });\n          }\n          getInternal(this).addr = addr;\n          return addr;\n        });\n      }\n    } else {\n      addrPromise = target.getAddress().then(addr => {\n        if (addr == null) {\n          throw new Error(\"TODO\");\n        }\n        getInternal(this).addr = addr;\n        return addr;\n      });\n    }\n    // Set our private values\n    setInternal(this, {\n      addrPromise,\n      addr,\n      deployTx,\n      subs\n    });\n    // Add the event filters\n    const filters = new Proxy({}, {\n      get: (target, prop, receiver) => {\n        // Pass important checks (like `then` for Promise) through\n        if (typeof prop === \"symbol\" || passProperties.indexOf(prop) >= 0) {\n          return Reflect.get(target, prop, receiver);\n        }\n        try {\n          return this.getEvent(prop);\n        } catch (error) {\n          if (!isError(error, \"INVALID_ARGUMENT\") || error.argument !== \"key\") {\n            throw error;\n          }\n        }\n        return undefined;\n      },\n      has: (target, prop) => {\n        // Pass important checks (like `then` for Promise) through\n        if (passProperties.indexOf(prop) >= 0) {\n          return Reflect.has(target, prop);\n        }\n        return Reflect.has(target, prop) || this.interface.hasEvent(String(prop));\n      }\n    });\n    defineProperties(this, {\n      filters\n    });\n    defineProperties(this, {\n      fallback: iface.receive || iface.fallback ? buildWrappedFallback(this) : null\n    });\n    // Return a Proxy that will respond to functions\n    return new Proxy(this, {\n      get: (target, prop, receiver) => {\n        if (typeof prop === \"symbol\" || prop in target || passProperties.indexOf(prop) >= 0) {\n          return Reflect.get(target, prop, receiver);\n        }\n        // Undefined properties should return undefined\n        try {\n          return target.getFunction(prop);\n        } catch (error) {\n          if (!isError(error, \"INVALID_ARGUMENT\") || error.argument !== \"key\") {\n            throw error;\n          }\n        }\n        return undefined;\n      },\n      has: (target, prop) => {\n        if (typeof prop === \"symbol\" || prop in target || passProperties.indexOf(prop) >= 0) {\n          return Reflect.has(target, prop);\n        }\n        return target.interface.hasFunction(prop);\n      }\n    });\n  }\n  /**\n   *  Return a new Contract instance with the same target and ABI, but\n   *  a different %%runner%%.\n   */\n  connect(runner) {\n    return new BaseContract(this.target, this.interface, runner);\n  }\n  /**\n   *  Return a new Contract instance with the same ABI and runner, but\n   *  a different %%target%%.\n   */\n  attach(target) {\n    return new BaseContract(target, this.interface, this.runner);\n  }\n  /**\n   *  Return the resolved address of this Contract.\n   */\n  async getAddress() {\n    return await getInternal(this).addrPromise;\n  }\n  /**\n   *  Return the deployed bytecode or null if no bytecode is found.\n   */\n  async getDeployedCode() {\n    const provider = getProvider(this.runner);\n    assert(provider, \"runner does not support .provider\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"getDeployedCode\"\n    });\n    const code = await provider.getCode(await this.getAddress());\n    if (code === \"0x\") {\n      return null;\n    }\n    return code;\n  }\n  /**\n   *  Resolve to this Contract once the bytecode has been deployed, or\n   *  resolve immediately if already deployed.\n   */\n  async waitForDeployment() {\n    // We have the deployement transaction; just use that (throws if deployement fails)\n    const deployTx = this.deploymentTransaction();\n    if (deployTx) {\n      await deployTx.wait();\n      return this;\n    }\n    // Check for code\n    const code = await this.getDeployedCode();\n    if (code != null) {\n      return this;\n    }\n    // Make sure we can subscribe to a provider event\n    const provider = getProvider(this.runner);\n    assert(provider != null, \"contract runner does not support .provider\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"waitForDeployment\"\n    });\n    return new Promise((resolve, reject) => {\n      const checkCode = async () => {\n        try {\n          const code = await this.getDeployedCode();\n          if (code != null) {\n            return resolve(this);\n          }\n          provider.once(\"block\", checkCode);\n        } catch (error) {\n          reject(error);\n        }\n      };\n      checkCode();\n    });\n  }\n  /**\n   *  Return the transaction used to deploy this contract.\n   *\n   *  This is only available if this instance was returned from a\n   *  [[ContractFactory]].\n   */\n  deploymentTransaction() {\n    return getInternal(this).deployTx;\n  }\n  /**\n   *  Return the function for a given name. This is useful when a contract\n   *  method name conflicts with a JavaScript name such as ``prototype`` or\n   *  when using a Contract programatically.\n   */\n  getFunction(key) {\n    if (typeof key !== \"string\") {\n      key = key.format();\n    }\n    const func = buildWrappedMethod(this, key);\n    return func;\n  }\n  /**\n   *  Return the event for a given name. This is useful when a contract\n   *  event name conflicts with a JavaScript name such as ``prototype`` or\n   *  when using a Contract programatically.\n   */\n  getEvent(key) {\n    if (typeof key !== \"string\") {\n      key = key.format();\n    }\n    return buildWrappedEvent(this, key);\n  }\n  /**\n   *  @_ignore:\n   */\n  async queryTransaction(hash) {\n    throw new Error(\"@TODO\");\n  }\n  /*\n  // @TODO: this is a non-backwards compatible change, but will be added\n  //        in v7 and in a potential SmartContract class in an upcoming\n  //        v6 release\n  async getTransactionReceipt(hash: string): Promise<null | ContractTransactionReceipt> {\n      const provider = getProvider(this.runner);\n      assert(provider, \"contract runner does not have a provider\",\n          \"UNSUPPORTED_OPERATION\", { operation: \"queryTransaction\" });\n       const receipt = await provider.getTransactionReceipt(hash);\n      if (receipt == null) { return null; }\n       return new ContractTransactionReceipt(this.interface, provider, receipt);\n  }\n  */\n  /**\n   *  Provide historic access to event data for %%event%% in the range\n   *  %%fromBlock%% (default: ``0``) to %%toBlock%% (default: ``\"latest\"``)\n   *  inclusive.\n   */\n  async queryFilter(event, fromBlock, toBlock) {\n    if (fromBlock == null) {\n      fromBlock = 0;\n    }\n    if (toBlock == null) {\n      toBlock = \"latest\";\n    }\n    const {\n      addr,\n      addrPromise\n    } = getInternal(this);\n    const address = addr ? addr : await addrPromise;\n    const {\n      fragment,\n      topics\n    } = await getSubInfo(this, event);\n    const filter = {\n      address,\n      topics,\n      fromBlock,\n      toBlock\n    };\n    const provider = getProvider(this.runner);\n    assert(provider, \"contract runner does not have a provider\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"queryFilter\"\n    });\n    return (await provider.getLogs(filter)).map(log => {\n      let foundFragment = fragment;\n      if (foundFragment == null) {\n        try {\n          foundFragment = this.interface.getEvent(log.topics[0]);\n        } catch (error) {}\n      }\n      if (foundFragment) {\n        try {\n          return new EventLog(log, this.interface, foundFragment);\n        } catch (error) {\n          return new UndecodedEventLog(log, error);\n        }\n      }\n      return new Log(log, provider);\n    });\n  }\n  /**\n   *  Add an event %%listener%% for the %%event%%.\n   */\n  async on(event, listener) {\n    const sub = await getSub(this, \"on\", event);\n    sub.listeners.push({\n      listener,\n      once: false\n    });\n    sub.start();\n    return this;\n  }\n  /**\n   *  Add an event %%listener%% for the %%event%%, but remove the listener\n   *  after it is fired once.\n   */\n  async once(event, listener) {\n    const sub = await getSub(this, \"once\", event);\n    sub.listeners.push({\n      listener,\n      once: true\n    });\n    sub.start();\n    return this;\n  }\n  /**\n   *  Emit an %%event%% calling all listeners with %%args%%.\n   *\n   *  Resolves to ``true`` if any listeners were called.\n   */\n  async emit(event, ...args) {\n    return await emit(this, event, args, null);\n  }\n  /**\n   *  Resolves to the number of listeners of %%event%% or the total number\n   *  of listeners if unspecified.\n   */\n  async listenerCount(event) {\n    if (event) {\n      const sub = await hasSub(this, event);\n      if (!sub) {\n        return 0;\n      }\n      return sub.listeners.length;\n    }\n    const {\n      subs\n    } = getInternal(this);\n    let total = 0;\n    for (const {\n      listeners\n    } of subs.values()) {\n      total += listeners.length;\n    }\n    return total;\n  }\n  /**\n   *  Resolves to the listeners subscribed to %%event%% or all listeners\n   *  if unspecified.\n   */\n  async listeners(event) {\n    if (event) {\n      const sub = await hasSub(this, event);\n      if (!sub) {\n        return [];\n      }\n      return sub.listeners.map(({\n        listener\n      }) => listener);\n    }\n    const {\n      subs\n    } = getInternal(this);\n    let result = [];\n    for (const {\n      listeners\n    } of subs.values()) {\n      result = result.concat(listeners.map(({\n        listener\n      }) => listener));\n    }\n    return result;\n  }\n  /**\n   *  Remove the %%listener%% from the listeners for %%event%% or remove\n   *  all listeners if unspecified.\n   */\n  async off(event, listener) {\n    const sub = await hasSub(this, event);\n    if (!sub) {\n      return this;\n    }\n    if (listener) {\n      const index = sub.listeners.map(({\n        listener\n      }) => listener).indexOf(listener);\n      if (index >= 0) {\n        sub.listeners.splice(index, 1);\n      }\n    }\n    if (listener == null || sub.listeners.length === 0) {\n      sub.stop();\n      getInternal(this).subs.delete(sub.tag);\n    }\n    return this;\n  }\n  /**\n   *  Remove all the listeners for %%event%% or remove all listeners if\n   *  unspecified.\n   */\n  async removeAllListeners(event) {\n    if (event) {\n      const sub = await hasSub(this, event);\n      if (!sub) {\n        return this;\n      }\n      sub.stop();\n      getInternal(this).subs.delete(sub.tag);\n    } else {\n      const {\n        subs\n      } = getInternal(this);\n      for (const {\n        tag,\n        stop\n      } of subs.values()) {\n        stop();\n        subs.delete(tag);\n      }\n    }\n    return this;\n  }\n  /**\n   *  Alias for [on].\n   */\n  async addListener(event, listener) {\n    return await this.on(event, listener);\n  }\n  /**\n   *  Alias for [off].\n   */\n  async removeListener(event, listener) {\n    return await this.off(event, listener);\n  }\n  /**\n   *  Create a new Class for the %%abi%%.\n   */\n  static buildClass(abi) {\n    class CustomContract extends BaseContract {\n      constructor(address, runner = null) {\n        super(address, abi, runner);\n      }\n    }\n    return CustomContract;\n  }\n  /**\n   *  Create a new BaseContract with a specified Interface.\n   */\n  static from(target, abi, runner) {\n    if (runner == null) {\n      runner = null;\n    }\n    const contract = new this(target, abi, runner);\n    return contract;\n  }\n}\nfunction _ContractBase() {\n  return BaseContract;\n}\n/**\n *  A [[BaseContract]] with no type guards on its methods or events.\n */\nexport class Contract extends _ContractBase() {}", "map": {"version": 3, "names": ["Interface", "Typed", "isAddressable", "resolve<PERSON>ddress", "copyRequest", "Log", "defineProperties", "getBigInt", "isCallException", "isHexString", "resolveProperties", "isError", "makeError", "assert", "assertArgument", "ContractEventPayload", "ContractUnknownEventPayload", "ContractTransactionResponse", "EventLog", "UndecodedEventLog", "BN_0", "BigInt", "canCall", "value", "call", "canEstimate", "estimateGas", "canResolve", "resolveName", "canSend", "sendTransaction", "getResolver", "provider", "undefined", "PreparedTopicFilter", "filter", "fragment", "constructor", "contract", "args", "inputs", "length", "Error", "runner", "get<PERSON><PERSON><PERSON>", "resolver", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "all", "map", "param", "index", "arg", "walkAsync", "type", "Array", "isArray", "v", "interface", "encodeFilterTopics", "getTopicFilter", "feature", "get<PERSON><PERSON><PERSON>", "copyOverrides", "allowed", "_overrides", "dereference", "overrides", "to", "indexOf", "data", "from", "resolveArgs", "_runner", "buildWrappedFallback", "populateTransaction", "tx", "get<PERSON><PERSON><PERSON>", "iface", "noValue", "noData", "fallback", "payable", "receive", "staticCall", "operation", "error", "send", "method", "_contract", "buildWrappedMethod", "key", "getFragment", "getFunction", "info", "pop", "Object", "assign", "encodeFunctionData", "result", "staticCallResult", "decodeFunctionResult", "constant", "name", "getFunctionName", "_key", "defineProperty", "configurable", "enumerable", "get", "buildWrappedEvent", "getEvent", "getEventName", "internal", "Symbol", "for", "internalValues", "WeakMap", "setInternal", "values", "set", "getInternal", "is<PERSON><PERSON><PERSON><PERSON>", "getSubInfo", "event", "topics", "topicHashify", "topicHash", "e", "t", "items", "Set", "toLowerCase", "sort", "tag", "join", "hasSub", "subs", "getSub", "addr", "sub", "address", "listener", "log", "foundFragment", "_foundFragment", "decodeEventLog", "emit", "starting", "start", "push", "on", "stop", "started", "off", "listeners", "lastEmit", "resolve", "_emit", "payloadFunc", "count", "once", "passArgs", "delete", "resultPromise", "passProperties", "BaseContract", "target", "filters", "abi", "_deployTx", "addrPromise", "deployTx", "Map", "then", "Proxy", "prop", "receiver", "Reflect", "argument", "has", "hasEvent", "String", "hasFunction", "connect", "attach", "getDeployedCode", "code", "getCode", "waitForDeployment", "deploymentTransaction", "wait", "reject", "checkCode", "format", "func", "queryTransaction", "hash", "queryFilter", "fromBlock", "toBlock", "getLogs", "listenerCount", "total", "concat", "splice", "removeAllListeners", "addListener", "removeListener", "buildClass", "CustomContract", "_ContractBase", "Contract"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\contract\\contract.ts"], "sourcesContent": ["import { Interface, Typed } from \"../abi/index.js\";\nimport { isAddressable, resolveAddress } from \"../address/index.js\";\n// import from provider.ts instead of index.ts to prevent circular dep\n// from EtherscanProvider\nimport { copyRequest, Log, TransactionResponse } from \"../providers/provider.js\";\nimport {\n    defineProperties, getBigInt, isCallException, isHexString, resolveProperties,\n    isError, makeError, assert, assertArgument\n} from \"../utils/index.js\";\n\nimport {\n    ContractEventPayload, ContractUnknownEventPayload,\n    ContractTransactionResponse,\n    EventLog, UndecodedEventLog\n} from \"./wrappers.js\";\n\nimport type { EventFragment, FunctionFragment, InterfaceAbi, ParamType, Result } from \"../abi/index.js\";\nimport type { Addressable, NameResolver } from \"../address/index.js\";\nimport type { EventEmitterable, Listener } from \"../utils/index.js\";\nimport type {\n    BlockTag, ContractRunner, Provider, TransactionRequest, TopicFilter\n} from \"../providers/index.js\";\n\nimport type {\n    BaseContractMethod,\n    ContractEventName,\n    ContractInterface,\n    ContractMethodArgs,\n    ContractMethod,\n    ContractEventArgs,\n    ContractEvent,\n    ContractTransaction,\n    DeferredTopicFilter,\n    WrappedFallback\n} from \"./types.js\";\n\nconst BN_0 = BigInt(0);\n\ninterface ContractRunnerCaller extends ContractRunner {\n    call: (tx: TransactionRequest) => Promise<string>;\n}\n\ninterface ContractRunnerEstimater extends ContractRunner {\n    estimateGas: (tx: TransactionRequest) => Promise<bigint>;\n}\n\ninterface ContractRunnerSender extends ContractRunner {\n    sendTransaction: (tx: TransactionRequest) => Promise<TransactionResponse>;\n}\n\ninterface ContractRunnerResolver extends ContractRunner {\n    resolveName: (name: string | Addressable) => Promise<null | string>;\n}\n\nfunction canCall(value: any): value is ContractRunnerCaller {\n    return (value && typeof(value.call) === \"function\");\n}\n\nfunction canEstimate(value: any): value is ContractRunnerEstimater {\n    return (value && typeof(value.estimateGas) === \"function\");\n}\n\nfunction canResolve(value: any): value is ContractRunnerResolver {\n    return (value && typeof(value.resolveName) === \"function\");\n}\n\nfunction canSend(value: any): value is ContractRunnerSender {\n    return (value && typeof(value.sendTransaction) === \"function\");\n}\n\nfunction getResolver(value: any): undefined | NameResolver {\n    if (value != null) {\n        if (canResolve(value)) { return value; }\n        if (value.provider) { return value.provider; }\n    }\n    return undefined;\n}\n\nclass PreparedTopicFilter implements DeferredTopicFilter {\n    #filter: Promise<TopicFilter>;\n    readonly fragment!: EventFragment;\n\n    constructor(contract: BaseContract, fragment: EventFragment, args: Array<any>) {\n        defineProperties<PreparedTopicFilter>(this, { fragment });\n        if (fragment.inputs.length < args.length) {\n            throw new Error(\"too many arguments\");\n        }\n\n        // Recursively descend into args and resolve any addresses\n        const runner = getRunner(contract.runner, \"resolveName\");\n        const resolver = canResolve(runner) ? runner: null;\n        this.#filter = (async function() {\n            const resolvedArgs = await Promise.all(fragment.inputs.map((param, index) => {\n                const arg = args[index];\n                if (arg == null) { return null; }\n\n                return param.walkAsync(args[index], (type, value) => {\n                    if (type === \"address\") {\n                        if (Array.isArray(value)) {\n                            return Promise.all(value.map((v) => resolveAddress(v, resolver)));\n                        }\n                        return resolveAddress(value, resolver);\n                    }\n                    return value;\n                });\n            }));\n\n            return contract.interface.encodeFilterTopics(fragment, resolvedArgs);\n        })();\n    }\n\n    getTopicFilter(): Promise<TopicFilter> {\n        return this.#filter;\n    }\n}\n\n\n// A = Arguments passed in as a tuple\n// R = The result type of the call (i.e. if only one return type,\n//     the qualified type, otherwise Result)\n// D = The type the default call will return (i.e. R for view/pure,\n//     TransactionResponse otherwise)\n//export interface ContractMethod<A extends Array<any> = Array<any>, R = any, D extends R | ContractTransactionResponse = ContractTransactionResponse> {\n\nfunction getRunner<T extends ContractRunner>(value: any, feature: keyof ContractRunner): null | T {\n    if (value == null) { return null; }\n    if (typeof(value[feature]) === \"function\") { return value; }\n    if (value.provider && typeof(value.provider[feature]) === \"function\") {\n        return value.provider;\n    }\n    return null;\n}\n\nfunction getProvider(value: null | ContractRunner): null | Provider {\n    if (value == null) { return null; }\n    return value.provider || null;\n}\n\n/**\n *  @_ignore:\n */\nexport async function copyOverrides<O extends string = \"data\" | \"to\">(arg: any, allowed?: Array<string>): Promise<Omit<ContractTransaction, O>> {\n\n    // Make sure the overrides passed in are a valid overrides object\n    const _overrides = Typed.dereference(arg, \"overrides\");\n    assertArgument(typeof(_overrides) === \"object\", \"invalid overrides parameter\", \"overrides\", arg);\n\n    // Create a shallow copy (we'll deep-ify anything needed during normalizing)\n    const overrides = copyRequest(_overrides);\n\n    assertArgument(overrides.to == null || (allowed || [ ]).indexOf(\"to\") >= 0,\n      \"cannot override to\", \"overrides.to\", overrides.to);\n    assertArgument(overrides.data == null || (allowed || [ ]).indexOf(\"data\") >= 0,\n      \"cannot override data\", \"overrides.data\", overrides.data);\n\n    // Resolve any from\n    if (overrides.from) { overrides.from = overrides.from; }\n\n    return <Omit<ContractTransaction, O>>overrides;\n}\n\n/**\n *  @_ignore:\n */\nexport async function resolveArgs(_runner: null | ContractRunner, inputs: ReadonlyArray<ParamType>, args: Array<any>): Promise<Array<any>> {\n    // Recursively descend into args and resolve any addresses\n    const runner = getRunner(_runner, \"resolveName\");\n    const resolver = canResolve(runner) ? runner: null;\n    return await Promise.all(inputs.map((param, index) => {\n        return param.walkAsync(args[index], (type, value) => {\n            value = Typed.dereference(value, type);\n            if (type === \"address\") { return resolveAddress(value, resolver); }\n            return value;\n        });\n    }));\n}\n\nfunction buildWrappedFallback(contract: BaseContract): WrappedFallback {\n\n    const populateTransaction = async function(overrides?: Omit<TransactionRequest, \"to\">): Promise<ContractTransaction> {\n        // If an overrides was passed in, copy it and normalize the values\n\n        const tx: ContractTransaction = <any>(await copyOverrides<\"data\">(overrides, [ \"data\" ]));\n        tx.to = await contract.getAddress();\n\n        if (tx.from) {\n            tx.from = await resolveAddress(tx.from, getResolver(contract.runner));\n        }\n\n        const iface = contract.interface;\n\n        const noValue = (getBigInt((tx.value || BN_0), \"overrides.value\") === BN_0);\n        const noData = ((tx.data || \"0x\") === \"0x\");\n\n        if (iface.fallback && !iface.fallback.payable && iface.receive && !noData && !noValue) {\n            assertArgument(false, \"cannot send data to receive or send value to non-payable fallback\", \"overrides\", overrides);\n        }\n\n        assertArgument(iface.fallback || noData,\n          \"cannot send data to receive-only contract\", \"overrides.data\", tx.data);\n\n        // Only allow payable contracts to set non-zero value\n        const payable = iface.receive || (iface.fallback && iface.fallback.payable);\n        assertArgument(payable || noValue,\n          \"cannot send value to non-payable fallback\", \"overrides.value\", tx.value);\n\n        // Only allow fallback contracts to set non-empty data\n        assertArgument(iface.fallback || noData,\n          \"cannot send data to receive-only contract\", \"overrides.data\", tx.data);\n\n        return tx;\n    }\n\n    const staticCall = async function(overrides?: Omit<TransactionRequest, \"to\">): Promise<string> {\n        const runner = getRunner(contract.runner, \"call\");\n        assert(canCall(runner), \"contract runner does not support calling\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"call\" });\n\n        const tx = await populateTransaction(overrides);\n\n        try {\n            return await runner.call(tx);\n        } catch (error: any) {\n            if (isCallException(error) && error.data) {\n                throw contract.interface.makeError(error.data, tx);\n            }\n            throw error;\n        }\n    }\n\n    const send = async function(overrides?: Omit<TransactionRequest, \"to\">): Promise<ContractTransactionResponse> {\n        const runner = contract.runner;\n        assert(canSend(runner), \"contract runner does not support sending transactions\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"sendTransaction\" });\n\n        const tx = await runner.sendTransaction(await populateTransaction(overrides));\n        const provider = getProvider(contract.runner);\n        // @TODO: the provider can be null; make a custom dummy provider that will throw a\n        // meaningful error\n        return new ContractTransactionResponse(contract.interface, <Provider>provider, tx);\n    }\n\n    const estimateGas = async function(overrides?: Omit<TransactionRequest, \"to\">): Promise<bigint> {\n        const runner = getRunner(contract.runner, \"estimateGas\");\n        assert(canEstimate(runner), \"contract runner does not support gas estimation\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"estimateGas\" });\n\n        return await runner.estimateGas(await populateTransaction(overrides));\n    }\n\n    const method = async (overrides?: Omit<TransactionRequest, \"to\">) => {\n        return await send(overrides);\n    };\n\n    defineProperties<any>(method, {\n        _contract: contract,\n\n        estimateGas,\n        populateTransaction,\n        send, staticCall\n    });\n\n    return <WrappedFallback>method;\n}\n\nfunction buildWrappedMethod<A extends Array<any> = Array<any>, R = any, D extends R | ContractTransactionResponse = ContractTransactionResponse>(contract: BaseContract, key: string): BaseContractMethod<A, R, D> {\n\n    const getFragment = function(...args: ContractMethodArgs<A>): FunctionFragment {\n        const fragment = contract.interface.getFunction(key, args);\n        assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"fragment\",\n            info: { key, args }\n        });\n        return fragment;\n    }\n\n    const populateTransaction = async function(...args: ContractMethodArgs<A>): Promise<ContractTransaction> {\n        const fragment = getFragment(...args);\n\n        // If an overrides was passed in, copy it and normalize the values\n        let overrides: Omit<ContractTransaction, \"data\" | \"to\"> = { };\n        if (fragment.inputs.length + 1 === args.length) {\n            overrides = await copyOverrides(args.pop());\n\n            if (overrides.from) {\n                overrides.from = await resolveAddress(overrides.from, getResolver(contract.runner));\n            }\n        }\n\n        if (fragment.inputs.length !== args.length) {\n            throw new Error(\"internal error: fragment inputs doesn't match arguments; should not happen\");\n        }\n\n        const resolvedArgs = await resolveArgs(contract.runner, fragment.inputs, args);\n\n        return Object.assign({ }, overrides, await resolveProperties({\n            to: contract.getAddress(),\n            data: contract.interface.encodeFunctionData(fragment, resolvedArgs)\n        }));\n    }\n\n    const staticCall = async function(...args: ContractMethodArgs<A>): Promise<R> {\n        const result = await staticCallResult(...args);\n        if (result.length === 1) { return result[0]; }\n        return <R><unknown>result;\n    }\n\n    const send = async function(...args: ContractMethodArgs<A>): Promise<ContractTransactionResponse> {\n        const runner = contract.runner;\n        assert(canSend(runner), \"contract runner does not support sending transactions\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"sendTransaction\" });\n\n        const tx = await runner.sendTransaction(await populateTransaction(...args));\n        const provider = getProvider(contract.runner);\n        // @TODO: the provider can be null; make a custom dummy provider that will throw a\n        // meaningful error\n        return new ContractTransactionResponse(contract.interface, <Provider>provider, tx);\n    }\n\n    const estimateGas = async function(...args: ContractMethodArgs<A>): Promise<bigint> {\n        const runner = getRunner(contract.runner, \"estimateGas\");\n        assert(canEstimate(runner), \"contract runner does not support gas estimation\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"estimateGas\" });\n\n        return await runner.estimateGas(await populateTransaction(...args));\n    }\n\n    const staticCallResult = async function(...args: ContractMethodArgs<A>): Promise<Result> {\n        const runner = getRunner(contract.runner, \"call\");\n        assert(canCall(runner), \"contract runner does not support calling\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"call\" });\n\n        const tx = await populateTransaction(...args);\n\n        let result = \"0x\";\n        try {\n            result = await runner.call(tx);\n        } catch (error: any) {\n            if (isCallException(error) && error.data) {\n                throw contract.interface.makeError(error.data, tx);\n            }\n            throw error;\n        }\n\n        const fragment = getFragment(...args);\n        return contract.interface.decodeFunctionResult(fragment, result);\n    };\n\n    const method = async (...args: ContractMethodArgs<A>) => {\n        const fragment = getFragment(...args);\n        if (fragment.constant) { return await staticCall(...args); }\n        return await send(...args);\n    };\n\n    defineProperties<any>(method, {\n        name: contract.interface.getFunctionName(key),\n        _contract: contract, _key: key,\n\n        getFragment,\n\n        estimateGas,\n        populateTransaction,\n        send, staticCall, staticCallResult,\n    });\n\n    // Only works on non-ambiguous keys (refined fragment is always non-ambiguous)\n    Object.defineProperty(method, \"fragment\", {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n            const fragment = contract.interface.getFunction(key);\n            assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"fragment\",\n                info: { key }\n            });\n            return fragment;\n        }\n    });\n\n    return <BaseContractMethod<A, R, D>>method;\n}\n\nfunction buildWrappedEvent<A extends Array<any> = Array<any>>(contract: BaseContract, key: string): ContractEvent<A> {\n\n    const getFragment = function(...args: ContractEventArgs<A>): EventFragment {\n        const fragment = contract.interface.getEvent(key, args);\n\n        assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"fragment\",\n            info: { key, args }\n        });\n\n        return fragment;\n    }\n\n    const method = function(...args: ContractMethodArgs<A>): PreparedTopicFilter {\n        return new PreparedTopicFilter(contract, getFragment(...args), args);\n    };\n\n    defineProperties<any>(method, {\n        name: contract.interface.getEventName(key),\n        _contract: contract, _key: key,\n\n        getFragment\n    });\n\n    // Only works on non-ambiguous keys (refined fragment is always non-ambiguous)\n    Object.defineProperty(method, \"fragment\", {\n        configurable: false,\n        enumerable: true,\n        get: () => {\n            const fragment = contract.interface.getEvent(key);\n\n            assert(fragment, \"no matching fragment\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"fragment\",\n                info: { key }\n            });\n\n            return fragment;\n        }\n    });\n\n    return <ContractEvent<A>><unknown>method;\n}\n\ntype Sub = {\n    tag: string;\n    listeners: Array<{ listener: Listener, once: boolean }>,\n    start: () => void;\n    stop: () => void;\n};\n\n\n// The combination of TypeScrype, Private Fields and Proxies makes\n// the world go boom; so we hide variables with some trickery keeping\n// a symbol attached to each BaseContract which its sub-class (even\n// via a Proxy) can reach and use to look up its internal values.\n\nconst internal = Symbol.for(\"_ethersInternal_contract\");\ntype Internal = {\n    addrPromise: Promise<string>;\n    addr: null | string;\n\n    deployTx: null | ContractTransactionResponse;\n\n    subs: Map<string, Sub>;\n};\n\nconst internalValues: WeakMap<BaseContract, Internal> = new WeakMap();\n\nfunction setInternal(contract: BaseContract, values: Internal): void {\n    internalValues.set(contract[internal], values);\n}\n\nfunction getInternal(contract: BaseContract): Internal {\n    return internalValues.get(contract[internal]) as Internal;\n}\n\nfunction isDeferred(value: any): value is DeferredTopicFilter {\n    return (value && typeof(value) === \"object\" && (\"getTopicFilter\" in value) &&\n      (typeof(value.getTopicFilter) === \"function\") && value.fragment);\n}\n\nasync function getSubInfo(contract: BaseContract, event: ContractEventName): Promise<{ fragment: null | EventFragment, tag: string, topics: TopicFilter }> {\n    let topics: Array<null | string | Array<string>>;\n    let fragment: null | EventFragment = null;\n\n    // Convert named events to topicHash and get the fragment for\n    // events which need deconstructing.\n\n    if (Array.isArray(event)) {\n        const topicHashify = function(name: string): string {\n            if (isHexString(name, 32)) { return name; }\n            const fragment = contract.interface.getEvent(name);\n            assertArgument(fragment, \"unknown fragment\", \"name\", name);\n            return fragment.topicHash;\n        }\n\n        // Array of Topics and Names; e.g. `[ \"0x1234...89ab\", \"Transfer(address)\" ]`\n        topics = event.map((e) => {\n            if (e == null) { return null; }\n            if (Array.isArray(e)) { return e.map(topicHashify); }\n            return topicHashify(e);\n        });\n\n    } else if (event === \"*\") {\n        topics = [ null ];\n\n    } else if (typeof(event) === \"string\") {\n        if (isHexString(event, 32)) {\n            // Topic Hash\n            topics = [ event ];\n        } else {\n           // Name or Signature; e.g. `\"Transfer\", `\"Transfer(address)\"`\n            fragment = contract.interface.getEvent(event);\n            assertArgument(fragment, \"unknown fragment\", \"event\", event);\n            topics = [ fragment.topicHash ];\n        }\n\n    } else if (isDeferred(event)) {\n        // Deferred Topic Filter; e.g. `contract.filter.Transfer(from)`\n        topics = await event.getTopicFilter();\n\n    } else if (\"fragment\" in event) {\n        // ContractEvent; e.g. `contract.filter.Transfer`\n        fragment = event.fragment;\n        topics = [ fragment.topicHash ];\n\n    } else {\n        assertArgument(false, \"unknown event name\", \"event\", event);\n    }\n\n    // Normalize topics and sort TopicSets\n    topics = topics.map((t) => {\n        if (t == null) { return null; }\n        if (Array.isArray(t)) {\n            const items = Array.from(new Set(t.map((t) => t.toLowerCase())).values());\n            if (items.length === 1) { return items[0]; }\n            items.sort();\n            return items;\n        }\n        return t.toLowerCase();\n    });\n\n    const tag = topics.map((t) => {\n        if (t == null) { return \"null\"; }\n        if (Array.isArray(t)) { return t.join(\"|\"); }\n        return t;\n    }).join(\"&\");\n\n    return { fragment, tag, topics }\n}\n\nasync function hasSub(contract: BaseContract, event: ContractEventName): Promise<null | Sub> {\n    const { subs } = getInternal(contract);\n    return subs.get((await getSubInfo(contract, event)).tag) || null;\n}\n\nasync function getSub(contract: BaseContract, operation: string, event: ContractEventName): Promise<Sub> {\n    // Make sure our runner can actually subscribe to events\n    const provider = getProvider(contract.runner);\n    assert(provider, \"contract runner does not support subscribing\",\n        \"UNSUPPORTED_OPERATION\", { operation });\n\n    const { fragment, tag, topics } = await getSubInfo(contract, event);\n\n    const { addr, subs } = getInternal(contract);\n\n    let sub = subs.get(tag);\n    if (!sub) {\n        const address: string | Addressable = (addr ? addr: contract);\n        const filter = { address, topics };\n        const listener = (log: Log) => {\n            let foundFragment = fragment;\n            if (foundFragment == null) {\n                try {\n                    foundFragment = contract.interface.getEvent(log.topics[0]);\n                } catch (error) { }\n            }\n\n            // If fragment is null, we do not deconstruct the args to emit\n\n            if (foundFragment) {\n                const _foundFragment = foundFragment;\n                const args = fragment ? contract.interface.decodeEventLog(fragment, log.data, log.topics): [ ];\n                emit(contract, event, args, (listener: null | Listener) => {\n                    return new ContractEventPayload(contract, listener, event, _foundFragment, log);\n                });\n            } else {\n                emit(contract, event, [ ], (listener: null | Listener) => {\n                    return new ContractUnknownEventPayload(contract, listener, event, log);\n                });\n            }\n        };\n\n        let starting: Array<Promise<any>> = [ ];\n        const start = () => {\n            if (starting.length) { return; }\n            starting.push(provider.on(filter, listener));\n        };\n\n        const stop = async () => {\n            if (starting.length == 0) { return; }\n\n            let started = starting;\n            starting = [ ];\n            await Promise.all(started);\n            provider.off(filter, listener);\n        };\n\n        sub = { tag, listeners: [ ], start, stop };\n        subs.set(tag, sub);\n    }\n    return sub;\n}\n\n// We use this to ensure one emit resolves before firing the next to\n// ensure correct ordering (note this cannot throw and just adds the\n// notice to the event queu using setTimeout).\nlet lastEmit: Promise<any> = Promise.resolve();\n\ntype PayloadFunc = (listener: null | Listener) => ContractUnknownEventPayload;\n\nasync function _emit(contract: BaseContract, event: ContractEventName, args: Array<any>, payloadFunc: null | PayloadFunc): Promise<boolean> {\n    await lastEmit;\n\n    const sub = await hasSub(contract, event);\n    if (!sub) { return false; }\n\n    const count = sub.listeners.length;\n    sub.listeners = sub.listeners.filter(({ listener, once }) => {\n        const passArgs = Array.from(args);\n        if (payloadFunc) {\n            passArgs.push(payloadFunc(once ? null: listener));\n        }\n        try {\n            listener.call(contract, ...passArgs);\n        } catch (error) { }\n        return !once;\n    });\n\n    if (sub.listeners.length === 0) {\n        sub.stop();\n        getInternal(contract).subs.delete(sub.tag);\n    }\n\n    return (count > 0);\n}\n\nasync function emit(contract: BaseContract, event: ContractEventName, args: Array<any>, payloadFunc: null | PayloadFunc): Promise<boolean> {\n    try {\n        await lastEmit;\n    } catch (error) { }\n\n    const resultPromise = _emit(contract, event, args, payloadFunc);\n    lastEmit = resultPromise;\n    return await resultPromise;\n}\n\nconst passProperties = [ \"then\" ];\nexport class BaseContract implements Addressable, EventEmitterable<ContractEventName> {\n    /**\n     *  The target to connect to.\n     *\n     *  This can be an address, ENS name or any [[Addressable]], such as\n     *  another contract. To get the resovled address, use the ``getAddress``\n     *  method.\n     */\n    readonly target!: string | Addressable;\n\n    /**\n     *  The contract Interface.\n     */\n    readonly interface!: Interface;\n\n    /**\n     *  The connected runner. This is generally a [[Provider]] or a\n     *  [[Signer]], which dictates what operations are supported.\n     *\n     *  For example, a **Contract** connected to a [[Provider]] may\n     *  only execute read-only operations.\n     */\n    readonly runner!: null | ContractRunner;\n\n    /**\n     *  All the Events available on this contract.\n     */\n    readonly filters!: Record<string, ContractEvent>;\n\n    /**\n     *  @_ignore:\n     */\n    readonly [internal]: any;\n\n    /**\n     *  The fallback or receive function if any.\n     */\n    readonly fallback!: null | WrappedFallback;\n\n    /**\n     *  Creates a new contract connected to %%target%% with the %%abi%% and\n     *  optionally connected to a %%runner%% to perform operations on behalf\n     *  of.\n     */\n    constructor(target: string | Addressable, abi: Interface | InterfaceAbi, runner?: null | ContractRunner, _deployTx?: null | TransactionResponse) {\n        assertArgument(typeof(target) === \"string\" || isAddressable(target),\n            \"invalid value for Contract target\", \"target\", target);\n\n        if (runner == null) { runner = null; }\n        const iface = Interface.from(abi);\n        defineProperties<BaseContract>(this, { target, runner, interface: iface });\n\n        Object.defineProperty(this, internal, { value: { } });\n\n        let addrPromise;\n        let addr: null | string = null;\n\n        let deployTx: null | ContractTransactionResponse = null;\n        if (_deployTx) {\n            const provider = getProvider(runner);\n            // @TODO: the provider can be null; make a custom dummy provider that will throw a\n            // meaningful error\n            deployTx = new ContractTransactionResponse(this.interface, <Provider>provider, _deployTx);\n        }\n\n        let subs = new Map();\n\n        // Resolve the target as the address\n        if (typeof(target) === \"string\") {\n            if (isHexString(target)) {\n                addr = target;\n                addrPromise = Promise.resolve(target);\n\n            } else {\n                const resolver = getRunner(runner, \"resolveName\");\n                if (!canResolve(resolver)) {\n                    throw makeError(\"contract runner does not support name resolution\", \"UNSUPPORTED_OPERATION\", {\n                        operation: \"resolveName\"\n                    });\n                }\n\n                addrPromise = resolver.resolveName(target).then((addr) => {\n                    if (addr == null) {\n                        throw makeError(\"an ENS name used for a contract target must be correctly configured\", \"UNCONFIGURED_NAME\", {\n                            value: target\n                        });\n                    }\n                    getInternal(this).addr = addr;\n                    return addr;\n                });\n            }\n        } else {\n            addrPromise = target.getAddress().then((addr) => {\n                if (addr == null) { throw new Error(\"TODO\"); }\n                getInternal(this).addr = addr;\n                return addr;\n            });\n        }\n\n        // Set our private values\n        setInternal(this, { addrPromise, addr, deployTx, subs });\n\n        // Add the event filters\n        const filters = new Proxy({ }, {\n            get: (target, prop, receiver) => {\n                // Pass important checks (like `then` for Promise) through\n                if (typeof(prop) === \"symbol\" || passProperties.indexOf(prop) >= 0) {\n                    return Reflect.get(target, prop, receiver);\n                }\n\n                try {\n                    return this.getEvent(prop);\n                } catch (error) {\n                    if (!isError(error, \"INVALID_ARGUMENT\") || error.argument !== \"key\") {\n                        throw error;\n                    }\n                }\n\n                return undefined;\n            },\n            has: (target, prop) => {\n                // Pass important checks (like `then` for Promise) through\n                if (passProperties.indexOf(<string>prop) >= 0) {\n                    return Reflect.has(target, prop);\n                }\n\n                return Reflect.has(target, prop) || this.interface.hasEvent(String(prop));\n            }\n        });\n        defineProperties<BaseContract>(this, { filters });\n\n        defineProperties<BaseContract>(this, {\n            fallback: ((iface.receive || iface.fallback) ? (buildWrappedFallback(this)): null)\n        });\n\n        // Return a Proxy that will respond to functions\n        return new Proxy(this, {\n            get: (target, prop, receiver) => {\n                if (typeof(prop) === \"symbol\" || prop in target || passProperties.indexOf(prop) >= 0) {\n                    return Reflect.get(target, prop, receiver);\n                }\n\n                // Undefined properties should return undefined\n                try {\n                    return target.getFunction(prop);\n                } catch (error) {\n                    if (!isError(error, \"INVALID_ARGUMENT\") || error.argument !== \"key\") {\n                        throw error;\n                    }\n                }\n\n                return undefined;\n            },\n            has: (target, prop) => {\n                if (typeof(prop) === \"symbol\" || prop in target || passProperties.indexOf(prop) >= 0) {\n                    return Reflect.has(target, prop);\n                }\n\n                return target.interface.hasFunction(prop);\n            }\n        });\n\n    }\n\n    /**\n     *  Return a new Contract instance with the same target and ABI, but\n     *  a different %%runner%%.\n     */\n    connect(runner: null | ContractRunner): BaseContract {\n        return new BaseContract(this.target, this.interface, runner);\n    }\n\n    /**\n     *  Return a new Contract instance with the same ABI and runner, but\n     *  a different %%target%%.\n     */\n    attach(target: string | Addressable): BaseContract {\n        return new BaseContract(target, this.interface, this.runner);\n    }\n\n    /**\n     *  Return the resolved address of this Contract.\n     */\n    async getAddress(): Promise<string> { return await getInternal(this).addrPromise; }\n\n    /**\n     *  Return the deployed bytecode or null if no bytecode is found.\n     */\n    async getDeployedCode(): Promise<null | string> {\n        const provider = getProvider(this.runner);\n        assert(provider, \"runner does not support .provider\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"getDeployedCode\" });\n\n        const code = await provider.getCode(await this.getAddress());\n        if (code === \"0x\") { return null; }\n        return code;\n    }\n\n    /**\n     *  Resolve to this Contract once the bytecode has been deployed, or\n     *  resolve immediately if already deployed.\n     */\n    async waitForDeployment(): Promise<this> {\n        // We have the deployement transaction; just use that (throws if deployement fails)\n        const deployTx = this.deploymentTransaction();\n        if (deployTx) {\n            await deployTx.wait();\n            return this;\n        }\n\n        // Check for code\n        const code = await this.getDeployedCode();\n        if (code != null) { return this; }\n\n        // Make sure we can subscribe to a provider event\n        const provider = getProvider(this.runner);\n        assert(provider != null, \"contract runner does not support .provider\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"waitForDeployment\" });\n\n        return new Promise((resolve, reject) => {\n            const checkCode = async () => {\n                try {\n                    const code = await this.getDeployedCode();\n                    if (code != null) { return resolve(this); }\n                    provider.once(\"block\", checkCode);\n                } catch (error) {\n                    reject(error);\n                }\n            };\n            checkCode();\n        });\n    }\n\n    /**\n     *  Return the transaction used to deploy this contract.\n     *\n     *  This is only available if this instance was returned from a\n     *  [[ContractFactory]].\n     */\n    deploymentTransaction(): null | ContractTransactionResponse {\n        return getInternal(this).deployTx;\n    }\n\n    /**\n     *  Return the function for a given name. This is useful when a contract\n     *  method name conflicts with a JavaScript name such as ``prototype`` or\n     *  when using a Contract programatically.\n     */\n    getFunction<T extends ContractMethod = ContractMethod>(key: string | FunctionFragment): T {\n        if (typeof(key) !== \"string\") { key = key.format(); }\n        const func = buildWrappedMethod(this, key);\n        return <T>func;\n    }\n\n    /**\n     *  Return the event for a given name. This is useful when a contract\n     *  event name conflicts with a JavaScript name such as ``prototype`` or\n     *  when using a Contract programatically.\n     */\n    getEvent(key: string | EventFragment): ContractEvent {\n        if (typeof(key) !== \"string\") { key = key.format(); }\n        return buildWrappedEvent(this, key);\n    }\n\n    /**\n     *  @_ignore:\n     */\n    async queryTransaction(hash: string): Promise<Array<EventLog>> {\n        throw new Error(\"@TODO\");\n    }\n\n    /*\n    // @TODO: this is a non-backwards compatible change, but will be added\n    //        in v7 and in a potential SmartContract class in an upcoming\n    //        v6 release\n    async getTransactionReceipt(hash: string): Promise<null | ContractTransactionReceipt> {\n        const provider = getProvider(this.runner);\n        assert(provider, \"contract runner does not have a provider\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"queryTransaction\" });\n\n        const receipt = await provider.getTransactionReceipt(hash);\n        if (receipt == null) { return null; }\n\n        return new ContractTransactionReceipt(this.interface, provider, receipt);\n    }\n    */\n\n    /**\n     *  Provide historic access to event data for %%event%% in the range\n     *  %%fromBlock%% (default: ``0``) to %%toBlock%% (default: ``\"latest\"``)\n     *  inclusive.\n     */\n    async queryFilter(event: ContractEventName, fromBlock?: BlockTag, toBlock?: BlockTag): Promise<Array<EventLog | Log>> {\n        if (fromBlock == null) { fromBlock = 0; }\n        if (toBlock == null) { toBlock = \"latest\"; }\n        const { addr, addrPromise } = getInternal(this);\n        const address = (addr ? addr: (await addrPromise));\n        const { fragment, topics } = await getSubInfo(this, event);\n        const filter = { address, topics, fromBlock, toBlock };\n\n        const provider = getProvider(this.runner);\n        assert(provider, \"contract runner does not have a provider\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"queryFilter\" });\n\n        return (await provider.getLogs(filter)).map((log) => {\n            let foundFragment = fragment;\n            if (foundFragment == null) {\n                try {\n                    foundFragment = this.interface.getEvent(log.topics[0]);\n                } catch (error) { }\n            }\n\n            if (foundFragment) {\n                try {\n                    return new EventLog(log, this.interface, foundFragment);\n                } catch (error: any) {\n                    return new UndecodedEventLog(log, error);\n                }\n            }\n\n            return new Log(log, provider);\n        });\n    }\n\n    /**\n     *  Add an event %%listener%% for the %%event%%.\n     */\n    async on(event: ContractEventName, listener: Listener): Promise<this> {\n        const sub = await getSub(this, \"on\", event);\n        sub.listeners.push({ listener, once: false });\n        sub.start();\n        return this;\n    }\n\n    /**\n     *  Add an event %%listener%% for the %%event%%, but remove the listener\n     *  after it is fired once.\n     */\n    async once(event: ContractEventName, listener: Listener): Promise<this> {\n        const sub = await getSub(this, \"once\", event);\n        sub.listeners.push({ listener, once: true });\n        sub.start();\n        return this;\n    }\n\n    /**\n     *  Emit an %%event%% calling all listeners with %%args%%.\n     *\n     *  Resolves to ``true`` if any listeners were called.\n     */\n    async emit(event: ContractEventName, ...args: Array<any>): Promise<boolean> {\n        return await emit(this, event, args, null);\n    }\n\n    /**\n     *  Resolves to the number of listeners of %%event%% or the total number\n     *  of listeners if unspecified.\n     */\n    async listenerCount(event?: ContractEventName): Promise<number> {\n        if (event) {\n            const sub = await hasSub(this, event);\n            if (!sub) { return 0; }\n            return sub.listeners.length;\n        }\n\n        const { subs } = getInternal(this);\n\n        let total = 0;\n        for (const { listeners } of subs.values()) {\n            total += listeners.length;\n        }\n        return total;\n    }\n\n    /**\n     *  Resolves to the listeners subscribed to %%event%% or all listeners\n     *  if unspecified.\n     */\n    async listeners(event?: ContractEventName): Promise<Array<Listener>> {\n        if (event) {\n            const sub = await hasSub(this, event);\n            if (!sub) { return [ ]; }\n            return sub.listeners.map(({ listener }) => listener);\n        }\n\n        const { subs } = getInternal(this);\n\n        let result: Array<Listener> = [ ];\n        for (const { listeners } of subs.values()) {\n            result = result.concat(listeners.map(({ listener }) => listener));\n        }\n        return result;\n    }\n\n    /**\n     *  Remove the %%listener%% from the listeners for %%event%% or remove\n     *  all listeners if unspecified.\n     */\n    async off(event: ContractEventName, listener?: Listener): Promise<this> {\n        const sub = await hasSub(this, event);\n        if (!sub) { return this; }\n\n        if (listener) {\n            const index = sub.listeners.map(({ listener }) => listener).indexOf(listener);\n            if (index >= 0) { sub.listeners.splice(index, 1); }\n        }\n\n        if (listener == null || sub.listeners.length === 0) {\n            sub.stop();\n            getInternal(this).subs.delete(sub.tag);\n        }\n\n        return this;\n    }\n\n    /**\n     *  Remove all the listeners for %%event%% or remove all listeners if\n     *  unspecified.\n     */\n    async removeAllListeners(event?: ContractEventName): Promise<this> {\n        if (event) {\n            const sub = await hasSub(this, event);\n            if (!sub) { return this; }\n            sub.stop();\n            getInternal(this).subs.delete(sub.tag);\n        } else {\n            const { subs } = getInternal(this);\n            for (const { tag, stop } of subs.values()) {\n                stop();\n                subs.delete(tag);\n            }\n        }\n\n        return this;\n    }\n\n    /**\n     *  Alias for [on].\n     */\n    async addListener(event: ContractEventName, listener: Listener): Promise<this> {\n        return await this.on(event, listener);\n    }\n\n    /**\n     *  Alias for [off].\n     */\n    async removeListener(event: ContractEventName, listener: Listener): Promise<this> {\n        return await this.off(event, listener);\n    }\n\n    /**\n     *  Create a new Class for the %%abi%%.\n     */\n    static buildClass<T = ContractInterface>(abi: Interface | InterfaceAbi): new (target: string, runner?: null | ContractRunner) => BaseContract & Omit<T, keyof BaseContract> {\n        class CustomContract extends BaseContract {\n            constructor(address: string, runner: null | ContractRunner = null) {\n                super(address, abi, runner);\n            }\n        }\n        return CustomContract as any;\n    };\n\n    /**\n     *  Create a new BaseContract with a specified Interface.\n     */\n    static from<T = ContractInterface>(target: string, abi: Interface | InterfaceAbi, runner?: null | ContractRunner): BaseContract & Omit<T, keyof BaseContract> {\n        if (runner == null) { runner = null; }\n        const contract = new this(target, abi, runner );\n        return contract as any;\n    }\n}\n\nfunction _ContractBase(): new (target: string | Addressable, abi: Interface | InterfaceAbi, runner?: null | ContractRunner) => BaseContract & Omit<ContractInterface, keyof BaseContract> {\n    return BaseContract as any;\n}\n\n/**\n *  A [[BaseContract]] with no type guards on its methods or events.\n */\nexport class Contract extends _ContractBase() { }\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,QAAQ,iBAAiB;AAClD,SAASC,aAAa,EAAEC,cAAc,QAAQ,qBAAqB;AACnE;AACA;AACA,SAASC,WAAW,EAAEC,GAAG,QAA6B,0BAA0B;AAChF,SACIC,gBAAgB,EAAEC,SAAS,EAAEC,eAAe,EAAEC,WAAW,EAAEC,iBAAiB,EAC5EC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,QACvC,mBAAmB;AAE1B,SACIC,oBAAoB,EAAEC,2BAA2B,EACjDC,2BAA2B,EAC3BC,QAAQ,EAAEC,iBAAiB,QACxB,eAAe;AAsBtB,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AAkBtB,SAASC,OAAOA,CAACC,KAAU;EACvB,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAK,KAAK,UAAU;AACtD;AAEA,SAASC,WAAWA,CAACF,KAAU;EAC3B,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACG,WAAY,KAAK,UAAU;AAC7D;AAEA,SAASC,UAAUA,CAACJ,KAAU;EAC1B,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACK,WAAY,KAAK,UAAU;AAC7D;AAEA,SAASC,OAAOA,CAACN,KAAU;EACvB,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACO,eAAgB,KAAK,UAAU;AACjE;AAEA,SAASC,WAAWA,CAACR,KAAU;EAC3B,IAAIA,KAAK,IAAI,IAAI,EAAE;IACf,IAAII,UAAU,CAACJ,KAAK,CAAC,EAAE;MAAE,OAAOA,KAAK;;IACrC,IAAIA,KAAK,CAACS,QAAQ,EAAE;MAAE,OAAOT,KAAK,CAACS,QAAQ;;;EAE/C,OAAOC,SAAS;AACpB;AAEA,MAAMC,mBAAmB;EACrB,CAAAC,MAAO;EACEC,QAAQ;EAEjBC,YAAYC,QAAsB,EAAEF,QAAuB,EAAEG,IAAgB;IACzEjC,gBAAgB,CAAsB,IAAI,EAAE;MAAE8B;IAAQ,CAAE,CAAC;IACzD,IAAIA,QAAQ,CAACI,MAAM,CAACC,MAAM,GAAGF,IAAI,CAACE,MAAM,EAAE;MACtC,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;;IAGzC;IACA,MAAMC,MAAM,GAAGC,SAAS,CAACN,QAAQ,CAACK,MAAM,EAAE,aAAa,CAAC;IACxD,MAAME,QAAQ,GAAGlB,UAAU,CAACgB,MAAM,CAAC,GAAGA,MAAM,GAAE,IAAI;IAClD,IAAI,CAAC,CAAAR,MAAO,GAAI,kBAAK;MACjB,MAAMW,YAAY,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACZ,QAAQ,CAACI,MAAM,CAACS,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;QACxE,MAAMC,GAAG,GAAGb,IAAI,CAACY,KAAK,CAAC;QACvB,IAAIC,GAAG,IAAI,IAAI,EAAE;UAAE,OAAO,IAAI;;QAE9B,OAAOF,KAAK,CAACG,SAAS,CAACd,IAAI,CAACY,KAAK,CAAC,EAAE,CAACG,IAAI,EAAE/B,KAAK,KAAI;UAChD,IAAI+B,IAAI,KAAK,SAAS,EAAE;YACpB,IAAIC,KAAK,CAACC,OAAO,CAACjC,KAAK,CAAC,EAAE;cACtB,OAAOwB,OAAO,CAACC,GAAG,CAACzB,KAAK,CAAC0B,GAAG,CAAEQ,CAAC,IAAKtD,cAAc,CAACsD,CAAC,EAAEZ,QAAQ,CAAC,CAAC,CAAC;;YAErE,OAAO1C,cAAc,CAACoB,KAAK,EAAEsB,QAAQ,CAAC;;UAE1C,OAAOtB,KAAK;QAChB,CAAC,CAAC;MACN,CAAC,CAAC,CAAC;MAEH,OAAOe,QAAQ,CAACoB,SAAS,CAACC,kBAAkB,CAACvB,QAAQ,EAAEU,YAAY,CAAC;IACxE,CAAC,CAAC,CAAE;EACR;EAEAc,cAAcA,CAAA;IACV,OAAO,IAAI,CAAC,CAAAzB,MAAO;EACvB;;AAIJ;AACA;AACA;AACA;AACA;AACA;AAEA,SAASS,SAASA,CAA2BrB,KAAU,EAAEsC,OAA6B;EAClF,IAAItC,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,IAAI;;EAChC,IAAI,OAAOA,KAAK,CAACsC,OAAO,CAAE,KAAK,UAAU,EAAE;IAAE,OAAOtC,KAAK;;EACzD,IAAIA,KAAK,CAACS,QAAQ,IAAI,OAAOT,KAAK,CAACS,QAAQ,CAAC6B,OAAO,CAAE,KAAK,UAAU,EAAE;IAClE,OAAOtC,KAAK,CAACS,QAAQ;;EAEzB,OAAO,IAAI;AACf;AAEA,SAAS8B,WAAWA,CAACvC,KAA4B;EAC7C,IAAIA,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,IAAI;;EAChC,OAAOA,KAAK,CAACS,QAAQ,IAAI,IAAI;AACjC;AAEA;;;AAGA,OAAO,eAAe+B,aAAaA,CAAmCX,GAAQ,EAAEY,OAAuB;EAEnG;EACA,MAAMC,UAAU,GAAGhE,KAAK,CAACiE,WAAW,CAACd,GAAG,EAAE,WAAW,CAAC;EACtDtC,cAAc,CAAC,OAAOmD,UAAW,KAAK,QAAQ,EAAE,6BAA6B,EAAE,WAAW,EAAEb,GAAG,CAAC;EAEhG;EACA,MAAMe,SAAS,GAAG/D,WAAW,CAAC6D,UAAU,CAAC;EAEzCnD,cAAc,CAACqD,SAAS,CAACC,EAAE,IAAI,IAAI,IAAI,CAACJ,OAAO,IAAI,EAAG,EAAEK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACxE,oBAAoB,EAAE,cAAc,EAAEF,SAAS,CAACC,EAAE,CAAC;EACrDtD,cAAc,CAACqD,SAAS,CAACG,IAAI,IAAI,IAAI,IAAI,CAACN,OAAO,IAAI,EAAG,EAAEK,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,EAC5E,sBAAsB,EAAE,gBAAgB,EAAEF,SAAS,CAACG,IAAI,CAAC;EAE3D;EACA,IAAIH,SAAS,CAACI,IAAI,EAAE;IAAEJ,SAAS,CAACI,IAAI,GAAGJ,SAAS,CAACI,IAAI;;EAErD,OAAqCJ,SAAS;AAClD;AAEA;;;AAGA,OAAO,eAAeK,WAAWA,CAACC,OAA8B,EAAEjC,MAAgC,EAAED,IAAgB;EAChH;EACA,MAAMI,MAAM,GAAGC,SAAS,CAAC6B,OAAO,EAAE,aAAa,CAAC;EAChD,MAAM5B,QAAQ,GAAGlB,UAAU,CAACgB,MAAM,CAAC,GAAGA,MAAM,GAAE,IAAI;EAClD,OAAO,MAAMI,OAAO,CAACC,GAAG,CAACR,MAAM,CAACS,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAI;IACjD,OAAOD,KAAK,CAACG,SAAS,CAACd,IAAI,CAACY,KAAK,CAAC,EAAE,CAACG,IAAI,EAAE/B,KAAK,KAAI;MAChDA,KAAK,GAAGtB,KAAK,CAACiE,WAAW,CAAC3C,KAAK,EAAE+B,IAAI,CAAC;MACtC,IAAIA,IAAI,KAAK,SAAS,EAAE;QAAE,OAAOnD,cAAc,CAACoB,KAAK,EAAEsB,QAAQ,CAAC;;MAChE,OAAOtB,KAAK;IAChB,CAAC,CAAC;EACN,CAAC,CAAC,CAAC;AACP;AAEA,SAASmD,oBAAoBA,CAACpC,QAAsB;EAEhD,MAAMqC,mBAAmB,GAAG,eAAAA,CAAeR,SAA0C;IACjF;IAEA,MAAMS,EAAE,GAA8B,MAAMb,aAAa,CAASI,SAAS,EAAE,CAAE,MAAM,CAAE,CAAE;IACzFS,EAAE,CAACR,EAAE,GAAG,MAAM9B,QAAQ,CAACuC,UAAU,EAAE;IAEnC,IAAID,EAAE,CAACL,IAAI,EAAE;MACTK,EAAE,CAACL,IAAI,GAAG,MAAMpE,cAAc,CAACyE,EAAE,CAACL,IAAI,EAAExC,WAAW,CAACO,QAAQ,CAACK,MAAM,CAAC,CAAC;;IAGzE,MAAMmC,KAAK,GAAGxC,QAAQ,CAACoB,SAAS;IAEhC,MAAMqB,OAAO,GAAIxE,SAAS,CAAEqE,EAAE,CAACrD,KAAK,IAAIH,IAAI,EAAG,iBAAiB,CAAC,KAAKA,IAAK;IAC3E,MAAM4D,MAAM,GAAI,CAACJ,EAAE,CAACN,IAAI,IAAI,IAAI,MAAM,IAAK;IAE3C,IAAIQ,KAAK,CAACG,QAAQ,IAAI,CAACH,KAAK,CAACG,QAAQ,CAACC,OAAO,IAAIJ,KAAK,CAACK,OAAO,IAAI,CAACH,MAAM,IAAI,CAACD,OAAO,EAAE;MACnFjE,cAAc,CAAC,KAAK,EAAE,mEAAmE,EAAE,WAAW,EAAEqD,SAAS,CAAC;;IAGtHrD,cAAc,CAACgE,KAAK,CAACG,QAAQ,IAAID,MAAM,EACrC,2CAA2C,EAAE,gBAAgB,EAAEJ,EAAE,CAACN,IAAI,CAAC;IAEzE;IACA,MAAMY,OAAO,GAAGJ,KAAK,CAACK,OAAO,IAAKL,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,OAAQ;IAC3EpE,cAAc,CAACoE,OAAO,IAAIH,OAAO,EAC/B,2CAA2C,EAAE,iBAAiB,EAAEH,EAAE,CAACrD,KAAK,CAAC;IAE3E;IACAT,cAAc,CAACgE,KAAK,CAACG,QAAQ,IAAID,MAAM,EACrC,2CAA2C,EAAE,gBAAgB,EAAEJ,EAAE,CAACN,IAAI,CAAC;IAEzE,OAAOM,EAAE;EACb,CAAC;EAED,MAAMQ,UAAU,GAAG,eAAAA,CAAejB,SAA0C;IACxE,MAAMxB,MAAM,GAAGC,SAAS,CAACN,QAAQ,CAACK,MAAM,EAAE,MAAM,CAAC;IACjD9B,MAAM,CAACS,OAAO,CAACqB,MAAM,CAAC,EAAE,0CAA0C,EAC9D,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAM,CAAE,CAAC;IAEnD,MAAMT,EAAE,GAAG,MAAMD,mBAAmB,CAACR,SAAS,CAAC;IAE/C,IAAI;MACA,OAAO,MAAMxB,MAAM,CAACnB,IAAI,CAACoD,EAAE,CAAC;KAC/B,CAAC,OAAOU,KAAU,EAAE;MACjB,IAAI9E,eAAe,CAAC8E,KAAK,CAAC,IAAIA,KAAK,CAAChB,IAAI,EAAE;QACtC,MAAMhC,QAAQ,CAACoB,SAAS,CAAC9C,SAAS,CAAC0E,KAAK,CAAChB,IAAI,EAAEM,EAAE,CAAC;;MAEtD,MAAMU,KAAK;;EAEnB,CAAC;EAED,MAAMC,IAAI,GAAG,eAAAA,CAAepB,SAA0C;IAClE,MAAMxB,MAAM,GAAGL,QAAQ,CAACK,MAAM;IAC9B9B,MAAM,CAACgB,OAAO,CAACc,MAAM,CAAC,EAAE,uDAAuD,EAC3E,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAiB,CAAE,CAAC;IAE9D,MAAMT,EAAE,GAAG,MAAMjC,MAAM,CAACb,eAAe,CAAC,MAAM6C,mBAAmB,CAACR,SAAS,CAAC,CAAC;IAC7E,MAAMnC,QAAQ,GAAG8B,WAAW,CAACxB,QAAQ,CAACK,MAAM,CAAC;IAC7C;IACA;IACA,OAAO,IAAI1B,2BAA2B,CAACqB,QAAQ,CAACoB,SAAS,EAAY1B,QAAQ,EAAE4C,EAAE,CAAC;EACtF,CAAC;EAED,MAAMlD,WAAW,GAAG,eAAAA,CAAeyC,SAA0C;IACzE,MAAMxB,MAAM,GAAGC,SAAS,CAACN,QAAQ,CAACK,MAAM,EAAE,aAAa,CAAC;IACxD9B,MAAM,CAACY,WAAW,CAACkB,MAAM,CAAC,EAAE,iDAAiD,EACzE,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAa,CAAE,CAAC;IAE1D,OAAO,MAAM1C,MAAM,CAACjB,WAAW,CAAC,MAAMiD,mBAAmB,CAACR,SAAS,CAAC,CAAC;EACzE,CAAC;EAED,MAAMqB,MAAM,GAAG,MAAOrB,SAA0C,IAAI;IAChE,OAAO,MAAMoB,IAAI,CAACpB,SAAS,CAAC;EAChC,CAAC;EAED7D,gBAAgB,CAAMkF,MAAM,EAAE;IAC1BC,SAAS,EAAEnD,QAAQ;IAEnBZ,WAAW;IACXiD,mBAAmB;IACnBY,IAAI;IAAEH;GACT,CAAC;EAEF,OAAwBI,MAAM;AAClC;AAEA,SAASE,kBAAkBA,CAAsHpD,QAAsB,EAAEqD,GAAW;EAEhL,MAAMC,WAAW,GAAG,SAAAA,CAAS,GAAGrD,IAA2B;IACvD,MAAMH,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACmC,WAAW,CAACF,GAAG,EAAEpD,IAAI,CAAC;IAC1D1B,MAAM,CAACuB,QAAQ,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;MAC9DiD,SAAS,EAAE,UAAU;MACrBS,IAAI,EAAE;QAAEH,GAAG;QAAEpD;MAAI;KACpB,CAAC;IACF,OAAOH,QAAQ;EACnB,CAAC;EAED,MAAMuC,mBAAmB,GAAG,eAAAA,CAAe,GAAGpC,IAA2B;IACrE,MAAMH,QAAQ,GAAGwD,WAAW,CAAC,GAAGrD,IAAI,CAAC;IAErC;IACA,IAAI4B,SAAS,GAA6C,EAAG;IAC7D,IAAI/B,QAAQ,CAACI,MAAM,CAACC,MAAM,GAAG,CAAC,KAAKF,IAAI,CAACE,MAAM,EAAE;MAC5C0B,SAAS,GAAG,MAAMJ,aAAa,CAACxB,IAAI,CAACwD,GAAG,EAAE,CAAC;MAE3C,IAAI5B,SAAS,CAACI,IAAI,EAAE;QAChBJ,SAAS,CAACI,IAAI,GAAG,MAAMpE,cAAc,CAACgE,SAAS,CAACI,IAAI,EAAExC,WAAW,CAACO,QAAQ,CAACK,MAAM,CAAC,CAAC;;;IAI3F,IAAIP,QAAQ,CAACI,MAAM,CAACC,MAAM,KAAKF,IAAI,CAACE,MAAM,EAAE;MACxC,MAAM,IAAIC,KAAK,CAAC,4EAA4E,CAAC;;IAGjG,MAAMI,YAAY,GAAG,MAAM0B,WAAW,CAAClC,QAAQ,CAACK,MAAM,EAAEP,QAAQ,CAACI,MAAM,EAAED,IAAI,CAAC;IAE9E,OAAOyD,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE9B,SAAS,EAAE,MAAMzD,iBAAiB,CAAC;MACzD0D,EAAE,EAAE9B,QAAQ,CAACuC,UAAU,EAAE;MACzBP,IAAI,EAAEhC,QAAQ,CAACoB,SAAS,CAACwC,kBAAkB,CAAC9D,QAAQ,EAAEU,YAAY;KACrE,CAAC,CAAC;EACP,CAAC;EAED,MAAMsC,UAAU,GAAG,eAAAA,CAAe,GAAG7C,IAA2B;IAC5D,MAAM4D,MAAM,GAAG,MAAMC,gBAAgB,CAAC,GAAG7D,IAAI,CAAC;IAC9C,IAAI4D,MAAM,CAAC1D,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO0D,MAAM,CAAC,CAAC,CAAC;;IAC3C,OAAmBA,MAAM;EAC7B,CAAC;EAED,MAAMZ,IAAI,GAAG,eAAAA,CAAe,GAAGhD,IAA2B;IACtD,MAAMI,MAAM,GAAGL,QAAQ,CAACK,MAAM;IAC9B9B,MAAM,CAACgB,OAAO,CAACc,MAAM,CAAC,EAAE,uDAAuD,EAC3E,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAiB,CAAE,CAAC;IAE9D,MAAMT,EAAE,GAAG,MAAMjC,MAAM,CAACb,eAAe,CAAC,MAAM6C,mBAAmB,CAAC,GAAGpC,IAAI,CAAC,CAAC;IAC3E,MAAMP,QAAQ,GAAG8B,WAAW,CAACxB,QAAQ,CAACK,MAAM,CAAC;IAC7C;IACA;IACA,OAAO,IAAI1B,2BAA2B,CAACqB,QAAQ,CAACoB,SAAS,EAAY1B,QAAQ,EAAE4C,EAAE,CAAC;EACtF,CAAC;EAED,MAAMlD,WAAW,GAAG,eAAAA,CAAe,GAAGa,IAA2B;IAC7D,MAAMI,MAAM,GAAGC,SAAS,CAACN,QAAQ,CAACK,MAAM,EAAE,aAAa,CAAC;IACxD9B,MAAM,CAACY,WAAW,CAACkB,MAAM,CAAC,EAAE,iDAAiD,EACzE,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAa,CAAE,CAAC;IAE1D,OAAO,MAAM1C,MAAM,CAACjB,WAAW,CAAC,MAAMiD,mBAAmB,CAAC,GAAGpC,IAAI,CAAC,CAAC;EACvE,CAAC;EAED,MAAM6D,gBAAgB,GAAG,eAAAA,CAAe,GAAG7D,IAA2B;IAClE,MAAMI,MAAM,GAAGC,SAAS,CAACN,QAAQ,CAACK,MAAM,EAAE,MAAM,CAAC;IACjD9B,MAAM,CAACS,OAAO,CAACqB,MAAM,CAAC,EAAE,0CAA0C,EAC9D,uBAAuB,EAAE;MAAE0C,SAAS,EAAE;IAAM,CAAE,CAAC;IAEnD,MAAMT,EAAE,GAAG,MAAMD,mBAAmB,CAAC,GAAGpC,IAAI,CAAC;IAE7C,IAAI4D,MAAM,GAAG,IAAI;IACjB,IAAI;MACAA,MAAM,GAAG,MAAMxD,MAAM,CAACnB,IAAI,CAACoD,EAAE,CAAC;KACjC,CAAC,OAAOU,KAAU,EAAE;MACjB,IAAI9E,eAAe,CAAC8E,KAAK,CAAC,IAAIA,KAAK,CAAChB,IAAI,EAAE;QACtC,MAAMhC,QAAQ,CAACoB,SAAS,CAAC9C,SAAS,CAAC0E,KAAK,CAAChB,IAAI,EAAEM,EAAE,CAAC;;MAEtD,MAAMU,KAAK;;IAGf,MAAMlD,QAAQ,GAAGwD,WAAW,CAAC,GAAGrD,IAAI,CAAC;IACrC,OAAOD,QAAQ,CAACoB,SAAS,CAAC2C,oBAAoB,CAACjE,QAAQ,EAAE+D,MAAM,CAAC;EACpE,CAAC;EAED,MAAMX,MAAM,GAAG,MAAAA,CAAO,GAAGjD,IAA2B,KAAI;IACpD,MAAMH,QAAQ,GAAGwD,WAAW,CAAC,GAAGrD,IAAI,CAAC;IACrC,IAAIH,QAAQ,CAACkE,QAAQ,EAAE;MAAE,OAAO,MAAMlB,UAAU,CAAC,GAAG7C,IAAI,CAAC;;IACzD,OAAO,MAAMgD,IAAI,CAAC,GAAGhD,IAAI,CAAC;EAC9B,CAAC;EAEDjC,gBAAgB,CAAMkF,MAAM,EAAE;IAC1Be,IAAI,EAAEjE,QAAQ,CAACoB,SAAS,CAAC8C,eAAe,CAACb,GAAG,CAAC;IAC7CF,SAAS,EAAEnD,QAAQ;IAAEmE,IAAI,EAAEd,GAAG;IAE9BC,WAAW;IAEXlE,WAAW;IACXiD,mBAAmB;IACnBY,IAAI;IAAEH,UAAU;IAAEgB;GACrB,CAAC;EAEF;EACAJ,MAAM,CAACU,cAAc,CAAClB,MAAM,EAAE,UAAU,EAAE;IACtCmB,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAEA,CAAA,KAAK;MACN,MAAMzE,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACmC,WAAW,CAACF,GAAG,CAAC;MACpD9E,MAAM,CAACuB,QAAQ,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;QAC9DiD,SAAS,EAAE,UAAU;QACrBS,IAAI,EAAE;UAAEH;QAAG;OACd,CAAC;MACF,OAAOvD,QAAQ;IACnB;GACH,CAAC;EAEF,OAAoCoD,MAAM;AAC9C;AAEA,SAASsB,iBAAiBA,CAAoCxE,QAAsB,EAAEqD,GAAW;EAE7F,MAAMC,WAAW,GAAG,SAAAA,CAAS,GAAGrD,IAA0B;IACtD,MAAMH,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACqD,QAAQ,CAACpB,GAAG,EAAEpD,IAAI,CAAC;IAEvD1B,MAAM,CAACuB,QAAQ,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;MAC9DiD,SAAS,EAAE,UAAU;MACrBS,IAAI,EAAE;QAAEH,GAAG;QAAEpD;MAAI;KACpB,CAAC;IAEF,OAAOH,QAAQ;EACnB,CAAC;EAED,MAAMoD,MAAM,GAAG,SAAAA,CAAS,GAAGjD,IAA2B;IAClD,OAAO,IAAIL,mBAAmB,CAACI,QAAQ,EAAEsD,WAAW,CAAC,GAAGrD,IAAI,CAAC,EAAEA,IAAI,CAAC;EACxE,CAAC;EAEDjC,gBAAgB,CAAMkF,MAAM,EAAE;IAC1Be,IAAI,EAAEjE,QAAQ,CAACoB,SAAS,CAACsD,YAAY,CAACrB,GAAG,CAAC;IAC1CF,SAAS,EAAEnD,QAAQ;IAAEmE,IAAI,EAAEd,GAAG;IAE9BC;GACH,CAAC;EAEF;EACAI,MAAM,CAACU,cAAc,CAAClB,MAAM,EAAE,UAAU,EAAE;IACtCmB,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE,IAAI;IAChBC,GAAG,EAAEA,CAAA,KAAK;MACN,MAAMzE,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACqD,QAAQ,CAACpB,GAAG,CAAC;MAEjD9E,MAAM,CAACuB,QAAQ,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;QAC9DiD,SAAS,EAAE,UAAU;QACrBS,IAAI,EAAE;UAAEH;QAAG;OACd,CAAC;MAEF,OAAOvD,QAAQ;IACnB;GACH,CAAC;EAEF,OAAkCoD,MAAM;AAC5C;AAUA;AACA;AACA;AACA;AAEA,MAAMyB,QAAQ,GAAGC,MAAM,CAACC,GAAG,CAAC,0BAA0B,CAAC;AAUvD,MAAMC,cAAc,GAAoC,IAAIC,OAAO,EAAE;AAErE,SAASC,WAAWA,CAAChF,QAAsB,EAAEiF,MAAgB;EACzDH,cAAc,CAACI,GAAG,CAAClF,QAAQ,CAAC2E,QAAQ,CAAC,EAAEM,MAAM,CAAC;AAClD;AAEA,SAASE,WAAWA,CAACnF,QAAsB;EACvC,OAAO8E,cAAc,CAACP,GAAG,CAACvE,QAAQ,CAAC2E,QAAQ,CAAC,CAAa;AAC7D;AAEA,SAASS,UAAUA,CAACnG,KAAU;EAC1B,OAAQA,KAAK,IAAI,OAAOA,KAAM,KAAK,QAAQ,IAAK,gBAAgB,IAAIA,KAAM,IACvE,OAAOA,KAAK,CAACqC,cAAe,KAAK,UAAW,IAAIrC,KAAK,CAACa,QAAQ;AACrE;AAEA,eAAeuF,UAAUA,CAACrF,QAAsB,EAAEsF,KAAwB;EACtE,IAAIC,MAA4C;EAChD,IAAIzF,QAAQ,GAAyB,IAAI;EAEzC;EACA;EAEA,IAAImB,KAAK,CAACC,OAAO,CAACoE,KAAK,CAAC,EAAE;IACtB,MAAME,YAAY,GAAG,SAAAA,CAASvB,IAAY;MACtC,IAAI9F,WAAW,CAAC8F,IAAI,EAAE,EAAE,CAAC,EAAE;QAAE,OAAOA,IAAI;;MACxC,MAAMnE,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACqD,QAAQ,CAACR,IAAI,CAAC;MAClDzF,cAAc,CAACsB,QAAQ,EAAE,kBAAkB,EAAE,MAAM,EAAEmE,IAAI,CAAC;MAC1D,OAAOnE,QAAQ,CAAC2F,SAAS;IAC7B,CAAC;IAED;IACAF,MAAM,GAAGD,KAAK,CAAC3E,GAAG,CAAE+E,CAAC,IAAI;MACrB,IAAIA,CAAC,IAAI,IAAI,EAAE;QAAE,OAAO,IAAI;;MAC5B,IAAIzE,KAAK,CAACC,OAAO,CAACwE,CAAC,CAAC,EAAE;QAAE,OAAOA,CAAC,CAAC/E,GAAG,CAAC6E,YAAY,CAAC;;MAClD,OAAOA,YAAY,CAACE,CAAC,CAAC;IAC1B,CAAC,CAAC;GAEL,MAAM,IAAIJ,KAAK,KAAK,GAAG,EAAE;IACtBC,MAAM,GAAG,CAAE,IAAI,CAAE;GAEpB,MAAM,IAAI,OAAOD,KAAM,KAAK,QAAQ,EAAE;IACnC,IAAInH,WAAW,CAACmH,KAAK,EAAE,EAAE,CAAC,EAAE;MACxB;MACAC,MAAM,GAAG,CAAED,KAAK,CAAE;KACrB,MAAM;MACJ;MACCxF,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACqD,QAAQ,CAACa,KAAK,CAAC;MAC7C9G,cAAc,CAACsB,QAAQ,EAAE,kBAAkB,EAAE,OAAO,EAAEwF,KAAK,CAAC;MAC5DC,MAAM,GAAG,CAAEzF,QAAQ,CAAC2F,SAAS,CAAE;;GAGtC,MAAM,IAAIL,UAAU,CAACE,KAAK,CAAC,EAAE;IAC1B;IACAC,MAAM,GAAG,MAAMD,KAAK,CAAChE,cAAc,EAAE;GAExC,MAAM,IAAI,UAAU,IAAIgE,KAAK,EAAE;IAC5B;IACAxF,QAAQ,GAAGwF,KAAK,CAACxF,QAAQ;IACzByF,MAAM,GAAG,CAAEzF,QAAQ,CAAC2F,SAAS,CAAE;GAElC,MAAM;IACHjH,cAAc,CAAC,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE8G,KAAK,CAAC;;EAG/D;EACAC,MAAM,GAAGA,MAAM,CAAC5E,GAAG,CAAEgF,CAAC,IAAI;IACtB,IAAIA,CAAC,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAC5B,IAAI1E,KAAK,CAACC,OAAO,CAACyE,CAAC,CAAC,EAAE;MAClB,MAAMC,KAAK,GAAG3E,KAAK,CAACgB,IAAI,CAAC,IAAI4D,GAAG,CAACF,CAAC,CAAChF,GAAG,CAAEgF,CAAC,IAAKA,CAAC,CAACG,WAAW,EAAE,CAAC,CAAC,CAACb,MAAM,EAAE,CAAC;MACzE,IAAIW,KAAK,CAACzF,MAAM,KAAK,CAAC,EAAE;QAAE,OAAOyF,KAAK,CAAC,CAAC,CAAC;;MACzCA,KAAK,CAACG,IAAI,EAAE;MACZ,OAAOH,KAAK;;IAEhB,OAAOD,CAAC,CAACG,WAAW,EAAE;EAC1B,CAAC,CAAC;EAEF,MAAME,GAAG,GAAGT,MAAM,CAAC5E,GAAG,CAAEgF,CAAC,IAAI;IACzB,IAAIA,CAAC,IAAI,IAAI,EAAE;MAAE,OAAO,MAAM;;IAC9B,IAAI1E,KAAK,CAACC,OAAO,CAACyE,CAAC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;;IAC1C,OAAON,CAAC;EACZ,CAAC,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;EAEZ,OAAO;IAAEnG,QAAQ;IAAEkG,GAAG;IAAET;EAAM,CAAE;AACpC;AAEA,eAAeW,MAAMA,CAAClG,QAAsB,EAAEsF,KAAwB;EAClE,MAAM;IAAEa;EAAI,CAAE,GAAGhB,WAAW,CAACnF,QAAQ,CAAC;EACtC,OAAOmG,IAAI,CAAC5B,GAAG,CAAC,CAAC,MAAMc,UAAU,CAACrF,QAAQ,EAAEsF,KAAK,CAAC,EAAEU,GAAG,CAAC,IAAI,IAAI;AACpE;AAEA,eAAeI,MAAMA,CAACpG,QAAsB,EAAE+C,SAAiB,EAAEuC,KAAwB;EACrF;EACA,MAAM5F,QAAQ,GAAG8B,WAAW,CAACxB,QAAQ,CAACK,MAAM,CAAC;EAC7C9B,MAAM,CAACmB,QAAQ,EAAE,8CAA8C,EAC3D,uBAAuB,EAAE;IAAEqD;EAAS,CAAE,CAAC;EAE3C,MAAM;IAAEjD,QAAQ;IAAEkG,GAAG;IAAET;EAAM,CAAE,GAAG,MAAMF,UAAU,CAACrF,QAAQ,EAAEsF,KAAK,CAAC;EAEnE,MAAM;IAAEe,IAAI;IAAEF;EAAI,CAAE,GAAGhB,WAAW,CAACnF,QAAQ,CAAC;EAE5C,IAAIsG,GAAG,GAAGH,IAAI,CAAC5B,GAAG,CAACyB,GAAG,CAAC;EACvB,IAAI,CAACM,GAAG,EAAE;IACN,MAAMC,OAAO,GAA0BF,IAAI,GAAGA,IAAI,GAAErG,QAAS;IAC7D,MAAMH,MAAM,GAAG;MAAE0G,OAAO;MAAEhB;IAAM,CAAE;IAClC,MAAMiB,QAAQ,GAAIC,GAAQ,IAAI;MAC1B,IAAIC,aAAa,GAAG5G,QAAQ;MAC5B,IAAI4G,aAAa,IAAI,IAAI,EAAE;QACvB,IAAI;UACAA,aAAa,GAAG1G,QAAQ,CAACoB,SAAS,CAACqD,QAAQ,CAACgC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;SAC7D,CAAC,OAAOvC,KAAK,EAAE;;MAGpB;MAEA,IAAI0D,aAAa,EAAE;QACf,MAAMC,cAAc,GAAGD,aAAa;QACpC,MAAMzG,IAAI,GAAGH,QAAQ,GAAGE,QAAQ,CAACoB,SAAS,CAACwF,cAAc,CAAC9G,QAAQ,EAAE2G,GAAG,CAACzE,IAAI,EAAEyE,GAAG,CAAClB,MAAM,CAAC,GAAE,EAAG;QAC9FsB,IAAI,CAAC7G,QAAQ,EAAEsF,KAAK,EAAErF,IAAI,EAAGuG,QAAyB,IAAI;UACtD,OAAO,IAAI/H,oBAAoB,CAACuB,QAAQ,EAAEwG,QAAQ,EAAElB,KAAK,EAAEqB,cAAc,EAAEF,GAAG,CAAC;QACnF,CAAC,CAAC;OACL,MAAM;QACHI,IAAI,CAAC7G,QAAQ,EAAEsF,KAAK,EAAE,EAAG,EAAGkB,QAAyB,IAAI;UACrD,OAAO,IAAI9H,2BAA2B,CAACsB,QAAQ,EAAEwG,QAAQ,EAAElB,KAAK,EAAEmB,GAAG,CAAC;QAC1E,CAAC,CAAC;;IAEV,CAAC;IAED,IAAIK,QAAQ,GAAwB,EAAG;IACvC,MAAMC,KAAK,GAAGA,CAAA,KAAK;MACf,IAAID,QAAQ,CAAC3G,MAAM,EAAE;QAAE;;MACvB2G,QAAQ,CAACE,IAAI,CAACtH,QAAQ,CAACuH,EAAE,CAACpH,MAAM,EAAE2G,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED,MAAMU,IAAI,GAAG,MAAAA,CAAA,KAAW;MACpB,IAAIJ,QAAQ,CAAC3G,MAAM,IAAI,CAAC,EAAE;QAAE;;MAE5B,IAAIgH,OAAO,GAAGL,QAAQ;MACtBA,QAAQ,GAAG,EAAG;MACd,MAAMrG,OAAO,CAACC,GAAG,CAACyG,OAAO,CAAC;MAC1BzH,QAAQ,CAAC0H,GAAG,CAACvH,MAAM,EAAE2G,QAAQ,CAAC;IAClC,CAAC;IAEDF,GAAG,GAAG;MAAEN,GAAG;MAAEqB,SAAS,EAAE,EAAG;MAAEN,KAAK;MAAEG;IAAI,CAAE;IAC1Cf,IAAI,CAACjB,GAAG,CAACc,GAAG,EAAEM,GAAG,CAAC;;EAEtB,OAAOA,GAAG;AACd;AAEA;AACA;AACA;AACA,IAAIgB,QAAQ,GAAiB7G,OAAO,CAAC8G,OAAO,EAAE;AAI9C,eAAeC,KAAKA,CAACxH,QAAsB,EAAEsF,KAAwB,EAAErF,IAAgB,EAAEwH,WAA+B;EACpH,MAAMH,QAAQ;EAEd,MAAMhB,GAAG,GAAG,MAAMJ,MAAM,CAAClG,QAAQ,EAAEsF,KAAK,CAAC;EACzC,IAAI,CAACgB,GAAG,EAAE;IAAE,OAAO,KAAK;;EAExB,MAAMoB,KAAK,GAAGpB,GAAG,CAACe,SAAS,CAAClH,MAAM;EAClCmG,GAAG,CAACe,SAAS,GAAGf,GAAG,CAACe,SAAS,CAACxH,MAAM,CAAC,CAAC;IAAE2G,QAAQ;IAAEmB;EAAI,CAAE,KAAI;IACxD,MAAMC,QAAQ,GAAG3G,KAAK,CAACgB,IAAI,CAAChC,IAAI,CAAC;IACjC,IAAIwH,WAAW,EAAE;MACbG,QAAQ,CAACZ,IAAI,CAACS,WAAW,CAACE,IAAI,GAAG,IAAI,GAAEnB,QAAQ,CAAC,CAAC;;IAErD,IAAI;MACAA,QAAQ,CAACtH,IAAI,CAACc,QAAQ,EAAE,GAAG4H,QAAQ,CAAC;KACvC,CAAC,OAAO5E,KAAK,EAAE;IAChB,OAAO,CAAC2E,IAAI;EAChB,CAAC,CAAC;EAEF,IAAIrB,GAAG,CAACe,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;IAC5BmG,GAAG,CAACY,IAAI,EAAE;IACV/B,WAAW,CAACnF,QAAQ,CAAC,CAACmG,IAAI,CAAC0B,MAAM,CAACvB,GAAG,CAACN,GAAG,CAAC;;EAG9C,OAAQ0B,KAAK,GAAG,CAAC;AACrB;AAEA,eAAeb,IAAIA,CAAC7G,QAAsB,EAAEsF,KAAwB,EAAErF,IAAgB,EAAEwH,WAA+B;EACnH,IAAI;IACA,MAAMH,QAAQ;GACjB,CAAC,OAAOtE,KAAK,EAAE;EAEhB,MAAM8E,aAAa,GAAGN,KAAK,CAACxH,QAAQ,EAAEsF,KAAK,EAAErF,IAAI,EAAEwH,WAAW,CAAC;EAC/DH,QAAQ,GAAGQ,aAAa;EACxB,OAAO,MAAMA,aAAa;AAC9B;AAEA,MAAMC,cAAc,GAAG,CAAE,MAAM,CAAE;AACjC,OAAM,MAAOC,YAAY;EACrB;;;;;;;EAOSC,MAAM;EAEf;;;EAGS7G,SAAS;EAElB;;;;;;;EAOSf,MAAM;EAEf;;;EAGS6H,OAAO;EAEhB;;;EAGS,CAACvD,QAAQ;EAElB;;;EAGShC,QAAQ;EAEjB;;;;;EAKA5C,YAAYkI,MAA4B,EAAEE,GAA6B,EAAE9H,MAA8B,EAAE+H,SAAsC;IAC3I5J,cAAc,CAAC,OAAOyJ,MAAO,KAAK,QAAQ,IAAIrK,aAAa,CAACqK,MAAM,CAAC,EAC/D,mCAAmC,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAE1D,IAAI5H,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,IAAI;;IACnC,MAAMmC,KAAK,GAAG9E,SAAS,CAACuE,IAAI,CAACkG,GAAG,CAAC;IACjCnK,gBAAgB,CAAe,IAAI,EAAE;MAAEiK,MAAM;MAAE5H,MAAM;MAAEe,SAAS,EAAEoB;IAAK,CAAE,CAAC;IAE1EkB,MAAM,CAACU,cAAc,CAAC,IAAI,EAAEO,QAAQ,EAAE;MAAE1F,KAAK,EAAE;IAAG,CAAE,CAAC;IAErD,IAAIoJ,WAAW;IACf,IAAIhC,IAAI,GAAkB,IAAI;IAE9B,IAAIiC,QAAQ,GAAuC,IAAI;IACvD,IAAIF,SAAS,EAAE;MACX,MAAM1I,QAAQ,GAAG8B,WAAW,CAACnB,MAAM,CAAC;MACpC;MACA;MACAiI,QAAQ,GAAG,IAAI3J,2BAA2B,CAAC,IAAI,CAACyC,SAAS,EAAY1B,QAAQ,EAAE0I,SAAS,CAAC;;IAG7F,IAAIjC,IAAI,GAAG,IAAIoC,GAAG,EAAE;IAEpB;IACA,IAAI,OAAON,MAAO,KAAK,QAAQ,EAAE;MAC7B,IAAI9J,WAAW,CAAC8J,MAAM,CAAC,EAAE;QACrB5B,IAAI,GAAG4B,MAAM;QACbI,WAAW,GAAG5H,OAAO,CAAC8G,OAAO,CAACU,MAAM,CAAC;OAExC,MAAM;QACH,MAAM1H,QAAQ,GAAGD,SAAS,CAACD,MAAM,EAAE,aAAa,CAAC;QACjD,IAAI,CAAChB,UAAU,CAACkB,QAAQ,CAAC,EAAE;UACvB,MAAMjC,SAAS,CAAC,kDAAkD,EAAE,uBAAuB,EAAE;YACzFyE,SAAS,EAAE;WACd,CAAC;;QAGNsF,WAAW,GAAG9H,QAAQ,CAACjB,WAAW,CAAC2I,MAAM,CAAC,CAACO,IAAI,CAAEnC,IAAI,IAAI;UACrD,IAAIA,IAAI,IAAI,IAAI,EAAE;YACd,MAAM/H,SAAS,CAAC,qEAAqE,EAAE,mBAAmB,EAAE;cACxGW,KAAK,EAAEgJ;aACV,CAAC;;UAEN9C,WAAW,CAAC,IAAI,CAAC,CAACkB,IAAI,GAAGA,IAAI;UAC7B,OAAOA,IAAI;QACf,CAAC,CAAC;;KAET,MAAM;MACHgC,WAAW,GAAGJ,MAAM,CAAC1F,UAAU,EAAE,CAACiG,IAAI,CAAEnC,IAAI,IAAI;QAC5C,IAAIA,IAAI,IAAI,IAAI,EAAE;UAAE,MAAM,IAAIjG,KAAK,CAAC,MAAM,CAAC;;QAC3C+E,WAAW,CAAC,IAAI,CAAC,CAACkB,IAAI,GAAGA,IAAI;QAC7B,OAAOA,IAAI;MACf,CAAC,CAAC;;IAGN;IACArB,WAAW,CAAC,IAAI,EAAE;MAAEqD,WAAW;MAAEhC,IAAI;MAAEiC,QAAQ;MAAEnC;IAAI,CAAE,CAAC;IAExD;IACA,MAAM+B,OAAO,GAAG,IAAIO,KAAK,CAAC,EAAG,EAAE;MAC3BlE,GAAG,EAAEA,CAAC0D,MAAM,EAAES,IAAI,EAAEC,QAAQ,KAAI;QAC5B;QACA,IAAI,OAAOD,IAAK,KAAK,QAAQ,IAAIX,cAAc,CAAChG,OAAO,CAAC2G,IAAI,CAAC,IAAI,CAAC,EAAE;UAChE,OAAOE,OAAO,CAACrE,GAAG,CAAC0D,MAAM,EAAES,IAAI,EAAEC,QAAQ,CAAC;;QAG9C,IAAI;UACA,OAAO,IAAI,CAAClE,QAAQ,CAACiE,IAAI,CAAC;SAC7B,CAAC,OAAO1F,KAAK,EAAE;UACZ,IAAI,CAAC3E,OAAO,CAAC2E,KAAK,EAAE,kBAAkB,CAAC,IAAIA,KAAK,CAAC6F,QAAQ,KAAK,KAAK,EAAE;YACjE,MAAM7F,KAAK;;;QAInB,OAAOrD,SAAS;MACpB,CAAC;MACDmJ,GAAG,EAAEA,CAACb,MAAM,EAAES,IAAI,KAAI;QAClB;QACA,IAAIX,cAAc,CAAChG,OAAO,CAAS2G,IAAI,CAAC,IAAI,CAAC,EAAE;UAC3C,OAAOE,OAAO,CAACE,GAAG,CAACb,MAAM,EAAES,IAAI,CAAC;;QAGpC,OAAOE,OAAO,CAACE,GAAG,CAACb,MAAM,EAAES,IAAI,CAAC,IAAI,IAAI,CAACtH,SAAS,CAAC2H,QAAQ,CAACC,MAAM,CAACN,IAAI,CAAC,CAAC;MAC7E;KACH,CAAC;IACF1K,gBAAgB,CAAe,IAAI,EAAE;MAAEkK;IAAO,CAAE,CAAC;IAEjDlK,gBAAgB,CAAe,IAAI,EAAE;MACjC2E,QAAQ,EAAIH,KAAK,CAACK,OAAO,IAAIL,KAAK,CAACG,QAAQ,GAAKP,oBAAoB,CAAC,IAAI,CAAC,GAAG;KAChF,CAAC;IAEF;IACA,OAAO,IAAIqG,KAAK,CAAC,IAAI,EAAE;MACnBlE,GAAG,EAAEA,CAAC0D,MAAM,EAAES,IAAI,EAAEC,QAAQ,KAAI;QAC5B,IAAI,OAAOD,IAAK,KAAK,QAAQ,IAAIA,IAAI,IAAIT,MAAM,IAAIF,cAAc,CAAChG,OAAO,CAAC2G,IAAI,CAAC,IAAI,CAAC,EAAE;UAClF,OAAOE,OAAO,CAACrE,GAAG,CAAC0D,MAAM,EAAES,IAAI,EAAEC,QAAQ,CAAC;;QAG9C;QACA,IAAI;UACA,OAAOV,MAAM,CAAC1E,WAAW,CAACmF,IAAI,CAAC;SAClC,CAAC,OAAO1F,KAAK,EAAE;UACZ,IAAI,CAAC3E,OAAO,CAAC2E,KAAK,EAAE,kBAAkB,CAAC,IAAIA,KAAK,CAAC6F,QAAQ,KAAK,KAAK,EAAE;YACjE,MAAM7F,KAAK;;;QAInB,OAAOrD,SAAS;MACpB,CAAC;MACDmJ,GAAG,EAAEA,CAACb,MAAM,EAAES,IAAI,KAAI;QAClB,IAAI,OAAOA,IAAK,KAAK,QAAQ,IAAIA,IAAI,IAAIT,MAAM,IAAIF,cAAc,CAAChG,OAAO,CAAC2G,IAAI,CAAC,IAAI,CAAC,EAAE;UAClF,OAAOE,OAAO,CAACE,GAAG,CAACb,MAAM,EAAES,IAAI,CAAC;;QAGpC,OAAOT,MAAM,CAAC7G,SAAS,CAAC6H,WAAW,CAACP,IAAI,CAAC;MAC7C;KACH,CAAC;EAEN;EAEA;;;;EAIAQ,OAAOA,CAAC7I,MAA6B;IACjC,OAAO,IAAI2H,YAAY,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAAC7G,SAAS,EAAEf,MAAM,CAAC;EAChE;EAEA;;;;EAIA8I,MAAMA,CAAClB,MAA4B;IAC/B,OAAO,IAAID,YAAY,CAACC,MAAM,EAAE,IAAI,CAAC7G,SAAS,EAAE,IAAI,CAACf,MAAM,CAAC;EAChE;EAEA;;;EAGA,MAAMkC,UAAUA,CAAA;IAAsB,OAAO,MAAM4C,WAAW,CAAC,IAAI,CAAC,CAACkD,WAAW;EAAE;EAElF;;;EAGA,MAAMe,eAAeA,CAAA;IACjB,MAAM1J,QAAQ,GAAG8B,WAAW,CAAC,IAAI,CAACnB,MAAM,CAAC;IACzC9B,MAAM,CAACmB,QAAQ,EAAE,mCAAmC,EAChD,uBAAuB,EAAE;MAAEqD,SAAS,EAAE;IAAiB,CAAE,CAAC;IAE9D,MAAMsG,IAAI,GAAG,MAAM3J,QAAQ,CAAC4J,OAAO,CAAC,MAAM,IAAI,CAAC/G,UAAU,EAAE,CAAC;IAC5D,IAAI8G,IAAI,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;;IAChC,OAAOA,IAAI;EACf;EAEA;;;;EAIA,MAAME,iBAAiBA,CAAA;IACnB;IACA,MAAMjB,QAAQ,GAAG,IAAI,CAACkB,qBAAqB,EAAE;IAC7C,IAAIlB,QAAQ,EAAE;MACV,MAAMA,QAAQ,CAACmB,IAAI,EAAE;MACrB,OAAO,IAAI;;IAGf;IACA,MAAMJ,IAAI,GAAG,MAAM,IAAI,CAACD,eAAe,EAAE;IACzC,IAAIC,IAAI,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAE/B;IACA,MAAM3J,QAAQ,GAAG8B,WAAW,CAAC,IAAI,CAACnB,MAAM,CAAC;IACzC9B,MAAM,CAACmB,QAAQ,IAAI,IAAI,EAAE,4CAA4C,EACjE,uBAAuB,EAAE;MAAEqD,SAAS,EAAE;IAAmB,CAAE,CAAC;IAEhE,OAAO,IAAItC,OAAO,CAAC,CAAC8G,OAAO,EAAEmC,MAAM,KAAI;MACnC,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAW;QACzB,IAAI;UACA,MAAMN,IAAI,GAAG,MAAM,IAAI,CAACD,eAAe,EAAE;UACzC,IAAIC,IAAI,IAAI,IAAI,EAAE;YAAE,OAAO9B,OAAO,CAAC,IAAI,CAAC;;UACxC7H,QAAQ,CAACiI,IAAI,CAAC,OAAO,EAAEgC,SAAS,CAAC;SACpC,CAAC,OAAO3G,KAAK,EAAE;UACZ0G,MAAM,CAAC1G,KAAK,CAAC;;MAErB,CAAC;MACD2G,SAAS,EAAE;IACf,CAAC,CAAC;EACN;EAEA;;;;;;EAMAH,qBAAqBA,CAAA;IACjB,OAAOrE,WAAW,CAAC,IAAI,CAAC,CAACmD,QAAQ;EACrC;EAEA;;;;;EAKA/E,WAAWA,CAA4CF,GAA8B;IACjF,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAAEA,GAAG,GAAGA,GAAG,CAACuG,MAAM,EAAE;;IAClD,MAAMC,IAAI,GAAGzG,kBAAkB,CAAC,IAAI,EAAEC,GAAG,CAAC;IAC1C,OAAUwG,IAAI;EAClB;EAEA;;;;;EAKApF,QAAQA,CAACpB,GAA2B;IAChC,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAAEA,GAAG,GAAGA,GAAG,CAACuG,MAAM,EAAE;;IAClD,OAAOpF,iBAAiB,CAAC,IAAI,EAAEnB,GAAG,CAAC;EACvC;EAEA;;;EAGA,MAAMyG,gBAAgBA,CAACC,IAAY;IAC/B,MAAM,IAAI3J,KAAK,CAAC,OAAO,CAAC;EAC5B;EAEA;;;;;;;;;;;;;EAgBA;;;;;EAKA,MAAM4J,WAAWA,CAAC1E,KAAwB,EAAE2E,SAAoB,EAAEC,OAAkB;IAChF,IAAID,SAAS,IAAI,IAAI,EAAE;MAAEA,SAAS,GAAG,CAAC;;IACtC,IAAIC,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,QAAQ;;IACzC,MAAM;MAAE7D,IAAI;MAAEgC;IAAW,CAAE,GAAGlD,WAAW,CAAC,IAAI,CAAC;IAC/C,MAAMoB,OAAO,GAAIF,IAAI,GAAGA,IAAI,GAAG,MAAMgC,WAAa;IAClD,MAAM;MAAEvI,QAAQ;MAAEyF;IAAM,CAAE,GAAG,MAAMF,UAAU,CAAC,IAAI,EAAEC,KAAK,CAAC;IAC1D,MAAMzF,MAAM,GAAG;MAAE0G,OAAO;MAAEhB,MAAM;MAAE0E,SAAS;MAAEC;IAAO,CAAE;IAEtD,MAAMxK,QAAQ,GAAG8B,WAAW,CAAC,IAAI,CAACnB,MAAM,CAAC;IACzC9B,MAAM,CAACmB,QAAQ,EAAE,0CAA0C,EACvD,uBAAuB,EAAE;MAAEqD,SAAS,EAAE;IAAa,CAAE,CAAC;IAE1D,OAAO,CAAC,MAAMrD,QAAQ,CAACyK,OAAO,CAACtK,MAAM,CAAC,EAAEc,GAAG,CAAE8F,GAAG,IAAI;MAChD,IAAIC,aAAa,GAAG5G,QAAQ;MAC5B,IAAI4G,aAAa,IAAI,IAAI,EAAE;QACvB,IAAI;UACAA,aAAa,GAAG,IAAI,CAACtF,SAAS,CAACqD,QAAQ,CAACgC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAAC,CAAC;SACzD,CAAC,OAAOvC,KAAK,EAAE;;MAGpB,IAAI0D,aAAa,EAAE;QACf,IAAI;UACA,OAAO,IAAI9H,QAAQ,CAAC6H,GAAG,EAAE,IAAI,CAACrF,SAAS,EAAEsF,aAAa,CAAC;SAC1D,CAAC,OAAO1D,KAAU,EAAE;UACjB,OAAO,IAAInE,iBAAiB,CAAC4H,GAAG,EAAEzD,KAAK,CAAC;;;MAIhD,OAAO,IAAIjF,GAAG,CAAC0I,GAAG,EAAE/G,QAAQ,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;;;EAGA,MAAMuH,EAAEA,CAAC3B,KAAwB,EAAEkB,QAAkB;IACjD,MAAMF,GAAG,GAAG,MAAMF,MAAM,CAAC,IAAI,EAAE,IAAI,EAAEd,KAAK,CAAC;IAC3CgB,GAAG,CAACe,SAAS,CAACL,IAAI,CAAC;MAAER,QAAQ;MAAEmB,IAAI,EAAE;IAAK,CAAE,CAAC;IAC7CrB,GAAG,CAACS,KAAK,EAAE;IACX,OAAO,IAAI;EACf;EAEA;;;;EAIA,MAAMY,IAAIA,CAACrC,KAAwB,EAAEkB,QAAkB;IACnD,MAAMF,GAAG,GAAG,MAAMF,MAAM,CAAC,IAAI,EAAE,MAAM,EAAEd,KAAK,CAAC;IAC7CgB,GAAG,CAACe,SAAS,CAACL,IAAI,CAAC;MAAER,QAAQ;MAAEmB,IAAI,EAAE;IAAI,CAAE,CAAC;IAC5CrB,GAAG,CAACS,KAAK,EAAE;IACX,OAAO,IAAI;EACf;EAEA;;;;;EAKA,MAAMF,IAAIA,CAACvB,KAAwB,EAAE,GAAGrF,IAAgB;IACpD,OAAO,MAAM4G,IAAI,CAAC,IAAI,EAAEvB,KAAK,EAAErF,IAAI,EAAE,IAAI,CAAC;EAC9C;EAEA;;;;EAIA,MAAMmK,aAAaA,CAAC9E,KAAyB;IACzC,IAAIA,KAAK,EAAE;MACP,MAAMgB,GAAG,GAAG,MAAMJ,MAAM,CAAC,IAAI,EAAEZ,KAAK,CAAC;MACrC,IAAI,CAACgB,GAAG,EAAE;QAAE,OAAO,CAAC;;MACpB,OAAOA,GAAG,CAACe,SAAS,CAAClH,MAAM;;IAG/B,MAAM;MAAEgG;IAAI,CAAE,GAAGhB,WAAW,CAAC,IAAI,CAAC;IAElC,IAAIkF,KAAK,GAAG,CAAC;IACb,KAAK,MAAM;MAAEhD;IAAS,CAAE,IAAIlB,IAAI,CAAClB,MAAM,EAAE,EAAE;MACvCoF,KAAK,IAAIhD,SAAS,CAAClH,MAAM;;IAE7B,OAAOkK,KAAK;EAChB;EAEA;;;;EAIA,MAAMhD,SAASA,CAAC/B,KAAyB;IACrC,IAAIA,KAAK,EAAE;MACP,MAAMgB,GAAG,GAAG,MAAMJ,MAAM,CAAC,IAAI,EAAEZ,KAAK,CAAC;MACrC,IAAI,CAACgB,GAAG,EAAE;QAAE,OAAO,EAAG;;MACtB,OAAOA,GAAG,CAACe,SAAS,CAAC1G,GAAG,CAAC,CAAC;QAAE6F;MAAQ,CAAE,KAAKA,QAAQ,CAAC;;IAGxD,MAAM;MAAEL;IAAI,CAAE,GAAGhB,WAAW,CAAC,IAAI,CAAC;IAElC,IAAItB,MAAM,GAAoB,EAAG;IACjC,KAAK,MAAM;MAAEwD;IAAS,CAAE,IAAIlB,IAAI,CAAClB,MAAM,EAAE,EAAE;MACvCpB,MAAM,GAAGA,MAAM,CAACyG,MAAM,CAACjD,SAAS,CAAC1G,GAAG,CAAC,CAAC;QAAE6F;MAAQ,CAAE,KAAKA,QAAQ,CAAC,CAAC;;IAErE,OAAO3C,MAAM;EACjB;EAEA;;;;EAIA,MAAMuD,GAAGA,CAAC9B,KAAwB,EAAEkB,QAAmB;IACnD,MAAMF,GAAG,GAAG,MAAMJ,MAAM,CAAC,IAAI,EAAEZ,KAAK,CAAC;IACrC,IAAI,CAACgB,GAAG,EAAE;MAAE,OAAO,IAAI;;IAEvB,IAAIE,QAAQ,EAAE;MACV,MAAM3F,KAAK,GAAGyF,GAAG,CAACe,SAAS,CAAC1G,GAAG,CAAC,CAAC;QAAE6F;MAAQ,CAAE,KAAKA,QAAQ,CAAC,CAACzE,OAAO,CAACyE,QAAQ,CAAC;MAC7E,IAAI3F,KAAK,IAAI,CAAC,EAAE;QAAEyF,GAAG,CAACe,SAAS,CAACkD,MAAM,CAAC1J,KAAK,EAAE,CAAC,CAAC;;;IAGpD,IAAI2F,QAAQ,IAAI,IAAI,IAAIF,GAAG,CAACe,SAAS,CAAClH,MAAM,KAAK,CAAC,EAAE;MAChDmG,GAAG,CAACY,IAAI,EAAE;MACV/B,WAAW,CAAC,IAAI,CAAC,CAACgB,IAAI,CAAC0B,MAAM,CAACvB,GAAG,CAACN,GAAG,CAAC;;IAG1C,OAAO,IAAI;EACf;EAEA;;;;EAIA,MAAMwE,kBAAkBA,CAAClF,KAAyB;IAC9C,IAAIA,KAAK,EAAE;MACP,MAAMgB,GAAG,GAAG,MAAMJ,MAAM,CAAC,IAAI,EAAEZ,KAAK,CAAC;MACrC,IAAI,CAACgB,GAAG,EAAE;QAAE,OAAO,IAAI;;MACvBA,GAAG,CAACY,IAAI,EAAE;MACV/B,WAAW,CAAC,IAAI,CAAC,CAACgB,IAAI,CAAC0B,MAAM,CAACvB,GAAG,CAACN,GAAG,CAAC;KACzC,MAAM;MACH,MAAM;QAAEG;MAAI,CAAE,GAAGhB,WAAW,CAAC,IAAI,CAAC;MAClC,KAAK,MAAM;QAAEa,GAAG;QAAEkB;MAAI,CAAE,IAAIf,IAAI,CAAClB,MAAM,EAAE,EAAE;QACvCiC,IAAI,EAAE;QACNf,IAAI,CAAC0B,MAAM,CAAC7B,GAAG,CAAC;;;IAIxB,OAAO,IAAI;EACf;EAEA;;;EAGA,MAAMyE,WAAWA,CAACnF,KAAwB,EAAEkB,QAAkB;IAC1D,OAAO,MAAM,IAAI,CAACS,EAAE,CAAC3B,KAAK,EAAEkB,QAAQ,CAAC;EACzC;EAEA;;;EAGA,MAAMkE,cAAcA,CAACpF,KAAwB,EAAEkB,QAAkB;IAC7D,OAAO,MAAM,IAAI,CAACY,GAAG,CAAC9B,KAAK,EAAEkB,QAAQ,CAAC;EAC1C;EAEA;;;EAGA,OAAOmE,UAAUA,CAAwBxC,GAA6B;IAClE,MAAMyC,cAAe,SAAQ5C,YAAY;MACrCjI,YAAYwG,OAAe,EAAElG,MAAA,GAAgC,IAAI;QAC7D,KAAK,CAACkG,OAAO,EAAE4B,GAAG,EAAE9H,MAAM,CAAC;MAC/B;;IAEJ,OAAOuK,cAAqB;EAChC;EAEA;;;EAGA,OAAO3I,IAAIA,CAAwBgG,MAAc,EAAEE,GAA6B,EAAE9H,MAA8B;IAC5G,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,IAAI;;IACnC,MAAML,QAAQ,GAAG,IAAI,IAAI,CAACiI,MAAM,EAAEE,GAAG,EAAE9H,MAAM,CAAE;IAC/C,OAAOL,QAAe;EAC1B;;AAGJ,SAAS6K,aAAaA,CAAA;EAClB,OAAO7C,YAAmB;AAC9B;AAEA;;;AAGA,OAAM,MAAO8C,QAAS,SAAQD,aAAa,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}