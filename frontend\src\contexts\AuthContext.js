import React, { createContext, useContext, useState, useEffect } from 'react';
import { ethers } from 'ethers';
import api from '../utils/api';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('cq_token'));
  const [loading, setLoading] = useState(true);
  const [walletAddress, setWalletAddress] = useState(null);

  // Check if user is logged in on app start
  useEffect(() => {
    const checkAuth = async () => {
      if (token) {
        try {
          const response = await api.get('/api/users/profile');
          setUser(response.data.user);
          setWalletAddress(response.data.user.walletAddress);
        } catch (error) {
          console.error('Auth check failed:', error);
          logout();
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, [token]);

  const connectWallet = async () => {
    try {
      if (!window.ethereum) {
        toast.error('MetaMask is not installed. Please install MetaMask to continue.');
        return null;
      }

      // Request account access
      const accounts = await window.ethereum.request({
        method: 'eth_requestAccounts',
      });

      if (accounts.length === 0) {
        toast.error('No accounts found. Please connect your wallet.');
        return null;
      }

      const address = accounts[0];
      setWalletAddress(address);

      // Switch to correct network if needed (optional)
      try {
        await window.ethereum.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: '0x89' }], // Polygon mainnet
        });
      } catch (switchError) {
        // If network doesn't exist, add it
        if (switchError.code === 4902) {
          try {
            await window.ethereum.request({
              method: 'wallet_addEthereumChain',
              params: [{
                chainId: '0x89',
                chainName: 'Polygon Mainnet',
                nativeCurrency: {
                  name: 'MATIC',
                  symbol: 'MATIC',
                  decimals: 18,
                },
                rpcUrls: ['https://polygon-rpc.com/'],
                blockExplorerUrls: ['https://polygonscan.com/'],
              }],
            });
          } catch (addError) {
            console.error('Failed to add network:', addError);
          }
        }
      }

      return address;
    } catch (error) {
      console.error('Wallet connection failed:', error);
      toast.error('Failed to connect wallet');
      return null;
    }
  };

  const login = async () => {
    try {
      setLoading(true);

      const address = await connectWallet();
      if (!address) {
        setLoading(false);
        return false;
      }

      // Get nonce for signing
      const nonceResponse = await api.post('/api/auth/nonce', {
        walletAddress: address,
      });

      const { message } = nonceResponse.data;

      // Sign message
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const signature = await signer.signMessage(message);

      // Login with signature
      const loginResponse = await api.post('/api/auth/login', {
        walletAddress: address,
        signature,
        message,
      });

      const { token: newToken, user: userData } = loginResponse.data;

      // Store token and user data
      localStorage.setItem('cq_token', newToken);
      setToken(newToken);
      setUser(userData);
      setWalletAddress(address);

      toast.success('Successfully logged in!');
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      toast.error(error.response?.data?.error || 'Login failed');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('cq_token');
    setToken(null);
    setUser(null);
    setWalletAddress(null);
    toast.success('Logged out successfully');
  };

  const updateUser = (userData) => {
    setUser(prev => ({ ...prev, ...userData }));
  };

  const refreshUserData = async () => {
    try {
      const response = await api.get('/api/users/profile');
      setUser(response.data.user);
      return response.data;
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      return null;
    }
  };

  const value = {
    user,
    token,
    walletAddress,
    loading,
    login,
    logout,
    connectWallet,
    updateUser,
    refreshUserData,
    isAuthenticated: !!token && !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
