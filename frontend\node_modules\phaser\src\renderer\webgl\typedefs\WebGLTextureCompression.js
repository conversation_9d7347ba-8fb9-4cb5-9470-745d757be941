/**
 * @typedef {object} Phaser.Types.Renderer.WebGL.WebGLTextureCompression
 * @since 3.55.0
 *
 * @property {object|undefined} ASTC - Indicates if ASTC compression is supported (mostly iOS).
 * @property {object|undefined} ATC - Indicates if ATC compression is supported.
 * @property {object|undefined} BPTC - Indicates if BPTC compression is supported.
 * @property {object|undefined} ETC - Indicates if ETC compression is supported (mostly Android).
 * @property {object|undefined} ETC1 - Indicates if ETC1 compression is supported (mostly Android).
 * @property {object|undefined} IMG - Indicates the browser supports true color images (all browsers).
 * @property {object|undefined} PVRTC - Indicates if PVRTC compression is supported (mostly iOS).
 * @property {object|undefined} RGTC - Indicates if RGTC compression is supported (mostly iOS).
 * @property {object|undefined} S3TC - Indicates if S3TC compression is supported on current device (mostly Windows).
 * @property {object|undefined} S3TCSRGB - Indicates if S3TCSRGB compression is supported on current device (mostly Windows).
 */
