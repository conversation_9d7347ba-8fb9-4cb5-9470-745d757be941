{"ast": null, "code": "/**\n *  [[link-etherscan]] provides a third-party service for connecting to\n *  various blockchains over a combination of JSON-RPC and custom API\n *  endpoints.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Base (``base``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *\n *  @_subsection api/providers/thirdparty:Etherscan  [providers-etherscan]\n */\nimport { AbiCoder } from \"../abi/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { accessListify, Transaction } from \"../transaction/index.js\";\nimport { defineProperties, hexlify, toQuantity, FetchRequest, assert, assertArgument, isError,\n//    parseUnits,\ntoUtf8String } from \"../utils/index.js\";\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { Network } from \"./network.js\";\nimport { NetworkPlugin } from \"./plugins-network.js\";\nimport { showThrottleMessage } from \"./community.js\";\nconst THROTTLE = 2000;\nfunction isPromise(value) {\n  return value && typeof value.then === \"function\";\n}\nconst EtherscanPluginId = \"org.ethers.plugins.provider.Etherscan\";\n/**\n *  A Network can include an **EtherscanPlugin** to provide\n *  a custom base URL.\n *\n *  @_docloc: api/providers/thirdparty:Etherscan\n */\nexport class EtherscanPlugin extends NetworkPlugin {\n  /**\n   *  The Etherscan API base URL.\n   */\n  baseUrl;\n  /**\n   *  Creates a new **EtherscanProvider** which will use\n   *  %%baseUrl%%.\n   */\n  constructor(baseUrl) {\n    super(EtherscanPluginId);\n    defineProperties(this, {\n      baseUrl\n    });\n  }\n  clone() {\n    return new EtherscanPlugin(this.baseUrl);\n  }\n}\nconst skipKeys = [\"enableCcipRead\"];\nlet nextId = 1;\n/**\n *  The **EtherscanBaseProvider** is the super-class of\n *  [[EtherscanProvider]], which should generally be used instead.\n *\n *  Since the **EtherscanProvider** includes additional code for\n *  [[Contract]] access, in //rare cases// that contracts are not\n *  used, this class can reduce code size.\n *\n *  @_docloc: api/providers/thirdparty:Etherscan\n */\nexport class EtherscanProvider extends AbstractProvider {\n  /**\n   *  The connected network.\n   */\n  network;\n  /**\n   *  The API key or null if using the community provided bandwidth.\n   */\n  apiKey;\n  #plugin;\n  /**\n   *  Creates a new **EtherscanBaseProvider**.\n   */\n  constructor(_network, _apiKey) {\n    const apiKey = _apiKey != null ? _apiKey : null;\n    super();\n    const network = Network.from(_network);\n    this.#plugin = network.getPlugin(EtherscanPluginId);\n    defineProperties(this, {\n      apiKey,\n      network\n    });\n  }\n  /**\n   *  Returns the base URL.\n   *\n   *  If an [[EtherscanPlugin]] is configured on the\n   *  [[EtherscanBaseProvider_network]], returns the plugin's\n   *  baseUrl.\n   *\n   *  Deprecated; for Etherscan v2 the base is no longer a simply\n   *  host, but instead a URL including a chainId parameter. Changing\n   *  this to return a URL prefix could break some libraries, so it\n   *  is left intact but will be removed in the future as it is unused.\n   */\n  getBaseUrl() {\n    if (this.#plugin) {\n      return this.#plugin.baseUrl;\n    }\n    switch (this.network.name) {\n      case \"mainnet\":\n        return \"https:/\\/api.etherscan.io\";\n      case \"goerli\":\n        return \"https:/\\/api-goerli.etherscan.io\";\n      case \"sepolia\":\n        return \"https:/\\/api-sepolia.etherscan.io\";\n      case \"holesky\":\n        return \"https:/\\/api-holesky.etherscan.io\";\n      case \"arbitrum\":\n        return \"https:/\\/api.arbiscan.io\";\n      case \"arbitrum-goerli\":\n        return \"https:/\\/api-goerli.arbiscan.io\";\n      case \"base\":\n        return \"https:/\\/api.basescan.org\";\n      case \"base-sepolia\":\n        return \"https:/\\/api-sepolia.basescan.org\";\n      case \"bnb\":\n        return \"https:/\\/api.bscscan.com\";\n      case \"bnbt\":\n        return \"https:/\\/api-testnet.bscscan.com\";\n      case \"matic\":\n        return \"https:/\\/api.polygonscan.com\";\n      case \"matic-amoy\":\n        return \"https:/\\/api-amoy.polygonscan.com\";\n      case \"matic-mumbai\":\n        return \"https:/\\/api-testnet.polygonscan.com\";\n      case \"optimism\":\n        return \"https:/\\/api-optimistic.etherscan.io\";\n      case \"optimism-goerli\":\n        return \"https:/\\/api-goerli-optimistic.etherscan.io\";\n      default:\n    }\n    assertArgument(false, \"unsupported network\", \"network\", this.network);\n  }\n  /**\n   *  Returns the URL for the %%module%% and %%params%%.\n   */\n  getUrl(module, params) {\n    let query = Object.keys(params).reduce((accum, key) => {\n      const value = params[key];\n      if (value != null) {\n        accum += `&${key}=${value}`;\n      }\n      return accum;\n    }, \"\");\n    if (this.apiKey) {\n      query += `&apikey=${this.apiKey}`;\n    }\n    return `https:/\\/api.etherscan.io/v2/api?chainid=${this.network.chainId}&module=${module}${query}`;\n  }\n  /**\n   *  Returns the URL for using POST requests.\n   */\n  getPostUrl() {\n    return `https:/\\/api.etherscan.io/v2/api?chainid=${this.network.chainId}`;\n  }\n  /**\n   *  Returns the parameters for using POST requests.\n   */\n  getPostData(module, params) {\n    params.module = module;\n    params.apikey = this.apiKey;\n    params.chainid = this.network.chainId;\n    return params;\n  }\n  async detectNetwork() {\n    return this.network;\n  }\n  /**\n   *  Resolves to the result of calling %%module%% with %%params%%.\n   *\n   *  If %%post%%, the request is made as a POST request.\n   */\n  async fetch(module, params, post) {\n    const id = nextId++;\n    const url = post ? this.getPostUrl() : this.getUrl(module, params);\n    const payload = post ? this.getPostData(module, params) : null;\n    this.emit(\"debug\", {\n      action: \"sendRequest\",\n      id,\n      url,\n      payload: payload\n    });\n    const request = new FetchRequest(url);\n    request.setThrottleParams({\n      slotInterval: 1000\n    });\n    request.retryFunc = (req, resp, attempt) => {\n      if (this.isCommunityResource()) {\n        showThrottleMessage(\"Etherscan\");\n      }\n      return Promise.resolve(true);\n    };\n    request.processFunc = async (request, response) => {\n      const result = response.hasBody() ? JSON.parse(toUtf8String(response.body)) : {};\n      const throttle = (typeof result.result === \"string\" ? result.result : \"\").toLowerCase().indexOf(\"rate limit\") >= 0;\n      if (module === \"proxy\") {\n        // This JSON response indicates we are being throttled\n        if (result && result.status == 0 && result.message == \"NOTOK\" && throttle) {\n          this.emit(\"debug\", {\n            action: \"receiveError\",\n            id,\n            reason: \"proxy-NOTOK\",\n            error: result\n          });\n          response.throwThrottleError(result.result, THROTTLE);\n        }\n      } else {\n        if (throttle) {\n          this.emit(\"debug\", {\n            action: \"receiveError\",\n            id,\n            reason: \"null result\",\n            error: result.result\n          });\n          response.throwThrottleError(result.result, THROTTLE);\n        }\n      }\n      return response;\n    };\n    if (payload) {\n      request.setHeader(\"content-type\", \"application/x-www-form-urlencoded; charset=UTF-8\");\n      request.body = Object.keys(payload).map(k => `${k}=${payload[k]}`).join(\"&\");\n    }\n    const response = await request.send();\n    try {\n      response.assertOk();\n    } catch (error) {\n      this.emit(\"debug\", {\n        action: \"receiveError\",\n        id,\n        error,\n        reason: \"assertOk\"\n      });\n      assert(false, \"response error\", \"SERVER_ERROR\", {\n        request,\n        response\n      });\n    }\n    if (!response.hasBody()) {\n      this.emit(\"debug\", {\n        action: \"receiveError\",\n        id,\n        error: \"missing body\",\n        reason: \"null body\"\n      });\n      assert(false, \"missing response\", \"SERVER_ERROR\", {\n        request,\n        response\n      });\n    }\n    const result = JSON.parse(toUtf8String(response.body));\n    if (module === \"proxy\") {\n      if (result.jsonrpc != \"2.0\") {\n        this.emit(\"debug\", {\n          action: \"receiveError\",\n          id,\n          result,\n          reason: \"invalid JSON-RPC\"\n        });\n        assert(false, \"invalid JSON-RPC response (missing jsonrpc='2.0')\", \"SERVER_ERROR\", {\n          request,\n          response,\n          info: {\n            result\n          }\n        });\n      }\n      if (result.error) {\n        this.emit(\"debug\", {\n          action: \"receiveError\",\n          id,\n          result,\n          reason: \"JSON-RPC error\"\n        });\n        assert(false, \"error response\", \"SERVER_ERROR\", {\n          request,\n          response,\n          info: {\n            result\n          }\n        });\n      }\n      this.emit(\"debug\", {\n        action: \"receiveRequest\",\n        id,\n        result\n      });\n      return result.result;\n    } else {\n      // getLogs, getHistory have weird success responses\n      if (result.status == 0 && (result.message === \"No records found\" || result.message === \"No transactions found\")) {\n        this.emit(\"debug\", {\n          action: \"receiveRequest\",\n          id,\n          result\n        });\n        return result.result;\n      }\n      if (result.status != 1 || typeof result.message === \"string\" && !result.message.match(/^OK/)) {\n        this.emit(\"debug\", {\n          action: \"receiveError\",\n          id,\n          result\n        });\n        assert(false, \"error response\", \"SERVER_ERROR\", {\n          request,\n          response,\n          info: {\n            result\n          }\n        });\n      }\n      this.emit(\"debug\", {\n        action: \"receiveRequest\",\n        id,\n        result\n      });\n      return result.result;\n    }\n  }\n  /**\n   *  Returns %%transaction%% normalized for the Etherscan API.\n   */\n  _getTransactionPostData(transaction) {\n    const result = {};\n    for (let key in transaction) {\n      if (skipKeys.indexOf(key) >= 0) {\n        continue;\n      }\n      if (transaction[key] == null) {\n        continue;\n      }\n      let value = transaction[key];\n      if (key === \"type\" && value === 0) {\n        continue;\n      }\n      if (key === \"blockTag\" && value === \"latest\") {\n        continue;\n      }\n      // Quantity-types require no leading zero, unless 0\n      if ({\n        type: true,\n        gasLimit: true,\n        gasPrice: true,\n        maxFeePerGs: true,\n        maxPriorityFeePerGas: true,\n        nonce: true,\n        value: true\n      }[key]) {\n        value = toQuantity(value);\n      } else if (key === \"accessList\") {\n        value = \"[\" + accessListify(value).map(set => {\n          return `{address:\"${set.address}\",storageKeys:[\"${set.storageKeys.join('\",\"')}\"]}`;\n        }).join(\",\") + \"]\";\n      } else if (key === \"blobVersionedHashes\") {\n        if (value.length === 0) {\n          continue;\n        }\n        // @TODO: update this once the API supports blobs\n        assert(false, \"Etherscan API does not support blobVersionedHashes\", \"UNSUPPORTED_OPERATION\", {\n          operation: \"_getTransactionPostData\",\n          info: {\n            transaction\n          }\n        });\n      } else {\n        value = hexlify(value);\n      }\n      result[key] = value;\n    }\n    return result;\n  }\n  /**\n   *  Throws the normalized Etherscan error.\n   */\n  _checkError(req, error, transaction) {\n    // Pull any message out if, possible\n    let message = \"\";\n    if (isError(error, \"SERVER_ERROR\")) {\n      // Check for an error emitted by a proxy call\n      try {\n        message = error.info.result.error.message;\n      } catch (e) {}\n      if (!message) {\n        try {\n          message = error.info.message;\n        } catch (e) {}\n      }\n    }\n    if (req.method === \"estimateGas\") {\n      if (!message.match(/revert/i) && message.match(/insufficient funds/i)) {\n        assert(false, \"insufficient funds\", \"INSUFFICIENT_FUNDS\", {\n          transaction: req.transaction\n        });\n      }\n    }\n    if (req.method === \"call\" || req.method === \"estimateGas\") {\n      if (message.match(/execution reverted/i)) {\n        let data = \"\";\n        try {\n          data = error.info.result.error.data;\n        } catch (error) {}\n        const e = AbiCoder.getBuiltinCallException(req.method, req.transaction, data);\n        e.info = {\n          request: req,\n          error\n        };\n        throw e;\n      }\n    }\n    if (message) {\n      if (req.method === \"broadcastTransaction\") {\n        const transaction = Transaction.from(req.signedTransaction);\n        if (message.match(/replacement/i) && message.match(/underpriced/i)) {\n          assert(false, \"replacement fee too low\", \"REPLACEMENT_UNDERPRICED\", {\n            transaction\n          });\n        }\n        if (message.match(/insufficient funds/)) {\n          assert(false, \"insufficient funds for intrinsic transaction cost\", \"INSUFFICIENT_FUNDS\", {\n            transaction\n          });\n        }\n        if (message.match(/same hash was already imported|transaction nonce is too low|nonce too low/)) {\n          assert(false, \"nonce has already been used\", \"NONCE_EXPIRED\", {\n            transaction\n          });\n        }\n      }\n    }\n    // Something we could not process\n    throw error;\n  }\n  async _detectNetwork() {\n    return this.network;\n  }\n  async _perform(req) {\n    switch (req.method) {\n      case \"chainId\":\n        return this.network.chainId;\n      case \"getBlockNumber\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_blockNumber\"\n        });\n      case \"getGasPrice\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_gasPrice\"\n        });\n      case \"getPriorityFee\":\n        // This is temporary until Etherscan completes support\n        if (this.network.name === \"mainnet\") {\n          return \"**********\";\n        } else if (this.network.name === \"optimism\") {\n          return \"1000000\";\n        } else {\n          throw new Error(\"fallback onto the AbstractProvider default\");\n        }\n      /* Working with Etherscan to get this added:\n      try {\n          const test = await this.fetch(\"proxy\", {\n              action: \"eth_maxPriorityFeePerGas\"\n          });\n          console.log(test);\n          return test;\n      } catch (e) {\n          console.log(\"DEBUG\", e);\n          throw e;\n      }\n      */\n      /* This might be safe; but due to rounding neither myself\n         or Etherscan are necessarily comfortable with this. :)\n      try {\n          const result = await this.fetch(\"gastracker\", { action: \"gasoracle\" });\n          console.log(result);\n          const gasPrice = parseUnits(result.SafeGasPrice, \"gwei\");\n          const baseFee = parseUnits(result.suggestBaseFee, \"gwei\");\n          const priorityFee = gasPrice - baseFee;\n          if (priorityFee < 0) { throw new Error(\"negative priority fee; defer to abstract provider default\"); }\n          return priorityFee;\n      } catch (error) {\n          console.log(\"DEBUG\", error);\n          throw error;\n      }\n      */\n      case \"getBalance\":\n        // Returns base-10 result\n        return this.fetch(\"account\", {\n          action: \"balance\",\n          address: req.address,\n          tag: req.blockTag\n        });\n      case \"getTransactionCount\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_getTransactionCount\",\n          address: req.address,\n          tag: req.blockTag\n        });\n      case \"getCode\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_getCode\",\n          address: req.address,\n          tag: req.blockTag\n        });\n      case \"getStorage\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_getStorageAt\",\n          address: req.address,\n          position: req.position,\n          tag: req.blockTag\n        });\n      case \"broadcastTransaction\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_sendRawTransaction\",\n          hex: req.signedTransaction\n        }, true).catch(error => {\n          return this._checkError(req, error, req.signedTransaction);\n        });\n      case \"getBlock\":\n        if (\"blockTag\" in req) {\n          return this.fetch(\"proxy\", {\n            action: \"eth_getBlockByNumber\",\n            tag: req.blockTag,\n            boolean: req.includeTransactions ? \"true\" : \"false\"\n          });\n        }\n        assert(false, \"getBlock by blockHash not supported by Etherscan\", \"UNSUPPORTED_OPERATION\", {\n          operation: \"getBlock(blockHash)\"\n        });\n      case \"getTransaction\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_getTransactionByHash\",\n          txhash: req.hash\n        });\n      case \"getTransactionReceipt\":\n        return this.fetch(\"proxy\", {\n          action: \"eth_getTransactionReceipt\",\n          txhash: req.hash\n        });\n      case \"call\":\n        {\n          if (req.blockTag !== \"latest\") {\n            throw new Error(\"EtherscanProvider does not support blockTag for call\");\n          }\n          const postData = this._getTransactionPostData(req.transaction);\n          postData.module = \"proxy\";\n          postData.action = \"eth_call\";\n          try {\n            return await this.fetch(\"proxy\", postData, true);\n          } catch (error) {\n            return this._checkError(req, error, req.transaction);\n          }\n        }\n      case \"estimateGas\":\n        {\n          const postData = this._getTransactionPostData(req.transaction);\n          postData.module = \"proxy\";\n          postData.action = \"eth_estimateGas\";\n          try {\n            return await this.fetch(\"proxy\", postData, true);\n          } catch (error) {\n            return this._checkError(req, error, req.transaction);\n          }\n        }\n      /*\n                  case \"getLogs\": {\n                      // Needs to complain if more than one address is passed in\n                      const args: Record<string, any> = { action: \"getLogs\" }\n      \n                      if (params.filter.fromBlock) {\n                          args.fromBlock = checkLogTag(params.filter.fromBlock);\n                      }\n      \n                      if (params.filter.toBlock) {\n                          args.toBlock = checkLogTag(params.filter.toBlock);\n                      }\n      \n                      if (params.filter.address) {\n                          args.address = params.filter.address;\n                      }\n      \n                      // @TODO: We can handle slightly more complicated logs using the logs API\n                      if (params.filter.topics && params.filter.topics.length > 0) {\n                          if (params.filter.topics.length > 1) {\n                              logger.throwError(\"unsupported topic count\", Logger.Errors.UNSUPPORTED_OPERATION, { topics: params.filter.topics });\n                          }\n                          if (params.filter.topics.length === 1) {\n                              const topic0 = params.filter.topics[0];\n                              if (typeof(topic0) !== \"string\" || topic0.length !== 66) {\n                                  logger.throwError(\"unsupported topic format\", Logger.Errors.UNSUPPORTED_OPERATION, { topic0: topic0 });\n                              }\n                              args.topic0 = topic0;\n                          }\n                      }\n      \n                      const logs: Array<any> = await this.fetch(\"logs\", args);\n      \n                      // Cache txHash => blockHash\n                      let blocks: { [tag: string]: string } = {};\n      \n                      // Add any missing blockHash to the logs\n                      for (let i = 0; i < logs.length; i++) {\n                          const log = logs[i];\n                          if (log.blockHash != null) { continue; }\n                          if (blocks[log.blockNumber] == null) {\n                              const block = await this.getBlock(log.blockNumber);\n                              if (block) {\n                                  blocks[log.blockNumber] = block.hash;\n                              }\n                          }\n      \n                          log.blockHash = blocks[log.blockNumber];\n                      }\n      \n                      return logs;\n                  }\n      */\n      default:\n        break;\n    }\n    return super._perform(req);\n  }\n  async getNetwork() {\n    return this.network;\n  }\n  /**\n   *  Resolves to the current price of ether.\n   *\n   *  This returns ``0`` on any network other than ``mainnet``.\n   */\n  async getEtherPrice() {\n    if (this.network.name !== \"mainnet\") {\n      return 0.0;\n    }\n    return parseFloat((await this.fetch(\"stats\", {\n      action: \"ethprice\"\n    })).ethusd);\n  }\n  /**\n   *  Resolves to a [Contract]] for %%address%%, using the\n   *  Etherscan API to retreive the Contract ABI.\n   */\n  async getContract(_address) {\n    let address = this._getAddress(_address);\n    if (isPromise(address)) {\n      address = await address;\n    }\n    try {\n      const resp = await this.fetch(\"contract\", {\n        action: \"getabi\",\n        address\n      });\n      const abi = JSON.parse(resp);\n      return new Contract(address, abi, this);\n    } catch (error) {\n      return null;\n    }\n  }\n  isCommunityResource() {\n    return this.apiKey == null;\n  }\n}", "map": {"version": 3, "names": ["AbiCoder", "Contract", "accessListify", "Transaction", "defineProperties", "hexlify", "toQuantity", "FetchRequest", "assert", "assertArgument", "isError", "toUtf8String", "AbstractProvider", "Network", "NetworkPlugin", "showThrottleMessage", "THROTTLE", "isPromise", "value", "then", "EtherscanPluginId", "EtherscanPlugin", "baseUrl", "constructor", "clone", "<PERSON><PERSON><PERSON><PERSON>", "nextId", "EtherscanProvider", "network", "<PERSON><PERSON><PERSON><PERSON>", "plugin", "_network", "_api<PERSON><PERSON>", "from", "getPlugin", "getBaseUrl", "name", "getUrl", "module", "params", "query", "Object", "keys", "reduce", "accum", "key", "chainId", "getPostUrl", "getPostData", "apikey", "chainid", "detectNetwork", "fetch", "post", "id", "url", "payload", "emit", "action", "request", "setThrottleParams", "slotInterval", "retryFunc", "req", "resp", "attempt", "isCommunityResource", "Promise", "resolve", "processFunc", "response", "result", "hasBody", "JSON", "parse", "body", "throttle", "toLowerCase", "indexOf", "status", "message", "reason", "error", "throwThrottleError", "<PERSON><PERSON><PERSON><PERSON>", "map", "k", "join", "send", "assertOk", "jsonrpc", "info", "match", "_getTransactionPostData", "transaction", "type", "gasLimit", "gasPrice", "maxFeePerGs", "maxPriorityFeePerGas", "nonce", "set", "address", "storageKeys", "length", "operation", "_checkError", "e", "method", "data", "getBuiltinCallException", "signedTransaction", "_detectNetwork", "_perform", "Error", "tag", "blockTag", "position", "hex", "catch", "boolean", "includeTransactions", "txhash", "hash", "postData", "getNetwork", "getEtherPrice", "parseFloat", "et<PERSON>d", "getContract", "_address", "_getAddress", "abi"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-etherscan.ts"], "sourcesContent": ["/**\n *  [[link-etherscan]] provides a third-party service for connecting to\n *  various blockchains over a combination of JSON-RPC and custom API\n *  endpoints.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Base (``base``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *\n *  @_subsection api/providers/thirdparty:Etherscan  [providers-etherscan]\n */\n\nimport { AbiCoder } from \"../abi/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { accessListify, Transaction } from \"../transaction/index.js\";\nimport {\n    defineProperties,\n    hexlify, toQuantity,\n    FetchRequest,\n    assert, assertArgument, isError,\n//    parseUnits,\n    toUtf8String\n } from \"../utils/index.js\";\n\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { Network } from \"./network.js\";\nimport { NetworkPlugin } from \"./plugins-network.js\";\nimport { showThrottleMessage } from \"./community.js\";\n\nimport { PerformActionRequest } from \"./abstract-provider.js\";\nimport type { Networkish } from \"./network.js\";\n//import type { } from \"./pagination\";\nimport type { TransactionRequest } from \"./provider.js\";\n\nconst THROTTLE = 2000;\n\nfunction isPromise<T = any>(value: any): value is Promise<T> {\n    return (value && typeof(value.then) === \"function\");\n}\n\n\n/**\n *  When subscribing to the ``\"debug\"`` event on an Etherscan-based\n *  provider, the events receive a **DebugEventEtherscanProvider**\n *  payload.\n *\n *  @_docloc: api/providers/thirdparty:Etherscan\n */\nexport type DebugEventEtherscanProvider = {\n    action: \"sendRequest\",\n    id: number,\n    url: string,\n    payload: Record<string, any>\n} | {\n    action: \"receiveRequest\",\n    id: number,\n    result: any\n} | {\n    action: \"receiveError\",\n    id: number,\n    error: any\n};\n\nconst EtherscanPluginId = \"org.ethers.plugins.provider.Etherscan\";\n\n/**\n *  A Network can include an **EtherscanPlugin** to provide\n *  a custom base URL.\n *\n *  @_docloc: api/providers/thirdparty:Etherscan\n */\nexport class EtherscanPlugin extends NetworkPlugin {\n    /**\n     *  The Etherscan API base URL.\n     */\n    readonly baseUrl!: string;\n\n    /**\n     *  Creates a new **EtherscanProvider** which will use\n     *  %%baseUrl%%.\n     */\n    constructor(baseUrl: string) {\n        super(EtherscanPluginId);\n        defineProperties<EtherscanPlugin>(this, { baseUrl });\n    }\n\n    clone(): EtherscanPlugin {\n        return new EtherscanPlugin(this.baseUrl);\n    }\n}\n\nconst skipKeys = [ \"enableCcipRead\" ];\n\nlet nextId = 1;\n\n/**\n *  The **EtherscanBaseProvider** is the super-class of\n *  [[EtherscanProvider]], which should generally be used instead.\n *\n *  Since the **EtherscanProvider** includes additional code for\n *  [[Contract]] access, in //rare cases// that contracts are not\n *  used, this class can reduce code size.\n *\n *  @_docloc: api/providers/thirdparty:Etherscan\n */\nexport class EtherscanProvider extends AbstractProvider {\n\n    /**\n     *  The connected network.\n     */\n    readonly network!: Network;\n\n    /**\n     *  The API key or null if using the community provided bandwidth.\n     */\n    readonly apiKey!: null | string;\n\n    readonly #plugin: null | EtherscanPlugin;\n\n    /**\n     *  Creates a new **EtherscanBaseProvider**.\n     */\n    constructor(_network?: Networkish, _apiKey?: string) {\n        const apiKey = (_apiKey != null) ? _apiKey: null;\n\n        super();\n\n        const network = Network.from(_network);\n\n        this.#plugin = network.getPlugin<EtherscanPlugin>(EtherscanPluginId);\n\n        defineProperties<EtherscanProvider>(this, { apiKey, network });\n    }\n\n    /**\n     *  Returns the base URL.\n     *\n     *  If an [[EtherscanPlugin]] is configured on the\n     *  [[EtherscanBaseProvider_network]], returns the plugin's\n     *  baseUrl.\n     *\n     *  Deprecated; for Etherscan v2 the base is no longer a simply\n     *  host, but instead a URL including a chainId parameter. Changing\n     *  this to return a URL prefix could break some libraries, so it\n     *  is left intact but will be removed in the future as it is unused.\n     */\n    getBaseUrl(): string {\n        if (this.#plugin) { return this.#plugin.baseUrl; }\n\n        switch(this.network.name) {\n            case \"mainnet\":\n                return \"https:/\\/api.etherscan.io\";\n            case \"goerli\":\n                return \"https:/\\/api-goerli.etherscan.io\";\n            case \"sepolia\":\n                return \"https:/\\/api-sepolia.etherscan.io\";\n            case \"holesky\":\n                return \"https:/\\/api-holesky.etherscan.io\";\n\n            case \"arbitrum\":\n                return \"https:/\\/api.arbiscan.io\";\n            case \"arbitrum-goerli\":\n                return \"https:/\\/api-goerli.arbiscan.io\";\n           case \"base\":\n                return \"https:/\\/api.basescan.org\";\n            case \"base-sepolia\":\n                return \"https:/\\/api-sepolia.basescan.org\";\n            case \"bnb\":\n                return \"https:/\\/api.bscscan.com\";\n            case \"bnbt\":\n                return \"https:/\\/api-testnet.bscscan.com\";\n            case \"matic\":\n                return \"https:/\\/api.polygonscan.com\";\n            case \"matic-amoy\":\n                return \"https:/\\/api-amoy.polygonscan.com\";\n            case \"matic-mumbai\":\n                return \"https:/\\/api-testnet.polygonscan.com\";\n            case \"optimism\":\n                return \"https:/\\/api-optimistic.etherscan.io\";\n            case \"optimism-goerli\":\n                return \"https:/\\/api-goerli-optimistic.etherscan.io\";\n\n            default:\n        }\n\n        assertArgument(false, \"unsupported network\", \"network\", this.network);\n    }\n\n    /**\n     *  Returns the URL for the %%module%% and %%params%%.\n     */\n    getUrl(module: string, params: Record<string, string>): string {\n        let query = Object.keys(params).reduce((accum, key) => {\n            const value = params[key];\n            if (value != null) {\n                accum += `&${ key }=${ value }`\n            }\n            return accum\n        }, \"\");\n        if (this.apiKey) { query += `&apikey=${ this.apiKey }`; }\n        return `https:/\\/api.etherscan.io/v2/api?chainid=${ this.network.chainId }&module=${ module }${ query }`;\n    }\n\n    /**\n     *  Returns the URL for using POST requests.\n     */\n    getPostUrl(): string {\n        return `https:/\\/api.etherscan.io/v2/api?chainid=${ this.network.chainId }`;\n    }\n\n    /**\n     *  Returns the parameters for using POST requests.\n     */\n    getPostData(module: string, params: Record<string, any>): Record<string, any> {\n        params.module = module;\n        params.apikey = this.apiKey;\n        params.chainid = this.network.chainId;\n        return params;\n    }\n\n    async detectNetwork(): Promise<Network> {\n        return this.network;\n    }\n\n    /**\n     *  Resolves to the result of calling %%module%% with %%params%%.\n     *\n     *  If %%post%%, the request is made as a POST request.\n     */\n    async fetch(module: string, params: Record<string, any>, post?: boolean): Promise<any> {\n        const id = nextId++;\n\n        const url = (post ? this.getPostUrl(): this.getUrl(module, params));\n        const payload = (post ? this.getPostData(module, params): null);\n\n        this.emit(\"debug\", { action: \"sendRequest\", id, url, payload: payload });\n\n        const request = new FetchRequest(url);\n        request.setThrottleParams({ slotInterval: 1000 });\n        request.retryFunc = (req, resp, attempt: number) => {\n            if (this.isCommunityResource()) {\n                showThrottleMessage(\"Etherscan\");\n            }\n            return Promise.resolve(true);\n        };\n        request.processFunc = async (request, response) => {\n            const result = response.hasBody() ? JSON.parse(toUtf8String(response.body)): { };\n            const throttle = ((typeof(result.result) === \"string\") ? result.result: \"\").toLowerCase().indexOf(\"rate limit\") >= 0;\n            if (module === \"proxy\") {\n                // This JSON response indicates we are being throttled\n                if (result && result.status == 0 && result.message == \"NOTOK\" && throttle) {\n                    this.emit(\"debug\", { action: \"receiveError\", id, reason: \"proxy-NOTOK\", error: result });\n                    response.throwThrottleError(result.result, THROTTLE);\n                }\n            } else {\n                if (throttle) {\n                    this.emit(\"debug\", { action: \"receiveError\", id, reason: \"null result\", error: result.result });\n                    response.throwThrottleError(result.result, THROTTLE);\n                }\n            }\n            return response;\n        };\n\n        if (payload) {\n            request.setHeader(\"content-type\", \"application/x-www-form-urlencoded; charset=UTF-8\");\n            request.body = Object.keys(payload).map((k) => `${ k }=${ payload[k] }`).join(\"&\");\n        }\n\n        const response = await request.send();\n        try {\n            response.assertOk();\n        } catch (error) {\n            this.emit(\"debug\", { action: \"receiveError\", id, error, reason: \"assertOk\" });\n            assert(false, \"response error\", \"SERVER_ERROR\", { request, response });\n        }\n\n        if (!response.hasBody()) {\n            this.emit(\"debug\", { action: \"receiveError\", id, error: \"missing body\", reason: \"null body\" });\n            assert(false, \"missing response\", \"SERVER_ERROR\", { request, response });\n        }\n\n        const result = JSON.parse(toUtf8String(response.body));\n        if (module === \"proxy\") {\n            if (result.jsonrpc != \"2.0\") {\n                this.emit(\"debug\", { action: \"receiveError\", id, result, reason: \"invalid JSON-RPC\" });\n                assert(false, \"invalid JSON-RPC response (missing jsonrpc='2.0')\", \"SERVER_ERROR\", { request, response, info: { result } });\n            }\n\n            if (result.error) {\n                this.emit(\"debug\", { action: \"receiveError\", id, result, reason: \"JSON-RPC error\" });\n                assert(false, \"error response\", \"SERVER_ERROR\", { request, response, info: { result } });\n            }\n\n            this.emit(\"debug\", { action: \"receiveRequest\", id, result });\n\n            return result.result;\n\n        } else {\n            // getLogs, getHistory have weird success responses\n            if (result.status == 0 && (result.message === \"No records found\" || result.message === \"No transactions found\")) {\n                this.emit(\"debug\", { action: \"receiveRequest\", id, result });\n                return result.result;\n            }\n\n            if (result.status != 1 || (typeof(result.message) === \"string\" && !result.message.match(/^OK/))) {\n                this.emit(\"debug\", { action: \"receiveError\", id, result });\n                assert(false, \"error response\", \"SERVER_ERROR\", { request, response, info: { result } });\n            }\n\n            this.emit(\"debug\", { action: \"receiveRequest\", id, result });\n\n            return result.result;\n        }\n    }\n\n    /**\n     *  Returns %%transaction%% normalized for the Etherscan API.\n     */\n    _getTransactionPostData(transaction: TransactionRequest): Record<string, string> {\n        const result: Record<string, string> = { };\n        for (let key in transaction) {\n            if (skipKeys.indexOf(key) >= 0) { continue; }\n\n            if ((<any>transaction)[key] == null) { continue; }\n            let value = (<any>transaction)[key];\n            if (key === \"type\" && value === 0) { continue; }\n            if (key === \"blockTag\" && value === \"latest\") { continue; }\n\n            // Quantity-types require no leading zero, unless 0\n            if ((<any>{ type: true, gasLimit: true, gasPrice: true, maxFeePerGs: true, maxPriorityFeePerGas: true, nonce: true, value: true })[key]) {\n                value = toQuantity(value);\n\n            } else if (key === \"accessList\") {\n                value = \"[\" + accessListify(value).map((set) => {\n                    return `{address:\"${ set.address }\",storageKeys:[\"${ set.storageKeys.join('\",\"') }\"]}`;\n                }).join(\",\") + \"]\";\n\n            } else if (key === \"blobVersionedHashes\") {\n                if (value.length === 0) { continue; }\n\n                // @TODO: update this once the API supports blobs\n                assert(false, \"Etherscan API does not support blobVersionedHashes\", \"UNSUPPORTED_OPERATION\", {\n                    operation: \"_getTransactionPostData\",\n                    info: { transaction }\n                });\n\n            } else {\n                value = hexlify(value);\n            }\n            result[key] = value;\n        }\n        return result;\n    }\n\n    /**\n     *  Throws the normalized Etherscan error.\n     */\n    _checkError(req: PerformActionRequest, error: Error, transaction: any): never {\n        // Pull any message out if, possible\n        let message = \"\";\n        if (isError(error, \"SERVER_ERROR\")) {\n            // Check for an error emitted by a proxy call\n            try {\n                message = (<any>error).info.result.error.message;\n            } catch (e) { }\n\n            if (!message) {\n                try {\n                    message = (<any>error).info.message;\n                } catch (e) { }\n            }\n        }\n\n        if (req.method === \"estimateGas\") {\n            if (!message.match(/revert/i) && message.match(/insufficient funds/i)) {\n                assert(false, \"insufficient funds\", \"INSUFFICIENT_FUNDS\", {\n                    transaction: req.transaction\n                });\n            }\n        }\n\n        if (req.method === \"call\" || req.method === \"estimateGas\") {\n            if (message.match(/execution reverted/i)) {\n                let data = \"\";\n                try {\n                    data = (<any>error).info.result.error.data;\n                } catch (error) { }\n\n                const e = AbiCoder.getBuiltinCallException(req.method, <any>req.transaction, data);\n                e.info = { request: req, error }\n                throw e;\n            }\n        }\n\n        if (message) {\n            if (req.method === \"broadcastTransaction\") {\n                const transaction = Transaction.from(req.signedTransaction);\n                if (message.match(/replacement/i) && message.match(/underpriced/i)) {\n                    assert(false, \"replacement fee too low\", \"REPLACEMENT_UNDERPRICED\", {\n                        transaction\n                    });\n                }\n\n                if (message.match(/insufficient funds/)) {\n                    assert(false, \"insufficient funds for intrinsic transaction cost\", \"INSUFFICIENT_FUNDS\", {\n                       transaction\n                    });\n                }\n\n                if (message.match(/same hash was already imported|transaction nonce is too low|nonce too low/)) {\n                    assert(false, \"nonce has already been used\", \"NONCE_EXPIRED\", {\n                       transaction\n                    });\n                }\n            }\n        }\n\n        // Something we could not process\n        throw error;\n    }\n\n    async _detectNetwork(): Promise<Network> {\n        return this.network;\n    }\n\n    async _perform(req: PerformActionRequest): Promise<any> {\n        switch (req.method) {\n            case \"chainId\":\n                return this.network.chainId;\n\n            case \"getBlockNumber\":\n                return this.fetch(\"proxy\", { action: \"eth_blockNumber\" });\n\n            case \"getGasPrice\":\n                return this.fetch(\"proxy\", { action: \"eth_gasPrice\" });\n\n            case \"getPriorityFee\":\n                // This is temporary until Etherscan completes support\n                if (this.network.name === \"mainnet\") {\n                    return \"**********\";\n                } else if (this.network.name === \"optimism\") {\n                    return \"1000000\";\n                } else {\n                    throw new Error(\"fallback onto the AbstractProvider default\");\n                }\n                /* Working with Etherscan to get this added:\n                try {\n                    const test = await this.fetch(\"proxy\", {\n                        action: \"eth_maxPriorityFeePerGas\"\n                    });\n                    console.log(test);\n                    return test;\n                } catch (e) {\n                    console.log(\"DEBUG\", e);\n                    throw e;\n                }\n                */\n                /* This might be safe; but due to rounding neither myself\n                   or Etherscan are necessarily comfortable with this. :)\n                try {\n                    const result = await this.fetch(\"gastracker\", { action: \"gasoracle\" });\n                    console.log(result);\n                    const gasPrice = parseUnits(result.SafeGasPrice, \"gwei\");\n                    const baseFee = parseUnits(result.suggestBaseFee, \"gwei\");\n                    const priorityFee = gasPrice - baseFee;\n                    if (priorityFee < 0) { throw new Error(\"negative priority fee; defer to abstract provider default\"); }\n                    return priorityFee;\n                } catch (error) {\n                    console.log(\"DEBUG\", error);\n                    throw error;\n                }\n                */\n\n            case \"getBalance\":\n                // Returns base-10 result\n                return this.fetch(\"account\", {\n                    action: \"balance\",\n                    address: req.address,\n                    tag: req.blockTag\n                });\n\n           case \"getTransactionCount\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_getTransactionCount\",\n                    address: req.address,\n                    tag: req.blockTag\n                });\n\n            case \"getCode\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_getCode\",\n                    address: req.address,\n                    tag: req.blockTag\n                });\n\n            case \"getStorage\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_getStorageAt\",\n                    address: req.address,\n                    position: req.position,\n                    tag: req.blockTag\n                });\n\n            case \"broadcastTransaction\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_sendRawTransaction\",\n                    hex: req.signedTransaction\n                }, true).catch((error) => {\n                    return this._checkError(req, <Error>error, req.signedTransaction);\n                });\n\n            case \"getBlock\":\n                if (\"blockTag\" in req) {\n                    return this.fetch(\"proxy\", {\n                        action: \"eth_getBlockByNumber\",\n                        tag: req.blockTag,\n                        boolean: (req.includeTransactions ? \"true\": \"false\")\n                    });\n                }\n\n                assert(false, \"getBlock by blockHash not supported by Etherscan\", \"UNSUPPORTED_OPERATION\", {\n                    operation: \"getBlock(blockHash)\"\n                });\n\n            case \"getTransaction\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_getTransactionByHash\",\n                    txhash: req.hash\n                });\n\n            case \"getTransactionReceipt\":\n                return this.fetch(\"proxy\", {\n                    action: \"eth_getTransactionReceipt\",\n                    txhash: req.hash\n                });\n\n            case \"call\": {\n                if (req.blockTag !== \"latest\") {\n                    throw new Error(\"EtherscanProvider does not support blockTag for call\");\n                }\n\n                const postData = this._getTransactionPostData(req.transaction);\n                postData.module = \"proxy\";\n                postData.action = \"eth_call\";\n\n                try {\n                    return await this.fetch(\"proxy\", postData, true);\n                } catch (error) {\n                    return this._checkError(req, <Error>error, req.transaction);\n                }\n            }\n\n            case \"estimateGas\": {\n                const postData = this._getTransactionPostData(req.transaction);\n                postData.module = \"proxy\";\n                postData.action = \"eth_estimateGas\";\n\n                try {\n                    return await this.fetch(\"proxy\", postData, true);\n                } catch (error) {\n                    return this._checkError(req, <Error>error, req.transaction);\n                }\n            }\n/*\n            case \"getLogs\": {\n                // Needs to complain if more than one address is passed in\n                const args: Record<string, any> = { action: \"getLogs\" }\n\n                if (params.filter.fromBlock) {\n                    args.fromBlock = checkLogTag(params.filter.fromBlock);\n                }\n\n                if (params.filter.toBlock) {\n                    args.toBlock = checkLogTag(params.filter.toBlock);\n                }\n\n                if (params.filter.address) {\n                    args.address = params.filter.address;\n                }\n\n                // @TODO: We can handle slightly more complicated logs using the logs API\n                if (params.filter.topics && params.filter.topics.length > 0) {\n                    if (params.filter.topics.length > 1) {\n                        logger.throwError(\"unsupported topic count\", Logger.Errors.UNSUPPORTED_OPERATION, { topics: params.filter.topics });\n                    }\n                    if (params.filter.topics.length === 1) {\n                        const topic0 = params.filter.topics[0];\n                        if (typeof(topic0) !== \"string\" || topic0.length !== 66) {\n                            logger.throwError(\"unsupported topic format\", Logger.Errors.UNSUPPORTED_OPERATION, { topic0: topic0 });\n                        }\n                        args.topic0 = topic0;\n                    }\n                }\n\n                const logs: Array<any> = await this.fetch(\"logs\", args);\n\n                // Cache txHash => blockHash\n                let blocks: { [tag: string]: string } = {};\n\n                // Add any missing blockHash to the logs\n                for (let i = 0; i < logs.length; i++) {\n                    const log = logs[i];\n                    if (log.blockHash != null) { continue; }\n                    if (blocks[log.blockNumber] == null) {\n                        const block = await this.getBlock(log.blockNumber);\n                        if (block) {\n                            blocks[log.blockNumber] = block.hash;\n                        }\n                    }\n\n                    log.blockHash = blocks[log.blockNumber];\n                }\n\n                return logs;\n            }\n*/\n            default:\n                break;\n        }\n\n        return super._perform(req);\n    }\n\n    async getNetwork(): Promise<Network> {\n        return this.network;\n    }\n\n    /**\n     *  Resolves to the current price of ether.\n     *\n     *  This returns ``0`` on any network other than ``mainnet``.\n     */\n    async getEtherPrice(): Promise<number> {\n        if (this.network.name !== \"mainnet\") { return 0.0; }\n        return parseFloat((await this.fetch(\"stats\", { action: \"ethprice\" })).ethusd);\n    }\n\n    /**\n     *  Resolves to a [Contract]] for %%address%%, using the\n     *  Etherscan API to retreive the Contract ABI.\n     */\n    async getContract(_address: string): Promise<null | Contract> {\n        let address = this._getAddress(_address);\n        if (isPromise(address)) { address = await address; }\n\n        try {\n            const resp = await this.fetch(\"contract\", {\n            action: \"getabi\", address });\n            const abi = JSON.parse(resp);\n            return new Contract(address, abi, this);\n        } catch (error) {\n            return null;\n        }\n    }\n\n    isCommunityResource(): boolean {\n        return (this.apiKey == null);\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,aAAa,EAAEC,WAAW,QAAQ,yBAAyB;AACpE,SACIC,gBAAgB,EAChBC,OAAO,EAAEC,UAAU,EACnBC,YAAY,EACZC,MAAM,EAAEC,cAAc,EAAEC,OAAO;AACnC;AACIC,YAAY,QACR,mBAAmB;AAE3B,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,mBAAmB,QAAQ,gBAAgB;AAOpD,MAAMC,QAAQ,GAAG,IAAI;AAErB,SAASC,SAASA,CAAUC,KAAU;EAClC,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACC,IAAK,KAAK,UAAU;AACtD;AAyBA,MAAMC,iBAAiB,GAAG,uCAAuC;AAEjE;;;;;;AAMA,OAAM,MAAOC,eAAgB,SAAQP,aAAa;EAC9C;;;EAGSQ,OAAO;EAEhB;;;;EAIAC,YAAYD,OAAe;IACvB,KAAK,CAACF,iBAAiB,CAAC;IACxBhB,gBAAgB,CAAkB,IAAI,EAAE;MAAEkB;IAAO,CAAE,CAAC;EACxD;EAEAE,KAAKA,CAAA;IACD,OAAO,IAAIH,eAAe,CAAC,IAAI,CAACC,OAAO,CAAC;EAC5C;;AAGJ,MAAMG,QAAQ,GAAG,CAAE,gBAAgB,CAAE;AAErC,IAAIC,MAAM,GAAG,CAAC;AAEd;;;;;;;;;;AAUA,OAAM,MAAOC,iBAAkB,SAAQf,gBAAgB;EAEnD;;;EAGSgB,OAAO;EAEhB;;;EAGSC,MAAM;EAEN,CAAAC,MAAO;EAEhB;;;EAGAP,YAAYQ,QAAqB,EAAEC,OAAgB;IAC/C,MAAMH,MAAM,GAAIG,OAAO,IAAI,IAAI,GAAIA,OAAO,GAAE,IAAI;IAEhD,KAAK,EAAE;IAEP,MAAMJ,OAAO,GAAGf,OAAO,CAACoB,IAAI,CAACF,QAAQ,CAAC;IAEtC,IAAI,CAAC,CAAAD,MAAO,GAAGF,OAAO,CAACM,SAAS,CAAkBd,iBAAiB,CAAC;IAEpEhB,gBAAgB,CAAoB,IAAI,EAAE;MAAEyB,MAAM;MAAED;IAAO,CAAE,CAAC;EAClE;EAEA;;;;;;;;;;;;EAYAO,UAAUA,CAAA;IACN,IAAI,IAAI,CAAC,CAAAL,MAAO,EAAE;MAAE,OAAO,IAAI,CAAC,CAAAA,MAAO,CAACR,OAAO;;IAE/C,QAAO,IAAI,CAACM,OAAO,CAACQ,IAAI;MACpB,KAAK,SAAS;QACV,OAAO,2BAA2B;MACtC,KAAK,QAAQ;QACT,OAAO,kCAAkC;MAC7C,KAAK,SAAS;QACV,OAAO,mCAAmC;MAC9C,KAAK,SAAS;QACV,OAAO,mCAAmC;MAE9C,KAAK,UAAU;QACX,OAAO,0BAA0B;MACrC,KAAK,iBAAiB;QAClB,OAAO,iCAAiC;MAC7C,KAAK,MAAM;QACN,OAAO,2BAA2B;MACtC,KAAK,cAAc;QACf,OAAO,mCAAmC;MAC9C,KAAK,KAAK;QACN,OAAO,0BAA0B;MACrC,KAAK,MAAM;QACP,OAAO,kCAAkC;MAC7C,KAAK,OAAO;QACR,OAAO,8BAA8B;MACzC,KAAK,YAAY;QACb,OAAO,mCAAmC;MAC9C,KAAK,cAAc;QACf,OAAO,sCAAsC;MACjD,KAAK,UAAU;QACX,OAAO,sCAAsC;MACjD,KAAK,iBAAiB;QAClB,OAAO,6CAA6C;MAExD;;IAGJ3B,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAE,IAAI,CAACmB,OAAO,CAAC;EACzE;EAEA;;;EAGAS,MAAMA,CAACC,MAAc,EAAEC,MAA8B;IACjD,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;MAClD,MAAM3B,KAAK,GAAGqB,MAAM,CAACM,GAAG,CAAC;MACzB,IAAI3B,KAAK,IAAI,IAAI,EAAE;QACf0B,KAAK,IAAI,IAAKC,GAAI,IAAK3B,KAAM,EAAE;;MAEnC,OAAO0B,KAAK;IAChB,CAAC,EAAE,EAAE,CAAC;IACN,IAAI,IAAI,CAACf,MAAM,EAAE;MAAEW,KAAK,IAAI,WAAY,IAAI,CAACX,MAAO,EAAE;;IACtD,OAAO,4CAA6C,IAAI,CAACD,OAAO,CAACkB,OAAQ,WAAYR,MAAO,GAAIE,KAAM,EAAE;EAC5G;EAEA;;;EAGAO,UAAUA,CAAA;IACN,OAAO,4CAA6C,IAAI,CAACnB,OAAO,CAACkB,OAAQ,EAAE;EAC/E;EAEA;;;EAGAE,WAAWA,CAACV,MAAc,EAAEC,MAA2B;IACnDA,MAAM,CAACD,MAAM,GAAGA,MAAM;IACtBC,MAAM,CAACU,MAAM,GAAG,IAAI,CAACpB,MAAM;IAC3BU,MAAM,CAACW,OAAO,GAAG,IAAI,CAACtB,OAAO,CAACkB,OAAO;IACrC,OAAOP,MAAM;EACjB;EAEA,MAAMY,aAAaA,CAAA;IACf,OAAO,IAAI,CAACvB,OAAO;EACvB;EAEA;;;;;EAKA,MAAMwB,KAAKA,CAACd,MAAc,EAAEC,MAA2B,EAAEc,IAAc;IACnE,MAAMC,EAAE,GAAG5B,MAAM,EAAE;IAEnB,MAAM6B,GAAG,GAAIF,IAAI,GAAG,IAAI,CAACN,UAAU,EAAE,GAAE,IAAI,CAACV,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAE;IACnE,MAAMiB,OAAO,GAAIH,IAAI,GAAG,IAAI,CAACL,WAAW,CAACV,MAAM,EAAEC,MAAM,CAAC,GAAE,IAAK;IAE/D,IAAI,CAACkB,IAAI,CAAC,OAAO,EAAE;MAAEC,MAAM,EAAE,aAAa;MAAEJ,EAAE;MAAEC,GAAG;MAAEC,OAAO,EAAEA;IAAO,CAAE,CAAC;IAExE,MAAMG,OAAO,GAAG,IAAIpD,YAAY,CAACgD,GAAG,CAAC;IACrCI,OAAO,CAACC,iBAAiB,CAAC;MAAEC,YAAY,EAAE;IAAI,CAAE,CAAC;IACjDF,OAAO,CAACG,SAAS,GAAG,CAACC,GAAG,EAAEC,IAAI,EAAEC,OAAe,KAAI;MAC/C,IAAI,IAAI,CAACC,mBAAmB,EAAE,EAAE;QAC5BnD,mBAAmB,CAAC,WAAW,CAAC;;MAEpC,OAAOoD,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;IAChC,CAAC;IACDT,OAAO,CAACU,WAAW,GAAG,OAAOV,OAAO,EAAEW,QAAQ,KAAI;MAC9C,MAAMC,MAAM,GAAGD,QAAQ,CAACE,OAAO,EAAE,GAAGC,IAAI,CAACC,KAAK,CAAC/D,YAAY,CAAC2D,QAAQ,CAACK,IAAI,CAAC,CAAC,GAAE,EAAG;MAChF,MAAMC,QAAQ,GAAG,CAAE,OAAOL,MAAM,CAACA,MAAO,KAAK,QAAQ,GAAIA,MAAM,CAACA,MAAM,GAAE,EAAE,EAAEM,WAAW,EAAE,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;MACpH,IAAIxC,MAAM,KAAK,OAAO,EAAE;QACpB;QACA,IAAIiC,MAAM,IAAIA,MAAM,CAACQ,MAAM,IAAI,CAAC,IAAIR,MAAM,CAACS,OAAO,IAAI,OAAO,IAAIJ,QAAQ,EAAE;UACvE,IAAI,CAACnB,IAAI,CAAC,OAAO,EAAE;YAAEC,MAAM,EAAE,cAAc;YAAEJ,EAAE;YAAE2B,MAAM,EAAE,aAAa;YAAEC,KAAK,EAAEX;UAAM,CAAE,CAAC;UACxFD,QAAQ,CAACa,kBAAkB,CAACZ,MAAM,CAACA,MAAM,EAAEvD,QAAQ,CAAC;;OAE3D,MAAM;QACH,IAAI4D,QAAQ,EAAE;UACV,IAAI,CAACnB,IAAI,CAAC,OAAO,EAAE;YAAEC,MAAM,EAAE,cAAc;YAAEJ,EAAE;YAAE2B,MAAM,EAAE,aAAa;YAAEC,KAAK,EAAEX,MAAM,CAACA;UAAM,CAAE,CAAC;UAC/FD,QAAQ,CAACa,kBAAkB,CAACZ,MAAM,CAACA,MAAM,EAAEvD,QAAQ,CAAC;;;MAG5D,OAAOsD,QAAQ;IACnB,CAAC;IAED,IAAId,OAAO,EAAE;MACTG,OAAO,CAACyB,SAAS,CAAC,cAAc,EAAE,kDAAkD,CAAC;MACrFzB,OAAO,CAACgB,IAAI,GAAGlC,MAAM,CAACC,IAAI,CAACc,OAAO,CAAC,CAAC6B,GAAG,CAAEC,CAAC,IAAK,GAAIA,CAAE,IAAK9B,OAAO,CAAC8B,CAAC,CAAE,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;IAGtF,MAAMjB,QAAQ,GAAG,MAAMX,OAAO,CAAC6B,IAAI,EAAE;IACrC,IAAI;MACAlB,QAAQ,CAACmB,QAAQ,EAAE;KACtB,CAAC,OAAOP,KAAK,EAAE;MACZ,IAAI,CAACzB,IAAI,CAAC,OAAO,EAAE;QAAEC,MAAM,EAAE,cAAc;QAAEJ,EAAE;QAAE4B,KAAK;QAAED,MAAM,EAAE;MAAU,CAAE,CAAC;MAC7EzE,MAAM,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE;QAAEmD,OAAO;QAAEW;MAAQ,CAAE,CAAC;;IAG1E,IAAI,CAACA,QAAQ,CAACE,OAAO,EAAE,EAAE;MACrB,IAAI,CAACf,IAAI,CAAC,OAAO,EAAE;QAAEC,MAAM,EAAE,cAAc;QAAEJ,EAAE;QAAE4B,KAAK,EAAE,cAAc;QAAED,MAAM,EAAE;MAAW,CAAE,CAAC;MAC9FzE,MAAM,CAAC,KAAK,EAAE,kBAAkB,EAAE,cAAc,EAAE;QAAEmD,OAAO;QAAEW;MAAQ,CAAE,CAAC;;IAG5E,MAAMC,MAAM,GAAGE,IAAI,CAACC,KAAK,CAAC/D,YAAY,CAAC2D,QAAQ,CAACK,IAAI,CAAC,CAAC;IACtD,IAAIrC,MAAM,KAAK,OAAO,EAAE;MACpB,IAAIiC,MAAM,CAACmB,OAAO,IAAI,KAAK,EAAE;QACzB,IAAI,CAACjC,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,cAAc;UAAEJ,EAAE;UAAEiB,MAAM;UAAEU,MAAM,EAAE;QAAkB,CAAE,CAAC;QACtFzE,MAAM,CAAC,KAAK,EAAE,mDAAmD,EAAE,cAAc,EAAE;UAAEmD,OAAO;UAAEW,QAAQ;UAAEqB,IAAI,EAAE;YAAEpB;UAAM;QAAE,CAAE,CAAC;;MAG/H,IAAIA,MAAM,CAACW,KAAK,EAAE;QACd,IAAI,CAACzB,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,cAAc;UAAEJ,EAAE;UAAEiB,MAAM;UAAEU,MAAM,EAAE;QAAgB,CAAE,CAAC;QACpFzE,MAAM,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE;UAAEmD,OAAO;UAAEW,QAAQ;UAAEqB,IAAI,EAAE;YAAEpB;UAAM;QAAE,CAAE,CAAC;;MAG5F,IAAI,CAACd,IAAI,CAAC,OAAO,EAAE;QAAEC,MAAM,EAAE,gBAAgB;QAAEJ,EAAE;QAAEiB;MAAM,CAAE,CAAC;MAE5D,OAAOA,MAAM,CAACA,MAAM;KAEvB,MAAM;MACH;MACA,IAAIA,MAAM,CAACQ,MAAM,IAAI,CAAC,KAAKR,MAAM,CAACS,OAAO,KAAK,kBAAkB,IAAIT,MAAM,CAACS,OAAO,KAAK,uBAAuB,CAAC,EAAE;QAC7G,IAAI,CAACvB,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,gBAAgB;UAAEJ,EAAE;UAAEiB;QAAM,CAAE,CAAC;QAC5D,OAAOA,MAAM,CAACA,MAAM;;MAGxB,IAAIA,MAAM,CAACQ,MAAM,IAAI,CAAC,IAAK,OAAOR,MAAM,CAACS,OAAQ,KAAK,QAAQ,IAAI,CAACT,MAAM,CAACS,OAAO,CAACY,KAAK,CAAC,KAAK,CAAE,EAAE;QAC7F,IAAI,CAACnC,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,cAAc;UAAEJ,EAAE;UAAEiB;QAAM,CAAE,CAAC;QAC1D/D,MAAM,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE;UAAEmD,OAAO;UAAEW,QAAQ;UAAEqB,IAAI,EAAE;YAAEpB;UAAM;QAAE,CAAE,CAAC;;MAG5F,IAAI,CAACd,IAAI,CAAC,OAAO,EAAE;QAAEC,MAAM,EAAE,gBAAgB;QAAEJ,EAAE;QAAEiB;MAAM,CAAE,CAAC;MAE5D,OAAOA,MAAM,CAACA,MAAM;;EAE5B;EAEA;;;EAGAsB,uBAAuBA,CAACC,WAA+B;IACnD,MAAMvB,MAAM,GAA2B,EAAG;IAC1C,KAAK,IAAI1B,GAAG,IAAIiD,WAAW,EAAE;MACzB,IAAIrE,QAAQ,CAACqD,OAAO,CAACjC,GAAG,CAAC,IAAI,CAAC,EAAE;QAAE;;MAElC,IAAUiD,WAAY,CAACjD,GAAG,CAAC,IAAI,IAAI,EAAE;QAAE;;MACvC,IAAI3B,KAAK,GAAS4E,WAAY,CAACjD,GAAG,CAAC;MACnC,IAAIA,GAAG,KAAK,MAAM,IAAI3B,KAAK,KAAK,CAAC,EAAE;QAAE;;MACrC,IAAI2B,GAAG,KAAK,UAAU,IAAI3B,KAAK,KAAK,QAAQ,EAAE;QAAE;;MAEhD;MACA,IAAU;QAAE6E,IAAI,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI;QAAEC,QAAQ,EAAE,IAAI;QAAEC,WAAW,EAAE,IAAI;QAAEC,oBAAoB,EAAE,IAAI;QAAEC,KAAK,EAAE,IAAI;QAAElF,KAAK,EAAE;MAAI,CAAG,CAAC2B,GAAG,CAAC,EAAE;QACrI3B,KAAK,GAAGZ,UAAU,CAACY,KAAK,CAAC;OAE5B,MAAM,IAAI2B,GAAG,KAAK,YAAY,EAAE;QAC7B3B,KAAK,GAAG,GAAG,GAAGhB,aAAa,CAACgB,KAAK,CAAC,CAACmE,GAAG,CAAEgB,GAAG,IAAI;UAC3C,OAAO,aAAcA,GAAG,CAACC,OAAQ,mBAAoBD,GAAG,CAACE,WAAW,CAAChB,IAAI,CAAC,KAAK,CAAE,KAAK;QAC1F,CAAC,CAAC,CAACA,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;OAErB,MAAM,IAAI1C,GAAG,KAAK,qBAAqB,EAAE;QACtC,IAAI3B,KAAK,CAACsF,MAAM,KAAK,CAAC,EAAE;UAAE;;QAE1B;QACAhG,MAAM,CAAC,KAAK,EAAE,oDAAoD,EAAE,uBAAuB,EAAE;UACzFiG,SAAS,EAAE,yBAAyB;UACpCd,IAAI,EAAE;YAAEG;UAAW;SACtB,CAAC;OAEL,MAAM;QACH5E,KAAK,GAAGb,OAAO,CAACa,KAAK,CAAC;;MAE1BqD,MAAM,CAAC1B,GAAG,CAAC,GAAG3B,KAAK;;IAEvB,OAAOqD,MAAM;EACjB;EAEA;;;EAGAmC,WAAWA,CAAC3C,GAAyB,EAAEmB,KAAY,EAAEY,WAAgB;IACjE;IACA,IAAId,OAAO,GAAG,EAAE;IAChB,IAAItE,OAAO,CAACwE,KAAK,EAAE,cAAc,CAAC,EAAE;MAChC;MACA,IAAI;QACAF,OAAO,GAASE,KAAM,CAACS,IAAI,CAACpB,MAAM,CAACW,KAAK,CAACF,OAAO;OACnD,CAAC,OAAO2B,CAAC,EAAE;MAEZ,IAAI,CAAC3B,OAAO,EAAE;QACV,IAAI;UACAA,OAAO,GAASE,KAAM,CAACS,IAAI,CAACX,OAAO;SACtC,CAAC,OAAO2B,CAAC,EAAE;;;IAIpB,IAAI5C,GAAG,CAAC6C,MAAM,KAAK,aAAa,EAAE;MAC9B,IAAI,CAAC5B,OAAO,CAACY,KAAK,CAAC,SAAS,CAAC,IAAIZ,OAAO,CAACY,KAAK,CAAC,qBAAqB,CAAC,EAAE;QACnEpF,MAAM,CAAC,KAAK,EAAE,oBAAoB,EAAE,oBAAoB,EAAE;UACtDsF,WAAW,EAAE/B,GAAG,CAAC+B;SACpB,CAAC;;;IAIV,IAAI/B,GAAG,CAAC6C,MAAM,KAAK,MAAM,IAAI7C,GAAG,CAAC6C,MAAM,KAAK,aAAa,EAAE;MACvD,IAAI5B,OAAO,CAACY,KAAK,CAAC,qBAAqB,CAAC,EAAE;QACtC,IAAIiB,IAAI,GAAG,EAAE;QACb,IAAI;UACAA,IAAI,GAAS3B,KAAM,CAACS,IAAI,CAACpB,MAAM,CAACW,KAAK,CAAC2B,IAAI;SAC7C,CAAC,OAAO3B,KAAK,EAAE;QAEhB,MAAMyB,CAAC,GAAG3G,QAAQ,CAAC8G,uBAAuB,CAAC/C,GAAG,CAAC6C,MAAM,EAAO7C,GAAG,CAAC+B,WAAW,EAAEe,IAAI,CAAC;QAClFF,CAAC,CAAChB,IAAI,GAAG;UAAEhC,OAAO,EAAEI,GAAG;UAAEmB;QAAK,CAAE;QAChC,MAAMyB,CAAC;;;IAIf,IAAI3B,OAAO,EAAE;MACT,IAAIjB,GAAG,CAAC6C,MAAM,KAAK,sBAAsB,EAAE;QACvC,MAAMd,WAAW,GAAG3F,WAAW,CAAC8B,IAAI,CAAC8B,GAAG,CAACgD,iBAAiB,CAAC;QAC3D,IAAI/B,OAAO,CAACY,KAAK,CAAC,cAAc,CAAC,IAAIZ,OAAO,CAACY,KAAK,CAAC,cAAc,CAAC,EAAE;UAChEpF,MAAM,CAAC,KAAK,EAAE,yBAAyB,EAAE,yBAAyB,EAAE;YAChEsF;WACH,CAAC;;QAGN,IAAId,OAAO,CAACY,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrCpF,MAAM,CAAC,KAAK,EAAE,mDAAmD,EAAE,oBAAoB,EAAE;YACtFsF;WACF,CAAC;;QAGN,IAAId,OAAO,CAACY,KAAK,CAAC,2EAA2E,CAAC,EAAE;UAC5FpF,MAAM,CAAC,KAAK,EAAE,6BAA6B,EAAE,eAAe,EAAE;YAC3DsF;WACF,CAAC;;;;IAKd;IACA,MAAMZ,KAAK;EACf;EAEA,MAAM8B,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACpF,OAAO;EACvB;EAEA,MAAMqF,QAAQA,CAAClD,GAAyB;IACpC,QAAQA,GAAG,CAAC6C,MAAM;MACd,KAAK,SAAS;QACV,OAAO,IAAI,CAAChF,OAAO,CAACkB,OAAO;MAE/B,KAAK,gBAAgB;QACjB,OAAO,IAAI,CAACM,KAAK,CAAC,OAAO,EAAE;UAAEM,MAAM,EAAE;QAAiB,CAAE,CAAC;MAE7D,KAAK,aAAa;QACd,OAAO,IAAI,CAACN,KAAK,CAAC,OAAO,EAAE;UAAEM,MAAM,EAAE;QAAc,CAAE,CAAC;MAE1D,KAAK,gBAAgB;QACjB;QACA,IAAI,IAAI,CAAC9B,OAAO,CAACQ,IAAI,KAAK,SAAS,EAAE;UACjC,OAAO,YAAY;SACtB,MAAM,IAAI,IAAI,CAACR,OAAO,CAACQ,IAAI,KAAK,UAAU,EAAE;UACzC,OAAO,SAAS;SACnB,MAAM;UACH,MAAM,IAAI8E,KAAK,CAAC,4CAA4C,CAAC;;MAEjE;;;;;;;;;;;;MAYA;;;;;;;;;;;;;;;MAgBJ,KAAK,YAAY;QACb;QACA,OAAO,IAAI,CAAC9D,KAAK,CAAC,SAAS,EAAE;UACzBM,MAAM,EAAE,SAAS;UACjB4C,OAAO,EAAEvC,GAAG,CAACuC,OAAO;UACpBa,GAAG,EAAEpD,GAAG,CAACqD;SACZ,CAAC;MAEP,KAAK,qBAAqB;QACrB,OAAO,IAAI,CAAChE,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,yBAAyB;UACjC4C,OAAO,EAAEvC,GAAG,CAACuC,OAAO;UACpBa,GAAG,EAAEpD,GAAG,CAACqD;SACZ,CAAC;MAEN,KAAK,SAAS;QACV,OAAO,IAAI,CAAChE,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,aAAa;UACrB4C,OAAO,EAAEvC,GAAG,CAACuC,OAAO;UACpBa,GAAG,EAAEpD,GAAG,CAACqD;SACZ,CAAC;MAEN,KAAK,YAAY;QACb,OAAO,IAAI,CAAChE,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,kBAAkB;UAC1B4C,OAAO,EAAEvC,GAAG,CAACuC,OAAO;UACpBe,QAAQ,EAAEtD,GAAG,CAACsD,QAAQ;UACtBF,GAAG,EAAEpD,GAAG,CAACqD;SACZ,CAAC;MAEN,KAAK,sBAAsB;QACvB,OAAO,IAAI,CAAChE,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,wBAAwB;UAChC4D,GAAG,EAAEvD,GAAG,CAACgD;SACZ,EAAE,IAAI,CAAC,CAACQ,KAAK,CAAErC,KAAK,IAAI;UACrB,OAAO,IAAI,CAACwB,WAAW,CAAC3C,GAAG,EAASmB,KAAK,EAAEnB,GAAG,CAACgD,iBAAiB,CAAC;QACrE,CAAC,CAAC;MAEN,KAAK,UAAU;QACX,IAAI,UAAU,IAAIhD,GAAG,EAAE;UACnB,OAAO,IAAI,CAACX,KAAK,CAAC,OAAO,EAAE;YACvBM,MAAM,EAAE,sBAAsB;YAC9ByD,GAAG,EAAEpD,GAAG,CAACqD,QAAQ;YACjBI,OAAO,EAAGzD,GAAG,CAAC0D,mBAAmB,GAAG,MAAM,GAAE;WAC/C,CAAC;;QAGNjH,MAAM,CAAC,KAAK,EAAE,kDAAkD,EAAE,uBAAuB,EAAE;UACvFiG,SAAS,EAAE;SACd,CAAC;MAEN,KAAK,gBAAgB;QACjB,OAAO,IAAI,CAACrD,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,0BAA0B;UAClCgE,MAAM,EAAE3D,GAAG,CAAC4D;SACf,CAAC;MAEN,KAAK,uBAAuB;QACxB,OAAO,IAAI,CAACvE,KAAK,CAAC,OAAO,EAAE;UACvBM,MAAM,EAAE,2BAA2B;UACnCgE,MAAM,EAAE3D,GAAG,CAAC4D;SACf,CAAC;MAEN,KAAK,MAAM;QAAE;UACT,IAAI5D,GAAG,CAACqD,QAAQ,KAAK,QAAQ,EAAE;YAC3B,MAAM,IAAIF,KAAK,CAAC,sDAAsD,CAAC;;UAG3E,MAAMU,QAAQ,GAAG,IAAI,CAAC/B,uBAAuB,CAAC9B,GAAG,CAAC+B,WAAW,CAAC;UAC9D8B,QAAQ,CAACtF,MAAM,GAAG,OAAO;UACzBsF,QAAQ,CAAClE,MAAM,GAAG,UAAU;UAE5B,IAAI;YACA,OAAO,MAAM,IAAI,CAACN,KAAK,CAAC,OAAO,EAAEwE,QAAQ,EAAE,IAAI,CAAC;WACnD,CAAC,OAAO1C,KAAK,EAAE;YACZ,OAAO,IAAI,CAACwB,WAAW,CAAC3C,GAAG,EAASmB,KAAK,EAAEnB,GAAG,CAAC+B,WAAW,CAAC;;;MAInE,KAAK,aAAa;QAAE;UAChB,MAAM8B,QAAQ,GAAG,IAAI,CAAC/B,uBAAuB,CAAC9B,GAAG,CAAC+B,WAAW,CAAC;UAC9D8B,QAAQ,CAACtF,MAAM,GAAG,OAAO;UACzBsF,QAAQ,CAAClE,MAAM,GAAG,iBAAiB;UAEnC,IAAI;YACA,OAAO,MAAM,IAAI,CAACN,KAAK,CAAC,OAAO,EAAEwE,QAAQ,EAAE,IAAI,CAAC;WACnD,CAAC,OAAO1C,KAAK,EAAE;YACZ,OAAO,IAAI,CAACwB,WAAW,CAAC3C,GAAG,EAASmB,KAAK,EAAEnB,GAAG,CAAC+B,WAAW,CAAC;;;MAG/E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAqDY;QACI;;IAGR,OAAO,KAAK,CAACmB,QAAQ,CAAClD,GAAG,CAAC;EAC9B;EAEA,MAAM8D,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACjG,OAAO;EACvB;EAEA;;;;;EAKA,MAAMkG,aAAaA,CAAA;IACf,IAAI,IAAI,CAAClG,OAAO,CAACQ,IAAI,KAAK,SAAS,EAAE;MAAE,OAAO,GAAG;;IACjD,OAAO2F,UAAU,CAAC,CAAC,MAAM,IAAI,CAAC3E,KAAK,CAAC,OAAO,EAAE;MAAEM,MAAM,EAAE;IAAU,CAAE,CAAC,EAAEsE,MAAM,CAAC;EACjF;EAEA;;;;EAIA,MAAMC,WAAWA,CAACC,QAAgB;IAC9B,IAAI5B,OAAO,GAAG,IAAI,CAAC6B,WAAW,CAACD,QAAQ,CAAC;IACxC,IAAIjH,SAAS,CAACqF,OAAO,CAAC,EAAE;MAAEA,OAAO,GAAG,MAAMA,OAAO;;IAEjD,IAAI;MACA,MAAMtC,IAAI,GAAG,MAAM,IAAI,CAACZ,KAAK,CAAC,UAAU,EAAE;QAC1CM,MAAM,EAAE,QAAQ;QAAE4C;OAAS,CAAC;MAC5B,MAAM8B,GAAG,GAAG3D,IAAI,CAACC,KAAK,CAACV,IAAI,CAAC;MAC5B,OAAO,IAAI/D,QAAQ,CAACqG,OAAO,EAAE8B,GAAG,EAAE,IAAI,CAAC;KAC1C,CAAC,OAAOlD,KAAK,EAAE;MACZ,OAAO,IAAI;;EAEnB;EAEAhB,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACrC,MAAM,IAAI,IAAI;EAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}