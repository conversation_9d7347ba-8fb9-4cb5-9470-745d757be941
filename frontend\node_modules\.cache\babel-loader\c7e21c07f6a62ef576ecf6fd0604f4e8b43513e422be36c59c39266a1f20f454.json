{"ast": null, "code": "/**\n *  The [Base58 Encoding](link-base58) scheme allows a **numeric** value\n *  to be encoded as a compact string using a radix of 58 using only\n *  alpha-numeric characters. Confusingly similar characters are omitted\n *  (i.e. ``\"l0O\"``).\n *\n *  Note that Base58 encodes a **numeric** value, not arbitrary bytes,\n *  since any zero-bytes on the left would get removed. To mitigate this\n *  issue most schemes that use Base58 choose specific high-order values\n *  to ensure non-zero prefixes.\n *\n *  @_subsection: api/utils:Base58 Encoding [about-base58]\n */\nimport { getBytes } from \"./data.js\";\nimport { assertArgument } from \"./errors.js\";\nimport { toBigInt } from \"./maths.js\";\nconst Alphabet = \"**********************************************************\";\nlet Lookup = null;\nfunction getAlpha(letter) {\n  if (Lookup == null) {\n    Lookup = {};\n    for (let i = 0; i < Alphabet.length; i++) {\n      Lookup[Alphabet[i]] = BigInt(i);\n    }\n  }\n  const result = Lookup[letter];\n  assertArgument(result != null, `invalid base58 value`, \"letter\", letter);\n  return result;\n}\nconst BN_0 = BigInt(0);\nconst BN_58 = BigInt(58);\n/**\n *  Encode %%value%% as a Base58-encoded string.\n */\nexport function encodeBase58(_value) {\n  const bytes = getBytes(_value);\n  let value = toBigInt(bytes);\n  let result = \"\";\n  while (value) {\n    result = Alphabet[Number(value % BN_58)] + result;\n    value /= BN_58;\n  }\n  // Account for leading padding zeros\n  for (let i = 0; i < bytes.length; i++) {\n    if (bytes[i]) {\n      break;\n    }\n    result = Alphabet[0] + result;\n  }\n  return result;\n}\n/**\n *  Decode the Base58-encoded %%value%%.\n */\nexport function decodeBase58(value) {\n  let result = BN_0;\n  for (let i = 0; i < value.length; i++) {\n    result *= BN_58;\n    result += getAlpha(value[i]);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["getBytes", "assertArgument", "toBigInt", "Alphabet", "Lookup", "get<PERSON><PERSON><PERSON>", "letter", "i", "length", "BigInt", "result", "BN_0", "BN_58", "encodeBase58", "_value", "bytes", "value", "Number", "decodeBase58"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\base58.ts"], "sourcesContent": ["/**\n *  The [Base58 Encoding](link-base58) scheme allows a **numeric** value\n *  to be encoded as a compact string using a radix of 58 using only\n *  alpha-numeric characters. Confusingly similar characters are omitted\n *  (i.e. ``\"l0O\"``).\n *\n *  Note that Base58 encodes a **numeric** value, not arbitrary bytes,\n *  since any zero-bytes on the left would get removed. To mitigate this\n *  issue most schemes that use Base58 choose specific high-order values\n *  to ensure non-zero prefixes.\n *\n *  @_subsection: api/utils:Base58 Encoding [about-base58]\n */\n\nimport { getBytes } from \"./data.js\";\nimport { assertArgument } from \"./errors.js\";\nimport { toBigInt } from \"./maths.js\";\n\nimport type { BytesLike } from \"./index.js\";\n\n\nconst Alphabet = \"**********************************************************\";\nlet Lookup: null | Record<string, bigint> = null;\n\nfunction getAlpha(letter: string): bigint {\n    if (Lookup == null) {\n        Lookup = { };\n        for (let i = 0; i < Alphabet.length; i++) {\n            Lookup[Alphabet[i]] = BigInt(i);\n        }\n    }\n    const result = Lookup[letter];\n    assertArgument(result != null, `invalid base58 value`, \"letter\", letter);\n    return result;\n}\n\n\nconst BN_0 = BigInt(0);\nconst BN_58 = BigInt(58);\n\n/**\n *  Encode %%value%% as a Base58-encoded string.\n */\nexport function encodeBase58(_value: BytesLike): string {\n    const bytes = getBytes(_value);\n\n    let value = toBigInt(bytes);\n    let result = \"\";\n    while (value) {\n        result = Alphabet[Number(value % BN_58)] + result;\n        value /= BN_58;\n    }\n\n    // Account for leading padding zeros\n    for (let i = 0; i < bytes.length; i++) {\n        if (bytes[i]) { break; }\n        result = Alphabet[0] + result;\n    }\n\n    return result;\n}\n\n/**\n *  Decode the Base58-encoded %%value%%.\n */\nexport function decodeBase58(value: string): bigint {\n    let result = BN_0;\n    for (let i = 0; i < value.length; i++) {\n        result *= BN_58;\n        result += getAlpha(value[i]);\n    }\n    return result;\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAcA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,aAAa;AAC5C,SAASC,QAAQ,QAAQ,YAAY;AAKrC,MAAMC,QAAQ,GAAG,4DAA4D;AAC7E,IAAIC,MAAM,GAAkC,IAAI;AAEhD,SAASC,QAAQA,CAACC,MAAc;EAC5B,IAAIF,MAAM,IAAI,IAAI,EAAE;IAChBA,MAAM,GAAG,EAAG;IACZ,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCH,MAAM,CAACD,QAAQ,CAACI,CAAC,CAAC,CAAC,GAAGE,MAAM,CAACF,CAAC,CAAC;;;EAGvC,MAAMG,MAAM,GAAGN,MAAM,CAACE,MAAM,CAAC;EAC7BL,cAAc,CAACS,MAAM,IAAI,IAAI,EAAE,sBAAsB,EAAE,QAAQ,EAAEJ,MAAM,CAAC;EACxE,OAAOI,MAAM;AACjB;AAGA,MAAMC,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMG,KAAK,GAAGH,MAAM,CAAC,EAAE,CAAC;AAExB;;;AAGA,OAAM,SAAUI,YAAYA,CAACC,MAAiB;EAC1C,MAAMC,KAAK,GAAGf,QAAQ,CAACc,MAAM,CAAC;EAE9B,IAAIE,KAAK,GAAGd,QAAQ,CAACa,KAAK,CAAC;EAC3B,IAAIL,MAAM,GAAG,EAAE;EACf,OAAOM,KAAK,EAAE;IACVN,MAAM,GAAGP,QAAQ,CAACc,MAAM,CAACD,KAAK,GAAGJ,KAAK,CAAC,CAAC,GAAGF,MAAM;IACjDM,KAAK,IAAIJ,KAAK;;EAGlB;EACA,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,KAAK,CAACP,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIQ,KAAK,CAACR,CAAC,CAAC,EAAE;MAAE;;IAChBG,MAAM,GAAGP,QAAQ,CAAC,CAAC,CAAC,GAAGO,MAAM;;EAGjC,OAAOA,MAAM;AACjB;AAEA;;;AAGA,OAAM,SAAUQ,YAAYA,CAACF,KAAa;EACtC,IAAIN,MAAM,GAAGC,IAAI;EACjB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,KAAK,CAACR,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCG,MAAM,IAAIE,KAAK;IACfF,MAAM,IAAIL,QAAQ,CAACW,KAAK,CAACT,CAAC,CAAC,CAAC;;EAEhC,OAAOG,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}