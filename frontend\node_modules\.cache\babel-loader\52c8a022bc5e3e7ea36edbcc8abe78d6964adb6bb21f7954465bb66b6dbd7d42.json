{"ast": null, "code": "import { SHA2 } from './_sha2.js';\nimport u64 from './_u64.js';\nimport { wrapConstructor } from './utils.js';\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */(() => u64.split(['0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc', '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118', '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2', '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694', '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65', '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5', '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4', '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70', '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df', '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b', '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30', '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8', '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8', '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3', '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec', '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b', '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178', '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b', '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c', '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'].map(n => BigInt(n))))();\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */new Uint32Array(80);\nexport class SHA512 extends SHA2 {\n  constructor() {\n    super(128, 64, 16, false);\n    // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n    // Also looks cleaner and easier to verify with spec.\n    // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0x6a09e667 | 0;\n    this.Al = 0xf3bcc908 | 0;\n    this.Bh = 0xbb67ae85 | 0;\n    this.Bl = 0x84caa73b | 0;\n    this.Ch = 0x3c6ef372 | 0;\n    this.Cl = 0xfe94f82b | 0;\n    this.Dh = 0xa54ff53a | 0;\n    this.Dl = 0x5f1d36f1 | 0;\n    this.Eh = 0x510e527f | 0;\n    this.El = 0xade682d1 | 0;\n    this.Fh = 0x9b05688c | 0;\n    this.Fl = 0x2b3e6c1f | 0;\n    this.Gh = 0x1f83d9ab | 0;\n    this.Gl = 0xfb41bd6b | 0;\n    this.Hh = 0x5be0cd19 | 0;\n    this.Hl = 0x137e2179 | 0;\n  }\n  // prettier-ignore\n  get() {\n    const {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl) {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32(offset += 4);\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let {\n      Ah,\n      Al,\n      Bh,\n      Bl,\n      Ch,\n      Cl,\n      Dh,\n      Dl,\n      Eh,\n      El,\n      Fh,\n      Fl,\n      Gh,\n      Gl,\n      Hh,\n      Hl\n    } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = Eh & Fh ^ ~Eh & Gh;\n      const CHIl = El & Fl ^ ~El & Gl;\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = Ah & Bh ^ Ah & Ch ^ Bh & Ch;\n      const MAJl = Al & Bl ^ Al & Cl ^ Bl & Cl;\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({\n        h: Eh,\n        l: El\n      } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({\n      h: Ah,\n      l: Al\n    } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({\n      h: Bh,\n      l: Bl\n    } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({\n      h: Ch,\n      l: Cl\n    } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({\n      h: Dh,\n      l: Dl\n    } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({\n      h: Eh,\n      l: El\n    } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({\n      h: Fh,\n      l: Fl\n    } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({\n      h: Gh,\n      l: Gl\n    } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({\n      h: Hh,\n      l: Hl\n    } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  roundClean() {\n    SHA512_W_H.fill(0);\n    SHA512_W_L.fill(0);\n  }\n  destroy() {\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\nclass SHA512_224 extends SHA512 {\n  constructor() {\n    super();\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0x8c3d37c8 | 0;\n    this.Al = 0x19544da2 | 0;\n    this.Bh = 0x73e19966 | 0;\n    this.Bl = 0x89dcd4d6 | 0;\n    this.Ch = 0x1dfab7ae | 0;\n    this.Cl = 0x32ff9c82 | 0;\n    this.Dh = 0x679dd514 | 0;\n    this.Dl = 0x582f9fcf | 0;\n    this.Eh = 0x0f6d2b69 | 0;\n    this.El = 0x7bd44da8 | 0;\n    this.Fh = 0x77e36f73 | 0;\n    this.Fl = 0x04c48942 | 0;\n    this.Gh = 0x3f9d85a8 | 0;\n    this.Gl = 0x6a1d36c8 | 0;\n    this.Hh = 0x1112e6ad | 0;\n    this.Hl = 0x91d692a1 | 0;\n    this.outputLen = 28;\n  }\n}\nclass SHA512_256 extends SHA512 {\n  constructor() {\n    super();\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0x22312194 | 0;\n    this.Al = 0xfc2bf72c | 0;\n    this.Bh = 0x9f555fa3 | 0;\n    this.Bl = 0xc84c64c2 | 0;\n    this.Ch = 0x2393b86b | 0;\n    this.Cl = 0x6f53b151 | 0;\n    this.Dh = 0x96387719 | 0;\n    this.Dl = 0x5940eabd | 0;\n    this.Eh = 0x96283ee2 | 0;\n    this.El = 0xa88effe3 | 0;\n    this.Fh = 0xbe5e1e25 | 0;\n    this.Fl = 0x53863992 | 0;\n    this.Gh = 0x2b0199fc | 0;\n    this.Gl = 0x2c85b8aa | 0;\n    this.Hh = 0x0eb72ddc | 0;\n    this.Hl = 0x81c52ca2 | 0;\n    this.outputLen = 32;\n  }\n}\nclass SHA384 extends SHA512 {\n  constructor() {\n    super();\n    // h -- high 32 bits, l -- low 32 bits\n    this.Ah = 0xcbbb9d5d | 0;\n    this.Al = 0xc1059ed8 | 0;\n    this.Bh = 0x629a292a | 0;\n    this.Bl = 0x367cd507 | 0;\n    this.Ch = 0x9159015a | 0;\n    this.Cl = 0x3070dd17 | 0;\n    this.Dh = 0x152fecd8 | 0;\n    this.Dl = 0xf70e5939 | 0;\n    this.Eh = 0x67332667 | 0;\n    this.El = 0xffc00b31 | 0;\n    this.Fh = 0x8eb44a87 | 0;\n    this.Fl = 0x68581511 | 0;\n    this.Gh = 0xdb0c2e0d | 0;\n    this.Gl = 0x64f98fa7 | 0;\n    this.Hh = 0x47b5481d | 0;\n    this.Hl = 0xbefa4fa4 | 0;\n    this.outputLen = 48;\n  }\n}\nexport const sha512 = /* @__PURE__ */wrapConstructor(() => new SHA512());\nexport const sha512_224 = /* @__PURE__ */wrapConstructor(() => new SHA512_224());\nexport const sha512_256 = /* @__PURE__ */wrapConstructor(() => new SHA512_256());\nexport const sha384 = /* @__PURE__ */wrapConstructor(() => new SHA384());", "map": {"version": 3, "names": ["SHA2", "u64", "wrapConstructor", "SHA512_Kh", "SHA512_Kl", "split", "map", "n", "BigInt", "SHA512_W_H", "Uint32Array", "SHA512_W_L", "SHA512", "constructor", "Ah", "Al", "Bh", "Bl", "Ch", "Cl", "Dh", "Dl", "Eh", "El", "Fh", "Fl", "Gh", "Gl", "Hh", "Hl", "get", "set", "process", "view", "offset", "i", "getUint32", "W15h", "W15l", "s0h", "rotrSH", "shrSH", "s0l", "rotrSL", "shrSL", "W2h", "W2l", "s1h", "rotrBH", "s1l", "rotrBL", "SUMl", "add4L", "SUMh", "add4H", "sigma1h", "sigma1l", "CHIh", "CHIl", "T1ll", "add5L", "T1h", "add5H", "T1l", "sigma0h", "sigma0l", "MAJh", "MAJl", "h", "l", "add", "All", "add3L", "add3H", "roundClean", "fill", "destroy", "buffer", "SHA512_224", "outputLen", "SHA512_256", "SHA384", "sha512", "sha512_224", "sha512_256", "sha384"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\sha512.ts"], "sourcesContent": ["import { SHA2 } from './_sha2.js';\nimport u64 from './_u64.js';\nimport { wrapConstructor } from './utils.js';\n\n// Round contants (first 32 bits of the fractional parts of the cube roots of the first 80 primes 2..409):\n// prettier-ignore\nconst [SHA512_Kh, SHA512_Kl] = /* @__PURE__ */ (() => u64.split([\n  '0x428a2f98d728ae22', '0x7137449123ef65cd', '0xb5c0fbcfec4d3b2f', '0xe9b5dba58189dbbc',\n  '0x3956c25bf348b538', '0x59f111f1b605d019', '0x923f82a4af194f9b', '0xab1c5ed5da6d8118',\n  '0xd807aa98a3030242', '0x12835b0145706fbe', '0x243185be4ee4b28c', '0x550c7dc3d5ffb4e2',\n  '0x72be5d74f27b896f', '0x80deb1fe3b1696b1', '0x9bdc06a725c71235', '0xc19bf174cf692694',\n  '0xe49b69c19ef14ad2', '0xefbe4786384f25e3', '0x0fc19dc68b8cd5b5', '0x240ca1cc77ac9c65',\n  '0x2de92c6f592b0275', '0x4a7484aa6ea6e483', '0x5cb0a9dcbd41fbd4', '0x76f988da831153b5',\n  '0x983e5152ee66dfab', '0xa831c66d2db43210', '0xb00327c898fb213f', '0xbf597fc7beef0ee4',\n  '0xc6e00bf33da88fc2', '0xd5a79147930aa725', '0x06ca6351e003826f', '0x142929670a0e6e70',\n  '0x27b70a8546d22ffc', '0x2e1b21385c26c926', '0x4d2c6dfc5ac42aed', '0x53380d139d95b3df',\n  '0x650a73548baf63de', '0x766a0abb3c77b2a8', '0x81c2c92e47edaee6', '0x92722c851482353b',\n  '0xa2bfe8a14cf10364', '0xa81a664bbc423001', '0xc24b8b70d0f89791', '0xc76c51a30654be30',\n  '0xd192e819d6ef5218', '0xd69906245565a910', '0xf40e35855771202a', '0x106aa07032bbd1b8',\n  '0x19a4c116b8d2d0c8', '0x1e376c085141ab53', '0x2748774cdf8eeb99', '0x34b0bcb5e19b48a8',\n  '0x391c0cb3c5c95a63', '0x4ed8aa4ae3418acb', '0x5b9cca4f7763e373', '0x682e6ff3d6b2b8a3',\n  '0x748f82ee5defb2fc', '0x78a5636f43172f60', '0x84c87814a1f0ab72', '0x8cc702081a6439ec',\n  '0x90befffa23631e28', '0xa4506cebde82bde9', '0xbef9a3f7b2c67915', '0xc67178f2e372532b',\n  '0xca273eceea26619c', '0xd186b8c721c0c207', '0xeada7dd6cde0eb1e', '0xf57d4f7fee6ed178',\n  '0x06f067aa72176fba', '0x0a637dc5a2c898a6', '0x113f9804bef90dae', '0x1b710b35131c471b',\n  '0x28db77f523047d84', '0x32caab7b40c72493', '0x3c9ebe0a15c9bebc', '0x431d67c49c100d4c',\n  '0x4cc5d4becb3e42b6', '0x597f299cfc657e2a', '0x5fcb6fab3ad6faec', '0x6c44198c4a475817'\n].map(n => BigInt(n))))();\n\n// Temporary buffer, not used to store anything between runs\nconst SHA512_W_H = /* @__PURE__ */ new Uint32Array(80);\nconst SHA512_W_L = /* @__PURE__ */ new Uint32Array(80);\nexport class SHA512 extends SHA2<SHA512> {\n  // We cannot use array here since array allows indexing by variable which means optimizer/compiler cannot use registers.\n  // Also looks cleaner and easier to verify with spec.\n  // Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n  // h -- high 32 bits, l -- low 32 bits\n  Ah = 0x6a09e667 | 0;\n  Al = 0xf3bcc908 | 0;\n  Bh = 0xbb67ae85 | 0;\n  Bl = 0x84caa73b | 0;\n  Ch = 0x3c6ef372 | 0;\n  Cl = 0xfe94f82b | 0;\n  Dh = 0xa54ff53a | 0;\n  Dl = 0x5f1d36f1 | 0;\n  Eh = 0x510e527f | 0;\n  El = 0xade682d1 | 0;\n  Fh = 0x9b05688c | 0;\n  Fl = 0x2b3e6c1f | 0;\n  Gh = 0x1f83d9ab | 0;\n  Gl = 0xfb41bd6b | 0;\n  Hh = 0x5be0cd19 | 0;\n  Hl = 0x137e2179 | 0;\n\n  constructor() {\n    super(128, 64, 16, false);\n  }\n  // prettier-ignore\n  protected get(): [\n    number, number, number, number, number, number, number, number,\n    number, number, number, number, number, number, number, number\n  ] {\n    const { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    return [Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl];\n  }\n  // prettier-ignore\n  protected set(\n    Ah: number, Al: number, Bh: number, Bl: number, Ch: number, Cl: number, Dh: number, Dl: number,\n    Eh: number, El: number, Fh: number, Fl: number, Gh: number, Gl: number, Hh: number, Hl: number\n  ) {\n    this.Ah = Ah | 0;\n    this.Al = Al | 0;\n    this.Bh = Bh | 0;\n    this.Bl = Bl | 0;\n    this.Ch = Ch | 0;\n    this.Cl = Cl | 0;\n    this.Dh = Dh | 0;\n    this.Dl = Dl | 0;\n    this.Eh = Eh | 0;\n    this.El = El | 0;\n    this.Fh = Fh | 0;\n    this.Fl = Fl | 0;\n    this.Gh = Gh | 0;\n    this.Gl = Gl | 0;\n    this.Hh = Hh | 0;\n    this.Hl = Hl | 0;\n  }\n  protected process(view: DataView, offset: number) {\n    // Extend the first 16 words into the remaining 64 words w[16..79] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) {\n      SHA512_W_H[i] = view.getUint32(offset);\n      SHA512_W_L[i] = view.getUint32((offset += 4));\n    }\n    for (let i = 16; i < 80; i++) {\n      // s0 := (w[i-15] rightrotate 1) xor (w[i-15] rightrotate 8) xor (w[i-15] rightshift 7)\n      const W15h = SHA512_W_H[i - 15] | 0;\n      const W15l = SHA512_W_L[i - 15] | 0;\n      const s0h = u64.rotrSH(W15h, W15l, 1) ^ u64.rotrSH(W15h, W15l, 8) ^ u64.shrSH(W15h, W15l, 7);\n      const s0l = u64.rotrSL(W15h, W15l, 1) ^ u64.rotrSL(W15h, W15l, 8) ^ u64.shrSL(W15h, W15l, 7);\n      // s1 := (w[i-2] rightrotate 19) xor (w[i-2] rightrotate 61) xor (w[i-2] rightshift 6)\n      const W2h = SHA512_W_H[i - 2] | 0;\n      const W2l = SHA512_W_L[i - 2] | 0;\n      const s1h = u64.rotrSH(W2h, W2l, 19) ^ u64.rotrBH(W2h, W2l, 61) ^ u64.shrSH(W2h, W2l, 6);\n      const s1l = u64.rotrSL(W2h, W2l, 19) ^ u64.rotrBL(W2h, W2l, 61) ^ u64.shrSL(W2h, W2l, 6);\n      // SHA256_W[i] = s0 + s1 + SHA256_W[i - 7] + SHA256_W[i - 16];\n      const SUMl = u64.add4L(s0l, s1l, SHA512_W_L[i - 7], SHA512_W_L[i - 16]);\n      const SUMh = u64.add4H(SUMl, s0h, s1h, SHA512_W_H[i - 7], SHA512_W_H[i - 16]);\n      SHA512_W_H[i] = SUMh | 0;\n      SHA512_W_L[i] = SUMl | 0;\n    }\n    let { Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl } = this;\n    // Compression function main loop, 80 rounds\n    for (let i = 0; i < 80; i++) {\n      // S1 := (e rightrotate 14) xor (e rightrotate 18) xor (e rightrotate 41)\n      const sigma1h = u64.rotrSH(Eh, El, 14) ^ u64.rotrSH(Eh, El, 18) ^ u64.rotrBH(Eh, El, 41);\n      const sigma1l = u64.rotrSL(Eh, El, 14) ^ u64.rotrSL(Eh, El, 18) ^ u64.rotrBL(Eh, El, 41);\n      //const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const CHIh = (Eh & Fh) ^ (~Eh & Gh);\n      const CHIl = (El & Fl) ^ (~El & Gl);\n      // T1 = H + sigma1 + Chi(E, F, G) + SHA512_K[i] + SHA512_W[i]\n      // prettier-ignore\n      const T1ll = u64.add5L(Hl, sigma1l, CHIl, SHA512_Kl[i], SHA512_W_L[i]);\n      const T1h = u64.add5H(T1ll, Hh, sigma1h, CHIh, SHA512_Kh[i], SHA512_W_H[i]);\n      const T1l = T1ll | 0;\n      // S0 := (a rightrotate 28) xor (a rightrotate 34) xor (a rightrotate 39)\n      const sigma0h = u64.rotrSH(Ah, Al, 28) ^ u64.rotrBH(Ah, Al, 34) ^ u64.rotrBH(Ah, Al, 39);\n      const sigma0l = u64.rotrSL(Ah, Al, 28) ^ u64.rotrBL(Ah, Al, 34) ^ u64.rotrBL(Ah, Al, 39);\n      const MAJh = (Ah & Bh) ^ (Ah & Ch) ^ (Bh & Ch);\n      const MAJl = (Al & Bl) ^ (Al & Cl) ^ (Bl & Cl);\n      Hh = Gh | 0;\n      Hl = Gl | 0;\n      Gh = Fh | 0;\n      Gl = Fl | 0;\n      Fh = Eh | 0;\n      Fl = El | 0;\n      ({ h: Eh, l: El } = u64.add(Dh | 0, Dl | 0, T1h | 0, T1l | 0));\n      Dh = Ch | 0;\n      Dl = Cl | 0;\n      Ch = Bh | 0;\n      Cl = Bl | 0;\n      Bh = Ah | 0;\n      Bl = Al | 0;\n      const All = u64.add3L(T1l, sigma0l, MAJl);\n      Ah = u64.add3H(All, T1h, sigma0h, MAJh);\n      Al = All | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    ({ h: Ah, l: Al } = u64.add(this.Ah | 0, this.Al | 0, Ah | 0, Al | 0));\n    ({ h: Bh, l: Bl } = u64.add(this.Bh | 0, this.Bl | 0, Bh | 0, Bl | 0));\n    ({ h: Ch, l: Cl } = u64.add(this.Ch | 0, this.Cl | 0, Ch | 0, Cl | 0));\n    ({ h: Dh, l: Dl } = u64.add(this.Dh | 0, this.Dl | 0, Dh | 0, Dl | 0));\n    ({ h: Eh, l: El } = u64.add(this.Eh | 0, this.El | 0, Eh | 0, El | 0));\n    ({ h: Fh, l: Fl } = u64.add(this.Fh | 0, this.Fl | 0, Fh | 0, Fl | 0));\n    ({ h: Gh, l: Gl } = u64.add(this.Gh | 0, this.Gl | 0, Gh | 0, Gl | 0));\n    ({ h: Hh, l: Hl } = u64.add(this.Hh | 0, this.Hl | 0, Hh | 0, Hl | 0));\n    this.set(Ah, Al, Bh, Bl, Ch, Cl, Dh, Dl, Eh, El, Fh, Fl, Gh, Gl, Hh, Hl);\n  }\n  protected roundClean() {\n    SHA512_W_H.fill(0);\n    SHA512_W_L.fill(0);\n  }\n  destroy() {\n    this.buffer.fill(0);\n    this.set(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);\n  }\n}\n\nclass SHA512_224 extends SHA512 {\n  // h -- high 32 bits, l -- low 32 bits\n  Ah = 0x8c3d37c8 | 0;\n  Al = 0x19544da2 | 0;\n  Bh = 0x73e19966 | 0;\n  Bl = 0x89dcd4d6 | 0;\n  Ch = 0x1dfab7ae | 0;\n  Cl = 0x32ff9c82 | 0;\n  Dh = 0x679dd514 | 0;\n  Dl = 0x582f9fcf | 0;\n  Eh = 0x0f6d2b69 | 0;\n  El = 0x7bd44da8 | 0;\n  Fh = 0x77e36f73 | 0;\n  Fl = 0x04c48942 | 0;\n  Gh = 0x3f9d85a8 | 0;\n  Gl = 0x6a1d36c8 | 0;\n  Hh = 0x1112e6ad | 0;\n  Hl = 0x91d692a1 | 0;\n\n  constructor() {\n    super();\n    this.outputLen = 28;\n  }\n}\n\nclass SHA512_256 extends SHA512 {\n  // h -- high 32 bits, l -- low 32 bits\n  Ah = 0x22312194 | 0;\n  Al = 0xfc2bf72c | 0;\n  Bh = 0x9f555fa3 | 0;\n  Bl = 0xc84c64c2 | 0;\n  Ch = 0x2393b86b | 0;\n  Cl = 0x6f53b151 | 0;\n  Dh = 0x96387719 | 0;\n  Dl = 0x5940eabd | 0;\n  Eh = 0x96283ee2 | 0;\n  El = 0xa88effe3 | 0;\n  Fh = 0xbe5e1e25 | 0;\n  Fl = 0x53863992 | 0;\n  Gh = 0x2b0199fc | 0;\n  Gl = 0x2c85b8aa | 0;\n  Hh = 0x0eb72ddc | 0;\n  Hl = 0x81c52ca2 | 0;\n\n  constructor() {\n    super();\n    this.outputLen = 32;\n  }\n}\n\nclass SHA384 extends SHA512 {\n  // h -- high 32 bits, l -- low 32 bits\n  Ah = 0xcbbb9d5d | 0;\n  Al = 0xc1059ed8 | 0;\n  Bh = 0x629a292a | 0;\n  Bl = 0x367cd507 | 0;\n  Ch = 0x9159015a | 0;\n  Cl = 0x3070dd17 | 0;\n  Dh = 0x152fecd8 | 0;\n  Dl = 0xf70e5939 | 0;\n  Eh = 0x67332667 | 0;\n  El = 0xffc00b31 | 0;\n  Fh = 0x8eb44a87 | 0;\n  Fl = 0x68581511 | 0;\n  Gh = 0xdb0c2e0d | 0;\n  Gl = 0x64f98fa7 | 0;\n  Hh = 0x47b5481d | 0;\n  Hl = 0xbefa4fa4 | 0;\n\n  constructor() {\n    super();\n    this.outputLen = 48;\n  }\n}\n\nexport const sha512 = /* @__PURE__ */ wrapConstructor(() => new SHA512());\nexport const sha512_224 = /* @__PURE__ */ wrapConstructor(() => new SHA512_224());\nexport const sha512_256 = /* @__PURE__ */ wrapConstructor(() => new SHA512_256());\nexport const sha384 = /* @__PURE__ */ wrapConstructor(() => new SHA384());\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AACjC,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAASC,eAAe,QAAQ,YAAY;AAE5C;AACA;AACA,MAAM,CAACC,SAAS,EAAEC,SAAS,CAAC,GAAG,eAAgB,CAAC,MAAMH,GAAG,CAACI,KAAK,CAAC,CAC9D,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EACtF,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,oBAAoB,CACvF,CAACC,GAAG,CAACC,CAAC,IAAIC,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC,EAAC,CAAE;AAEzB;AACA,MAAME,UAAU,GAAG,eAAgB,IAAIC,WAAW,CAAC,EAAE,CAAC;AACtD,MAAMC,UAAU,GAAG,eAAgB,IAAID,WAAW,CAAC,EAAE,CAAC;AACtD,OAAM,MAAOE,MAAO,SAAQZ,IAAY;EAsBtCa,YAAA;IACE,KAAK,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC;IAtB3B;IACA;IACA;IACA;IACA,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;EAInB;EACA;EACUC,GAAGA,CAAA;IAIX,MAAM;MAAEhB,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAE,GAAG,IAAI;IAC/E,OAAO,CAACf,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACzE;EACA;EACUE,GAAGA,CACXjB,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAC9FC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;IAE9F,IAAI,CAACf,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC;EAClB;EACUG,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAE;MACxCzB,UAAU,CAAC0B,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,CAAC;MACtCvB,UAAU,CAACwB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAAEF,MAAM,IAAI,CAAE,CAAC;;IAE/C,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B;MACA,MAAME,IAAI,GAAG5B,UAAU,CAAC0B,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;MACnC,MAAMG,IAAI,GAAG3B,UAAU,CAACwB,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;MACnC,MAAMI,GAAG,GAAGtC,GAAG,CAACuC,MAAM,CAACH,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGrC,GAAG,CAACuC,MAAM,CAACH,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGrC,GAAG,CAACwC,KAAK,CAACJ,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC;MAC5F,MAAMI,GAAG,GAAGzC,GAAG,CAAC0C,MAAM,CAACN,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGrC,GAAG,CAAC0C,MAAM,CAACN,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC,GAAGrC,GAAG,CAAC2C,KAAK,CAACP,IAAI,EAAEC,IAAI,EAAE,CAAC,CAAC;MAC5F;MACA,MAAMO,GAAG,GAAGpC,UAAU,CAAC0B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjC,MAAMW,GAAG,GAAGnC,UAAU,CAACwB,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACjC,MAAMY,GAAG,GAAG9C,GAAG,CAACuC,MAAM,CAACK,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAG7C,GAAG,CAAC+C,MAAM,CAACH,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAG7C,GAAG,CAACwC,KAAK,CAACI,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;MACxF,MAAMG,GAAG,GAAGhD,GAAG,CAAC0C,MAAM,CAACE,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAG7C,GAAG,CAACiD,MAAM,CAACL,GAAG,EAAEC,GAAG,EAAE,EAAE,CAAC,GAAG7C,GAAG,CAAC2C,KAAK,CAACC,GAAG,EAAEC,GAAG,EAAE,CAAC,CAAC;MACxF;MACA,MAAMK,IAAI,GAAGlD,GAAG,CAACmD,KAAK,CAACV,GAAG,EAAEO,GAAG,EAAEtC,UAAU,CAACwB,CAAC,GAAG,CAAC,CAAC,EAAExB,UAAU,CAACwB,CAAC,GAAG,EAAE,CAAC,CAAC;MACvE,MAAMkB,IAAI,GAAGpD,GAAG,CAACqD,KAAK,CAACH,IAAI,EAAEZ,GAAG,EAAEQ,GAAG,EAAEtC,UAAU,CAAC0B,CAAC,GAAG,CAAC,CAAC,EAAE1B,UAAU,CAAC0B,CAAC,GAAG,EAAE,CAAC,CAAC;MAC7E1B,UAAU,CAAC0B,CAAC,CAAC,GAAGkB,IAAI,GAAG,CAAC;MACxB1C,UAAU,CAACwB,CAAC,CAAC,GAAGgB,IAAI,GAAG,CAAC;;IAE1B,IAAI;MAAErC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC;IAAE,CAAE,GAAG,IAAI;IAC7E;IACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACA,MAAMoB,OAAO,GAAGtD,GAAG,CAACuC,MAAM,CAAClB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGtB,GAAG,CAACuC,MAAM,CAAClB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGtB,GAAG,CAAC+C,MAAM,CAAC1B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAMiC,OAAO,GAAGvD,GAAG,CAAC0C,MAAM,CAACrB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGtB,GAAG,CAAC0C,MAAM,CAACrB,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGtB,GAAG,CAACiD,MAAM,CAAC5B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF;MACA,MAAMkC,IAAI,GAAInC,EAAE,GAAGE,EAAE,GAAK,CAACF,EAAE,GAAGI,EAAG;MACnC,MAAMgC,IAAI,GAAInC,EAAE,GAAGE,EAAE,GAAK,CAACF,EAAE,GAAGI,EAAG;MACnC;MACA;MACA,MAAMgC,IAAI,GAAG1D,GAAG,CAAC2D,KAAK,CAAC/B,EAAE,EAAE2B,OAAO,EAAEE,IAAI,EAAEtD,SAAS,CAAC+B,CAAC,CAAC,EAAExB,UAAU,CAACwB,CAAC,CAAC,CAAC;MACtE,MAAM0B,GAAG,GAAG5D,GAAG,CAAC6D,KAAK,CAACH,IAAI,EAAE/B,EAAE,EAAE2B,OAAO,EAAEE,IAAI,EAAEtD,SAAS,CAACgC,CAAC,CAAC,EAAE1B,UAAU,CAAC0B,CAAC,CAAC,CAAC;MAC3E,MAAM4B,GAAG,GAAGJ,IAAI,GAAG,CAAC;MACpB;MACA,MAAMK,OAAO,GAAG/D,GAAG,CAACuC,MAAM,CAAC1B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGd,GAAG,CAAC+C,MAAM,CAAClC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGd,GAAG,CAAC+C,MAAM,CAAClC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAMkD,OAAO,GAAGhE,GAAG,CAAC0C,MAAM,CAAC7B,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGd,GAAG,CAACiD,MAAM,CAACpC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC,GAAGd,GAAG,CAACiD,MAAM,CAACpC,EAAE,EAAEC,EAAE,EAAE,EAAE,CAAC;MACxF,MAAMmD,IAAI,GAAIpD,EAAE,GAAGE,EAAE,GAAKF,EAAE,GAAGI,EAAG,GAAIF,EAAE,GAAGE,EAAG;MAC9C,MAAMiD,IAAI,GAAIpD,EAAE,GAAGE,EAAE,GAAKF,EAAE,GAAGI,EAAG,GAAIF,EAAE,GAAGE,EAAG;MAC9CS,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACX,CAAC;QAAE6C,CAAC,EAAE9C,EAAE;QAAE+C,CAAC,EAAE9C;MAAE,CAAE,GAAGtB,GAAG,CAACqE,GAAG,CAAClD,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,EAAEwC,GAAG,GAAG,CAAC,EAAEE,GAAG,GAAG,CAAC,CAAC;MAC7D3C,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXD,EAAE,GAAGF,EAAE,GAAG,CAAC;MACXG,EAAE,GAAGF,EAAE,GAAG,CAAC;MACX,MAAMwD,GAAG,GAAGtE,GAAG,CAACuE,KAAK,CAACT,GAAG,EAAEE,OAAO,EAAEE,IAAI,CAAC;MACzCrD,EAAE,GAAGb,GAAG,CAACwE,KAAK,CAACF,GAAG,EAAEV,GAAG,EAAEG,OAAO,EAAEE,IAAI,CAAC;MACvCnD,EAAE,GAAGwD,GAAG,GAAG,CAAC;;IAEd;IACA,CAAC;MAAEH,CAAC,EAAEtD,EAAE;MAAEuD,CAAC,EAAEtD;IAAE,CAAE,GAAGd,GAAG,CAACqE,GAAG,CAAC,IAAI,CAACxD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEqD,CAAC,EAAEpD,EAAE;MAAEqD,CAAC,EAAEpD;IAAE,CAAE,GAAGhB,GAAG,CAACqE,GAAG,CAAC,IAAI,CAACtD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEmD,CAAC,EAAElD,EAAE;MAAEmD,CAAC,EAAElD;IAAE,CAAE,GAAGlB,GAAG,CAACqE,GAAG,CAAC,IAAI,CAACpD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEiD,CAAC,EAAEhD,EAAE;MAAEiD,CAAC,EAAEhD;IAAE,CAAE,GAAGpB,GAAG,CAACqE,GAAG,CAAC,IAAI,CAAClD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE+C,CAAC,EAAE9C,EAAE;MAAE+C,CAAC,EAAE9C;IAAE,CAAE,GAAGtB,GAAG,CAACqE,GAAG,CAAC,IAAI,CAAChD,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE6C,CAAC,EAAE5C,EAAE;MAAE6C,CAAC,EAAE5C;IAAE,CAAE,GAAGxB,GAAG,CAACqE,GAAG,CAAC,IAAI,CAAC9C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAE2C,CAAC,EAAE1C,EAAE;MAAE2C,CAAC,EAAE1C;IAAE,CAAE,GAAG1B,GAAG,CAACqE,GAAG,CAAC,IAAI,CAAC5C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;MAAEyC,CAAC,EAAExC,EAAE;MAAEyC,CAAC,EAAExC;IAAE,CAAE,GAAG5B,GAAG,CAACqE,GAAG,CAAC,IAAI,CAAC1C,EAAE,GAAG,CAAC,EAAE,IAAI,CAACC,EAAE,GAAG,CAAC,EAAED,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,CAAC;IACrE,IAAI,CAACE,GAAG,CAACjB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC1E;EACU6C,UAAUA,CAAA;IAClBjE,UAAU,CAACkE,IAAI,CAAC,CAAC,CAAC;IAClBhE,UAAU,CAACgE,IAAI,CAAC,CAAC,CAAC;EACpB;EACAC,OAAOA,CAAA;IACL,IAAI,CAACC,MAAM,CAACF,IAAI,CAAC,CAAC,CAAC;IACnB,IAAI,CAAC5C,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1D;;AAGF,MAAM+C,UAAW,SAAQlE,MAAM;EAmB7BC,YAAA;IACE,KAAK,EAAE;IAnBT;IACA,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IAIjB,IAAI,CAACkD,SAAS,GAAG,EAAE;EACrB;;AAGF,MAAMC,UAAW,SAAQpE,MAAM;EAmB7BC,YAAA;IACE,KAAK,EAAE;IAnBT;IACA,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IAIjB,IAAI,CAACkD,SAAS,GAAG,EAAE;EACrB;;AAGF,MAAME,MAAO,SAAQrE,MAAM;EAmBzBC,YAAA;IACE,KAAK,EAAE;IAnBT;IACA,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IACnB,KAAAC,EAAE,GAAG,UAAU,GAAG,CAAC;IAIjB,IAAI,CAACkD,SAAS,GAAG,EAAE;EACrB;;AAGF,OAAO,MAAMG,MAAM,GAAG,eAAgBhF,eAAe,CAAC,MAAM,IAAIU,MAAM,EAAE,CAAC;AACzE,OAAO,MAAMuE,UAAU,GAAG,eAAgBjF,eAAe,CAAC,MAAM,IAAI4E,UAAU,EAAE,CAAC;AACjF,OAAO,MAAMM,UAAU,GAAG,eAAgBlF,eAAe,CAAC,MAAM,IAAI8E,UAAU,EAAE,CAAC;AACjF,OAAO,MAAMK,MAAM,GAAG,eAAgBnF,eAAe,CAAC,MAAM,IAAI+E,MAAM,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}