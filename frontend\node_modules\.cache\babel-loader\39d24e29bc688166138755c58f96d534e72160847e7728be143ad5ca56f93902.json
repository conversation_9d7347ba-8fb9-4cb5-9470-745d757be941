{"ast": null, "code": "/**\n *  Some data helpers.\n *\n *\n *  @_subsection api/utils:Data Helpers  [about-data]\n */\nimport { assert, assertArgument } from \"./errors.js\";\nfunction _getBytes(value, name, copy) {\n  if (value instanceof Uint8Array) {\n    if (copy) {\n      return new Uint8Array(value);\n    }\n    return value;\n  }\n  if (typeof value === \"string\" && value.match(/^0x(?:[0-9a-f][0-9a-f])*$/i)) {\n    const result = new Uint8Array((value.length - 2) / 2);\n    let offset = 2;\n    for (let i = 0; i < result.length; i++) {\n      result[i] = parseInt(value.substring(offset, offset + 2), 16);\n      offset += 2;\n    }\n    return result;\n  }\n  assertArgument(false, \"invalid BytesLike value\", name || \"value\", value);\n}\n/**\n *  Get a typed Uint8Array for %%value%%. If already a Uint8Array\n *  the original %%value%% is returned; if a copy is required use\n *  [[getBytesCopy]].\n *\n *  @see: getBytesCopy\n */\nexport function getBytes(value, name) {\n  return _getBytes(value, name, false);\n}\n/**\n *  Get a typed Uint8Array for %%value%%, creating a copy if necessary\n *  to prevent any modifications of the returned value from being\n *  reflected elsewhere.\n *\n *  @see: getBytes\n */\nexport function getBytesCopy(value, name) {\n  return _getBytes(value, name, true);\n}\n/**\n *  Returns true if %%value%% is a valid [[HexString]].\n *\n *  If %%length%% is ``true`` or a //number//, it also checks that\n *  %%value%% is a valid [[DataHexString]] of %%length%% (if a //number//)\n *  bytes of data (e.g. ``0x1234`` is 2 bytes).\n */\nexport function isHexString(value, length) {\n  if (typeof value !== \"string\" || !value.match(/^0x[0-9A-Fa-f]*$/)) {\n    return false;\n  }\n  if (typeof length === \"number\" && value.length !== 2 + 2 * length) {\n    return false;\n  }\n  if (length === true && value.length % 2 !== 0) {\n    return false;\n  }\n  return true;\n}\n/**\n *  Returns true if %%value%% is a valid representation of arbitrary\n *  data (i.e. a valid [[DataHexString]] or a Uint8Array).\n */\nexport function isBytesLike(value) {\n  return isHexString(value, true) || value instanceof Uint8Array;\n}\nconst HexCharacters = \"0123456789abcdef\";\n/**\n *  Returns a [[DataHexString]] representation of %%data%%.\n */\nexport function hexlify(data) {\n  const bytes = getBytes(data);\n  let result = \"0x\";\n  for (let i = 0; i < bytes.length; i++) {\n    const v = bytes[i];\n    result += HexCharacters[(v & 0xf0) >> 4] + HexCharacters[v & 0x0f];\n  }\n  return result;\n}\n/**\n *  Returns a [[DataHexString]] by concatenating all values\n *  within %%data%%.\n */\nexport function concat(datas) {\n  return \"0x\" + datas.map(d => hexlify(d).substring(2)).join(\"\");\n}\n/**\n *  Returns the length of %%data%%, in bytes.\n */\nexport function dataLength(data) {\n  if (isHexString(data, true)) {\n    return (data.length - 2) / 2;\n  }\n  return getBytes(data).length;\n}\n/**\n *  Returns a [[DataHexString]] by slicing %%data%% from the %%start%%\n *  offset to the %%end%% offset.\n *\n *  By default %%start%% is 0 and %%end%% is the length of %%data%%.\n */\nexport function dataSlice(data, start, end) {\n  const bytes = getBytes(data);\n  if (end != null && end > bytes.length) {\n    assert(false, \"cannot slice beyond data bounds\", \"BUFFER_OVERRUN\", {\n      buffer: bytes,\n      length: bytes.length,\n      offset: end\n    });\n  }\n  return hexlify(bytes.slice(start == null ? 0 : start, end == null ? bytes.length : end));\n}\n/**\n *  Return the [[DataHexString]] result by stripping all **leading**\n ** zero bytes from %%data%%.\n */\nexport function stripZerosLeft(data) {\n  let bytes = hexlify(data).substring(2);\n  while (bytes.startsWith(\"00\")) {\n    bytes = bytes.substring(2);\n  }\n  return \"0x\" + bytes;\n}\nfunction zeroPad(data, length, left) {\n  const bytes = getBytes(data);\n  assert(length >= bytes.length, \"padding exceeds data length\", \"BUFFER_OVERRUN\", {\n    buffer: new Uint8Array(bytes),\n    length: length,\n    offset: length + 1\n  });\n  const result = new Uint8Array(length);\n  result.fill(0);\n  if (left) {\n    result.set(bytes, length - bytes.length);\n  } else {\n    result.set(bytes, 0);\n  }\n  return hexlify(result);\n}\n/**\n *  Return the [[DataHexString]] of %%data%% padded on the **left**\n *  to %%length%% bytes.\n *\n *  If %%data%% already exceeds %%length%%, a [[BufferOverrunError]] is\n *  thrown.\n *\n *  This pads data the same as **values** are in Solidity\n *  (e.g. ``uint128``).\n */\nexport function zeroPadValue(data, length) {\n  return zeroPad(data, length, true);\n}\n/**\n *  Return the [[DataHexString]] of %%data%% padded on the **right**\n *  to %%length%% bytes.\n *\n *  If %%data%% already exceeds %%length%%, a [[BufferOverrunError]] is\n *  thrown.\n *\n *  This pads data the same as **bytes** are in Solidity\n *  (e.g. ``bytes16``).\n */\nexport function zeroPadBytes(data, length) {\n  return zeroPad(data, length, false);\n}", "map": {"version": 3, "names": ["assert", "assertArgument", "_getBytes", "value", "name", "copy", "Uint8Array", "match", "result", "length", "offset", "i", "parseInt", "substring", "getBytes", "getBytesCopy", "isHexString", "isBytesLike", "HexCharacters", "hexlify", "data", "bytes", "v", "concat", "datas", "map", "d", "join", "dataLength", "dataSlice", "start", "end", "buffer", "slice", "stripZerosLeft", "startsWith", "zeroPad", "left", "fill", "set", "zeroPadValue", "zeroPadBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\data.ts"], "sourcesContent": ["/**\n *  Some data helpers.\n *\n *\n *  @_subsection api/utils:Data Helpers  [about-data]\n */\nimport { assert, assertArgument } from \"./errors.js\";\n\n/**\n *  A [[HexString]] whose length is even, which ensures it is a valid\n *  representation of binary data.\n */\nexport type DataHexString = string;\n\n/**\n *  A string which is prefixed with ``0x`` and followed by any number\n *  of case-agnostic hexadecimal characters.\n *\n *  It must match the regular expression ``/0x[0-9A-Fa-f]*\\/``.\n */\nexport type HexString = string;\n\n/**\n *  An object that can be used to represent binary data.\n */\nexport type BytesLike = DataHexString | Uint8Array;\n\nfunction _getBytes(value: BytesLike, name?: string, copy?: boolean): Uint8Array {\n    if (value instanceof Uint8Array) {\n        if (copy) { return new Uint8Array(value); }\n        return value;\n    }\n\n    if (typeof(value) === \"string\" && value.match(/^0x(?:[0-9a-f][0-9a-f])*$/i)) {\n        const result = new Uint8Array((value.length - 2) / 2);\n        let offset = 2;\n        for (let i = 0; i < result.length; i++) {\n            result[i] = parseInt(value.substring(offset, offset + 2), 16);\n            offset += 2;\n        }\n        return result;\n    }\n\n    assertArgument(false, \"invalid BytesLike value\", name || \"value\", value);\n}\n\n/**\n *  Get a typed Uint8Array for %%value%%. If already a Uint8Array\n *  the original %%value%% is returned; if a copy is required use\n *  [[getBytesCopy]].\n *\n *  @see: getBytesCopy\n */\nexport function getBytes(value: BytesLike, name?: string): Uint8Array {\n    return _getBytes(value, name, false);\n}\n\n/**\n *  Get a typed Uint8Array for %%value%%, creating a copy if necessary\n *  to prevent any modifications of the returned value from being\n *  reflected elsewhere.\n *\n *  @see: getBytes\n */\nexport function getBytesCopy(value: BytesLike, name?: string): Uint8Array {\n    return _getBytes(value, name, true);\n}\n\n\n/**\n *  Returns true if %%value%% is a valid [[HexString]].\n *\n *  If %%length%% is ``true`` or a //number//, it also checks that\n *  %%value%% is a valid [[DataHexString]] of %%length%% (if a //number//)\n *  bytes of data (e.g. ``0x1234`` is 2 bytes).\n */\nexport function isHexString(value: any, length?: number | boolean): value is `0x${ string }` {\n    if (typeof(value) !== \"string\" || !value.match(/^0x[0-9A-Fa-f]*$/)) {\n        return false\n    }\n\n    if (typeof(length) === \"number\" && value.length !== 2 + 2 * length) { return false; }\n    if (length === true && (value.length % 2) !== 0) { return false; }\n\n    return true;\n}\n\n/**\n *  Returns true if %%value%% is a valid representation of arbitrary\n *  data (i.e. a valid [[DataHexString]] or a Uint8Array).\n */\nexport function isBytesLike(value: any): value is BytesLike {\n    return (isHexString(value, true) || (value instanceof Uint8Array));\n}\n\nconst HexCharacters: string = \"0123456789abcdef\";\n\n/**\n *  Returns a [[DataHexString]] representation of %%data%%.\n */\nexport function hexlify(data: BytesLike): string {\n    const bytes = getBytes(data);\n\n    let result = \"0x\";\n    for (let i = 0; i < bytes.length; i++) {\n        const v = bytes[i];\n        result += HexCharacters[(v & 0xf0) >> 4] + HexCharacters[v & 0x0f];\n    }\n    return result;\n}\n\n/**\n *  Returns a [[DataHexString]] by concatenating all values\n *  within %%data%%.\n */\nexport function concat(datas: ReadonlyArray<BytesLike>): string {\n    return \"0x\" + datas.map((d) => hexlify(d).substring(2)).join(\"\");\n}\n\n/**\n *  Returns the length of %%data%%, in bytes.\n */\nexport function dataLength(data: BytesLike): number {\n    if (isHexString(data, true)) { return (data.length - 2) / 2; }\n    return getBytes(data).length;\n}\n\n/**\n *  Returns a [[DataHexString]] by slicing %%data%% from the %%start%%\n *  offset to the %%end%% offset.\n *\n *  By default %%start%% is 0 and %%end%% is the length of %%data%%.\n */\nexport function dataSlice(data: BytesLike, start?: number, end?: number): string {\n    const bytes = getBytes(data);\n    if (end != null && end > bytes.length) {\n        assert(false, \"cannot slice beyond data bounds\", \"BUFFER_OVERRUN\", {\n            buffer: bytes, length: bytes.length, offset: end\n        });\n    }\n    return hexlify(bytes.slice((start == null) ? 0: start, (end == null) ? bytes.length: end));\n}\n\n/**\n *  Return the [[DataHexString]] result by stripping all **leading**\n ** zero bytes from %%data%%.\n */\nexport function stripZerosLeft(data: BytesLike): string {\n    let bytes = hexlify(data).substring(2);\n    while (bytes.startsWith(\"00\")) { bytes = bytes.substring(2); }\n    return \"0x\" + bytes;\n}\n\nfunction zeroPad(data: BytesLike, length: number, left: boolean): string {\n    const bytes = getBytes(data);\n    assert(length >= bytes.length, \"padding exceeds data length\", \"BUFFER_OVERRUN\", {\n        buffer: new Uint8Array(bytes),\n        length: length,\n        offset: length + 1\n    });\n\n    const result = new Uint8Array(length);\n    result.fill(0);\n    if (left) {\n        result.set(bytes, length - bytes.length);\n    } else {\n        result.set(bytes, 0);\n    }\n\n    return hexlify(result);\n}\n\n/**\n *  Return the [[DataHexString]] of %%data%% padded on the **left**\n *  to %%length%% bytes.\n *\n *  If %%data%% already exceeds %%length%%, a [[BufferOverrunError]] is\n *  thrown.\n *\n *  This pads data the same as **values** are in Solidity\n *  (e.g. ``uint128``).\n */\nexport function zeroPadValue(data: BytesLike, length: number): string {\n    return zeroPad(data, length, true);\n}\n\n/**\n *  Return the [[DataHexString]] of %%data%% padded on the **right**\n *  to %%length%% bytes.\n *\n *  If %%data%% already exceeds %%length%%, a [[BufferOverrunError]] is\n *  thrown.\n *\n *  This pads data the same as **bytes** are in Solidity\n *  (e.g. ``bytes16``).\n */\nexport function zeroPadBytes(data: BytesLike, length: number): string {\n    return zeroPad(data, length, false);\n}\n"], "mappings": "AAAA;;;;;;AAMA,SAASA,MAAM,EAAEC,cAAc,QAAQ,aAAa;AAqBpD,SAASC,SAASA,CAACC,KAAgB,EAAEC,IAAa,EAAEC,IAAc;EAC9D,IAAIF,KAAK,YAAYG,UAAU,EAAE;IAC7B,IAAID,IAAI,EAAE;MAAE,OAAO,IAAIC,UAAU,CAACH,KAAK,CAAC;;IACxC,OAAOA,KAAK;;EAGhB,IAAI,OAAOA,KAAM,KAAK,QAAQ,IAAIA,KAAK,CAACI,KAAK,CAAC,4BAA4B,CAAC,EAAE;IACzE,MAAMC,MAAM,GAAG,IAAIF,UAAU,CAAC,CAACH,KAAK,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;IACrD,IAAIC,MAAM,GAAG,CAAC;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACC,MAAM,EAAEE,CAAC,EAAE,EAAE;MACpCH,MAAM,CAACG,CAAC,CAAC,GAAGC,QAAQ,CAACT,KAAK,CAACU,SAAS,CAACH,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;MAC7DA,MAAM,IAAI,CAAC;;IAEf,OAAOF,MAAM;;EAGjBP,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAEG,IAAI,IAAI,OAAO,EAAED,KAAK,CAAC;AAC5E;AAEA;;;;;;;AAOA,OAAM,SAAUW,QAAQA,CAACX,KAAgB,EAAEC,IAAa;EACpD,OAAOF,SAAS,CAACC,KAAK,EAAEC,IAAI,EAAE,KAAK,CAAC;AACxC;AAEA;;;;;;;AAOA,OAAM,SAAUW,YAAYA,CAACZ,KAAgB,EAAEC,IAAa;EACxD,OAAOF,SAAS,CAACC,KAAK,EAAEC,IAAI,EAAE,IAAI,CAAC;AACvC;AAGA;;;;;;;AAOA,OAAM,SAAUY,WAAWA,CAACb,KAAU,EAAEM,MAAyB;EAC7D,IAAI,OAAON,KAAM,KAAK,QAAQ,IAAI,CAACA,KAAK,CAACI,KAAK,CAAC,kBAAkB,CAAC,EAAE;IAChE,OAAO,KAAK;;EAGhB,IAAI,OAAOE,MAAO,KAAK,QAAQ,IAAIN,KAAK,CAACM,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGA,MAAM,EAAE;IAAE,OAAO,KAAK;;EAClF,IAAIA,MAAM,KAAK,IAAI,IAAKN,KAAK,CAACM,MAAM,GAAG,CAAC,KAAM,CAAC,EAAE;IAAE,OAAO,KAAK;;EAE/D,OAAO,IAAI;AACf;AAEA;;;;AAIA,OAAM,SAAUQ,WAAWA,CAACd,KAAU;EAClC,OAAQa,WAAW,CAACb,KAAK,EAAE,IAAI,CAAC,IAAKA,KAAK,YAAYG,UAAW;AACrE;AAEA,MAAMY,aAAa,GAAW,kBAAkB;AAEhD;;;AAGA,OAAM,SAAUC,OAAOA,CAACC,IAAe;EACnC,MAAMC,KAAK,GAAGP,QAAQ,CAACM,IAAI,CAAC;EAE5B,IAAIZ,MAAM,GAAG,IAAI;EACjB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,KAAK,CAACZ,MAAM,EAAEE,CAAC,EAAE,EAAE;IACnC,MAAMW,CAAC,GAAGD,KAAK,CAACV,CAAC,CAAC;IAClBH,MAAM,IAAIU,aAAa,CAAC,CAACI,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,GAAGJ,aAAa,CAACI,CAAC,GAAG,IAAI,CAAC;;EAEtE,OAAOd,MAAM;AACjB;AAEA;;;;AAIA,OAAM,SAAUe,MAAMA,CAACC,KAA+B;EAClD,OAAO,IAAI,GAAGA,KAAK,CAACC,GAAG,CAAEC,CAAC,IAAKP,OAAO,CAACO,CAAC,CAAC,CAACb,SAAS,CAAC,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC,EAAE,CAAC;AACpE;AAEA;;;AAGA,OAAM,SAAUC,UAAUA,CAACR,IAAe;EACtC,IAAIJ,WAAW,CAACI,IAAI,EAAE,IAAI,CAAC,EAAE;IAAE,OAAO,CAACA,IAAI,CAACX,MAAM,GAAG,CAAC,IAAI,CAAC;;EAC3D,OAAOK,QAAQ,CAACM,IAAI,CAAC,CAACX,MAAM;AAChC;AAEA;;;;;;AAMA,OAAM,SAAUoB,SAASA,CAACT,IAAe,EAAEU,KAAc,EAAEC,GAAY;EACnE,MAAMV,KAAK,GAAGP,QAAQ,CAACM,IAAI,CAAC;EAC5B,IAAIW,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGV,KAAK,CAACZ,MAAM,EAAE;IACnCT,MAAM,CAAC,KAAK,EAAE,iCAAiC,EAAE,gBAAgB,EAAE;MAC/DgC,MAAM,EAAEX,KAAK;MAAEZ,MAAM,EAAEY,KAAK,CAACZ,MAAM;MAAEC,MAAM,EAAEqB;KAChD,CAAC;;EAEN,OAAOZ,OAAO,CAACE,KAAK,CAACY,KAAK,CAAEH,KAAK,IAAI,IAAI,GAAI,CAAC,GAAEA,KAAK,EAAGC,GAAG,IAAI,IAAI,GAAIV,KAAK,CAACZ,MAAM,GAAEsB,GAAG,CAAC,CAAC;AAC9F;AAEA;;;;AAIA,OAAM,SAAUG,cAAcA,CAACd,IAAe;EAC1C,IAAIC,KAAK,GAAGF,OAAO,CAACC,IAAI,CAAC,CAACP,SAAS,CAAC,CAAC,CAAC;EACtC,OAAOQ,KAAK,CAACc,UAAU,CAAC,IAAI,CAAC,EAAE;IAAEd,KAAK,GAAGA,KAAK,CAACR,SAAS,CAAC,CAAC,CAAC;;EAC3D,OAAO,IAAI,GAAGQ,KAAK;AACvB;AAEA,SAASe,OAAOA,CAAChB,IAAe,EAAEX,MAAc,EAAE4B,IAAa;EAC3D,MAAMhB,KAAK,GAAGP,QAAQ,CAACM,IAAI,CAAC;EAC5BpB,MAAM,CAACS,MAAM,IAAIY,KAAK,CAACZ,MAAM,EAAE,6BAA6B,EAAE,gBAAgB,EAAE;IAC5EuB,MAAM,EAAE,IAAI1B,UAAU,CAACe,KAAK,CAAC;IAC7BZ,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAED,MAAM,GAAG;GACpB,CAAC;EAEF,MAAMD,MAAM,GAAG,IAAIF,UAAU,CAACG,MAAM,CAAC;EACrCD,MAAM,CAAC8B,IAAI,CAAC,CAAC,CAAC;EACd,IAAID,IAAI,EAAE;IACN7B,MAAM,CAAC+B,GAAG,CAAClB,KAAK,EAAEZ,MAAM,GAAGY,KAAK,CAACZ,MAAM,CAAC;GAC3C,MAAM;IACHD,MAAM,CAAC+B,GAAG,CAAClB,KAAK,EAAE,CAAC,CAAC;;EAGxB,OAAOF,OAAO,CAACX,MAAM,CAAC;AAC1B;AAEA;;;;;;;;;;AAUA,OAAM,SAAUgC,YAAYA,CAACpB,IAAe,EAAEX,MAAc;EACxD,OAAO2B,OAAO,CAAChB,IAAI,EAAEX,MAAM,EAAE,IAAI,CAAC;AACtC;AAEA;;;;;;;;;;AAUA,OAAM,SAAUgC,YAAYA,CAACrB,IAAe,EAAEX,MAAc;EACxD,OAAO2B,OAAO,CAAChB,IAAI,EAAEX,MAAM,EAAE,KAAK,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}