{"ast": null, "code": "// import from provider.ts instead of index.ts to prevent circular dep\n// from EtherscanProvider\nimport { Log, TransactionReceipt, TransactionResponse } from \"../providers/provider.js\";\nimport { defineProperties, EventPayload } from \"../utils/index.js\";\n/**\n *  An **EventLog** contains additional properties parsed from the [[Log]].\n */\nexport class EventLog extends Log {\n  /**\n   *  The Contract Interface.\n   */\n  interface;\n  /**\n   *  The matching event.\n   */\n  fragment;\n  /**\n   *  The parsed arguments passed to the event by ``emit``.\n   */\n  args;\n  /**\n   * @_ignore:\n   */\n  constructor(log, iface, fragment) {\n    super(log, log.provider);\n    const args = iface.decodeEventLog(fragment, log.data, log.topics);\n    defineProperties(this, {\n      args,\n      fragment,\n      interface: iface\n    });\n  }\n  /**\n   *  The name of the event.\n   */\n  get eventName() {\n    return this.fragment.name;\n  }\n  /**\n   *  The signature of the event.\n   */\n  get eventSignature() {\n    return this.fragment.format();\n  }\n}\n/**\n *  An **EventLog** contains additional properties parsed from the [[Log]].\n */\nexport class UndecodedEventLog extends Log {\n  /**\n   *  The error encounted when trying to decode the log.\n   */\n  error;\n  /**\n   * @_ignore:\n   */\n  constructor(log, error) {\n    super(log, log.provider);\n    defineProperties(this, {\n      error\n    });\n  }\n}\n/**\n *  A **ContractTransactionReceipt** includes the parsed logs from a\n *  [[TransactionReceipt]].\n */\nexport class ContractTransactionReceipt extends TransactionReceipt {\n  #iface;\n  /**\n   *  @_ignore:\n   */\n  constructor(iface, provider, tx) {\n    super(tx, provider);\n    this.#iface = iface;\n  }\n  /**\n   *  The parsed logs for any [[Log]] which has a matching event in the\n   *  Contract ABI.\n   */\n  get logs() {\n    return super.logs.map(log => {\n      const fragment = log.topics.length ? this.#iface.getEvent(log.topics[0]) : null;\n      if (fragment) {\n        try {\n          return new EventLog(log, this.#iface, fragment);\n        } catch (error) {\n          return new UndecodedEventLog(log, error);\n        }\n      }\n      return log;\n    });\n  }\n}\n/**\n *  A **ContractTransactionResponse** will return a\n *  [[ContractTransactionReceipt]] when waited on.\n */\nexport class ContractTransactionResponse extends TransactionResponse {\n  #iface;\n  /**\n   *  @_ignore:\n   */\n  constructor(iface, provider, tx) {\n    super(tx, provider);\n    this.#iface = iface;\n  }\n  /**\n   *  Resolves once this transaction has been mined and has\n   *  %%confirms%% blocks including it (default: ``1``) with an\n   *  optional %%timeout%%.\n   *\n   *  This can resolve to ``null`` only if %%confirms%% is ``0``\n   *  and the transaction has not been mined, otherwise this will\n   *  wait until enough confirmations have completed.\n   */\n  async wait(confirms, timeout) {\n    const receipt = await super.wait(confirms, timeout);\n    if (receipt == null) {\n      return null;\n    }\n    return new ContractTransactionReceipt(this.#iface, this.provider, receipt);\n  }\n}\n/**\n *  A **ContractUnknownEventPayload** is included as the last parameter to\n *  Contract Events when the event does not match any events in the ABI.\n */\nexport class ContractUnknownEventPayload extends EventPayload {\n  /**\n   *  The log with no matching events.\n   */\n  log;\n  /**\n   *  @_event:\n   */\n  constructor(contract, listener, filter, log) {\n    super(contract, listener, filter);\n    defineProperties(this, {\n      log\n    });\n  }\n  /**\n   *  Resolves to the block the event occured in.\n   */\n  async getBlock() {\n    return await this.log.getBlock();\n  }\n  /**\n   *  Resolves to the transaction the event occured in.\n   */\n  async getTransaction() {\n    return await this.log.getTransaction();\n  }\n  /**\n   *  Resolves to the transaction receipt the event occured in.\n   */\n  async getTransactionReceipt() {\n    return await this.log.getTransactionReceipt();\n  }\n}\n/**\n *  A **ContractEventPayload** is included as the last parameter to\n *  Contract Events when the event is known.\n */\nexport class ContractEventPayload extends ContractUnknownEventPayload {\n  /**\n   *  @_ignore:\n   */\n  constructor(contract, listener, filter, fragment, _log) {\n    super(contract, listener, filter, new EventLog(_log, contract.interface, fragment));\n    const args = contract.interface.decodeEventLog(fragment, this.log.data, this.log.topics);\n    defineProperties(this, {\n      args,\n      fragment\n    });\n  }\n  /**\n   *  The event name.\n   */\n  get eventName() {\n    return this.fragment.name;\n  }\n  /**\n   *  The event signature.\n   */\n  get eventSignature() {\n    return this.fragment.format();\n  }\n}", "map": {"version": 3, "names": ["Log", "TransactionReceipt", "TransactionResponse", "defineProperties", "EventPayload", "EventLog", "interface", "fragment", "args", "constructor", "log", "iface", "provider", "decodeEventLog", "data", "topics", "eventName", "name", "eventSignature", "format", "UndecodedEventLog", "error", "ContractTransactionReceipt", "tx", "logs", "map", "length", "getEvent", "ContractTransactionResponse", "wait", "confirms", "timeout", "receipt", "ContractUnknownEventPayload", "contract", "listener", "filter", "getBlock", "getTransaction", "getTransactionReceipt", "ContractEventPayload", "_log"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\contract\\wrappers.ts"], "sourcesContent": ["// import from provider.ts instead of index.ts to prevent circular dep\n// from EtherscanProvider\nimport {\n    Block, Log, TransactionReceipt, TransactionResponse\n} from \"../providers/provider.js\";\nimport { defineProperties, EventPayload } from \"../utils/index.js\";\n\nimport type { EventFragment, Interface, Result } from \"../abi/index.js\";\nimport type { Listener } from \"../utils/index.js\";\nimport type {\n    Provider\n} from \"../providers/index.js\";\n\nimport type { BaseContract } from \"./contract.js\";\nimport type { ContractEventName } from \"./types.js\";\n\n/**\n *  An **EventLog** contains additional properties parsed from the [[Log]].\n */\nexport class EventLog extends Log {\n    /**\n     *  The Contract Interface.\n     */\n    readonly interface!: Interface;\n\n    /**\n     *  The matching event.\n     */\n    readonly fragment!: EventFragment;\n\n    /**\n     *  The parsed arguments passed to the event by ``emit``.\n     */\n    readonly args!: Result;\n\n    /**\n     * @_ignore:\n     */\n    constructor(log: Log, iface: Interface, fragment: EventFragment) {\n        super(log, log.provider);\n        const args = iface.decodeEventLog(fragment, log.data, log.topics);\n        defineProperties<EventLog>(this, { args, fragment, interface: iface });\n    }\n\n    /**\n     *  The name of the event.\n     */\n    get eventName(): string { return this.fragment.name; }\n\n    /**\n     *  The signature of the event.\n     */\n    get eventSignature(): string { return this.fragment.format(); }\n}\n\n/**\n *  An **EventLog** contains additional properties parsed from the [[Log]].\n */\nexport class UndecodedEventLog extends Log {\n\n    /**\n     *  The error encounted when trying to decode the log.\n     */\n    readonly error!: Error;\n\n    /**\n     * @_ignore:\n     */\n    constructor(log: Log, error: Error) {\n        super(log, log.provider);\n        defineProperties<UndecodedEventLog>(this, { error });\n    }\n}\n\n/**\n *  A **ContractTransactionReceipt** includes the parsed logs from a\n *  [[TransactionReceipt]].\n */\nexport class ContractTransactionReceipt extends TransactionReceipt {\n    readonly #iface: Interface;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(iface: Interface, provider: Provider, tx: TransactionReceipt) {\n        super(tx, provider);\n        this.#iface = iface;\n    }\n\n    /**\n     *  The parsed logs for any [[Log]] which has a matching event in the\n     *  Contract ABI.\n     */\n    get logs(): Array<EventLog | Log> {\n        return super.logs.map((log) => {\n            const fragment = log.topics.length ? this.#iface.getEvent(log.topics[0]): null;\n            if (fragment) {\n                try {\n                    return new EventLog(log, this.#iface, fragment)\n                } catch (error: any) {\n                    return new UndecodedEventLog(log, error);\n                }\n            }\n\n            return log;\n        });\n    }\n\n}\n\n/**\n *  A **ContractTransactionResponse** will return a\n *  [[ContractTransactionReceipt]] when waited on.\n */\nexport class ContractTransactionResponse extends TransactionResponse {\n    readonly #iface: Interface;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(iface: Interface, provider: Provider, tx: TransactionResponse) {\n        super(tx, provider);\n        this.#iface = iface;\n    }\n\n    /**\n     *  Resolves once this transaction has been mined and has\n     *  %%confirms%% blocks including it (default: ``1``) with an\n     *  optional %%timeout%%.\n     *\n     *  This can resolve to ``null`` only if %%confirms%% is ``0``\n     *  and the transaction has not been mined, otherwise this will\n     *  wait until enough confirmations have completed.\n     */\n    async wait(confirms?: number, timeout?: number): Promise<null | ContractTransactionReceipt> {\n        const receipt = await super.wait(confirms, timeout);\n        if (receipt == null) { return null; }\n        return new ContractTransactionReceipt(this.#iface, this.provider, receipt);\n    }\n}\n\n/**\n *  A **ContractUnknownEventPayload** is included as the last parameter to\n *  Contract Events when the event does not match any events in the ABI.\n */\nexport  class ContractUnknownEventPayload extends EventPayload<ContractEventName> {\n    /**\n     *  The log with no matching events.\n     */\n    readonly log!: Log;\n\n    /**\n     *  @_event:\n     */\n    constructor(contract: BaseContract, listener: null | Listener, filter: ContractEventName, log: Log) {\n        super(contract, listener, filter);\n        defineProperties<ContractUnknownEventPayload>(this, { log });\n    }\n\n    /**\n     *  Resolves to the block the event occured in.\n     */\n    async getBlock(): Promise<Block> {\n        return await this.log.getBlock();\n    }\n\n    /**\n     *  Resolves to the transaction the event occured in.\n     */\n    async getTransaction(): Promise<TransactionResponse> {\n        return await this.log.getTransaction();\n    }\n\n    /**\n     *  Resolves to the transaction receipt the event occured in.\n     */\n    async getTransactionReceipt(): Promise<TransactionReceipt> {\n        return await this.log.getTransactionReceipt();\n    }\n}\n\n/**\n *  A **ContractEventPayload** is included as the last parameter to\n *  Contract Events when the event is known.\n */\nexport class ContractEventPayload extends ContractUnknownEventPayload {\n\n    /**\n     *  The matching event.\n     */\n    declare readonly fragment: EventFragment;\n\n    /**\n     *  The log, with parsed properties.\n     */\n    declare readonly log: EventLog;\n\n    /**\n     *  The parsed arguments passed to the event by ``emit``.\n     */\n    declare readonly args: Result;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(contract: BaseContract, listener: null | Listener, filter: ContractEventName, fragment: EventFragment, _log: Log) {\n        super(contract, listener, filter, new EventLog(_log, contract.interface, fragment));\n        const args = contract.interface.decodeEventLog(fragment, this.log.data, this.log.topics);\n        defineProperties<ContractEventPayload>(this, { args, fragment });\n    }\n\n    /**\n     *  The event name.\n     */\n    get eventName(): string {\n        return this.fragment.name;\n    }\n\n    /**\n     *  The event signature.\n     */\n    get eventSignature(): string {\n        return this.fragment.format();\n    }\n}\n"], "mappings": "AAAA;AACA;AACA,SACWA,GAAG,EAAEC,kBAAkB,EAAEC,mBAAmB,QAChD,0BAA0B;AACjC,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,mBAAmB;AAWlE;;;AAGA,OAAM,MAAOC,QAAS,SAAQL,GAAG;EAC7B;;;EAGSM,SAAS;EAElB;;;EAGSC,QAAQ;EAEjB;;;EAGSC,IAAI;EAEb;;;EAGAC,YAAYC,GAAQ,EAAEC,KAAgB,EAAEJ,QAAuB;IAC3D,KAAK,CAACG,GAAG,EAAEA,GAAG,CAACE,QAAQ,CAAC;IACxB,MAAMJ,IAAI,GAAGG,KAAK,CAACE,cAAc,CAACN,QAAQ,EAAEG,GAAG,CAACI,IAAI,EAAEJ,GAAG,CAACK,MAAM,CAAC;IACjEZ,gBAAgB,CAAW,IAAI,EAAE;MAAEK,IAAI;MAAED,QAAQ;MAAED,SAAS,EAAEK;IAAK,CAAE,CAAC;EAC1E;EAEA;;;EAGA,IAAIK,SAASA,CAAA;IAAa,OAAO,IAAI,CAACT,QAAQ,CAACU,IAAI;EAAE;EAErD;;;EAGA,IAAIC,cAAcA,CAAA;IAAa,OAAO,IAAI,CAACX,QAAQ,CAACY,MAAM,EAAE;EAAE;;AAGlE;;;AAGA,OAAM,MAAOC,iBAAkB,SAAQpB,GAAG;EAEtC;;;EAGSqB,KAAK;EAEd;;;EAGAZ,YAAYC,GAAQ,EAAEW,KAAY;IAC9B,KAAK,CAACX,GAAG,EAAEA,GAAG,CAACE,QAAQ,CAAC;IACxBT,gBAAgB,CAAoB,IAAI,EAAE;MAAEkB;IAAK,CAAE,CAAC;EACxD;;AAGJ;;;;AAIA,OAAM,MAAOC,0BAA2B,SAAQrB,kBAAkB;EACrD,CAAAU,KAAM;EAEf;;;EAGAF,YAAYE,KAAgB,EAAEC,QAAkB,EAAEW,EAAsB;IACpE,KAAK,CAACA,EAAE,EAAEX,QAAQ,CAAC;IACnB,IAAI,CAAC,CAAAD,KAAM,GAAGA,KAAK;EACvB;EAEA;;;;EAIA,IAAIa,IAAIA,CAAA;IACJ,OAAO,KAAK,CAACA,IAAI,CAACC,GAAG,CAAEf,GAAG,IAAI;MAC1B,MAAMH,QAAQ,GAAGG,GAAG,CAACK,MAAM,CAACW,MAAM,GAAG,IAAI,CAAC,CAAAf,KAAM,CAACgB,QAAQ,CAACjB,GAAG,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,GAAE,IAAI;MAC9E,IAAIR,QAAQ,EAAE;QACV,IAAI;UACA,OAAO,IAAIF,QAAQ,CAACK,GAAG,EAAE,IAAI,CAAC,CAAAC,KAAM,EAAEJ,QAAQ,CAAC;SAClD,CAAC,OAAOc,KAAU,EAAE;UACjB,OAAO,IAAID,iBAAiB,CAACV,GAAG,EAAEW,KAAK,CAAC;;;MAIhD,OAAOX,GAAG;IACd,CAAC,CAAC;EACN;;AAIJ;;;;AAIA,OAAM,MAAOkB,2BAA4B,SAAQ1B,mBAAmB;EACvD,CAAAS,KAAM;EAEf;;;EAGAF,YAAYE,KAAgB,EAAEC,QAAkB,EAAEW,EAAuB;IACrE,KAAK,CAACA,EAAE,EAAEX,QAAQ,CAAC;IACnB,IAAI,CAAC,CAAAD,KAAM,GAAGA,KAAK;EACvB;EAEA;;;;;;;;;EASA,MAAMkB,IAAIA,CAACC,QAAiB,EAAEC,OAAgB;IAC1C,MAAMC,OAAO,GAAG,MAAM,KAAK,CAACH,IAAI,CAACC,QAAQ,EAAEC,OAAO,CAAC;IACnD,IAAIC,OAAO,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAClC,OAAO,IAAIV,0BAA0B,CAAC,IAAI,CAAC,CAAAX,KAAM,EAAE,IAAI,CAACC,QAAQ,EAAEoB,OAAO,CAAC;EAC9E;;AAGJ;;;;AAIA,OAAM,MAAQC,2BAA4B,SAAQ7B,YAA+B;EAC7E;;;EAGSM,GAAG;EAEZ;;;EAGAD,YAAYyB,QAAsB,EAAEC,QAAyB,EAAEC,MAAyB,EAAE1B,GAAQ;IAC9F,KAAK,CAACwB,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,CAAC;IACjCjC,gBAAgB,CAA8B,IAAI,EAAE;MAAEO;IAAG,CAAE,CAAC;EAChE;EAEA;;;EAGA,MAAM2B,QAAQA,CAAA;IACV,OAAO,MAAM,IAAI,CAAC3B,GAAG,CAAC2B,QAAQ,EAAE;EACpC;EAEA;;;EAGA,MAAMC,cAAcA,CAAA;IAChB,OAAO,MAAM,IAAI,CAAC5B,GAAG,CAAC4B,cAAc,EAAE;EAC1C;EAEA;;;EAGA,MAAMC,qBAAqBA,CAAA;IACvB,OAAO,MAAM,IAAI,CAAC7B,GAAG,CAAC6B,qBAAqB,EAAE;EACjD;;AAGJ;;;;AAIA,OAAM,MAAOC,oBAAqB,SAAQP,2BAA2B;EAiBjE;;;EAGAxB,YAAYyB,QAAsB,EAAEC,QAAyB,EAAEC,MAAyB,EAAE7B,QAAuB,EAAEkC,IAAS;IACxH,KAAK,CAACP,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAE,IAAI/B,QAAQ,CAACoC,IAAI,EAAEP,QAAQ,CAAC5B,SAAS,EAAEC,QAAQ,CAAC,CAAC;IACnF,MAAMC,IAAI,GAAG0B,QAAQ,CAAC5B,SAAS,CAACO,cAAc,CAACN,QAAQ,EAAE,IAAI,CAACG,GAAG,CAACI,IAAI,EAAE,IAAI,CAACJ,GAAG,CAACK,MAAM,CAAC;IACxFZ,gBAAgB,CAAuB,IAAI,EAAE;MAAEK,IAAI;MAAED;IAAQ,CAAE,CAAC;EACpE;EAEA;;;EAGA,IAAIS,SAASA,CAAA;IACT,OAAO,IAAI,CAACT,QAAQ,CAACU,IAAI;EAC7B;EAEA;;;EAGA,IAAIC,cAAcA,CAAA;IACd,OAAO,IAAI,CAACX,QAAQ,CAACY,MAAM,EAAE;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}