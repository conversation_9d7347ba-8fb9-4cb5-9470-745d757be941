{"ast": null, "code": "/**\n *  About bytes32 strings...\n *\n *  @_docloc: api/utils:Bytes32 Strings\n */\nimport { getBytes, toUtf8Bytes, toUtf8String, zeroPadBytes } from \"../utils/index.js\";\n/**\n *  Encodes %%text%% as a Bytes32 string.\n */\nexport function encodeBytes32String(text) {\n  // Get the bytes\n  const bytes = toUtf8Bytes(text);\n  // Check we have room for null-termination\n  if (bytes.length > 31) {\n    throw new Error(\"bytes32 string must be less than 32 bytes\");\n  }\n  // Zero-pad (implicitly null-terminates)\n  return zeroPadBytes(bytes, 32);\n}\n/**\n *  Encodes the Bytes32-encoded %%bytes%% into a string.\n */\nexport function decodeBytes32String(_bytes) {\n  const data = getBytes(_bytes, \"bytes\");\n  // Must be 32 bytes with a null-termination\n  if (data.length !== 32) {\n    throw new Error(\"invalid bytes32 - not 32 bytes long\");\n  }\n  if (data[31] !== 0) {\n    throw new Error(\"invalid bytes32 string - no null terminator\");\n  }\n  // Find the null termination\n  let length = 31;\n  while (data[length - 1] === 0) {\n    length--;\n  }\n  // Determine the string value\n  return toUtf8String(data.slice(0, length));\n}", "map": {"version": 3, "names": ["getBytes", "toUtf8Bytes", "toUtf8String", "zeroPadBytes", "encodeBytes32String", "text", "bytes", "length", "Error", "decodeBytes32String", "_bytes", "data", "slice"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\bytes32.ts"], "sourcesContent": ["/**\n *  About bytes32 strings...\n *\n *  @_docloc: api/utils:Bytes32 Strings\n */\n\nimport {\n    getBytes, toUtf8Bytes, toUtf8String, zeroPadBytes\n} from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n/**\n *  Encodes %%text%% as a Bytes32 string.\n */\nexport function encodeBytes32String(text: string): string {\n\n    // Get the bytes\n    const bytes = toUtf8Bytes(text);\n\n    // Check we have room for null-termination\n    if (bytes.length > 31) { throw new Error(\"bytes32 string must be less than 32 bytes\"); }\n\n    // Zero-pad (implicitly null-terminates)\n    return zeroPadBytes(bytes, 32);\n}\n\n/**\n *  Encodes the Bytes32-encoded %%bytes%% into a string.\n */\nexport function decodeBytes32String(_bytes: BytesLike): string {\n    const data = getBytes(_bytes, \"bytes\");\n\n    // Must be 32 bytes with a null-termination\n    if (data.length !== 32) { throw new Error(\"invalid bytes32 - not 32 bytes long\"); }\n    if (data[31] !== 0) { throw new Error(\"invalid bytes32 string - no null terminator\"); }\n\n    // Find the null termination\n    let length = 31;\n    while (data[length - 1] === 0) { length--; }\n\n    // Determine the string value\n    return toUtf8String(data.slice(0, length));\n}\n\n"], "mappings": "AAAA;;;;;AAMA,SACIA,QAAQ,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,QAC9C,mBAAmB;AAI1B;;;AAGA,OAAM,SAAUC,mBAAmBA,CAACC,IAAY;EAE5C;EACA,MAAMC,KAAK,GAAGL,WAAW,CAACI,IAAI,CAAC;EAE/B;EACA,IAAIC,KAAK,CAACC,MAAM,GAAG,EAAE,EAAE;IAAE,MAAM,IAAIC,KAAK,CAAC,2CAA2C,CAAC;;EAErF;EACA,OAAOL,YAAY,CAACG,KAAK,EAAE,EAAE,CAAC;AAClC;AAEA;;;AAGA,OAAM,SAAUG,mBAAmBA,CAACC,MAAiB;EACjD,MAAMC,IAAI,GAAGX,QAAQ,CAACU,MAAM,EAAE,OAAO,CAAC;EAEtC;EACA,IAAIC,IAAI,CAACJ,MAAM,KAAK,EAAE,EAAE;IAAE,MAAM,IAAIC,KAAK,CAAC,qCAAqC,CAAC;;EAChF,IAAIG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;IAAE,MAAM,IAAIH,KAAK,CAAC,6CAA6C,CAAC;;EAEpF;EACA,IAAID,MAAM,GAAG,EAAE;EACf,OAAOI,IAAI,CAACJ,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;IAAEA,MAAM,EAAE;;EAEzC;EACA,OAAOL,YAAY,CAACS,IAAI,CAACC,KAAK,CAAC,CAAC,EAAEL,MAAM,CAAC,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}