{"ast": null, "code": "/**\n *  Property helper functions.\n *\n *  @_subsection api/utils:Properties  [about-properties]\n */\nfunction checkType(value, type, name) {\n  const types = type.split(\"|\").map(t => t.trim());\n  for (let i = 0; i < types.length; i++) {\n    switch (type) {\n      case \"any\":\n        return;\n      case \"bigint\":\n      case \"boolean\":\n      case \"number\":\n      case \"string\":\n        if (typeof value === type) {\n          return;\n        }\n    }\n  }\n  const error = new Error(`invalid value for type ${type}`);\n  error.code = \"INVALID_ARGUMENT\";\n  error.argument = `value.${name}`;\n  error.value = value;\n  throw error;\n}\n/**\n *  Resolves to a new object that is a copy of %%value%%, but with all\n *  values resolved.\n */\nexport async function resolveProperties(value) {\n  const keys = Object.keys(value);\n  const results = await Promise.all(keys.map(k => Promise.resolve(value[k])));\n  return results.reduce((accum, v, index) => {\n    accum[keys[index]] = v;\n    return accum;\n  }, {});\n}\n/**\n *  Assigns the %%values%% to %%target%% as read-only values.\n *\n *  It %%types%% is specified, the values are checked.\n */\nexport function defineProperties(target, values, types) {\n  for (let key in values) {\n    let value = values[key];\n    const type = types ? types[key] : null;\n    if (type) {\n      checkType(value, type, key);\n    }\n    Object.defineProperty(target, key, {\n      enumerable: true,\n      value,\n      writable: false\n    });\n  }\n}", "map": {"version": 3, "names": ["checkType", "value", "type", "name", "types", "split", "map", "t", "trim", "i", "length", "error", "Error", "code", "argument", "resolveProperties", "keys", "Object", "results", "Promise", "all", "k", "resolve", "reduce", "accum", "v", "index", "defineProperties", "target", "values", "key", "defineProperty", "enumerable", "writable"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\properties.ts"], "sourcesContent": ["/**\n *  Property helper functions.\n *\n *  @_subsection api/utils:Properties  [about-properties]\n */\n\nfunction checkType(value: any, type: string, name: string): void {\n    const types = type.split(\"|\").map(t => t.trim());\n    for (let i = 0; i < types.length; i++) {\n        switch (type) {\n            case \"any\":\n                return;\n            case \"bigint\":\n            case \"boolean\":\n            case \"number\":\n            case \"string\":\n                if (typeof(value) === type) { return; }\n        }\n    }\n\n    const error: any = new Error(`invalid value for type ${ type }`);\n    error.code = \"INVALID_ARGUMENT\";\n    error.argument = `value.${ name }`;\n    error.value = value;\n\n    throw error;\n}\n\n/**\n *  Resolves to a new object that is a copy of %%value%%, but with all\n *  values resolved.\n */\nexport async function resolveProperties<T>(value: { [ P in keyof T ]: T[P] | Promise<T[P]>}): Promise<T> {\n    const keys = Object.keys(value);\n    const results = await Promise.all(keys.map((k) => Promise.resolve(value[<keyof T>k])));\n    return results.reduce((accum: any, v, index) => {\n        accum[keys[index]] = v;\n        return accum;\n    }, <{ [ P in keyof T]: T[P] }>{ });\n}\n\n/**\n *  Assigns the %%values%% to %%target%% as read-only values.\n *\n *  It %%types%% is specified, the values are checked.\n */\nexport function defineProperties<T>(\n target: T,\n values: { [ K in keyof T ]?: T[K] },\n types?: { [ K in keyof T ]?: string }): void {\n\n    for (let key in values) {\n        let value = values[key];\n\n        const type = (types ? types[key]: null);\n        if (type) { checkType(value, type, key); }\n\n        Object.defineProperty(target, key, { enumerable: true, value, writable: false });\n    }\n}\n"], "mappings": "AAAA;;;;;AAMA,SAASA,SAASA,CAACC,KAAU,EAAEC,IAAY,EAAEC,IAAY;EACrD,MAAMC,KAAK,GAAGF,IAAI,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,EAAE,CAAC;EAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,QAAQP,IAAI;MACR,KAAK,KAAK;QACN;MACJ,KAAK,QAAQ;MACb,KAAK,SAAS;MACd,KAAK,QAAQ;MACb,KAAK,QAAQ;QACT,IAAI,OAAOD,KAAM,KAAKC,IAAI,EAAE;UAAE;;;;EAI1C,MAAMS,KAAK,GAAQ,IAAIC,KAAK,CAAC,0BAA2BV,IAAK,EAAE,CAAC;EAChES,KAAK,CAACE,IAAI,GAAG,kBAAkB;EAC/BF,KAAK,CAACG,QAAQ,GAAG,SAAUX,IAAK,EAAE;EAClCQ,KAAK,CAACV,KAAK,GAAGA,KAAK;EAEnB,MAAMU,KAAK;AACf;AAEA;;;;AAIA,OAAO,eAAeI,iBAAiBA,CAAId,KAAgD;EACvF,MAAMe,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACf,KAAK,CAAC;EAC/B,MAAMiB,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACJ,IAAI,CAACV,GAAG,CAAEe,CAAC,IAAKF,OAAO,CAACG,OAAO,CAACrB,KAAK,CAAUoB,CAAC,CAAC,CAAC,CAAC,CAAC;EACtF,OAAOH,OAAO,CAACK,MAAM,CAAC,CAACC,KAAU,EAAEC,CAAC,EAAEC,KAAK,KAAI;IAC3CF,KAAK,CAACR,IAAI,CAACU,KAAK,CAAC,CAAC,GAAGD,CAAC;IACtB,OAAOD,KAAK;EAChB,CAAC,EAA6B,EAAG,CAAC;AACtC;AAEA;;;;;AAKA,OAAM,SAAUG,gBAAgBA,CAC/BC,MAAS,EACTC,MAAmC,EACnCzB,KAAqC;EAElC,KAAK,IAAI0B,GAAG,IAAID,MAAM,EAAE;IACpB,IAAI5B,KAAK,GAAG4B,MAAM,CAACC,GAAG,CAAC;IAEvB,MAAM5B,IAAI,GAAIE,KAAK,GAAGA,KAAK,CAAC0B,GAAG,CAAC,GAAE,IAAK;IACvC,IAAI5B,IAAI,EAAE;MAAEF,SAAS,CAACC,KAAK,EAAEC,IAAI,EAAE4B,GAAG,CAAC;;IAEvCb,MAAM,CAACc,cAAc,CAACH,MAAM,EAAEE,GAAG,EAAE;MAAEE,UAAU,EAAE,IAAI;MAAE/B,KAAK;MAAEgC,QAAQ,EAAE;IAAK,CAAE,CAAC;;AAExF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}