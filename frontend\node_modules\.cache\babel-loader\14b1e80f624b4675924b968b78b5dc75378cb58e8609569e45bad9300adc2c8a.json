{"ast": null, "code": "/**\n *  A fundamental building block of Ethereum is the underlying\n *  cryptographic primitives.\n *\n *  @_section: api/crypto:Cryptographic Functions   [about-crypto]\n */\nnull;\n// We import all these so we can export lock()\nimport { computeHmac } from \"./hmac.js\";\nimport { keccak256 } from \"./keccak.js\";\nimport { ripemd160 } from \"./ripemd160.js\";\nimport { pbkdf2 } from \"./pbkdf2.js\";\nimport { randomBytes } from \"./random.js\";\nimport { scrypt, scryptSync } from \"./scrypt.js\";\nimport { sha256, sha512 } from \"./sha2.js\";\nexport { computeHmac, randomBytes, keccak256, ripemd160, sha256, sha512, pbkdf2, scrypt, scryptSync };\nexport { SigningKey } from \"./signing-key.js\";\nexport { Signature } from \"./signature.js\";\n/**\n *  Once called, prevents any future change to the underlying cryptographic\n *  primitives using the ``.register`` feature for hooks.\n */\nfunction lock() {\n  computeHmac.lock();\n  keccak256.lock();\n  pbkdf2.lock();\n  randomBytes.lock();\n  ripemd160.lock();\n  scrypt.lock();\n  scryptSync.lock();\n  sha256.lock();\n  sha512.lock();\n  randomBytes.lock();\n}\nexport { lock };", "map": {"version": 3, "names": ["computeHmac", "keccak256", "ripemd160", "pbkdf2", "randomBytes", "scrypt", "scryptSync", "sha256", "sha512", "SigningKey", "Signature", "lock"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\index.ts"], "sourcesContent": ["/**\n *  A fundamental building block of Ethereum is the underlying\n *  cryptographic primitives.\n *\n *  @_section: api/crypto:Cryptographic Functions   [about-crypto]\n */\n\nnull\n\n// We import all these so we can export lock()\nimport { computeHmac } from \"./hmac.js\";\nimport { keccak256 } from \"./keccak.js\";\nimport { ripemd160 } from \"./ripemd160.js\";\nimport { pbkdf2 } from \"./pbkdf2.js\";\nimport { randomBytes } from \"./random.js\";\nimport { scrypt, scryptSync } from \"./scrypt.js\";\nimport { sha256, sha512 } from \"./sha2.js\";\n\nexport {\n    computeHmac,\n\n    randomBytes,\n\n    keccak256,\n    ripemd160,\n    sha256, sha512,\n\n    pbkdf2,\n    scrypt, scryptSync\n};\n\nexport { SigningKey } from \"./signing-key.js\";\nexport { Signature } from \"./signature.js\";\n\n/**\n *  Once called, prevents any future change to the underlying cryptographic\n *  primitives using the ``.register`` feature for hooks.\n */\nfunction lock(): void {\n    computeHmac.lock();\n    keccak256.lock();\n    pbkdf2.lock();\n    randomBytes.lock();\n    ripemd160.lock();\n    scrypt.lock();\n    scryptSync.lock();\n    sha256.lock();\n    sha512.lock();\n    randomBytes.lock();\n}\n\nexport { lock };\n\n/////////////////////////////\n// Types\n\nexport type { ProgressCallback } from \"./scrypt.js\";\n\nexport type { SignatureLike } from \"./signature.js\";\n"], "mappings": "AAAA;;;;;;AAOA,IAAI;AAEJ;AACA,SAASA,WAAW,QAAQ,WAAW;AACvC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,EAAEC,UAAU,QAAQ,aAAa;AAChD,SAASC,MAAM,EAAEC,MAAM,QAAQ,WAAW;AAE1C,SACIR,WAAW,EAEXI,WAAW,EAEXH,SAAS,EACTC,SAAS,EACTK,MAAM,EAAEC,MAAM,EAEdL,MAAM,EACNE,MAAM,EAAEC,UAAU;AAGtB,SAASG,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,SAAS,QAAQ,gBAAgB;AAE1C;;;;AAIA,SAASC,IAAIA,CAAA;EACTX,WAAW,CAACW,IAAI,EAAE;EAClBV,SAAS,CAACU,IAAI,EAAE;EAChBR,MAAM,CAACQ,IAAI,EAAE;EACbP,WAAW,CAACO,IAAI,EAAE;EAClBT,SAAS,CAACS,IAAI,EAAE;EAChBN,MAAM,CAACM,IAAI,EAAE;EACbL,UAAU,CAACK,IAAI,EAAE;EACjBJ,MAAM,CAACI,IAAI,EAAE;EACbH,MAAM,CAACG,IAAI,EAAE;EACbP,WAAW,CAACO,IAAI,EAAE;AACtB;AAEA,SAASA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}