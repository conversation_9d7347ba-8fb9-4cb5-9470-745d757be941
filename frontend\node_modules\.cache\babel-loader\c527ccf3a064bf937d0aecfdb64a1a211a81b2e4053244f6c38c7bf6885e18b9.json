{"ast": null, "code": "import { assert, isHexString } from \"../utils/index.js\";\nfunction copy(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}\n/**\n *  Return the polling subscriber for common events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport function getPollingSubscriber(provider, event) {\n  if (event === \"block\") {\n    return new PollingBlockSubscriber(provider);\n  }\n  if (isHexString(event, 32)) {\n    return new PollingTransactionSubscriber(provider, event);\n  }\n  assert(false, \"unsupported polling event\", \"UNSUPPORTED_OPERATION\", {\n    operation: \"getPollingSubscriber\",\n    info: {\n      event\n    }\n  });\n}\n// @TODO: refactor this\n/**\n *  A **PollingBlockSubscriber** polls at a regular interval for a change\n *  in the block number.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingBlockSubscriber {\n  #provider;\n  #poller;\n  #interval;\n  // The most recent block we have scanned for events. The value -2\n  // indicates we still need to fetch an initial block number\n  #blockNumber;\n  /**\n   *  Create a new **PollingBlockSubscriber** attached to %%provider%%.\n   */\n  constructor(provider) {\n    this.#provider = provider;\n    this.#poller = null;\n    this.#interval = 4000;\n    this.#blockNumber = -2;\n  }\n  /**\n   *  The polling interval.\n   */\n  get pollingInterval() {\n    return this.#interval;\n  }\n  set pollingInterval(value) {\n    this.#interval = value;\n  }\n  async #poll() {\n    try {\n      const blockNumber = await this.#provider.getBlockNumber();\n      // Bootstrap poll to setup our initial block number\n      if (this.#blockNumber === -2) {\n        this.#blockNumber = blockNumber;\n        return;\n      }\n      // @TODO: Put a cap on the maximum number of events per loop?\n      if (blockNumber !== this.#blockNumber) {\n        for (let b = this.#blockNumber + 1; b <= blockNumber; b++) {\n          // We have been stopped\n          if (this.#poller == null) {\n            return;\n          }\n          await this.#provider.emit(\"block\", b);\n        }\n        this.#blockNumber = blockNumber;\n      }\n    } catch (error) {\n      // @TODO: Minor bump, add an \"error\" event to let subscribers\n      //        know things went awry.\n      //console.log(error);\n    }\n    // We have been stopped\n    if (this.#poller == null) {\n      return;\n    }\n    this.#poller = this.#provider._setTimeout(this.#poll.bind(this), this.#interval);\n  }\n  start() {\n    if (this.#poller) {\n      return;\n    }\n    this.#poller = this.#provider._setTimeout(this.#poll.bind(this), this.#interval);\n    this.#poll();\n  }\n  stop() {\n    if (!this.#poller) {\n      return;\n    }\n    this.#provider._clearTimeout(this.#poller);\n    this.#poller = null;\n  }\n  pause(dropWhilePaused) {\n    this.stop();\n    if (dropWhilePaused) {\n      this.#blockNumber = -2;\n    }\n  }\n  resume() {\n    this.start();\n  }\n}\n/**\n *  An **OnBlockSubscriber** can be sub-classed, with a [[_poll]]\n *  implmentation which will be called on every new block.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class OnBlockSubscriber {\n  #provider;\n  #poll;\n  #running;\n  /**\n   *  Create a new **OnBlockSubscriber** attached to %%provider%%.\n   */\n  constructor(provider) {\n    this.#provider = provider;\n    this.#running = false;\n    this.#poll = blockNumber => {\n      this._poll(blockNumber, this.#provider);\n    };\n  }\n  /**\n   *  Called on every new block.\n   */\n  async _poll(blockNumber, provider) {\n    throw new Error(\"sub-classes must override this\");\n  }\n  start() {\n    if (this.#running) {\n      return;\n    }\n    this.#running = true;\n    this.#poll(-2);\n    this.#provider.on(\"block\", this.#poll);\n  }\n  stop() {\n    if (!this.#running) {\n      return;\n    }\n    this.#running = false;\n    this.#provider.off(\"block\", this.#poll);\n  }\n  pause(dropWhilePaused) {\n    this.stop();\n  }\n  resume() {\n    this.start();\n  }\n}\nexport class PollingBlockTagSubscriber extends OnBlockSubscriber {\n  #tag;\n  #lastBlock;\n  constructor(provider, tag) {\n    super(provider);\n    this.#tag = tag;\n    this.#lastBlock = -2;\n  }\n  pause(dropWhilePaused) {\n    if (dropWhilePaused) {\n      this.#lastBlock = -2;\n    }\n    super.pause(dropWhilePaused);\n  }\n  async _poll(blockNumber, provider) {\n    const block = await provider.getBlock(this.#tag);\n    if (block == null) {\n      return;\n    }\n    if (this.#lastBlock === -2) {\n      this.#lastBlock = block.number;\n    } else if (block.number > this.#lastBlock) {\n      provider.emit(this.#tag, block.number);\n      this.#lastBlock = block.number;\n    }\n  }\n}\n/**\n *  @_ignore:\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingOrphanSubscriber extends OnBlockSubscriber {\n  #filter;\n  constructor(provider, filter) {\n    super(provider);\n    this.#filter = copy(filter);\n  }\n  async _poll(blockNumber, provider) {\n    throw new Error(\"@TODO\");\n    console.log(this.#filter);\n  }\n}\n/**\n *  A **PollingTransactionSubscriber** will poll for a given transaction\n *  hash for its receipt.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingTransactionSubscriber extends OnBlockSubscriber {\n  #hash;\n  /**\n   *  Create a new **PollingTransactionSubscriber** attached to\n   *  %%provider%%, listening for %%hash%%.\n   */\n  constructor(provider, hash) {\n    super(provider);\n    this.#hash = hash;\n  }\n  async _poll(blockNumber, provider) {\n    const tx = await provider.getTransactionReceipt(this.#hash);\n    if (tx) {\n      provider.emit(this.#hash, tx);\n    }\n  }\n}\n/**\n *  A **PollingEventSubscriber** will poll for a given filter for its logs.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingEventSubscriber {\n  #provider;\n  #filter;\n  #poller;\n  #running;\n  // The most recent block we have scanned for events. The value -2\n  // indicates we still need to fetch an initial block number\n  #blockNumber;\n  /**\n   *  Create a new **PollingTransactionSubscriber** attached to\n   *  %%provider%%, listening for %%filter%%.\n   */\n  constructor(provider, filter) {\n    this.#provider = provider;\n    this.#filter = copy(filter);\n    this.#poller = this.#poll.bind(this);\n    this.#running = false;\n    this.#blockNumber = -2;\n  }\n  async #poll(blockNumber) {\n    // The initial block hasn't been determined yet\n    if (this.#blockNumber === -2) {\n      return;\n    }\n    const filter = copy(this.#filter);\n    filter.fromBlock = this.#blockNumber + 1;\n    filter.toBlock = blockNumber;\n    const logs = await this.#provider.getLogs(filter);\n    // No logs could just mean the node has not indexed them yet,\n    // so we keep a sliding window of 60 blocks to keep scanning\n    if (logs.length === 0) {\n      if (this.#blockNumber < blockNumber - 60) {\n        this.#blockNumber = blockNumber - 60;\n      }\n      return;\n    }\n    for (const log of logs) {\n      this.#provider.emit(this.#filter, log);\n      // Only advance the block number when logs were found to\n      // account for networks (like BNB and Polygon) which may\n      // sacrifice event consistency for block event speed\n      this.#blockNumber = log.blockNumber;\n    }\n  }\n  start() {\n    if (this.#running) {\n      return;\n    }\n    this.#running = true;\n    if (this.#blockNumber === -2) {\n      this.#provider.getBlockNumber().then(blockNumber => {\n        this.#blockNumber = blockNumber;\n      });\n    }\n    this.#provider.on(\"block\", this.#poller);\n  }\n  stop() {\n    if (!this.#running) {\n      return;\n    }\n    this.#running = false;\n    this.#provider.off(\"block\", this.#poller);\n  }\n  pause(dropWhilePaused) {\n    this.stop();\n    if (dropWhilePaused) {\n      this.#blockNumber = -2;\n    }\n  }\n  resume() {\n    this.start();\n  }\n}", "map": {"version": 3, "names": ["assert", "isHexString", "copy", "obj", "JSON", "parse", "stringify", "getPollingSubscriber", "provider", "event", "PollingBlockSubscriber", "PollingTransactionSubscriber", "operation", "info", "poller", "interval", "blockNumber", "constructor", "pollingInterval", "value", "poll", "#poll", "getBlockNumber", "b", "emit", "error", "_setTimeout", "bind", "start", "stop", "_clearTimeout", "pause", "dropWhilePaused", "resume", "OnBlockSubscriber", "running", "_poll", "Error", "on", "off", "PollingBlockTagSubscriber", "tag", "lastBlock", "block", "getBlock", "number", "PollingOrphanSubscriber", "filter", "console", "log", "hash", "tx", "getTransactionReceipt", "PollingEventSubscriber", "fromBlock", "toBlock", "logs", "getLogs", "length", "then"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\subscriber-polling.ts"], "sourcesContent": ["import { assert, isHexString } from \"../utils/index.js\";\n\nimport type { AbstractProvider, Subscriber } from \"./abstract-provider.js\";\nimport type { EventFilter, OrphanFilter, ProviderEvent } from \"./provider.js\";\n\nfunction copy(obj: any): any {\n    return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n *  Return the polling subscriber for common events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport function getPollingSubscriber(provider: AbstractProvider, event: ProviderEvent): Subscriber {\n    if (event === \"block\") { return new PollingBlockSubscriber(provider); }\n    if (isHexString(event, 32)) { return new PollingTransactionSubscriber(provider, event); }\n\n    assert(false, \"unsupported polling event\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"getPollingSubscriber\", info: { event }\n    });\n}\n\n// @TODO: refactor this\n\n/**\n *  A **PollingBlockSubscriber** polls at a regular interval for a change\n *  in the block number.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingBlockSubscriber implements Subscriber {\n    #provider: AbstractProvider;\n    #poller: null | number;\n\n    #interval: number;\n\n    // The most recent block we have scanned for events. The value -2\n    // indicates we still need to fetch an initial block number\n    #blockNumber: number;\n\n    /**\n     *  Create a new **PollingBlockSubscriber** attached to %%provider%%.\n     */\n    constructor(provider: AbstractProvider) {\n        this.#provider = provider;\n        this.#poller = null;\n        this.#interval = 4000;\n\n        this.#blockNumber = -2;\n    }\n\n    /**\n     *  The polling interval.\n     */\n    get pollingInterval(): number { return this.#interval; }\n    set pollingInterval(value: number) { this.#interval = value; }\n\n    async #poll(): Promise<void> {\n        try {\n            const blockNumber = await this.#provider.getBlockNumber();\n\n            // Bootstrap poll to setup our initial block number\n            if (this.#blockNumber === -2) {\n                this.#blockNumber = blockNumber;\n                return;\n            }\n\n            // @TODO: Put a cap on the maximum number of events per loop?\n\n            if (blockNumber !== this.#blockNumber) {\n                for (let b = this.#blockNumber + 1; b <= blockNumber; b++) {\n                    // We have been stopped\n                    if (this.#poller == null) { return; }\n\n                    await this.#provider.emit(\"block\", b);\n                }\n\n                this.#blockNumber = blockNumber;\n            }\n\n        } catch (error) {\n            // @TODO: Minor bump, add an \"error\" event to let subscribers\n            //        know things went awry.\n            //console.log(error);\n        }\n\n        // We have been stopped\n        if (this.#poller == null) { return; }\n\n        this.#poller = this.#provider._setTimeout(this.#poll.bind(this), this.#interval);\n    }\n\n    start(): void {\n        if (this.#poller) { return; }\n        this.#poller = this.#provider._setTimeout(this.#poll.bind(this), this.#interval);\n        this.#poll();\n    }\n\n    stop(): void {\n        if (!this.#poller) { return; }\n        this.#provider._clearTimeout(this.#poller);\n        this.#poller = null;\n    }\n\n    pause(dropWhilePaused?: boolean): void {\n        this.stop();\n        if (dropWhilePaused) { this.#blockNumber = -2; }\n    }\n\n    resume(): void {\n        this.start();\n    }\n}\n\n\n/**\n *  An **OnBlockSubscriber** can be sub-classed, with a [[_poll]]\n *  implmentation which will be called on every new block.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class OnBlockSubscriber implements Subscriber {\n    #provider: AbstractProvider;\n    #poll: (b: number) => void;\n    #running: boolean;\n\n    /**\n     *  Create a new **OnBlockSubscriber** attached to %%provider%%.\n     */\n    constructor(provider: AbstractProvider) {\n        this.#provider = provider;\n        this.#running = false;\n        this.#poll = (blockNumber: number) => {\n            this._poll(blockNumber, this.#provider);\n        }\n    }\n\n    /**\n     *  Called on every new block.\n     */\n    async _poll(blockNumber: number, provider: AbstractProvider): Promise<void> {\n        throw new Error(\"sub-classes must override this\");\n    }\n\n    start(): void {\n        if (this.#running) { return; }\n        this.#running = true;\n\n        this.#poll(-2);\n        this.#provider.on(\"block\", this.#poll);\n    }\n\n    stop(): void {\n        if (!this.#running) { return; }\n        this.#running = false;\n\n        this.#provider.off(\"block\", this.#poll);\n    }\n\n    pause(dropWhilePaused?: boolean): void { this.stop(); }\n    resume(): void { this.start(); }\n}\n\nexport class PollingBlockTagSubscriber extends OnBlockSubscriber {\n    readonly #tag: string;\n    #lastBlock: number;\n\n    constructor(provider: AbstractProvider, tag: string) {\n        super(provider);\n        this.#tag = tag;\n        this.#lastBlock = -2;\n    }\n\n    pause(dropWhilePaused?: boolean): void {\n        if (dropWhilePaused) { this.#lastBlock = -2; }\n        super.pause(dropWhilePaused);\n    }\n\n    async _poll(blockNumber: number, provider: AbstractProvider): Promise<void> {\n        const block = await provider.getBlock(this.#tag);\n        if (block == null) { return; }\n\n        if (this.#lastBlock === -2) {\n            this.#lastBlock = block.number;\n        } else if (block.number > this.#lastBlock) {\n            provider.emit(this.#tag, block.number);\n            this.#lastBlock = block.number;\n        }\n    }\n}\n\n\n/**\n *  @_ignore:\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingOrphanSubscriber extends OnBlockSubscriber {\n    #filter: OrphanFilter;\n\n    constructor(provider: AbstractProvider, filter: OrphanFilter) {\n        super(provider);\n        this.#filter = copy(filter);\n    }\n\n    async _poll(blockNumber: number, provider: AbstractProvider): Promise<void> {\n        throw new Error(\"@TODO\");\n        console.log(this.#filter);\n    }\n}\n\n/**\n *  A **PollingTransactionSubscriber** will poll for a given transaction\n *  hash for its receipt.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingTransactionSubscriber extends OnBlockSubscriber {\n    #hash: string;\n\n    /**\n     *  Create a new **PollingTransactionSubscriber** attached to\n     *  %%provider%%, listening for %%hash%%.\n     */\n    constructor(provider: AbstractProvider, hash: string) {\n        super(provider);\n        this.#hash = hash;\n    }\n\n    async _poll(blockNumber: number, provider: AbstractProvider): Promise<void> {\n        const tx = await provider.getTransactionReceipt(this.#hash);\n        if (tx) { provider.emit(this.#hash, tx); }\n    }\n}\n\n/**\n *  A **PollingEventSubscriber** will poll for a given filter for its logs.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class PollingEventSubscriber implements Subscriber {\n    #provider: AbstractProvider;\n    #filter: EventFilter;\n    #poller: (b: number) => void;\n\n    #running: boolean;\n\n    // The most recent block we have scanned for events. The value -2\n    // indicates we still need to fetch an initial block number\n    #blockNumber: number;\n\n    /**\n     *  Create a new **PollingTransactionSubscriber** attached to\n     *  %%provider%%, listening for %%filter%%.\n     */\n    constructor(provider: AbstractProvider, filter: EventFilter) {\n        this.#provider = provider;\n        this.#filter = copy(filter);\n        this.#poller = this.#poll.bind(this);\n        this.#running = false;\n        this.#blockNumber = -2;\n    }\n\n    async #poll(blockNumber: number): Promise<void> {\n        // The initial block hasn't been determined yet\n        if (this.#blockNumber === -2) { return; }\n\n        const filter = copy(this.#filter);\n        filter.fromBlock = this.#blockNumber + 1;\n        filter.toBlock = blockNumber;\n\n        const logs = await this.#provider.getLogs(filter);\n\n        // No logs could just mean the node has not indexed them yet,\n        // so we keep a sliding window of 60 blocks to keep scanning\n        if (logs.length === 0) {\n            if (this.#blockNumber < blockNumber - 60) {\n                this.#blockNumber = blockNumber - 60;\n            }\n            return;\n        }\n\n        for (const log of logs) {\n            this.#provider.emit(this.#filter, log);\n\n            // Only advance the block number when logs were found to\n            // account for networks (like BNB and Polygon) which may\n            // sacrifice event consistency for block event speed\n            this.#blockNumber = log.blockNumber;\n        }\n    }\n\n    start(): void {\n        if (this.#running) { return; }\n        this.#running = true;\n\n        if (this.#blockNumber === -2) {\n            this.#provider.getBlockNumber().then((blockNumber) => {\n                this.#blockNumber = blockNumber;\n            });\n        }\n        this.#provider.on(\"block\", this.#poller);\n    }\n\n    stop(): void {\n        if (!this.#running) { return; }\n        this.#running = false;\n\n        this.#provider.off(\"block\", this.#poller);\n    }\n\n    pause(dropWhilePaused?: boolean): void {\n        this.stop();\n        if (dropWhilePaused) { this.#blockNumber = -2; }\n    }\n\n    resume(): void {\n        this.start();\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,WAAW,QAAQ,mBAAmB;AAKvD,SAASC,IAAIA,CAACC,GAAQ;EAClB,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAAC,CAAC;AAC1C;AAEA;;;;;AAKA,OAAM,SAAUI,oBAAoBA,CAACC,QAA0B,EAAEC,KAAoB;EACjF,IAAIA,KAAK,KAAK,OAAO,EAAE;IAAE,OAAO,IAAIC,sBAAsB,CAACF,QAAQ,CAAC;;EACpE,IAAIP,WAAW,CAACQ,KAAK,EAAE,EAAE,CAAC,EAAE;IAAE,OAAO,IAAIE,4BAA4B,CAACH,QAAQ,EAAEC,KAAK,CAAC;;EAEtFT,MAAM,CAAC,KAAK,EAAE,2BAA2B,EAAE,uBAAuB,EAAE;IAChEY,SAAS,EAAE,sBAAsB;IAAEC,IAAI,EAAE;MAAEJ;IAAK;GACnD,CAAC;AACN;AAEA;AAEA;;;;;;AAMA,OAAM,MAAOC,sBAAsB;EAC/B,CAAAF,QAAS;EACT,CAAAM,MAAO;EAEP,CAAAC,QAAS;EAET;EACA;EACA,CAAAC,WAAY;EAEZ;;;EAGAC,YAAYT,QAA0B;IAClC,IAAI,CAAC,CAAAA,QAAS,GAAGA,QAAQ;IACzB,IAAI,CAAC,CAAAM,MAAO,GAAG,IAAI;IACnB,IAAI,CAAC,CAAAC,QAAS,GAAG,IAAI;IAErB,IAAI,CAAC,CAAAC,WAAY,GAAG,CAAC,CAAC;EAC1B;EAEA;;;EAGA,IAAIE,eAAeA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAH,QAAS;EAAE;EACvD,IAAIG,eAAeA,CAACC,KAAa;IAAI,IAAI,CAAC,CAAAJ,QAAS,GAAGI,KAAK;EAAE;EAE7D,MAAM,CAAAC,IAAKC,CAAA;IACP,IAAI;MACA,MAAML,WAAW,GAAG,MAAM,IAAI,CAAC,CAAAR,QAAS,CAACc,cAAc,EAAE;MAEzD;MACA,IAAI,IAAI,CAAC,CAAAN,WAAY,KAAK,CAAC,CAAC,EAAE;QAC1B,IAAI,CAAC,CAAAA,WAAY,GAAGA,WAAW;QAC/B;;MAGJ;MAEA,IAAIA,WAAW,KAAK,IAAI,CAAC,CAAAA,WAAY,EAAE;QACnC,KAAK,IAAIO,CAAC,GAAG,IAAI,CAAC,CAAAP,WAAY,GAAG,CAAC,EAAEO,CAAC,IAAIP,WAAW,EAAEO,CAAC,EAAE,EAAE;UACvD;UACA,IAAI,IAAI,CAAC,CAAAT,MAAO,IAAI,IAAI,EAAE;YAAE;;UAE5B,MAAM,IAAI,CAAC,CAAAN,QAAS,CAACgB,IAAI,CAAC,OAAO,EAAED,CAAC,CAAC;;QAGzC,IAAI,CAAC,CAAAP,WAAY,GAAGA,WAAW;;KAGtC,CAAC,OAAOS,KAAK,EAAE;MACZ;MACA;MACA;IAAA;IAGJ;IACA,IAAI,IAAI,CAAC,CAAAX,MAAO,IAAI,IAAI,EAAE;MAAE;;IAE5B,IAAI,CAAC,CAAAA,MAAO,GAAG,IAAI,CAAC,CAAAN,QAAS,CAACkB,WAAW,CAAC,IAAI,CAAC,CAAAN,IAAK,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAAZ,QAAS,CAAC;EACpF;EAEAa,KAAKA,CAAA;IACD,IAAI,IAAI,CAAC,CAAAd,MAAO,EAAE;MAAE;;IACpB,IAAI,CAAC,CAAAA,MAAO,GAAG,IAAI,CAAC,CAAAN,QAAS,CAACkB,WAAW,CAAC,IAAI,CAAC,CAAAN,IAAK,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAAZ,QAAS,CAAC;IAChF,IAAI,CAAC,CAAAK,IAAK,EAAE;EAChB;EAEAS,IAAIA,CAAA;IACA,IAAI,CAAC,IAAI,CAAC,CAAAf,MAAO,EAAE;MAAE;;IACrB,IAAI,CAAC,CAAAN,QAAS,CAACsB,aAAa,CAAC,IAAI,CAAC,CAAAhB,MAAO,CAAC;IAC1C,IAAI,CAAC,CAAAA,MAAO,GAAG,IAAI;EACvB;EAEAiB,KAAKA,CAACC,eAAyB;IAC3B,IAAI,CAACH,IAAI,EAAE;IACX,IAAIG,eAAe,EAAE;MAAE,IAAI,CAAC,CAAAhB,WAAY,GAAG,CAAC,CAAC;;EACjD;EAEAiB,MAAMA,CAAA;IACF,IAAI,CAACL,KAAK,EAAE;EAChB;;AAIJ;;;;;;AAMA,OAAM,MAAOM,iBAAiB;EAC1B,CAAA1B,QAAS;EACT,CAAAY,IAAK;EACL,CAAAe,OAAQ;EAER;;;EAGAlB,YAAYT,QAA0B;IAClC,IAAI,CAAC,CAAAA,QAAS,GAAGA,QAAQ;IACzB,IAAI,CAAC,CAAA2B,OAAQ,GAAG,KAAK;IACrB,IAAI,CAAC,CAAAf,IAAK,GAAIJ,WAAmB,IAAI;MACjC,IAAI,CAACoB,KAAK,CAACpB,WAAW,EAAE,IAAI,CAAC,CAAAR,QAAS,CAAC;IAC3C,CAAC;EACL;EAEA;;;EAGA,MAAM4B,KAAKA,CAACpB,WAAmB,EAAER,QAA0B;IACvD,MAAM,IAAI6B,KAAK,CAAC,gCAAgC,CAAC;EACrD;EAEAT,KAAKA,CAAA;IACD,IAAI,IAAI,CAAC,CAAAO,OAAQ,EAAE;MAAE;;IACrB,IAAI,CAAC,CAAAA,OAAQ,GAAG,IAAI;IAEpB,IAAI,CAAC,CAAAf,IAAK,CAAC,CAAC,CAAC,CAAC;IACd,IAAI,CAAC,CAAAZ,QAAS,CAAC8B,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAAlB,IAAK,CAAC;EAC1C;EAEAS,IAAIA,CAAA;IACA,IAAI,CAAC,IAAI,CAAC,CAAAM,OAAQ,EAAE;MAAE;;IACtB,IAAI,CAAC,CAAAA,OAAQ,GAAG,KAAK;IAErB,IAAI,CAAC,CAAA3B,QAAS,CAAC+B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAAnB,IAAK,CAAC;EAC3C;EAEAW,KAAKA,CAACC,eAAyB;IAAU,IAAI,CAACH,IAAI,EAAE;EAAE;EACtDI,MAAMA,CAAA;IAAW,IAAI,CAACL,KAAK,EAAE;EAAE;;AAGnC,OAAM,MAAOY,yBAA0B,SAAQN,iBAAiB;EACnD,CAAAO,GAAI;EACb,CAAAC,SAAU;EAEVzB,YAAYT,QAA0B,EAAEiC,GAAW;IAC/C,KAAK,CAACjC,QAAQ,CAAC;IACf,IAAI,CAAC,CAAAiC,GAAI,GAAGA,GAAG;IACf,IAAI,CAAC,CAAAC,SAAU,GAAG,CAAC,CAAC;EACxB;EAEAX,KAAKA,CAACC,eAAyB;IAC3B,IAAIA,eAAe,EAAE;MAAE,IAAI,CAAC,CAAAU,SAAU,GAAG,CAAC,CAAC;;IAC3C,KAAK,CAACX,KAAK,CAACC,eAAe,CAAC;EAChC;EAEA,MAAMI,KAAKA,CAACpB,WAAmB,EAAER,QAA0B;IACvD,MAAMmC,KAAK,GAAG,MAAMnC,QAAQ,CAACoC,QAAQ,CAAC,IAAI,CAAC,CAAAH,GAAI,CAAC;IAChD,IAAIE,KAAK,IAAI,IAAI,EAAE;MAAE;;IAErB,IAAI,IAAI,CAAC,CAAAD,SAAU,KAAK,CAAC,CAAC,EAAE;MACxB,IAAI,CAAC,CAAAA,SAAU,GAAGC,KAAK,CAACE,MAAM;KACjC,MAAM,IAAIF,KAAK,CAACE,MAAM,GAAG,IAAI,CAAC,CAAAH,SAAU,EAAE;MACvClC,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAAiB,GAAI,EAAEE,KAAK,CAACE,MAAM,CAAC;MACtC,IAAI,CAAC,CAAAH,SAAU,GAAGC,KAAK,CAACE,MAAM;;EAEtC;;AAIJ;;;;;AAKA,OAAM,MAAOC,uBAAwB,SAAQZ,iBAAiB;EAC1D,CAAAa,MAAO;EAEP9B,YAAYT,QAA0B,EAAEuC,MAAoB;IACxD,KAAK,CAACvC,QAAQ,CAAC;IACf,IAAI,CAAC,CAAAuC,MAAO,GAAG7C,IAAI,CAAC6C,MAAM,CAAC;EAC/B;EAEA,MAAMX,KAAKA,CAACpB,WAAmB,EAAER,QAA0B;IACvD,MAAM,IAAI6B,KAAK,CAAC,OAAO,CAAC;IACxBW,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC,CAAAF,MAAO,CAAC;EAC7B;;AAGJ;;;;;;AAMA,OAAM,MAAOpC,4BAA6B,SAAQuB,iBAAiB;EAC/D,CAAAgB,IAAK;EAEL;;;;EAIAjC,YAAYT,QAA0B,EAAE0C,IAAY;IAChD,KAAK,CAAC1C,QAAQ,CAAC;IACf,IAAI,CAAC,CAAA0C,IAAK,GAAGA,IAAI;EACrB;EAEA,MAAMd,KAAKA,CAACpB,WAAmB,EAAER,QAA0B;IACvD,MAAM2C,EAAE,GAAG,MAAM3C,QAAQ,CAAC4C,qBAAqB,CAAC,IAAI,CAAC,CAAAF,IAAK,CAAC;IAC3D,IAAIC,EAAE,EAAE;MAAE3C,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAA0B,IAAK,EAAEC,EAAE,CAAC;;EAC3C;;AAGJ;;;;;AAKA,OAAM,MAAOE,sBAAsB;EAC/B,CAAA7C,QAAS;EACT,CAAAuC,MAAO;EACP,CAAAjC,MAAO;EAEP,CAAAqB,OAAQ;EAER;EACA;EACA,CAAAnB,WAAY;EAEZ;;;;EAIAC,YAAYT,QAA0B,EAAEuC,MAAmB;IACvD,IAAI,CAAC,CAAAvC,QAAS,GAAGA,QAAQ;IACzB,IAAI,CAAC,CAAAuC,MAAO,GAAG7C,IAAI,CAAC6C,MAAM,CAAC;IAC3B,IAAI,CAAC,CAAAjC,MAAO,GAAG,IAAI,CAAC,CAAAM,IAAK,CAACO,IAAI,CAAC,IAAI,CAAC;IACpC,IAAI,CAAC,CAAAQ,OAAQ,GAAG,KAAK;IACrB,IAAI,CAAC,CAAAnB,WAAY,GAAG,CAAC,CAAC;EAC1B;EAEA,MAAM,CAAAI,IAAKC,CAACL,WAAmB;IAC3B;IACA,IAAI,IAAI,CAAC,CAAAA,WAAY,KAAK,CAAC,CAAC,EAAE;MAAE;;IAEhC,MAAM+B,MAAM,GAAG7C,IAAI,CAAC,IAAI,CAAC,CAAA6C,MAAO,CAAC;IACjCA,MAAM,CAACO,SAAS,GAAG,IAAI,CAAC,CAAAtC,WAAY,GAAG,CAAC;IACxC+B,MAAM,CAACQ,OAAO,GAAGvC,WAAW;IAE5B,MAAMwC,IAAI,GAAG,MAAM,IAAI,CAAC,CAAAhD,QAAS,CAACiD,OAAO,CAACV,MAAM,CAAC;IAEjD;IACA;IACA,IAAIS,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;MACnB,IAAI,IAAI,CAAC,CAAA1C,WAAY,GAAGA,WAAW,GAAG,EAAE,EAAE;QACtC,IAAI,CAAC,CAAAA,WAAY,GAAGA,WAAW,GAAG,EAAE;;MAExC;;IAGJ,KAAK,MAAMiC,GAAG,IAAIO,IAAI,EAAE;MACpB,IAAI,CAAC,CAAAhD,QAAS,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAAuB,MAAO,EAAEE,GAAG,CAAC;MAEtC;MACA;MACA;MACA,IAAI,CAAC,CAAAjC,WAAY,GAAGiC,GAAG,CAACjC,WAAW;;EAE3C;EAEAY,KAAKA,CAAA;IACD,IAAI,IAAI,CAAC,CAAAO,OAAQ,EAAE;MAAE;;IACrB,IAAI,CAAC,CAAAA,OAAQ,GAAG,IAAI;IAEpB,IAAI,IAAI,CAAC,CAAAnB,WAAY,KAAK,CAAC,CAAC,EAAE;MAC1B,IAAI,CAAC,CAAAR,QAAS,CAACc,cAAc,EAAE,CAACqC,IAAI,CAAE3C,WAAW,IAAI;QACjD,IAAI,CAAC,CAAAA,WAAY,GAAGA,WAAW;MACnC,CAAC,CAAC;;IAEN,IAAI,CAAC,CAAAR,QAAS,CAAC8B,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAAxB,MAAO,CAAC;EAC5C;EAEAe,IAAIA,CAAA;IACA,IAAI,CAAC,IAAI,CAAC,CAAAM,OAAQ,EAAE;MAAE;;IACtB,IAAI,CAAC,CAAAA,OAAQ,GAAG,KAAK;IAErB,IAAI,CAAC,CAAA3B,QAAS,CAAC+B,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAAzB,MAAO,CAAC;EAC7C;EAEAiB,KAAKA,CAACC,eAAyB;IAC3B,IAAI,CAACH,IAAI,EAAE;IACX,IAAIG,eAAe,EAAE;MAAE,IAAI,CAAC,CAAAhB,WAAY,GAAG,CAAC,CAAC;;EACjD;EAEAiB,MAAMA,CAAA;IACF,IAAI,CAACL,KAAK,EAAE;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}