{"ast": null, "code": "/**\n *  @_ignore\n */\nimport { getBytesCopy, assertArgument, toUtf8Bytes } from \"../utils/index.js\";\nexport function looseArrayify(hexString) {\n  if (typeof hexString === \"string\" && !hexString.startsWith(\"0x\")) {\n    hexString = \"0x\" + hexString;\n  }\n  return getBytesCopy(hexString);\n}\nexport function zpad(value, length) {\n  value = String(value);\n  while (value.length < length) {\n    value = '0' + value;\n  }\n  return value;\n}\nexport function getPassword(password) {\n  if (typeof password === 'string') {\n    return toUtf8Bytes(password, \"NFKC\");\n  }\n  return getBytesCopy(password);\n}\nexport function spelunk(object, _path) {\n  const match = _path.match(/^([a-z0-9$_.-]*)(:([a-z]+))?(!)?$/i);\n  assertArgument(match != null, \"invalid path\", \"path\", _path);\n  const path = match[1];\n  const type = match[3];\n  const reqd = match[4] === \"!\";\n  let cur = object;\n  for (const comp of path.toLowerCase().split('.')) {\n    // Search for a child object with a case-insensitive matching key\n    if (Array.isArray(cur)) {\n      if (!comp.match(/^[0-9]+$/)) {\n        break;\n      }\n      cur = cur[parseInt(comp)];\n    } else if (typeof cur === \"object\") {\n      let found = null;\n      for (const key in cur) {\n        if (key.toLowerCase() === comp) {\n          found = cur[key];\n          break;\n        }\n      }\n      cur = found;\n    } else {\n      cur = null;\n    }\n    if (cur == null) {\n      break;\n    }\n  }\n  assertArgument(!reqd || cur != null, \"missing required value\", \"path\", path);\n  if (type && cur != null) {\n    if (type === \"int\") {\n      if (typeof cur === \"string\" && cur.match(/^-?[0-9]+$/)) {\n        return parseInt(cur);\n      } else if (Number.isSafeInteger(cur)) {\n        return cur;\n      }\n    }\n    if (type === \"number\") {\n      if (typeof cur === \"string\" && cur.match(/^-?[0-9.]*$/)) {\n        return parseFloat(cur);\n      }\n    }\n    if (type === \"data\") {\n      if (typeof cur === \"string\") {\n        return looseArrayify(cur);\n      }\n    }\n    if (type === \"array\" && Array.isArray(cur)) {\n      return cur;\n    }\n    if (type === typeof cur) {\n      return cur;\n    }\n    assertArgument(false, `wrong type found for ${type} `, \"path\", path);\n  }\n  return cur;\n}\n/*\nexport function follow(object: any, path: string): null | string {\n    let currentChild = object;\n\n    for (const comp of path.toLowerCase().split('/')) {\n\n        // Search for a child object with a case-insensitive matching key\n        let matchingChild = null;\n        for (const key in currentChild) {\n             if (key.toLowerCase() === comp) {\n                 matchingChild = currentChild[key];\n                 break;\n             }\n        }\n\n        if (matchingChild === null) { return null; }\n\n        currentChild = matchingChild;\n    }\n\n    return currentChild;\n}\n\n// \"path/to/something:type!\"\nexport function followRequired(data: any, path: string): string {\n    const value = follow(data, path);\n    if (value != null) { return value; }\n    return logger.throwArgumentError(\"invalid value\", `data:${ path }`,\n    JSON.stringify(data));\n}\n*/\n// See: https://www.ietf.org/rfc/rfc4122.txt (Section 4.4)\n/*\nexport function uuidV4(randomBytes: BytesLike): string {\n    const bytes = getBytes(randomBytes, \"randomBytes\");\n\n    // Section: 4.1.3:\n    // - time_hi_and_version[12:16] = 0b0100\n    bytes[6] = (bytes[6] & 0x0f) | 0x40;\n\n    // Section 4.4\n    // - clock_seq_hi_and_reserved[6] = 0b0\n    // - clock_seq_hi_and_reserved[7] = 0b1\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n\n    const value = hexlify(bytes);\n\n    return [\n       value.substring(2, 10),\n       value.substring(10, 14),\n       value.substring(14, 18),\n       value.substring(18, 22),\n       value.substring(22, 34),\n    ].join(\"-\");\n}\n*/", "map": {"version": 3, "names": ["getBytesCopy", "assertArgument", "toUtf8Bytes", "looseArrayify", "hexString", "startsWith", "zpad", "value", "length", "String", "getPassword", "password", "spelunk", "object", "_path", "match", "path", "type", "reqd", "cur", "comp", "toLowerCase", "split", "Array", "isArray", "parseInt", "found", "key", "Number", "isSafeInteger", "parseFloat"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\utils.ts"], "sourcesContent": ["/**\n *  @_ignore\n */\n\nimport {\n    getBytesCopy, assertArgument, toUtf8Bytes\n} from \"../utils/index.js\";\n\nexport function looseArrayify(hexString: string): Uint8Array {\n    if (typeof(hexString) === \"string\" && !hexString.startsWith(\"0x\")) {\n        hexString = \"0x\" + hexString;\n    }\n    return getBytesCopy(hexString);\n}\n\nexport function zpad(value: String | number, length: number): String {\n    value = String(value);\n    while (value.length < length) { value = '0' + value; }\n    return value;\n}\n\nexport function getPassword(password: string | Uint8Array): Uint8Array {\n    if (typeof(password) === 'string') {\n        return toUtf8Bytes(password, \"NFKC\");\n    }\n    return getBytesCopy(password);\n}\n\nexport function spelunk<T>(object: any, _path: string): T {\n\n    const match = _path.match(/^([a-z0-9$_.-]*)(:([a-z]+))?(!)?$/i);\n    assertArgument(match != null, \"invalid path\", \"path\", _path);\n\n    const path = match[1];\n    const type = match[3];\n    const reqd = (match[4] === \"!\");\n\n    let cur = object;\n    for (const comp of path.toLowerCase().split('.')) {\n\n        // Search for a child object with a case-insensitive matching key\n        if (Array.isArray(cur)) {\n            if (!comp.match(/^[0-9]+$/)) { break; }\n            cur = cur[parseInt(comp)];\n\n        } else if (typeof(cur) === \"object\") {\n            let found: any = null;\n            for (const key in cur) {\n                 if (key.toLowerCase() === comp) {\n                     found = cur[key];\n                     break;\n                 }\n            }\n            cur = found;\n\n        } else {\n            cur = null;\n        }\n\n        if (cur == null) { break; }\n    }\n\n    assertArgument(!reqd || cur != null, \"missing required value\", \"path\", path);\n\n    if (type && cur != null) {\n        if (type === \"int\") {\n            if (typeof(cur) === \"string\" && cur.match(/^-?[0-9]+$/)) {\n                return <T><unknown>parseInt(cur);\n            } else if (Number.isSafeInteger(cur)) {\n                return cur;\n            }\n        }\n\n        if (type === \"number\") {\n            if (typeof(cur) === \"string\" && cur.match(/^-?[0-9.]*$/)) {\n                return <T><unknown>parseFloat(cur);\n            }\n        }\n\n        if (type === \"data\") {\n            if (typeof(cur) === \"string\") { return <T><unknown>looseArrayify(cur); }\n        }\n\n        if (type === \"array\" && Array.isArray(cur)) { return <T><unknown>cur; }\n        if (type === typeof(cur)) { return cur; }\n\n        assertArgument(false, `wrong type found for ${ type } `, \"path\", path);\n    }\n\n    return cur;\n}\n/*\nexport function follow(object: any, path: string): null | string {\n    let currentChild = object;\n\n    for (const comp of path.toLowerCase().split('/')) {\n\n        // Search for a child object with a case-insensitive matching key\n        let matchingChild = null;\n        for (const key in currentChild) {\n             if (key.toLowerCase() === comp) {\n                 matchingChild = currentChild[key];\n                 break;\n             }\n        }\n\n        if (matchingChild === null) { return null; }\n\n        currentChild = matchingChild;\n    }\n\n    return currentChild;\n}\n\n// \"path/to/something:type!\"\nexport function followRequired(data: any, path: string): string {\n    const value = follow(data, path);\n    if (value != null) { return value; }\n    return logger.throwArgumentError(\"invalid value\", `data:${ path }`,\n    JSON.stringify(data));\n}\n*/\n// See: https://www.ietf.org/rfc/rfc4122.txt (Section 4.4)\n/*\nexport function uuidV4(randomBytes: BytesLike): string {\n    const bytes = getBytes(randomBytes, \"randomBytes\");\n\n    // Section: 4.1.3:\n    // - time_hi_and_version[12:16] = 0b0100\n    bytes[6] = (bytes[6] & 0x0f) | 0x40;\n\n    // Section 4.4\n    // - clock_seq_hi_and_reserved[6] = 0b0\n    // - clock_seq_hi_and_reserved[7] = 0b1\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n\n    const value = hexlify(bytes);\n\n    return [\n       value.substring(2, 10),\n       value.substring(10, 14),\n       value.substring(14, 18),\n       value.substring(18, 22),\n       value.substring(22, 34),\n    ].join(\"-\");\n}\n*/\n"], "mappings": "AAAA;;;AAIA,SACIA,YAAY,EAAEC,cAAc,EAAEC,WAAW,QACtC,mBAAmB;AAE1B,OAAM,SAAUC,aAAaA,CAACC,SAAiB;EAC3C,IAAI,OAAOA,SAAU,KAAK,QAAQ,IAAI,CAACA,SAAS,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;IAC/DD,SAAS,GAAG,IAAI,GAAGA,SAAS;;EAEhC,OAAOJ,YAAY,CAACI,SAAS,CAAC;AAClC;AAEA,OAAM,SAAUE,IAAIA,CAACC,KAAsB,EAAEC,MAAc;EACvDD,KAAK,GAAGE,MAAM,CAACF,KAAK,CAAC;EACrB,OAAOA,KAAK,CAACC,MAAM,GAAGA,MAAM,EAAE;IAAED,KAAK,GAAG,GAAG,GAAGA,KAAK;;EACnD,OAAOA,KAAK;AAChB;AAEA,OAAM,SAAUG,WAAWA,CAACC,QAA6B;EACrD,IAAI,OAAOA,QAAS,KAAK,QAAQ,EAAE;IAC/B,OAAOT,WAAW,CAACS,QAAQ,EAAE,MAAM,CAAC;;EAExC,OAAOX,YAAY,CAACW,QAAQ,CAAC;AACjC;AAEA,OAAM,SAAUC,OAAOA,CAAIC,MAAW,EAAEC,KAAa;EAEjD,MAAMC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC,oCAAoC,CAAC;EAC/Dd,cAAc,CAACc,KAAK,IAAI,IAAI,EAAE,cAAc,EAAE,MAAM,EAAED,KAAK,CAAC;EAE5D,MAAME,IAAI,GAAGD,KAAK,CAAC,CAAC,CAAC;EACrB,MAAME,IAAI,GAAGF,KAAK,CAAC,CAAC,CAAC;EACrB,MAAMG,IAAI,GAAIH,KAAK,CAAC,CAAC,CAAC,KAAK,GAAI;EAE/B,IAAII,GAAG,GAAGN,MAAM;EAChB,KAAK,MAAMO,IAAI,IAAIJ,IAAI,CAACK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,EAAE;IAE9C;IACA,IAAIC,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;MACpB,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC,UAAU,CAAC,EAAE;QAAE;;MAC/BI,GAAG,GAAGA,GAAG,CAACM,QAAQ,CAACL,IAAI,CAAC,CAAC;KAE5B,MAAM,IAAI,OAAOD,GAAI,KAAK,QAAQ,EAAE;MACjC,IAAIO,KAAK,GAAQ,IAAI;MACrB,KAAK,MAAMC,GAAG,IAAIR,GAAG,EAAE;QAClB,IAAIQ,GAAG,CAACN,WAAW,EAAE,KAAKD,IAAI,EAAE;UAC5BM,KAAK,GAAGP,GAAG,CAACQ,GAAG,CAAC;UAChB;;;MAGTR,GAAG,GAAGO,KAAK;KAEd,MAAM;MACHP,GAAG,GAAG,IAAI;;IAGd,IAAIA,GAAG,IAAI,IAAI,EAAE;MAAE;;;EAGvBlB,cAAc,CAAC,CAACiB,IAAI,IAAIC,GAAG,IAAI,IAAI,EAAE,wBAAwB,EAAE,MAAM,EAAEH,IAAI,CAAC;EAE5E,IAAIC,IAAI,IAAIE,GAAG,IAAI,IAAI,EAAE;IACrB,IAAIF,IAAI,KAAK,KAAK,EAAE;MAChB,IAAI,OAAOE,GAAI,KAAK,QAAQ,IAAIA,GAAG,CAACJ,KAAK,CAAC,YAAY,CAAC,EAAE;QACrD,OAAmBU,QAAQ,CAACN,GAAG,CAAC;OACnC,MAAM,IAAIS,MAAM,CAACC,aAAa,CAACV,GAAG,CAAC,EAAE;QAClC,OAAOA,GAAG;;;IAIlB,IAAIF,IAAI,KAAK,QAAQ,EAAE;MACnB,IAAI,OAAOE,GAAI,KAAK,QAAQ,IAAIA,GAAG,CAACJ,KAAK,CAAC,aAAa,CAAC,EAAE;QACtD,OAAmBe,UAAU,CAACX,GAAG,CAAC;;;IAI1C,IAAIF,IAAI,KAAK,MAAM,EAAE;MACjB,IAAI,OAAOE,GAAI,KAAK,QAAQ,EAAE;QAAE,OAAmBhB,aAAa,CAACgB,GAAG,CAAC;;;IAGzE,IAAIF,IAAI,KAAK,OAAO,IAAIM,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;MAAE,OAAmBA,GAAG;;IACpE,IAAIF,IAAI,KAAK,OAAOE,GAAI,EAAE;MAAE,OAAOA,GAAG;;IAEtClB,cAAc,CAAC,KAAK,EAAE,wBAAyBgB,IAAK,GAAG,EAAE,MAAM,EAAED,IAAI,CAAC;;EAG1E,OAAOG,GAAG;AACd;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}