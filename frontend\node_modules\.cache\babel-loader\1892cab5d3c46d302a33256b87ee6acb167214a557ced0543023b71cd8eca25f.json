{"ast": null, "code": "/* Do NOT modify this file; see /src.ts/_admin/update-version.ts */\n/**\n *  The current version of Ethers.\n */\nexport const version = \"6.14.3\";", "map": {"version": 3, "names": ["version"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\_version.ts"], "sourcesContent": ["/* Do NOT modify this file; see /src.ts/_admin/update-version.ts */\n\n/**\n *  The current version of Ethers.\n */\nexport const version: string = \"6.14.3\";\n"], "mappings": "AAAA;AAEA;;;AAGA,OAAO,MAAMA,OAAO,GAAW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}