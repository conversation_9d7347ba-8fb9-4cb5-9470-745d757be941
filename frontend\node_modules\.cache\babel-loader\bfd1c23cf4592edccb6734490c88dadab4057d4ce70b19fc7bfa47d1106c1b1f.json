{"ast": null, "code": "/**\n *  Add details about signing here.\n *\n *  @_subsection: api/crypto:Signing  [about-signing]\n */\nimport { secp256k1 } from \"@noble/curves/secp256k1\";\nimport { concat, dataLength, getBytes, getBytesCopy, hexlify, toBeHex, assertArgument } from \"../utils/index.js\";\nimport { Signature } from \"./signature.js\";\n/**\n *  A **SigningKey** provides high-level access to the elliptic curve\n *  cryptography (ECC) operations and key management.\n */\nexport class SigningKey {\n  #privateKey;\n  /**\n   *  Creates a new **SigningKey** for %%privateKey%%.\n   */\n  constructor(privateKey) {\n    assertArgument(dataLength(privateKey) === 32, \"invalid private key\", \"privateKey\", \"[REDACTED]\");\n    this.#privateKey = hexlify(privateKey);\n  }\n  /**\n   *  The private key.\n   */\n  get privateKey() {\n    return this.#privateKey;\n  }\n  /**\n   *  The uncompressed public key.\n   *\n   * This will always begin with the prefix ``0x04`` and be 132\n   * characters long (the ``0x`` prefix and 130 hexadecimal nibbles).\n   */\n  get publicKey() {\n    return SigningKey.computePublicKey(this.#privateKey);\n  }\n  /**\n   *  The compressed public key.\n   *\n   *  This will always begin with either the prefix ``0x02`` or ``0x03``\n   *  and be 68 characters long (the ``0x`` prefix and 33 hexadecimal\n   *  nibbles)\n   */\n  get compressedPublicKey() {\n    return SigningKey.computePublicKey(this.#privateKey, true);\n  }\n  /**\n   *  Return the signature of the signed %%digest%%.\n   */\n  sign(digest) {\n    assertArgument(dataLength(digest) === 32, \"invalid digest length\", \"digest\", digest);\n    const sig = secp256k1.sign(getBytesCopy(digest), getBytesCopy(this.#privateKey), {\n      lowS: true\n    });\n    return Signature.from({\n      r: toBeHex(sig.r, 32),\n      s: toBeHex(sig.s, 32),\n      v: sig.recovery ? 0x1c : 0x1b\n    });\n  }\n  /**\n   *  Returns the [[link-wiki-ecdh]] shared secret between this\n   *  private key and the %%other%% key.\n   *\n   *  The %%other%% key may be any type of key, a raw public key,\n   *  a compressed/uncompressed pubic key or aprivate key.\n   *\n   *  Best practice is usually to use a cryptographic hash on the\n   *  returned value before using it as a symetric secret.\n   *\n   *  @example:\n   *    sign1 = new SigningKey(id(\"some-secret-1\"))\n   *    sign2 = new SigningKey(id(\"some-secret-2\"))\n   *\n   *    // Notice that privA.computeSharedSecret(pubB)...\n   *    sign1.computeSharedSecret(sign2.publicKey)\n   *    //_result:\n   *\n   *    // ...is equal to privB.computeSharedSecret(pubA).\n   *    sign2.computeSharedSecret(sign1.publicKey)\n   *    //_result:\n   */\n  computeSharedSecret(other) {\n    const pubKey = SigningKey.computePublicKey(other);\n    return hexlify(secp256k1.getSharedSecret(getBytesCopy(this.#privateKey), getBytes(pubKey), false));\n  }\n  /**\n   *  Compute the public key for %%key%%, optionally %%compressed%%.\n   *\n   *  The %%key%% may be any type of key, a raw public key, a\n   *  compressed/uncompressed public key or private key.\n   *\n   *  @example:\n   *    sign = new SigningKey(id(\"some-secret\"));\n   *\n   *    // Compute the uncompressed public key for a private key\n   *    SigningKey.computePublicKey(sign.privateKey)\n   *    //_result:\n   *\n   *    // Compute the compressed public key for a private key\n   *    SigningKey.computePublicKey(sign.privateKey, true)\n   *    //_result:\n   *\n   *    // Compute the uncompressed public key\n   *    SigningKey.computePublicKey(sign.publicKey, false);\n   *    //_result:\n   *\n   *    // Compute the Compressed a public key\n   *    SigningKey.computePublicKey(sign.publicKey, true);\n   *    //_result:\n   */\n  static computePublicKey(key, compressed) {\n    let bytes = getBytes(key, \"key\");\n    // private key\n    if (bytes.length === 32) {\n      const pubKey = secp256k1.getPublicKey(bytes, !!compressed);\n      return hexlify(pubKey);\n    }\n    // raw public key; use uncompressed key with 0x04 prefix\n    if (bytes.length === 64) {\n      const pub = new Uint8Array(65);\n      pub[0] = 0x04;\n      pub.set(bytes, 1);\n      bytes = pub;\n    }\n    const point = secp256k1.ProjectivePoint.fromHex(bytes);\n    return hexlify(point.toRawBytes(compressed));\n  }\n  /**\n   *  Returns the public key for the private key which produced the\n   *  %%signature%% for the given %%digest%%.\n   *\n   *  @example:\n   *    key = new SigningKey(id(\"some-secret\"))\n   *    digest = id(\"hello world\")\n   *    sig = key.sign(digest)\n   *\n   *    // Notice the signer public key...\n   *    key.publicKey\n   *    //_result:\n   *\n   *    // ...is equal to the recovered public key\n   *    SigningKey.recoverPublicKey(digest, sig)\n   *    //_result:\n   *\n   */\n  static recoverPublicKey(digest, signature) {\n    assertArgument(dataLength(digest) === 32, \"invalid digest length\", \"digest\", digest);\n    const sig = Signature.from(signature);\n    let secpSig = secp256k1.Signature.fromCompact(getBytesCopy(concat([sig.r, sig.s])));\n    secpSig = secpSig.addRecoveryBit(sig.yParity);\n    const pubKey = secpSig.recoverPublicKey(getBytesCopy(digest));\n    assertArgument(pubKey != null, \"invalid signature for digest\", \"signature\", signature);\n    return \"0x\" + pubKey.toHex(false);\n  }\n  /**\n   *  Returns the point resulting from adding the ellipic curve points\n   *  %%p0%% and %%p1%%.\n   *\n   *  This is not a common function most developers should require, but\n   *  can be useful for certain privacy-specific techniques.\n   *\n   *  For example, it is used by [[HDNodeWallet]] to compute child\n   *  addresses from parent public keys and chain codes.\n   */\n  static addPoints(p0, p1, compressed) {\n    const pub0 = secp256k1.ProjectivePoint.fromHex(SigningKey.computePublicKey(p0).substring(2));\n    const pub1 = secp256k1.ProjectivePoint.fromHex(SigningKey.computePublicKey(p1).substring(2));\n    return \"0x\" + pub0.add(pub1).toHex(!!compressed);\n  }\n}", "map": {"version": 3, "names": ["secp256k1", "concat", "dataLength", "getBytes", "getBytesCopy", "hexlify", "toBeHex", "assertArgument", "Signature", "SigningKey", "privateKey", "constructor", "public<PERSON>ey", "computePublicKey", "compressedPublicKey", "sign", "digest", "sig", "lowS", "from", "r", "s", "v", "recovery", "computeSharedSecret", "other", "pubKey", "getSharedSecret", "key", "compressed", "bytes", "length", "getPublicKey", "pub", "Uint8Array", "set", "point", "ProjectivePoint", "fromHex", "toRawBytes", "recoverPublicKey", "signature", "secpSig", "fromCompact", "addRecoveryBit", "yParity", "toHex", "addPoints", "p0", "p1", "pub0", "substring", "pub1", "add"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\signing-key.ts"], "sourcesContent": ["/**\n *  Add details about signing here.\n *\n *  @_subsection: api/crypto:Signing  [about-signing]\n */\n\nimport { secp256k1 } from \"@noble/curves/secp256k1\";\n\nimport {\n    concat, dataLength, getBytes, getBytesCopy, hexlify, toBeHex,\n    assertArgument\n} from \"../utils/index.js\";\n\nimport { Signature } from \"./signature.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\nimport type { SignatureLike } from \"./index.js\";\n\n\n/**\n *  A **SigningKey** provides high-level access to the elliptic curve\n *  cryptography (ECC) operations and key management.\n */\nexport class SigningKey {\n    #privateKey: string;\n\n    /**\n     *  Creates a new **SigningKey** for %%privateKey%%.\n     */\n    constructor(privateKey: BytesLike) {\n        assertArgument(dataLength(privateKey) === 32, \"invalid private key\", \"privateKey\", \"[REDACTED]\");\n        this.#privateKey = hexlify(privateKey);\n    }\n\n    /**\n     *  The private key.\n     */\n    get privateKey(): string { return this.#privateKey; }\n\n    /**\n     *  The uncompressed public key.\n     *\n     * This will always begin with the prefix ``0x04`` and be 132\n     * characters long (the ``0x`` prefix and 130 hexadecimal nibbles).\n     */\n    get publicKey(): string { return SigningKey.computePublicKey(this.#privateKey); }\n\n    /**\n     *  The compressed public key.\n     *\n     *  This will always begin with either the prefix ``0x02`` or ``0x03``\n     *  and be 68 characters long (the ``0x`` prefix and 33 hexadecimal\n     *  nibbles)\n     */\n    get compressedPublicKey(): string { return SigningKey.computePublicKey(this.#privateKey, true); }\n\n    /**\n     *  Return the signature of the signed %%digest%%.\n     */\n    sign(digest: BytesLike): Signature {\n        assertArgument(dataLength(digest) === 32, \"invalid digest length\", \"digest\", digest);\n\n        const sig = secp256k1.sign(getBytesCopy(digest), getBytesCopy(this.#privateKey), {\n            lowS: true\n        });\n\n        return Signature.from({\n            r: toBeHex(sig.r, 32),\n            s: toBeHex(sig.s, 32),\n            v: (sig.recovery ? 0x1c: 0x1b)\n        });\n    }\n\n    /**\n     *  Returns the [[link-wiki-ecdh]] shared secret between this\n     *  private key and the %%other%% key.\n     *\n     *  The %%other%% key may be any type of key, a raw public key,\n     *  a compressed/uncompressed pubic key or aprivate key.\n     *\n     *  Best practice is usually to use a cryptographic hash on the\n     *  returned value before using it as a symetric secret.\n     *\n     *  @example:\n     *    sign1 = new SigningKey(id(\"some-secret-1\"))\n     *    sign2 = new SigningKey(id(\"some-secret-2\"))\n     *\n     *    // Notice that privA.computeSharedSecret(pubB)...\n     *    sign1.computeSharedSecret(sign2.publicKey)\n     *    //_result:\n     *\n     *    // ...is equal to privB.computeSharedSecret(pubA).\n     *    sign2.computeSharedSecret(sign1.publicKey)\n     *    //_result:\n     */\n    computeSharedSecret(other: BytesLike): string {\n        const pubKey = SigningKey.computePublicKey(other);\n        return hexlify(secp256k1.getSharedSecret(getBytesCopy(this.#privateKey), getBytes(pubKey), false));\n    }\n\n    /**\n     *  Compute the public key for %%key%%, optionally %%compressed%%.\n     *\n     *  The %%key%% may be any type of key, a raw public key, a\n     *  compressed/uncompressed public key or private key.\n     *\n     *  @example:\n     *    sign = new SigningKey(id(\"some-secret\"));\n     *\n     *    // Compute the uncompressed public key for a private key\n     *    SigningKey.computePublicKey(sign.privateKey)\n     *    //_result:\n     *\n     *    // Compute the compressed public key for a private key\n     *    SigningKey.computePublicKey(sign.privateKey, true)\n     *    //_result:\n     *\n     *    // Compute the uncompressed public key\n     *    SigningKey.computePublicKey(sign.publicKey, false);\n     *    //_result:\n     *\n     *    // Compute the Compressed a public key\n     *    SigningKey.computePublicKey(sign.publicKey, true);\n     *    //_result:\n     */\n    static computePublicKey(key: BytesLike, compressed?: boolean): string {\n        let bytes = getBytes(key, \"key\");\n\n        // private key\n        if (bytes.length === 32) {\n            const pubKey = secp256k1.getPublicKey(bytes, !!compressed);\n            return hexlify(pubKey);\n        }\n\n        // raw public key; use uncompressed key with 0x04 prefix\n        if (bytes.length === 64) {\n            const pub = new Uint8Array(65);\n            pub[0] = 0x04;\n            pub.set(bytes, 1);\n            bytes = pub;\n        }\n\n        const point = secp256k1.ProjectivePoint.fromHex(bytes);\n        return hexlify(point.toRawBytes(compressed));\n    }\n\n    /**\n     *  Returns the public key for the private key which produced the\n     *  %%signature%% for the given %%digest%%.\n     *\n     *  @example:\n     *    key = new SigningKey(id(\"some-secret\"))\n     *    digest = id(\"hello world\")\n     *    sig = key.sign(digest)\n     *\n     *    // Notice the signer public key...\n     *    key.publicKey\n     *    //_result:\n     *\n     *    // ...is equal to the recovered public key\n     *    SigningKey.recoverPublicKey(digest, sig)\n     *    //_result:\n     *\n     */\n    static recoverPublicKey(digest: BytesLike, signature: SignatureLike): string {\n        assertArgument(dataLength(digest) === 32, \"invalid digest length\", \"digest\", digest);\n\n        const sig = Signature.from(signature);\n\n        let secpSig = secp256k1.Signature.fromCompact(getBytesCopy(concat([ sig.r, sig.s ])));\n        secpSig = secpSig.addRecoveryBit(sig.yParity);\n\n        const pubKey = secpSig.recoverPublicKey(getBytesCopy(digest));\n        assertArgument(pubKey != null, \"invalid signature for digest\", \"signature\", signature);\n\n        return \"0x\" + pubKey.toHex(false);\n    }\n\n    /**\n     *  Returns the point resulting from adding the ellipic curve points\n     *  %%p0%% and %%p1%%.\n     *\n     *  This is not a common function most developers should require, but\n     *  can be useful for certain privacy-specific techniques.\n     *\n     *  For example, it is used by [[HDNodeWallet]] to compute child\n     *  addresses from parent public keys and chain codes.\n     */\n    static addPoints(p0: BytesLike, p1: BytesLike, compressed?: boolean): string {\n        const pub0 = secp256k1.ProjectivePoint.fromHex(SigningKey.computePublicKey(p0).substring(2));\n        const pub1 = secp256k1.ProjectivePoint.fromHex(SigningKey.computePublicKey(p1).substring(2));\n        return \"0x\" + pub0.add(pub1).toHex(!!compressed)\n    }\n}\n\n"], "mappings": "AAAA;;;;;AAMA,SAASA,SAAS,QAAQ,yBAAyB;AAEnD,SACIC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAC5DC,cAAc,QACX,mBAAmB;AAE1B,SAASC,SAAS,QAAQ,gBAAgB;AAO1C;;;;AAIA,OAAM,MAAOC,UAAU;EACnB,CAAAC,UAAW;EAEX;;;EAGAC,YAAYD,UAAqB;IAC7BH,cAAc,CAACL,UAAU,CAACQ,UAAU,CAAC,KAAK,EAAE,EAAE,qBAAqB,EAAE,YAAY,EAAE,YAAY,CAAC;IAChG,IAAI,CAAC,CAAAA,UAAW,GAAGL,OAAO,CAACK,UAAU,CAAC;EAC1C;EAEA;;;EAGA,IAAIA,UAAUA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,UAAW;EAAE;EAEpD;;;;;;EAMA,IAAIE,SAASA,CAAA;IAAa,OAAOH,UAAU,CAACI,gBAAgB,CAAC,IAAI,CAAC,CAAAH,UAAW,CAAC;EAAE;EAEhF;;;;;;;EAOA,IAAII,mBAAmBA,CAAA;IAAa,OAAOL,UAAU,CAACI,gBAAgB,CAAC,IAAI,CAAC,CAAAH,UAAW,EAAE,IAAI,CAAC;EAAE;EAEhG;;;EAGAK,IAAIA,CAACC,MAAiB;IAClBT,cAAc,CAACL,UAAU,CAACc,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAEpF,MAAMC,GAAG,GAAGjB,SAAS,CAACe,IAAI,CAACX,YAAY,CAACY,MAAM,CAAC,EAAEZ,YAAY,CAAC,IAAI,CAAC,CAAAM,UAAW,CAAC,EAAE;MAC7EQ,IAAI,EAAE;KACT,CAAC;IAEF,OAAOV,SAAS,CAACW,IAAI,CAAC;MAClBC,CAAC,EAAEd,OAAO,CAACW,GAAG,CAACG,CAAC,EAAE,EAAE,CAAC;MACrBC,CAAC,EAAEf,OAAO,CAACW,GAAG,CAACI,CAAC,EAAE,EAAE,CAAC;MACrBC,CAAC,EAAGL,GAAG,CAACM,QAAQ,GAAG,IAAI,GAAE;KAC5B,CAAC;EACN;EAEA;;;;;;;;;;;;;;;;;;;;;;EAsBAC,mBAAmBA,CAACC,KAAgB;IAChC,MAAMC,MAAM,GAAGjB,UAAU,CAACI,gBAAgB,CAACY,KAAK,CAAC;IACjD,OAAOpB,OAAO,CAACL,SAAS,CAAC2B,eAAe,CAACvB,YAAY,CAAC,IAAI,CAAC,CAAAM,UAAW,CAAC,EAAEP,QAAQ,CAACuB,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;EACtG;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;EAyBA,OAAOb,gBAAgBA,CAACe,GAAc,EAAEC,UAAoB;IACxD,IAAIC,KAAK,GAAG3B,QAAQ,CAACyB,GAAG,EAAE,KAAK,CAAC;IAEhC;IACA,IAAIE,KAAK,CAACC,MAAM,KAAK,EAAE,EAAE;MACrB,MAAML,MAAM,GAAG1B,SAAS,CAACgC,YAAY,CAACF,KAAK,EAAE,CAAC,CAACD,UAAU,CAAC;MAC1D,OAAOxB,OAAO,CAACqB,MAAM,CAAC;;IAG1B;IACA,IAAII,KAAK,CAACC,MAAM,KAAK,EAAE,EAAE;MACrB,MAAME,GAAG,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;MAC9BD,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;MACbA,GAAG,CAACE,GAAG,CAACL,KAAK,EAAE,CAAC,CAAC;MACjBA,KAAK,GAAGG,GAAG;;IAGf,MAAMG,KAAK,GAAGpC,SAAS,CAACqC,eAAe,CAACC,OAAO,CAACR,KAAK,CAAC;IACtD,OAAOzB,OAAO,CAAC+B,KAAK,CAACG,UAAU,CAACV,UAAU,CAAC,CAAC;EAChD;EAEA;;;;;;;;;;;;;;;;;;EAkBA,OAAOW,gBAAgBA,CAACxB,MAAiB,EAAEyB,SAAwB;IAC/DlC,cAAc,CAACL,UAAU,CAACc,MAAM,CAAC,KAAK,EAAE,EAAE,uBAAuB,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAEpF,MAAMC,GAAG,GAAGT,SAAS,CAACW,IAAI,CAACsB,SAAS,CAAC;IAErC,IAAIC,OAAO,GAAG1C,SAAS,CAACQ,SAAS,CAACmC,WAAW,CAACvC,YAAY,CAACH,MAAM,CAAC,CAAEgB,GAAG,CAACG,CAAC,EAAEH,GAAG,CAACI,CAAC,CAAE,CAAC,CAAC,CAAC;IACrFqB,OAAO,GAAGA,OAAO,CAACE,cAAc,CAAC3B,GAAG,CAAC4B,OAAO,CAAC;IAE7C,MAAMnB,MAAM,GAAGgB,OAAO,CAACF,gBAAgB,CAACpC,YAAY,CAACY,MAAM,CAAC,CAAC;IAC7DT,cAAc,CAACmB,MAAM,IAAI,IAAI,EAAE,8BAA8B,EAAE,WAAW,EAAEe,SAAS,CAAC;IAEtF,OAAO,IAAI,GAAGf,MAAM,CAACoB,KAAK,CAAC,KAAK,CAAC;EACrC;EAEA;;;;;;;;;;EAUA,OAAOC,SAASA,CAACC,EAAa,EAAEC,EAAa,EAAEpB,UAAoB;IAC/D,MAAMqB,IAAI,GAAGlD,SAAS,CAACqC,eAAe,CAACC,OAAO,CAAC7B,UAAU,CAACI,gBAAgB,CAACmC,EAAE,CAAC,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5F,MAAMC,IAAI,GAAGpD,SAAS,CAACqC,eAAe,CAACC,OAAO,CAAC7B,UAAU,CAACI,gBAAgB,CAACoC,EAAE,CAAC,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;IAC5F,OAAO,IAAI,GAAGD,IAAI,CAACG,GAAG,CAACD,IAAI,CAAC,CAACN,KAAK,CAAC,CAAC,CAACjB,UAAU,CAAC;EACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}