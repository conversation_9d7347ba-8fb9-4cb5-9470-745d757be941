{"ast": null, "code": "import { assertArgument } from \"../utils/index.js\";\nimport { decodeBits } from \"./bit-reader.js\";\nimport { decodeOwl } from \"./decode-owl.js\";\n/**\n *  @_ignore\n */\nexport function decodeOwlA(data, accents) {\n  let words = decodeOwl(data).join(\",\");\n  // Inject the accents\n  accents.split(/,/g).forEach(accent => {\n    const match = accent.match(/^([a-z]*)([0-9]+)([0-9])(.*)$/);\n    assertArgument(match !== null, \"internal error parsing accents\", \"accents\", accents);\n    let posOffset = 0;\n    const positions = decodeBits(parseInt(match[3]), match[4]);\n    const charCode = parseInt(match[2]);\n    const regex = new RegExp(`([${match[1]}])`, \"g\");\n    words = words.replace(regex, (all, letter) => {\n      const rem = --positions[posOffset];\n      if (rem === 0) {\n        letter = String.fromCharCode(letter.charCodeAt(0), charCode);\n        posOffset++;\n      }\n      return letter;\n    });\n  });\n  return words.split(\",\");\n}", "map": {"version": 3, "names": ["assertArgument", "decodeBits", "decodeOwl", "decodeOwlA", "data", "accents", "words", "join", "split", "for<PERSON>ach", "accent", "match", "posOffset", "positions", "parseInt", "charCode", "regex", "RegExp", "replace", "all", "letter", "rem", "String", "fromCharCode", "charCodeAt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\decode-owla.ts"], "sourcesContent": ["import { assertArgument } from \"../utils/index.js\";\n\nimport { decodeBits } from \"./bit-reader.js\";\nimport { decodeOwl } from \"./decode-owl.js\";\n\n/**\n *  @_ignore\n */\nexport function decodeOwlA(data: string, accents: string): Array<string> {\n    let words = decodeOwl(data).join(\",\");\n\n    // Inject the accents\n    accents.split(/,/g).forEach((accent) => {\n\n        const match = accent.match(/^([a-z]*)([0-9]+)([0-9])(.*)$/);\n        assertArgument(match !== null, \"internal error parsing accents\", \"accents\", accents);\n\n        let posOffset = 0;\n        const positions = decodeBits(parseInt(match[3]), match[4]);\n        const charCode = parseInt(match[2]);\n        const regex = new RegExp(`([${ match[1] }])`, \"g\");\n        words = words.replace(regex, (all, letter) => {\n            const rem = --positions[posOffset];\n            if (rem === 0) {\n                letter = String.fromCharCode(letter.charCodeAt(0), charCode);\n                posOffset++;\n            }\n            return letter;\n        });\n    });\n\n    return words.split(\",\");\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAElD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,SAAS,QAAQ,iBAAiB;AAE3C;;;AAGA,OAAM,SAAUC,UAAUA,CAACC,IAAY,EAAEC,OAAe;EACpD,IAAIC,KAAK,GAAGJ,SAAS,CAACE,IAAI,CAAC,CAACG,IAAI,CAAC,GAAG,CAAC;EAErC;EACAF,OAAO,CAACG,KAAK,CAAC,IAAI,CAAC,CAACC,OAAO,CAAEC,MAAM,IAAI;IAEnC,MAAMC,KAAK,GAAGD,MAAM,CAACC,KAAK,CAAC,+BAA+B,CAAC;IAC3DX,cAAc,CAACW,KAAK,KAAK,IAAI,EAAE,gCAAgC,EAAE,SAAS,EAAEN,OAAO,CAAC;IAEpF,IAAIO,SAAS,GAAG,CAAC;IACjB,MAAMC,SAAS,GAAGZ,UAAU,CAACa,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAMI,QAAQ,GAAGD,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACnC,MAAMK,KAAK,GAAG,IAAIC,MAAM,CAAC,KAAMN,KAAK,CAAC,CAAC,CAAE,IAAI,EAAE,GAAG,CAAC;IAClDL,KAAK,GAAGA,KAAK,CAACY,OAAO,CAACF,KAAK,EAAE,CAACG,GAAG,EAAEC,MAAM,KAAI;MACzC,MAAMC,GAAG,GAAG,EAAER,SAAS,CAACD,SAAS,CAAC;MAClC,IAAIS,GAAG,KAAK,CAAC,EAAE;QACXD,MAAM,GAAGE,MAAM,CAACC,YAAY,CAACH,MAAM,CAACI,UAAU,CAAC,CAAC,CAAC,EAAET,QAAQ,CAAC;QAC5DH,SAAS,EAAE;;MAEf,OAAOQ,MAAM;IACjB,CAAC,CAAC;EACN,CAAC,CAAC;EAEF,OAAOd,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}