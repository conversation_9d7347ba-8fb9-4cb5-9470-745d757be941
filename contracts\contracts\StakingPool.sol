// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./CQTToken.sol";

/**
 * @title StakingPool
 * @dev Allows players to stake CQT tokens for rewards and game bonuses
 */
contract StakingPool is Ownable, Pausable, ReentrancyGuard {
    
    CQTToken public cqtToken;
    
    // Staking parameters
    uint256 public constant MINIMUM_STAKE = 100 * 10**18; // 100 CQT minimum
    uint256 public constant LOCK_PERIOD = 7 days;         // 7 days lock period
    uint256 public annualRewardRate = 1200; // 12% APY (basis points)
    
    // Staking data
    struct StakeInfo {
        uint256 amount;
        uint256 stakeTime;
        uint256 lastRewardTime;
        uint256 accumulatedRewards;
        bool isActive;
    }
    
    mapping(address => StakeInfo) public stakes;
    mapping(address => bool) public hasXPBoost;
    
    uint256 public totalStaked;
    uint256 public totalRewardsDistributed;
    
    // Events
    event Staked(address indexed user, uint256 amount);
    event Unstaked(address indexed user, uint256 amount);
    event RewardsClaimed(address indexed user, uint256 amount);
    event XPBoostActivated(address indexed user);
    event XPBoostDeactivated(address indexed user);
    
    constructor(address _cqtToken) Ownable(msg.sender) {
        require(_cqtToken != address(0), "Invalid CQT token address");
        cqtToken = CQTToken(_cqtToken);
    }
    
    /**
     * @dev Stake CQT tokens
     */
    function stake(uint256 _amount) external whenNotPaused nonReentrant {
        require(_amount >= MINIMUM_STAKE, "Amount below minimum stake");
        require(cqtToken.balanceOf(msg.sender) >= _amount, "Insufficient balance");
        
        // If user already has a stake, claim pending rewards first
        if (stakes[msg.sender].isActive) {
            _claimRewards(msg.sender);
            stakes[msg.sender].amount += _amount;
        } else {
            stakes[msg.sender] = StakeInfo({
                amount: _amount,
                stakeTime: block.timestamp,
                lastRewardTime: block.timestamp,
                accumulatedRewards: 0,
                isActive: true
            });
        }
        
        totalStaked += _amount;
        
        // Transfer tokens to this contract
        cqtToken.transferFrom(msg.sender, address(this), _amount);
        
        // Activate XP boost if staking >= 1000 CQT
        if (stakes[msg.sender].amount >= 1000 * 10**18) {
            hasXPBoost[msg.sender] = true;
            emit XPBoostActivated(msg.sender);
        }
        
        emit Staked(msg.sender, _amount);
    }
    
    /**
     * @dev Unstake CQT tokens
     */
    function unstake() external nonReentrant {
        StakeInfo storage userStake = stakes[msg.sender];
        require(userStake.isActive, "No active stake");
        require(
            block.timestamp >= userStake.stakeTime + LOCK_PERIOD,
            "Stake is still locked"
        );
        
        uint256 stakedAmount = userStake.amount;
        
        // Claim any pending rewards
        _claimRewards(msg.sender);
        
        // Reset stake info
        userStake.isActive = false;
        userStake.amount = 0;
        totalStaked -= stakedAmount;
        
        // Deactivate XP boost
        hasXPBoost[msg.sender] = false;
        emit XPBoostDeactivated(msg.sender);
        
        // Transfer staked tokens back to user
        cqtToken.transfer(msg.sender, stakedAmount);
        
        emit Unstaked(msg.sender, stakedAmount);
    }
    
    /**
     * @dev Claim staking rewards
     */
    function claimRewards() external nonReentrant {
        require(stakes[msg.sender].isActive, "No active stake");
        _claimRewards(msg.sender);
    }
    
    /**
     * @dev Internal function to claim rewards
     */
    function _claimRewards(address _user) internal {
        StakeInfo storage userStake = stakes[_user];
        uint256 pendingRewards = calculatePendingRewards(_user);
        
        if (pendingRewards > 0) {
            userStake.accumulatedRewards += pendingRewards;
            userStake.lastRewardTime = block.timestamp;
            totalRewardsDistributed += pendingRewards;
            
            // Mint rewards from staking pool allocation
            cqtToken.transfer(_user, pendingRewards);
            
            emit RewardsClaimed(_user, pendingRewards);
        }
    }
    
    /**
     * @dev Calculate pending rewards for a user
     */
    function calculatePendingRewards(address _user) public view returns (uint256) {
        StakeInfo memory userStake = stakes[_user];
        
        if (!userStake.isActive || userStake.amount == 0) {
            return 0;
        }
        
        uint256 timeStaked = block.timestamp - userStake.lastRewardTime;
        uint256 annualReward = (userStake.amount * annualRewardRate) / 10000;
        uint256 reward = (annualReward * timeStaked) / 365 days;
        
        return reward;
    }
    
    /**
     * @dev Get user stake information
     */
    function getStakeInfo(address _user) external view returns (
        uint256 amount,
        uint256 stakeTime,
        uint256 pendingRewards,
        uint256 accumulatedRewards,
        bool isActive,
        bool xpBoost,
        uint256 unlockTime
    ) {
        StakeInfo memory userStake = stakes[_user];
        return (
            userStake.amount,
            userStake.stakeTime,
            calculatePendingRewards(_user),
            userStake.accumulatedRewards,
            userStake.isActive,
            hasXPBoost[_user],
            userStake.stakeTime + LOCK_PERIOD
        );
    }
    
    /**
     * @dev Check if user has XP boost from staking
     */
    function hasStakingBoost(address _user) external view returns (bool) {
        return hasXPBoost[_user];
    }
    
    /**
     * @dev Update annual reward rate (only owner)
     */
    function updateRewardRate(uint256 _newRate) external onlyOwner {
        require(_newRate <= 5000, "Rate too high"); // Max 50% APY
        annualRewardRate = _newRate;
    }
    
    /**
     * @dev Get pool statistics
     */
    function getPoolStats() external view returns (
        uint256 _totalStaked,
        uint256 _totalRewardsDistributed,
        uint256 _currentAPY,
        uint256 _poolBalance
    ) {
        return (
            totalStaked,
            totalRewardsDistributed,
            annualRewardRate,
            cqtToken.balanceOf(address(this))
        );
    }
    
    /**
     * @dev Emergency unstake (forfeit rewards)
     */
    function emergencyUnstake() external nonReentrant {
        StakeInfo storage userStake = stakes[msg.sender];
        require(userStake.isActive, "No active stake");
        
        uint256 stakedAmount = userStake.amount;
        
        // Reset stake info without claiming rewards
        userStake.isActive = false;
        userStake.amount = 0;
        totalStaked -= stakedAmount;
        
        // Deactivate XP boost
        hasXPBoost[msg.sender] = false;
        emit XPBoostDeactivated(msg.sender);
        
        // Transfer staked tokens back (no rewards)
        cqtToken.transfer(msg.sender, stakedAmount);
        
        emit Unstaked(msg.sender, stakedAmount);
    }
    
    /**
     * @dev Pause staking
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause staking
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Emergency withdrawal (only owner)
     */
    function emergencyWithdraw(uint256 _amount) external onlyOwner {
        require(_amount <= cqtToken.balanceOf(address(this)), "Insufficient balance");
        cqtToken.transfer(owner(), _amount);
    }
}
