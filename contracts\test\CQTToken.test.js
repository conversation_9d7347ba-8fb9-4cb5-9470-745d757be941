const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("CQTToken", function () {
  let cqtToken;
  let owner, playToEarn, ecosystem, team, marketing, staking, liquidity;
  let gameContract, player1, player2;

  beforeEach(async function () {
    [owner, playToEarn, ecosystem, team, marketing, staking, liquidity, gameContract, player1, player2] = await ethers.getSigners();

    const CQTToken = await ethers.getContractFactory("CQTToken");
    cqtToken = await CQTToken.deploy(
      playToEarn.address,
      ecosystem.address,
      team.address,
      marketing.address,
      staking.address,
      liquidity.address
    );
    await cqtToken.waitForDeployment();
  });

  describe("Deployment", function () {
    it("Should set the correct name and symbol", async function () {
      expect(await cqtToken.name()).to.equal("CryptoQuest Token");
      expect(await cqtToken.symbol()).to.equal("CQT");
    });

    it("Should have correct total supply", async function () {
      const totalSupply = await cqtToken.totalSupply();
      expect(totalSupply).to.equal(ethers.parseEther("1000000000")); // 1 billion tokens
    });

    it("Should distribute tokens correctly", async function () {
      const playToEarnBalance = await cqtToken.balanceOf(playToEarn.address);
      const ecosystemBalance = await cqtToken.balanceOf(ecosystem.address);
      const teamBalance = await cqtToken.balanceOf(team.address);
      const marketingBalance = await cqtToken.balanceOf(marketing.address);
      const stakingBalance = await cqtToken.balanceOf(staking.address);
      const liquidityBalance = await cqtToken.balanceOf(liquidity.address);

      expect(playToEarnBalance).to.equal(ethers.parseEther("400000000")); // 40%
      expect(ecosystemBalance).to.equal(ethers.parseEther("200000000"));  // 20%
      expect(teamBalance).to.equal(ethers.parseEther("150000000"));       // 15%
      expect(marketingBalance).to.equal(ethers.parseEther("100000000"));  // 10%
      expect(stakingBalance).to.equal(ethers.parseEther("100000000"));    // 10%
      expect(liquidityBalance).to.equal(ethers.parseEther("50000000"));   // 5%
    });

    it("Should set the correct owner", async function () {
      expect(await cqtToken.owner()).to.equal(owner.address);
    });
  });

  describe("Game Contract Management", function () {
    it("Should allow owner to add game contract", async function () {
      await cqtToken.addGameContract(gameContract.address);
      expect(await cqtToken.gameContracts(gameContract.address)).to.be.true;
    });

    it("Should not allow non-owner to add game contract", async function () {
      await expect(
        cqtToken.connect(player1).addGameContract(gameContract.address)
      ).to.be.revertedWithCustomError(cqtToken, "OwnableUnauthorizedAccount");
    });

    it("Should allow owner to remove game contract", async function () {
      await cqtToken.addGameContract(gameContract.address);
      await cqtToken.removeGameContract(gameContract.address);
      expect(await cqtToken.gameContracts(gameContract.address)).to.be.false;
    });
  });

  describe("Reward Distribution", function () {
    beforeEach(async function () {
      await cqtToken.addGameContract(gameContract.address);
    });

    it("Should allow authorized game contract to distribute rewards", async function () {
      const rewardAmount = ethers.parseEther("100");
      
      await cqtToken.connect(gameContract).distributeReward(
        player1.address,
        rewardAmount,
        "PvE Victory"
      );

      const playerBalance = await cqtToken.balanceOf(player1.address);
      expect(playerBalance).to.equal(rewardAmount);
    });

    it("Should not allow unauthorized contract to distribute rewards", async function () {
      const rewardAmount = ethers.parseEther("100");
      
      await expect(
        cqtToken.connect(player1).distributeReward(
          player2.address,
          rewardAmount,
          "PvE Victory"
        )
      ).to.be.revertedWith("Not authorized game contract");
    });

    it("Should emit TokensDistributed event", async function () {
      const rewardAmount = ethers.parseEther("100");
      
      await expect(
        cqtToken.connect(gameContract).distributeReward(
          player1.address,
          rewardAmount,
          "PvE Victory"
        )
      ).to.emit(cqtToken, "TokensDistributed")
       .withArgs(player1.address, rewardAmount, "PvE Victory");
    });
  });

  describe("Burning", function () {
    beforeEach(async function () {
      await cqtToken.addGameContract(gameContract.address);
      // Give player1 some tokens
      await cqtToken.connect(gameContract).distributeReward(
        player1.address,
        ethers.parseEther("1000"),
        "Test"
      );
    });

    it("Should allow token holder to burn their own tokens", async function () {
      const burnAmount = ethers.parseEther("100");
      const initialBalance = await cqtToken.balanceOf(player1.address);
      
      await cqtToken.connect(player1).burn(burnAmount);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      expect(finalBalance).to.equal(initialBalance - burnAmount);
    });

    it("Should allow game contract to burn from player", async function () {
      const burnAmount = ethers.parseEther("100");
      const initialBalance = await cqtToken.balanceOf(player1.address);
      
      // Player approves game contract to spend tokens
      await cqtToken.connect(player1).approve(gameContract.address, burnAmount);
      
      // Game contract burns tokens
      await cqtToken.connect(gameContract).burnFrom(player1.address, burnAmount);
      
      const finalBalance = await cqtToken.balanceOf(player1.address);
      expect(finalBalance).to.equal(initialBalance - burnAmount);
    });
  });

  describe("Pause Functionality", function () {
    it("Should allow owner to pause transfers", async function () {
      await cqtToken.pause();
      expect(await cqtToken.paused()).to.be.true;
    });

    it("Should prevent transfers when paused", async function () {
      // Give player1 some tokens first
      await cqtToken.addGameContract(gameContract.address);
      await cqtToken.connect(gameContract).distributeReward(
        player1.address,
        ethers.parseEther("100"),
        "Test"
      );
      
      await cqtToken.pause();
      
      await expect(
        cqtToken.connect(player1).transfer(player2.address, ethers.parseEther("50"))
      ).to.be.revertedWithCustomError(cqtToken, "EnforcedPause");
    });

    it("Should allow owner to unpause", async function () {
      await cqtToken.pause();
      await cqtToken.unpause();
      expect(await cqtToken.paused()).to.be.false;
    });
  });
});
