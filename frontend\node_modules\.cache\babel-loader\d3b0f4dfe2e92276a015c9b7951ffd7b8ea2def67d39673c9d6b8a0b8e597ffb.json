{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useGame } from '../contexts/GameContext';\nimport { useWeb3 } from '../contexts/Web3Context';\nimport TokenBalance from '../components/TokenBalance';\nimport QuestCard from '../components/QuestCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport { ShieldCheckIcon, TrophyIcon, CurrencyDollarIcon, ChartBarIcon, PlayIcon, GiftIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    heroes,\n    userStats,\n    battleHistory,\n    dailyQuests,\n    loading,\n    claimQuestReward\n  } = useGame();\n  const [claimingQuest, setClaimingQuest] = useState(null);\n  const handleClaimQuest = async questId => {\n    try {\n      setClaimingQuest(questId);\n      await claimQuestReward(questId);\n    } catch (error) {\n      console.error('Failed to claim quest:', error);\n    } finally {\n      setClaimingQuest(null);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        size: \"lg\",\n        text: \"Loading dashboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this);\n  }\n  const quickStats = [{\n    label: 'Heroes',\n    value: (heroes === null || heroes === void 0 ? void 0 : heroes.length) || 0,\n    icon: ShieldCheckIcon,\n    color: 'text-blue-400'\n  }, {\n    label: 'Level',\n    value: (user === null || user === void 0 ? void 0 : user.level) || 1,\n    icon: ChartBarIcon,\n    color: 'text-green-400'\n  }, {\n    label: 'Wins',\n    value: (user === null || user === void 0 ? void 0 : user.wins) || 0,\n    icon: TrophyIcon,\n    color: 'text-purple-400'\n  }];\n  const quickActions = [{\n    title: 'Start Battle',\n    description: 'Fight monsters and earn CQT',\n    href: '/battle',\n    icon: PlayIcon,\n    color: 'from-red-500 to-red-600'\n  }, {\n    title: 'Manage Heroes',\n    description: 'Upgrade and customize your team',\n    href: '/heroes',\n    icon: ShieldCheckIcon,\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    title: 'Stake Tokens',\n    description: 'Earn passive rewards',\n    href: '/staking',\n    icon: CurrencyDollarIcon,\n    color: 'from-green-500 to-green-600'\n  }, {\n    title: 'Join Tournament',\n    description: 'Compete for big prizes',\n    href: '/tournaments',\n    icon: TrophyIcon,\n    color: 'from-purple-500 to-purple-600'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-white mb-2\",\n        children: [\"Welcome back, \", (user === null || user === void 0 ? void 0 : user.username) || 'Warrior', \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-dark-300\",\n        children: \"Ready for your next adventure?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center mb-8\",\n      children: /*#__PURE__*/_jsxDEV(TokenBalance, {\n        showRefresh: true,\n        className: \"scale-110\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n      children: quickStats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"game-card p-6 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(stat.icon, {\n          className: `w-10 h-10 ${stat.color} mx-auto mb-3`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-3xl font-bold text-white mb-1\",\n          children: stat.value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-dark-400\",\n          children: stat.label\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-white mb-4\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n        children: quickActions.map((action, index) => /*#__PURE__*/_jsxDEV(Link, {\n          to: action.href,\n          className: \"game-card p-6 hover:glow-primary transition-all duration-300 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`,\n            children: /*#__PURE__*/_jsxDEV(action.icon, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-white mb-2\",\n            children: action.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-dark-400 text-sm\",\n            children: action.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), dailyQuests && dailyQuests.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-white mb-4 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(GiftIcon, {\n          className: \"w-6 h-6 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), \"Daily Quests\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n        children: dailyQuests.slice(0, 3).map((quest, index) => /*#__PURE__*/_jsxDEV(QuestCard, {\n          quest: quest,\n          onClaim: handleClaimQuest,\n          claiming: claimingQuest === quest.id\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-white mb-4\",\n          children: \"Recent Battles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"game-card p-4\",\n          children: battleHistory && battleHistory.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: battleHistory.slice(0, 5).map((battle, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-white font-medium capitalize\",\n                  children: battle.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-dark-400 text-sm ml-2\",\n                  children: [\"vs \", battle.opponent === 'AI' ? 'Monster' : 'Player']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `font-medium ${battle.result === 'win' ? 'text-green-400' : 'text-red-400'}`,\n                  children: battle.result === 'win' ? 'Victory' : 'Defeat'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 23\n                }, this), battle.reward > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-yellow-400 text-sm\",\n                  children: [\"+\", battle.reward, \" CQT\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-dark-400\",\n            children: [/*#__PURE__*/_jsxDEV(TrophyIcon, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No battles yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/battle\",\n              className: \"text-primary-400 hover:text-primary-300 text-sm\",\n              children: \"Start your first battle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-white mb-4\",\n          children: \"Your Heroes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"game-card p-4\",\n          children: heroes && heroes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [heroes.slice(0, 3).map((hero, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-white font-medium\",\n                  children: hero.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-dark-400 text-sm capitalize\",\n                  children: [hero.type, \" \\u2022 Level \", hero.level]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-primary-400 font-medium\",\n                  children: [hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk, \" Power\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 19\n            }, this)), heroes.length > 3 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center pt-2\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/heroes\",\n                className: \"text-primary-400 hover:text-primary-300 text-sm\",\n                children: [\"View all \", heroes.length, \" heroes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-dark-400\",\n            children: [/*#__PURE__*/_jsxDEV(ShieldCheckIcon, {\n              className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No heroes found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/heroes\",\n              className: \"text-primary-400 hover:text-primary-300 text-sm\",\n              children: \"Manage your heroes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"0HufjV/GR3g9XrEFte1yqBwzqBw=\", false, function () {\n  return [useAuth, useGame];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "useAuth", "useGame", "useWeb3", "TokenBalance", "QuestCard", "LoadingSpinner", "ShieldCheckIcon", "TrophyIcon", "CurrencyDollarIcon", "ChartBarIcon", "PlayIcon", "GiftIcon", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "user", "heroes", "userStats", "battleHistory", "dailyQuests", "loading", "claimQuestReward", "claimingQuest", "setClaimingQuest", "handleClaimQuest", "questId", "error", "console", "className", "children", "size", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "quickStats", "label", "value", "length", "icon", "color", "level", "wins", "quickActions", "title", "description", "href", "username", "showRefresh", "map", "stat", "index", "action", "to", "slice", "quest", "onClaim", "claiming", "id", "battle", "type", "opponent", "result", "reward", "hero", "name", "stats", "hp", "atk", "def", "spd", "luk", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useGame } from '../contexts/GameContext';\nimport { useWeb3 } from '../contexts/Web3Context';\nimport TokenBalance from '../components/TokenBalance';\nimport QuestCard from '../components/QuestCard';\nimport LoadingSpinner from '../components/LoadingSpinner';\nimport {\n  ShieldCheckIcon,\n  TrophyIcon,\n  CurrencyDollarIcon,\n  ChartBarIcon,\n  PlayIcon,\n  GiftIcon\n} from '@heroicons/react/24/outline';\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const { heroes, userStats, battleHistory, dailyQuests, loading, claimQuestReward } = useGame();\n  const [claimingQuest, setClaimingQuest] = useState(null);\n\n  const handleClaimQuest = async (questId) => {\n    try {\n      setClaimingQuest(questId);\n      await claimQuestReward(questId);\n    } catch (error) {\n      console.error('Failed to claim quest:', error);\n    } finally {\n      setClaimingQuest(null);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <LoadingSpinner size=\"lg\" text=\"Loading dashboard...\" />\n      </div>\n    );\n  }\n\n  const quickStats = [\n    {\n      label: 'Heroes',\n      value: heroes?.length || 0,\n      icon: ShieldCheckIcon,\n      color: 'text-blue-400',\n    },\n    {\n      label: 'Level',\n      value: user?.level || 1,\n      icon: ChartBarIcon,\n      color: 'text-green-400',\n    },\n    {\n      label: 'Wins',\n      value: user?.wins || 0,\n      icon: TrophyIcon,\n      color: 'text-purple-400',\n    },\n  ];\n\n  const quickActions = [\n    {\n      title: 'Start Battle',\n      description: 'Fight monsters and earn CQT',\n      href: '/battle',\n      icon: PlayIcon,\n      color: 'from-red-500 to-red-600',\n    },\n    {\n      title: 'Manage Heroes',\n      description: 'Upgrade and customize your team',\n      href: '/heroes',\n      icon: ShieldCheckIcon,\n      color: 'from-blue-500 to-blue-600',\n    },\n    {\n      title: 'Stake Tokens',\n      description: 'Earn passive rewards',\n      href: '/staking',\n      icon: CurrencyDollarIcon,\n      color: 'from-green-500 to-green-600',\n    },\n    {\n      title: 'Join Tournament',\n      description: 'Compete for big prizes',\n      href: '/tournaments',\n      icon: TrophyIcon,\n      color: 'from-purple-500 to-purple-600',\n    },\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Header */}\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-white mb-2\">\n          Welcome back, {user?.username || 'Warrior'}!\n        </h1>\n        <p className=\"text-dark-300\">\n          Ready for your next adventure?\n        </p>\n      </div>\n\n      {/* Token Balance */}\n      <div className=\"flex justify-center mb-8\">\n        <TokenBalance showRefresh={true} className=\"scale-110\" />\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        {quickStats.map((stat, index) => (\n          <div key={index} className=\"game-card p-6 text-center\">\n            <stat.icon className={`w-10 h-10 ${stat.color} mx-auto mb-3`} />\n            <div className=\"text-3xl font-bold text-white mb-1\">{stat.value}</div>\n            <div className=\"text-sm text-dark-400\">{stat.label}</div>\n          </div>\n        ))}\n      </div>\n\n      {/* Quick Actions */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-white mb-4\">Quick Actions</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {quickActions.map((action, index) => (\n            <Link\n              key={index}\n              to={action.href}\n              className=\"game-card p-6 hover:glow-primary transition-all duration-300 group\"\n            >\n              <div className={`w-12 h-12 bg-gradient-to-r ${action.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>\n                <action.icon className=\"w-6 h-6 text-white\" />\n              </div>\n              <h3 className=\"text-lg font-semibold text-white mb-2\">{action.title}</h3>\n              <p className=\"text-dark-400 text-sm\">{action.description}</p>\n            </Link>\n          ))}\n        </div>\n      </div>\n\n      {/* Daily Quests */}\n      {dailyQuests && dailyQuests.length > 0 && (\n        <div>\n          <h2 className=\"text-2xl font-bold text-white mb-4 flex items-center\">\n            <GiftIcon className=\"w-6 h-6 mr-2\" />\n            Daily Quests\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {dailyQuests.slice(0, 3).map((quest, index) => (\n              <QuestCard\n                key={index}\n                quest={quest}\n                onClaim={handleClaimQuest}\n                claiming={claimingQuest === quest.id}\n              />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Recent Battles */}\n        <div>\n          <h2 className=\"text-2xl font-bold text-white mb-4\">Recent Battles</h2>\n          <div className=\"game-card p-4\">\n            {battleHistory && battleHistory.length > 0 ? (\n              <div className=\"space-y-3\">\n                {battleHistory.slice(0, 5).map((battle, index) => (\n                  <div key={index} className=\"flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0\">\n                    <div>\n                      <span className=\"text-white font-medium capitalize\">{battle.type}</span>\n                      <span className=\"text-dark-400 text-sm ml-2\">\n                        vs {battle.opponent === 'AI' ? 'Monster' : 'Player'}\n                      </span>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className={`font-medium ${battle.result === 'win' ? 'text-green-400' : 'text-red-400'}`}>\n                        {battle.result === 'win' ? 'Victory' : 'Defeat'}\n                      </div>\n                      {battle.reward > 0 && (\n                        <div className=\"text-yellow-400 text-sm\">+{battle.reward} CQT</div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-dark-400\">\n                <TrophyIcon className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                <p>No battles yet</p>\n                <Link to=\"/battle\" className=\"text-primary-400 hover:text-primary-300 text-sm\">\n                  Start your first battle\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Hero Overview */}\n        <div>\n          <h2 className=\"text-2xl font-bold text-white mb-4\">Your Heroes</h2>\n          <div className=\"game-card p-4\">\n            {heroes && heroes.length > 0 ? (\n              <div className=\"space-y-3\">\n                {heroes.slice(0, 3).map((hero, index) => (\n                  <div key={index} className=\"flex justify-between items-center py-2 border-b border-dark-700 last:border-b-0\">\n                    <div>\n                      <div className=\"text-white font-medium\">{hero.name}</div>\n                      <div className=\"text-dark-400 text-sm capitalize\">{hero.type} • Level {hero.level}</div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-primary-400 font-medium\">\n                        {hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk} Power\n                      </div>\n                    </div>\n                  </div>\n                ))}\n                {heroes.length > 3 && (\n                  <div className=\"text-center pt-2\">\n                    <Link to=\"/heroes\" className=\"text-primary-400 hover:text-primary-300 text-sm\">\n                      View all {heroes.length} heroes\n                    </Link>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-dark-400\">\n                <ShieldCheckIcon className=\"w-12 h-12 mx-auto mb-2 opacity-50\" />\n                <p>No heroes found</p>\n                <Link to=\"/heroes\" className=\"text-primary-400 hover:text-primary-300 text-sm\">\n                  Manage your heroes\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SACEC,eAAe,EACfC,UAAU,EACVC,kBAAkB,EAClBC,YAAY,EACZC,QAAQ,EACRC,QAAQ,QACH,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEiB,MAAM;IAAEC,SAAS;IAAEC,aAAa;IAAEC,WAAW;IAAEC,OAAO;IAAEC;EAAiB,CAAC,GAAGrB,OAAO,CAAC,CAAC;EAC9F,MAAM,CAACsB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAM2B,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MACFF,gBAAgB,CAACE,OAAO,CAAC;MACzB,MAAMJ,gBAAgB,CAACI,OAAO,CAAC;IACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRH,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,IAAIH,OAAO,EAAE;IACX,oBACER,OAAA;MAAKgB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DjB,OAAA,CAACR,cAAc;QAAC0B,IAAI,EAAC,IAAI;QAACC,IAAI,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEV;EAEA,MAAMC,UAAU,GAAG,CACjB;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEuB,MAAM,KAAI,CAAC;IAC1BC,IAAI,EAAEnC,eAAe;IACrBoC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,KAAK,KAAI,CAAC;IACvBF,IAAI,EAAEhC,YAAY;IAClBiC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI,KAAI,CAAC;IACtBH,IAAI,EAAElC,UAAU;IAChBmC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,YAAY,GAAG,CACnB;IACEC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,6BAA6B;IAC1CC,IAAI,EAAE,SAAS;IACfP,IAAI,EAAE/B,QAAQ;IACdgC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE,iCAAiC;IAC9CC,IAAI,EAAE,SAAS;IACfP,IAAI,EAAEnC,eAAe;IACrBoC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,sBAAsB;IACnCC,IAAI,EAAE,UAAU;IAChBP,IAAI,EAAEjC,kBAAkB;IACxBkC,KAAK,EAAE;EACT,CAAC,EACD;IACEI,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,wBAAwB;IACrCC,IAAI,EAAE,cAAc;IACpBP,IAAI,EAAElC,UAAU;IAChBmC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACE7B,OAAA;IAAKgB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBjB,OAAA;MAAKgB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BjB,OAAA;QAAIgB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,GAAC,gBACnC,EAAC,CAAAd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,QAAQ,KAAI,SAAS,EAAC,GAC7C;MAAA;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvB,OAAA;QAAGgB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNvB,OAAA;MAAKgB,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvCjB,OAAA,CAACV,YAAY;QAAC+C,WAAW,EAAE,IAAK;QAACrB,SAAS,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAGNvB,OAAA;MAAKgB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnDO,UAAU,CAACc,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC1BxC,OAAA;QAAiBgB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACpDjB,OAAA,CAACuC,IAAI,CAACX,IAAI;UAACZ,SAAS,EAAE,aAAauB,IAAI,CAACV,KAAK;QAAgB;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEvB,OAAA;UAAKgB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAEsB,IAAI,CAACb;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtEvB,OAAA;UAAKgB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEsB,IAAI,CAACd;QAAK;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAHjDiB,KAAK;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvB,OAAA;MAAAiB,QAAA,gBACEjB,OAAA;QAAIgB,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAa;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrEvB,OAAA;QAAKgB,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEe,YAAY,CAACM,GAAG,CAAC,CAACG,MAAM,EAAED,KAAK,kBAC9BxC,OAAA,CAACd,IAAI;UAEHwD,EAAE,EAAED,MAAM,CAACN,IAAK;UAChBnB,SAAS,EAAC,oEAAoE;UAAAC,QAAA,gBAE9EjB,OAAA;YAAKgB,SAAS,EAAE,8BAA8ByB,MAAM,CAACZ,KAAK,8FAA+F;YAAAZ,QAAA,eACvJjB,OAAA,CAACyC,MAAM,CAACb,IAAI;cAACZ,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNvB,OAAA;YAAIgB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAEwB,MAAM,CAACR;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzEvB,OAAA;YAAGgB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAEwB,MAAM,CAACP;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GARxDiB,KAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASN,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLhB,WAAW,IAAIA,WAAW,CAACoB,MAAM,GAAG,CAAC,iBACpC3B,OAAA;MAAAiB,QAAA,gBACEjB,OAAA;QAAIgB,SAAS,EAAC,sDAAsD;QAAAC,QAAA,gBAClEjB,OAAA,CAACF,QAAQ;UAACkB,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvB,OAAA;QAAKgB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDV,WAAW,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,KAAK,EAAEJ,KAAK,kBACxCxC,OAAA,CAACT,SAAS;UAERqD,KAAK,EAAEA,KAAM;UACbC,OAAO,EAAEjC,gBAAiB;UAC1BkC,QAAQ,EAAEpC,aAAa,KAAKkC,KAAK,CAACG;QAAG,GAHhCP,KAAK;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDvB,OAAA;MAAKgB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDjB,OAAA;QAAAiB,QAAA,gBACEjB,OAAA;UAAIgB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtEvB,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3BX,aAAa,IAAIA,aAAa,CAACqB,MAAM,GAAG,CAAC,gBACxC3B,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBX,aAAa,CAACqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACL,GAAG,CAAC,CAACU,MAAM,EAAER,KAAK,kBAC3CxC,OAAA;cAAiBgB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAC1GjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAMgB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAE+B,MAAM,CAACC;gBAAI;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxEvB,OAAA;kBAAMgB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,GAAC,KACxC,EAAC+B,MAAM,CAACE,QAAQ,KAAK,IAAI,GAAG,SAAS,GAAG,QAAQ;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNvB,OAAA;gBAAKgB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjB,OAAA;kBAAKgB,SAAS,EAAE,eAAegC,MAAM,CAACG,MAAM,KAAK,KAAK,GAAG,gBAAgB,GAAG,cAAc,EAAG;kBAAAlC,QAAA,EAC1F+B,MAAM,CAACG,MAAM,KAAK,KAAK,GAAG,SAAS,GAAG;gBAAQ;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EACLyB,MAAM,CAACI,MAAM,GAAG,CAAC,iBAChBpD,OAAA;kBAAKgB,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,GAAC,GAAC,EAAC+B,MAAM,CAACI,MAAM,EAAC,MAAI;gBAAA;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACnE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAdEiB,KAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENvB,OAAA;YAAKgB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CjB,OAAA,CAACN,UAAU;cAACsB,SAAS,EAAC;YAAmC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DvB,OAAA;cAAAiB,QAAA,EAAG;YAAc;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACrBvB,OAAA,CAACd,IAAI;cAACwD,EAAE,EAAC,SAAS;cAAC1B,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAE/E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAAiB,QAAA,gBACEjB,OAAA;UAAIgB,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAW;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEvB,OAAA;UAAKgB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC3Bb,MAAM,IAAIA,MAAM,CAACuB,MAAM,GAAG,CAAC,gBAC1B3B,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,GACvBb,MAAM,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACL,GAAG,CAAC,CAACe,IAAI,EAAEb,KAAK,kBAClCxC,OAAA;cAAiBgB,SAAS,EAAC,iFAAiF;cAAAC,QAAA,gBAC1GjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAKgB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EAAEoC,IAAI,CAACC;gBAAI;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzDvB,OAAA;kBAAKgB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAEoC,IAAI,CAACJ,IAAI,EAAC,gBAAS,EAACI,IAAI,CAACvB,KAAK;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eACNvB,OAAA;gBAAKgB,SAAS,EAAC,YAAY;gBAAAC,QAAA,eACzBjB,OAAA;kBAAKgB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,GAC1CoC,IAAI,CAACE,KAAK,CAACC,EAAE,GAAGH,IAAI,CAACE,KAAK,CAACE,GAAG,GAAGJ,IAAI,CAACE,KAAK,CAACG,GAAG,GAAGL,IAAI,CAACE,KAAK,CAACI,GAAG,GAAGN,IAAI,CAACE,KAAK,CAACK,GAAG,EAAC,QACrF;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GATEiB,KAAK;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAUV,CACN,CAAC,EACDnB,MAAM,CAACuB,MAAM,GAAG,CAAC,iBAChB3B,OAAA;cAAKgB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BjB,OAAA,CAACd,IAAI;gBAACwD,EAAE,EAAC,SAAS;gBAAC1B,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,GAAC,WACpE,EAACb,MAAM,CAACuB,MAAM,EAAC,SAC1B;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,gBAENvB,OAAA;YAAKgB,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBAC7CjB,OAAA,CAACP,eAAe;cAACuB,SAAS,EAAC;YAAmC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjEvB,OAAA;cAAAiB,QAAA,EAAG;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACtBvB,OAAA,CAACd,IAAI;cAACwD,EAAE,EAAC,SAAS;cAAC1B,SAAS,EAAC,iDAAiD;cAAAC,QAAA,EAAC;YAE/E;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CAhOID,SAAS;EAAA,QACId,OAAO,EAC6DC,OAAO;AAAA;AAAAyE,EAAA,GAFxF5D,SAAS;AAkOf,eAAeA,SAAS;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}