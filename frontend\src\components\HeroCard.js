import React from 'react';
import { 
  HeartIcon, 
  BoltIcon, 
  ShieldCheckIcon,
  StarIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';

const HeroCard = ({ hero, onSelect, onUpgrade, isSelected, showUpgrade = false, upgrading = false }) => {
  const getHeroTypeColor = (type) => {
    const colors = {
      warrior: 'from-red-500 to-red-600',
      mage: 'from-blue-500 to-blue-600',
      archer: 'from-green-500 to-green-600',
      assassin: 'from-purple-500 to-purple-600',
      tank: 'from-gray-500 to-gray-600',
    };
    return colors[type] || 'from-gray-500 to-gray-600';
  };

  const getStatIcon = (stat) => {
    const icons = {
      hp: HeartIcon,
      atk: BoltIcon,
      def: ShieldCheckIcon,
      spd: StarIcon,
      luk: StarIcon,
    };
    return icons[stat] || StarIcon;
  };

  const getStatColor = (stat) => {
    const colors = {
      hp: 'text-red-400',
      atk: 'text-orange-400',
      def: 'text-blue-400',
      spd: 'text-green-400',
      luk: 'text-purple-400',
    };
    return colors[stat] || 'text-gray-400';
  };

  const totalPower = Object.values(hero.stats).reduce((sum, stat) => sum + stat, 0);

  return (
    <div
      className={`hero-card ${isSelected ? 'border-primary-500 glow-primary' : ''} ${
        onSelect ? 'cursor-pointer' : ''
      }`}
      onClick={onSelect}
    >
      {/* Hero Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-bold text-white truncate">{hero.name}</h3>
          <div className="flex items-center space-x-2">
            <span className="text-dark-400 capitalize text-sm">{hero.type}</span>
            <span className="text-dark-500">•</span>
            <span className="text-primary-400 text-sm font-medium">Level {hero.level}</span>
          </div>
        </div>
        <div className={`w-10 h-10 bg-gradient-to-br ${getHeroTypeColor(hero.type)} rounded-lg flex items-center justify-center flex-shrink-0`}>
          <ShieldCheckIcon className="w-5 h-5 text-white" />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-2 mb-4">
        {Object.entries(hero.stats).map(([stat, value]) => {
          const Icon = getStatIcon(stat);
          const color = getStatColor(stat);
          return (
            <div key={stat} className="flex items-center justify-between bg-dark-700/50 rounded-lg p-2">
              <div className="flex items-center space-x-1">
                <Icon className={`w-3 h-3 ${color}`} />
                <span className="text-dark-300 uppercase text-xs font-medium">{stat}</span>
              </div>
              <span className="text-white font-bold text-sm">{value}</span>
            </div>
          );
        })}
      </div>

      {/* Total Power */}
      <div className="flex items-center justify-between mb-3 p-2 bg-gradient-to-r from-primary-900/30 to-secondary-900/30 rounded-lg border border-primary-700/30">
        <span className="text-dark-300 text-sm font-medium">Total Power</span>
        <span className="text-primary-400 font-bold">{totalPower}</span>
      </div>

      {/* Experience Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-xs text-dark-400 mb-1">
          <span>Experience</span>
          <span>{hero.experience || 0}/100</span>
        </div>
        <div className="stat-bar h-1.5">
          <div 
            className="stat-fill bg-gradient-to-r from-yellow-500 to-yellow-600" 
            style={{ width: `${((hero.experience || 0) / 100) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Skills Preview */}
      {hero.skills && hero.skills.length > 0 && (
        <div className="mb-4">
          <h4 className="text-xs font-medium text-white mb-2">Skills</h4>
          <div className="flex flex-wrap gap-1">
            {hero.skills.slice(0, 2).map((skill, index) => (
              <span
                key={index}
                className={`text-xs px-2 py-1 rounded-full ${
                  skill.isUnlocked 
                    ? 'bg-green-600/80 text-green-100' 
                    : 'bg-dark-600/80 text-dark-400'
                }`}
              >
                {skill.name}
              </span>
            ))}
            {hero.skills.length > 2 && (
              <span className="text-xs px-2 py-1 rounded-full bg-dark-600/80 text-dark-400">
                +{hero.skills.length - 2}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Upgrade Button */}
      {showUpgrade && onUpgrade && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onUpgrade(hero.id);
          }}
          disabled={upgrading}
          className="w-full game-button text-sm py-2 flex items-center justify-center space-x-1 disabled:opacity-50"
        >
          {upgrading ? (
            <>
              <div className="loading-spinner w-3 h-3"></div>
              <span>Upgrading...</span>
            </>
          ) : (
            <>
              <ArrowUpIcon className="w-3 h-3" />
              <span>Upgrade (50 CQT)</span>
            </>
          )}
        </button>
      )}

      {/* Selection Indicator */}
      {isSelected && (
        <div className="absolute top-2 right-2 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">✓</span>
        </div>
      )}
    </div>
  );
};

export default HeroCard;
