import React, { useState } from 'react';
import { useGame } from '../contexts/GameContext';
import HeroCard from '../components/HeroCard';
import BattleArena from '../components/BattleArena';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  PlayIcon,
  ShieldCheckIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const Battle = () => {
  const { heroes, startPvEBattle } = useGame();
  const [selectedHeroes, setSelectedHeroes] = useState([]);
  const [difficulty, setDifficulty] = useState(1);
  const [battling, setBattling] = useState(false);
  const [battleResult, setBattleResult] = useState(null);
  const [showBattleArena, setShowBattleArena] = useState(false);
  const [enemyHeroes, setEnemyHeroes] = useState([]);

  const handleHeroSelect = (heroId) => {
    if (selectedHeroes.includes(heroId)) {
      setSelectedHeroes(selectedHeroes.filter(id => id !== heroId));
    } else if (selectedHeroes.length < 3) {
      setSelectedHeroes([...selectedHeroes, heroId]);
    } else {
      toast.error('You can only select up to 3 heroes');
    }
  };

  const handleStartBattle = async () => {
    if (selectedHeroes.length === 0) {
      toast.error('Please select at least one hero');
      return;
    }

    try {
      setBattling(true);
      const result = await startPvEBattle(selectedHeroes, difficulty);

      // Generate enemy for display
      const enemy = generatePvEEnemy(difficulty);
      setEnemyHeroes([enemy]);

      setBattleResult(result);
      setShowBattleArena(true);
      toast.success(`Battle ${result.result === 'player' ? 'won' : 'lost'}!`);
    } catch (error) {
      toast.error(error.response?.data?.error || 'Battle failed');
    } finally {
      setBattling(false);
    }
  };

  const generatePvEEnemy = (difficulty) => {
    const enemyTypes = ['Goblin', 'Orc', 'Skeleton', 'Dragon', 'Demon'];
    const baseStats = {
      hp: 80 + (difficulty * 15),
      atk: 15 + (difficulty * 3),
      def: 10 + (difficulty * 2),
      spd: 8 + difficulty,
      luk: 5 + difficulty
    };

    return {
      id: `enemy_${Date.now()}`,
      name: `${enemyTypes[Math.floor(Math.random() * enemyTypes.length)]} Lv.${difficulty}`,
      type: 'enemy',
      level: difficulty,
      stats: baseStats,
      skills: [
        { name: 'Attack', damage: baseStats.atk, cooldown: 1 },
        { name: 'Power Strike', damage: Math.floor(baseStats.atk * 1.5), cooldown: 3 }
      ]
    };
  };

  const getDifficultyReward = (diff) => {
    return diff * 10; // Base reward * difficulty
  };

  const getDifficultyColor = (diff) => {
    const colors = {
      1: 'text-green-400',
      2: 'text-green-400',
      3: 'text-yellow-400',
      4: 'text-yellow-400',
      5: 'text-orange-400',
      6: 'text-orange-400',
      7: 'text-red-400',
      8: 'text-red-400',
      9: 'text-purple-400',
      10: 'text-purple-400',
    };
    return colors[diff] || 'text-gray-400';
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">Adventure Mode</h1>
        <p className="text-dark-300">
          Battle monsters and earn CQT tokens
        </p>
      </div>

      {!battleResult ? (
        <>
          {/* Difficulty Selection */}
          <div className="game-card p-6">
            <h2 className="text-2xl font-bold text-white mb-4">Select Difficulty</h2>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((diff) => (
                <button
                  key={diff}
                  onClick={() => setDifficulty(diff)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    difficulty === diff
                      ? 'border-primary-500 bg-primary-500/20'
                      : 'border-dark-600 hover:border-dark-500'
                  }`}
                >
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${getDifficultyColor(diff)}`}>
                      {diff}
                    </div>
                    <div className="text-sm text-dark-400">
                      +{getDifficultyReward(diff)} CQT
                    </div>
                  </div>
                </button>
              ))}
            </div>
            <div className="mt-4 text-center">
              <p className="text-dark-300">
                Selected: <span className={`font-bold ${getDifficultyColor(difficulty)}`}>
                  Level {difficulty}
                </span> • Reward: <span className="text-yellow-400">
                  {getDifficultyReward(difficulty)} CQT
                </span>
              </p>
            </div>
          </div>

          {/* Hero Selection */}
          <div className="game-card p-6">
            <h2 className="text-2xl font-bold text-white mb-4">
              Select Heroes ({selectedHeroes.length}/3)
            </h2>

            {heroes && heroes.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {heroes.map((hero) => (
                  <div
                    key={hero.id}
                    onClick={() => handleHeroSelect(hero.id)}
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                      selectedHeroes.includes(hero.id)
                        ? 'border-primary-500 bg-primary-500/20 glow-primary'
                        : 'border-dark-600 hover:border-dark-500'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-bold text-white">{hero.name}</h3>
                        <p className="text-dark-400 capitalize text-sm">
                          {hero.type} • Level {hero.level}
                        </p>
                      </div>
                      <ShieldCheckIcon className="w-8 h-8 text-primary-400" />
                    </div>

                    <div className="grid grid-cols-3 gap-2 text-center">
                      <div>
                        <div className="text-lg font-bold text-red-400">{hero.stats.hp}</div>
                        <div className="text-xs text-dark-400">HP</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-orange-400">{hero.stats.atk}</div>
                        <div className="text-xs text-dark-400">ATK</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-blue-400">{hero.stats.def}</div>
                        <div className="text-xs text-dark-400">DEF</div>
                      </div>
                    </div>

                    <div className="mt-3 text-center">
                      <span className="text-primary-400 font-medium">
                        Power: {Object.values(hero.stats).reduce((sum, stat) => sum + stat, 0)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-dark-400">
                <ShieldCheckIcon className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No heroes available</p>
              </div>
            )}
          </div>

          {/* Battle Button */}
          <div className="text-center">
            <button
              onClick={handleStartBattle}
              disabled={selectedHeroes.length === 0 || battling}
              className="game-button text-lg px-8 py-4 flex items-center space-x-2 mx-auto disabled:opacity-50"
            >
              {battling ? (
                <>
                  <div className="loading-spinner w-5 h-5"></div>
                  <span>Battling...</span>
                </>
              ) : (
                <>
                  <PlayIcon className="w-5 h-5" />
                  <span>Start Battle</span>
                </>
              )}
            </button>
          </div>
        </>
      ) : (
        /* Battle Result */
        <div className="game-card p-8 text-center">
          <div className={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center ${
            battleResult.result === 'player'
              ? 'bg-green-500/20 text-green-400'
              : 'bg-red-500/20 text-red-400'
          }`}>
            <TrophyIcon className="w-10 h-10" />
          </div>

          <h2 className={`text-3xl font-bold mb-4 ${
            battleResult.result === 'player' ? 'text-green-400' : 'text-red-400'
          }`}>
            {battleResult.result === 'player' ? 'Victory!' : 'Defeat!'}
          </h2>

          {battleResult.reward > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-center space-x-2 text-yellow-400 text-xl font-bold">
                <CurrencyDollarIcon className="w-6 h-6" />
                <span>+{battleResult.reward} CQT</span>
              </div>
              <p className="text-dark-300 mt-2">Tokens have been added to your wallet</p>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Battle Summary</h3>
              <p className="text-dark-300">
                Duration: {battleResult.duration} seconds
              </p>
            </div>

            <div className="flex space-x-4 justify-center">
              <button
                onClick={() => {
                  setBattleResult(null);
                  setSelectedHeroes([]);
                }}
                className="game-button"
              >
                Battle Again
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="game-button-secondary"
              >
                Return to Dashboard
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Battle;
