{"ast": null, "code": "/**\n *  All errors in ethers include properties to ensure they are both\n *  human-readable (i.e. ``.message``) and machine-readable (i.e. ``.code``).\n *\n *  The [[isError]] function can be used to check the error ``code`` and\n *  provide a type guard for the properties present on that error interface.\n *\n *  @_section: api/utils/errors:Errors  [about-errors]\n */\nimport { version } from \"../_version.js\";\nimport { defineProperties } from \"./properties.js\";\nfunction stringify(value, seen) {\n  if (value == null) {\n    return \"null\";\n  }\n  if (seen == null) {\n    seen = new Set();\n  }\n  if (typeof value === \"object\") {\n    if (seen.has(value)) {\n      return \"[Circular]\";\n    }\n    seen.add(value);\n  }\n  if (Array.isArray(value)) {\n    return \"[ \" + value.map(v => stringify(v, seen)).join(\", \") + \" ]\";\n  }\n  if (value instanceof Uint8Array) {\n    const HEX = \"0123456789abcdef\";\n    let result = \"0x\";\n    for (let i = 0; i < value.length; i++) {\n      result += HEX[value[i] >> 4];\n      result += HEX[value[i] & 0xf];\n    }\n    return result;\n  }\n  if (typeof value === \"object\" && typeof value.toJSON === \"function\") {\n    return stringify(value.toJSON(), seen);\n  }\n  switch (typeof value) {\n    case \"boolean\":\n    case \"number\":\n    case \"symbol\":\n      return value.toString();\n    case \"bigint\":\n      return BigInt(value).toString();\n    case \"string\":\n      return JSON.stringify(value);\n    case \"object\":\n      {\n        const keys = Object.keys(value);\n        keys.sort();\n        return \"{ \" + keys.map(k => `${stringify(k, seen)}: ${stringify(value[k], seen)}`).join(\", \") + \" }\";\n      }\n  }\n  return `[ COULD NOT SERIALIZE ]`;\n}\n/**\n *  Returns true if the %%error%% matches an error thrown by ethers\n *  that matches the error %%code%%.\n *\n *  In TypeScript environments, this can be used to check that %%error%%\n *  matches an EthersError type, which means the expected properties will\n *  be set.\n *\n *  @See [ErrorCodes](api:ErrorCode)\n *  @example\n *    try {\n *      // code....\n *    } catch (e) {\n *      if (isError(e, \"CALL_EXCEPTION\")) {\n *          // The Type Guard has validated this object\n *          console.log(e.data);\n *      }\n *    }\n */\nexport function isError(error, code) {\n  return error && error.code === code;\n}\n/**\n *  Returns true if %%error%% is a [[CallExceptionError].\n */\nexport function isCallException(error) {\n  return isError(error, \"CALL_EXCEPTION\");\n}\n/**\n *  Returns a new Error configured to the format ethers emits errors, with\n *  the %%message%%, [[api:ErrorCode]] %%code%% and additional properties\n *  for the corresponding EthersError.\n *\n *  Each error in ethers includes the version of ethers, a\n *  machine-readable [[ErrorCode]], and depending on %%code%%, additional\n *  required properties. The error message will also include the %%message%%,\n *  ethers version, %%code%% and all additional properties, serialized.\n */\nexport function makeError(message, code, info) {\n  let shortMessage = message;\n  {\n    const details = [];\n    if (info) {\n      if (\"message\" in info || \"code\" in info || \"name\" in info) {\n        throw new Error(`value will overwrite populated values: ${stringify(info)}`);\n      }\n      for (const key in info) {\n        if (key === \"shortMessage\") {\n          continue;\n        }\n        const value = info[key];\n        //                try {\n        details.push(key + \"=\" + stringify(value));\n        //                } catch (error: any) {\n        //                console.log(\"MMM\", error.message);\n        //                    details.push(key + \"=[could not serialize object]\");\n        //                }\n      }\n    }\n    details.push(`code=${code}`);\n    details.push(`version=${version}`);\n    if (details.length) {\n      message += \" (\" + details.join(\", \") + \")\";\n    }\n  }\n  let error;\n  switch (code) {\n    case \"INVALID_ARGUMENT\":\n      error = new TypeError(message);\n      break;\n    case \"NUMERIC_FAULT\":\n    case \"BUFFER_OVERRUN\":\n      error = new RangeError(message);\n      break;\n    default:\n      error = new Error(message);\n  }\n  defineProperties(error, {\n    code\n  });\n  if (info) {\n    Object.assign(error, info);\n  }\n  if (error.shortMessage == null) {\n    defineProperties(error, {\n      shortMessage\n    });\n  }\n  return error;\n}\n/**\n *  Throws an EthersError with %%message%%, %%code%% and additional error\n *  %%info%% when %%check%% is falsish..\n *\n *  @see [[api:makeError]]\n */\nexport function assert(check, message, code, info) {\n  if (!check) {\n    throw makeError(message, code, info);\n  }\n}\n/**\n *  A simple helper to simply ensuring provided arguments match expected\n *  constraints, throwing if not.\n *\n *  In TypeScript environments, the %%check%% has been asserted true, so\n *  any further code does not need additional compile-time checks.\n */\nexport function assertArgument(check, message, name, value) {\n  assert(check, message, \"INVALID_ARGUMENT\", {\n    argument: name,\n    value: value\n  });\n}\nexport function assertArgumentCount(count, expectedCount, message) {\n  if (message == null) {\n    message = \"\";\n  }\n  if (message) {\n    message = \": \" + message;\n  }\n  assert(count >= expectedCount, \"missing argument\" + message, \"MISSING_ARGUMENT\", {\n    count: count,\n    expectedCount: expectedCount\n  });\n  assert(count <= expectedCount, \"too many arguments\" + message, \"UNEXPECTED_ARGUMENT\", {\n    count: count,\n    expectedCount: expectedCount\n  });\n}\nconst _normalizeForms = [\"NFD\", \"NFC\", \"NFKD\", \"NFKC\"].reduce((accum, form) => {\n  try {\n    // General test for normalize\n    /* c8 ignore start */\n    if (\"test\".normalize(form) !== \"test\") {\n      throw new Error(\"bad\");\n    }\n    ;\n    /* c8 ignore stop */\n    if (form === \"NFD\") {\n      const check = String.fromCharCode(0xe9).normalize(\"NFD\");\n      const expected = String.fromCharCode(0x65, 0x0301);\n      /* c8 ignore start */\n      if (check !== expected) {\n        throw new Error(\"broken\");\n      }\n      /* c8 ignore stop */\n    }\n    accum.push(form);\n  } catch (error) {}\n  return accum;\n}, []);\n/**\n *  Throws if the normalization %%form%% is not supported.\n */\nexport function assertNormalize(form) {\n  assert(_normalizeForms.indexOf(form) >= 0, \"platform missing String.prototype.normalize\", \"UNSUPPORTED_OPERATION\", {\n    operation: \"String.prototype.normalize\",\n    info: {\n      form\n    }\n  });\n}\n/**\n *  Many classes use file-scoped values to guard the constructor,\n *  making it effectively private. This facilitates that pattern\n *  by ensuring the %%givenGaurd%% matches the file-scoped %%guard%%,\n *  throwing if not, indicating the %%className%% if provided.\n */\nexport function assertPrivate(givenGuard, guard, className) {\n  if (className == null) {\n    className = \"\";\n  }\n  if (givenGuard !== guard) {\n    let method = className,\n      operation = \"new\";\n    if (className) {\n      method += \".\";\n      operation += \" \" + className;\n    }\n    assert(false, `private constructor; use ${method}from* methods`, \"UNSUPPORTED_OPERATION\", {\n      operation\n    });\n  }\n}", "map": {"version": 3, "names": ["version", "defineProperties", "stringify", "value", "seen", "Set", "has", "add", "Array", "isArray", "map", "v", "join", "Uint8Array", "HEX", "result", "i", "length", "toJSON", "toString", "BigInt", "JSON", "keys", "Object", "sort", "k", "isError", "error", "code", "isCallException", "makeError", "message", "info", "shortMessage", "details", "Error", "key", "push", "TypeError", "RangeError", "assign", "assert", "check", "assertArgument", "name", "argument", "assertArgumentCount", "count", "expectedCount", "_normalizeForms", "reduce", "accum", "form", "normalize", "String", "fromCharCode", "expected", "assertNormalize", "indexOf", "operation", "assertPrivate", "<PERSON><PERSON><PERSON>", "guard", "className", "method"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\errors.ts"], "sourcesContent": ["/**\n *  All errors in ethers include properties to ensure they are both\n *  human-readable (i.e. ``.message``) and machine-readable (i.e. ``.code``).\n *\n *  The [[isError]] function can be used to check the error ``code`` and\n *  provide a type guard for the properties present on that error interface.\n *\n *  @_section: api/utils/errors:Errors  [about-errors]\n */\n\nimport { version } from \"../_version.js\";\n\nimport { defineProperties } from \"./properties.js\";\n\nimport type {\n    TransactionRequest, TransactionReceipt, TransactionResponse\n} from \"../providers/index.js\";\n\nimport type { FetchRequest, FetchResponse } from \"./fetch.js\";\n\n/**\n *  An error may contain additional properties, but those must not\n *  conflict with any implicit properties.\n */\nexport type ErrorInfo<T> = Omit<T, \"code\" | \"name\" | \"message\" | \"shortMessage\"> & { shortMessage?: string };\n\n\nfunction stringify(value: any, seen?: Set<any>): any {\n    if (value == null) { return \"null\"; }\n\n    if (seen == null) { seen = new Set(); }\n    if (typeof(value) === \"object\") {\n        if (seen.has(value)) { return \"[Circular]\"; }\n        seen.add(value);\n    }\n\n    if (Array.isArray(value)) {\n        return \"[ \" + (value.map((v) => stringify(v, seen))).join(\", \") + \" ]\";\n    }\n\n    if (value instanceof Uint8Array) {\n        const HEX = \"0123456789abcdef\";\n        let result = \"0x\";\n        for (let i = 0; i < value.length; i++) {\n            result += HEX[value[i] >> 4];\n            result += HEX[value[i] & 0xf];\n        }\n        return result;\n    }\n\n    if (typeof(value) === \"object\" && typeof(value.toJSON) === \"function\") {\n        return stringify(value.toJSON(), seen);\n    }\n\n    switch (typeof(value)) {\n        case \"boolean\": case \"number\": case \"symbol\":\n            return value.toString();\n        case \"bigint\":\n            return BigInt(value).toString();\n        case \"string\":\n            return JSON.stringify(value);\n        case \"object\": {\n            const keys = Object.keys(value);\n            keys.sort();\n            return \"{ \" + keys.map((k) => `${ stringify(k, seen) }: ${ stringify(value[k], seen) }`).join(\", \") + \" }\";\n        }\n    }\n\n    return `[ COULD NOT SERIALIZE ]`;\n}\n\n/**\n *  All errors emitted by ethers have an **ErrorCode** to help\n *  identify and coalesce errors to simplify programmatic analysis.\n *\n *  Each **ErrorCode** is the %%code%% proerty of a coresponding\n *  [[EthersError]].\n *\n *  **Generic Errors**\n *\n *  **``\"UNKNOWN_ERROR\"``** - see [[UnknownError]]\n *\n *  **``\"NOT_IMPLEMENTED\"``** - see [[NotImplementedError]]\n *\n *  **``\"UNSUPPORTED_OPERATION\"``** - see [[UnsupportedOperationError]]\n *\n *  **``\"NETWORK_ERROR\"``** - see [[NetworkError]]\n *\n *  **``\"SERVER_ERROR\"``** - see [[ServerError]]\n *\n *  **``\"TIMEOUT\"``** - see [[TimeoutError]]\n *\n *  **``\"BAD_DATA\"``** - see [[BadDataError]]\n *\n *  **``\"CANCELLED\"``** - see [[CancelledError]]\n *\n *  **Operational Errors**\n *\n *  **``\"BUFFER_OVERRUN\"``** - see [[BufferOverrunError]]\n *\n *  **``\"NUMERIC_FAULT\"``** - see [[NumericFaultError]]\n *\n *  **Argument Errors**\n *\n *  **``\"INVALID_ARGUMENT\"``** - see [[InvalidArgumentError]]\n *\n *  **``\"MISSING_ARGUMENT\"``** - see [[MissingArgumentError]]\n *\n *  **``\"UNEXPECTED_ARGUMENT\"``** - see [[UnexpectedArgumentError]]\n *\n *  **``\"VALUE_MISMATCH\"``** - //unused//\n *\n *  **Blockchain Errors**\n *\n *  **``\"CALL_EXCEPTION\"``** - see [[CallExceptionError]]\n *\n *  **``\"INSUFFICIENT_FUNDS\"``** - see [[InsufficientFundsError]]\n *\n *  **``\"NONCE_EXPIRED\"``** - see [[NonceExpiredError]]\n *\n *  **``\"REPLACEMENT_UNDERPRICED\"``** - see [[ReplacementUnderpricedError]]\n *\n *  **``\"TRANSACTION_REPLACED\"``** - see [[TransactionReplacedError]]\n *\n *  **``\"UNCONFIGURED_NAME\"``** - see [[UnconfiguredNameError]]\n *\n *  **``\"OFFCHAIN_FAULT\"``** - see [[OffchainFaultError]]\n *\n *  **User Interaction Errors**\n *\n *  **``\"ACTION_REJECTED\"``** - see [[ActionRejectedError]]\n */\nexport type ErrorCode =\n\n    // Generic Errors\n    \"UNKNOWN_ERROR\" | \"NOT_IMPLEMENTED\" | \"UNSUPPORTED_OPERATION\" |\n    \"NETWORK_ERROR\" | \"SERVER_ERROR\" | \"TIMEOUT\" | \"BAD_DATA\" |\n    \"CANCELLED\" |\n\n    // Operational Errors\n    \"BUFFER_OVERRUN\" |  \"NUMERIC_FAULT\" |\n\n    // Argument Errors\n    \"INVALID_ARGUMENT\" | \"MISSING_ARGUMENT\" | \"UNEXPECTED_ARGUMENT\" |\n    \"VALUE_MISMATCH\" |\n\n    // Blockchain Errors\n    \"CALL_EXCEPTION\" | \"INSUFFICIENT_FUNDS\" | \"NONCE_EXPIRED\" |\n    \"REPLACEMENT_UNDERPRICED\" | \"TRANSACTION_REPLACED\" |\n    \"UNCONFIGURED_NAME\" | \"OFFCHAIN_FAULT\" |\n\n    // User Interaction\n    \"ACTION_REJECTED\"\n;\n\n/**\n *  All errors in Ethers include properties to assist in\n *  machine-readable errors.\n */\nexport interface EthersError<T extends ErrorCode = ErrorCode> extends Error {\n    /**\n     *  The string error code.\n     */\n    code: ErrorCode;\n\n    /**\n     *  A short message describing the error, with minimal additional\n     *  details.\n     */\n    shortMessage: string;\n\n    /**\n     *  Additional info regarding the error that may be useful.\n     *\n     *  This is generally helpful mostly for human-based debugging.\n     */\n    info?: Record<string, any>;\n\n    /**\n     *  Any related error.\n     */\n    error?: Error;\n}\n\n// Generic Errors\n\n/**\n *  This Error is a catch-all for when there is no way for Ethers to\n *  know what the underlying problem is.\n */\nexport interface UnknownError extends EthersError<\"UNKNOWN_ERROR\"> {\n    [ key: string ]: any;\n}\n\n/**\n *  This Error is mostly used as a stub for functionality that is\n *  intended for the future, but is currently not implemented.\n */\nexport interface NotImplementedError extends EthersError<\"NOT_IMPLEMENTED\"> {\n    /**\n     *  The attempted operation.\n     */\n    operation: string;\n}\n\n/**\n *  This Error indicates that the attempted operation is not supported.\n *\n *  This could range from a specific JSON-RPC end-point not supporting\n *  a feature to a specific configuration of an object prohibiting the\n *  operation.\n *\n *  For example, a [[Wallet]] with no connected [[Provider]] is unable\n *  to send a transaction.\n */\nexport interface UnsupportedOperationError extends EthersError<\"UNSUPPORTED_OPERATION\"> {\n    /**\n     *  The attempted operation.\n     */\n    operation: string;\n}\n\n/**\n *  This Error indicates a problem connecting to a network.\n */\nexport interface NetworkError extends EthersError<\"NETWORK_ERROR\"> {\n    /**\n     *  The network event.\n     */\n    event: string;\n}\n\n/**\n *  This Error indicates there was a problem fetching a resource from\n *  a server.\n */\nexport interface ServerError extends EthersError<\"SERVER_ERROR\"> {\n    /**\n     *  The requested resource.\n     */\n    request: FetchRequest | string;\n\n    /**\n     *  The response received from the server, if available.\n     */\n    response?: FetchResponse;\n}\n\n/**\n *  This Error indicates that the timeout duration has expired and\n *  that the operation has been implicitly cancelled.\n *\n *  The side-effect of the operation may still occur, as this\n *  generally means a request has been sent and there has simply\n *  been no response to indicate whether it was processed or not.\n */\nexport interface TimeoutError extends EthersError<\"TIMEOUT\"> {\n    /**\n     *  The attempted operation.\n     */\n    operation: string;\n\n    /**\n     *  The reason.\n     */\n    reason: string;\n\n    /**\n     *  The resource request, if available.\n     */\n    request?: FetchRequest;\n}\n\n/**\n *  This Error indicates that a provided set of data cannot\n *  be correctly interpreted.\n */\nexport interface BadDataError extends EthersError<\"BAD_DATA\"> {\n    /**\n     *  The data.\n     */\n    value: any;\n}\n\n/**\n *  This Error indicates that the operation was cancelled by a\n *  programmatic call, for example to ``cancel()``.\n */\nexport interface CancelledError extends EthersError<\"CANCELLED\"> {\n}\n\n\n// Operational Errors\n\n/**\n *  This Error indicates an attempt was made to read outside the bounds\n *  of protected data.\n *\n *  Most operations in Ethers are protected by bounds checks, to mitigate\n *  exploits when parsing data.\n */\nexport interface BufferOverrunError extends EthersError<\"BUFFER_OVERRUN\"> {\n    /**\n     *  The buffer that was overrun.\n     */\n    buffer: Uint8Array;\n\n    /**\n     *  The length of the buffer.\n     */\n    length: number;\n\n    /**\n     *  The offset that was requested.\n     */\n    offset: number;\n}\n\n/**\n *  This Error indicates an operation which would result in incorrect\n *  arithmetic output has occurred.\n *\n *  For example, trying to divide by zero or using a ``uint8`` to store\n *  a negative value.\n */\nexport interface NumericFaultError extends EthersError<\"NUMERIC_FAULT\"> {\n    /**\n     *  The attempted operation.\n     */\n    operation: string;\n\n    /**\n     *  The fault reported.\n     */\n    fault: string;\n\n    /**\n     *  The value the operation was attempted against.\n     */\n    value: any;\n}\n\n\n// Argument Errors\n\n/**\n *  This Error indicates an incorrect type or value was passed to\n *  a function or method.\n */\nexport interface InvalidArgumentError extends EthersError<\"INVALID_ARGUMENT\"> {\n    /**\n     *  The name of the argument.\n     */\n    argument: string;\n\n    /**\n     *  The value that was provided.\n     */\n    value: any;\n\n    info?: Record<string, any>\n}\n\n/**\n *  This Error indicates there were too few arguments were provided.\n */\nexport interface MissingArgumentError extends EthersError<\"MISSING_ARGUMENT\"> {\n    /**\n     *  The number of arguments received.\n     */\n    count: number;\n\n    /**\n     *  The number of arguments expected.\n     */\n    expectedCount: number;\n}\n\n/**\n *  This Error indicates too many arguments were provided.\n */\nexport interface UnexpectedArgumentError extends EthersError<\"UNEXPECTED_ARGUMENT\"> {\n    /**\n     *  The number of arguments received.\n     */\n    count: number;\n\n    /**\n     *  The number of arguments expected.\n     */\n    expectedCount: number;\n}\n\n\n// Blockchain Errors\n\n/**\n *  The action that resulted in the call exception.\n */\nexport type CallExceptionAction = \"call\" | \"estimateGas\" | \"getTransactionResult\" | \"sendTransaction\" | \"unknown\";\n\n/**\n *  The related transaction that caused the error.\n */\nexport type CallExceptionTransaction = {\n    to: null | string;\n    from?: string;\n    data: string;\n};\n\n/**\n *  This **Error** indicates a transaction reverted.\n */\nexport interface CallExceptionError extends EthersError<\"CALL_EXCEPTION\"> {\n\n    /**\n     *  The action being performed when the revert was encountered.\n     */\n    action: CallExceptionAction;\n\n    /**\n     *  The revert data returned.\n     */\n    data: null | string;\n\n    /**\n     *  A human-readable representation of data, if possible.\n     */\n    reason: null | string;\n\n    /**\n     *  The transaction that triggered the exception.\n     */\n    transaction: CallExceptionTransaction,\n\n    /**\n     *  The contract invocation details, if available.\n     */\n    invocation: null | {\n        method: string;\n        signature: string;\n        args: Array<any>;\n    }\n\n    /**\n     *  The built-in or custom revert error, if available\n     */\n    revert: null | {\n        signature: string;\n        name: string;\n        args: Array<any>;\n    }\n\n    /**\n     *  If the error occurred in a transaction that was mined\n     *  (with a status of ``0``), this is the receipt.\n     */\n    receipt?: TransactionReceipt;   // @TODO: in v7, make this `null | TransactionReceipt`\n}\n\n\n/**\n *  The sending account has insufficient funds to cover the\n *  entire transaction cost.\n */\nexport interface InsufficientFundsError extends EthersError<\"INSUFFICIENT_FUNDS\"> {\n    /**\n     *  The transaction.\n     */\n    transaction: TransactionRequest;\n}\n\n/**\n *  The sending account has already used this nonce in a\n *  transaction that has been included.\n */\nexport interface NonceExpiredError extends EthersError<\"NONCE_EXPIRED\"> {\n    /**\n     *  The transaction.\n     */\n    transaction: TransactionRequest;\n}\n\n/**\n *  A CCIP-read exception, which cannot be recovered from or\n *  be further processed.\n */\nexport interface OffchainFaultError extends EthersError<\"OFFCHAIN_FAULT\"> {\n    /**\n     *  The transaction.\n     */\n    transaction?: TransactionRequest;\n\n    /**\n     *  The reason the CCIP-read failed.\n     */\n    reason: string;\n}\n\n/**\n *  An attempt was made to replace a transaction, but with an\n *  insufficient additional fee to afford evicting the old\n *  transaction from the memory pool.\n */\nexport interface ReplacementUnderpricedError extends EthersError<\"REPLACEMENT_UNDERPRICED\"> {\n    /**\n     *  The transaction.\n     */\n    transaction: TransactionRequest;\n}\n\n/**\n *  A pending transaction was replaced by another.\n */\nexport interface TransactionReplacedError extends EthersError<\"TRANSACTION_REPLACED\"> {\n    /**\n     *  If the transaction was cancelled, such that the original\n     *  effects of the transaction cannot be assured.\n     */\n    cancelled: boolean;\n\n    /**\n     *  The reason the transaction was replaced.\n     */\n    reason: \"repriced\" | \"cancelled\" | \"replaced\";\n\n    /**\n     *  The hash of the replaced transaction.\n     */\n    hash: string;\n\n    /**\n     *  The transaction that replaced the transaction.\n     */\n    replacement: TransactionResponse;\n\n    /**\n     *  The receipt of the transaction that replace the transaction.\n     */\n    receipt: TransactionReceipt;\n}\n\n/**\n *  This Error indicates an ENS name was used, but the name has not\n *  been configured.\n *\n *  This could indicate an ENS name is unowned or that the current\n *  address being pointed to is the [[ZeroAddress]].\n */\nexport interface UnconfiguredNameError extends EthersError<\"UNCONFIGURED_NAME\"> {\n    /**\n     *  The ENS name that was requested\n     */\n    value: string;\n}\n\n/**\n *  This Error indicates a request was rejected by the user.\n *\n *  In most clients (such as MetaMask), when an operation requires user\n *  authorization (such as ``signer.sendTransaction``), the client\n *  presents a dialog box to the user. If the user denies the request\n *  this error is thrown.\n */\nexport interface ActionRejectedError extends EthersError<\"ACTION_REJECTED\"> {\n    /**\n     *  The requested action.\n     */\n    action: \"requestAccess\" | \"sendTransaction\" | \"signMessage\" | \"signTransaction\" | \"signTypedData\" | \"unknown\",\n\n    /**\n     *  The reason the action was rejected.\n     *\n     *  If there is already a pending request, some clients may indicate\n     *  there is already a ``\"pending\"`` action. This prevents an app\n     *  from spamming the user.\n     */\n    reason: \"expired\" | \"rejected\" | \"pending\"\n}\n\n// Coding; converts an ErrorCode its Typed Error\n\n/**\n *  A conditional type that transforms the [[ErrorCode]] T into\n *  its EthersError type.\n *\n *  @flatworm-skip-docs\n */\nexport type CodedEthersError<T> =\n    T extends \"UNKNOWN_ERROR\" ? UnknownError:\n    T extends \"NOT_IMPLEMENTED\" ? NotImplementedError:\n    T extends \"UNSUPPORTED_OPERATION\" ? UnsupportedOperationError:\n    T extends \"NETWORK_ERROR\" ? NetworkError:\n    T extends \"SERVER_ERROR\" ? ServerError:\n    T extends \"TIMEOUT\" ? TimeoutError:\n    T extends \"BAD_DATA\" ? BadDataError:\n    T extends \"CANCELLED\" ? CancelledError:\n\n    T extends \"BUFFER_OVERRUN\" ? BufferOverrunError:\n    T extends \"NUMERIC_FAULT\" ? NumericFaultError:\n\n    T extends \"INVALID_ARGUMENT\" ? InvalidArgumentError:\n    T extends \"MISSING_ARGUMENT\" ? MissingArgumentError:\n    T extends \"UNEXPECTED_ARGUMENT\" ? UnexpectedArgumentError:\n\n    T extends \"CALL_EXCEPTION\" ? CallExceptionError:\n    T extends \"INSUFFICIENT_FUNDS\" ? InsufficientFundsError:\n    T extends \"NONCE_EXPIRED\" ? NonceExpiredError:\n    T extends \"OFFCHAIN_FAULT\" ? OffchainFaultError:\n    T extends \"REPLACEMENT_UNDERPRICED\" ? ReplacementUnderpricedError:\n    T extends \"TRANSACTION_REPLACED\" ? TransactionReplacedError:\n    T extends \"UNCONFIGURED_NAME\" ? UnconfiguredNameError:\n\n    T extends \"ACTION_REJECTED\" ? ActionRejectedError:\n\n    never;\n\n\n\n/**\n *  Returns true if the %%error%% matches an error thrown by ethers\n *  that matches the error %%code%%.\n *\n *  In TypeScript environments, this can be used to check that %%error%%\n *  matches an EthersError type, which means the expected properties will\n *  be set.\n *\n *  @See [ErrorCodes](api:ErrorCode)\n *  @example\n *    try {\n *      // code....\n *    } catch (e) {\n *      if (isError(e, \"CALL_EXCEPTION\")) {\n *          // The Type Guard has validated this object\n *          console.log(e.data);\n *      }\n *    }\n */\nexport function isError<K extends ErrorCode, T extends CodedEthersError<K>>(error: any, code: K): error is T {\n    return (error && (<EthersError>error).code === code);\n}\n\n/**\n *  Returns true if %%error%% is a [[CallExceptionError].\n */\nexport function isCallException(error: any): error is CallExceptionError {\n    return isError(error, \"CALL_EXCEPTION\");\n}\n\n/**\n *  Returns a new Error configured to the format ethers emits errors, with\n *  the %%message%%, [[api:ErrorCode]] %%code%% and additional properties\n *  for the corresponding EthersError.\n *\n *  Each error in ethers includes the version of ethers, a\n *  machine-readable [[ErrorCode]], and depending on %%code%%, additional\n *  required properties. The error message will also include the %%message%%,\n *  ethers version, %%code%% and all additional properties, serialized.\n */\nexport function makeError<K extends ErrorCode, T extends CodedEthersError<K>>(message: string, code: K, info?: ErrorInfo<T>): T {\n    let shortMessage = message;\n\n    {\n        const details: Array<string> = [];\n        if (info) {\n            if (\"message\" in info || \"code\" in info || \"name\" in info) {\n                throw new Error(`value will overwrite populated values: ${ stringify(info) }`);\n            }\n            for (const key in info) {\n                if (key === \"shortMessage\") { continue; }\n                const value = <any>(info[<keyof ErrorInfo<T>>key]);\n//                try {\n                    details.push(key + \"=\" + stringify(value));\n//                } catch (error: any) {\n//                console.log(\"MMM\", error.message);\n//                    details.push(key + \"=[could not serialize object]\");\n//                }\n            }\n        }\n        details.push(`code=${ code }`);\n        details.push(`version=${ version }`);\n\n        if (details.length) {\n            message += \" (\" + details.join(\", \") + \")\";\n        }\n    }\n\n    let error;\n    switch (code) {\n        case \"INVALID_ARGUMENT\":\n            error = new TypeError(message);\n            break;\n        case \"NUMERIC_FAULT\":\n        case \"BUFFER_OVERRUN\":\n            error = new RangeError(message);\n            break;\n        default:\n            error = new Error(message);\n    }\n\n    defineProperties<EthersError>(<EthersError>error, { code });\n\n    if (info) { Object.assign(error, info); }\n\n    if ((<any>error).shortMessage == null) {\n        defineProperties<EthersError>(<EthersError>error, { shortMessage });\n    }\n\n    return <T>error;\n}\n\n/**\n *  Throws an EthersError with %%message%%, %%code%% and additional error\n *  %%info%% when %%check%% is falsish..\n *\n *  @see [[api:makeError]]\n */\nexport function assert<K extends ErrorCode, T extends CodedEthersError<K>>(check: unknown, message: string, code: K, info?: ErrorInfo<T>): asserts check {\n    if (!check) { throw makeError(message, code, info); }\n}\n\n\n/**\n *  A simple helper to simply ensuring provided arguments match expected\n *  constraints, throwing if not.\n *\n *  In TypeScript environments, the %%check%% has been asserted true, so\n *  any further code does not need additional compile-time checks.\n */\nexport function assertArgument(check: unknown, message: string, name: string, value: unknown): asserts check {\n    assert(check, message, \"INVALID_ARGUMENT\", { argument: name, value: value });\n}\n\nexport function assertArgumentCount(count: number, expectedCount: number, message?: string): void {\n    if (message == null) { message = \"\"; }\n    if (message) { message = \": \" + message; }\n\n    assert(count >= expectedCount, \"missing argument\" + message, \"MISSING_ARGUMENT\", {\n        count: count,\n        expectedCount: expectedCount\n    });\n\n    assert(count <= expectedCount, \"too many arguments\" + message, \"UNEXPECTED_ARGUMENT\", {\n        count: count,\n        expectedCount: expectedCount\n    });\n}\n\nconst _normalizeForms = [\"NFD\", \"NFC\", \"NFKD\", \"NFKC\"].reduce((accum, form) => {\n    try {\n        // General test for normalize\n        /* c8 ignore start */\n        if (\"test\".normalize(form) !== \"test\") { throw new Error(\"bad\"); };\n        /* c8 ignore stop */\n\n        if (form === \"NFD\") {\n            const check = String.fromCharCode(0xe9).normalize(\"NFD\");\n            const expected = String.fromCharCode(0x65, 0x0301)\n            /* c8 ignore start */\n            if (check !== expected) { throw new Error(\"broken\") }\n            /* c8 ignore stop */\n        }\n\n        accum.push(form);\n    } catch(error) { }\n\n    return accum;\n}, <Array<string>>[]);\n\n/**\n *  Throws if the normalization %%form%% is not supported.\n */\nexport function assertNormalize(form: string): void {\n    assert(_normalizeForms.indexOf(form) >= 0, \"platform missing String.prototype.normalize\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"String.prototype.normalize\", info: { form }\n    });\n}\n\n/**\n *  Many classes use file-scoped values to guard the constructor,\n *  making it effectively private. This facilitates that pattern\n *  by ensuring the %%givenGaurd%% matches the file-scoped %%guard%%,\n *  throwing if not, indicating the %%className%% if provided.\n */\nexport function assertPrivate(givenGuard: any, guard: any, className?: string): void {\n    if (className == null) { className = \"\"; }\n    if (givenGuard !== guard) {\n        let method = className, operation = \"new\";\n        if (className) {\n            method += \".\";\n            operation += \" \" + className;\n        }\n        assert(false, `private constructor; use ${ method }from* methods`, \"UNSUPPORTED_OPERATION\", {\n            operation\n        });\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;AAUA,SAASA,OAAO,QAAQ,gBAAgB;AAExC,SAASC,gBAAgB,QAAQ,iBAAiB;AAelD,SAASC,SAASA,CAACC,KAAU,EAAEC,IAAe;EAC1C,IAAID,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,MAAM;;EAElC,IAAIC,IAAI,IAAI,IAAI,EAAE;IAAEA,IAAI,GAAG,IAAIC,GAAG,EAAE;;EACpC,IAAI,OAAOF,KAAM,KAAK,QAAQ,EAAE;IAC5B,IAAIC,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC,EAAE;MAAE,OAAO,YAAY;;IAC1CC,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC;;EAGnB,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI,GAAIA,KAAK,CAACO,GAAG,CAAEC,CAAC,IAAKT,SAAS,CAACS,CAAC,EAAEP,IAAI,CAAC,CAAC,CAAEQ,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;;EAG1E,IAAIT,KAAK,YAAYU,UAAU,EAAE;IAC7B,MAAMC,GAAG,GAAG,kBAAkB;IAC9B,IAAIC,MAAM,GAAG,IAAI;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,KAAK,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;MACnCD,MAAM,IAAID,GAAG,CAACX,KAAK,CAACa,CAAC,CAAC,IAAI,CAAC,CAAC;MAC5BD,MAAM,IAAID,GAAG,CAACX,KAAK,CAACa,CAAC,CAAC,GAAG,GAAG,CAAC;;IAEjC,OAAOD,MAAM;;EAGjB,IAAI,OAAOZ,KAAM,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACe,MAAO,KAAK,UAAU,EAAE;IACnE,OAAOhB,SAAS,CAACC,KAAK,CAACe,MAAM,EAAE,EAAEd,IAAI,CAAC;;EAG1C,QAAQ,OAAOD,KAAM;IACjB,KAAK,SAAS;IAAE,KAAK,QAAQ;IAAE,KAAK,QAAQ;MACxC,OAAOA,KAAK,CAACgB,QAAQ,EAAE;IAC3B,KAAK,QAAQ;MACT,OAAOC,MAAM,CAACjB,KAAK,CAAC,CAACgB,QAAQ,EAAE;IACnC,KAAK,QAAQ;MACT,OAAOE,IAAI,CAACnB,SAAS,CAACC,KAAK,CAAC;IAChC,KAAK,QAAQ;MAAE;QACX,MAAMmB,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACnB,KAAK,CAAC;QAC/BmB,IAAI,CAACE,IAAI,EAAE;QACX,OAAO,IAAI,GAAGF,IAAI,CAACZ,GAAG,CAAEe,CAAC,IAAK,GAAIvB,SAAS,CAACuB,CAAC,EAAErB,IAAI,CAAE,KAAMF,SAAS,CAACC,KAAK,CAACsB,CAAC,CAAC,EAAErB,IAAI,CAAE,EAAE,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;;;EAIlH,OAAO,yBAAyB;AACpC;AAsiBA;;;;;;;;;;;;;;;;;;;AAmBA,OAAM,SAAUc,OAAOA,CAAqDC,KAAU,EAAEC,IAAO;EAC3F,OAAQD,KAAK,IAAkBA,KAAM,CAACC,IAAI,KAAKA,IAAI;AACvD;AAEA;;;AAGA,OAAM,SAAUC,eAAeA,CAACF,KAAU;EACtC,OAAOD,OAAO,CAACC,KAAK,EAAE,gBAAgB,CAAC;AAC3C;AAEA;;;;;;;;;;AAUA,OAAM,SAAUG,SAASA,CAAqDC,OAAe,EAAEH,IAAO,EAAEI,IAAmB;EACvH,IAAIC,YAAY,GAAGF,OAAO;EAE1B;IACI,MAAMG,OAAO,GAAkB,EAAE;IACjC,IAAIF,IAAI,EAAE;MACN,IAAI,SAAS,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,EAAE;QACvD,MAAM,IAAIG,KAAK,CAAC,0CAA2CjC,SAAS,CAAC8B,IAAI,CAAE,EAAE,CAAC;;MAElF,KAAK,MAAMI,GAAG,IAAIJ,IAAI,EAAE;QACpB,IAAII,GAAG,KAAK,cAAc,EAAE;UAAE;;QAC9B,MAAMjC,KAAK,GAAS6B,IAAI,CAAqBI,GAAG,CAAE;QAClE;QACoBF,OAAO,CAACG,IAAI,CAACD,GAAG,GAAG,GAAG,GAAGlC,SAAS,CAACC,KAAK,CAAC,CAAC;QAC9D;QACA;QACA;QACA;;;IAGQ+B,OAAO,CAACG,IAAI,CAAC,QAAST,IAAK,EAAE,CAAC;IAC9BM,OAAO,CAACG,IAAI,CAAC,WAAYrC,OAAQ,EAAE,CAAC;IAEpC,IAAIkC,OAAO,CAACjB,MAAM,EAAE;MAChBc,OAAO,IAAI,IAAI,GAAGG,OAAO,CAACtB,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;;;EAIlD,IAAIe,KAAK;EACT,QAAQC,IAAI;IACR,KAAK,kBAAkB;MACnBD,KAAK,GAAG,IAAIW,SAAS,CAACP,OAAO,CAAC;MAC9B;IACJ,KAAK,eAAe;IACpB,KAAK,gBAAgB;MACjBJ,KAAK,GAAG,IAAIY,UAAU,CAACR,OAAO,CAAC;MAC/B;IACJ;MACIJ,KAAK,GAAG,IAAIQ,KAAK,CAACJ,OAAO,CAAC;;EAGlC9B,gBAAgB,CAA2B0B,KAAK,EAAE;IAAEC;EAAI,CAAE,CAAC;EAE3D,IAAII,IAAI,EAAE;IAAET,MAAM,CAACiB,MAAM,CAACb,KAAK,EAAEK,IAAI,CAAC;;EAEtC,IAAUL,KAAM,CAACM,YAAY,IAAI,IAAI,EAAE;IACnChC,gBAAgB,CAA2B0B,KAAK,EAAE;MAAEM;IAAY,CAAE,CAAC;;EAGvE,OAAUN,KAAK;AACnB;AAEA;;;;;;AAMA,OAAM,SAAUc,MAAMA,CAAqDC,KAAc,EAAEX,OAAe,EAAEH,IAAO,EAAEI,IAAmB;EACpI,IAAI,CAACU,KAAK,EAAE;IAAE,MAAMZ,SAAS,CAACC,OAAO,EAAEH,IAAI,EAAEI,IAAI,CAAC;;AACtD;AAGA;;;;;;;AAOA,OAAM,SAAUW,cAAcA,CAACD,KAAc,EAAEX,OAAe,EAAEa,IAAY,EAAEzC,KAAc;EACxFsC,MAAM,CAACC,KAAK,EAAEX,OAAO,EAAE,kBAAkB,EAAE;IAAEc,QAAQ,EAAED,IAAI;IAAEzC,KAAK,EAAEA;EAAK,CAAE,CAAC;AAChF;AAEA,OAAM,SAAU2C,mBAAmBA,CAACC,KAAa,EAAEC,aAAqB,EAAEjB,OAAgB;EACtF,IAAIA,OAAO,IAAI,IAAI,EAAE;IAAEA,OAAO,GAAG,EAAE;;EACnC,IAAIA,OAAO,EAAE;IAAEA,OAAO,GAAG,IAAI,GAAGA,OAAO;;EAEvCU,MAAM,CAACM,KAAK,IAAIC,aAAa,EAAE,kBAAkB,GAAGjB,OAAO,EAAE,kBAAkB,EAAE;IAC7EgB,KAAK,EAAEA,KAAK;IACZC,aAAa,EAAEA;GAClB,CAAC;EAEFP,MAAM,CAACM,KAAK,IAAIC,aAAa,EAAE,oBAAoB,GAAGjB,OAAO,EAAE,qBAAqB,EAAE;IAClFgB,KAAK,EAAEA,KAAK;IACZC,aAAa,EAAEA;GAClB,CAAC;AACN;AAEA,MAAMC,eAAe,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAI;EAC1E,IAAI;IACA;IACA;IACA,IAAI,MAAM,CAACC,SAAS,CAACD,IAAI,CAAC,KAAK,MAAM,EAAE;MAAE,MAAM,IAAIjB,KAAK,CAAC,KAAK,CAAC;;IAAG;IAClE;IAEA,IAAIiB,IAAI,KAAK,KAAK,EAAE;MAChB,MAAMV,KAAK,GAAGY,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC,CAACF,SAAS,CAAC,KAAK,CAAC;MACxD,MAAMG,QAAQ,GAAGF,MAAM,CAACC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC;MAClD;MACA,IAAIb,KAAK,KAAKc,QAAQ,EAAE;QAAE,MAAM,IAAIrB,KAAK,CAAC,QAAQ,CAAC;;MACnD;;IAGJgB,KAAK,CAACd,IAAI,CAACe,IAAI,CAAC;GACnB,CAAC,OAAMzB,KAAK,EAAE;EAEf,OAAOwB,KAAK;AAChB,CAAC,EAAiB,EAAE,CAAC;AAErB;;;AAGA,OAAM,SAAUM,eAAeA,CAACL,IAAY;EACxCX,MAAM,CAACQ,eAAe,CAACS,OAAO,CAACN,IAAI,CAAC,IAAI,CAAC,EAAE,6CAA6C,EAAE,uBAAuB,EAAE;IAC/GO,SAAS,EAAE,4BAA4B;IAAE3B,IAAI,EAAE;MAAEoB;IAAI;GACxD,CAAC;AACN;AAEA;;;;;;AAMA,OAAM,SAAUQ,aAAaA,CAACC,UAAe,EAAEC,KAAU,EAAEC,SAAkB;EACzE,IAAIA,SAAS,IAAI,IAAI,EAAE;IAAEA,SAAS,GAAG,EAAE;;EACvC,IAAIF,UAAU,KAAKC,KAAK,EAAE;IACtB,IAAIE,MAAM,GAAGD,SAAS;MAAEJ,SAAS,GAAG,KAAK;IACzC,IAAII,SAAS,EAAE;MACXC,MAAM,IAAI,GAAG;MACbL,SAAS,IAAI,GAAG,GAAGI,SAAS;;IAEhCtB,MAAM,CAAC,KAAK,EAAE,4BAA6BuB,MAAO,eAAe,EAAE,uBAAuB,EAAE;MACxFL;KACH,CAAC;;AAEV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}