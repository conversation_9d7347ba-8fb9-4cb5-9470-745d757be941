{"ast": null, "code": "/**\n *  Some common constants useful for Ethereum.\n *\n *  @_section: api/constants: Constants  [about-constants]\n */\nexport { ZeroAddress } from \"./addresses.js\";\nexport { ZeroHash } from \"./hashes.js\";\nexport { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MaxUint256, MinInt256, <PERSON>Int256 } from \"./numbers.js\";\nexport { EtherSymbol, MessagePrefix } from \"./strings.js\";", "map": {"version": 3, "names": ["ZeroAddress", "ZeroHash", "N", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MaxUint256", "MinInt256", "MaxInt256", "EtherSymbol", "MessagePrefix"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\constants\\index.ts"], "sourcesContent": ["/**\n *  Some common constants useful for Ethereum.\n *\n *  @_section: api/constants: Constants  [about-constants]\n */\n\nexport { ZeroAddress } from \"./addresses.js\";\nexport { ZeroHash } from \"./hashes.js\";\nexport {\n    <PERSON>,\n    <PERSON><PERSON><PERSON><PERSON><PERSON>,\n    MaxUint256,\n    MinInt256,\n    <PERSON>Int256\n} from \"./numbers.js\";\nexport { EtherSymbol, MessagePrefix } from \"./strings.js\";\n"], "mappings": "AAAA;;;;;AAMA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,QAAQ,QAAQ,aAAa;AACtC,SACIC,CAAC,EACDC,WAAW,EACXC,UAAU,EACVC,SAAS,EACTC,SAAS,QACN,cAAc;AACrB,SAASC,WAAW,EAAEC,aAAa,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}