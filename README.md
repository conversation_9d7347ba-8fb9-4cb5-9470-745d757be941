# CryptoQuest - Web3 Play-to-Earn Battle Game

🎮 **CryptoQuest** is a Web3 Play-to-Earn strategy battle game where players earn $CQT tokens through turn-based PvE and PvP battles.

## 🚀 Features

- **Turn-based Combat**: Strategic 3v3 hero battles
- **Earn $CQT Tokens**: Real cryptocurrency rewards for victories
- **Multiple Game Modes**: Adventure (PvE), Arena (PvP), Tournaments
- **Hero Collection**: Unique heroes with upgradeable stats
- **Staking System**: Stake $CQT for bonuses and rewards
- **Web3 Integration**: MetaMask wallet connection on Polygon network

## 🛠️ Tech Stack

- **Frontend**: React + Tailwind CSS + Phaser.js
- **Backend**: Node.js + Express + MySQL
- **Blockchain**: Solidity + Hardhat + Polygon
- **Token**: $CQT (ERC-20)

## 📦 Installation

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up MySQL database (see backend/README.md)
4. Deploy smart contracts: `npm run compile && npm run deploy`
5. Start development: `npm run dev`

## 🎯 Game Modes

### Adventure Mode (PvE)
- Battle AI-controlled monsters
- Increasing difficulty levels
- Earn XP and $CQT tokens

### Arena Mode (PvP)
- Ranked 3v3 battles
- Weekly prize pools
- Leaderboard competition

### Tournaments
- Seasonal competitions
- Entry fees in $CQT
- Big reward payouts

## 💰 $CQT Token

- **Type**: ERC-20 on Polygon
- **Total Supply**: 1 billion tokens
- **Use Cases**: Hero upgrades, tournament entries, staking
- **Earning**: Win battles, complete quests, tournament rewards

## 🏗️ Project Structure

```
cryptoquest/
├── contracts/          # Smart contracts (Solidity)
├── backend/           # Node.js API server
├── frontend/          # React application
├── database/          # MySQL schema
└── docs/             # Documentation
```

## 🎮 Getting Started

1. Connect your MetaMask wallet
2. Choose your starting heroes
3. Battle monsters in Adventure Mode
4. Earn $CQT tokens and upgrade your heroes
5. Compete in Arena and Tournaments

## 📄 License

MIT License - see LICENSE file for details
