import React from 'react';
import { TrophyIcon, ClockIcon } from '@heroicons/react/24/outline';

const Tournaments = () => {
  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">Tournaments</h1>
        <p className="text-dark-300">
          Competitive tournaments coming soon!
        </p>
      </div>

      <div className="game-card p-12 text-center">
        <TrophyIcon className="w-20 h-20 text-dark-400 mx-auto mb-6" />
        <h2 className="text-2xl font-bold text-white mb-4">Tournaments Under Development</h2>
        <p className="text-dark-300 mb-6 max-w-md mx-auto">
          Seasonal tournaments with entry fees and big prize pools are being developed. 
          Compete against the best players for massive CQT rewards!
        </p>
        <div className="flex items-center justify-center space-x-2 text-yellow-400">
          <ClockIcon className="w-5 h-5" />
          <span>Coming Soon</span>
        </div>
      </div>
    </div>
  );
};

export default Tournaments;
