{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\pages\\\\Arena.js\";\nimport React from 'react';\nimport { TrophyIcon, ClockIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Arena = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl font-bold text-white mb-2\",\n        children: \"Arena Mode\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-dark-300\",\n        children: \"PvP battles coming soon!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"game-card p-12 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(TrophyIcon, {\n        className: \"w-20 h-20 text-dark-400 mx-auto mb-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-white mb-4\",\n        children: \"Arena Under Construction\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-dark-300 mb-6 max-w-md mx-auto\",\n        children: \"Player vs Player battles are being developed. Soon you'll be able to challenge other players and compete for bigger CQT rewards!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center space-x-2 text-yellow-400\",\n        children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Coming Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Arena;\nexport default Arena;\nvar _c;\n$RefreshReg$(_c, \"Arena\");", "map": {"version": 3, "names": ["React", "TrophyIcon", "ClockIcon", "jsxDEV", "_jsxDEV", "Arena", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/pages/Arena.js"], "sourcesContent": ["import React from 'react';\nimport { TrophyIcon, ClockIcon } from '@heroicons/react/24/outline';\n\nconst Arena = () => {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"text-center\">\n        <h1 className=\"text-4xl font-bold text-white mb-2\">Arena Mode</h1>\n        <p className=\"text-dark-300\">\n          PvP battles coming soon!\n        </p>\n      </div>\n\n      <div className=\"game-card p-12 text-center\">\n        <TrophyIcon className=\"w-20 h-20 text-dark-400 mx-auto mb-6\" />\n        <h2 className=\"text-2xl font-bold text-white mb-4\">Arena Under Construction</h2>\n        <p className=\"text-dark-300 mb-6 max-w-md mx-auto\">\n          Player vs Player battles are being developed. Soon you'll be able to challenge other players \n          and compete for bigger CQT rewards!\n        </p>\n        <div className=\"flex items-center justify-center space-x-2 text-yellow-400\">\n          <ClockIcon className=\"w-5 h-5\" />\n          <span>Coming Soon</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Arena;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,SAAS,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAClB,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BH,OAAA;QAAIE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClEP,OAAA;QAAGE,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENP,OAAA;MAAKE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCH,OAAA,CAACH,UAAU;QAACK,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DP,OAAA;QAAIE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChFP,OAAA;QAAGE,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAGnD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzEH,OAAA,CAACF,SAAS;UAACI,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjCP,OAAA;UAAAG,QAAA,EAAM;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAxBIP,KAAK;AA0BX,eAAeA,KAAK;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}