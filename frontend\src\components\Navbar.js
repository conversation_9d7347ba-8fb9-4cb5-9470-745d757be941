import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useWeb3 } from '../contexts/Web3Context';
import {
  HomeIcon,
  UserIcon,
  ShieldCheckIcon,
  TrophyIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const Navbar = () => {
  const { isAuthenticated, user, logout } = useAuth();
  const { getTokenBalance, isConnected } = useWeb3();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [tokenBalance, setTokenBalance] = useState('0');

  // Load token balance when connected
  React.useEffect(() => {
    const loadBalance = async () => {
      if (isConnected && user?.walletAddress) {
        const balance = await getTokenBalance(user.walletAddress);
        setTokenBalance(parseFloat(balance).toFixed(2));
      }
    };
    loadBalance();
  }, [isConnected, user?.walletAddress, getTokenBalance]);

  const navigation = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Dashboard', href: '/dashboard', icon: ChartBarIcon, protected: true },
    { name: 'Heroes', href: '/heroes', icon: ShieldCheckIcon, protected: true },
    { name: 'Battle', href: '/battle', icon: TrophyIcon, protected: true },
    { name: 'Arena', href: '/arena', icon: TrophyIcon, protected: true },
    { name: 'Tournaments', href: '/tournaments', icon: TrophyIcon, protected: true },
    { name: 'Staking', href: '/staking', icon: CurrencyDollarIcon, protected: true },
    { name: 'Leaderboard', href: '/leaderboard', icon: ChartBarIcon },
  ];

  const isActivePage = (href) => {
    return location.pathname === href;
  };

  const handleLogout = () => {
    logout();
    setMobileMenuOpen(false);
  };

  return (
    <nav className="bg-dark-900/95 backdrop-blur-sm border-b border-dark-700 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <img
                src="/logo.png"
                alt="CryptoQuest Logo"
                className="w-8 h-8 object-contain"
              />
              <span className="font-game text-xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                CryptoQuest
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {navigation.map((item) => {
              if (item.protected && !isAuthenticated) return null;

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActivePage(item.href)
                      ? 'bg-primary-600 text-white shadow-lg'
                      : 'text-dark-300 hover:text-white hover:bg-dark-800'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>

          {/* User Info & Actions */}
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                {/* Token Balance */}
                <div className="hidden sm:flex items-center space-x-2 bg-dark-800 px-3 py-1 rounded-lg">
                  <div className="w-4 h-4 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full"></div>
                  <span className="text-sm font-medium text-white">{tokenBalance} CQT</span>
                </div>

                {/* User Menu */}
                <div className="flex items-center space-x-2">
                  <div className="hidden sm:block text-right">
                    <p className="text-sm font-medium text-white">
                      {user.username || `${user.walletAddress?.slice(0, 6)}...${user.walletAddress?.slice(-4)}`}
                    </p>
                    <p className="text-xs text-dark-400">Level {user.level}</p>
                  </div>
                  <button
                    onClick={handleLogout}
                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg text-sm font-medium transition-colors"
                  >
                    Logout
                  </button>
                </div>
              </>
            ) : (
              <Link
                to="/login"
                className="game-button text-sm"
              >
                Connect Wallet
              </Link>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2 rounded-lg text-dark-400 hover:text-white hover:bg-dark-800"
            >
              {mobileMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-dark-800 border-t border-dark-700">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => {
              if (item.protected && !isAuthenticated) return null;

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setMobileMenuOpen(false)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${
                    isActivePage(item.href)
                      ? 'bg-primary-600 text-white'
                      : 'text-dark-300 hover:text-white hover:bg-dark-700'
                  }`}
                >
                  <item.icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {isAuthenticated && (
              <div className="pt-4 border-t border-dark-700">
                <div className="flex items-center space-x-2 px-3 py-2">
                  <div className="w-4 h-4 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full"></div>
                  <span className="text-sm font-medium text-white">{tokenBalance} CQT</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="w-full text-left px-3 py-2 text-red-400 hover:text-red-300 hover:bg-dark-700 rounded-lg"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
