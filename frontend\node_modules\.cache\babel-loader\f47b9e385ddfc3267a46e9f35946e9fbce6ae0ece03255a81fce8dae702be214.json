{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = a => a instanceof Uint8Array;\nconst hexes = /* @__PURE__ */Array.from({\n  length: 256\n}, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\nexport function numberToHexUnpadded(num) {\n  const hex = num.toString(16);\n  return hex.length & 1 ? `0${hex}` : hex;\n}\nexport function hexToNumber(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // Big Endian\n  return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const len = hex.length;\n  if (len % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n  const array = new Uint8Array(len / 2);\n  for (let i = 0; i < array.length; i++) {\n    const j = i * 2;\n    const hexByte = hex.slice(j, j + 2);\n    const byte = Number.parseInt(hexByte, 16);\n    if (Number.isNaN(byte) || byte < 0) throw new Error('Invalid byte sequence');\n    array[i] = byte;\n  }\n  return array;\n}\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes) {\n  return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes) {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\nexport function numberToBytesBE(n, len) {\n  return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n, len) {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n) {\n  return hexToBytes(numberToHexUnpadded(n));\n}\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title, hex, expectedLength) {\n  let res;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes(hex);\n    } catch (e) {\n      throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n    }\n  } else if (u8a(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(`${title} must be hex string or Uint8Array`);\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength) throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n  return res;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n  const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n  let pad = 0; // walk through each item, ensure they have proper type\n  arrays.forEach(a => {\n    if (!u8a(a)) throw new Error('Uint8Array expected');\n    r.set(a, pad);\n    pad += a.length;\n  });\n  return r;\n}\nexport function equalBytes(b1, b2) {\n  // We don't care about timing attacks here\n  if (b1.length !== b2.length) return false;\n  for (let i = 0; i < b1.length; i++) if (b1[i] !== b2[i]) return false;\n  return true;\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n// Bit operations\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n) {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n, pos) {\n  return n >> BigInt(pos) & _1n;\n}\n/**\n * Sets single bit at position.\n */\nexport const bitSet = (n, pos, value) => {\n  return n | (value ? _1n : _0n) << BigInt(pos);\n};\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = n => (_2n << BigInt(n - 1)) - _1n;\n// DRBG\nconst u8n = data => new Uint8Array(data); // creates Uint8Array\nconst u8fr = arr => Uint8Array.from(arr); // another shortcut\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg(hashLen, qByteLen, hmacFn) {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n()) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes(...out);\n  };\n  const genUntil = (seed, pred) => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n// Validating curves and fields\nconst validatorFns = {\n  bigint: val => typeof val === 'bigint',\n  function: val => typeof val === 'function',\n  boolean: val => typeof val === 'boolean',\n  string: val => typeof val === 'string',\n  stringOrUint8Array: val => typeof val === 'string' || val instanceof Uint8Array,\n  isSafeInteger: val => Number.isSafeInteger(val),\n  array: val => Array.isArray(val),\n  field: (val, object) => object.Fp.isValid(val),\n  hash: val => typeof val === 'function' && Number.isSafeInteger(val.outputLen)\n};\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\nexport function validateObject(object, validators, optValidators = {}) {\n  const checkField = (fieldName, type, isOptional) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function') throw new Error(`Invalid validator \"${type}\", expected function`);\n    const val = object[fieldName];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error(`Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`);\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });", "map": {"version": 3, "names": ["_0n", "BigInt", "_1n", "_2n", "u8a", "a", "Uint8Array", "hexes", "Array", "from", "length", "_", "i", "toString", "padStart", "bytesToHex", "bytes", "Error", "hex", "numberToHexUnpadded", "num", "hexToNumber", "hexToBytes", "len", "array", "j", "hexByte", "slice", "byte", "Number", "parseInt", "isNaN", "bytesToNumberBE", "bytesToNumberLE", "reverse", "numberToBytesBE", "n", "numberToBytesLE", "numberToVarBytesBE", "ensureBytes", "title", "<PERSON><PERSON><PERSON><PERSON>", "res", "e", "concatBytes", "arrays", "r", "reduce", "sum", "pad", "for<PERSON>ach", "set", "equalBytes", "b1", "b2", "utf8ToBytes", "str", "TextEncoder", "encode", "bitLen", "bitGet", "pos", "bitSet", "value", "bitMask", "u8n", "data", "u8fr", "arr", "createHmacDrbg", "hashLen", "qByteLen", "hmacFn", "v", "k", "reset", "fill", "h", "b", "reseed", "seed", "gen", "out", "sl", "push", "genUntil", "pred", "undefined", "validatorFns", "bigint", "val", "function", "boolean", "string", "stringOrUint8Array", "isSafeInteger", "isArray", "field", "object", "Fp", "<PERSON><PERSON><PERSON><PERSON>", "hash", "outputLen", "validateObject", "validators", "optValidators", "checkField", "fieldName", "type", "isOptional", "checkVal", "String", "Object", "entries"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\abstract\\utils.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// 100 lines of code in the file are duplicated from noble-hashes (utils).\n// This is OK: `abstract` directory does not use noble-hashes.\n// User may opt-in into using different hashing library. This way, noble-hashes\n// won't be included into their bundle.\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst u8a = (a: any): a is Uint8Array => a instanceof Uint8Array;\nexport type Hex = Uint8Array | string; // hex strings are accepted for simplicity\nexport type PrivKey = Hex | bigint; // bigints are accepted to ease learning curve\nexport type CHash = {\n  (message: Uint8Array | string): Uint8Array;\n  blockLen: number;\n  outputLen: number;\n  create(opts?: { dkLen?: number }): any; // For shake\n};\nexport type FHash = (message: Uint8Array | string) => Uint8Array;\n\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\nexport function numberToHexUnpadded(num: number | bigint): string {\n  const hex = num.toString(16);\n  return hex.length & 1 ? `0${hex}` : hex;\n}\n\nexport function hexToNumber(hex: string): bigint {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  // Big Endian\n  return BigInt(hex === '' ? '0' : `0x${hex}`);\n}\n\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const len = hex.length;\n  if (len % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n  const array = new Uint8Array(len / 2);\n  for (let i = 0; i < array.length; i++) {\n    const j = i * 2;\n    const hexByte = hex.slice(j, j + 2);\n    const byte = Number.parseInt(hexByte, 16);\n    if (Number.isNaN(byte) || byte < 0) throw new Error('Invalid byte sequence');\n    array[i] = byte;\n  }\n  return array;\n}\n\n// BE: Big Endian, LE: Little Endian\nexport function bytesToNumberBE(bytes: Uint8Array): bigint {\n  return hexToNumber(bytesToHex(bytes));\n}\nexport function bytesToNumberLE(bytes: Uint8Array): bigint {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  return hexToNumber(bytesToHex(Uint8Array.from(bytes).reverse()));\n}\n\nexport function numberToBytesBE(n: number | bigint, len: number): Uint8Array {\n  return hexToBytes(n.toString(16).padStart(len * 2, '0'));\n}\nexport function numberToBytesLE(n: number | bigint, len: number): Uint8Array {\n  return numberToBytesBE(n, len).reverse();\n}\n// Unpadded, rarely used\nexport function numberToVarBytesBE(n: number | bigint): Uint8Array {\n  return hexToBytes(numberToHexUnpadded(n));\n}\n\n/**\n * Takes hex string or Uint8Array, converts to Uint8Array.\n * Validates output length.\n * Will throw error for other types.\n * @param title descriptive title for an error e.g. 'private key'\n * @param hex hex string or Uint8Array\n * @param expectedLength optional, will compare to result array's length\n * @returns\n */\nexport function ensureBytes(title: string, hex: Hex, expectedLength?: number): Uint8Array {\n  let res: Uint8Array;\n  if (typeof hex === 'string') {\n    try {\n      res = hexToBytes(hex);\n    } catch (e) {\n      throw new Error(`${title} must be valid hex string, got \"${hex}\". Cause: ${e}`);\n    }\n  } else if (u8a(hex)) {\n    // Uint8Array.from() instead of hash.slice() because node.js Buffer\n    // is instance of Uint8Array, and its slice() creates **mutable** copy\n    res = Uint8Array.from(hex);\n  } else {\n    throw new Error(`${title} must be hex string or Uint8Array`);\n  }\n  const len = res.length;\n  if (typeof expectedLength === 'number' && len !== expectedLength)\n    throw new Error(`${title} expected ${expectedLength} bytes, got ${len}`);\n  return res;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n  let pad = 0; // walk through each item, ensure they have proper type\n  arrays.forEach((a) => {\n    if (!u8a(a)) throw new Error('Uint8Array expected');\n    r.set(a, pad);\n    pad += a.length;\n  });\n  return r;\n}\n\nexport function equalBytes(b1: Uint8Array, b2: Uint8Array) {\n  // We don't care about timing attacks here\n  if (b1.length !== b2.length) return false;\n  for (let i = 0; i < b1.length; i++) if (b1[i] !== b2[i]) return false;\n  return true;\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\n// Bit operations\n\n/**\n * Calculates amount of bits in a bigint.\n * Same as `n.toString(2).length`\n */\nexport function bitLen(n: bigint) {\n  let len;\n  for (len = 0; n > _0n; n >>= _1n, len += 1);\n  return len;\n}\n\n/**\n * Gets single bit at position.\n * NOTE: first bit position is 0 (same as arrays)\n * Same as `!!+Array.from(n.toString(2)).reverse()[pos]`\n */\nexport function bitGet(n: bigint, pos: number) {\n  return (n >> BigInt(pos)) & _1n;\n}\n\n/**\n * Sets single bit at position.\n */\nexport const bitSet = (n: bigint, pos: number, value: boolean) => {\n  return n | ((value ? _1n : _0n) << BigInt(pos));\n};\n\n/**\n * Calculate mask for N bits. Not using ** operator with bigints because of old engines.\n * Same as BigInt(`0b${Array(i).fill('1').join('')}`)\n */\nexport const bitMask = (n: number) => (_2n << BigInt(n - 1)) - _1n;\n\n// DRBG\n\nconst u8n = (data?: any) => new Uint8Array(data); // creates Uint8Array\nconst u8fr = (arr: any) => Uint8Array.from(arr); // another shortcut\ntype Pred<T> = (v: Uint8Array) => T | undefined;\n/**\n * Minimal HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n * @returns function that will call DRBG until 2nd arg returns something meaningful\n * @example\n *   const drbg = createHmacDRBG<Key>(32, 32, hmac);\n *   drbg(seed, bytesToKey); // bytesToKey must return Key or undefined\n */\nexport function createHmacDrbg<T>(\n  hashLen: number,\n  qByteLen: number,\n  hmacFn: (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array\n): (seed: Uint8Array, predicate: Pred<T>) => T {\n  if (typeof hashLen !== 'number' || hashLen < 2) throw new Error('hashLen must be a number');\n  if (typeof qByteLen !== 'number' || qByteLen < 2) throw new Error('qByteLen must be a number');\n  if (typeof hmacFn !== 'function') throw new Error('hmacFn must be a function');\n  // Step B, Step C: set hashLen to 8*ceil(hlen/8)\n  let v = u8n(hashLen); // Minimal non-full-spec HMAC-DRBG from NIST 800-90 for RFC6979 sigs.\n  let k = u8n(hashLen); // Steps B and C of RFC6979 3.2: set hashLen, in our case always same\n  let i = 0; // Iterations counter, will throw when over 1000\n  const reset = () => {\n    v.fill(1);\n    k.fill(0);\n    i = 0;\n  };\n  const h = (...b: Uint8Array[]) => hmacFn(k, v, ...b); // hmac(k)(v, ...values)\n  const reseed = (seed = u8n()) => {\n    // HMAC-DRBG reseed() function. Steps D-G\n    k = h(u8fr([0x00]), seed); // k = hmac(k || v || 0x00 || seed)\n    v = h(); // v = hmac(k || v)\n    if (seed.length === 0) return;\n    k = h(u8fr([0x01]), seed); // k = hmac(k || v || 0x01 || seed)\n    v = h(); // v = hmac(k || v)\n  };\n  const gen = () => {\n    // HMAC-DRBG generate() function\n    if (i++ >= 1000) throw new Error('drbg: tried 1000 values');\n    let len = 0;\n    const out: Uint8Array[] = [];\n    while (len < qByteLen) {\n      v = h();\n      const sl = v.slice();\n      out.push(sl);\n      len += v.length;\n    }\n    return concatBytes(...out);\n  };\n  const genUntil = (seed: Uint8Array, pred: Pred<T>): T => {\n    reset();\n    reseed(seed); // Steps D-G\n    let res: T | undefined = undefined; // Step H: grind until k is in [1..n-1]\n    while (!(res = pred(gen()))) reseed();\n    reset();\n    return res;\n  };\n  return genUntil;\n}\n\n// Validating curves and fields\n\nconst validatorFns = {\n  bigint: (val: any) => typeof val === 'bigint',\n  function: (val: any) => typeof val === 'function',\n  boolean: (val: any) => typeof val === 'boolean',\n  string: (val: any) => typeof val === 'string',\n  stringOrUint8Array: (val: any) => typeof val === 'string' || val instanceof Uint8Array,\n  isSafeInteger: (val: any) => Number.isSafeInteger(val),\n  array: (val: any) => Array.isArray(val),\n  field: (val: any, object: any) => (object as any).Fp.isValid(val),\n  hash: (val: any) => typeof val === 'function' && Number.isSafeInteger(val.outputLen),\n} as const;\ntype Validator = keyof typeof validatorFns;\ntype ValMap<T extends Record<string, any>> = { [K in keyof T]?: Validator };\n// type Record<K extends string | number | symbol, T> = { [P in K]: T; }\n\nexport function validateObject<T extends Record<string, any>>(\n  object: T,\n  validators: ValMap<T>,\n  optValidators: ValMap<T> = {}\n) {\n  const checkField = (fieldName: keyof T, type: Validator, isOptional: boolean) => {\n    const checkVal = validatorFns[type];\n    if (typeof checkVal !== 'function')\n      throw new Error(`Invalid validator \"${type}\", expected function`);\n\n    const val = object[fieldName as keyof typeof object];\n    if (isOptional && val === undefined) return;\n    if (!checkVal(val, object)) {\n      throw new Error(\n        `Invalid param ${String(fieldName)}=${val} (${typeof val}), expected ${type}`\n      );\n    }\n  };\n  for (const [fieldName, type] of Object.entries(validators)) checkField(fieldName, type!, false);\n  for (const [fieldName, type] of Object.entries(optValidators)) checkField(fieldName, type!, true);\n  return object;\n}\n// validate type tests\n// const o: { a: number; b: number; c: number } = { a: 1, b: 5, c: 6 };\n// const z0 = validateObject(o, { a: 'isSafeInteger' }, { c: 'bigint' }); // Ok!\n// // Should fail type-check\n// const z1 = validateObject(o, { a: 'tmp' }, { c: 'zz' });\n// const z2 = validateObject(o, { a: 'isSafeInteger' }, { c: 'zz' });\n// const z3 = validateObject(o, { test: 'boolean', z: 'bug' });\n// const z4 = validateObject(o, { a: 'boolean', z: 'bug' });\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;AACrB,MAAME,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMG,GAAG,GAAIC,CAAM,IAAsBA,CAAC,YAAYC,UAAU;AAWhE,MAAMC,KAAK,GAAG,eAAgBC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAC7DA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAChC;AACD;;;AAGA,OAAM,SAAUC,UAAUA,CAACC,KAAiB;EAC1C,IAAI,CAACZ,GAAG,CAACY,KAAK,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;EACvD;EACA,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACN,MAAM,EAAEE,CAAC,EAAE,EAAE;IACrCM,GAAG,IAAIX,KAAK,CAACS,KAAK,CAACJ,CAAC,CAAC,CAAC;;EAExB,OAAOM,GAAG;AACZ;AAEA,OAAM,SAAUC,mBAAmBA,CAACC,GAAoB;EACtD,MAAMF,GAAG,GAAGE,GAAG,CAACP,QAAQ,CAAC,EAAE,CAAC;EAC5B,OAAOK,GAAG,CAACR,MAAM,GAAG,CAAC,GAAG,IAAIQ,GAAG,EAAE,GAAGA,GAAG;AACzC;AAEA,OAAM,SAAUG,WAAWA,CAACH,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAID,KAAK,CAAC,2BAA2B,GAAG,OAAOC,GAAG,CAAC;EACtF;EACA,OAAOjB,MAAM,CAACiB,GAAG,KAAK,EAAE,GAAG,GAAG,GAAG,KAAKA,GAAG,EAAE,CAAC;AAC9C;AAEA;;;AAGA,OAAM,SAAUI,UAAUA,CAACJ,GAAW;EACpC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAID,KAAK,CAAC,2BAA2B,GAAG,OAAOC,GAAG,CAAC;EACtF,MAAMK,GAAG,GAAGL,GAAG,CAACR,MAAM;EACtB,IAAIa,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIN,KAAK,CAAC,yDAAyD,GAAGM,GAAG,CAAC;EAC7F,MAAMC,KAAK,GAAG,IAAIlB,UAAU,CAACiB,GAAG,GAAG,CAAC,CAAC;EACrC,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,KAAK,CAACd,MAAM,EAAEE,CAAC,EAAE,EAAE;IACrC,MAAMa,CAAC,GAAGb,CAAC,GAAG,CAAC;IACf,MAAMc,OAAO,GAAGR,GAAG,CAACS,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACnC,MAAMG,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACJ,OAAO,EAAE,EAAE,CAAC;IACzC,IAAIG,MAAM,CAACE,KAAK,CAACH,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE,MAAM,IAAIX,KAAK,CAAC,uBAAuB,CAAC;IAC5EO,KAAK,CAACZ,CAAC,CAAC,GAAGgB,IAAI;;EAEjB,OAAOJ,KAAK;AACd;AAEA;AACA,OAAM,SAAUQ,eAAeA,CAAChB,KAAiB;EAC/C,OAAOK,WAAW,CAACN,UAAU,CAACC,KAAK,CAAC,CAAC;AACvC;AACA,OAAM,SAAUiB,eAAeA,CAACjB,KAAiB;EAC/C,IAAI,CAACZ,GAAG,CAACY,KAAK,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;EACvD,OAAOI,WAAW,CAACN,UAAU,CAACT,UAAU,CAACG,IAAI,CAACO,KAAK,CAAC,CAACkB,OAAO,EAAE,CAAC,CAAC;AAClE;AAEA,OAAM,SAAUC,eAAeA,CAACC,CAAkB,EAAEb,GAAW;EAC7D,OAAOD,UAAU,CAACc,CAAC,CAACvB,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAACS,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC1D;AACA,OAAM,SAAUc,eAAeA,CAACD,CAAkB,EAAEb,GAAW;EAC7D,OAAOY,eAAe,CAACC,CAAC,EAAEb,GAAG,CAAC,CAACW,OAAO,EAAE;AAC1C;AACA;AACA,OAAM,SAAUI,kBAAkBA,CAACF,CAAkB;EACnD,OAAOd,UAAU,CAACH,mBAAmB,CAACiB,CAAC,CAAC,CAAC;AAC3C;AAEA;;;;;;;;;AASA,OAAM,SAAUG,WAAWA,CAACC,KAAa,EAAEtB,GAAQ,EAAEuB,cAAuB;EAC1E,IAAIC,GAAe;EACnB,IAAI,OAAOxB,GAAG,KAAK,QAAQ,EAAE;IAC3B,IAAI;MACFwB,GAAG,GAAGpB,UAAU,CAACJ,GAAG,CAAC;KACtB,CAAC,OAAOyB,CAAC,EAAE;MACV,MAAM,IAAI1B,KAAK,CAAC,GAAGuB,KAAK,mCAAmCtB,GAAG,aAAayB,CAAC,EAAE,CAAC;;GAElF,MAAM,IAAIvC,GAAG,CAACc,GAAG,CAAC,EAAE;IACnB;IACA;IACAwB,GAAG,GAAGpC,UAAU,CAACG,IAAI,CAACS,GAAG,CAAC;GAC3B,MAAM;IACL,MAAM,IAAID,KAAK,CAAC,GAAGuB,KAAK,mCAAmC,CAAC;;EAE9D,MAAMjB,GAAG,GAAGmB,GAAG,CAAChC,MAAM;EACtB,IAAI,OAAO+B,cAAc,KAAK,QAAQ,IAAIlB,GAAG,KAAKkB,cAAc,EAC9D,MAAM,IAAIxB,KAAK,CAAC,GAAGuB,KAAK,aAAaC,cAAc,eAAelB,GAAG,EAAE,CAAC;EAC1E,OAAOmB,GAAG;AACZ;AAEA;;;AAGA,OAAM,SAAUE,WAAWA,CAAC,GAAGC,MAAoB;EACjD,MAAMC,CAAC,GAAG,IAAIxC,UAAU,CAACuC,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAE3C,CAAC,KAAK2C,GAAG,GAAG3C,CAAC,CAACK,MAAM,EAAE,CAAC,CAAC,CAAC;EACtE,IAAIuC,GAAG,GAAG,CAAC,CAAC,CAAC;EACbJ,MAAM,CAACK,OAAO,CAAE7C,CAAC,IAAI;IACnB,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,EAAE,MAAM,IAAIY,KAAK,CAAC,qBAAqB,CAAC;IACnD6B,CAAC,CAACK,GAAG,CAAC9C,CAAC,EAAE4C,GAAG,CAAC;IACbA,GAAG,IAAI5C,CAAC,CAACK,MAAM;EACjB,CAAC,CAAC;EACF,OAAOoC,CAAC;AACV;AAEA,OAAM,SAAUM,UAAUA,CAACC,EAAc,EAAEC,EAAc;EACvD;EACA,IAAID,EAAE,CAAC3C,MAAM,KAAK4C,EAAE,CAAC5C,MAAM,EAAE,OAAO,KAAK;EACzC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,EAAE,CAAC3C,MAAM,EAAEE,CAAC,EAAE,EAAE,IAAIyC,EAAE,CAACzC,CAAC,CAAC,KAAK0C,EAAE,CAAC1C,CAAC,CAAC,EAAE,OAAO,KAAK;EACrE,OAAO,IAAI;AACb;AAMA;;;AAGA,OAAM,SAAU2C,WAAWA,CAACC,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIvC,KAAK,CAAC,oCAAoC,OAAOuC,GAAG,EAAE,CAAC;EAC9F,OAAO,IAAIlD,UAAU,CAAC,IAAImD,WAAW,EAAE,CAACC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AAEA;AAEA;;;;AAIA,OAAM,SAAUG,MAAMA,CAACvB,CAAS;EAC9B,IAAIb,GAAG;EACP,KAAKA,GAAG,GAAG,CAAC,EAAEa,CAAC,GAAGpC,GAAG,EAAEoC,CAAC,KAAKlC,GAAG,EAAEqB,GAAG,IAAI,CAAC,CAAC;EAC3C,OAAOA,GAAG;AACZ;AAEA;;;;;AAKA,OAAM,SAAUqC,MAAMA,CAACxB,CAAS,EAAEyB,GAAW;EAC3C,OAAQzB,CAAC,IAAInC,MAAM,CAAC4D,GAAG,CAAC,GAAI3D,GAAG;AACjC;AAEA;;;AAGA,OAAO,MAAM4D,MAAM,GAAGA,CAAC1B,CAAS,EAAEyB,GAAW,EAAEE,KAAc,KAAI;EAC/D,OAAO3B,CAAC,GAAI,CAAC2B,KAAK,GAAG7D,GAAG,GAAGF,GAAG,KAAKC,MAAM,CAAC4D,GAAG,CAAE;AACjD,CAAC;AAED;;;;AAIA,OAAO,MAAMG,OAAO,GAAI5B,CAAS,IAAK,CAACjC,GAAG,IAAIF,MAAM,CAACmC,CAAC,GAAG,CAAC,CAAC,IAAIlC,GAAG;AAElE;AAEA,MAAM+D,GAAG,GAAIC,IAAU,IAAK,IAAI5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC;AAClD,MAAMC,IAAI,GAAIC,GAAQ,IAAK9D,UAAU,CAACG,IAAI,CAAC2D,GAAG,CAAC,CAAC,CAAC;AAEjD;;;;;;;AAOA,OAAM,SAAUC,cAAcA,CAC5BC,OAAe,EACfC,QAAgB,EAChBC,MAAkE;EAElE,IAAI,OAAOF,OAAO,KAAK,QAAQ,IAAIA,OAAO,GAAG,CAAC,EAAE,MAAM,IAAIrD,KAAK,CAAC,0BAA0B,CAAC;EAC3F,IAAI,OAAOsD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE,MAAM,IAAItD,KAAK,CAAC,2BAA2B,CAAC;EAC9F,IAAI,OAAOuD,MAAM,KAAK,UAAU,EAAE,MAAM,IAAIvD,KAAK,CAAC,2BAA2B,CAAC;EAC9E;EACA,IAAIwD,CAAC,GAAGR,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC;EACtB,IAAII,CAAC,GAAGT,GAAG,CAACK,OAAO,CAAC,CAAC,CAAC;EACtB,IAAI1D,CAAC,GAAG,CAAC,CAAC,CAAC;EACX,MAAM+D,KAAK,GAAGA,CAAA,KAAK;IACjBF,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;IACTF,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;IACThE,CAAC,GAAG,CAAC;EACP,CAAC;EACD,MAAMiE,CAAC,GAAGA,CAAC,GAAGC,CAAe,KAAKN,MAAM,CAACE,CAAC,EAAED,CAAC,EAAE,GAAGK,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMC,MAAM,GAAGA,CAACC,IAAI,GAAGf,GAAG,EAAE,KAAI;IAC9B;IACAS,CAAC,GAAGG,CAAC,CAACV,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEa,IAAI,CAAC,CAAC,CAAC;IAC3BP,CAAC,GAAGI,CAAC,EAAE,CAAC,CAAC;IACT,IAAIG,IAAI,CAACtE,MAAM,KAAK,CAAC,EAAE;IACvBgE,CAAC,GAAGG,CAAC,CAACV,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEa,IAAI,CAAC,CAAC,CAAC;IAC3BP,CAAC,GAAGI,CAAC,EAAE,CAAC,CAAC;EACX,CAAC;EACD,MAAMI,GAAG,GAAGA,CAAA,KAAK;IACf;IACA,IAAIrE,CAAC,EAAE,IAAI,IAAI,EAAE,MAAM,IAAIK,KAAK,CAAC,yBAAyB,CAAC;IAC3D,IAAIM,GAAG,GAAG,CAAC;IACX,MAAM2D,GAAG,GAAiB,EAAE;IAC5B,OAAO3D,GAAG,GAAGgD,QAAQ,EAAE;MACrBE,CAAC,GAAGI,CAAC,EAAE;MACP,MAAMM,EAAE,GAAGV,CAAC,CAAC9C,KAAK,EAAE;MACpBuD,GAAG,CAACE,IAAI,CAACD,EAAE,CAAC;MACZ5D,GAAG,IAAIkD,CAAC,CAAC/D,MAAM;;IAEjB,OAAOkC,WAAW,CAAC,GAAGsC,GAAG,CAAC;EAC5B,CAAC;EACD,MAAMG,QAAQ,GAAGA,CAACL,IAAgB,EAAEM,IAAa,KAAO;IACtDX,KAAK,EAAE;IACPI,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC;IACd,IAAItC,GAAG,GAAkB6C,SAAS,CAAC,CAAC;IACpC,OAAO,EAAE7C,GAAG,GAAG4C,IAAI,CAACL,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,EAAE;IACrCJ,KAAK,EAAE;IACP,OAAOjC,GAAG;EACZ,CAAC;EACD,OAAO2C,QAAQ;AACjB;AAEA;AAEA,MAAMG,YAAY,GAAG;EACnBC,MAAM,EAAGC,GAAQ,IAAK,OAAOA,GAAG,KAAK,QAAQ;EAC7CC,QAAQ,EAAGD,GAAQ,IAAK,OAAOA,GAAG,KAAK,UAAU;EACjDE,OAAO,EAAGF,GAAQ,IAAK,OAAOA,GAAG,KAAK,SAAS;EAC/CG,MAAM,EAAGH,GAAQ,IAAK,OAAOA,GAAG,KAAK,QAAQ;EAC7CI,kBAAkB,EAAGJ,GAAQ,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,YAAYpF,UAAU;EACtFyF,aAAa,EAAGL,GAAQ,IAAK7D,MAAM,CAACkE,aAAa,CAACL,GAAG,CAAC;EACtDlE,KAAK,EAAGkE,GAAQ,IAAKlF,KAAK,CAACwF,OAAO,CAACN,GAAG,CAAC;EACvCO,KAAK,EAAEA,CAACP,GAAQ,EAAEQ,MAAW,KAAMA,MAAc,CAACC,EAAE,CAACC,OAAO,CAACV,GAAG,CAAC;EACjEW,IAAI,EAAGX,GAAQ,IAAK,OAAOA,GAAG,KAAK,UAAU,IAAI7D,MAAM,CAACkE,aAAa,CAACL,GAAG,CAACY,SAAS;CAC3E;AAGV;AAEA,OAAM,SAAUC,cAAcA,CAC5BL,MAAS,EACTM,UAAqB,EACrBC,aAAA,GAA2B,EAAE;EAE7B,MAAMC,UAAU,GAAGA,CAACC,SAAkB,EAAEC,IAAe,EAAEC,UAAmB,KAAI;IAC9E,MAAMC,QAAQ,GAAGtB,YAAY,CAACoB,IAAI,CAAC;IACnC,IAAI,OAAOE,QAAQ,KAAK,UAAU,EAChC,MAAM,IAAI7F,KAAK,CAAC,sBAAsB2F,IAAI,sBAAsB,CAAC;IAEnE,MAAMlB,GAAG,GAAGQ,MAAM,CAACS,SAAgC,CAAC;IACpD,IAAIE,UAAU,IAAInB,GAAG,KAAKH,SAAS,EAAE;IACrC,IAAI,CAACuB,QAAQ,CAACpB,GAAG,EAAEQ,MAAM,CAAC,EAAE;MAC1B,MAAM,IAAIjF,KAAK,CACb,iBAAiB8F,MAAM,CAACJ,SAAS,CAAC,IAAIjB,GAAG,KAAK,OAAOA,GAAG,eAAekB,IAAI,EAAE,CAC9E;;EAEL,CAAC;EACD,KAAK,MAAM,CAACD,SAAS,EAAEC,IAAI,CAAC,IAAII,MAAM,CAACC,OAAO,CAACT,UAAU,CAAC,EAAEE,UAAU,CAACC,SAAS,EAAEC,IAAK,EAAE,KAAK,CAAC;EAC/F,KAAK,MAAM,CAACD,SAAS,EAAEC,IAAI,CAAC,IAAII,MAAM,CAACC,OAAO,CAACR,aAAa,CAAC,EAAEC,UAAU,CAACC,SAAS,EAAEC,IAAK,EAAE,IAAI,CAAC;EACjG,OAAOV,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}