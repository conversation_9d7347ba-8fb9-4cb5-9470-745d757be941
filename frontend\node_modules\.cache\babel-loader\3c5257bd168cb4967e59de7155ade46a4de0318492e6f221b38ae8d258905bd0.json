{"ast": null, "code": "/**\n *  A Typed object allows a value to have its type explicitly\n *  specified.\n *\n *  For example, in Solidity, the value ``45`` could represent a\n *  ``uint8`` or a ``uint256``. The value ``0x1234`` could represent\n *  a ``bytes2`` or ``bytes``.\n *\n *  Since JavaScript has no meaningful way to explicitly inform any\n *  APIs which what the type is, this allows transparent interoperation\n *  with Soldity.\n *\n *  @_subsection: api/abi:Typed Values\n */\nimport { assertPrivate, defineProperties } from \"../utils/index.js\";\nconst _gaurd = {};\nfunction n(value, width) {\n  let signed = false;\n  if (width < 0) {\n    signed = true;\n    width *= -1;\n  }\n  // @TODO: Check range is valid for value\n  return new Typed(_gaurd, `${signed ? \"\" : \"u\"}int${width}`, value, {\n    signed,\n    width\n  });\n}\nfunction b(value, size) {\n  // @TODO: Check range is valid for value\n  return new Typed(_gaurd, `bytes${size ? size : \"\"}`, value, {\n    size\n  });\n}\nconst _typedSymbol = Symbol.for(\"_ethers_typed\");\n/**\n *  The **Typed** class to wrap values providing explicit type information.\n */\nexport class Typed {\n  /**\n   *  The type, as a Solidity-compatible type.\n   */\n  type;\n  /**\n   *  The actual value.\n   */\n  value;\n  #options;\n  /**\n   *  @_ignore:\n   */\n  _typedSymbol;\n  /**\n   *  @_ignore:\n   */\n  constructor(gaurd, type, value, options) {\n    if (options == null) {\n      options = null;\n    }\n    assertPrivate(_gaurd, gaurd, \"Typed\");\n    defineProperties(this, {\n      _typedSymbol,\n      type,\n      value\n    });\n    this.#options = options;\n    // Check the value is valid\n    this.format();\n  }\n  /**\n   *  Format the type as a Human-Readable type.\n   */\n  format() {\n    if (this.type === \"array\") {\n      throw new Error(\"\");\n    } else if (this.type === \"dynamicArray\") {\n      throw new Error(\"\");\n    } else if (this.type === \"tuple\") {\n      return `tuple(${this.value.map(v => v.format()).join(\",\")})`;\n    }\n    return this.type;\n  }\n  /**\n   *  The default value returned by this type.\n   */\n  defaultValue() {\n    return 0;\n  }\n  /**\n   *  The minimum value for numeric types.\n   */\n  minValue() {\n    return 0;\n  }\n  /**\n   *  The maximum value for numeric types.\n   */\n  maxValue() {\n    return 0;\n  }\n  /**\n   *  Returns ``true`` and provides a type guard is this is a [[TypedBigInt]].\n   */\n  isBigInt() {\n    return !!this.type.match(/^u?int[0-9]+$/);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard is this is a [[TypedData]].\n   */\n  isData() {\n    return this.type.startsWith(\"bytes\");\n  }\n  /**\n   *  Returns ``true`` and provides a type guard is this is a [[TypedString]].\n   */\n  isString() {\n    return this.type === \"string\";\n  }\n  /**\n   *  Returns the tuple name, if this is a tuple. Throws otherwise.\n   */\n  get tupleName() {\n    if (this.type !== \"tuple\") {\n      throw TypeError(\"not a tuple\");\n    }\n    return this.#options;\n  }\n  // Returns the length of this type as an array\n  // - `null` indicates the length is unforced, it could be dynamic\n  // - `-1` indicates the length is dynamic\n  // - any other value indicates it is a static array and is its length\n  /**\n   *  Returns the length of the array type or ``-1`` if it is dynamic.\n   *\n   *  Throws if the type is not an array.\n   */\n  get arrayLength() {\n    if (this.type !== \"array\") {\n      throw TypeError(\"not an array\");\n    }\n    if (this.#options === true) {\n      return -1;\n    }\n    if (this.#options === false) {\n      return this.value.length;\n    }\n    return null;\n  }\n  /**\n   *  Returns a new **Typed** of %%type%% with the %%value%%.\n   */\n  static from(type, value) {\n    return new Typed(_gaurd, type, value);\n  }\n  /**\n   *  Return a new ``uint8`` type for %%v%%.\n   */\n  static uint8(v) {\n    return n(v, 8);\n  }\n  /**\n   *  Return a new ``uint16`` type for %%v%%.\n   */\n  static uint16(v) {\n    return n(v, 16);\n  }\n  /**\n   *  Return a new ``uint24`` type for %%v%%.\n   */\n  static uint24(v) {\n    return n(v, 24);\n  }\n  /**\n   *  Return a new ``uint32`` type for %%v%%.\n   */\n  static uint32(v) {\n    return n(v, 32);\n  }\n  /**\n   *  Return a new ``uint40`` type for %%v%%.\n   */\n  static uint40(v) {\n    return n(v, 40);\n  }\n  /**\n   *  Return a new ``uint48`` type for %%v%%.\n   */\n  static uint48(v) {\n    return n(v, 48);\n  }\n  /**\n   *  Return a new ``uint56`` type for %%v%%.\n   */\n  static uint56(v) {\n    return n(v, 56);\n  }\n  /**\n   *  Return a new ``uint64`` type for %%v%%.\n   */\n  static uint64(v) {\n    return n(v, 64);\n  }\n  /**\n   *  Return a new ``uint72`` type for %%v%%.\n   */\n  static uint72(v) {\n    return n(v, 72);\n  }\n  /**\n   *  Return a new ``uint80`` type for %%v%%.\n   */\n  static uint80(v) {\n    return n(v, 80);\n  }\n  /**\n   *  Return a new ``uint88`` type for %%v%%.\n   */\n  static uint88(v) {\n    return n(v, 88);\n  }\n  /**\n   *  Return a new ``uint96`` type for %%v%%.\n   */\n  static uint96(v) {\n    return n(v, 96);\n  }\n  /**\n   *  Return a new ``uint104`` type for %%v%%.\n   */\n  static uint104(v) {\n    return n(v, 104);\n  }\n  /**\n   *  Return a new ``uint112`` type for %%v%%.\n   */\n  static uint112(v) {\n    return n(v, 112);\n  }\n  /**\n   *  Return a new ``uint120`` type for %%v%%.\n   */\n  static uint120(v) {\n    return n(v, 120);\n  }\n  /**\n   *  Return a new ``uint128`` type for %%v%%.\n   */\n  static uint128(v) {\n    return n(v, 128);\n  }\n  /**\n   *  Return a new ``uint136`` type for %%v%%.\n   */\n  static uint136(v) {\n    return n(v, 136);\n  }\n  /**\n   *  Return a new ``uint144`` type for %%v%%.\n   */\n  static uint144(v) {\n    return n(v, 144);\n  }\n  /**\n   *  Return a new ``uint152`` type for %%v%%.\n   */\n  static uint152(v) {\n    return n(v, 152);\n  }\n  /**\n   *  Return a new ``uint160`` type for %%v%%.\n   */\n  static uint160(v) {\n    return n(v, 160);\n  }\n  /**\n   *  Return a new ``uint168`` type for %%v%%.\n   */\n  static uint168(v) {\n    return n(v, 168);\n  }\n  /**\n   *  Return a new ``uint176`` type for %%v%%.\n   */\n  static uint176(v) {\n    return n(v, 176);\n  }\n  /**\n   *  Return a new ``uint184`` type for %%v%%.\n   */\n  static uint184(v) {\n    return n(v, 184);\n  }\n  /**\n   *  Return a new ``uint192`` type for %%v%%.\n   */\n  static uint192(v) {\n    return n(v, 192);\n  }\n  /**\n   *  Return a new ``uint200`` type for %%v%%.\n   */\n  static uint200(v) {\n    return n(v, 200);\n  }\n  /**\n   *  Return a new ``uint208`` type for %%v%%.\n   */\n  static uint208(v) {\n    return n(v, 208);\n  }\n  /**\n   *  Return a new ``uint216`` type for %%v%%.\n   */\n  static uint216(v) {\n    return n(v, 216);\n  }\n  /**\n   *  Return a new ``uint224`` type for %%v%%.\n   */\n  static uint224(v) {\n    return n(v, 224);\n  }\n  /**\n   *  Return a new ``uint232`` type for %%v%%.\n   */\n  static uint232(v) {\n    return n(v, 232);\n  }\n  /**\n   *  Return a new ``uint240`` type for %%v%%.\n   */\n  static uint240(v) {\n    return n(v, 240);\n  }\n  /**\n   *  Return a new ``uint248`` type for %%v%%.\n   */\n  static uint248(v) {\n    return n(v, 248);\n  }\n  /**\n   *  Return a new ``uint256`` type for %%v%%.\n   */\n  static uint256(v) {\n    return n(v, 256);\n  }\n  /**\n   *  Return a new ``uint256`` type for %%v%%.\n   */\n  static uint(v) {\n    return n(v, 256);\n  }\n  /**\n   *  Return a new ``int8`` type for %%v%%.\n   */\n  static int8(v) {\n    return n(v, -8);\n  }\n  /**\n   *  Return a new ``int16`` type for %%v%%.\n   */\n  static int16(v) {\n    return n(v, -16);\n  }\n  /**\n   *  Return a new ``int24`` type for %%v%%.\n   */\n  static int24(v) {\n    return n(v, -24);\n  }\n  /**\n   *  Return a new ``int32`` type for %%v%%.\n   */\n  static int32(v) {\n    return n(v, -32);\n  }\n  /**\n   *  Return a new ``int40`` type for %%v%%.\n   */\n  static int40(v) {\n    return n(v, -40);\n  }\n  /**\n   *  Return a new ``int48`` type for %%v%%.\n   */\n  static int48(v) {\n    return n(v, -48);\n  }\n  /**\n   *  Return a new ``int56`` type for %%v%%.\n   */\n  static int56(v) {\n    return n(v, -56);\n  }\n  /**\n   *  Return a new ``int64`` type for %%v%%.\n   */\n  static int64(v) {\n    return n(v, -64);\n  }\n  /**\n   *  Return a new ``int72`` type for %%v%%.\n   */\n  static int72(v) {\n    return n(v, -72);\n  }\n  /**\n   *  Return a new ``int80`` type for %%v%%.\n   */\n  static int80(v) {\n    return n(v, -80);\n  }\n  /**\n   *  Return a new ``int88`` type for %%v%%.\n   */\n  static int88(v) {\n    return n(v, -88);\n  }\n  /**\n   *  Return a new ``int96`` type for %%v%%.\n   */\n  static int96(v) {\n    return n(v, -96);\n  }\n  /**\n   *  Return a new ``int104`` type for %%v%%.\n   */\n  static int104(v) {\n    return n(v, -104);\n  }\n  /**\n   *  Return a new ``int112`` type for %%v%%.\n   */\n  static int112(v) {\n    return n(v, -112);\n  }\n  /**\n   *  Return a new ``int120`` type for %%v%%.\n   */\n  static int120(v) {\n    return n(v, -120);\n  }\n  /**\n   *  Return a new ``int128`` type for %%v%%.\n   */\n  static int128(v) {\n    return n(v, -128);\n  }\n  /**\n   *  Return a new ``int136`` type for %%v%%.\n   */\n  static int136(v) {\n    return n(v, -136);\n  }\n  /**\n   *  Return a new ``int144`` type for %%v%%.\n   */\n  static int144(v) {\n    return n(v, -144);\n  }\n  /**\n   *  Return a new ``int52`` type for %%v%%.\n   */\n  static int152(v) {\n    return n(v, -152);\n  }\n  /**\n   *  Return a new ``int160`` type for %%v%%.\n   */\n  static int160(v) {\n    return n(v, -160);\n  }\n  /**\n   *  Return a new ``int168`` type for %%v%%.\n   */\n  static int168(v) {\n    return n(v, -168);\n  }\n  /**\n   *  Return a new ``int176`` type for %%v%%.\n   */\n  static int176(v) {\n    return n(v, -176);\n  }\n  /**\n   *  Return a new ``int184`` type for %%v%%.\n   */\n  static int184(v) {\n    return n(v, -184);\n  }\n  /**\n   *  Return a new ``int92`` type for %%v%%.\n   */\n  static int192(v) {\n    return n(v, -192);\n  }\n  /**\n   *  Return a new ``int200`` type for %%v%%.\n   */\n  static int200(v) {\n    return n(v, -200);\n  }\n  /**\n   *  Return a new ``int208`` type for %%v%%.\n   */\n  static int208(v) {\n    return n(v, -208);\n  }\n  /**\n   *  Return a new ``int216`` type for %%v%%.\n   */\n  static int216(v) {\n    return n(v, -216);\n  }\n  /**\n   *  Return a new ``int224`` type for %%v%%.\n   */\n  static int224(v) {\n    return n(v, -224);\n  }\n  /**\n   *  Return a new ``int232`` type for %%v%%.\n   */\n  static int232(v) {\n    return n(v, -232);\n  }\n  /**\n   *  Return a new ``int240`` type for %%v%%.\n   */\n  static int240(v) {\n    return n(v, -240);\n  }\n  /**\n   *  Return a new ``int248`` type for %%v%%.\n   */\n  static int248(v) {\n    return n(v, -248);\n  }\n  /**\n   *  Return a new ``int256`` type for %%v%%.\n   */\n  static int256(v) {\n    return n(v, -256);\n  }\n  /**\n   *  Return a new ``int256`` type for %%v%%.\n   */\n  static int(v) {\n    return n(v, -256);\n  }\n  /**\n   *  Return a new ``bytes1`` type for %%v%%.\n   */\n  static bytes1(v) {\n    return b(v, 1);\n  }\n  /**\n   *  Return a new ``bytes2`` type for %%v%%.\n   */\n  static bytes2(v) {\n    return b(v, 2);\n  }\n  /**\n   *  Return a new ``bytes3`` type for %%v%%.\n   */\n  static bytes3(v) {\n    return b(v, 3);\n  }\n  /**\n   *  Return a new ``bytes4`` type for %%v%%.\n   */\n  static bytes4(v) {\n    return b(v, 4);\n  }\n  /**\n   *  Return a new ``bytes5`` type for %%v%%.\n   */\n  static bytes5(v) {\n    return b(v, 5);\n  }\n  /**\n   *  Return a new ``bytes6`` type for %%v%%.\n   */\n  static bytes6(v) {\n    return b(v, 6);\n  }\n  /**\n   *  Return a new ``bytes7`` type for %%v%%.\n   */\n  static bytes7(v) {\n    return b(v, 7);\n  }\n  /**\n   *  Return a new ``bytes8`` type for %%v%%.\n   */\n  static bytes8(v) {\n    return b(v, 8);\n  }\n  /**\n   *  Return a new ``bytes9`` type for %%v%%.\n   */\n  static bytes9(v) {\n    return b(v, 9);\n  }\n  /**\n   *  Return a new ``bytes10`` type for %%v%%.\n   */\n  static bytes10(v) {\n    return b(v, 10);\n  }\n  /**\n   *  Return a new ``bytes11`` type for %%v%%.\n   */\n  static bytes11(v) {\n    return b(v, 11);\n  }\n  /**\n   *  Return a new ``bytes12`` type for %%v%%.\n   */\n  static bytes12(v) {\n    return b(v, 12);\n  }\n  /**\n   *  Return a new ``bytes13`` type for %%v%%.\n   */\n  static bytes13(v) {\n    return b(v, 13);\n  }\n  /**\n   *  Return a new ``bytes14`` type for %%v%%.\n   */\n  static bytes14(v) {\n    return b(v, 14);\n  }\n  /**\n   *  Return a new ``bytes15`` type for %%v%%.\n   */\n  static bytes15(v) {\n    return b(v, 15);\n  }\n  /**\n   *  Return a new ``bytes16`` type for %%v%%.\n   */\n  static bytes16(v) {\n    return b(v, 16);\n  }\n  /**\n   *  Return a new ``bytes17`` type for %%v%%.\n   */\n  static bytes17(v) {\n    return b(v, 17);\n  }\n  /**\n   *  Return a new ``bytes18`` type for %%v%%.\n   */\n  static bytes18(v) {\n    return b(v, 18);\n  }\n  /**\n   *  Return a new ``bytes19`` type for %%v%%.\n   */\n  static bytes19(v) {\n    return b(v, 19);\n  }\n  /**\n   *  Return a new ``bytes20`` type for %%v%%.\n   */\n  static bytes20(v) {\n    return b(v, 20);\n  }\n  /**\n   *  Return a new ``bytes21`` type for %%v%%.\n   */\n  static bytes21(v) {\n    return b(v, 21);\n  }\n  /**\n   *  Return a new ``bytes22`` type for %%v%%.\n   */\n  static bytes22(v) {\n    return b(v, 22);\n  }\n  /**\n   *  Return a new ``bytes23`` type for %%v%%.\n   */\n  static bytes23(v) {\n    return b(v, 23);\n  }\n  /**\n   *  Return a new ``bytes24`` type for %%v%%.\n   */\n  static bytes24(v) {\n    return b(v, 24);\n  }\n  /**\n   *  Return a new ``bytes25`` type for %%v%%.\n   */\n  static bytes25(v) {\n    return b(v, 25);\n  }\n  /**\n   *  Return a new ``bytes26`` type for %%v%%.\n   */\n  static bytes26(v) {\n    return b(v, 26);\n  }\n  /**\n   *  Return a new ``bytes27`` type for %%v%%.\n   */\n  static bytes27(v) {\n    return b(v, 27);\n  }\n  /**\n   *  Return a new ``bytes28`` type for %%v%%.\n   */\n  static bytes28(v) {\n    return b(v, 28);\n  }\n  /**\n   *  Return a new ``bytes29`` type for %%v%%.\n   */\n  static bytes29(v) {\n    return b(v, 29);\n  }\n  /**\n   *  Return a new ``bytes30`` type for %%v%%.\n   */\n  static bytes30(v) {\n    return b(v, 30);\n  }\n  /**\n   *  Return a new ``bytes31`` type for %%v%%.\n   */\n  static bytes31(v) {\n    return b(v, 31);\n  }\n  /**\n   *  Return a new ``bytes32`` type for %%v%%.\n   */\n  static bytes32(v) {\n    return b(v, 32);\n  }\n  /**\n   *  Return a new ``address`` type for %%v%%.\n   */\n  static address(v) {\n    return new Typed(_gaurd, \"address\", v);\n  }\n  /**\n   *  Return a new ``bool`` type for %%v%%.\n   */\n  static bool(v) {\n    return new Typed(_gaurd, \"bool\", !!v);\n  }\n  /**\n   *  Return a new ``bytes`` type for %%v%%.\n   */\n  static bytes(v) {\n    return new Typed(_gaurd, \"bytes\", v);\n  }\n  /**\n   *  Return a new ``string`` type for %%v%%.\n   */\n  static string(v) {\n    return new Typed(_gaurd, \"string\", v);\n  }\n  /**\n   *  Return a new ``array`` type for %%v%%, allowing %%dynamic%% length.\n   */\n  static array(v, dynamic) {\n    throw new Error(\"not implemented yet\");\n    return new Typed(_gaurd, \"array\", v, dynamic);\n  }\n  /**\n   *  Return a new ``tuple`` type for %%v%%, with the optional %%name%%.\n   */\n  static tuple(v, name) {\n    throw new Error(\"not implemented yet\");\n    return new Typed(_gaurd, \"tuple\", v, name);\n  }\n  /**\n   *  Return a new ``uint8`` type for %%v%%.\n   */\n  static overrides(v) {\n    return new Typed(_gaurd, \"overrides\", Object.assign({}, v));\n  }\n  /**\n   *  Returns true only if %%value%% is a [[Typed]] instance.\n   */\n  static isTyped(value) {\n    return value && typeof value === \"object\" && \"_typedSymbol\" in value && value._typedSymbol === _typedSymbol;\n  }\n  /**\n   *  If the value is a [[Typed]] instance, validates the underlying value\n   *  and returns it, otherwise returns value directly.\n   *\n   *  This is useful for functions that with to accept either a [[Typed]]\n   *  object or values.\n   */\n  static dereference(value, type) {\n    if (Typed.isTyped(value)) {\n      if (value.type !== type) {\n        throw new Error(`invalid type: expecetd ${type}, got ${value.type}`);\n      }\n      return value.value;\n    }\n    return value;\n  }\n}", "map": {"version": 3, "names": ["assertPrivate", "defineProperties", "_gaurd", "n", "value", "width", "signed", "Typed", "b", "size", "_typedSymbol", "Symbol", "for", "type", "options", "constructor", "gaurd", "format", "Error", "map", "v", "join", "defaultValue", "minValue", "maxValue", "isBigInt", "match", "isData", "startsWith", "isString", "tupleName", "TypeError", "array<PERSON>ength", "length", "from", "uint8", "uint16", "uint24", "uint32", "uint40", "uint48", "uint56", "uint64", "uint72", "uint80", "uint88", "uint96", "uint104", "uint112", "uint120", "uint128", "uint136", "uint144", "uint152", "uint160", "uint168", "uint176", "uint184", "uint192", "uint200", "uint208", "uint216", "uint224", "uint232", "uint240", "uint248", "uint256", "uint", "int8", "int16", "int24", "int32", "int40", "int48", "int56", "int64", "int72", "int80", "int88", "int96", "int104", "int112", "int120", "int128", "int136", "int144", "int152", "int160", "int168", "int176", "int184", "int192", "int200", "int208", "int216", "int224", "int232", "int240", "int248", "int256", "int", "bytes1", "bytes2", "bytes3", "bytes4", "bytes5", "bytes6", "bytes7", "bytes8", "bytes9", "bytes10", "bytes11", "bytes12", "bytes13", "bytes14", "bytes15", "bytes16", "bytes17", "bytes18", "bytes19", "bytes20", "bytes21", "bytes22", "bytes23", "bytes24", "bytes25", "bytes26", "bytes27", "bytes28", "bytes29", "bytes30", "bytes31", "bytes32", "address", "bool", "bytes", "string", "array", "dynamic", "tuple", "name", "overrides", "Object", "assign", "isTyped", "dereference"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\typed.ts"], "sourcesContent": ["/**\n *  A Typed object allows a value to have its type explicitly\n *  specified.\n *\n *  For example, in Solidity, the value ``45`` could represent a\n *  ``uint8`` or a ``uint256``. The value ``0x1234`` could represent\n *  a ``bytes2`` or ``bytes``.\n *\n *  Since JavaScript has no meaningful way to explicitly inform any\n *  APIs which what the type is, this allows transparent interoperation\n *  with Soldity.\n *\n *  @_subsection: api/abi:Typed Values\n */\n\nimport { assertPrivate, defineProperties } from \"../utils/index.js\";\n\nimport type { Addressable } from \"../address/index.js\";\nimport type { BigNumberish, BytesLike } from \"../utils/index.js\";\n\nimport type { Result } from \"./coders/abstract-coder.js\";\n\nconst _gaurd = { };\n\nfunction n(value: BigNumberish, width: number): Typed {\n    let signed = false;\n    if (width < 0) {\n        signed = true;\n        width *= -1;\n    }\n\n    // @TODO: Check range is valid for value\n    return new Typed(_gaurd, `${ signed ? \"\": \"u\" }int${ width }`, value, { signed, width });\n}\n\nfunction b(value: BytesLike, size?: number): Typed {\n    // @TODO: Check range is valid for value\n    return new Typed(_gaurd, `bytes${ (size) ? size: \"\" }`, value, { size });\n}\n\n// @TODO: Remove this in v7, it was replaced by TypedBigInt\n/**\n *  @_ignore:\n */\nexport interface TypedNumber extends Typed {\n    value: number;\n    defaultValue(): number;\n    minValue(): number;\n    maxValue(): number;\n}\n\n/**\n *  A **Typed** that represents a numeric value.\n */\nexport interface TypedBigInt extends Typed {\n    /**\n     *  The value.\n     */\n    value: bigint;\n\n    /**\n     *  The default value for all numeric types is ``0``.\n     */\n    defaultValue(): bigint;\n\n    /**\n     *  The minimum value for this type, accounting for bit-width and signed-ness.\n     */\n    minValue(): bigint;\n\n    /**\n     *  The minimum value for this type, accounting for bit-width.\n     */\n    maxValue(): bigint;\n}\n\n/**\n *  A **Typed** that represents a binary sequence of data as bytes.\n */\nexport interface TypedData extends Typed {\n    /**\n     *  The value.\n     */\n    value: string;\n\n    /**\n     *  The default value for this type.\n     */\n    defaultValue(): string;\n}\n\n/**\n *  A **Typed** that represents a UTF-8 sequence of bytes.\n */\nexport interface TypedString extends Typed {\n    /**\n     *  The value.\n     */\n    value: string;\n\n    /**\n     *  The default value for the string type is the empty string (i.e. ``\"\"``).\n     */\n    defaultValue(): string;\n}\n\nconst _typedSymbol = Symbol.for(\"_ethers_typed\");\n\n/**\n *  The **Typed** class to wrap values providing explicit type information.\n */\nexport class Typed {\n\n    /**\n     *  The type, as a Solidity-compatible type.\n     */\n    readonly type!: string;\n\n    /**\n     *  The actual value.\n     */\n    readonly value!: any;\n\n    readonly #options: any;\n\n    /**\n     *  @_ignore:\n     */\n    readonly _typedSymbol!: Symbol;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(gaurd: any, type: string, value: any, options?: any) {\n        if (options == null) { options = null; }\n        assertPrivate(_gaurd, gaurd, \"Typed\");\n        defineProperties<Typed>(this, { _typedSymbol, type, value });\n        this.#options = options;\n\n        // Check the value is valid\n        this.format();\n    }\n\n    /**\n     *  Format the type as a Human-Readable type.\n     */\n    format(): string {\n        if (this.type === \"array\") {\n            throw new Error(\"\");\n        } else if (this.type === \"dynamicArray\") {\n            throw new Error(\"\");\n        } else if (this.type === \"tuple\") {\n            return `tuple(${ this.value.map((v: Typed) => v.format()).join(\",\") })`\n        }\n\n        return this.type;\n    }\n\n    /**\n     *  The default value returned by this type.\n     */\n    defaultValue(): string | number | bigint | Result {\n        return 0;\n    }\n\n    /**\n     *  The minimum value for numeric types.\n     */\n    minValue(): string | number | bigint {\n        return 0;\n    }\n\n    /**\n     *  The maximum value for numeric types.\n     */\n    maxValue(): string | number | bigint {\n        return 0;\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard is this is a [[TypedBigInt]].\n     */\n    isBigInt(): this is TypedBigInt {\n        return !!(this.type.match(/^u?int[0-9]+$/));\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard is this is a [[TypedData]].\n     */\n    isData(): this is TypedData {\n        return this.type.startsWith(\"bytes\");\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard is this is a [[TypedString]].\n     */\n    isString(): this is TypedString {\n        return (this.type === \"string\");\n    }\n\n    /**\n     *  Returns the tuple name, if this is a tuple. Throws otherwise.\n     */\n    get tupleName(): null | string {\n        if (this.type !== \"tuple\") { throw TypeError(\"not a tuple\"); }\n        return this.#options;\n    }\n\n    // Returns the length of this type as an array\n    // - `null` indicates the length is unforced, it could be dynamic\n    // - `-1` indicates the length is dynamic\n    // - any other value indicates it is a static array and is its length\n\n    /**\n     *  Returns the length of the array type or ``-1`` if it is dynamic.\n     *\n     *  Throws if the type is not an array.\n     */\n    get arrayLength(): null | number {\n        if (this.type !== \"array\") { throw TypeError(\"not an array\"); }\n        if (this.#options === true) { return -1; }\n        if (this.#options === false) { return (<Array<any>>(this.value)).length; }\n        return null;\n    }\n\n    /**\n     *  Returns a new **Typed** of %%type%% with the %%value%%.\n     */\n    static from(type: string, value: any): Typed {\n        return new Typed(_gaurd, type, value);\n    }\n\n    /**\n     *  Return a new ``uint8`` type for %%v%%.\n     */\n    static uint8(v: BigNumberish): Typed { return n(v, 8); }\n\n    /**\n     *  Return a new ``uint16`` type for %%v%%.\n     */\n    static uint16(v: BigNumberish): Typed { return n(v, 16); }\n\n    /**\n     *  Return a new ``uint24`` type for %%v%%.\n     */\n    static uint24(v: BigNumberish): Typed { return n(v, 24); }\n\n    /**\n     *  Return a new ``uint32`` type for %%v%%.\n     */\n    static uint32(v: BigNumberish): Typed { return n(v, 32); }\n\n    /**\n     *  Return a new ``uint40`` type for %%v%%.\n     */\n    static uint40(v: BigNumberish): Typed { return n(v, 40); }\n\n    /**\n     *  Return a new ``uint48`` type for %%v%%.\n     */\n    static uint48(v: BigNumberish): Typed { return n(v, 48); }\n\n    /**\n     *  Return a new ``uint56`` type for %%v%%.\n     */\n    static uint56(v: BigNumberish): Typed { return n(v, 56); }\n\n    /**\n     *  Return a new ``uint64`` type for %%v%%.\n     */\n    static uint64(v: BigNumberish): Typed { return n(v, 64); }\n\n    /**\n     *  Return a new ``uint72`` type for %%v%%.\n     */\n    static uint72(v: BigNumberish): Typed { return n(v, 72); }\n\n    /**\n     *  Return a new ``uint80`` type for %%v%%.\n     */\n    static uint80(v: BigNumberish): Typed { return n(v, 80); }\n\n    /**\n     *  Return a new ``uint88`` type for %%v%%.\n     */\n    static uint88(v: BigNumberish): Typed { return n(v, 88); }\n\n    /**\n     *  Return a new ``uint96`` type for %%v%%.\n     */\n    static uint96(v: BigNumberish): Typed { return n(v, 96); }\n\n    /**\n     *  Return a new ``uint104`` type for %%v%%.\n     */\n    static uint104(v: BigNumberish): Typed { return n(v, 104); }\n\n    /**\n     *  Return a new ``uint112`` type for %%v%%.\n     */\n    static uint112(v: BigNumberish): Typed { return n(v, 112); }\n\n    /**\n     *  Return a new ``uint120`` type for %%v%%.\n     */\n    static uint120(v: BigNumberish): Typed { return n(v, 120); }\n\n    /**\n     *  Return a new ``uint128`` type for %%v%%.\n     */\n    static uint128(v: BigNumberish): Typed { return n(v, 128); }\n\n    /**\n     *  Return a new ``uint136`` type for %%v%%.\n     */\n    static uint136(v: BigNumberish): Typed { return n(v, 136); }\n\n    /**\n     *  Return a new ``uint144`` type for %%v%%.\n     */\n    static uint144(v: BigNumberish): Typed { return n(v, 144); }\n\n    /**\n     *  Return a new ``uint152`` type for %%v%%.\n     */\n    static uint152(v: BigNumberish): Typed { return n(v, 152); }\n\n    /**\n     *  Return a new ``uint160`` type for %%v%%.\n     */\n    static uint160(v: BigNumberish): Typed { return n(v, 160); }\n\n    /**\n     *  Return a new ``uint168`` type for %%v%%.\n     */\n    static uint168(v: BigNumberish): Typed { return n(v, 168); }\n\n    /**\n     *  Return a new ``uint176`` type for %%v%%.\n     */\n    static uint176(v: BigNumberish): Typed { return n(v, 176); }\n\n    /**\n     *  Return a new ``uint184`` type for %%v%%.\n     */\n    static uint184(v: BigNumberish): Typed { return n(v, 184); }\n\n    /**\n     *  Return a new ``uint192`` type for %%v%%.\n     */\n    static uint192(v: BigNumberish): Typed { return n(v, 192); }\n\n    /**\n     *  Return a new ``uint200`` type for %%v%%.\n     */\n    static uint200(v: BigNumberish): Typed { return n(v, 200); }\n\n    /**\n     *  Return a new ``uint208`` type for %%v%%.\n     */\n    static uint208(v: BigNumberish): Typed { return n(v, 208); }\n\n    /**\n     *  Return a new ``uint216`` type for %%v%%.\n     */\n    static uint216(v: BigNumberish): Typed { return n(v, 216); }\n\n    /**\n     *  Return a new ``uint224`` type for %%v%%.\n     */\n    static uint224(v: BigNumberish): Typed { return n(v, 224); }\n\n    /**\n     *  Return a new ``uint232`` type for %%v%%.\n     */\n    static uint232(v: BigNumberish): Typed { return n(v, 232); }\n\n    /**\n     *  Return a new ``uint240`` type for %%v%%.\n     */\n    static uint240(v: BigNumberish): Typed { return n(v, 240); }\n\n    /**\n     *  Return a new ``uint248`` type for %%v%%.\n     */\n    static uint248(v: BigNumberish): Typed { return n(v, 248); }\n\n    /**\n     *  Return a new ``uint256`` type for %%v%%.\n     */\n    static uint256(v: BigNumberish): Typed { return n(v, 256); }\n\n    /**\n     *  Return a new ``uint256`` type for %%v%%.\n     */\n    static uint(v: BigNumberish): Typed { return n(v, 256); }\n\n    /**\n     *  Return a new ``int8`` type for %%v%%.\n     */\n    static int8(v: BigNumberish): Typed { return n(v, -8); }\n\n    /**\n     *  Return a new ``int16`` type for %%v%%.\n     */\n    static int16(v: BigNumberish): Typed { return n(v, -16); }\n\n    /**\n     *  Return a new ``int24`` type for %%v%%.\n     */\n    static int24(v: BigNumberish): Typed { return n(v, -24); }\n\n    /**\n     *  Return a new ``int32`` type for %%v%%.\n     */\n    static int32(v: BigNumberish): Typed { return n(v, -32); }\n\n    /**\n     *  Return a new ``int40`` type for %%v%%.\n     */\n    static int40(v: BigNumberish): Typed { return n(v, -40); }\n\n    /**\n     *  Return a new ``int48`` type for %%v%%.\n     */\n    static int48(v: BigNumberish): Typed { return n(v, -48); }\n\n    /**\n     *  Return a new ``int56`` type for %%v%%.\n     */\n    static int56(v: BigNumberish): Typed { return n(v, -56); }\n\n    /**\n     *  Return a new ``int64`` type for %%v%%.\n     */\n    static int64(v: BigNumberish): Typed { return n(v, -64); }\n\n    /**\n     *  Return a new ``int72`` type for %%v%%.\n     */\n    static int72(v: BigNumberish): Typed { return n(v, -72); }\n\n    /**\n     *  Return a new ``int80`` type for %%v%%.\n     */\n    static int80(v: BigNumberish): Typed { return n(v, -80); }\n\n    /**\n     *  Return a new ``int88`` type for %%v%%.\n     */\n    static int88(v: BigNumberish): Typed { return n(v, -88); }\n\n    /**\n     *  Return a new ``int96`` type for %%v%%.\n     */\n    static int96(v: BigNumberish): Typed { return n(v, -96); }\n\n    /**\n     *  Return a new ``int104`` type for %%v%%.\n     */\n    static int104(v: BigNumberish): Typed { return n(v, -104); }\n\n    /**\n     *  Return a new ``int112`` type for %%v%%.\n     */\n    static int112(v: BigNumberish): Typed { return n(v, -112); }\n\n    /**\n     *  Return a new ``int120`` type for %%v%%.\n     */\n    static int120(v: BigNumberish): Typed { return n(v, -120); }\n\n    /**\n     *  Return a new ``int128`` type for %%v%%.\n     */\n    static int128(v: BigNumberish): Typed { return n(v, -128); }\n\n    /**\n     *  Return a new ``int136`` type for %%v%%.\n     */\n    static int136(v: BigNumberish): Typed { return n(v, -136); }\n\n    /**\n     *  Return a new ``int144`` type for %%v%%.\n     */\n    static int144(v: BigNumberish): Typed { return n(v, -144); }\n\n    /**\n     *  Return a new ``int52`` type for %%v%%.\n     */\n    static int152(v: BigNumberish): Typed { return n(v, -152); }\n\n    /**\n     *  Return a new ``int160`` type for %%v%%.\n     */\n    static int160(v: BigNumberish): Typed { return n(v, -160); }\n\n    /**\n     *  Return a new ``int168`` type for %%v%%.\n     */\n    static int168(v: BigNumberish): Typed { return n(v, -168); }\n\n    /**\n     *  Return a new ``int176`` type for %%v%%.\n     */\n    static int176(v: BigNumberish): Typed { return n(v, -176); }\n\n    /**\n     *  Return a new ``int184`` type for %%v%%.\n     */\n    static int184(v: BigNumberish): Typed { return n(v, -184); }\n\n    /**\n     *  Return a new ``int92`` type for %%v%%.\n     */\n    static int192(v: BigNumberish): Typed { return n(v, -192); }\n\n    /**\n     *  Return a new ``int200`` type for %%v%%.\n     */\n    static int200(v: BigNumberish): Typed { return n(v, -200); }\n\n    /**\n     *  Return a new ``int208`` type for %%v%%.\n     */\n    static int208(v: BigNumberish): Typed { return n(v, -208); }\n\n    /**\n     *  Return a new ``int216`` type for %%v%%.\n     */\n    static int216(v: BigNumberish): Typed { return n(v, -216); }\n\n    /**\n     *  Return a new ``int224`` type for %%v%%.\n     */\n    static int224(v: BigNumberish): Typed { return n(v, -224); }\n\n    /**\n     *  Return a new ``int232`` type for %%v%%.\n     */\n    static int232(v: BigNumberish): Typed { return n(v, -232); }\n\n    /**\n     *  Return a new ``int240`` type for %%v%%.\n     */\n    static int240(v: BigNumberish): Typed { return n(v, -240); }\n\n    /**\n     *  Return a new ``int248`` type for %%v%%.\n     */\n    static int248(v: BigNumberish): Typed { return n(v, -248); }\n\n    /**\n     *  Return a new ``int256`` type for %%v%%.\n     */\n    static int256(v: BigNumberish): Typed { return n(v, -256); }\n\n    /**\n     *  Return a new ``int256`` type for %%v%%.\n     */\n    static int(v: BigNumberish): Typed { return n(v, -256); }\n\n    /**\n     *  Return a new ``bytes1`` type for %%v%%.\n     */\n    static bytes1(v: BytesLike): Typed { return b(v, 1); }\n\n    /**\n     *  Return a new ``bytes2`` type for %%v%%.\n     */\n    static bytes2(v: BytesLike): Typed { return b(v, 2); }\n\n    /**\n     *  Return a new ``bytes3`` type for %%v%%.\n     */\n    static bytes3(v: BytesLike): Typed { return b(v, 3); }\n\n    /**\n     *  Return a new ``bytes4`` type for %%v%%.\n     */\n    static bytes4(v: BytesLike): Typed { return b(v, 4); }\n\n    /**\n     *  Return a new ``bytes5`` type for %%v%%.\n     */\n    static bytes5(v: BytesLike): Typed { return b(v, 5); }\n\n    /**\n     *  Return a new ``bytes6`` type for %%v%%.\n     */\n    static bytes6(v: BytesLike): Typed { return b(v, 6); }\n\n    /**\n     *  Return a new ``bytes7`` type for %%v%%.\n     */\n    static bytes7(v: BytesLike): Typed { return b(v, 7); }\n\n    /**\n     *  Return a new ``bytes8`` type for %%v%%.\n     */\n    static bytes8(v: BytesLike): Typed { return b(v, 8); }\n\n    /**\n     *  Return a new ``bytes9`` type for %%v%%.\n     */\n    static bytes9(v: BytesLike): Typed { return b(v, 9); }\n\n    /**\n     *  Return a new ``bytes10`` type for %%v%%.\n     */\n    static bytes10(v: BytesLike): Typed { return b(v, 10); }\n\n    /**\n     *  Return a new ``bytes11`` type for %%v%%.\n     */\n    static bytes11(v: BytesLike): Typed { return b(v, 11); }\n\n    /**\n     *  Return a new ``bytes12`` type for %%v%%.\n     */\n    static bytes12(v: BytesLike): Typed { return b(v, 12); }\n\n    /**\n     *  Return a new ``bytes13`` type for %%v%%.\n     */\n    static bytes13(v: BytesLike): Typed { return b(v, 13); }\n\n    /**\n     *  Return a new ``bytes14`` type for %%v%%.\n     */\n    static bytes14(v: BytesLike): Typed { return b(v, 14); }\n\n    /**\n     *  Return a new ``bytes15`` type for %%v%%.\n     */\n    static bytes15(v: BytesLike): Typed { return b(v, 15); }\n\n    /**\n     *  Return a new ``bytes16`` type for %%v%%.\n     */\n    static bytes16(v: BytesLike): Typed { return b(v, 16); }\n\n    /**\n     *  Return a new ``bytes17`` type for %%v%%.\n     */\n    static bytes17(v: BytesLike): Typed { return b(v, 17); }\n\n    /**\n     *  Return a new ``bytes18`` type for %%v%%.\n     */\n    static bytes18(v: BytesLike): Typed { return b(v, 18); }\n\n    /**\n     *  Return a new ``bytes19`` type for %%v%%.\n     */\n    static bytes19(v: BytesLike): Typed { return b(v, 19); }\n\n    /**\n     *  Return a new ``bytes20`` type for %%v%%.\n     */\n    static bytes20(v: BytesLike): Typed { return b(v, 20); }\n\n    /**\n     *  Return a new ``bytes21`` type for %%v%%.\n     */\n    static bytes21(v: BytesLike): Typed { return b(v, 21); }\n\n    /**\n     *  Return a new ``bytes22`` type for %%v%%.\n     */\n    static bytes22(v: BytesLike): Typed { return b(v, 22); }\n\n    /**\n     *  Return a new ``bytes23`` type for %%v%%.\n     */\n    static bytes23(v: BytesLike): Typed { return b(v, 23); }\n\n    /**\n     *  Return a new ``bytes24`` type for %%v%%.\n     */\n    static bytes24(v: BytesLike): Typed { return b(v, 24); }\n\n    /**\n     *  Return a new ``bytes25`` type for %%v%%.\n     */\n    static bytes25(v: BytesLike): Typed { return b(v, 25); }\n\n    /**\n     *  Return a new ``bytes26`` type for %%v%%.\n     */\n    static bytes26(v: BytesLike): Typed { return b(v, 26); }\n\n    /**\n     *  Return a new ``bytes27`` type for %%v%%.\n     */\n    static bytes27(v: BytesLike): Typed { return b(v, 27); }\n\n    /**\n     *  Return a new ``bytes28`` type for %%v%%.\n     */\n    static bytes28(v: BytesLike): Typed { return b(v, 28); }\n\n    /**\n     *  Return a new ``bytes29`` type for %%v%%.\n     */\n    static bytes29(v: BytesLike): Typed { return b(v, 29); }\n\n    /**\n     *  Return a new ``bytes30`` type for %%v%%.\n     */\n    static bytes30(v: BytesLike): Typed { return b(v, 30); }\n\n    /**\n     *  Return a new ``bytes31`` type for %%v%%.\n     */\n    static bytes31(v: BytesLike): Typed { return b(v, 31); }\n\n    /**\n     *  Return a new ``bytes32`` type for %%v%%.\n     */\n    static bytes32(v: BytesLike): Typed { return b(v, 32); }\n\n\n    /**\n     *  Return a new ``address`` type for %%v%%.\n     */\n    static address(v: string | Addressable): Typed { return new Typed(_gaurd, \"address\", v); }\n\n    /**\n     *  Return a new ``bool`` type for %%v%%.\n     */\n    static bool(v: any): Typed { return new Typed(_gaurd, \"bool\", !!v); }\n\n    /**\n     *  Return a new ``bytes`` type for %%v%%.\n     */\n    static bytes(v: BytesLike): Typed { return new Typed(_gaurd, \"bytes\", v); }\n\n    /**\n     *  Return a new ``string`` type for %%v%%.\n     */\n    static string(v: string): Typed { return new Typed(_gaurd, \"string\", v); }\n\n\n    /**\n     *  Return a new ``array`` type for %%v%%, allowing %%dynamic%% length.\n     */\n    static array(v: Array<any | Typed>, dynamic?: null | boolean): Typed {\n        throw new Error(\"not implemented yet\");\n        return new Typed(_gaurd, \"array\", v, dynamic);\n    }\n\n\n    /**\n     *  Return a new ``tuple`` type for %%v%%, with the optional %%name%%.\n     */\n    static tuple(v: Array<any | Typed> | Record<string, any | Typed>, name?: string): Typed {\n        throw new Error(\"not implemented yet\");\n        return new Typed(_gaurd, \"tuple\", v, name);\n    }\n\n\n    /**\n     *  Return a new ``uint8`` type for %%v%%.\n     */\n    static overrides(v: Record<string, any>): Typed {\n        return new Typed(_gaurd, \"overrides\", Object.assign({ }, v));\n    }\n\n    /**\n     *  Returns true only if %%value%% is a [[Typed]] instance.\n     */\n    static isTyped(value: any): value is Typed {\n        return (value\n            && typeof(value) === \"object\"\n            && \"_typedSymbol\" in value\n            && value._typedSymbol === _typedSymbol);\n    }\n\n    /**\n     *  If the value is a [[Typed]] instance, validates the underlying value\n     *  and returns it, otherwise returns value directly.\n     *\n     *  This is useful for functions that with to accept either a [[Typed]]\n     *  object or values.\n     */\n    static dereference<T>(value: Typed | T, type: string): T {\n        if (Typed.isTyped(value)) {\n            if (value.type !== type) {\n                throw new Error(`invalid type: expecetd ${ type }, got ${ value.type }`);\n            }\n            return value.value;\n        }\n        return value;\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;AAeA,SAASA,aAAa,EAAEC,gBAAgB,QAAQ,mBAAmB;AAOnE,MAAMC,MAAM,GAAG,EAAG;AAElB,SAASC,CAACA,CAACC,KAAmB,EAAEC,KAAa;EACzC,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAID,KAAK,GAAG,CAAC,EAAE;IACXC,MAAM,GAAG,IAAI;IACbD,KAAK,IAAI,CAAC,CAAC;;EAGf;EACA,OAAO,IAAIE,KAAK,CAACL,MAAM,EAAE,GAAII,MAAM,GAAG,EAAE,GAAE,GAAI,MAAOD,KAAM,EAAE,EAAED,KAAK,EAAE;IAAEE,MAAM;IAAED;EAAK,CAAE,CAAC;AAC5F;AAEA,SAASG,CAACA,CAACJ,KAAgB,EAAEK,IAAa;EACtC;EACA,OAAO,IAAIF,KAAK,CAACL,MAAM,EAAE,QAAUO,IAAI,GAAIA,IAAI,GAAE,EAAG,EAAE,EAAEL,KAAK,EAAE;IAAEK;EAAI,CAAE,CAAC;AAC5E;AAoEA,MAAMC,YAAY,GAAGC,MAAM,CAACC,GAAG,CAAC,eAAe,CAAC;AAEhD;;;AAGA,OAAM,MAAOL,KAAK;EAEd;;;EAGSM,IAAI;EAEb;;;EAGST,KAAK;EAEL,CAAAU,OAAQ;EAEjB;;;EAGSJ,YAAY;EAErB;;;EAGAK,YAAYC,KAAU,EAAEH,IAAY,EAAET,KAAU,EAAEU,OAAa;IAC3D,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,IAAI;;IACrCd,aAAa,CAACE,MAAM,EAAEc,KAAK,EAAE,OAAO,CAAC;IACrCf,gBAAgB,CAAQ,IAAI,EAAE;MAAES,YAAY;MAAEG,IAAI;MAAET;IAAK,CAAE,CAAC;IAC5D,IAAI,CAAC,CAAAU,OAAQ,GAAGA,OAAO;IAEvB;IACA,IAAI,CAACG,MAAM,EAAE;EACjB;EAEA;;;EAGAA,MAAMA,CAAA;IACF,IAAI,IAAI,CAACJ,IAAI,KAAK,OAAO,EAAE;MACvB,MAAM,IAAIK,KAAK,CAAC,EAAE,CAAC;KACtB,MAAM,IAAI,IAAI,CAACL,IAAI,KAAK,cAAc,EAAE;MACrC,MAAM,IAAIK,KAAK,CAAC,EAAE,CAAC;KACtB,MAAM,IAAI,IAAI,CAACL,IAAI,KAAK,OAAO,EAAE;MAC9B,OAAO,SAAU,IAAI,CAACT,KAAK,CAACe,GAAG,CAAEC,CAAQ,IAAKA,CAAC,CAACH,MAAM,EAAE,CAAC,CAACI,IAAI,CAAC,GAAG,CAAE,GAAG;;IAG3E,OAAO,IAAI,CAACR,IAAI;EACpB;EAEA;;;EAGAS,YAAYA,CAAA;IACR,OAAO,CAAC;EACZ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ,OAAO,CAAC;EACZ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ,OAAO,CAAC;EACZ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ,OAAO,CAAC,CAAE,IAAI,CAACZ,IAAI,CAACa,KAAK,CAAC,eAAe,CAAE;EAC/C;EAEA;;;EAGAC,MAAMA,CAAA;IACF,OAAO,IAAI,CAACd,IAAI,CAACe,UAAU,CAAC,OAAO,CAAC;EACxC;EAEA;;;EAGAC,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAAChB,IAAI,KAAK,QAAQ;EAClC;EAEA;;;EAGA,IAAIiB,SAASA,CAAA;IACT,IAAI,IAAI,CAACjB,IAAI,KAAK,OAAO,EAAE;MAAE,MAAMkB,SAAS,CAAC,aAAa,CAAC;;IAC3D,OAAO,IAAI,CAAC,CAAAjB,OAAQ;EACxB;EAEA;EACA;EACA;EACA;EAEA;;;;;EAKA,IAAIkB,WAAWA,CAAA;IACX,IAAI,IAAI,CAACnB,IAAI,KAAK,OAAO,EAAE;MAAE,MAAMkB,SAAS,CAAC,cAAc,CAAC;;IAC5D,IAAI,IAAI,CAAC,CAAAjB,OAAQ,KAAK,IAAI,EAAE;MAAE,OAAO,CAAC,CAAC;;IACvC,IAAI,IAAI,CAAC,CAAAA,OAAQ,KAAK,KAAK,EAAE;MAAE,OAAqB,IAAI,CAACV,KAAK,CAAG6B,MAAM;;IACvE,OAAO,IAAI;EACf;EAEA;;;EAGA,OAAOC,IAAIA,CAACrB,IAAY,EAAET,KAAU;IAChC,OAAO,IAAIG,KAAK,CAACL,MAAM,EAAEW,IAAI,EAAET,KAAK,CAAC;EACzC;EAEA;;;EAGA,OAAO+B,KAAKA,CAACf,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,CAAC;EAAE;EAEvD;;;EAGA,OAAOgB,MAAMA,CAAChB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOiB,MAAMA,CAACjB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOkB,MAAMA,CAAClB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOmB,MAAMA,CAACnB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOoB,MAAMA,CAACpB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOqB,MAAMA,CAACrB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOsB,MAAMA,CAACtB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOuB,MAAMA,CAACvB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOwB,MAAMA,CAACxB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOyB,MAAMA,CAACzB,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAO0B,MAAMA,CAAC1B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAO2B,OAAOA,CAAC3B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO4B,OAAOA,CAAC5B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO6B,OAAOA,CAAC7B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO8B,OAAOA,CAAC9B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO+B,OAAOA,CAAC/B,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOgC,OAAOA,CAAChC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOiC,OAAOA,CAACjC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOkC,OAAOA,CAAClC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOmC,OAAOA,CAACnC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOoC,OAAOA,CAACpC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOqC,OAAOA,CAACrC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOsC,OAAOA,CAACtC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOuC,OAAOA,CAACvC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOwC,OAAOA,CAACxC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOyC,OAAOA,CAACzC,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO0C,OAAOA,CAAC1C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO2C,OAAOA,CAAC3C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO4C,OAAOA,CAAC5C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO6C,OAAOA,CAAC7C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO8C,OAAOA,CAAC9C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO+C,IAAIA,CAAC/C,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,GAAG,CAAC;EAAE;EAExD;;;EAGA,OAAOgD,IAAIA,CAAChD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,CAAC,CAAC;EAAE;EAEvD;;;EAGA,OAAOiD,KAAKA,CAACjD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOkD,KAAKA,CAAClD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOmD,KAAKA,CAACnD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOoD,KAAKA,CAACpD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOqD,KAAKA,CAACrD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOsD,KAAKA,CAACtD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOuD,KAAKA,CAACvD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOwD,KAAKA,CAACxD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAOyD,KAAKA,CAACzD,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAO0D,KAAKA,CAAC1D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAO2D,KAAKA,CAAC3D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,EAAE,CAAC;EAAE;EAEzD;;;EAGA,OAAO4D,MAAMA,CAAC5D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO6D,MAAMA,CAAC7D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO8D,MAAMA,CAAC9D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO+D,MAAMA,CAAC/D,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOgE,MAAMA,CAAChE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOiE,MAAMA,CAACjE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOkE,MAAMA,CAAClE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOmE,MAAMA,CAACnE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOoE,MAAMA,CAACpE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOqE,MAAMA,CAACrE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOsE,MAAMA,CAACtE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOuE,MAAMA,CAACvE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOwE,MAAMA,CAACxE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOyE,MAAMA,CAACzE,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO0E,MAAMA,CAAC1E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO2E,MAAMA,CAAC3E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO4E,MAAMA,CAAC5E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO6E,MAAMA,CAAC7E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO8E,MAAMA,CAAC9E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAO+E,MAAMA,CAAC/E,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAE3D;;;EAGA,OAAOgF,GAAGA,CAAChF,CAAe;IAAW,OAAOjB,CAAC,CAACiB,CAAC,EAAE,CAAC,GAAG,CAAC;EAAE;EAExD;;;EAGA,OAAOiF,MAAMA,CAACjF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOkF,MAAMA,CAAClF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOmF,MAAMA,CAACnF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOoF,MAAMA,CAACpF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOqF,MAAMA,CAACrF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOsF,MAAMA,CAACtF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOuF,MAAMA,CAACvF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOwF,MAAMA,CAACxF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAOyF,MAAMA,CAACzF,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,CAAC,CAAC;EAAE;EAErD;;;EAGA,OAAO0F,OAAOA,CAAC1F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO2F,OAAOA,CAAC3F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO4F,OAAOA,CAAC5F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO6F,OAAOA,CAAC7F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO8F,OAAOA,CAAC9F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO+F,OAAOA,CAAC/F,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOgG,OAAOA,CAAChG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOiG,OAAOA,CAACjG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOkG,OAAOA,CAAClG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOmG,OAAOA,CAACnG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOoG,OAAOA,CAACpG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOqG,OAAOA,CAACrG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOsG,OAAOA,CAACtG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOuG,OAAOA,CAACvG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOwG,OAAOA,CAACxG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOyG,OAAOA,CAACzG,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO0G,OAAOA,CAAC1G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO2G,OAAOA,CAAC3G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO4G,OAAOA,CAAC5G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO6G,OAAOA,CAAC7G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO8G,OAAOA,CAAC9G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAO+G,OAAOA,CAAC/G,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAEvD;;;EAGA,OAAOgH,OAAOA,CAAChH,CAAY;IAAW,OAAOZ,CAAC,CAACY,CAAC,EAAE,EAAE,CAAC;EAAE;EAGvD;;;EAGA,OAAOiH,OAAOA,CAACjH,CAAuB;IAAW,OAAO,IAAIb,KAAK,CAACL,MAAM,EAAE,SAAS,EAAEkB,CAAC,CAAC;EAAE;EAEzF;;;EAGA,OAAOkH,IAAIA,CAAClH,CAAM;IAAW,OAAO,IAAIb,KAAK,CAACL,MAAM,EAAE,MAAM,EAAE,CAAC,CAACkB,CAAC,CAAC;EAAE;EAEpE;;;EAGA,OAAOmH,KAAKA,CAACnH,CAAY;IAAW,OAAO,IAAIb,KAAK,CAACL,MAAM,EAAE,OAAO,EAAEkB,CAAC,CAAC;EAAE;EAE1E;;;EAGA,OAAOoH,MAAMA,CAACpH,CAAS;IAAW,OAAO,IAAIb,KAAK,CAACL,MAAM,EAAE,QAAQ,EAAEkB,CAAC,CAAC;EAAE;EAGzE;;;EAGA,OAAOqH,KAAKA,CAACrH,CAAqB,EAAEsH,OAAwB;IACxD,MAAM,IAAIxH,KAAK,CAAC,qBAAqB,CAAC;IACtC,OAAO,IAAIX,KAAK,CAACL,MAAM,EAAE,OAAO,EAAEkB,CAAC,EAAEsH,OAAO,CAAC;EACjD;EAGA;;;EAGA,OAAOC,KAAKA,CAACvH,CAAmD,EAAEwH,IAAa;IAC3E,MAAM,IAAI1H,KAAK,CAAC,qBAAqB,CAAC;IACtC,OAAO,IAAIX,KAAK,CAACL,MAAM,EAAE,OAAO,EAAEkB,CAAC,EAAEwH,IAAI,CAAC;EAC9C;EAGA;;;EAGA,OAAOC,SAASA,CAACzH,CAAsB;IACnC,OAAO,IAAIb,KAAK,CAACL,MAAM,EAAE,WAAW,EAAE4I,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE3H,CAAC,CAAC,CAAC;EAChE;EAEA;;;EAGA,OAAO4H,OAAOA,CAAC5I,KAAU;IACrB,OAAQA,KAAK,IACN,OAAOA,KAAM,KAAK,QAAQ,IAC1B,cAAc,IAAIA,KAAK,IACvBA,KAAK,CAACM,YAAY,KAAKA,YAAY;EAC9C;EAEA;;;;;;;EAOA,OAAOuI,WAAWA,CAAI7I,KAAgB,EAAES,IAAY;IAChD,IAAIN,KAAK,CAACyI,OAAO,CAAC5I,KAAK,CAAC,EAAE;MACtB,IAAIA,KAAK,CAACS,IAAI,KAAKA,IAAI,EAAE;QACrB,MAAM,IAAIK,KAAK,CAAC,0BAA2BL,IAAK,SAAUT,KAAK,CAACS,IAAK,EAAE,CAAC;;MAE5E,OAAOT,KAAK,CAACA,KAAK;;IAEtB,OAAOA,KAAK;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}