{"ast": null, "code": "/**\n *  One of the most common ways to interact with the blockchain is\n *  by a node running a JSON-RPC interface which can be connected to,\n *  based on the transport, using:\n *\n *  - HTTP or HTTPS - [[JsonRpcProvider]]\n *  - WebSocket - [[WebSocketProvider]]\n *  - IPC - [[IpcSocketProvider]]\n *\n * @_section: api/providers/jsonrpc:JSON-RPC Provider  [about-jsonrpcProvider]\n */\n// @TODO:\n// - Add the batching API\n// https://playground.open-rpc.org/?schemaUrl=https://raw.githubusercontent.com/ethereum/eth1.0-apis/assembled-spec/openrpc.json&uiSchema%5BappBar%5D%5Bui:splitView%5D=true&uiSchema%5BappBar%5D%5Bui:input%5D=false&uiSchema%5BappBar%5D%5Bui:examplesDropdown%5D=false\nimport { AbiCoder } from \"../abi/index.js\";\nimport { getAddress, resolveAddress } from \"../address/index.js\";\nimport { TypedDataEncoder } from \"../hash/index.js\";\nimport { accessListify, authorizationify } from \"../transaction/index.js\";\nimport { defineProperties, getBigInt, hexlify, isHexString, toQuantity, toUtf8Bytes, isError, makeError, assert, assertArgument, FetchRequest, resolveProperties } from \"../utils/index.js\";\nimport { AbstractProvider, UnmanagedSubscriber } from \"./abstract-provider.js\";\nimport { AbstractSigner } from \"./abstract-signer.js\";\nimport { Network } from \"./network.js\";\nimport { FilterIdEventSubscriber, FilterIdPendingSubscriber } from \"./subscriber-filterid.js\";\nimport { PollingEventSubscriber } from \"./subscriber-polling.js\";\nconst Primitive = \"bigint,boolean,function,number,string,symbol\".split(/,/g);\n//const Methods = \"getAddress,then\".split(/,/g);\nfunction deepCopy(value) {\n  if (value == null || Primitive.indexOf(typeof value) >= 0) {\n    return value;\n  }\n  // Keep any Addressable\n  if (typeof value.getAddress === \"function\") {\n    return value;\n  }\n  if (Array.isArray(value)) {\n    return value.map(deepCopy);\n  }\n  if (typeof value === \"object\") {\n    return Object.keys(value).reduce((accum, key) => {\n      accum[key] = value[key];\n      return accum;\n    }, {});\n  }\n  throw new Error(`should not happen: ${value} (${typeof value})`);\n}\nfunction stall(duration) {\n  return new Promise(resolve => {\n    setTimeout(resolve, duration);\n  });\n}\nfunction getLowerCase(value) {\n  if (value) {\n    return value.toLowerCase();\n  }\n  return value;\n}\nfunction isPollable(value) {\n  return value && typeof value.pollingInterval === \"number\";\n}\nconst defaultOptions = {\n  polling: false,\n  staticNetwork: null,\n  batchStallTime: 10,\n  batchMaxSize: 1 << 20,\n  batchMaxCount: 100,\n  cacheTimeout: 250,\n  pollingInterval: 4000\n};\n// @TODO: Unchecked Signers\nexport class JsonRpcSigner extends AbstractSigner {\n  address;\n  constructor(provider, address) {\n    super(provider);\n    address = getAddress(address);\n    defineProperties(this, {\n      address\n    });\n  }\n  connect(provider) {\n    assert(false, \"cannot reconnect JsonRpcSigner\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"signer.connect\"\n    });\n  }\n  async getAddress() {\n    return this.address;\n  }\n  // JSON-RPC will automatially fill in nonce, etc. so we just check from\n  async populateTransaction(tx) {\n    return await this.populateCall(tx);\n  }\n  // Returns just the hash of the transaction after sent, which is what\n  // the bare JSON-RPC API does;\n  async sendUncheckedTransaction(_tx) {\n    const tx = deepCopy(_tx);\n    const promises = [];\n    // Make sure the from matches the sender\n    if (tx.from) {\n      const _from = tx.from;\n      promises.push((async () => {\n        const from = await resolveAddress(_from, this.provider);\n        assertArgument(from != null && from.toLowerCase() === this.address.toLowerCase(), \"from address mismatch\", \"transaction\", _tx);\n        tx.from = from;\n      })());\n    } else {\n      tx.from = this.address;\n    }\n    // The JSON-RPC for eth_sendTransaction uses 90000 gas; if the user\n    // wishes to use this, it is easy to specify explicitly, otherwise\n    // we look it up for them.\n    if (tx.gasLimit == null) {\n      promises.push((async () => {\n        tx.gasLimit = await this.provider.estimateGas({\n          ...tx,\n          from: this.address\n        });\n      })());\n    }\n    // The address may be an ENS name or Addressable\n    if (tx.to != null) {\n      const _to = tx.to;\n      promises.push((async () => {\n        tx.to = await resolveAddress(_to, this.provider);\n      })());\n    }\n    // Wait until all of our properties are filled in\n    if (promises.length) {\n      await Promise.all(promises);\n    }\n    const hexTx = this.provider.getRpcTransaction(tx);\n    return this.provider.send(\"eth_sendTransaction\", [hexTx]);\n  }\n  async sendTransaction(tx) {\n    // This cannot be mined any earlier than any recent block\n    const blockNumber = await this.provider.getBlockNumber();\n    // Send the transaction\n    const hash = await this.sendUncheckedTransaction(tx);\n    // Unfortunately, JSON-RPC only provides and opaque transaction hash\n    // for a response, and we need the actual transaction, so we poll\n    // for it; it should show up very quickly\n    return await new Promise((resolve, reject) => {\n      const timeouts = [1000, 100];\n      let invalids = 0;\n      const checkTx = async () => {\n        try {\n          // Try getting the transaction\n          const tx = await this.provider.getTransaction(hash);\n          if (tx != null) {\n            resolve(tx.replaceableTransaction(blockNumber));\n            return;\n          }\n        } catch (error) {\n          // If we were cancelled: stop polling.\n          // If the data is bad: the node returns bad transactions\n          // If the network changed: calling again will also fail\n          // If unsupported: likely destroyed\n          if (isError(error, \"CANCELLED\") || isError(error, \"BAD_DATA\") || isError(error, \"NETWORK_ERROR\") || isError(error, \"UNSUPPORTED_OPERATION\")) {\n            if (error.info == null) {\n              error.info = {};\n            }\n            error.info.sendTransactionHash = hash;\n            reject(error);\n            return;\n          }\n          // Stop-gap for misbehaving backends; see #4513\n          if (isError(error, \"INVALID_ARGUMENT\")) {\n            invalids++;\n            if (error.info == null) {\n              error.info = {};\n            }\n            error.info.sendTransactionHash = hash;\n            if (invalids > 10) {\n              reject(error);\n              return;\n            }\n          }\n          // Notify anyone that cares; but we will try again, since\n          // it is likely an intermittent service error\n          this.provider.emit(\"error\", makeError(\"failed to fetch transation after sending (will try again)\", \"UNKNOWN_ERROR\", {\n            error\n          }));\n        }\n        // Wait another 4 seconds\n        this.provider._setTimeout(() => {\n          checkTx();\n        }, timeouts.pop() || 4000);\n      };\n      checkTx();\n    });\n  }\n  async signTransaction(_tx) {\n    const tx = deepCopy(_tx);\n    // Make sure the from matches the sender\n    if (tx.from) {\n      const from = await resolveAddress(tx.from, this.provider);\n      assertArgument(from != null && from.toLowerCase() === this.address.toLowerCase(), \"from address mismatch\", \"transaction\", _tx);\n      tx.from = from;\n    } else {\n      tx.from = this.address;\n    }\n    const hexTx = this.provider.getRpcTransaction(tx);\n    return await this.provider.send(\"eth_signTransaction\", [hexTx]);\n  }\n  async signMessage(_message) {\n    const message = typeof _message === \"string\" ? toUtf8Bytes(_message) : _message;\n    return await this.provider.send(\"personal_sign\", [hexlify(message), this.address.toLowerCase()]);\n  }\n  async signTypedData(domain, types, _value) {\n    const value = deepCopy(_value);\n    // Populate any ENS names (in-place)\n    const populated = await TypedDataEncoder.resolveNames(domain, types, value, async value => {\n      const address = await resolveAddress(value);\n      assertArgument(address != null, \"TypedData does not support null address\", \"value\", value);\n      return address;\n    });\n    return await this.provider.send(\"eth_signTypedData_v4\", [this.address.toLowerCase(), JSON.stringify(TypedDataEncoder.getPayload(populated.domain, types, populated.value))]);\n  }\n  async unlock(password) {\n    return this.provider.send(\"personal_unlockAccount\", [this.address.toLowerCase(), password, null]);\n  }\n  // https://github.com/ethereum/wiki/wiki/JSON-RPC#eth_sign\n  async _legacySignMessage(_message) {\n    const message = typeof _message === \"string\" ? toUtf8Bytes(_message) : _message;\n    return await this.provider.send(\"eth_sign\", [this.address.toLowerCase(), hexlify(message)]);\n  }\n}\n/**\n *  The JsonRpcApiProvider is an abstract class and **MUST** be\n *  sub-classed.\n *\n *  It provides the base for all JSON-RPC-based Provider interaction.\n *\n *  Sub-classing Notes:\n *  - a sub-class MUST override _send\n *  - a sub-class MUST call the `_start()` method once connected\n */\nexport class JsonRpcApiProvider extends AbstractProvider {\n  #options;\n  // The next ID to use for the JSON-RPC ID field\n  #nextId;\n  // Payloads are queued and triggered in batches using the drainTimer\n  #payloads;\n  #drainTimer;\n  #notReady;\n  #network;\n  #pendingDetectNetwork;\n  #scheduleDrain() {\n    if (this.#drainTimer) {\n      return;\n    }\n    // If we aren't using batching, no harm in sending it immediately\n    const stallTime = this._getOption(\"batchMaxCount\") === 1 ? 0 : this._getOption(\"batchStallTime\");\n    this.#drainTimer = setTimeout(() => {\n      this.#drainTimer = null;\n      const payloads = this.#payloads;\n      this.#payloads = [];\n      while (payloads.length) {\n        // Create payload batches that satisfy our batch constraints\n        const batch = [payloads.shift()];\n        while (payloads.length) {\n          if (batch.length === this.#options.batchMaxCount) {\n            break;\n          }\n          batch.push(payloads.shift());\n          const bytes = JSON.stringify(batch.map(p => p.payload));\n          if (bytes.length > this.#options.batchMaxSize) {\n            payloads.unshift(batch.pop());\n            break;\n          }\n        }\n        // Process the result to each payload\n        (async () => {\n          const payload = batch.length === 1 ? batch[0].payload : batch.map(p => p.payload);\n          this.emit(\"debug\", {\n            action: \"sendRpcPayload\",\n            payload\n          });\n          try {\n            const result = await this._send(payload);\n            this.emit(\"debug\", {\n              action: \"receiveRpcResult\",\n              result\n            });\n            // Process results in batch order\n            for (const {\n              resolve,\n              reject,\n              payload\n            } of batch) {\n              if (this.destroyed) {\n                reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", {\n                  operation: payload.method\n                }));\n                continue;\n              }\n              // Find the matching result\n              const resp = result.filter(r => r.id === payload.id)[0];\n              // No result; the node failed us in unexpected ways\n              if (resp == null) {\n                const error = makeError(\"missing response for request\", \"BAD_DATA\", {\n                  value: result,\n                  info: {\n                    payload\n                  }\n                });\n                this.emit(\"error\", error);\n                reject(error);\n                continue;\n              }\n              // The response is an error\n              if (\"error\" in resp) {\n                reject(this.getRpcError(payload, resp));\n                continue;\n              }\n              // All good; send the result\n              resolve(resp.result);\n            }\n          } catch (error) {\n            this.emit(\"debug\", {\n              action: \"receiveRpcError\",\n              error\n            });\n            for (const {\n              reject\n            } of batch) {\n              // @TODO: augment the error with the payload\n              reject(error);\n            }\n          }\n        })();\n      }\n    }, stallTime);\n  }\n  constructor(network, options) {\n    super(network, options);\n    this.#nextId = 1;\n    this.#options = Object.assign({}, defaultOptions, options || {});\n    this.#payloads = [];\n    this.#drainTimer = null;\n    this.#network = null;\n    this.#pendingDetectNetwork = null;\n    {\n      let resolve = null;\n      const promise = new Promise(_resolve => {\n        resolve = _resolve;\n      });\n      this.#notReady = {\n        promise,\n        resolve\n      };\n    }\n    const staticNetwork = this._getOption(\"staticNetwork\");\n    if (typeof staticNetwork === \"boolean\") {\n      assertArgument(!staticNetwork || network !== \"any\", \"staticNetwork cannot be used on special network 'any'\", \"options\", options);\n      if (staticNetwork && network != null) {\n        this.#network = Network.from(network);\n      }\n    } else if (staticNetwork) {\n      // Make sure any static network is compatbile with the provided netwrok\n      assertArgument(network == null || staticNetwork.matches(network), \"staticNetwork MUST match network object\", \"options\", options);\n      this.#network = staticNetwork;\n    }\n  }\n  /**\n   *  Returns the value associated with the option %%key%%.\n   *\n   *  Sub-classes can use this to inquire about configuration options.\n   */\n  _getOption(key) {\n    return this.#options[key];\n  }\n  /**\n   *  Gets the [[Network]] this provider has committed to. On each call, the network\n   *  is detected, and if it has changed, the call will reject.\n   */\n  get _network() {\n    assert(this.#network, \"network is not available yet\", \"NETWORK_ERROR\");\n    return this.#network;\n  }\n  /**\n   *  Resolves to the non-normalized value by performing %%req%%.\n   *\n   *  Sub-classes may override this to modify behavior of actions,\n   *  and should generally call ``super._perform`` as a fallback.\n   */\n  async _perform(req) {\n    // Legacy networks do not like the type field being passed along (which\n    // is fair), so we delete type if it is 0 and a non-EIP-1559 network\n    if (req.method === \"call\" || req.method === \"estimateGas\") {\n      let tx = req.transaction;\n      if (tx && tx.type != null && getBigInt(tx.type)) {\n        // If there are no EIP-1559 or newer properties, it might be pre-EIP-1559\n        if (tx.maxFeePerGas == null && tx.maxPriorityFeePerGas == null) {\n          const feeData = await this.getFeeData();\n          if (feeData.maxFeePerGas == null && feeData.maxPriorityFeePerGas == null) {\n            // Network doesn't know about EIP-1559 (and hence type)\n            req = Object.assign({}, req, {\n              transaction: Object.assign({}, tx, {\n                type: undefined\n              })\n            });\n          }\n        }\n      }\n    }\n    const request = this.getRpcRequest(req);\n    if (request != null) {\n      return await this.send(request.method, request.args);\n    }\n    return super._perform(req);\n  }\n  /**\n   *  Sub-classes may override this; it detects the *actual* network that\n   *  we are **currently** connected to.\n   *\n   *  Keep in mind that [[send]] may only be used once [[ready]], otherwise the\n   *  _send primitive must be used instead.\n   */\n  async _detectNetwork() {\n    const network = this._getOption(\"staticNetwork\");\n    if (network) {\n      if (network === true) {\n        if (this.#network) {\n          return this.#network;\n        }\n      } else {\n        return network;\n      }\n    }\n    if (this.#pendingDetectNetwork) {\n      return await this.#pendingDetectNetwork;\n    }\n    // If we are ready, use ``send``, which enabled requests to be batched\n    if (this.ready) {\n      this.#pendingDetectNetwork = (async () => {\n        try {\n          const result = Network.from(getBigInt(await this.send(\"eth_chainId\", [])));\n          this.#pendingDetectNetwork = null;\n          return result;\n        } catch (error) {\n          this.#pendingDetectNetwork = null;\n          throw error;\n        }\n      })();\n      return await this.#pendingDetectNetwork;\n    }\n    // We are not ready yet; use the primitive _send\n    this.#pendingDetectNetwork = (async () => {\n      const payload = {\n        id: this.#nextId++,\n        method: \"eth_chainId\",\n        params: [],\n        jsonrpc: \"2.0\"\n      };\n      this.emit(\"debug\", {\n        action: \"sendRpcPayload\",\n        payload\n      });\n      let result;\n      try {\n        result = (await this._send(payload))[0];\n        this.#pendingDetectNetwork = null;\n      } catch (error) {\n        this.#pendingDetectNetwork = null;\n        this.emit(\"debug\", {\n          action: \"receiveRpcError\",\n          error\n        });\n        throw error;\n      }\n      this.emit(\"debug\", {\n        action: \"receiveRpcResult\",\n        result\n      });\n      if (\"result\" in result) {\n        return Network.from(getBigInt(result.result));\n      }\n      throw this.getRpcError(payload, result);\n    })();\n    return await this.#pendingDetectNetwork;\n  }\n  /**\n   *  Sub-classes **MUST** call this. Until [[_start]] has been called, no calls\n   *  will be passed to [[_send]] from [[send]]. If it is overridden, then\n   *  ``super._start()`` **MUST** be called.\n   *\n   *  Calling it multiple times is safe and has no effect.\n   */\n  _start() {\n    if (this.#notReady == null || this.#notReady.resolve == null) {\n      return;\n    }\n    this.#notReady.resolve();\n    this.#notReady = null;\n    (async () => {\n      // Bootstrap the network\n      while (this.#network == null && !this.destroyed) {\n        try {\n          this.#network = await this._detectNetwork();\n        } catch (error) {\n          if (this.destroyed) {\n            break;\n          }\n          console.log(\"JsonRpcProvider failed to detect network and cannot start up; retry in 1s (perhaps the URL is wrong or the node is not started)\");\n          this.emit(\"error\", makeError(\"failed to bootstrap network detection\", \"NETWORK_ERROR\", {\n            event: \"initial-network-discovery\",\n            info: {\n              error\n            }\n          }));\n          await stall(1000);\n        }\n      }\n      // Start dispatching requests\n      this.#scheduleDrain();\n    })();\n  }\n  /**\n   *  Resolves once the [[_start]] has been called. This can be used in\n   *  sub-classes to defer sending data until the connection has been\n   *  established.\n   */\n  async _waitUntilReady() {\n    if (this.#notReady == null) {\n      return;\n    }\n    return await this.#notReady.promise;\n  }\n  /**\n   *  Return a Subscriber that will manage the %%sub%%.\n   *\n   *  Sub-classes may override this to modify the behavior of\n   *  subscription management.\n   */\n  _getSubscriber(sub) {\n    // Pending Filters aren't availble via polling\n    if (sub.type === \"pending\") {\n      return new FilterIdPendingSubscriber(this);\n    }\n    if (sub.type === \"event\") {\n      if (this._getOption(\"polling\")) {\n        return new PollingEventSubscriber(this, sub.filter);\n      }\n      return new FilterIdEventSubscriber(this, sub.filter);\n    }\n    // Orphaned Logs are handled automatically, by the filter, since\n    // logs with removed are emitted by it\n    if (sub.type === \"orphan\" && sub.filter.orphan === \"drop-log\") {\n      return new UnmanagedSubscriber(\"orphan\");\n    }\n    return super._getSubscriber(sub);\n  }\n  /**\n   *  Returns true only if the [[_start]] has been called.\n   */\n  get ready() {\n    return this.#notReady == null;\n  }\n  /**\n   *  Returns %%tx%% as a normalized JSON-RPC transaction request,\n   *  which has all values hexlified and any numeric values converted\n   *  to Quantity values.\n   */\n  getRpcTransaction(tx) {\n    const result = {};\n    // JSON-RPC now requires numeric values to be \"quantity\" values\n    [\"chainId\", \"gasLimit\", \"gasPrice\", \"type\", \"maxFeePerGas\", \"maxPriorityFeePerGas\", \"nonce\", \"value\"].forEach(key => {\n      if (tx[key] == null) {\n        return;\n      }\n      let dstKey = key;\n      if (key === \"gasLimit\") {\n        dstKey = \"gas\";\n      }\n      result[dstKey] = toQuantity(getBigInt(tx[key], `tx.${key}`));\n    });\n    // Make sure addresses and data are lowercase\n    [\"from\", \"to\", \"data\"].forEach(key => {\n      if (tx[key] == null) {\n        return;\n      }\n      result[key] = hexlify(tx[key]);\n    });\n    // Normalize the access list object\n    if (tx.accessList) {\n      result[\"accessList\"] = accessListify(tx.accessList);\n    }\n    if (tx.blobVersionedHashes) {\n      // @TODO: Remove this <any> case once EIP-4844 added to prepared tx\n      result[\"blobVersionedHashes\"] = tx.blobVersionedHashes.map(h => h.toLowerCase());\n    }\n    if (tx.authorizationList) {\n      result[\"authorizationList\"] = tx.authorizationList.map(_a => {\n        const a = authorizationify(_a);\n        return {\n          address: a.address,\n          nonce: toQuantity(a.nonce),\n          chainId: toQuantity(a.chainId),\n          yParity: toQuantity(a.signature.yParity),\n          r: toQuantity(a.signature.r),\n          s: toQuantity(a.signature.s)\n        };\n      });\n    }\n    // @TODO: blobs should probably also be copied over, optionally\n    // accounting for the kzg property to backfill blobVersionedHashes\n    // using the commitment. Or should that be left as an exercise to\n    // the caller?\n    return result;\n  }\n  /**\n   *  Returns the request method and arguments required to perform\n   *  %%req%%.\n   */\n  getRpcRequest(req) {\n    switch (req.method) {\n      case \"chainId\":\n        return {\n          method: \"eth_chainId\",\n          args: []\n        };\n      case \"getBlockNumber\":\n        return {\n          method: \"eth_blockNumber\",\n          args: []\n        };\n      case \"getGasPrice\":\n        return {\n          method: \"eth_gasPrice\",\n          args: []\n        };\n      case \"getPriorityFee\":\n        return {\n          method: \"eth_maxPriorityFeePerGas\",\n          args: []\n        };\n      case \"getBalance\":\n        return {\n          method: \"eth_getBalance\",\n          args: [getLowerCase(req.address), req.blockTag]\n        };\n      case \"getTransactionCount\":\n        return {\n          method: \"eth_getTransactionCount\",\n          args: [getLowerCase(req.address), req.blockTag]\n        };\n      case \"getCode\":\n        return {\n          method: \"eth_getCode\",\n          args: [getLowerCase(req.address), req.blockTag]\n        };\n      case \"getStorage\":\n        return {\n          method: \"eth_getStorageAt\",\n          args: [getLowerCase(req.address), \"0x\" + req.position.toString(16), req.blockTag]\n        };\n      case \"broadcastTransaction\":\n        return {\n          method: \"eth_sendRawTransaction\",\n          args: [req.signedTransaction]\n        };\n      case \"getBlock\":\n        if (\"blockTag\" in req) {\n          return {\n            method: \"eth_getBlockByNumber\",\n            args: [req.blockTag, !!req.includeTransactions]\n          };\n        } else if (\"blockHash\" in req) {\n          return {\n            method: \"eth_getBlockByHash\",\n            args: [req.blockHash, !!req.includeTransactions]\n          };\n        }\n        break;\n      case \"getTransaction\":\n        return {\n          method: \"eth_getTransactionByHash\",\n          args: [req.hash]\n        };\n      case \"getTransactionReceipt\":\n        return {\n          method: \"eth_getTransactionReceipt\",\n          args: [req.hash]\n        };\n      case \"call\":\n        return {\n          method: \"eth_call\",\n          args: [this.getRpcTransaction(req.transaction), req.blockTag]\n        };\n      case \"estimateGas\":\n        {\n          return {\n            method: \"eth_estimateGas\",\n            args: [this.getRpcTransaction(req.transaction)]\n          };\n        }\n      case \"getLogs\":\n        if (req.filter && req.filter.address != null) {\n          if (Array.isArray(req.filter.address)) {\n            req.filter.address = req.filter.address.map(getLowerCase);\n          } else {\n            req.filter.address = getLowerCase(req.filter.address);\n          }\n        }\n        return {\n          method: \"eth_getLogs\",\n          args: [req.filter]\n        };\n    }\n    return null;\n  }\n  /**\n   *  Returns an ethers-style Error for the given JSON-RPC error\n   *  %%payload%%, coalescing the various strings and error shapes\n   *  that different nodes return, coercing them into a machine-readable\n   *  standardized error.\n   */\n  getRpcError(payload, _error) {\n    const {\n      method\n    } = payload;\n    const {\n      error\n    } = _error;\n    if (method === \"eth_estimateGas\" && error.message) {\n      const msg = error.message;\n      if (!msg.match(/revert/i) && msg.match(/insufficient funds/i)) {\n        return makeError(\"insufficient funds\", \"INSUFFICIENT_FUNDS\", {\n          transaction: payload.params[0],\n          info: {\n            payload,\n            error\n          }\n        });\n      } else if (msg.match(/nonce/i) && msg.match(/too low/i)) {\n        return makeError(\"nonce has already been used\", \"NONCE_EXPIRED\", {\n          transaction: payload.params[0],\n          info: {\n            payload,\n            error\n          }\n        });\n      }\n    }\n    if (method === \"eth_call\" || method === \"eth_estimateGas\") {\n      const result = spelunkData(error);\n      const e = AbiCoder.getBuiltinCallException(method === \"eth_call\" ? \"call\" : \"estimateGas\", payload.params[0], result ? result.data : null);\n      e.info = {\n        error,\n        payload\n      };\n      return e;\n    }\n    // Only estimateGas and call can return arbitrary contract-defined text, so now we\n    // we can process text safely.\n    const message = JSON.stringify(spelunkMessage(error));\n    if (typeof error.message === \"string\" && error.message.match(/user denied|ethers-user-denied/i)) {\n      const actionMap = {\n        eth_sign: \"signMessage\",\n        personal_sign: \"signMessage\",\n        eth_signTypedData_v4: \"signTypedData\",\n        eth_signTransaction: \"signTransaction\",\n        eth_sendTransaction: \"sendTransaction\",\n        eth_requestAccounts: \"requestAccess\",\n        wallet_requestAccounts: \"requestAccess\"\n      };\n      return makeError(`user rejected action`, \"ACTION_REJECTED\", {\n        action: actionMap[method] || \"unknown\",\n        reason: \"rejected\",\n        info: {\n          payload,\n          error\n        }\n      });\n    }\n    if (method === \"eth_sendRawTransaction\" || method === \"eth_sendTransaction\") {\n      const transaction = payload.params[0];\n      if (message.match(/insufficient funds|base fee exceeds gas limit/i)) {\n        return makeError(\"insufficient funds for intrinsic transaction cost\", \"INSUFFICIENT_FUNDS\", {\n          transaction,\n          info: {\n            error\n          }\n        });\n      }\n      if (message.match(/nonce/i) && message.match(/too low/i)) {\n        return makeError(\"nonce has already been used\", \"NONCE_EXPIRED\", {\n          transaction,\n          info: {\n            error\n          }\n        });\n      }\n      // \"replacement transaction underpriced\"\n      if (message.match(/replacement transaction/i) && message.match(/underpriced/i)) {\n        return makeError(\"replacement fee too low\", \"REPLACEMENT_UNDERPRICED\", {\n          transaction,\n          info: {\n            error\n          }\n        });\n      }\n      if (message.match(/only replay-protected/i)) {\n        return makeError(\"legacy pre-eip-155 transactions not supported\", \"UNSUPPORTED_OPERATION\", {\n          operation: method,\n          info: {\n            transaction,\n            info: {\n              error\n            }\n          }\n        });\n      }\n    }\n    let unsupported = !!message.match(/the method .* does not exist/i);\n    if (!unsupported) {\n      if (error && error.details && error.details.startsWith(\"Unauthorized method:\")) {\n        unsupported = true;\n      }\n    }\n    if (unsupported) {\n      return makeError(\"unsupported operation\", \"UNSUPPORTED_OPERATION\", {\n        operation: payload.method,\n        info: {\n          error,\n          payload\n        }\n      });\n    }\n    return makeError(\"could not coalesce error\", \"UNKNOWN_ERROR\", {\n      error,\n      payload\n    });\n  }\n  /**\n   *  Requests the %%method%% with %%params%% via the JSON-RPC protocol\n   *  over the underlying channel. This can be used to call methods\n   *  on the backend that do not have a high-level API within the Provider\n   *  API.\n   *\n   *  This method queues requests according to the batch constraints\n   *  in the options, assigns the request a unique ID.\n   *\n   *  **Do NOT override** this method in sub-classes; instead\n   *  override [[_send]] or force the options values in the\n   *  call to the constructor to modify this method's behavior.\n   */\n  send(method, params) {\n    // @TODO: cache chainId?? purge on switch_networks\n    // We have been destroyed; no operations are supported anymore\n    if (this.destroyed) {\n      return Promise.reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", {\n        operation: method\n      }));\n    }\n    const id = this.#nextId++;\n    const promise = new Promise((resolve, reject) => {\n      this.#payloads.push({\n        resolve,\n        reject,\n        payload: {\n          method,\n          params,\n          id,\n          jsonrpc: \"2.0\"\n        }\n      });\n    });\n    // If there is not a pending drainTimer, set one\n    this.#scheduleDrain();\n    return promise;\n  }\n  /**\n   *  Resolves to the [[Signer]] account for  %%address%% managed by\n   *  the client.\n   *\n   *  If the %%address%% is a number, it is used as an index in the\n   *  the accounts from [[listAccounts]].\n   *\n   *  This can only be used on clients which manage accounts (such as\n   *  Geth with imported account or MetaMask).\n   *\n   *  Throws if the account doesn't exist.\n   */\n  async getSigner(address) {\n    if (address == null) {\n      address = 0;\n    }\n    const accountsPromise = this.send(\"eth_accounts\", []);\n    // Account index\n    if (typeof address === \"number\") {\n      const accounts = await accountsPromise;\n      if (address >= accounts.length) {\n        throw new Error(\"no such account\");\n      }\n      return new JsonRpcSigner(this, accounts[address]);\n    }\n    const {\n      accounts\n    } = await resolveProperties({\n      network: this.getNetwork(),\n      accounts: accountsPromise\n    });\n    // Account address\n    address = getAddress(address);\n    for (const account of accounts) {\n      if (getAddress(account) === address) {\n        return new JsonRpcSigner(this, address);\n      }\n    }\n    throw new Error(\"invalid account\");\n  }\n  async listAccounts() {\n    const accounts = await this.send(\"eth_accounts\", []);\n    return accounts.map(a => new JsonRpcSigner(this, a));\n  }\n  destroy() {\n    // Stop processing requests\n    if (this.#drainTimer) {\n      clearTimeout(this.#drainTimer);\n      this.#drainTimer = null;\n    }\n    // Cancel all pending requests\n    for (const {\n      payload,\n      reject\n    } of this.#payloads) {\n      reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", {\n        operation: payload.method\n      }));\n    }\n    this.#payloads = [];\n    // Parent clean-up\n    super.destroy();\n  }\n}\n// @TODO: remove this in v7, it is not exported because this functionality\n// is exposed in the JsonRpcApiProvider by setting polling to true. It should\n// be safe to remove regardless, because it isn't reachable, but just in case.\n/**\n *  @_ignore:\n */\nexport class JsonRpcApiPollingProvider extends JsonRpcApiProvider {\n  #pollingInterval;\n  constructor(network, options) {\n    super(network, options);\n    let pollingInterval = this._getOption(\"pollingInterval\");\n    if (pollingInterval == null) {\n      pollingInterval = defaultOptions.pollingInterval;\n    }\n    this.#pollingInterval = pollingInterval;\n  }\n  _getSubscriber(sub) {\n    const subscriber = super._getSubscriber(sub);\n    if (isPollable(subscriber)) {\n      subscriber.pollingInterval = this.#pollingInterval;\n    }\n    return subscriber;\n  }\n  /**\n   *  The polling interval (default: 4000 ms)\n   */\n  get pollingInterval() {\n    return this.#pollingInterval;\n  }\n  set pollingInterval(value) {\n    if (!Number.isInteger(value) || value < 0) {\n      throw new Error(\"invalid interval\");\n    }\n    this.#pollingInterval = value;\n    this._forEachSubscriber(sub => {\n      if (isPollable(sub)) {\n        sub.pollingInterval = this.#pollingInterval;\n      }\n    });\n  }\n}\n/**\n *  The JsonRpcProvider is one of the most common Providers,\n *  which performs all operations over HTTP (or HTTPS) requests.\n *\n *  Events are processed by polling the backend for the current block\n *  number; when it advances, all block-base events are then checked\n *  for updates.\n */\nexport class JsonRpcProvider extends JsonRpcApiPollingProvider {\n  #connect;\n  constructor(url, network, options) {\n    if (url == null) {\n      url = \"http:/\\/localhost:8545\";\n    }\n    super(network, options);\n    if (typeof url === \"string\") {\n      this.#connect = new FetchRequest(url);\n    } else {\n      this.#connect = url.clone();\n    }\n  }\n  _getConnection() {\n    return this.#connect.clone();\n  }\n  async send(method, params) {\n    // All requests are over HTTP, so we can just start handling requests\n    // We do this here rather than the constructor so that we don't send any\n    // requests to the network (i.e. eth_chainId) until we absolutely have to.\n    await this._start();\n    return await super.send(method, params);\n  }\n  async _send(payload) {\n    // Configure a POST connection for the requested method\n    const request = this._getConnection();\n    request.body = JSON.stringify(payload);\n    request.setHeader(\"content-type\", \"application/json\");\n    const response = await request.send();\n    response.assertOk();\n    let resp = response.bodyJson;\n    if (!Array.isArray(resp)) {\n      resp = [resp];\n    }\n    return resp;\n  }\n}\nfunction spelunkData(value) {\n  if (value == null) {\n    return null;\n  }\n  // These *are* the droids we're looking for.\n  if (typeof value.message === \"string\" && value.message.match(/revert/i) && isHexString(value.data)) {\n    return {\n      message: value.message,\n      data: value.data\n    };\n  }\n  // Spelunk further...\n  if (typeof value === \"object\") {\n    for (const key in value) {\n      const result = spelunkData(value[key]);\n      if (result) {\n        return result;\n      }\n    }\n    return null;\n  }\n  // Might be a JSON string we can further descend...\n  if (typeof value === \"string\") {\n    try {\n      return spelunkData(JSON.parse(value));\n    } catch (error) {}\n  }\n  return null;\n}\nfunction _spelunkMessage(value, result) {\n  if (value == null) {\n    return;\n  }\n  // These *are* the droids we're looking for.\n  if (typeof value.message === \"string\") {\n    result.push(value.message);\n  }\n  // Spelunk further...\n  if (typeof value === \"object\") {\n    for (const key in value) {\n      _spelunkMessage(value[key], result);\n    }\n  }\n  // Might be a JSON string we can further descend...\n  if (typeof value === \"string\") {\n    try {\n      return _spelunkMessage(JSON.parse(value), result);\n    } catch (error) {}\n  }\n}\nfunction spelunkMessage(value) {\n  const result = [];\n  _spelunkMessage(value, result);\n  return result;\n}", "map": {"version": 3, "names": ["AbiCoder", "get<PERSON><PERSON><PERSON>", "resolve<PERSON>ddress", "TypedDataEncoder", "accessListify", "authorizationify", "defineProperties", "getBigInt", "hexlify", "isHexString", "toQuantity", "toUtf8Bytes", "isError", "makeError", "assert", "assertArgument", "FetchRequest", "resolveProperties", "AbstractProvider", "UnmanagedSubscriber", "Abstract<PERSON><PERSON><PERSON>", "Network", "FilterIdEventSubscriber", "FilterIdPendingSubscriber", "PollingEventSubscriber", "Primitive", "split", "deepCopy", "value", "indexOf", "Array", "isArray", "map", "Object", "keys", "reduce", "accum", "key", "Error", "stall", "duration", "Promise", "resolve", "setTimeout", "getLowerCase", "toLowerCase", "isPollable", "pollingInterval", "defaultOptions", "polling", "staticNetwork", "batchStallTime", "batchMaxSize", "batchMaxCount", "cacheTimeout", "JsonRpcSigner", "address", "constructor", "provider", "connect", "operation", "populateTransaction", "tx", "populateCall", "sendUncheckedTransaction", "_tx", "promises", "from", "_from", "push", "gasLimit", "estimateGas", "to", "_to", "length", "all", "hexTx", "getRpcTransaction", "send", "sendTransaction", "blockNumber", "getBlockNumber", "hash", "reject", "timeouts", "invalids", "checkTx", "getTransaction", "replaceableTransaction", "error", "info", "sendTransactionHash", "emit", "_setTimeout", "pop", "signTransaction", "signMessage", "_message", "message", "signTypedData", "domain", "types", "_value", "populated", "resolveNames", "JSON", "stringify", "getPayload", "unlock", "password", "_legacySignMessage", "JsonRpcApiProvider", "options", "nextId", "payloads", "drainTimer", "notReady", "network", "pendingDetectNetwork", "scheduleDrain", "#scheduleDrain", "stallTime", "_getOption", "batch", "shift", "bytes", "p", "payload", "unshift", "action", "result", "_send", "destroyed", "method", "resp", "filter", "r", "id", "getRpcError", "assign", "promise", "_resolve", "matches", "_network", "_perform", "req", "transaction", "type", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "feeData", "getFeeData", "undefined", "request", "getRpcRequest", "args", "_detectNetwork", "ready", "params", "jsonrpc", "_start", "console", "log", "event", "_waitUntilReady", "_getSubscriber", "sub", "orphan", "for<PERSON>ach", "dst<PERSON>ey", "accessList", "blobVersionedHashes", "h", "authorizationList", "_a", "a", "nonce", "chainId", "yParity", "signature", "s", "blockTag", "position", "toString", "signedTransaction", "includeTransactions", "blockHash", "_error", "msg", "match", "spelunkData", "e", "getBuiltinCallException", "data", "spelunkMessage", "actionMap", "eth_sign", "personal_sign", "eth_signTypedData_v4", "eth_signTransaction", "eth_sendTransaction", "eth_requestAccounts", "wallet_requestAccounts", "reason", "unsupported", "details", "startsWith", "<PERSON><PERSON><PERSON><PERSON>", "accountsPromise", "accounts", "getNetwork", "account", "listAccounts", "destroy", "clearTimeout", "JsonRpcApiPollingProvider", "subscriber", "Number", "isInteger", "_forEachSubscriber", "JsonRpcProvider", "url", "clone", "_getConnection", "body", "<PERSON><PERSON><PERSON><PERSON>", "response", "assertOk", "bodyJson", "parse", "_spelunkMessage"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-jsonrpc.ts"], "sourcesContent": ["/**\n *  One of the most common ways to interact with the blockchain is\n *  by a node running a JSON-RPC interface which can be connected to,\n *  based on the transport, using:\n *\n *  - HTTP or HTTPS - [[JsonRpcProvider]]\n *  - WebSocket - [[WebSocketProvider]]\n *  - IPC - [[IpcSocketProvider]]\n *\n * @_section: api/providers/jsonrpc:JSON-RPC Provider  [about-jsonrpcProvider]\n */\n\n// @TODO:\n// - Add the batching API\n\n// https://playground.open-rpc.org/?schemaUrl=https://raw.githubusercontent.com/ethereum/eth1.0-apis/assembled-spec/openrpc.json&uiSchema%5BappBar%5D%5Bui:splitView%5D=true&uiSchema%5BappBar%5D%5Bui:input%5D=false&uiSchema%5BappBar%5D%5Bui:examplesDropdown%5D=false\n\nimport { AbiCoder } from \"../abi/index.js\";\nimport { getAddress, resolveAddress } from \"../address/index.js\";\nimport { TypedDataEncoder } from \"../hash/index.js\";\nimport { accessListify, authorizationify } from \"../transaction/index.js\";\nimport {\n    defineProperties, getBigInt, hexlify, isHexString, toQuantity, toUtf8Bytes,\n    isError, makeError, assert, assertArgument,\n    FetchRequest, resolveProperties\n} from \"../utils/index.js\";\n\nimport { AbstractProvider, UnmanagedSubscriber } from \"./abstract-provider.js\";\nimport { AbstractSigner } from \"./abstract-signer.js\";\nimport { Network } from \"./network.js\";\nimport { FilterIdEventSubscriber, FilterIdPendingSubscriber } from \"./subscriber-filterid.js\";\nimport { PollingEventSubscriber } from \"./subscriber-polling.js\";\n\nimport type { TypedDataDomain, TypedDataField } from \"../hash/index.js\";\nimport type { TransactionLike } from \"../transaction/index.js\";\n\nimport type { PerformActionRequest, Subscriber, Subscription } from \"./abstract-provider.js\";\nimport type { Networkish } from \"./network.js\";\nimport type { Provider, TransactionRequest, TransactionResponse } from \"./provider.js\";\nimport type { Signer } from \"./signer.js\";\n\ntype Timer = ReturnType<typeof setTimeout>;\n\nconst Primitive = \"bigint,boolean,function,number,string,symbol\".split(/,/g);\n//const Methods = \"getAddress,then\".split(/,/g);\nfunction deepCopy<T = any>(value: T): T {\n    if (value == null || Primitive.indexOf(typeof(value)) >= 0) {\n        return value;\n    }\n\n    // Keep any Addressable\n    if (typeof((<any>value).getAddress) === \"function\") {\n        return value;\n    }\n\n    if (Array.isArray(value)) { return <any>(value.map(deepCopy)); }\n\n    if (typeof(value) === \"object\") {\n        return Object.keys(value).reduce((accum, key) => {\n            accum[key] = (<any>value)[key];\n            return accum;\n        }, <any>{ });\n    }\n\n    throw new Error(`should not happen: ${ value } (${ typeof(value) })`);\n}\n\nfunction stall(duration: number): Promise<void> {\n    return new Promise((resolve) => { setTimeout(resolve, duration); });\n}\n\nfunction getLowerCase(value: string): string {\n    if (value) { return value.toLowerCase(); }\n    return value;\n}\n\ninterface Pollable {\n    pollingInterval: number;\n}\n\nfunction isPollable(value: any): value is Pollable {\n    return (value && typeof(value.pollingInterval) === \"number\");\n}\n\n/**\n *  A JSON-RPC payload, which are sent to a JSON-RPC server.\n */\nexport type JsonRpcPayload = {\n    /**\n     *  The JSON-RPC request ID.\n     */\n    id: number;\n\n    /**\n     *  The JSON-RPC request method.\n     */\n    method: string;\n\n    /**\n     *  The JSON-RPC request parameters.\n     */\n    params: Array<any> | Record<string, any>;\n\n    /**\n     *  A required constant in the JSON-RPC specification.\n     */\n    jsonrpc: \"2.0\";\n};\n\n/**\n *  A JSON-RPC result, which are returned on success from a JSON-RPC server.\n */\nexport type JsonRpcResult = {\n    /**\n     *  The response ID to match it to the relevant request.\n     */\n    id: number;\n\n    /**\n     *  The response result.\n     */\n    result: any;\n};\n\n/**\n *  A JSON-RPC error, which are returned on failure from a JSON-RPC server.\n */\nexport type JsonRpcError = {\n    /**\n     *  The response ID to match it to the relevant request.\n     */\n    id: number;\n\n    /**\n     *  The response error.\n     */\n    error: {\n        code: number;\n        message?: string;\n        data?: any;\n    }\n};\n\n/**\n *  When subscribing to the ``\"debug\"`` event, the [[Listener]] will\n *  receive this object as the first parameter.\n */\nexport type DebugEventJsonRpcApiProvider = {\n    action: \"sendRpcPayload\",\n    payload: JsonRpcPayload | Array<JsonRpcPayload>\n} | {\n    action: \"receiveRpcResult\",\n    result: Array<JsonRpcResult | JsonRpcError>\n} | {\n    action: \"receiveRpcError\",\n    error: Error\n};\n\n/**\n *  Options for configuring a [[JsonRpcApiProvider]]. Much of this\n *  is targetted towards sub-classes, which often will not expose\n *  any of these options to their consumers.\n *\n *  **``polling``** - use the polling strategy is used immediately\n *  for events; otherwise, attempt to use filters and fall back onto\n *  polling (default: ``false``)\n *\n *  **``staticNetwork``** - do not request chain ID on requests to\n *  validate the underlying chain has not changed (default: ``null``)\n *\n *  This should **ONLY** be used if it is **certain** that the network\n *  cannot change, such as when using INFURA (since the URL dictates the\n *  network). If the network is assumed static and it does change, this\n *  can have tragic consequences. For example, this **CANNOT** be used\n *  with MetaMask, since the user can select a new network from the\n *  drop-down at any time.\n *\n *  **``batchStallTime``** - how long (ms) to aggregate requests into a\n *  single batch. ``0`` indicates batching will only encompass the current\n *  event loop. If ``batchMaxCount = 1``, this is ignored. (default: ``10``)\n *\n *  **``batchMaxSize``** - target maximum size (bytes) to allow per batch\n *  request (default: 1Mb)\n *\n *  **``batchMaxCount``** - maximum number of requests to allow in a batch.\n *  If ``batchMaxCount = 1``, then batching is disabled. (default: ``100``)\n *\n *  **``cacheTimeout``** - passed as [[AbstractProviderOptions]].\n */\nexport type JsonRpcApiProviderOptions = {\n    polling?: boolean;\n    staticNetwork?: null | boolean | Network;\n    batchStallTime?: number;\n    batchMaxSize?: number;\n    batchMaxCount?: number;\n\n    cacheTimeout?: number;\n    pollingInterval?: number;\n};\n\nconst defaultOptions = {\n    polling: false,\n    staticNetwork: null,\n\n    batchStallTime: 10,      // 10ms\n    batchMaxSize: (1 << 20), // 1Mb\n    batchMaxCount: 100,      // 100 requests\n\n    cacheTimeout: 250,\n    pollingInterval: 4000\n}\n\n/**\n *  A **JsonRpcTransactionRequest** is formatted as needed by the JSON-RPC\n *  Ethereum API specification.\n */\nexport interface JsonRpcTransactionRequest {\n     /**\n      *  The sender address to use when signing.\n      */\n     from?: string;\n\n     /**\n      *  The target address.\n      */\n     to?: string;\n\n     /**\n      *  The transaction data.\n      */\n     data?: string;\n\n     /**\n      *  The chain ID the transaction is valid on.\n      */\n     chainId?: string;\n\n     /**\n      *  The [[link-eip-2718]] transaction type.\n      */\n     type?: string;\n\n     /**\n      *  The maximum amount of gas to allow a transaction to consume.\n      *\n      *  In most other places in ethers, this is called ``gasLimit`` which\n      *  differs from the JSON-RPC Ethereum API specification.\n      */\n     gas?: string;\n\n     /**\n      *  The gas price per wei for transactions prior to [[link-eip-1559]].\n      */\n     gasPrice?: string;\n\n     /**\n      *  The maximum fee per gas for [[link-eip-1559]] transactions.\n      */\n     maxFeePerGas?: string;\n\n     /**\n      *  The maximum priority fee per gas for [[link-eip-1559]] transactions.\n      */\n     maxPriorityFeePerGas?: string;\n\n     /**\n      *  The nonce for the transaction.\n      */\n     nonce?: string;\n\n     /**\n      *  The transaction value (in wei).\n      */\n     value?: string;\n\n     /**\n      *  The transaction access list.\n      */\n     accessList?: Array<{ address: string, storageKeys: Array<string> }>;\n\n     /**\n      *  The transaction authorization list.\n      */\n     authorizationList?: Array<{\n         address: string, nonce: string, chainId: string,\n         yParity: string, r: string, s: string\n     }>;\n}\n\n// @TODO: Unchecked Signers\n\nexport class JsonRpcSigner extends AbstractSigner<JsonRpcApiProvider> {\n    address!: string;\n\n    constructor(provider: JsonRpcApiProvider, address: string) {\n        super(provider);\n        address = getAddress(address);\n        defineProperties<JsonRpcSigner>(this, { address });\n    }\n\n    connect(provider: null | Provider): Signer {\n        assert(false, \"cannot reconnect JsonRpcSigner\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"signer.connect\"\n        });\n    }\n\n    async getAddress(): Promise<string> {\n        return this.address;\n    }\n\n    // JSON-RPC will automatially fill in nonce, etc. so we just check from\n    async populateTransaction(tx: TransactionRequest): Promise<TransactionLike<string>> {\n        return await this.populateCall(tx);\n    }\n\n    // Returns just the hash of the transaction after sent, which is what\n    // the bare JSON-RPC API does;\n    async sendUncheckedTransaction(_tx: TransactionRequest): Promise<string> {\n        const tx = deepCopy(_tx);\n\n        const promises: Array<Promise<void>> = [];\n\n        // Make sure the from matches the sender\n        if (tx.from) {\n            const _from = tx.from;\n            promises.push((async () => {\n                const from = await resolveAddress(_from, this.provider);\n                assertArgument(from != null && from.toLowerCase() === this.address.toLowerCase(),\n                    \"from address mismatch\", \"transaction\", _tx);\n                tx.from = from;\n            })());\n        } else {\n            tx.from = this.address;\n        }\n\n        // The JSON-RPC for eth_sendTransaction uses 90000 gas; if the user\n        // wishes to use this, it is easy to specify explicitly, otherwise\n        // we look it up for them.\n        if (tx.gasLimit == null) {\n            promises.push((async () => {\n                tx.gasLimit = await this.provider.estimateGas({ ...tx, from: this.address});\n            })());\n        }\n\n        // The address may be an ENS name or Addressable\n        if (tx.to != null) {\n            const _to = tx.to;\n            promises.push((async () => {\n                tx.to = await resolveAddress(_to, this.provider);\n            })());\n        }\n\n        // Wait until all of our properties are filled in\n        if (promises.length) { await Promise.all(promises); }\n\n        const hexTx = this.provider.getRpcTransaction(tx);\n\n        return this.provider.send(\"eth_sendTransaction\", [ hexTx ]);\n    }\n\n    async sendTransaction(tx: TransactionRequest): Promise<TransactionResponse> {\n        // This cannot be mined any earlier than any recent block\n        const blockNumber = await this.provider.getBlockNumber();\n\n        // Send the transaction\n        const hash = await this.sendUncheckedTransaction(tx);\n\n        // Unfortunately, JSON-RPC only provides and opaque transaction hash\n        // for a response, and we need the actual transaction, so we poll\n        // for it; it should show up very quickly\n        return await (new Promise((resolve, reject) => {\n            const timeouts = [ 1000, 100 ];\n            let invalids = 0;\n\n            const checkTx = async () => {\n\n                try {\n                    // Try getting the transaction\n                    const tx = await this.provider.getTransaction(hash);\n\n                    if (tx != null) {\n                        resolve(tx.replaceableTransaction(blockNumber));\n                        return;\n                    }\n\n                } catch (error) {\n\n                    // If we were cancelled: stop polling.\n                    // If the data is bad: the node returns bad transactions\n                    // If the network changed: calling again will also fail\n                    // If unsupported: likely destroyed\n                    if (isError(error, \"CANCELLED\") || isError(error, \"BAD_DATA\") ||\n                        isError(error, \"NETWORK_ERROR\") || isError(error, \"UNSUPPORTED_OPERATION\")) {\n\n                        if (error.info == null) { error.info = { }; }\n                        error.info.sendTransactionHash = hash;\n\n                        reject(error);\n                        return;\n                    }\n\n                    // Stop-gap for misbehaving backends; see #4513\n                    if (isError(error, \"INVALID_ARGUMENT\")) {\n                        invalids++;\n                        if (error.info == null) { error.info = { }; }\n                        error.info.sendTransactionHash = hash;\n                        if (invalids > 10) {\n                            reject(error);\n                            return;\n                        }\n                    }\n\n                    // Notify anyone that cares; but we will try again, since\n                    // it is likely an intermittent service error\n                    this.provider.emit(\"error\", makeError(\"failed to fetch transation after sending (will try again)\", \"UNKNOWN_ERROR\", { error }));\n                }\n\n                // Wait another 4 seconds\n                this.provider._setTimeout(() => { checkTx(); }, timeouts.pop() || 4000);\n            };\n            checkTx();\n        }));\n    }\n\n    async signTransaction(_tx: TransactionRequest): Promise<string> {\n        const tx = deepCopy(_tx);\n\n        // Make sure the from matches the sender\n        if (tx.from) {\n            const from = await resolveAddress(tx.from, this.provider);\n            assertArgument(from != null && from.toLowerCase() === this.address.toLowerCase(),\n                \"from address mismatch\", \"transaction\", _tx);\n            tx.from = from;\n        } else {\n            tx.from = this.address;\n        }\n\n        const hexTx = this.provider.getRpcTransaction(tx);\n        return await this.provider.send(\"eth_signTransaction\", [ hexTx ]);\n    }\n\n\n    async signMessage(_message: string | Uint8Array): Promise<string> {\n        const message = ((typeof(_message) === \"string\") ? toUtf8Bytes(_message): _message);\n        return await this.provider.send(\"personal_sign\", [\n            hexlify(message), this.address.toLowerCase() ]);\n    }\n\n    async signTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, _value: Record<string, any>): Promise<string> {\n        const value = deepCopy(_value);\n\n        // Populate any ENS names (in-place)\n        const populated = await TypedDataEncoder.resolveNames(domain, types, value, async (value: string) => {\n            const address = await resolveAddress(value);\n            assertArgument(address != null, \"TypedData does not support null address\", \"value\", value);\n            return address;\n        });\n\n        return await this.provider.send(\"eth_signTypedData_v4\", [\n            this.address.toLowerCase(),\n            JSON.stringify(TypedDataEncoder.getPayload(populated.domain, types, populated.value))\n        ]);\n    }\n\n    async unlock(password: string): Promise<boolean> {\n        return this.provider.send(\"personal_unlockAccount\", [\n            this.address.toLowerCase(), password, null ]);\n    }\n\n    // https://github.com/ethereum/wiki/wiki/JSON-RPC#eth_sign\n    async _legacySignMessage(_message: string | Uint8Array): Promise<string> {\n        const message = ((typeof(_message) === \"string\") ? toUtf8Bytes(_message): _message);\n        return await this.provider.send(\"eth_sign\", [\n            this.address.toLowerCase(), hexlify(message) ]);\n    }\n}\n\ntype ResolveFunc = (result: JsonRpcResult) => void;\ntype RejectFunc = (error: Error) => void;\n\ntype Payload = { payload: JsonRpcPayload, resolve: ResolveFunc, reject: RejectFunc };\n\n/**\n *  The JsonRpcApiProvider is an abstract class and **MUST** be\n *  sub-classed.\n *\n *  It provides the base for all JSON-RPC-based Provider interaction.\n *\n *  Sub-classing Notes:\n *  - a sub-class MUST override _send\n *  - a sub-class MUST call the `_start()` method once connected\n */\nexport abstract class JsonRpcApiProvider extends AbstractProvider {\n\n    #options: Required<JsonRpcApiProviderOptions>;\n\n    // The next ID to use for the JSON-RPC ID field\n    #nextId: number;\n\n    // Payloads are queued and triggered in batches using the drainTimer\n    #payloads: Array<Payload>;\n    #drainTimer: null | Timer;\n\n    #notReady: null | {\n        promise: Promise<void>,\n        resolve: null | ((v: void) => void)\n    };\n\n    #network: null | Network;\n    #pendingDetectNetwork: null | Promise<Network>;\n\n    #scheduleDrain(): void {\n        if (this.#drainTimer) { return; }\n\n        // If we aren't using batching, no harm in sending it immediately\n        const stallTime = (this._getOption(\"batchMaxCount\") === 1) ? 0: this._getOption(\"batchStallTime\");\n\n        this.#drainTimer = setTimeout(() => {\n            this.#drainTimer = null;\n\n            const payloads = this.#payloads;\n            this.#payloads = [ ];\n\n            while (payloads.length) {\n\n                // Create payload batches that satisfy our batch constraints\n                const batch = [ <Payload>(payloads.shift()) ];\n                while (payloads.length) {\n                    if (batch.length === this.#options.batchMaxCount) { break; }\n                    batch.push(<Payload>(payloads.shift()));\n                    const bytes = JSON.stringify(batch.map((p) => p.payload));\n                    if (bytes.length > this.#options.batchMaxSize) {\n                        payloads.unshift(<Payload>(batch.pop()));\n                        break;\n                    }\n                }\n\n                // Process the result to each payload\n                (async () => {\n                    const payload = ((batch.length === 1) ? batch[0].payload: batch.map((p) => p.payload));\n\n                    this.emit(\"debug\", { action: \"sendRpcPayload\", payload });\n\n                    try {\n                        const result = await this._send(payload);\n                        this.emit(\"debug\", { action: \"receiveRpcResult\", result });\n\n                        // Process results in batch order\n                        for (const { resolve, reject, payload } of batch) {\n\n                            if (this.destroyed) {\n                                reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", { operation: payload.method }));\n                                continue;\n                            }\n\n                            // Find the matching result\n                            const resp = result.filter((r) => (r.id === payload.id))[0];\n\n                            // No result; the node failed us in unexpected ways\n                            if (resp == null) {\n                                const error = makeError(\"missing response for request\", \"BAD_DATA\", {\n                                    value: result, info: { payload }\n                                });\n                                this.emit(\"error\", error);\n                                reject(error);\n                                continue;\n                            }\n\n                            // The response is an error\n                            if (\"error\" in resp) {\n                                reject(this.getRpcError(payload, resp));\n                                continue;\n                            }\n\n                            // All good; send the result\n                            resolve(resp.result);\n                        }\n\n                    } catch (error: any) {\n                        this.emit(\"debug\", { action: \"receiveRpcError\", error });\n\n                        for (const { reject } of batch) {\n                            // @TODO: augment the error with the payload\n                            reject(error);\n                        }\n                    }\n                })();\n            }\n        }, stallTime);\n    }\n\n    constructor(network?: Networkish, options?: JsonRpcApiProviderOptions) {\n        super(network, options);\n\n        this.#nextId = 1;\n        this.#options = Object.assign({ }, defaultOptions, options || { });\n\n        this.#payloads = [ ];\n        this.#drainTimer = null;\n\n        this.#network = null;\n        this.#pendingDetectNetwork = null;\n\n        {\n            let resolve: null | ((value: void) => void) = null;\n            const promise = new Promise((_resolve: (value: void) => void) => {\n                resolve = _resolve;\n            });\n            this.#notReady = { promise, resolve };\n        }\n\n        const staticNetwork = this._getOption(\"staticNetwork\");\n        if (typeof(staticNetwork) === \"boolean\") {\n            assertArgument(!staticNetwork || network !== \"any\", \"staticNetwork cannot be used on special network 'any'\", \"options\", options);\n            if (staticNetwork && network != null) {\n                this.#network = Network.from(network);\n            }\n\n        } else if (staticNetwork) {\n            // Make sure any static network is compatbile with the provided netwrok\n            assertArgument(network == null || staticNetwork.matches(network),\n                \"staticNetwork MUST match network object\", \"options\", options);\n            this.#network = staticNetwork;\n        }\n    }\n\n    /**\n     *  Returns the value associated with the option %%key%%.\n     *\n     *  Sub-classes can use this to inquire about configuration options.\n     */\n    _getOption<K extends keyof JsonRpcApiProviderOptions>(key: K): JsonRpcApiProviderOptions[K] {\n        return this.#options[key];\n    }\n\n    /**\n     *  Gets the [[Network]] this provider has committed to. On each call, the network\n     *  is detected, and if it has changed, the call will reject.\n     */\n    get _network(): Network {\n        assert (this.#network, \"network is not available yet\", \"NETWORK_ERROR\");\n        return this.#network;\n    }\n\n    /**\n     *  Sends a JSON-RPC %%payload%% (or a batch) to the underlying channel.\n     *\n     *  Sub-classes **MUST** override this.\n     */\n    abstract _send(payload: JsonRpcPayload | Array<JsonRpcPayload>): Promise<Array<JsonRpcResult | JsonRpcError>>;\n\n\n    /**\n     *  Resolves to the non-normalized value by performing %%req%%.\n     *\n     *  Sub-classes may override this to modify behavior of actions,\n     *  and should generally call ``super._perform`` as a fallback.\n     */\n    async _perform(req: PerformActionRequest): Promise<any> {\n\n        // Legacy networks do not like the type field being passed along (which\n        // is fair), so we delete type if it is 0 and a non-EIP-1559 network\n        if (req.method === \"call\" || req.method === \"estimateGas\") {\n            let tx = req.transaction;\n            if (tx && tx.type != null && getBigInt(tx.type)) {\n                // If there are no EIP-1559 or newer properties, it might be pre-EIP-1559\n                if (tx.maxFeePerGas == null && tx.maxPriorityFeePerGas == null) {\n                    const feeData = await this.getFeeData();\n                    if (feeData.maxFeePerGas == null && feeData.maxPriorityFeePerGas == null) {\n                        // Network doesn't know about EIP-1559 (and hence type)\n                        req = Object.assign({ }, req, {\n                            transaction: Object.assign({ }, tx, { type: undefined })\n                        });\n                    }\n                }\n            }\n        }\n\n        const request = this.getRpcRequest(req);\n\n        if (request != null) {\n            return await this.send(request.method, request.args);\n        }\n\n        return super._perform(req);\n    }\n\n    /**\n     *  Sub-classes may override this; it detects the *actual* network that\n     *  we are **currently** connected to.\n     *\n     *  Keep in mind that [[send]] may only be used once [[ready]], otherwise the\n     *  _send primitive must be used instead.\n     */\n    async _detectNetwork(): Promise<Network> {\n        const network = this._getOption(\"staticNetwork\");\n        if (network) {\n            if (network === true) {\n                if (this.#network) { return this.#network; }\n            } else {\n                return network;\n            }\n        }\n\n        if (this.#pendingDetectNetwork) {\n            return await this.#pendingDetectNetwork;\n        }\n\n        // If we are ready, use ``send``, which enabled requests to be batched\n        if (this.ready) {\n            this.#pendingDetectNetwork = (async () => {\n                try {\n                    const result = Network.from(getBigInt(await this.send(\"eth_chainId\", [ ])));\n                    this.#pendingDetectNetwork = null;\n                    return result;\n                } catch (error) {\n                    this.#pendingDetectNetwork = null;\n                    throw error;\n                }\n            })();\n            return await this.#pendingDetectNetwork;\n        }\n\n        // We are not ready yet; use the primitive _send\n        this.#pendingDetectNetwork = (async () => {\n            const payload: JsonRpcPayload = {\n                id: this.#nextId++, method: \"eth_chainId\", params: [ ], jsonrpc: \"2.0\"\n            };\n\n            this.emit(\"debug\", { action: \"sendRpcPayload\", payload });\n\n            let result: JsonRpcResult | JsonRpcError;\n            try {\n                result = (await this._send(payload))[0];\n                this.#pendingDetectNetwork = null;\n            } catch (error) {\n                this.#pendingDetectNetwork = null;\n                this.emit(\"debug\", { action: \"receiveRpcError\", error });\n                throw error;\n            }\n\n            this.emit(\"debug\", { action: \"receiveRpcResult\", result });\n\n            if (\"result\" in result) {\n                return Network.from(getBigInt(result.result));\n            }\n\n            throw this.getRpcError(payload, result);\n        })();\n\n        return await this.#pendingDetectNetwork;\n    }\n\n    /**\n     *  Sub-classes **MUST** call this. Until [[_start]] has been called, no calls\n     *  will be passed to [[_send]] from [[send]]. If it is overridden, then\n     *  ``super._start()`` **MUST** be called.\n     *\n     *  Calling it multiple times is safe and has no effect.\n     */\n    _start(): void {\n        if (this.#notReady == null || this.#notReady.resolve == null) { return; }\n\n        this.#notReady.resolve();\n        this.#notReady = null;\n\n        (async () => {\n\n            // Bootstrap the network\n            while (this.#network == null && !this.destroyed) {\n                try {\n                    this.#network = await this._detectNetwork();\n                } catch (error) {\n                    if (this.destroyed) { break; }\n                    console.log(\"JsonRpcProvider failed to detect network and cannot start up; retry in 1s (perhaps the URL is wrong or the node is not started)\");\n                    this.emit(\"error\", makeError(\"failed to bootstrap network detection\", \"NETWORK_ERROR\", { event: \"initial-network-discovery\", info: { error } }));\n                    await stall(1000);\n                }\n            }\n\n            // Start dispatching requests\n            this.#scheduleDrain();\n        })();\n    }\n\n    /**\n     *  Resolves once the [[_start]] has been called. This can be used in\n     *  sub-classes to defer sending data until the connection has been\n     *  established.\n     */\n    async _waitUntilReady(): Promise<void> {\n        if (this.#notReady == null) { return; }\n        return await this.#notReady.promise;\n    }\n\n\n    /**\n     *  Return a Subscriber that will manage the %%sub%%.\n     *\n     *  Sub-classes may override this to modify the behavior of\n     *  subscription management.\n     */\n    _getSubscriber(sub: Subscription): Subscriber {\n\n        // Pending Filters aren't availble via polling\n        if (sub.type === \"pending\") { return new FilterIdPendingSubscriber(this); }\n\n        if (sub.type === \"event\") {\n            if (this._getOption(\"polling\")) {\n                return new PollingEventSubscriber(this, sub.filter);\n            }\n            return new FilterIdEventSubscriber(this, sub.filter);\n        }\n\n        // Orphaned Logs are handled automatically, by the filter, since\n        // logs with removed are emitted by it\n        if (sub.type === \"orphan\" && sub.filter.orphan === \"drop-log\") {\n            return new UnmanagedSubscriber(\"orphan\");\n        }\n\n        return super._getSubscriber(sub);\n    }\n\n    /**\n     *  Returns true only if the [[_start]] has been called.\n     */\n    get ready(): boolean { return this.#notReady == null; }\n\n    /**\n     *  Returns %%tx%% as a normalized JSON-RPC transaction request,\n     *  which has all values hexlified and any numeric values converted\n     *  to Quantity values.\n     */\n    getRpcTransaction(tx: TransactionRequest): JsonRpcTransactionRequest {\n        const result: JsonRpcTransactionRequest = {};\n\n        // JSON-RPC now requires numeric values to be \"quantity\" values\n        [\"chainId\", \"gasLimit\", \"gasPrice\", \"type\", \"maxFeePerGas\", \"maxPriorityFeePerGas\", \"nonce\", \"value\"].forEach((key) => {\n            if ((<any>tx)[key] == null) { return; }\n            let dstKey = key;\n            if (key === \"gasLimit\") { dstKey = \"gas\"; }\n            (<any>result)[dstKey] = toQuantity(getBigInt((<any>tx)[key], `tx.${ key }`));\n        });\n\n        // Make sure addresses and data are lowercase\n        [\"from\", \"to\", \"data\"].forEach((key) => {\n            if ((<any>tx)[key] == null) { return; }\n            (<any>result)[key] = hexlify((<any>tx)[key]);\n        });\n\n        // Normalize the access list object\n        if (tx.accessList) {\n            result[\"accessList\"] = accessListify(tx.accessList);\n        }\n\n        if (tx.blobVersionedHashes) {\n            // @TODO: Remove this <any> case once EIP-4844 added to prepared tx\n            (<any>result)[\"blobVersionedHashes\"] = tx.blobVersionedHashes.map(h => h.toLowerCase());\n        }\n\n        if (tx.authorizationList) {\n            result[\"authorizationList\"] = tx.authorizationList.map((_a) => {\n                const a = authorizationify(_a);\n                return {\n                    address: a.address,\n                    nonce: toQuantity(a.nonce),\n                    chainId: toQuantity(a.chainId),\n                    yParity: toQuantity(a.signature.yParity),\n                    r: toQuantity(a.signature.r),\n                    s: toQuantity(a.signature.s),\n                }\n            });\n        }\n\n        // @TODO: blobs should probably also be copied over, optionally\n        // accounting for the kzg property to backfill blobVersionedHashes\n        // using the commitment. Or should that be left as an exercise to\n        // the caller?\n\n        return result;\n    }\n\n    /**\n     *  Returns the request method and arguments required to perform\n     *  %%req%%.\n     */\n    getRpcRequest(req: PerformActionRequest): null | { method: string, args: Array<any> } {\n        switch (req.method) {\n            case \"chainId\":\n                return { method: \"eth_chainId\", args: [ ] };\n\n            case \"getBlockNumber\":\n                return { method: \"eth_blockNumber\", args: [ ] };\n\n            case \"getGasPrice\":\n                return { method: \"eth_gasPrice\", args: [] };\n\n            case \"getPriorityFee\":\n                return { method: \"eth_maxPriorityFeePerGas\", args: [ ] };\n\n            case \"getBalance\":\n                return {\n                    method: \"eth_getBalance\",\n                    args: [ getLowerCase(req.address), req.blockTag ]\n                };\n\n            case \"getTransactionCount\":\n                return {\n                    method: \"eth_getTransactionCount\",\n                    args: [ getLowerCase(req.address), req.blockTag ]\n                };\n\n            case \"getCode\":\n                return {\n                    method: \"eth_getCode\",\n                    args: [ getLowerCase(req.address), req.blockTag ]\n                };\n\n            case \"getStorage\":\n                return {\n                    method: \"eth_getStorageAt\",\n                    args: [\n                        getLowerCase(req.address),\n                        (\"0x\" + req.position.toString(16)),\n                        req.blockTag\n                    ]\n                };\n\n            case \"broadcastTransaction\":\n                return {\n                    method: \"eth_sendRawTransaction\",\n                    args: [ req.signedTransaction ]\n                };\n\n            case \"getBlock\":\n                if (\"blockTag\" in req) {\n                    return {\n                        method: \"eth_getBlockByNumber\",\n                        args: [ req.blockTag, !!req.includeTransactions ]\n                    };\n                } else if (\"blockHash\" in req) {\n                    return {\n                        method: \"eth_getBlockByHash\",\n                        args: [ req.blockHash, !!req.includeTransactions ]\n                    };\n                }\n                break;\n\n            case \"getTransaction\":\n                return {\n                    method: \"eth_getTransactionByHash\",\n                    args: [ req.hash ]\n                };\n\n            case \"getTransactionReceipt\":\n                return {\n                    method: \"eth_getTransactionReceipt\",\n                    args: [ req.hash ]\n                };\n\n            case \"call\":\n                return {\n                    method: \"eth_call\",\n                    args: [ this.getRpcTransaction(req.transaction), req.blockTag ]\n                };\n\n            case \"estimateGas\": {\n                return {\n                    method: \"eth_estimateGas\",\n                    args: [ this.getRpcTransaction(req.transaction) ]\n                };\n            }\n\n            case \"getLogs\":\n                if (req.filter && req.filter.address != null) {\n                    if (Array.isArray(req.filter.address)) {\n                        req.filter.address = req.filter.address.map(getLowerCase);\n                    } else {\n                        req.filter.address = getLowerCase(req.filter.address);\n                    }\n                }\n                return { method: \"eth_getLogs\", args: [ req.filter ] };\n        }\n\n        return null;\n    }\n\n    /**\n     *  Returns an ethers-style Error for the given JSON-RPC error\n     *  %%payload%%, coalescing the various strings and error shapes\n     *  that different nodes return, coercing them into a machine-readable\n     *  standardized error.\n     */\n    getRpcError(payload: JsonRpcPayload, _error: JsonRpcError): Error {\n        const { method } = payload;\n        const { error } = _error;\n\n        if (method === \"eth_estimateGas\" && error.message) {\n            const msg = error.message;\n            if (!msg.match(/revert/i) && msg.match(/insufficient funds/i)) {\n                return makeError(\"insufficient funds\", \"INSUFFICIENT_FUNDS\", {\n                    transaction: ((<any>payload).params[0]),\n                    info: { payload, error }\n                });\n            } else if (msg.match(/nonce/i) && msg.match(/too low/i)) {\n                return makeError(\"nonce has already been used\", \"NONCE_EXPIRED\", {\n                    transaction: ((<any>payload).params[0]),\n                    info: { payload, error }\n                });\n            }\n        }\n\n        if (method === \"eth_call\" || method === \"eth_estimateGas\") {\n            const result = spelunkData(error);\n\n            const e = AbiCoder.getBuiltinCallException(\n                (method === \"eth_call\") ? \"call\": \"estimateGas\",\n                ((<any>payload).params[0]),\n                (result ? result.data: null)\n            );\n            e.info = { error, payload };\n            return e;\n        }\n\n        // Only estimateGas and call can return arbitrary contract-defined text, so now we\n        // we can process text safely.\n\n        const message = JSON.stringify(spelunkMessage(error));\n\n        if (typeof(error.message) === \"string\" && error.message.match(/user denied|ethers-user-denied/i)) {\n            const actionMap: Record<string, \"requestAccess\" | \"sendTransaction\" | \"signMessage\" | \"signTransaction\" | \"signTypedData\"> = {\n                eth_sign: \"signMessage\",\n                personal_sign: \"signMessage\",\n                eth_signTypedData_v4: \"signTypedData\",\n                eth_signTransaction: \"signTransaction\",\n                eth_sendTransaction: \"sendTransaction\",\n                eth_requestAccounts: \"requestAccess\",\n                wallet_requestAccounts: \"requestAccess\",\n            };\n\n            return makeError(`user rejected action`, \"ACTION_REJECTED\", {\n                action: (actionMap[method] || \"unknown\") ,\n                reason: \"rejected\",\n                info: { payload, error }\n            });\n        }\n\n        if (method === \"eth_sendRawTransaction\" || method === \"eth_sendTransaction\") {\n            const transaction = <TransactionLike<string>>((<any>payload).params[0]);\n\n            if (message.match(/insufficient funds|base fee exceeds gas limit/i)) {\n                return makeError(\"insufficient funds for intrinsic transaction cost\", \"INSUFFICIENT_FUNDS\", {\n                    transaction, info: { error }\n                });\n            }\n\n            if (message.match(/nonce/i) && message.match(/too low/i)) {\n                return makeError(\"nonce has already been used\", \"NONCE_EXPIRED\", { transaction, info: { error } });\n            }\n\n            // \"replacement transaction underpriced\"\n            if (message.match(/replacement transaction/i) && message.match(/underpriced/i)) {\n                return makeError(\"replacement fee too low\", \"REPLACEMENT_UNDERPRICED\", { transaction, info: { error } });\n            }\n\n            if (message.match(/only replay-protected/i)) {\n                return makeError(\"legacy pre-eip-155 transactions not supported\", \"UNSUPPORTED_OPERATION\", {\n                    operation: method, info: { transaction, info: { error } }\n                });\n            }\n        }\n\n        let unsupported = !!message.match(/the method .* does not exist/i);\n        if (!unsupported) {\n            if (error && (<any>error).details && (<any>error).details.startsWith(\"Unauthorized method:\")) {\n                unsupported = true;\n            }\n        }\n\n        if (unsupported) {\n            return makeError(\"unsupported operation\", \"UNSUPPORTED_OPERATION\", {\n                operation: payload.method, info: { error, payload }\n            });\n        }\n\n        return makeError(\"could not coalesce error\", \"UNKNOWN_ERROR\", { error, payload });\n    }\n\n\n    /**\n     *  Requests the %%method%% with %%params%% via the JSON-RPC protocol\n     *  over the underlying channel. This can be used to call methods\n     *  on the backend that do not have a high-level API within the Provider\n     *  API.\n     *\n     *  This method queues requests according to the batch constraints\n     *  in the options, assigns the request a unique ID.\n     *\n     *  **Do NOT override** this method in sub-classes; instead\n     *  override [[_send]] or force the options values in the\n     *  call to the constructor to modify this method's behavior.\n     */\n    send(method: string, params: Array<any> | Record<string, any>): Promise<any> {\n        // @TODO: cache chainId?? purge on switch_networks\n\n        // We have been destroyed; no operations are supported anymore\n        if (this.destroyed) {\n            return Promise.reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", { operation: method }));\n        }\n\n        const id = this.#nextId++;\n        const promise = new Promise((resolve, reject) => {\n            this.#payloads.push({\n                resolve, reject,\n                payload: { method, params, id, jsonrpc: \"2.0\" }\n            });\n        });\n\n        // If there is not a pending drainTimer, set one\n        this.#scheduleDrain();\n\n        return <Promise<JsonRpcResult>>promise;\n    }\n\n    /**\n     *  Resolves to the [[Signer]] account for  %%address%% managed by\n     *  the client.\n     *\n     *  If the %%address%% is a number, it is used as an index in the\n     *  the accounts from [[listAccounts]].\n     *\n     *  This can only be used on clients which manage accounts (such as\n     *  Geth with imported account or MetaMask).\n     *\n     *  Throws if the account doesn't exist.\n     */\n    async getSigner(address?: number | string): Promise<JsonRpcSigner> {\n        if (address == null) { address = 0; }\n\n        const accountsPromise = this.send(\"eth_accounts\", [ ]);\n\n        // Account index\n        if (typeof(address) === \"number\") {\n            const accounts = <Array<string>>(await accountsPromise);\n            if (address >= accounts.length) { throw new Error(\"no such account\"); }\n            return new JsonRpcSigner(this, accounts[address]);\n        }\n\n        const { accounts } = await resolveProperties({\n            network: this.getNetwork(),\n            accounts: accountsPromise\n        });\n\n        // Account address\n        address = getAddress(address);\n        for (const account of accounts) {\n            if (getAddress(account) === address) {\n                return new JsonRpcSigner(this, address);\n            }\n        }\n\n        throw new Error(\"invalid account\");\n    }\n\n    async listAccounts(): Promise<Array<JsonRpcSigner>> {\n        const accounts: Array<string> = await this.send(\"eth_accounts\", [ ]);\n        return accounts.map((a) => new JsonRpcSigner(this, a));\n    }\n\n    destroy(): void {\n\n        // Stop processing requests\n        if (this.#drainTimer) {\n            clearTimeout(this.#drainTimer);\n            this.#drainTimer = null;\n        }\n\n        // Cancel all pending requests\n        for (const { payload, reject } of this.#payloads) {\n            reject(makeError(\"provider destroyed; cancelled request\", \"UNSUPPORTED_OPERATION\", { operation: payload.method }));\n        }\n\n        this.#payloads = [ ];\n\n        // Parent clean-up\n        super.destroy();\n\n    }\n}\n\n// @TODO: remove this in v7, it is not exported because this functionality\n// is exposed in the JsonRpcApiProvider by setting polling to true. It should\n// be safe to remove regardless, because it isn't reachable, but just in case.\n/**\n *  @_ignore:\n */\nexport abstract class JsonRpcApiPollingProvider extends JsonRpcApiProvider {\n    #pollingInterval: number;\n    constructor(network?: Networkish, options?: JsonRpcApiProviderOptions) {\n        super(network, options);\n\n        let pollingInterval = this._getOption(\"pollingInterval\");\n        if (pollingInterval == null) { pollingInterval = defaultOptions.pollingInterval; }\n\n        this.#pollingInterval = pollingInterval;\n    }\n\n    _getSubscriber(sub: Subscription): Subscriber {\n        const subscriber = super._getSubscriber(sub);\n        if (isPollable(subscriber)) {\n            subscriber.pollingInterval = this.#pollingInterval;\n        }\n        return subscriber;\n    }\n\n    /**\n     *  The polling interval (default: 4000 ms)\n     */\n    get pollingInterval(): number { return this.#pollingInterval; }\n    set pollingInterval(value: number) {\n        if (!Number.isInteger(value) || value < 0) { throw new Error(\"invalid interval\"); }\n        this.#pollingInterval = value;\n        this._forEachSubscriber((sub) => {\n            if (isPollable(sub)) {\n                sub.pollingInterval = this.#pollingInterval;\n            }\n        });\n    }\n}\n\n/**\n *  The JsonRpcProvider is one of the most common Providers,\n *  which performs all operations over HTTP (or HTTPS) requests.\n *\n *  Events are processed by polling the backend for the current block\n *  number; when it advances, all block-base events are then checked\n *  for updates.\n */\nexport class JsonRpcProvider extends JsonRpcApiPollingProvider {\n    #connect: FetchRequest;\n\n    constructor(url?: string | FetchRequest, network?: Networkish, options?: JsonRpcApiProviderOptions) {\n        if (url == null) { url = \"http:/\\/localhost:8545\"; }\n        super(network, options);\n\n        if (typeof(url) === \"string\") {\n            this.#connect = new FetchRequest(url);\n        } else {\n            this.#connect = url.clone();\n        }\n    }\n\n    _getConnection(): FetchRequest {\n        return this.#connect.clone();\n    }\n\n    async send(method: string, params: Array<any> | Record<string, any>): Promise<any> {\n        // All requests are over HTTP, so we can just start handling requests\n        // We do this here rather than the constructor so that we don't send any\n        // requests to the network (i.e. eth_chainId) until we absolutely have to.\n        await this._start();\n\n        return await super.send(method, params);\n    }\n\n    async _send(payload: JsonRpcPayload | Array<JsonRpcPayload>): Promise<Array<JsonRpcResult>> {\n        // Configure a POST connection for the requested method\n        const request = this._getConnection();\n        request.body = JSON.stringify(payload);\n        request.setHeader(\"content-type\", \"application/json\");\n        const response = await request.send();\n        response.assertOk();\n\n        let resp = response.bodyJson;\n        if (!Array.isArray(resp)) { resp = [ resp ]; }\n\n        return resp;\n    }\n}\n\nfunction spelunkData(value: any): null | { message: string, data: string } {\n    if (value == null) { return null; }\n\n    // These *are* the droids we're looking for.\n    if (typeof(value.message) === \"string\" && value.message.match(/revert/i) && isHexString(value.data)) {\n        return { message: value.message, data: value.data };\n    }\n\n    // Spelunk further...\n    if (typeof(value) === \"object\") {\n        for (const key in value) {\n            const result = spelunkData(value[key]);\n            if (result) { return result; }\n        }\n        return null;\n    }\n\n    // Might be a JSON string we can further descend...\n    if (typeof(value) === \"string\") {\n        try {\n            return spelunkData(JSON.parse(value));\n        } catch (error) { }\n    }\n\n    return null;\n}\n\nfunction _spelunkMessage(value: any, result: Array<string>): void {\n    if (value == null) { return; }\n\n    // These *are* the droids we're looking for.\n    if (typeof(value.message) === \"string\") {\n        result.push(value.message);\n    }\n\n    // Spelunk further...\n    if (typeof(value) === \"object\") {\n        for (const key in value) {\n            _spelunkMessage(value[key], result);\n        }\n    }\n\n    // Might be a JSON string we can further descend...\n    if (typeof(value) === \"string\") {\n        try {\n            return _spelunkMessage(JSON.parse(value), result);\n        } catch (error) { }\n    }\n}\n\nfunction spelunkMessage(value: any): Array<string> {\n    const result: Array<string> = [ ];\n    _spelunkMessage(value, result);\n    return result;\n}\n"], "mappings": "AAAA;;;;;;;;;;;AAYA;AACA;AAEA;AAEA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,UAAU,EAAEC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,yBAAyB;AACzE,SACIC,gBAAgB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAC1EC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,cAAc,EAC1CC,YAAY,EAAEC,iBAAiB,QAC5B,mBAAmB;AAE1B,SAASC,gBAAgB,EAAEC,mBAAmB,QAAQ,wBAAwB;AAC9E,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,uBAAuB,EAAEC,yBAAyB,QAAQ,0BAA0B;AAC7F,SAASC,sBAAsB,QAAQ,yBAAyB;AAYhE,MAAMC,SAAS,GAAG,8CAA8C,CAACC,KAAK,CAAC,IAAI,CAAC;AAC5E;AACA,SAASC,QAAQA,CAAUC,KAAQ;EAC/B,IAAIA,KAAK,IAAI,IAAI,IAAIH,SAAS,CAACI,OAAO,CAAC,OAAOD,KAAM,CAAC,IAAI,CAAC,EAAE;IACxD,OAAOA,KAAK;;EAGhB;EACA,IAAI,OAAaA,KAAM,CAAC3B,UAAW,KAAK,UAAU,EAAE;IAChD,OAAO2B,KAAK;;EAGhB,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;IAAE,OAAaA,KAAK,CAACI,GAAG,CAACL,QAAQ,CAAC;;EAE5D,IAAI,OAAOC,KAAM,KAAK,QAAQ,EAAE;IAC5B,OAAOK,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,MAAM,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;MAC5CD,KAAK,CAACC,GAAG,CAAC,GAAST,KAAM,CAACS,GAAG,CAAC;MAC9B,OAAOD,KAAK;IAChB,CAAC,EAAO,EAAG,CAAC;;EAGhB,MAAM,IAAIE,KAAK,CAAC,sBAAuBV,KAAM,KAAM,OAAOA,KAAO,GAAG,CAAC;AACzE;AAEA,SAASW,KAAKA,CAACC,QAAgB;EAC3B,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;IAAGC,UAAU,CAACD,OAAO,EAAEF,QAAQ,CAAC;EAAE,CAAC,CAAC;AACvE;AAEA,SAASI,YAAYA,CAAChB,KAAa;EAC/B,IAAIA,KAAK,EAAE;IAAE,OAAOA,KAAK,CAACiB,WAAW,EAAE;;EACvC,OAAOjB,KAAK;AAChB;AAMA,SAASkB,UAAUA,CAAClB,KAAU;EAC1B,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACmB,eAAgB,KAAK,QAAQ;AAC/D;AAsHA,MAAMC,cAAc,GAAG;EACnBC,OAAO,EAAE,KAAK;EACdC,aAAa,EAAE,IAAI;EAEnBC,cAAc,EAAE,EAAE;EAClBC,YAAY,EAAG,CAAC,IAAI,EAAG;EACvBC,aAAa,EAAE,GAAG;EAElBC,YAAY,EAAE,GAAG;EACjBP,eAAe,EAAE;CACpB;AA+ED;AAEA,OAAM,MAAOQ,aAAc,SAAQnC,cAAkC;EACjEoC,OAAO;EAEPC,YAAYC,QAA4B,EAAEF,OAAe;IACrD,KAAK,CAACE,QAAQ,CAAC;IACfF,OAAO,GAAGvD,UAAU,CAACuD,OAAO,CAAC;IAC7BlD,gBAAgB,CAAgB,IAAI,EAAE;MAAEkD;IAAO,CAAE,CAAC;EACtD;EAEAG,OAAOA,CAACD,QAAyB;IAC7B5C,MAAM,CAAC,KAAK,EAAE,gCAAgC,EAAE,uBAAuB,EAAE;MACrE8C,SAAS,EAAE;KACd,CAAC;EACN;EAEA,MAAM3D,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACuD,OAAO;EACvB;EAEA;EACA,MAAMK,mBAAmBA,CAACC,EAAsB;IAC5C,OAAO,MAAM,IAAI,CAACC,YAAY,CAACD,EAAE,CAAC;EACtC;EAEA;EACA;EACA,MAAME,wBAAwBA,CAACC,GAAuB;IAClD,MAAMH,EAAE,GAAGnC,QAAQ,CAACsC,GAAG,CAAC;IAExB,MAAMC,QAAQ,GAAyB,EAAE;IAEzC;IACA,IAAIJ,EAAE,CAACK,IAAI,EAAE;MACT,MAAMC,KAAK,GAAGN,EAAE,CAACK,IAAI;MACrBD,QAAQ,CAACG,IAAI,CAAC,CAAC,YAAW;QACtB,MAAMF,IAAI,GAAG,MAAMjE,cAAc,CAACkE,KAAK,EAAE,IAAI,CAACV,QAAQ,CAAC;QACvD3C,cAAc,CAACoD,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACtB,WAAW,EAAE,KAAK,IAAI,CAACW,OAAO,CAACX,WAAW,EAAE,EAC5E,uBAAuB,EAAE,aAAa,EAAEoB,GAAG,CAAC;QAChDH,EAAE,CAACK,IAAI,GAAGA,IAAI;MAClB,CAAC,EAAC,CAAE,CAAC;KACR,MAAM;MACHL,EAAE,CAACK,IAAI,GAAG,IAAI,CAACX,OAAO;;IAG1B;IACA;IACA;IACA,IAAIM,EAAE,CAACQ,QAAQ,IAAI,IAAI,EAAE;MACrBJ,QAAQ,CAACG,IAAI,CAAC,CAAC,YAAW;QACtBP,EAAE,CAACQ,QAAQ,GAAG,MAAM,IAAI,CAACZ,QAAQ,CAACa,WAAW,CAAC;UAAE,GAAGT,EAAE;UAAEK,IAAI,EAAE,IAAI,CAACX;QAAO,CAAC,CAAC;MAC/E,CAAC,EAAC,CAAE,CAAC;;IAGT;IACA,IAAIM,EAAE,CAACU,EAAE,IAAI,IAAI,EAAE;MACf,MAAMC,GAAG,GAAGX,EAAE,CAACU,EAAE;MACjBN,QAAQ,CAACG,IAAI,CAAC,CAAC,YAAW;QACtBP,EAAE,CAACU,EAAE,GAAG,MAAMtE,cAAc,CAACuE,GAAG,EAAE,IAAI,CAACf,QAAQ,CAAC;MACpD,CAAC,EAAC,CAAE,CAAC;;IAGT;IACA,IAAIQ,QAAQ,CAACQ,MAAM,EAAE;MAAE,MAAMjC,OAAO,CAACkC,GAAG,CAACT,QAAQ,CAAC;;IAElD,MAAMU,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAACmB,iBAAiB,CAACf,EAAE,CAAC;IAEjD,OAAO,IAAI,CAACJ,QAAQ,CAACoB,IAAI,CAAC,qBAAqB,EAAE,CAAEF,KAAK,CAAE,CAAC;EAC/D;EAEA,MAAMG,eAAeA,CAACjB,EAAsB;IACxC;IACA,MAAMkB,WAAW,GAAG,MAAM,IAAI,CAACtB,QAAQ,CAACuB,cAAc,EAAE;IAExD;IACA,MAAMC,IAAI,GAAG,MAAM,IAAI,CAAClB,wBAAwB,CAACF,EAAE,CAAC;IAEpD;IACA;IACA;IACA,OAAO,MAAO,IAAIrB,OAAO,CAAC,CAACC,OAAO,EAAEyC,MAAM,KAAI;MAC1C,MAAMC,QAAQ,GAAG,CAAE,IAAI,EAAE,GAAG,CAAE;MAC9B,IAAIC,QAAQ,GAAG,CAAC;MAEhB,MAAMC,OAAO,GAAG,MAAAA,CAAA,KAAW;QAEvB,IAAI;UACA;UACA,MAAMxB,EAAE,GAAG,MAAM,IAAI,CAACJ,QAAQ,CAAC6B,cAAc,CAACL,IAAI,CAAC;UAEnD,IAAIpB,EAAE,IAAI,IAAI,EAAE;YACZpB,OAAO,CAACoB,EAAE,CAAC0B,sBAAsB,CAACR,WAAW,CAAC,CAAC;YAC/C;;SAGP,CAAC,OAAOS,KAAK,EAAE;UAEZ;UACA;UACA;UACA;UACA,IAAI7E,OAAO,CAAC6E,KAAK,EAAE,WAAW,CAAC,IAAI7E,OAAO,CAAC6E,KAAK,EAAE,UAAU,CAAC,IACzD7E,OAAO,CAAC6E,KAAK,EAAE,eAAe,CAAC,IAAI7E,OAAO,CAAC6E,KAAK,EAAE,uBAAuB,CAAC,EAAE;YAE5E,IAAIA,KAAK,CAACC,IAAI,IAAI,IAAI,EAAE;cAAED,KAAK,CAACC,IAAI,GAAG,EAAG;;YAC1CD,KAAK,CAACC,IAAI,CAACC,mBAAmB,GAAGT,IAAI;YAErCC,MAAM,CAACM,KAAK,CAAC;YACb;;UAGJ;UACA,IAAI7E,OAAO,CAAC6E,KAAK,EAAE,kBAAkB,CAAC,EAAE;YACpCJ,QAAQ,EAAE;YACV,IAAII,KAAK,CAACC,IAAI,IAAI,IAAI,EAAE;cAAED,KAAK,CAACC,IAAI,GAAG,EAAG;;YAC1CD,KAAK,CAACC,IAAI,CAACC,mBAAmB,GAAGT,IAAI;YACrC,IAAIG,QAAQ,GAAG,EAAE,EAAE;cACfF,MAAM,CAACM,KAAK,CAAC;cACb;;;UAIR;UACA;UACA,IAAI,CAAC/B,QAAQ,CAACkC,IAAI,CAAC,OAAO,EAAE/E,SAAS,CAAC,2DAA2D,EAAE,eAAe,EAAE;YAAE4E;UAAK,CAAE,CAAC,CAAC;;QAGnI;QACA,IAAI,CAAC/B,QAAQ,CAACmC,WAAW,CAAC,MAAK;UAAGP,OAAO,EAAE;QAAE,CAAC,EAAEF,QAAQ,CAACU,GAAG,EAAE,IAAI,IAAI,CAAC;MAC3E,CAAC;MACDR,OAAO,EAAE;IACb,CAAC,CAAE;EACP;EAEA,MAAMS,eAAeA,CAAC9B,GAAuB;IACzC,MAAMH,EAAE,GAAGnC,QAAQ,CAACsC,GAAG,CAAC;IAExB;IACA,IAAIH,EAAE,CAACK,IAAI,EAAE;MACT,MAAMA,IAAI,GAAG,MAAMjE,cAAc,CAAC4D,EAAE,CAACK,IAAI,EAAE,IAAI,CAACT,QAAQ,CAAC;MACzD3C,cAAc,CAACoD,IAAI,IAAI,IAAI,IAAIA,IAAI,CAACtB,WAAW,EAAE,KAAK,IAAI,CAACW,OAAO,CAACX,WAAW,EAAE,EAC5E,uBAAuB,EAAE,aAAa,EAAEoB,GAAG,CAAC;MAChDH,EAAE,CAACK,IAAI,GAAGA,IAAI;KACjB,MAAM;MACHL,EAAE,CAACK,IAAI,GAAG,IAAI,CAACX,OAAO;;IAG1B,MAAMoB,KAAK,GAAG,IAAI,CAAClB,QAAQ,CAACmB,iBAAiB,CAACf,EAAE,CAAC;IACjD,OAAO,MAAM,IAAI,CAACJ,QAAQ,CAACoB,IAAI,CAAC,qBAAqB,EAAE,CAAEF,KAAK,CAAE,CAAC;EACrE;EAGA,MAAMoB,WAAWA,CAACC,QAA6B;IAC3C,MAAMC,OAAO,GAAK,OAAOD,QAAS,KAAK,QAAQ,GAAItF,WAAW,CAACsF,QAAQ,CAAC,GAAEA,QAAS;IACnF,OAAO,MAAM,IAAI,CAACvC,QAAQ,CAACoB,IAAI,CAAC,eAAe,EAAE,CAC7CtE,OAAO,CAAC0F,OAAO,CAAC,EAAE,IAAI,CAAC1C,OAAO,CAACX,WAAW,EAAE,CAAE,CAAC;EACvD;EAEA,MAAMsD,aAAaA,CAACC,MAAuB,EAAEC,KAA4C,EAAEC,MAA2B;IAClH,MAAM1E,KAAK,GAAGD,QAAQ,CAAC2E,MAAM,CAAC;IAE9B;IACA,MAAMC,SAAS,GAAG,MAAMpG,gBAAgB,CAACqG,YAAY,CAACJ,MAAM,EAAEC,KAAK,EAAEzE,KAAK,EAAE,MAAOA,KAAa,IAAI;MAChG,MAAM4B,OAAO,GAAG,MAAMtD,cAAc,CAAC0B,KAAK,CAAC;MAC3Cb,cAAc,CAACyC,OAAO,IAAI,IAAI,EAAE,yCAAyC,EAAE,OAAO,EAAE5B,KAAK,CAAC;MAC1F,OAAO4B,OAAO;IAClB,CAAC,CAAC;IAEF,OAAO,MAAM,IAAI,CAACE,QAAQ,CAACoB,IAAI,CAAC,sBAAsB,EAAE,CACpD,IAAI,CAACtB,OAAO,CAACX,WAAW,EAAE,EAC1B4D,IAAI,CAACC,SAAS,CAACvG,gBAAgB,CAACwG,UAAU,CAACJ,SAAS,CAACH,MAAM,EAAEC,KAAK,EAAEE,SAAS,CAAC3E,KAAK,CAAC,CAAC,CACxF,CAAC;EACN;EAEA,MAAMgF,MAAMA,CAACC,QAAgB;IACzB,OAAO,IAAI,CAACnD,QAAQ,CAACoB,IAAI,CAAC,wBAAwB,EAAE,CAChD,IAAI,CAACtB,OAAO,CAACX,WAAW,EAAE,EAAEgE,QAAQ,EAAE,IAAI,CAAE,CAAC;EACrD;EAEA;EACA,MAAMC,kBAAkBA,CAACb,QAA6B;IAClD,MAAMC,OAAO,GAAK,OAAOD,QAAS,KAAK,QAAQ,GAAItF,WAAW,CAACsF,QAAQ,CAAC,GAAEA,QAAS;IACnF,OAAO,MAAM,IAAI,CAACvC,QAAQ,CAACoB,IAAI,CAAC,UAAU,EAAE,CACxC,IAAI,CAACtB,OAAO,CAACX,WAAW,EAAE,EAAErC,OAAO,CAAC0F,OAAO,CAAC,CAAE,CAAC;EACvD;;AAQJ;;;;;;;;;;AAUA,OAAM,MAAgBa,kBAAmB,SAAQ7F,gBAAgB;EAE7D,CAAA8F,OAAQ;EAER;EACA,CAAAC,MAAO;EAEP;EACA,CAAAC,QAAS;EACT,CAAAC,UAAW;EAEX,CAAAC,QAAS;EAKT,CAAAC,OAAQ;EACR,CAAAC,oBAAqB;EAErB,CAAAC,aAAcC,CAAA;IACV,IAAI,IAAI,CAAC,CAAAL,UAAW,EAAE;MAAE;;IAExB;IACA,MAAMM,SAAS,GAAI,IAAI,CAACC,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,GAAI,CAAC,GAAE,IAAI,CAACA,UAAU,CAAC,gBAAgB,CAAC;IAEjG,IAAI,CAAC,CAAAP,UAAW,GAAGxE,UAAU,CAAC,MAAK;MAC/B,IAAI,CAAC,CAAAwE,UAAW,GAAG,IAAI;MAEvB,MAAMD,QAAQ,GAAG,IAAI,CAAC,CAAAA,QAAS;MAC/B,IAAI,CAAC,CAAAA,QAAS,GAAG,EAAG;MAEpB,OAAOA,QAAQ,CAACxC,MAAM,EAAE;QAEpB;QACA,MAAMiD,KAAK,GAAG,CAAYT,QAAQ,CAACU,KAAK,EAAE,CAAG;QAC7C,OAAOV,QAAQ,CAACxC,MAAM,EAAE;UACpB,IAAIiD,KAAK,CAACjD,MAAM,KAAK,IAAI,CAAC,CAAAsC,OAAQ,CAAC3D,aAAa,EAAE;YAAE;;UACpDsE,KAAK,CAACtD,IAAI,CAAW6C,QAAQ,CAACU,KAAK,EAAG,CAAC;UACvC,MAAMC,KAAK,GAAGpB,IAAI,CAACC,SAAS,CAACiB,KAAK,CAAC3F,GAAG,CAAE8F,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAC,CAAC;UACzD,IAAIF,KAAK,CAACnD,MAAM,GAAG,IAAI,CAAC,CAAAsC,OAAQ,CAAC5D,YAAY,EAAE;YAC3C8D,QAAQ,CAACc,OAAO,CAAWL,KAAK,CAAC7B,GAAG,EAAG,CAAC;YACxC;;;QAIR;QACA,CAAC,YAAW;UACR,MAAMiC,OAAO,GAAKJ,KAAK,CAACjD,MAAM,KAAK,CAAC,GAAIiD,KAAK,CAAC,CAAC,CAAC,CAACI,OAAO,GAAEJ,KAAK,CAAC3F,GAAG,CAAE8F,CAAC,IAAKA,CAAC,CAACC,OAAO,CAAE;UAEtF,IAAI,CAACnC,IAAI,CAAC,OAAO,EAAE;YAAEqC,MAAM,EAAE,gBAAgB;YAAEF;UAAO,CAAE,CAAC;UAEzD,IAAI;YACA,MAAMG,MAAM,GAAG,MAAM,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC;YACxC,IAAI,CAACnC,IAAI,CAAC,OAAO,EAAE;cAAEqC,MAAM,EAAE,kBAAkB;cAAEC;YAAM,CAAE,CAAC;YAE1D;YACA,KAAK,MAAM;cAAExF,OAAO;cAAEyC,MAAM;cAAE4C;YAAO,CAAE,IAAIJ,KAAK,EAAE;cAE9C,IAAI,IAAI,CAACS,SAAS,EAAE;gBAChBjD,MAAM,CAACtE,SAAS,CAAC,uCAAuC,EAAE,uBAAuB,EAAE;kBAAE+C,SAAS,EAAEmE,OAAO,CAACM;gBAAM,CAAE,CAAC,CAAC;gBAClH;;cAGJ;cACA,MAAMC,IAAI,GAAGJ,MAAM,CAACK,MAAM,CAAEC,CAAC,IAAMA,CAAC,CAACC,EAAE,KAAKV,OAAO,CAACU,EAAG,CAAC,CAAC,CAAC,CAAC;cAE3D;cACA,IAAIH,IAAI,IAAI,IAAI,EAAE;gBACd,MAAM7C,KAAK,GAAG5E,SAAS,CAAC,8BAA8B,EAAE,UAAU,EAAE;kBAChEe,KAAK,EAAEsG,MAAM;kBAAExC,IAAI,EAAE;oBAAEqC;kBAAO;iBACjC,CAAC;gBACF,IAAI,CAACnC,IAAI,CAAC,OAAO,EAAEH,KAAK,CAAC;gBACzBN,MAAM,CAACM,KAAK,CAAC;gBACb;;cAGJ;cACA,IAAI,OAAO,IAAI6C,IAAI,EAAE;gBACjBnD,MAAM,CAAC,IAAI,CAACuD,WAAW,CAACX,OAAO,EAAEO,IAAI,CAAC,CAAC;gBACvC;;cAGJ;cACA5F,OAAO,CAAC4F,IAAI,CAACJ,MAAM,CAAC;;WAG3B,CAAC,OAAOzC,KAAU,EAAE;YACjB,IAAI,CAACG,IAAI,CAAC,OAAO,EAAE;cAAEqC,MAAM,EAAE,iBAAiB;cAAExC;YAAK,CAAE,CAAC;YAExD,KAAK,MAAM;cAAEN;YAAM,CAAE,IAAIwC,KAAK,EAAE;cAC5B;cACAxC,MAAM,CAACM,KAAK,CAAC;;;QAGzB,CAAC,EAAC,CAAE;;IAEZ,CAAC,EAAEgC,SAAS,CAAC;EACjB;EAEAhE,YAAY4D,OAAoB,EAAEL,OAAmC;IACjE,KAAK,CAACK,OAAO,EAAEL,OAAO,CAAC;IAEvB,IAAI,CAAC,CAAAC,MAAO,GAAG,CAAC;IAChB,IAAI,CAAC,CAAAD,OAAQ,GAAG/E,MAAM,CAAC0G,MAAM,CAAC,EAAG,EAAE3F,cAAc,EAAEgE,OAAO,IAAI,EAAG,CAAC;IAElE,IAAI,CAAC,CAAAE,QAAS,GAAG,EAAG;IACpB,IAAI,CAAC,CAAAC,UAAW,GAAG,IAAI;IAEvB,IAAI,CAAC,CAAAE,OAAQ,GAAG,IAAI;IACpB,IAAI,CAAC,CAAAC,oBAAqB,GAAG,IAAI;IAEjC;MACI,IAAI5E,OAAO,GAAmC,IAAI;MAClD,MAAMkG,OAAO,GAAG,IAAInG,OAAO,CAAEoG,QAA+B,IAAI;QAC5DnG,OAAO,GAAGmG,QAAQ;MACtB,CAAC,CAAC;MACF,IAAI,CAAC,CAAAzB,QAAS,GAAG;QAAEwB,OAAO;QAAElG;MAAO,CAAE;;IAGzC,MAAMQ,aAAa,GAAG,IAAI,CAACwE,UAAU,CAAC,eAAe,CAAC;IACtD,IAAI,OAAOxE,aAAc,KAAK,SAAS,EAAE;MACrCnC,cAAc,CAAC,CAACmC,aAAa,IAAImE,OAAO,KAAK,KAAK,EAAE,uDAAuD,EAAE,SAAS,EAAEL,OAAO,CAAC;MAChI,IAAI9D,aAAa,IAAImE,OAAO,IAAI,IAAI,EAAE;QAClC,IAAI,CAAC,CAAAA,OAAQ,GAAGhG,OAAO,CAAC8C,IAAI,CAACkD,OAAO,CAAC;;KAG5C,MAAM,IAAInE,aAAa,EAAE;MACtB;MACAnC,cAAc,CAACsG,OAAO,IAAI,IAAI,IAAInE,aAAa,CAAC4F,OAAO,CAACzB,OAAO,CAAC,EAC5D,yCAAyC,EAAE,SAAS,EAAEL,OAAO,CAAC;MAClE,IAAI,CAAC,CAAAK,OAAQ,GAAGnE,aAAa;;EAErC;EAEA;;;;;EAKAwE,UAAUA,CAA4CrF,GAAM;IACxD,OAAO,IAAI,CAAC,CAAA2E,OAAQ,CAAC3E,GAAG,CAAC;EAC7B;EAEA;;;;EAIA,IAAI0G,QAAQA,CAAA;IACRjI,MAAM,CAAE,IAAI,CAAC,CAAAuG,OAAQ,EAAE,8BAA8B,EAAE,eAAe,CAAC;IACvE,OAAO,IAAI,CAAC,CAAAA,OAAQ;EACxB;EAUA;;;;;;EAMA,MAAM2B,QAAQA,CAACC,GAAyB;IAEpC;IACA;IACA,IAAIA,GAAG,CAACZ,MAAM,KAAK,MAAM,IAAIY,GAAG,CAACZ,MAAM,KAAK,aAAa,EAAE;MACvD,IAAIvE,EAAE,GAAGmF,GAAG,CAACC,WAAW;MACxB,IAAIpF,EAAE,IAAIA,EAAE,CAACqF,IAAI,IAAI,IAAI,IAAI5I,SAAS,CAACuD,EAAE,CAACqF,IAAI,CAAC,EAAE;QAC7C;QACA,IAAIrF,EAAE,CAACsF,YAAY,IAAI,IAAI,IAAItF,EAAE,CAACuF,oBAAoB,IAAI,IAAI,EAAE;UAC5D,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACC,UAAU,EAAE;UACvC,IAAID,OAAO,CAACF,YAAY,IAAI,IAAI,IAAIE,OAAO,CAACD,oBAAoB,IAAI,IAAI,EAAE;YACtE;YACAJ,GAAG,GAAGhH,MAAM,CAAC0G,MAAM,CAAC,EAAG,EAAEM,GAAG,EAAE;cAC1BC,WAAW,EAAEjH,MAAM,CAAC0G,MAAM,CAAC,EAAG,EAAE7E,EAAE,EAAE;gBAAEqF,IAAI,EAAEK;cAAS,CAAE;aAC1D,CAAC;;;;;IAMlB,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACT,GAAG,CAAC;IAEvC,IAAIQ,OAAO,IAAI,IAAI,EAAE;MACjB,OAAO,MAAM,IAAI,CAAC3E,IAAI,CAAC2E,OAAO,CAACpB,MAAM,EAAEoB,OAAO,CAACE,IAAI,CAAC;;IAGxD,OAAO,KAAK,CAACX,QAAQ,CAACC,GAAG,CAAC;EAC9B;EAEA;;;;;;;EAOA,MAAMW,cAAcA,CAAA;IAChB,MAAMvC,OAAO,GAAG,IAAI,CAACK,UAAU,CAAC,eAAe,CAAC;IAChD,IAAIL,OAAO,EAAE;MACT,IAAIA,OAAO,KAAK,IAAI,EAAE;QAClB,IAAI,IAAI,CAAC,CAAAA,OAAQ,EAAE;UAAE,OAAO,IAAI,CAAC,CAAAA,OAAQ;;OAC5C,MAAM;QACH,OAAOA,OAAO;;;IAItB,IAAI,IAAI,CAAC,CAAAC,oBAAqB,EAAE;MAC5B,OAAO,MAAM,IAAI,CAAC,CAAAA,oBAAqB;;IAG3C;IACA,IAAI,IAAI,CAACuC,KAAK,EAAE;MACZ,IAAI,CAAC,CAAAvC,oBAAqB,GAAG,CAAC,YAAW;QACrC,IAAI;UACA,MAAMY,MAAM,GAAG7G,OAAO,CAAC8C,IAAI,CAAC5D,SAAS,CAAC,MAAM,IAAI,CAACuE,IAAI,CAAC,aAAa,EAAE,EAAG,CAAC,CAAC,CAAC;UAC3E,IAAI,CAAC,CAAAwC,oBAAqB,GAAG,IAAI;UACjC,OAAOY,MAAM;SAChB,CAAC,OAAOzC,KAAK,EAAE;UACZ,IAAI,CAAC,CAAA6B,oBAAqB,GAAG,IAAI;UACjC,MAAM7B,KAAK;;MAEnB,CAAC,EAAC,CAAE;MACJ,OAAO,MAAM,IAAI,CAAC,CAAA6B,oBAAqB;;IAG3C;IACA,IAAI,CAAC,CAAAA,oBAAqB,GAAG,CAAC,YAAW;MACrC,MAAMS,OAAO,GAAmB;QAC5BU,EAAE,EAAE,IAAI,CAAC,CAAAxB,MAAO,EAAE;QAAEoB,MAAM,EAAE,aAAa;QAAEyB,MAAM,EAAE,EAAG;QAAEC,OAAO,EAAE;OACpE;MAED,IAAI,CAACnE,IAAI,CAAC,OAAO,EAAE;QAAEqC,MAAM,EAAE,gBAAgB;QAAEF;MAAO,CAAE,CAAC;MAEzD,IAAIG,MAAoC;MACxC,IAAI;QACAA,MAAM,GAAG,CAAC,MAAM,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC,EAAE,CAAC,CAAC;QACvC,IAAI,CAAC,CAAAT,oBAAqB,GAAG,IAAI;OACpC,CAAC,OAAO7B,KAAK,EAAE;QACZ,IAAI,CAAC,CAAA6B,oBAAqB,GAAG,IAAI;QACjC,IAAI,CAAC1B,IAAI,CAAC,OAAO,EAAE;UAAEqC,MAAM,EAAE,iBAAiB;UAAExC;QAAK,CAAE,CAAC;QACxD,MAAMA,KAAK;;MAGf,IAAI,CAACG,IAAI,CAAC,OAAO,EAAE;QAAEqC,MAAM,EAAE,kBAAkB;QAAEC;MAAM,CAAE,CAAC;MAE1D,IAAI,QAAQ,IAAIA,MAAM,EAAE;QACpB,OAAO7G,OAAO,CAAC8C,IAAI,CAAC5D,SAAS,CAAC2H,MAAM,CAACA,MAAM,CAAC,CAAC;;MAGjD,MAAM,IAAI,CAACQ,WAAW,CAACX,OAAO,EAAEG,MAAM,CAAC;IAC3C,CAAC,EAAC,CAAE;IAEJ,OAAO,MAAM,IAAI,CAAC,CAAAZ,oBAAqB;EAC3C;EAEA;;;;;;;EAOA0C,MAAMA,CAAA;IACF,IAAI,IAAI,CAAC,CAAA5C,QAAS,IAAI,IAAI,IAAI,IAAI,CAAC,CAAAA,QAAS,CAAC1E,OAAO,IAAI,IAAI,EAAE;MAAE;;IAEhE,IAAI,CAAC,CAAA0E,QAAS,CAAC1E,OAAO,EAAE;IACxB,IAAI,CAAC,CAAA0E,QAAS,GAAG,IAAI;IAErB,CAAC,YAAW;MAER;MACA,OAAO,IAAI,CAAC,CAAAC,OAAQ,IAAI,IAAI,IAAI,CAAC,IAAI,CAACe,SAAS,EAAE;QAC7C,IAAI;UACA,IAAI,CAAC,CAAAf,OAAQ,GAAG,MAAM,IAAI,CAACuC,cAAc,EAAE;SAC9C,CAAC,OAAOnE,KAAK,EAAE;UACZ,IAAI,IAAI,CAAC2C,SAAS,EAAE;YAAE;;UACtB6B,OAAO,CAACC,GAAG,CAAC,iIAAiI,CAAC;UAC9I,IAAI,CAACtE,IAAI,CAAC,OAAO,EAAE/E,SAAS,CAAC,uCAAuC,EAAE,eAAe,EAAE;YAAEsJ,KAAK,EAAE,2BAA2B;YAAEzE,IAAI,EAAE;cAAED;YAAK;UAAE,CAAE,CAAC,CAAC;UAChJ,MAAMlD,KAAK,CAAC,IAAI,CAAC;;;MAIzB;MACA,IAAI,CAAC,CAAAgF,aAAc,EAAE;IACzB,CAAC,EAAC,CAAE;EACR;EAEA;;;;;EAKA,MAAM6C,eAAeA,CAAA;IACjB,IAAI,IAAI,CAAC,CAAAhD,QAAS,IAAI,IAAI,EAAE;MAAE;;IAC9B,OAAO,MAAM,IAAI,CAAC,CAAAA,QAAS,CAACwB,OAAO;EACvC;EAGA;;;;;;EAMAyB,cAAcA,CAACC,GAAiB;IAE5B;IACA,IAAIA,GAAG,CAACnB,IAAI,KAAK,SAAS,EAAE;MAAE,OAAO,IAAI5H,yBAAyB,CAAC,IAAI,CAAC;;IAExE,IAAI+I,GAAG,CAACnB,IAAI,KAAK,OAAO,EAAE;MACtB,IAAI,IAAI,CAACzB,UAAU,CAAC,SAAS,CAAC,EAAE;QAC5B,OAAO,IAAIlG,sBAAsB,CAAC,IAAI,EAAE8I,GAAG,CAAC/B,MAAM,CAAC;;MAEvD,OAAO,IAAIjH,uBAAuB,CAAC,IAAI,EAAEgJ,GAAG,CAAC/B,MAAM,CAAC;;IAGxD;IACA;IACA,IAAI+B,GAAG,CAACnB,IAAI,KAAK,QAAQ,IAAImB,GAAG,CAAC/B,MAAM,CAACgC,MAAM,KAAK,UAAU,EAAE;MAC3D,OAAO,IAAIpJ,mBAAmB,CAAC,QAAQ,CAAC;;IAG5C,OAAO,KAAK,CAACkJ,cAAc,CAACC,GAAG,CAAC;EACpC;EAEA;;;EAGA,IAAIT,KAAKA,CAAA;IAAc,OAAO,IAAI,CAAC,CAAAzC,QAAS,IAAI,IAAI;EAAE;EAEtD;;;;;EAKAvC,iBAAiBA,CAACf,EAAsB;IACpC,MAAMoE,MAAM,GAA8B,EAAE;IAE5C;IACA,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,sBAAsB,EAAE,OAAO,EAAE,OAAO,CAAC,CAACsC,OAAO,CAAEnI,GAAG,IAAI;MAClH,IAAUyB,EAAG,CAACzB,GAAG,CAAC,IAAI,IAAI,EAAE;QAAE;;MAC9B,IAAIoI,MAAM,GAAGpI,GAAG;MAChB,IAAIA,GAAG,KAAK,UAAU,EAAE;QAAEoI,MAAM,GAAG,KAAK;;MAClCvC,MAAO,CAACuC,MAAM,CAAC,GAAG/J,UAAU,CAACH,SAAS,CAAOuD,EAAG,CAACzB,GAAG,CAAC,EAAE,MAAOA,GAAI,EAAE,CAAC,CAAC;IAChF,CAAC,CAAC;IAEF;IACA,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,CAAC,CAACmI,OAAO,CAAEnI,GAAG,IAAI;MACnC,IAAUyB,EAAG,CAACzB,GAAG,CAAC,IAAI,IAAI,EAAE;QAAE;;MACxB6F,MAAO,CAAC7F,GAAG,CAAC,GAAG7B,OAAO,CAAOsD,EAAG,CAACzB,GAAG,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF;IACA,IAAIyB,EAAE,CAAC4G,UAAU,EAAE;MACfxC,MAAM,CAAC,YAAY,CAAC,GAAG9H,aAAa,CAAC0D,EAAE,CAAC4G,UAAU,CAAC;;IAGvD,IAAI5G,EAAE,CAAC6G,mBAAmB,EAAE;MACxB;MACMzC,MAAO,CAAC,qBAAqB,CAAC,GAAGpE,EAAE,CAAC6G,mBAAmB,CAAC3I,GAAG,CAAC4I,CAAC,IAAIA,CAAC,CAAC/H,WAAW,EAAE,CAAC;;IAG3F,IAAIiB,EAAE,CAAC+G,iBAAiB,EAAE;MACtB3C,MAAM,CAAC,mBAAmB,CAAC,GAAGpE,EAAE,CAAC+G,iBAAiB,CAAC7I,GAAG,CAAE8I,EAAE,IAAI;QAC1D,MAAMC,CAAC,GAAG1K,gBAAgB,CAACyK,EAAE,CAAC;QAC9B,OAAO;UACHtH,OAAO,EAAEuH,CAAC,CAACvH,OAAO;UAClBwH,KAAK,EAAEtK,UAAU,CAACqK,CAAC,CAACC,KAAK,CAAC;UAC1BC,OAAO,EAAEvK,UAAU,CAACqK,CAAC,CAACE,OAAO,CAAC;UAC9BC,OAAO,EAAExK,UAAU,CAACqK,CAAC,CAACI,SAAS,CAACD,OAAO,CAAC;UACxC1C,CAAC,EAAE9H,UAAU,CAACqK,CAAC,CAACI,SAAS,CAAC3C,CAAC,CAAC;UAC5B4C,CAAC,EAAE1K,UAAU,CAACqK,CAAC,CAACI,SAAS,CAACC,CAAC;SAC9B;MACL,CAAC,CAAC;;IAGN;IACA;IACA;IACA;IAEA,OAAOlD,MAAM;EACjB;EAEA;;;;EAIAwB,aAAaA,CAACT,GAAyB;IACnC,QAAQA,GAAG,CAACZ,MAAM;MACd,KAAK,SAAS;QACV,OAAO;UAAEA,MAAM,EAAE,aAAa;UAAEsB,IAAI,EAAE;QAAG,CAAE;MAE/C,KAAK,gBAAgB;QACjB,OAAO;UAAEtB,MAAM,EAAE,iBAAiB;UAAEsB,IAAI,EAAE;QAAG,CAAE;MAEnD,KAAK,aAAa;QACd,OAAO;UAAEtB,MAAM,EAAE,cAAc;UAAEsB,IAAI,EAAE;QAAE,CAAE;MAE/C,KAAK,gBAAgB;QACjB,OAAO;UAAEtB,MAAM,EAAE,0BAA0B;UAAEsB,IAAI,EAAE;QAAG,CAAE;MAE5D,KAAK,YAAY;QACb,OAAO;UACHtB,MAAM,EAAE,gBAAgB;UACxBsB,IAAI,EAAE,CAAE/G,YAAY,CAACqG,GAAG,CAACzF,OAAO,CAAC,EAAEyF,GAAG,CAACoC,QAAQ;SAClD;MAEL,KAAK,qBAAqB;QACtB,OAAO;UACHhD,MAAM,EAAE,yBAAyB;UACjCsB,IAAI,EAAE,CAAE/G,YAAY,CAACqG,GAAG,CAACzF,OAAO,CAAC,EAAEyF,GAAG,CAACoC,QAAQ;SAClD;MAEL,KAAK,SAAS;QACV,OAAO;UACHhD,MAAM,EAAE,aAAa;UACrBsB,IAAI,EAAE,CAAE/G,YAAY,CAACqG,GAAG,CAACzF,OAAO,CAAC,EAAEyF,GAAG,CAACoC,QAAQ;SAClD;MAEL,KAAK,YAAY;QACb,OAAO;UACHhD,MAAM,EAAE,kBAAkB;UAC1BsB,IAAI,EAAE,CACF/G,YAAY,CAACqG,GAAG,CAACzF,OAAO,CAAC,EACxB,IAAI,GAAGyF,GAAG,CAACqC,QAAQ,CAACC,QAAQ,CAAC,EAAE,CAAC,EACjCtC,GAAG,CAACoC,QAAQ;SAEnB;MAEL,KAAK,sBAAsB;QACvB,OAAO;UACHhD,MAAM,EAAE,wBAAwB;UAChCsB,IAAI,EAAE,CAAEV,GAAG,CAACuC,iBAAiB;SAChC;MAEL,KAAK,UAAU;QACX,IAAI,UAAU,IAAIvC,GAAG,EAAE;UACnB,OAAO;YACHZ,MAAM,EAAE,sBAAsB;YAC9BsB,IAAI,EAAE,CAAEV,GAAG,CAACoC,QAAQ,EAAE,CAAC,CAACpC,GAAG,CAACwC,mBAAmB;WAClD;SACJ,MAAM,IAAI,WAAW,IAAIxC,GAAG,EAAE;UAC3B,OAAO;YACHZ,MAAM,EAAE,oBAAoB;YAC5BsB,IAAI,EAAE,CAAEV,GAAG,CAACyC,SAAS,EAAE,CAAC,CAACzC,GAAG,CAACwC,mBAAmB;WACnD;;QAEL;MAEJ,KAAK,gBAAgB;QACjB,OAAO;UACHpD,MAAM,EAAE,0BAA0B;UAClCsB,IAAI,EAAE,CAAEV,GAAG,CAAC/D,IAAI;SACnB;MAEL,KAAK,uBAAuB;QACxB,OAAO;UACHmD,MAAM,EAAE,2BAA2B;UACnCsB,IAAI,EAAE,CAAEV,GAAG,CAAC/D,IAAI;SACnB;MAEL,KAAK,MAAM;QACP,OAAO;UACHmD,MAAM,EAAE,UAAU;UAClBsB,IAAI,EAAE,CAAE,IAAI,CAAC9E,iBAAiB,CAACoE,GAAG,CAACC,WAAW,CAAC,EAAED,GAAG,CAACoC,QAAQ;SAChE;MAEL,KAAK,aAAa;QAAE;UAChB,OAAO;YACHhD,MAAM,EAAE,iBAAiB;YACzBsB,IAAI,EAAE,CAAE,IAAI,CAAC9E,iBAAiB,CAACoE,GAAG,CAACC,WAAW,CAAC;WAClD;;MAGL,KAAK,SAAS;QACV,IAAID,GAAG,CAACV,MAAM,IAAIU,GAAG,CAACV,MAAM,CAAC/E,OAAO,IAAI,IAAI,EAAE;UAC1C,IAAI1B,KAAK,CAACC,OAAO,CAACkH,GAAG,CAACV,MAAM,CAAC/E,OAAO,CAAC,EAAE;YACnCyF,GAAG,CAACV,MAAM,CAAC/E,OAAO,GAAGyF,GAAG,CAACV,MAAM,CAAC/E,OAAO,CAACxB,GAAG,CAACY,YAAY,CAAC;WAC5D,MAAM;YACHqG,GAAG,CAACV,MAAM,CAAC/E,OAAO,GAAGZ,YAAY,CAACqG,GAAG,CAACV,MAAM,CAAC/E,OAAO,CAAC;;;QAG7D,OAAO;UAAE6E,MAAM,EAAE,aAAa;UAAEsB,IAAI,EAAE,CAAEV,GAAG,CAACV,MAAM;QAAE,CAAE;;IAG9D,OAAO,IAAI;EACf;EAEA;;;;;;EAMAG,WAAWA,CAACX,OAAuB,EAAE4D,MAAoB;IACrD,MAAM;MAAEtD;IAAM,CAAE,GAAGN,OAAO;IAC1B,MAAM;MAAEtC;IAAK,CAAE,GAAGkG,MAAM;IAExB,IAAItD,MAAM,KAAK,iBAAiB,IAAI5C,KAAK,CAACS,OAAO,EAAE;MAC/C,MAAM0F,GAAG,GAAGnG,KAAK,CAACS,OAAO;MACzB,IAAI,CAAC0F,GAAG,CAACC,KAAK,CAAC,SAAS,CAAC,IAAID,GAAG,CAACC,KAAK,CAAC,qBAAqB,CAAC,EAAE;QAC3D,OAAOhL,SAAS,CAAC,oBAAoB,EAAE,oBAAoB,EAAE;UACzDqI,WAAW,EAASnB,OAAQ,CAAC+B,MAAM,CAAC,CAAC,CAAE;UACvCpE,IAAI,EAAE;YAAEqC,OAAO;YAAEtC;UAAK;SACzB,CAAC;OACL,MAAM,IAAImG,GAAG,CAACC,KAAK,CAAC,QAAQ,CAAC,IAAID,GAAG,CAACC,KAAK,CAAC,UAAU,CAAC,EAAE;QACrD,OAAOhL,SAAS,CAAC,6BAA6B,EAAE,eAAe,EAAE;UAC7DqI,WAAW,EAASnB,OAAQ,CAAC+B,MAAM,CAAC,CAAC,CAAE;UACvCpE,IAAI,EAAE;YAAEqC,OAAO;YAAEtC;UAAK;SACzB,CAAC;;;IAIV,IAAI4C,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,iBAAiB,EAAE;MACvD,MAAMH,MAAM,GAAG4D,WAAW,CAACrG,KAAK,CAAC;MAEjC,MAAMsG,CAAC,GAAG/L,QAAQ,CAACgM,uBAAuB,CACrC3D,MAAM,KAAK,UAAU,GAAI,MAAM,GAAE,aAAa,EACxCN,OAAQ,CAAC+B,MAAM,CAAC,CAAC,CAAC,EACxB5B,MAAM,GAAGA,MAAM,CAAC+D,IAAI,GAAE,IAAK,CAC/B;MACDF,CAAC,CAACrG,IAAI,GAAG;QAAED,KAAK;QAAEsC;MAAO,CAAE;MAC3B,OAAOgE,CAAC;;IAGZ;IACA;IAEA,MAAM7F,OAAO,GAAGO,IAAI,CAACC,SAAS,CAACwF,cAAc,CAACzG,KAAK,CAAC,CAAC;IAErD,IAAI,OAAOA,KAAK,CAACS,OAAQ,KAAK,QAAQ,IAAIT,KAAK,CAACS,OAAO,CAAC2F,KAAK,CAAC,iCAAiC,CAAC,EAAE;MAC9F,MAAMM,SAAS,GAA8G;QACzHC,QAAQ,EAAE,aAAa;QACvBC,aAAa,EAAE,aAAa;QAC5BC,oBAAoB,EAAE,eAAe;QACrCC,mBAAmB,EAAE,iBAAiB;QACtCC,mBAAmB,EAAE,iBAAiB;QACtCC,mBAAmB,EAAE,eAAe;QACpCC,sBAAsB,EAAE;OAC3B;MAED,OAAO7L,SAAS,CAAC,sBAAsB,EAAE,iBAAiB,EAAE;QACxDoH,MAAM,EAAGkE,SAAS,CAAC9D,MAAM,CAAC,IAAI,SAAU;QACxCsE,MAAM,EAAE,UAAU;QAClBjH,IAAI,EAAE;UAAEqC,OAAO;UAAEtC;QAAK;OACzB,CAAC;;IAGN,IAAI4C,MAAM,KAAK,wBAAwB,IAAIA,MAAM,KAAK,qBAAqB,EAAE;MACzE,MAAMa,WAAW,GAAmCnB,OAAQ,CAAC+B,MAAM,CAAC,CAAC,CAAE;MAEvE,IAAI5D,OAAO,CAAC2F,KAAK,CAAC,gDAAgD,CAAC,EAAE;QACjE,OAAOhL,SAAS,CAAC,mDAAmD,EAAE,oBAAoB,EAAE;UACxFqI,WAAW;UAAExD,IAAI,EAAE;YAAED;UAAK;SAC7B,CAAC;;MAGN,IAAIS,OAAO,CAAC2F,KAAK,CAAC,QAAQ,CAAC,IAAI3F,OAAO,CAAC2F,KAAK,CAAC,UAAU,CAAC,EAAE;QACtD,OAAOhL,SAAS,CAAC,6BAA6B,EAAE,eAAe,EAAE;UAAEqI,WAAW;UAAExD,IAAI,EAAE;YAAED;UAAK;QAAE,CAAE,CAAC;;MAGtG;MACA,IAAIS,OAAO,CAAC2F,KAAK,CAAC,0BAA0B,CAAC,IAAI3F,OAAO,CAAC2F,KAAK,CAAC,cAAc,CAAC,EAAE;QAC5E,OAAOhL,SAAS,CAAC,yBAAyB,EAAE,yBAAyB,EAAE;UAAEqI,WAAW;UAAExD,IAAI,EAAE;YAAED;UAAK;QAAE,CAAE,CAAC;;MAG5G,IAAIS,OAAO,CAAC2F,KAAK,CAAC,wBAAwB,CAAC,EAAE;QACzC,OAAOhL,SAAS,CAAC,+CAA+C,EAAE,uBAAuB,EAAE;UACvF+C,SAAS,EAAEyE,MAAM;UAAE3C,IAAI,EAAE;YAAEwD,WAAW;YAAExD,IAAI,EAAE;cAAED;YAAK;UAAE;SAC1D,CAAC;;;IAIV,IAAImH,WAAW,GAAG,CAAC,CAAC1G,OAAO,CAAC2F,KAAK,CAAC,+BAA+B,CAAC;IAClE,IAAI,CAACe,WAAW,EAAE;MACd,IAAInH,KAAK,IAAUA,KAAM,CAACoH,OAAO,IAAUpH,KAAM,CAACoH,OAAO,CAACC,UAAU,CAAC,sBAAsB,CAAC,EAAE;QAC1FF,WAAW,GAAG,IAAI;;;IAI1B,IAAIA,WAAW,EAAE;MACb,OAAO/L,SAAS,CAAC,uBAAuB,EAAE,uBAAuB,EAAE;QAC/D+C,SAAS,EAAEmE,OAAO,CAACM,MAAM;QAAE3C,IAAI,EAAE;UAAED,KAAK;UAAEsC;QAAO;OACpD,CAAC;;IAGN,OAAOlH,SAAS,CAAC,0BAA0B,EAAE,eAAe,EAAE;MAAE4E,KAAK;MAAEsC;IAAO,CAAE,CAAC;EACrF;EAGA;;;;;;;;;;;;;EAaAjD,IAAIA,CAACuD,MAAc,EAAEyB,MAAwC;IACzD;IAEA;IACA,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAChB,OAAO3F,OAAO,CAAC0C,MAAM,CAACtE,SAAS,CAAC,uCAAuC,EAAE,uBAAuB,EAAE;QAAE+C,SAAS,EAAEyE;MAAM,CAAE,CAAC,CAAC;;IAG7H,MAAMI,EAAE,GAAG,IAAI,CAAC,CAAAxB,MAAO,EAAE;IACzB,MAAM2B,OAAO,GAAG,IAAInG,OAAO,CAAC,CAACC,OAAO,EAAEyC,MAAM,KAAI;MAC5C,IAAI,CAAC,CAAA+B,QAAS,CAAC7C,IAAI,CAAC;QAChB3B,OAAO;QAAEyC,MAAM;QACf4C,OAAO,EAAE;UAAEM,MAAM;UAAEyB,MAAM;UAAErB,EAAE;UAAEsB,OAAO,EAAE;QAAK;OAChD,CAAC;IACN,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,CAAAxC,aAAc,EAAE;IAErB,OAA+BqB,OAAO;EAC1C;EAEA;;;;;;;;;;;;EAYA,MAAMmE,SAASA,CAACvJ,OAAyB;IACrC,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,CAAC;;IAElC,MAAMwJ,eAAe,GAAG,IAAI,CAAClI,IAAI,CAAC,cAAc,EAAE,EAAG,CAAC;IAEtD;IACA,IAAI,OAAOtB,OAAQ,KAAK,QAAQ,EAAE;MAC9B,MAAMyJ,QAAQ,GAAmB,MAAMD,eAAgB;MACvD,IAAIxJ,OAAO,IAAIyJ,QAAQ,CAACvI,MAAM,EAAE;QAAE,MAAM,IAAIpC,KAAK,CAAC,iBAAiB,CAAC;;MACpE,OAAO,IAAIiB,aAAa,CAAC,IAAI,EAAE0J,QAAQ,CAACzJ,OAAO,CAAC,CAAC;;IAGrD,MAAM;MAAEyJ;IAAQ,CAAE,GAAG,MAAMhM,iBAAiB,CAAC;MACzCoG,OAAO,EAAE,IAAI,CAAC6F,UAAU,EAAE;MAC1BD,QAAQ,EAAED;KACb,CAAC;IAEF;IACAxJ,OAAO,GAAGvD,UAAU,CAACuD,OAAO,CAAC;IAC7B,KAAK,MAAM2J,OAAO,IAAIF,QAAQ,EAAE;MAC5B,IAAIhN,UAAU,CAACkN,OAAO,CAAC,KAAK3J,OAAO,EAAE;QACjC,OAAO,IAAID,aAAa,CAAC,IAAI,EAAEC,OAAO,CAAC;;;IAI/C,MAAM,IAAIlB,KAAK,CAAC,iBAAiB,CAAC;EACtC;EAEA,MAAM8K,YAAYA,CAAA;IACd,MAAMH,QAAQ,GAAkB,MAAM,IAAI,CAACnI,IAAI,CAAC,cAAc,EAAE,EAAG,CAAC;IACpE,OAAOmI,QAAQ,CAACjL,GAAG,CAAE+I,CAAC,IAAK,IAAIxH,aAAa,CAAC,IAAI,EAAEwH,CAAC,CAAC,CAAC;EAC1D;EAEAsC,OAAOA,CAAA;IAEH;IACA,IAAI,IAAI,CAAC,CAAAlG,UAAW,EAAE;MAClBmG,YAAY,CAAC,IAAI,CAAC,CAAAnG,UAAW,CAAC;MAC9B,IAAI,CAAC,CAAAA,UAAW,GAAG,IAAI;;IAG3B;IACA,KAAK,MAAM;MAAEY,OAAO;MAAE5C;IAAM,CAAE,IAAI,IAAI,CAAC,CAAA+B,QAAS,EAAE;MAC9C/B,MAAM,CAACtE,SAAS,CAAC,uCAAuC,EAAE,uBAAuB,EAAE;QAAE+C,SAAS,EAAEmE,OAAO,CAACM;MAAM,CAAE,CAAC,CAAC;;IAGtH,IAAI,CAAC,CAAAnB,QAAS,GAAG,EAAG;IAEpB;IACA,KAAK,CAACmG,OAAO,EAAE;EAEnB;;AAGJ;AACA;AACA;AACA;;;AAGA,OAAM,MAAgBE,yBAA0B,SAAQxG,kBAAkB;EACtE,CAAAhE,eAAgB;EAChBU,YAAY4D,OAAoB,EAAEL,OAAmC;IACjE,KAAK,CAACK,OAAO,EAAEL,OAAO,CAAC;IAEvB,IAAIjE,eAAe,GAAG,IAAI,CAAC2E,UAAU,CAAC,iBAAiB,CAAC;IACxD,IAAI3E,eAAe,IAAI,IAAI,EAAE;MAAEA,eAAe,GAAGC,cAAc,CAACD,eAAe;;IAE/E,IAAI,CAAC,CAAAA,eAAgB,GAAGA,eAAe;EAC3C;EAEAsH,cAAcA,CAACC,GAAiB;IAC5B,MAAMkD,UAAU,GAAG,KAAK,CAACnD,cAAc,CAACC,GAAG,CAAC;IAC5C,IAAIxH,UAAU,CAAC0K,UAAU,CAAC,EAAE;MACxBA,UAAU,CAACzK,eAAe,GAAG,IAAI,CAAC,CAAAA,eAAgB;;IAEtD,OAAOyK,UAAU;EACrB;EAEA;;;EAGA,IAAIzK,eAAeA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,eAAgB;EAAE;EAC9D,IAAIA,eAAeA,CAACnB,KAAa;IAC7B,IAAI,CAAC6L,MAAM,CAACC,SAAS,CAAC9L,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAAE,MAAM,IAAIU,KAAK,CAAC,kBAAkB,CAAC;;IAChF,IAAI,CAAC,CAAAS,eAAgB,GAAGnB,KAAK;IAC7B,IAAI,CAAC+L,kBAAkB,CAAErD,GAAG,IAAI;MAC5B,IAAIxH,UAAU,CAACwH,GAAG,CAAC,EAAE;QACjBA,GAAG,CAACvH,eAAe,GAAG,IAAI,CAAC,CAAAA,eAAgB;;IAEnD,CAAC,CAAC;EACN;;AAGJ;;;;;;;;AAQA,OAAM,MAAO6K,eAAgB,SAAQL,yBAAyB;EAC1D,CAAA5J,OAAQ;EAERF,YAAYoK,GAA2B,EAAExG,OAAoB,EAAEL,OAAmC;IAC9F,IAAI6G,GAAG,IAAI,IAAI,EAAE;MAAEA,GAAG,GAAG,wBAAwB;;IACjD,KAAK,CAACxG,OAAO,EAAEL,OAAO,CAAC;IAEvB,IAAI,OAAO6G,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAAC,CAAAlK,OAAQ,GAAG,IAAI3C,YAAY,CAAC6M,GAAG,CAAC;KACxC,MAAM;MACH,IAAI,CAAC,CAAAlK,OAAQ,GAAGkK,GAAG,CAACC,KAAK,EAAE;;EAEnC;EAEAC,cAAcA,CAAA;IACV,OAAO,IAAI,CAAC,CAAApK,OAAQ,CAACmK,KAAK,EAAE;EAChC;EAEA,MAAMhJ,IAAIA,CAACuD,MAAc,EAAEyB,MAAwC;IAC/D;IACA;IACA;IACA,MAAM,IAAI,CAACE,MAAM,EAAE;IAEnB,OAAO,MAAM,KAAK,CAAClF,IAAI,CAACuD,MAAM,EAAEyB,MAAM,CAAC;EAC3C;EAEA,MAAM3B,KAAKA,CAACJ,OAA+C;IACvD;IACA,MAAM0B,OAAO,GAAG,IAAI,CAACsE,cAAc,EAAE;IACrCtE,OAAO,CAACuE,IAAI,GAAGvH,IAAI,CAACC,SAAS,CAACqB,OAAO,CAAC;IACtC0B,OAAO,CAACwE,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC;IACrD,MAAMC,QAAQ,GAAG,MAAMzE,OAAO,CAAC3E,IAAI,EAAE;IACrCoJ,QAAQ,CAACC,QAAQ,EAAE;IAEnB,IAAI7F,IAAI,GAAG4F,QAAQ,CAACE,QAAQ;IAC5B,IAAI,CAACtM,KAAK,CAACC,OAAO,CAACuG,IAAI,CAAC,EAAE;MAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;;IAE3C,OAAOA,IAAI;EACf;;AAGJ,SAASwD,WAAWA,CAAClK,KAAU;EAC3B,IAAIA,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,IAAI;;EAEhC;EACA,IAAI,OAAOA,KAAK,CAACsE,OAAQ,KAAK,QAAQ,IAAItE,KAAK,CAACsE,OAAO,CAAC2F,KAAK,CAAC,SAAS,CAAC,IAAIpL,WAAW,CAACmB,KAAK,CAACqK,IAAI,CAAC,EAAE;IACjG,OAAO;MAAE/F,OAAO,EAAEtE,KAAK,CAACsE,OAAO;MAAE+F,IAAI,EAAErK,KAAK,CAACqK;IAAI,CAAE;;EAGvD;EACA,IAAI,OAAOrK,KAAM,KAAK,QAAQ,EAAE;IAC5B,KAAK,MAAMS,GAAG,IAAIT,KAAK,EAAE;MACrB,MAAMsG,MAAM,GAAG4D,WAAW,CAAClK,KAAK,CAACS,GAAG,CAAC,CAAC;MACtC,IAAI6F,MAAM,EAAE;QAAE,OAAOA,MAAM;;;IAE/B,OAAO,IAAI;;EAGf;EACA,IAAI,OAAOtG,KAAM,KAAK,QAAQ,EAAE;IAC5B,IAAI;MACA,OAAOkK,WAAW,CAACrF,IAAI,CAAC4H,KAAK,CAACzM,KAAK,CAAC,CAAC;KACxC,CAAC,OAAO6D,KAAK,EAAE;;EAGpB,OAAO,IAAI;AACf;AAEA,SAAS6I,eAAeA,CAAC1M,KAAU,EAAEsG,MAAqB;EACtD,IAAItG,KAAK,IAAI,IAAI,EAAE;IAAE;;EAErB;EACA,IAAI,OAAOA,KAAK,CAACsE,OAAQ,KAAK,QAAQ,EAAE;IACpCgC,MAAM,CAAC7D,IAAI,CAACzC,KAAK,CAACsE,OAAO,CAAC;;EAG9B;EACA,IAAI,OAAOtE,KAAM,KAAK,QAAQ,EAAE;IAC5B,KAAK,MAAMS,GAAG,IAAIT,KAAK,EAAE;MACrB0M,eAAe,CAAC1M,KAAK,CAACS,GAAG,CAAC,EAAE6F,MAAM,CAAC;;;EAI3C;EACA,IAAI,OAAOtG,KAAM,KAAK,QAAQ,EAAE;IAC5B,IAAI;MACA,OAAO0M,eAAe,CAAC7H,IAAI,CAAC4H,KAAK,CAACzM,KAAK,CAAC,EAAEsG,MAAM,CAAC;KACpD,CAAC,OAAOzC,KAAK,EAAE;;AAExB;AAEA,SAASyG,cAAcA,CAACtK,KAAU;EAC9B,MAAMsG,MAAM,GAAkB,EAAG;EACjCoG,eAAe,CAAC1M,KAAK,EAAEsG,MAAM,CAAC;EAC9B,OAAOA,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}