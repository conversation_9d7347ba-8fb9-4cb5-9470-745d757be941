{"ast": null, "code": "import { keccak256 } from \"../crypto/index.js\";\nimport { MessagePrefix } from \"../constants/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport { concat, toUtf8Bytes } from \"../utils/index.js\";\n/**\n *  Computes the [[link-eip-191]] personal-sign message digest to sign.\n *\n *  This prefixes the message with [[MessagePrefix]] and the decimal length\n *  of %%message%% and computes the [[keccak256]] digest.\n *\n *  If %%message%% is a string, it is converted to its UTF-8 bytes\n *  first. To compute the digest of a [[DataHexString]], it must be converted\n *  to [bytes](getBytes).\n *\n *  @example:\n *    hashMessage(\"Hello World\")\n *    //_result:\n *\n *    // Hashes the SIX (6) string characters, i.e.\n *    // [ \"0\", \"x\", \"4\", \"2\", \"4\", \"3\" ]\n *    hashMessage(\"0x4243\")\n *    //_result:\n *\n *    // Hashes the TWO (2) bytes [ 0x42, 0x43 ]...\n *    hashMessage(getBytes(\"0x4243\"))\n *    //_result:\n *\n *    // ...which is equal to using data\n *    hashMessage(new Uint8Array([ 0x42, 0x43 ]))\n *    //_result:\n *\n */\nexport function hashMessage(message) {\n  if (typeof message === \"string\") {\n    message = toUtf8Bytes(message);\n  }\n  return keccak256(concat([toUtf8Bytes(MessagePrefix), toUtf8Bytes(String(message.length)), message]));\n}\n/**\n *  Return the address of the private key that produced\n *  the signature %%sig%% during signing for %%message%%.\n */\nexport function verifyMessage(message, sig) {\n  const digest = hashMessage(message);\n  return recoverAddress(digest, sig);\n}", "map": {"version": 3, "names": ["keccak256", "MessagePrefix", "recoverAddress", "concat", "toUtf8Bytes", "hashMessage", "message", "String", "length", "verifyMessage", "sig", "digest"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\message.ts"], "sourcesContent": ["import { keccak256 } from \"../crypto/index.js\";\nimport { MessagePrefix } from \"../constants/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport { concat, toUtf8Bytes } from \"../utils/index.js\";\n\nimport type { SignatureLike } from \"../crypto/index.js\";\n/**\n *  Computes the [[link-eip-191]] personal-sign message digest to sign.\n *\n *  This prefixes the message with [[MessagePrefix]] and the decimal length\n *  of %%message%% and computes the [[keccak256]] digest.\n *\n *  If %%message%% is a string, it is converted to its UTF-8 bytes\n *  first. To compute the digest of a [[DataHexString]], it must be converted\n *  to [bytes](getBytes).\n *\n *  @example:\n *    hashMessage(\"Hello World\")\n *    //_result:\n *\n *    // Hashes the SIX (6) string characters, i.e.\n *    // [ \"0\", \"x\", \"4\", \"2\", \"4\", \"3\" ]\n *    hashMessage(\"0x4243\")\n *    //_result:\n *\n *    // Hashes the TWO (2) bytes [ 0x42, 0x43 ]...\n *    hashMessage(getBytes(\"0x4243\"))\n *    //_result:\n *\n *    // ...which is equal to using data\n *    hashMessage(new Uint8Array([ 0x42, 0x43 ]))\n *    //_result:\n *\n */\nexport function hashMessage(message: Uint8Array | string): string {\n    if (typeof(message) === \"string\") { message = toUtf8Bytes(message); }\n    return keccak256(concat([\n        toUtf8Bytes(MessagePrefix),\n        toUtf8Bytes(String(message.length)),\n        message\n    ]));\n}\n\n/**\n *  Return the address of the private key that produced\n *  the signature %%sig%% during signing for %%message%%.\n */\nexport function verifyMessage(message: Uint8Array | string, sig: SignatureLike): string {\n    const digest = hashMessage(message);\n    return recoverAddress(digest, sig);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,MAAM,EAAEC,WAAW,QAAQ,mBAAmB;AAGvD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAM,SAAUC,WAAWA,CAACC,OAA4B;EACpD,IAAI,OAAOA,OAAQ,KAAK,QAAQ,EAAE;IAAEA,OAAO,GAAGF,WAAW,CAACE,OAAO,CAAC;;EAClE,OAAON,SAAS,CAACG,MAAM,CAAC,CACpBC,WAAW,CAACH,aAAa,CAAC,EAC1BG,WAAW,CAACG,MAAM,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC,EACnCF,OAAO,CACV,CAAC,CAAC;AACP;AAEA;;;;AAIA,OAAM,SAAUG,aAAaA,CAACH,OAA4B,EAAEI,GAAkB;EAC1E,MAAMC,MAAM,GAAGN,WAAW,CAACC,OAAO,CAAC;EACnC,OAAOJ,cAAc,CAACS,MAAM,EAAED,GAAG,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}