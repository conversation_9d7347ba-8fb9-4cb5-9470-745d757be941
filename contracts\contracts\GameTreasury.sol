// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./CQTToken.sol";

/**
 * @title GameTreasury
 * @dev Manages CQT token distribution for game rewards, upgrades, and tournaments
 */
contract GameTreasury is Ownable, Pausable, ReentrancyGuard {
    
    CQTToken public cqtToken;
    
    // Reward amounts for different activities
    uint256 public pveWinReward = 10 * 10**18;      // 10 CQT per PvE win
    uint256 public pvpWinReward = 25 * 10**18;      // 25 CQT per PvP win
    uint256 public dailyQuestReward = 5 * 10**18;   // 5 CQT per daily quest
    
    // Upgrade costs
    uint256 public heroUpgradeCost = 50 * 10**18;   // 50 CQT per hero upgrade
    uint256 public skillUnlockCost = 100 * 10**18;  // 100 CQT per skill unlock
    
    // Tournament settings
    uint256 public tournamentEntryFee = 100 * 10**18; // 100 CQT entry fee
    uint256 public tournamentPrizePool;
    
    // Authorized game servers that can trigger rewards
    mapping(address => bool) public authorizedServers;
    
    // Player statistics
    mapping(address => uint256) public totalEarned;
    mapping(address => uint256) public totalSpent;
    mapping(address => uint256) public pveWins;
    mapping(address => uint256) public pvpWins;
    
    // Events
    event RewardDistributed(address indexed player, uint256 amount, string gameMode);
    event TokensSpent(address indexed player, uint256 amount, string purpose);
    event TournamentEntry(address indexed player, uint256 entryFee);
    event TournamentPrize(address indexed winner, uint256 prize);
    event ServerAuthorized(address indexed server);
    event ServerDeauthorized(address indexed server);
    
    modifier onlyAuthorizedServer() {
        require(authorizedServers[msg.sender], "Not authorized server");
        _;
    }
    
    constructor(address _cqtToken) Ownable(msg.sender) {
        require(_cqtToken != address(0), "Invalid CQT token address");
        cqtToken = CQTToken(_cqtToken);
    }
    
    /**
     * @dev Authorize a game server to distribute rewards
     */
    function authorizeServer(address _server) external onlyOwner {
        require(_server != address(0), "Invalid server address");
        authorizedServers[_server] = true;
        emit ServerAuthorized(_server);
    }
    
    /**
     * @dev Deauthorize a game server
     */
    function deauthorizeServer(address _server) external onlyOwner {
        authorizedServers[_server] = false;
        emit ServerDeauthorized(_server);
    }
    
    /**
     * @dev Distribute PvE win reward
     */
    function distributePvEReward(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        
        pveWins[_player]++;
        totalEarned[_player] += pveWinReward;
        
        cqtToken.distributeReward(_player, pveWinReward, "PvE Victory");
        emit RewardDistributed(_player, pveWinReward, "PvE");
    }
    
    /**
     * @dev Distribute PvP win reward
     */
    function distributePvPReward(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        
        pvpWins[_player]++;
        totalEarned[_player] += pvpWinReward;
        
        cqtToken.distributeReward(_player, pvpWinReward, "PvP Victory");
        emit RewardDistributed(_player, pvpWinReward, "PvP");
    }
    
    /**
     * @dev Distribute daily quest reward
     */
    function distributeDailyQuestReward(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        
        totalEarned[_player] += dailyQuestReward;
        
        cqtToken.distributeReward(_player, dailyQuestReward, "Daily Quest");
        emit RewardDistributed(_player, dailyQuestReward, "Daily Quest");
    }
    
    /**
     * @dev Player spends CQT for hero upgrade
     */
    function upgradeHero(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        require(cqtToken.balanceOf(_player) >= heroUpgradeCost, "Insufficient CQT balance");
        
        totalSpent[_player] += heroUpgradeCost;
        
        // Burn tokens for upgrade
        cqtToken.burnFrom(_player, heroUpgradeCost);
        emit TokensSpent(_player, heroUpgradeCost, "Hero Upgrade");
    }
    
    /**
     * @dev Player spends CQT for skill unlock
     */
    function unlockSkill(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        require(cqtToken.balanceOf(_player) >= skillUnlockCost, "Insufficient CQT balance");
        
        totalSpent[_player] += skillUnlockCost;
        
        // Burn tokens for skill unlock
        cqtToken.burnFrom(_player, skillUnlockCost);
        emit TokensSpent(_player, skillUnlockCost, "Skill Unlock");
    }
    
    /**
     * @dev Player enters tournament
     */
    function enterTournament(address _player) external onlyAuthorizedServer nonReentrant {
        require(_player != address(0), "Invalid player address");
        require(cqtToken.balanceOf(_player) >= tournamentEntryFee, "Insufficient CQT balance");
        
        totalSpent[_player] += tournamentEntryFee;
        tournamentPrizePool += tournamentEntryFee;
        
        // Transfer entry fee to this contract
        cqtToken.transferFrom(_player, address(this), tournamentEntryFee);
        emit TournamentEntry(_player, tournamentEntryFee);
    }
    
    /**
     * @dev Distribute tournament prize
     */
    function distributeTournamentPrize(address _winner, uint256 _prizeAmount) external onlyAuthorizedServer nonReentrant {
        require(_winner != address(0), "Invalid winner address");
        require(_prizeAmount <= tournamentPrizePool, "Prize exceeds pool");
        
        tournamentPrizePool -= _prizeAmount;
        totalEarned[_winner] += _prizeAmount;
        
        cqtToken.transfer(_winner, _prizeAmount);
        emit TournamentPrize(_winner, _prizeAmount);
    }
    
    /**
     * @dev Update reward amounts (only owner)
     */
    function updateRewards(
        uint256 _pveWinReward,
        uint256 _pvpWinReward,
        uint256 _dailyQuestReward
    ) external onlyOwner {
        pveWinReward = _pveWinReward;
        pvpWinReward = _pvpWinReward;
        dailyQuestReward = _dailyQuestReward;
    }
    
    /**
     * @dev Update upgrade costs (only owner)
     */
    function updateCosts(
        uint256 _heroUpgradeCost,
        uint256 _skillUnlockCost,
        uint256 _tournamentEntryFee
    ) external onlyOwner {
        heroUpgradeCost = _heroUpgradeCost;
        skillUnlockCost = _skillUnlockCost;
        tournamentEntryFee = _tournamentEntryFee;
    }
    
    /**
     * @dev Get player statistics
     */
    function getPlayerStats(address _player) external view returns (
        uint256 earned,
        uint256 spent,
        uint256 pveWinCount,
        uint256 pvpWinCount,
        uint256 balance
    ) {
        return (
            totalEarned[_player],
            totalSpent[_player],
            pveWins[_player],
            pvpWins[_player],
            cqtToken.balanceOf(_player)
        );
    }
    
    /**
     * @dev Pause contract functions
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause contract functions
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Emergency withdrawal (only owner)
     */
    function emergencyWithdraw(uint256 _amount) external onlyOwner {
        require(_amount <= cqtToken.balanceOf(address(this)), "Insufficient balance");
        cqtToken.transfer(owner(), _amount);
    }
}
