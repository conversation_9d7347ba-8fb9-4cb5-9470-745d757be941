{"ast": null, "code": "import { assert, makeError } from \"./errors.js\";\nexport function createGetUrl(options) {\n  async function getUrl(req, _signal) {\n    assert(_signal == null || !_signal.cancelled, \"request cancelled before sending\", \"CANCELLED\");\n    const protocol = req.url.split(\":\")[0].toLowerCase();\n    assert(protocol === \"http\" || protocol === \"https\", `unsupported protocol ${protocol}`, \"UNSUPPORTED_OPERATION\", {\n      info: {\n        protocol\n      },\n      operation: \"request\"\n    });\n    assert(protocol === \"https\" || !req.credentials || req.allowInsecureAuthentication, \"insecure authorized connections unsupported\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"request\"\n    });\n    let error = null;\n    const controller = new AbortController();\n    const timer = setTimeout(() => {\n      error = makeError(\"request timeout\", \"TIMEOUT\");\n      controller.abort();\n    }, req.timeout);\n    if (_signal) {\n      _signal.addListener(() => {\n        error = makeError(\"request cancelled\", \"CANCELLED\");\n        controller.abort();\n      });\n    }\n    const init = Object.assign({}, options, {\n      method: req.method,\n      headers: new Headers(Array.from(req)),\n      body: req.body || undefined,\n      signal: controller.signal\n    });\n    let resp;\n    try {\n      resp = await fetch(req.url, init);\n    } catch (_error) {\n      clearTimeout(timer);\n      if (error) {\n        throw error;\n      }\n      throw _error;\n    }\n    clearTimeout(timer);\n    const headers = {};\n    resp.headers.forEach((value, key) => {\n      headers[key.toLowerCase()] = value;\n    });\n    const respBody = await resp.arrayBuffer();\n    const body = respBody == null ? null : new Uint8Array(respBody);\n    return {\n      statusCode: resp.status,\n      statusMessage: resp.statusText,\n      headers,\n      body\n    };\n  }\n  return getUrl;\n}\n// @TODO: remove in v7; provided for backwards compat\nconst defaultGetUrl = createGetUrl({});\nexport async function getUrl(req, _signal) {\n  return defaultGetUrl(req, _signal);\n}", "map": {"version": 3, "names": ["assert", "makeError", "createGetUrl", "options", "getUrl", "req", "_signal", "cancelled", "protocol", "url", "split", "toLowerCase", "info", "operation", "credentials", "allowInsecureAuthentication", "error", "controller", "AbortController", "timer", "setTimeout", "abort", "timeout", "addListener", "init", "Object", "assign", "method", "headers", "Headers", "Array", "from", "body", "undefined", "signal", "resp", "fetch", "_error", "clearTimeout", "for<PERSON>ach", "value", "key", "respBody", "arrayBuffer", "Uint8Array", "statusCode", "status", "statusMessage", "statusText", "defaultGetUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\geturl-browser.ts"], "sourcesContent": ["import { assert, makeError } from \"./errors.js\";\n\nimport type {\n    FetchGetUrlFunc, FetchRequest, FetchCancelSignal, GetUrlResponse\n} from \"./fetch.js\";\n\nexport function createGetUrl(options?: Record<string, any>): FetchGetUrlFunc {\n\n    async function getUrl(req: FetchRequest, _signal?: FetchCancelSignal): Promise<GetUrlResponse> {\n        assert(_signal == null || !_signal.cancelled, \"request cancelled before sending\", \"CANCELLED\");\n\n        const protocol = req.url.split(\":\")[0].toLowerCase();\n\n        assert(protocol === \"http\" || protocol === \"https\", `unsupported protocol ${ protocol }`, \"UNSUPPORTED_OPERATION\", {\n            info: { protocol },\n            operation: \"request\"\n        });\n\n        assert(protocol === \"https\" || !req.credentials || req.allowInsecureAuthentication, \"insecure authorized connections unsupported\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"request\"\n        });\n\n        let error: null | Error = null;\n\n        const controller = new AbortController();\n\n        const timer = setTimeout(() => {\n            error = makeError(\"request timeout\", \"TIMEOUT\");\n            controller.abort();\n        }, req.timeout);\n\n        if (_signal) {\n            _signal.addListener(() => {\n                error = makeError(\"request cancelled\", \"CANCELLED\");\n                controller.abort();\n            });\n        }\n\n        const init = Object.assign({ }, options, {\n            method: req.method,\n            headers: new Headers(Array.from(req)),\n            body: req.body || undefined,\n            signal: controller.signal\n        });\n\n        let resp: Awaited<ReturnType<typeof fetch>>;\n        try {\n            resp = await fetch(req.url, init);\n        } catch (_error) {\n            clearTimeout(timer);\n            if (error) { throw error; }\n            throw _error;\n        }\n\n        clearTimeout(timer);\n\n        const headers: Record<string, string> = { };\n        resp.headers.forEach((value, key) => {\n            headers[key.toLowerCase()] = value;\n        });\n\n        const respBody = await resp.arrayBuffer();\n        const body = (respBody == null) ? null: new Uint8Array(respBody);\n\n        return {\n            statusCode: resp.status,\n            statusMessage: resp.statusText,\n            headers, body\n        };\n    }\n\n    return getUrl;\n}\n\n// @TODO: remove in v7; provided for backwards compat\nconst defaultGetUrl: FetchGetUrlFunc = createGetUrl({ });\n\nexport async function getUrl(req: FetchRequest, _signal?: FetchCancelSignal): Promise<GetUrlResponse> {\n    return defaultGetUrl(req, _signal);\n}\n\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,aAAa;AAM/C,OAAM,SAAUC,YAAYA,CAACC,OAA6B;EAEtD,eAAeC,MAAMA,CAACC,GAAiB,EAAEC,OAA2B;IAChEN,MAAM,CAACM,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,kCAAkC,EAAE,WAAW,CAAC;IAE9F,MAAMC,QAAQ,GAAGH,GAAG,CAACI,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;IAEpDX,MAAM,CAACQ,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,OAAO,EAAE,wBAAyBA,QAAS,EAAE,EAAE,uBAAuB,EAAE;MAC/GI,IAAI,EAAE;QAAEJ;MAAQ,CAAE;MAClBK,SAAS,EAAE;KACd,CAAC;IAEFb,MAAM,CAACQ,QAAQ,KAAK,OAAO,IAAI,CAACH,GAAG,CAACS,WAAW,IAAIT,GAAG,CAACU,2BAA2B,EAAE,6CAA6C,EAAE,uBAAuB,EAAE;MACxJF,SAAS,EAAE;KACd,CAAC;IAEF,IAAIG,KAAK,GAAiB,IAAI;IAE9B,MAAMC,UAAU,GAAG,IAAIC,eAAe,EAAE;IAExC,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAK;MAC1BJ,KAAK,GAAGf,SAAS,CAAC,iBAAiB,EAAE,SAAS,CAAC;MAC/CgB,UAAU,CAACI,KAAK,EAAE;IACtB,CAAC,EAAEhB,GAAG,CAACiB,OAAO,CAAC;IAEf,IAAIhB,OAAO,EAAE;MACTA,OAAO,CAACiB,WAAW,CAAC,MAAK;QACrBP,KAAK,GAAGf,SAAS,CAAC,mBAAmB,EAAE,WAAW,CAAC;QACnDgB,UAAU,CAACI,KAAK,EAAE;MACtB,CAAC,CAAC;;IAGN,MAAMG,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEvB,OAAO,EAAE;MACrCwB,MAAM,EAAEtB,GAAG,CAACsB,MAAM;MAClBC,OAAO,EAAE,IAAIC,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC1B,GAAG,CAAC,CAAC;MACrC2B,IAAI,EAAE3B,GAAG,CAAC2B,IAAI,IAAIC,SAAS;MAC3BC,MAAM,EAAEjB,UAAU,CAACiB;KACtB,CAAC;IAEF,IAAIC,IAAuC;IAC3C,IAAI;MACAA,IAAI,GAAG,MAAMC,KAAK,CAAC/B,GAAG,CAACI,GAAG,EAAEe,IAAI,CAAC;KACpC,CAAC,OAAOa,MAAM,EAAE;MACbC,YAAY,CAACnB,KAAK,CAAC;MACnB,IAAIH,KAAK,EAAE;QAAE,MAAMA,KAAK;;MACxB,MAAMqB,MAAM;;IAGhBC,YAAY,CAACnB,KAAK,CAAC;IAEnB,MAAMS,OAAO,GAA2B,EAAG;IAC3CO,IAAI,CAACP,OAAO,CAACW,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAI;MAChCb,OAAO,CAACa,GAAG,CAAC9B,WAAW,EAAE,CAAC,GAAG6B,KAAK;IACtC,CAAC,CAAC;IAEF,MAAME,QAAQ,GAAG,MAAMP,IAAI,CAACQ,WAAW,EAAE;IACzC,MAAMX,IAAI,GAAIU,QAAQ,IAAI,IAAI,GAAI,IAAI,GAAE,IAAIE,UAAU,CAACF,QAAQ,CAAC;IAEhE,OAAO;MACHG,UAAU,EAAEV,IAAI,CAACW,MAAM;MACvBC,aAAa,EAAEZ,IAAI,CAACa,UAAU;MAC9BpB,OAAO;MAAEI;KACZ;EACL;EAEA,OAAO5B,MAAM;AACjB;AAEA;AACA,MAAM6C,aAAa,GAAoB/C,YAAY,CAAC,EAAG,CAAC;AAExD,OAAO,eAAeE,MAAMA,CAACC,GAAiB,EAAEC,OAA2B;EACvE,OAAO2C,aAAa,CAAC5C,GAAG,EAAEC,OAAO,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}