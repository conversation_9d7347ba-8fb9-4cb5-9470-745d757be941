{"ast": null, "code": "import { defineProperties, concat, getBytesCopy, getNumber, hexlify, toBeArray, toBigInt, toNumber, assert, assertArgument\n/*, isError*/ } from \"../../utils/index.js\";\n/**\n * @_ignore:\n */\nexport const WordSize = 32;\nconst Padding = new Uint8Array(WordSize);\n// Properties used to immediate pass through to the underlying object\n// - `then` is used to detect if an object is a Promise for await\nconst passProperties = [\"then\"];\nconst _guard = {};\nconst resultNames = new WeakMap();\nfunction getNames(result) {\n  return resultNames.get(result);\n}\nfunction setNames(result, names) {\n  resultNames.set(result, names);\n}\nfunction throwError(name, error) {\n  const wrapped = new Error(`deferred error during ABI decoding triggered accessing ${name}`);\n  wrapped.error = error;\n  throw wrapped;\n}\nfunction toObject(names, items, deep) {\n  if (names.indexOf(null) >= 0) {\n    return items.map((item, index) => {\n      if (item instanceof Result) {\n        return toObject(getNames(item), item, deep);\n      }\n      return item;\n    });\n  }\n  return names.reduce((accum, name, index) => {\n    let item = items.getValue(name);\n    if (!(name in accum)) {\n      if (deep && item instanceof Result) {\n        item = toObject(getNames(item), item, deep);\n      }\n      accum[name] = item;\n    }\n    return accum;\n  }, {});\n}\n/**\n *  A [[Result]] is a sub-class of Array, which allows accessing any\n *  of its values either positionally by its index or, if keys are\n *  provided by its name.\n *\n *  @_docloc: api/abi\n */\nexport class Result extends Array {\n  // No longer used; but cannot be removed as it will remove the\n  // #private field from the .d.ts which may break backwards\n  // compatibility\n  #names;\n  /**\n   *  @private\n   */\n  constructor(...args) {\n    // To properly sub-class Array so the other built-in\n    // functions work, the constructor has to behave fairly\n    // well. So, in the event we are created via fromItems()\n    // we build the read-only Result object we want, but on\n    // any other input, we use the default constructor\n    // constructor(guard: any, items: Array<any>, keys?: Array<null | string>);\n    const guard = args[0];\n    let items = args[1];\n    let names = (args[2] || []).slice();\n    let wrap = true;\n    if (guard !== _guard) {\n      items = args;\n      names = [];\n      wrap = false;\n    }\n    // Can't just pass in ...items since an array of length 1\n    // is a special case in the super.\n    super(items.length);\n    items.forEach((item, index) => {\n      this[index] = item;\n    });\n    // Find all unique keys\n    const nameCounts = names.reduce((accum, name) => {\n      if (typeof name === \"string\") {\n        accum.set(name, (accum.get(name) || 0) + 1);\n      }\n      return accum;\n    }, new Map());\n    // Remove any key thats not unique\n    setNames(this, Object.freeze(items.map((item, index) => {\n      const name = names[index];\n      if (name != null && nameCounts.get(name) === 1) {\n        return name;\n      }\n      return null;\n    })));\n    // Dummy operations to prevent TypeScript from complaining\n    this.#names = [];\n    if (this.#names == null) {\n      void this.#names;\n    }\n    if (!wrap) {\n      return;\n    }\n    // A wrapped Result is immutable\n    Object.freeze(this);\n    // Proxy indices and names so we can trap deferred errors\n    const proxy = new Proxy(this, {\n      get: (target, prop, receiver) => {\n        if (typeof prop === \"string\") {\n          // Index accessor\n          if (prop.match(/^[0-9]+$/)) {\n            const index = getNumber(prop, \"%index\");\n            if (index < 0 || index >= this.length) {\n              throw new RangeError(\"out of result range\");\n            }\n            const item = target[index];\n            if (item instanceof Error) {\n              throwError(`index ${index}`, item);\n            }\n            return item;\n          }\n          // Pass important checks (like `then` for Promise) through\n          if (passProperties.indexOf(prop) >= 0) {\n            return Reflect.get(target, prop, receiver);\n          }\n          const value = target[prop];\n          if (value instanceof Function) {\n            // Make sure functions work with private variables\n            // See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy#no_private_property_forwarding\n            return function (...args) {\n              return value.apply(this === receiver ? target : this, args);\n            };\n          } else if (!(prop in target)) {\n            // Possible name accessor\n            return target.getValue.apply(this === receiver ? target : this, [prop]);\n          }\n        }\n        return Reflect.get(target, prop, receiver);\n      }\n    });\n    setNames(proxy, getNames(this));\n    return proxy;\n  }\n  /**\n   *  Returns the Result as a normal Array. If %%deep%%, any children\n   *  which are Result objects are also converted to a normal Array.\n   *\n   *  This will throw if there are any outstanding deferred\n   *  errors.\n   */\n  toArray(deep) {\n    const result = [];\n    this.forEach((item, index) => {\n      if (item instanceof Error) {\n        throwError(`index ${index}`, item);\n      }\n      if (deep && item instanceof Result) {\n        item = item.toArray(deep);\n      }\n      result.push(item);\n    });\n    return result;\n  }\n  /**\n   *  Returns the Result as an Object with each name-value pair. If\n   *  %%deep%%, any children which are Result objects are also\n   *  converted to an Object.\n   *\n   *  This will throw if any value is unnamed, or if there are\n   *  any outstanding deferred errors.\n   */\n  toObject(deep) {\n    const names = getNames(this);\n    return names.reduce((accum, name, index) => {\n      assert(name != null, `value at index ${index} unnamed`, \"UNSUPPORTED_OPERATION\", {\n        operation: \"toObject()\"\n      });\n      return toObject(names, this, deep);\n    }, {});\n  }\n  /**\n   *  @_ignore\n   */\n  slice(start, end) {\n    if (start == null) {\n      start = 0;\n    }\n    if (start < 0) {\n      start += this.length;\n      if (start < 0) {\n        start = 0;\n      }\n    }\n    if (end == null) {\n      end = this.length;\n    }\n    if (end < 0) {\n      end += this.length;\n      if (end < 0) {\n        end = 0;\n      }\n    }\n    if (end > this.length) {\n      end = this.length;\n    }\n    const _names = getNames(this);\n    const result = [],\n      names = [];\n    for (let i = start; i < end; i++) {\n      result.push(this[i]);\n      names.push(_names[i]);\n    }\n    return new Result(_guard, result, names);\n  }\n  /**\n   *  @_ignore\n   */\n  filter(callback, thisArg) {\n    const _names = getNames(this);\n    const result = [],\n      names = [];\n    for (let i = 0; i < this.length; i++) {\n      const item = this[i];\n      if (item instanceof Error) {\n        throwError(`index ${i}`, item);\n      }\n      if (callback.call(thisArg, item, i, this)) {\n        result.push(item);\n        names.push(_names[i]);\n      }\n    }\n    return new Result(_guard, result, names);\n  }\n  /**\n   *  @_ignore\n   */\n  map(callback, thisArg) {\n    const result = [];\n    for (let i = 0; i < this.length; i++) {\n      const item = this[i];\n      if (item instanceof Error) {\n        throwError(`index ${i}`, item);\n      }\n      result.push(callback.call(thisArg, item, i, this));\n    }\n    return result;\n  }\n  /**\n   *  Returns the value for %%name%%.\n   *\n   *  Since it is possible to have a key whose name conflicts with\n   *  a method on a [[Result]] or its superclass Array, or any\n   *  JavaScript keyword, this ensures all named values are still\n   *  accessible by name.\n   */\n  getValue(name) {\n    const index = getNames(this).indexOf(name);\n    if (index === -1) {\n      return undefined;\n    }\n    const value = this[index];\n    if (value instanceof Error) {\n      throwError(`property ${JSON.stringify(name)}`, value.error);\n    }\n    return value;\n  }\n  /**\n   *  Creates a new [[Result]] for %%items%% with each entry\n   *  also accessible by its corresponding name in %%keys%%.\n   */\n  static fromItems(items, keys) {\n    return new Result(_guard, items, keys);\n  }\n}\n/**\n *  Returns all errors found in a [[Result]].\n *\n *  Since certain errors encountered when creating a [[Result]] do\n *  not impact the ability to continue parsing data, they are\n *  deferred until they are actually accessed. Hence a faulty string\n *  in an Event that is never used does not impact the program flow.\n *\n *  However, sometimes it may be useful to access, identify or\n *  validate correctness of a [[Result]].\n *\n *  @_docloc api/abi\n */\nexport function checkResultErrors(result) {\n  // Find the first error (if any)\n  const errors = [];\n  const checkErrors = function (path, object) {\n    if (!Array.isArray(object)) {\n      return;\n    }\n    for (let key in object) {\n      const childPath = path.slice();\n      childPath.push(key);\n      try {\n        checkErrors(childPath, object[key]);\n      } catch (error) {\n        errors.push({\n          path: childPath,\n          error: error\n        });\n      }\n    }\n  };\n  checkErrors([], result);\n  return errors;\n}\nfunction getValue(value) {\n  let bytes = toBeArray(value);\n  assert(bytes.length <= WordSize, \"value out-of-bounds\", \"BUFFER_OVERRUN\", {\n    buffer: bytes,\n    length: WordSize,\n    offset: bytes.length\n  });\n  if (bytes.length !== WordSize) {\n    bytes = getBytesCopy(concat([Padding.slice(bytes.length % WordSize), bytes]));\n  }\n  return bytes;\n}\n/**\n *  @_ignore\n */\nexport class Coder {\n  // The coder name:\n  //   - address, uint256, tuple, array, etc.\n  name;\n  // The fully expanded type, including composite types:\n  //   - address, uint256, tuple(address,bytes), uint256[3][4][],  etc.\n  type;\n  // The localName bound in the signature, in this example it is \"baz\":\n  //   - tuple(address foo, uint bar) baz\n  localName;\n  // Whether this type is dynamic:\n  //  - Dynamic: bytes, string, address[], tuple(boolean[]), etc.\n  //  - Not Dynamic: address, uint256, boolean[3], tuple(address, uint8)\n  dynamic;\n  constructor(name, type, localName, dynamic) {\n    defineProperties(this, {\n      name,\n      type,\n      localName,\n      dynamic\n    }, {\n      name: \"string\",\n      type: \"string\",\n      localName: \"string\",\n      dynamic: \"boolean\"\n    });\n  }\n  _throwError(message, value) {\n    assertArgument(false, message, this.localName, value);\n  }\n}\n/**\n *  @_ignore\n */\nexport class Writer {\n  // An array of WordSize lengthed objects to concatenation\n  #data;\n  #dataLength;\n  constructor() {\n    this.#data = [];\n    this.#dataLength = 0;\n  }\n  get data() {\n    return concat(this.#data);\n  }\n  get length() {\n    return this.#dataLength;\n  }\n  #writeData(data) {\n    this.#data.push(data);\n    this.#dataLength += data.length;\n    return data.length;\n  }\n  appendWriter(writer) {\n    return this.#writeData(getBytesCopy(writer.data));\n  }\n  // Arrayish item; pad on the right to *nearest* WordSize\n  writeBytes(value) {\n    let bytes = getBytesCopy(value);\n    const paddingOffset = bytes.length % WordSize;\n    if (paddingOffset) {\n      bytes = getBytesCopy(concat([bytes, Padding.slice(paddingOffset)]));\n    }\n    return this.#writeData(bytes);\n  }\n  // Numeric item; pad on the left *to* WordSize\n  writeValue(value) {\n    return this.#writeData(getValue(value));\n  }\n  // Inserts a numeric place-holder, returning a callback that can\n  // be used to asjust the value later\n  writeUpdatableValue() {\n    const offset = this.#data.length;\n    this.#data.push(Padding);\n    this.#dataLength += WordSize;\n    return value => {\n      this.#data[offset] = getValue(value);\n    };\n  }\n}\n/**\n *  @_ignore\n */\nexport class Reader {\n  // Allows incomplete unpadded data to be read; otherwise an error\n  // is raised if attempting to overrun the buffer. This is required\n  // to deal with an old Solidity bug, in which event data for\n  // external (not public thoguh) was tightly packed.\n  allowLoose;\n  #data;\n  #offset;\n  #bytesRead;\n  #parent;\n  #maxInflation;\n  constructor(data, allowLoose, maxInflation) {\n    defineProperties(this, {\n      allowLoose: !!allowLoose\n    });\n    this.#data = getBytesCopy(data);\n    this.#bytesRead = 0;\n    this.#parent = null;\n    this.#maxInflation = maxInflation != null ? maxInflation : 1024;\n    this.#offset = 0;\n  }\n  get data() {\n    return hexlify(this.#data);\n  }\n  get dataLength() {\n    return this.#data.length;\n  }\n  get consumed() {\n    return this.#offset;\n  }\n  get bytes() {\n    return new Uint8Array(this.#data);\n  }\n  #incrementBytesRead(count) {\n    if (this.#parent) {\n      return this.#parent.#incrementBytesRead(count);\n    }\n    this.#bytesRead += count;\n    // Check for excessive inflation (see: #4537)\n    assert(this.#maxInflation < 1 || this.#bytesRead <= this.#maxInflation * this.dataLength, `compressed ABI data exceeds inflation ratio of ${this.#maxInflation} ( see: https:/\\/github.com/ethers-io/ethers.js/issues/4537 )`, \"BUFFER_OVERRUN\", {\n      buffer: getBytesCopy(this.#data),\n      offset: this.#offset,\n      length: count,\n      info: {\n        bytesRead: this.#bytesRead,\n        dataLength: this.dataLength\n      }\n    });\n  }\n  #peekBytes(offset, length, loose) {\n    let alignedLength = Math.ceil(length / WordSize) * WordSize;\n    if (this.#offset + alignedLength > this.#data.length) {\n      if (this.allowLoose && loose && this.#offset + length <= this.#data.length) {\n        alignedLength = length;\n      } else {\n        assert(false, \"data out-of-bounds\", \"BUFFER_OVERRUN\", {\n          buffer: getBytesCopy(this.#data),\n          length: this.#data.length,\n          offset: this.#offset + alignedLength\n        });\n      }\n    }\n    return this.#data.slice(this.#offset, this.#offset + alignedLength);\n  }\n  // Create a sub-reader with the same underlying data, but offset\n  subReader(offset) {\n    const reader = new Reader(this.#data.slice(this.#offset + offset), this.allowLoose, this.#maxInflation);\n    reader.#parent = this;\n    return reader;\n  }\n  // Read bytes\n  readBytes(length, loose) {\n    let bytes = this.#peekBytes(0, length, !!loose);\n    this.#incrementBytesRead(length);\n    this.#offset += bytes.length;\n    // @TODO: Make sure the length..end bytes are all 0?\n    return bytes.slice(0, length);\n  }\n  // Read a numeric values\n  readValue() {\n    return toBigInt(this.readBytes(WordSize));\n  }\n  readIndex() {\n    return toNumber(this.readBytes(WordSize));\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "concat", "getBytesCopy", "getNumber", "hexlify", "toBeArray", "toBigInt", "toNumber", "assert", "assertArgument", "WordSize", "Padding", "Uint8Array", "passProperties", "_guard", "resultNames", "WeakMap", "getNames", "result", "get", "setNames", "names", "set", "throwError", "name", "error", "wrapped", "Error", "toObject", "items", "deep", "indexOf", "map", "item", "index", "Result", "reduce", "accum", "getValue", "Array", "constructor", "args", "guard", "slice", "wrap", "length", "for<PERSON>ach", "nameCounts", "Map", "Object", "freeze", "proxy", "Proxy", "target", "prop", "receiver", "match", "RangeError", "Reflect", "value", "Function", "apply", "toArray", "push", "operation", "start", "end", "_names", "i", "filter", "callback", "thisArg", "call", "undefined", "JSON", "stringify", "fromItems", "keys", "checkResultErrors", "errors", "checkErrors", "path", "object", "isArray", "key", "child<PERSON><PERSON>", "bytes", "buffer", "offset", "Coder", "type", "localName", "dynamic", "_throwError", "message", "Writer", "data", "dataLength", "writeData", "#writeData", "appendWriter", "writer", "writeBytes", "paddingOffset", "writeValue", "writeUpdatableValue", "Reader", "allowLoose", "bytesRead", "parent", "maxInflation", "consumed", "incrementBytesRead", "#incrementBytesRead", "count", "info", "peekBytes", "#peekBytes", "loose", "<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "subReader", "reader", "readBytes", "readValue", "readIndex"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\abstract-coder.ts"], "sourcesContent": ["\nimport {\n    defineProperties, concat, getBytesCopy, getNumber, hexlify,\n    toBeArray, toBigInt, toNumber,\n    assert, assertArgument\n    /*, isError*/\n} from \"../../utils/index.js\";\n\nimport type { BigNumberish, BytesLike } from \"../../utils/index.js\";\n\n/**\n * @_ignore:\n */\nexport const WordSize: number = 32;\nconst Padding = new Uint8Array(WordSize);\n\n// Properties used to immediate pass through to the underlying object\n// - `then` is used to detect if an object is a Promise for await\nconst passProperties = [ \"then\" ];\n\nconst _guard = { };\n\nconst resultNames: WeakMap<Result, ReadonlyArray<null | string>> = new WeakMap();\n\nfunction getNames(result: Result): ReadonlyArray<null | string> {\n    return resultNames.get(result)!;\n}\nfunction setNames(result: Result, names: ReadonlyArray<null | string>): void {\n    resultNames.set(result, names);\n}\n\nfunction throwError(name: string, error: Error): never {\n    const wrapped = new Error(`deferred error during ABI decoding triggered accessing ${ name }`);\n    (<any>wrapped).error = error;\n    throw wrapped;\n}\n\nfunction toObject(names: ReadonlyArray<null | string>, items: Result, deep?: boolean): Record<string, any> | Array<any> {\n    if (names.indexOf(null) >= 0) {\n        return items.map((item, index) => {\n            if (item instanceof Result) {\n                return toObject(getNames(item), item, deep);\n            }\n            return item;\n        });\n    }\n\n    return (<Array<string>>names).reduce((accum, name, index) => {\n        let item = items.getValue(name);\n        if (!(name in accum)) {\n            if (deep && item instanceof Result) {\n                item = toObject(getNames(item), item, deep);\n            }\n            accum[name] = item;\n        }\n        return accum;\n    }, <Record<string, any>>{ });\n}\n\n\n/**\n *  A [[Result]] is a sub-class of Array, which allows accessing any\n *  of its values either positionally by its index or, if keys are\n *  provided by its name.\n *\n *  @_docloc: api/abi\n */\nexport class Result extends Array<any> {\n    // No longer used; but cannot be removed as it will remove the\n    // #private field from the .d.ts which may break backwards\n    // compatibility\n    readonly #names: ReadonlyArray<null | string>;\n\n    [ K: string | number ]: any\n\n    /**\n     *  @private\n     */\n    constructor(...args: Array<any>) {\n        // To properly sub-class Array so the other built-in\n        // functions work, the constructor has to behave fairly\n        // well. So, in the event we are created via fromItems()\n        // we build the read-only Result object we want, but on\n        // any other input, we use the default constructor\n\n        // constructor(guard: any, items: Array<any>, keys?: Array<null | string>);\n        const guard = args[0];\n        let items: Array<any> = args[1];\n        let names: Array<null | string> = (args[2] || [ ]).slice();\n\n        let wrap = true;\n        if (guard !== _guard) {\n            items = args;\n            names = [ ];\n            wrap = false;\n        }\n\n        // Can't just pass in ...items since an array of length 1\n        // is a special case in the super.\n        super(items.length);\n        items.forEach((item, index) => { this[index] = item; });\n\n        // Find all unique keys\n        const nameCounts = names.reduce((accum, name) => {\n            if (typeof(name) === \"string\") {\n                accum.set(name, (accum.get(name) || 0) + 1);\n            }\n            return accum;\n        }, <Map<string, number>>(new Map()));\n\n        // Remove any key thats not unique\n        setNames(this, Object.freeze(items.map((item, index) => {\n            const name = names[index];\n            if (name != null && nameCounts.get(name) === 1) {\n                return name;\n            }\n            return null;\n        })));\n\n        // Dummy operations to prevent TypeScript from complaining\n        this.#names = [ ];\n        if (this.#names == null) { void(this.#names); }\n\n        if (!wrap) { return; }\n\n        // A wrapped Result is immutable\n        Object.freeze(this);\n\n        // Proxy indices and names so we can trap deferred errors\n        const proxy = new Proxy(this, {\n            get: (target, prop, receiver) => {\n                if (typeof(prop) === \"string\") {\n\n                    // Index accessor\n                    if (prop.match(/^[0-9]+$/)) {\n                        const index = getNumber(prop, \"%index\");\n                        if (index < 0 || index >= this.length) {\n                            throw new RangeError(\"out of result range\");\n                        }\n\n                        const item = target[index];\n                        if (item instanceof Error) {\n                            throwError(`index ${ index }`, item);\n                        }\n                        return item;\n                    }\n\n                    // Pass important checks (like `then` for Promise) through\n                    if (passProperties.indexOf(prop) >= 0) {\n                        return Reflect.get(target, prop, receiver);\n                    }\n\n                    const value = target[prop];\n                    if (value instanceof Function) {\n                        // Make sure functions work with private variables\n                        // See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy#no_private_property_forwarding\n                        return function(this: any, ...args: Array<any>) {\n                            return value.apply((this === receiver) ? target: this, args);\n                        };\n\n                    } else if (!(prop in target)) {\n                        // Possible name accessor\n                        return target.getValue.apply((this === receiver) ? target: this, [ prop ]);\n                    }\n                }\n\n                return Reflect.get(target, prop, receiver);\n            }\n        });\n        setNames(proxy, getNames(this));\n        return proxy;\n    }\n\n    /**\n     *  Returns the Result as a normal Array. If %%deep%%, any children\n     *  which are Result objects are also converted to a normal Array.\n     *\n     *  This will throw if there are any outstanding deferred\n     *  errors.\n     */\n    toArray(deep?: boolean): Array<any> {\n        const result: Array<any> = [ ];\n        this.forEach((item, index) => {\n            if (item instanceof Error) { throwError(`index ${ index }`, item); }\n            if (deep && item instanceof Result) {\n                item = item.toArray(deep);\n            }\n            result.push(item);\n        });\n        return result;\n    }\n\n    /**\n     *  Returns the Result as an Object with each name-value pair. If\n     *  %%deep%%, any children which are Result objects are also\n     *  converted to an Object.\n     *\n     *  This will throw if any value is unnamed, or if there are\n     *  any outstanding deferred errors.\n     */\n    toObject(deep?: boolean): Record<string, any> {\n        const names = getNames(this);\n        return names.reduce((accum, name, index) => {\n\n            assert(name != null, `value at index ${ index } unnamed`, \"UNSUPPORTED_OPERATION\", {\n                operation: \"toObject()\"\n            });\n\n            return toObject(names, this, deep);\n        }, <Record<string, any>>{});\n    }\n\n    /**\n     *  @_ignore\n     */\n    slice(start?: number | undefined, end?: number | undefined): Result {\n        if (start == null) { start = 0; }\n        if (start < 0) {\n            start += this.length;\n            if (start < 0) { start = 0; }\n        }\n\n        if (end == null) { end = this.length; }\n        if (end < 0) {\n            end += this.length;\n            if (end < 0) { end = 0; }\n        }\n        if (end > this.length) { end = this.length; }\n\n        const _names = getNames(this);\n\n        const result: Array<any> = [ ], names: Array<null | string> = [ ];\n        for (let i = start; i < end; i++) {\n            result.push(this[i]);\n            names.push(_names[i]);\n        }\n\n        return new Result(_guard, result, names);\n    }\n\n    /**\n     *  @_ignore\n     */\n    filter(callback: (el: any, index: number, array: Result) => boolean, thisArg?: any): Result {\n        const _names = getNames(this);\n\n        const result: Array<any> = [ ], names: Array<null | string> = [ ];\n        for (let i = 0; i < this.length; i++) {\n            const item = this[i];\n            if (item instanceof Error) {\n                throwError(`index ${ i }`, item);\n            }\n\n            if (callback.call(thisArg, item, i, this)) {\n                result.push(item);\n                names.push(_names[i]);\n            }\n        }\n\n        return new Result(_guard, result, names);\n    }\n\n    /**\n     *  @_ignore\n     */\n    map<T extends any = any>(callback: (el: any, index: number, array: Result) => T, thisArg?: any): Array<T> {\n        const result: Array<T> = [ ];\n        for (let i = 0; i < this.length; i++) {\n            const item = this[i];\n            if (item instanceof Error) {\n                throwError(`index ${ i }`, item);\n            }\n\n            result.push(callback.call(thisArg, item, i, this));\n        }\n\n        return result;\n    }\n\n\n    /**\n     *  Returns the value for %%name%%.\n     *\n     *  Since it is possible to have a key whose name conflicts with\n     *  a method on a [[Result]] or its superclass Array, or any\n     *  JavaScript keyword, this ensures all named values are still\n     *  accessible by name.\n     */\n    getValue(name: string): any {\n        const index = getNames(this).indexOf(name);\n        if (index === -1) { return undefined; }\n\n        const value = this[index];\n\n        if (value instanceof Error) {\n            throwError(`property ${ JSON.stringify(name) }`, (<any>value).error);\n        }\n\n        return value;\n    }\n\n    /**\n     *  Creates a new [[Result]] for %%items%% with each entry\n     *  also accessible by its corresponding name in %%keys%%.\n     */\n    static fromItems(items: Array<any>, keys?: Array<null | string>): Result {\n        return new Result(_guard, items, keys);\n    }\n}\n\n/**\n *  Returns all errors found in a [[Result]].\n *\n *  Since certain errors encountered when creating a [[Result]] do\n *  not impact the ability to continue parsing data, they are\n *  deferred until they are actually accessed. Hence a faulty string\n *  in an Event that is never used does not impact the program flow.\n *\n *  However, sometimes it may be useful to access, identify or\n *  validate correctness of a [[Result]].\n *\n *  @_docloc api/abi\n */\nexport function checkResultErrors(result: Result): Array<{ path: Array<string | number>, error: Error }> {\n    // Find the first error (if any)\n    const errors: Array<{ path: Array<string | number>, error: Error }> = [ ];\n\n    const checkErrors = function(path: Array<string | number>, object: any): void {\n        if (!Array.isArray(object)) { return; }\n        for (let key in object) {\n            const childPath = path.slice();\n            childPath.push(key);\n\n            try {\n                 checkErrors(childPath, object[key]);\n            } catch (error: any) {\n                errors.push({ path: childPath, error: error });\n            }\n        }\n    }\n    checkErrors([ ], result);\n\n    return errors;\n\n}\n\nfunction getValue(value: BigNumberish): Uint8Array {\n    let bytes = toBeArray(value);\n\n    assert (bytes.length <= WordSize, \"value out-of-bounds\",\n        \"BUFFER_OVERRUN\", { buffer: bytes, length: WordSize, offset: bytes.length });\n\n    if (bytes.length !== WordSize) {\n        bytes = getBytesCopy(concat([ Padding.slice(bytes.length % WordSize), bytes ]));\n    }\n\n    return bytes;\n}\n\n/**\n *  @_ignore\n */\nexport abstract class Coder {\n\n    // The coder name:\n    //   - address, uint256, tuple, array, etc.\n    readonly name!: string;\n\n    // The fully expanded type, including composite types:\n    //   - address, uint256, tuple(address,bytes), uint256[3][4][],  etc.\n    readonly type!: string;\n\n    // The localName bound in the signature, in this example it is \"baz\":\n    //   - tuple(address foo, uint bar) baz\n    readonly localName!: string;\n\n    // Whether this type is dynamic:\n    //  - Dynamic: bytes, string, address[], tuple(boolean[]), etc.\n    //  - Not Dynamic: address, uint256, boolean[3], tuple(address, uint8)\n    readonly dynamic!: boolean;\n\n    constructor(name: string, type: string, localName: string, dynamic: boolean) {\n        defineProperties<Coder>(this, { name, type, localName, dynamic }, {\n            name: \"string\", type: \"string\", localName: \"string\", dynamic: \"boolean\"\n        });\n    }\n\n    _throwError(message: string, value: any): never {\n        assertArgument(false, message, this.localName, value);\n    }\n\n    abstract encode(writer: Writer, value: any): number;\n    abstract decode(reader: Reader): any;\n\n    abstract defaultValue(): any;\n}\n\n/**\n *  @_ignore\n */\nexport class Writer {\n    // An array of WordSize lengthed objects to concatenation\n    #data: Array<Uint8Array>;\n    #dataLength: number;\n\n    constructor() {\n        this.#data = [ ];\n        this.#dataLength = 0;\n    }\n\n    get data(): string {\n        return concat(this.#data);\n    }\n    get length(): number { return this.#dataLength; }\n\n    #writeData(data: Uint8Array): number {\n        this.#data.push(data);\n        this.#dataLength += data.length;\n        return data.length;\n    }\n\n    appendWriter(writer: Writer): number {\n        return this.#writeData(getBytesCopy(writer.data));\n    }\n\n    // Arrayish item; pad on the right to *nearest* WordSize\n    writeBytes(value: BytesLike): number {\n        let bytes = getBytesCopy(value);\n        const paddingOffset = bytes.length % WordSize;\n        if (paddingOffset) {\n            bytes = getBytesCopy(concat([ bytes, Padding.slice(paddingOffset) ]))\n        }\n        return this.#writeData(bytes);\n    }\n\n    // Numeric item; pad on the left *to* WordSize\n    writeValue(value: BigNumberish): number {\n        return this.#writeData(getValue(value));\n    }\n\n    // Inserts a numeric place-holder, returning a callback that can\n    // be used to asjust the value later\n    writeUpdatableValue(): (value: BigNumberish) => void {\n        const offset = this.#data.length;\n        this.#data.push(Padding);\n        this.#dataLength += WordSize;\n        return (value: BigNumberish) => {\n            this.#data[offset] = getValue(value);\n        };\n    }\n}\n\n/**\n *  @_ignore\n */\nexport class Reader {\n    // Allows incomplete unpadded data to be read; otherwise an error\n    // is raised if attempting to overrun the buffer. This is required\n    // to deal with an old Solidity bug, in which event data for\n    // external (not public thoguh) was tightly packed.\n    readonly allowLoose!: boolean;\n\n    readonly #data: Uint8Array;\n    #offset: number;\n\n    #bytesRead: number;\n    #parent: null | Reader;\n    #maxInflation: number;\n\n    constructor(data: BytesLike, allowLoose?: boolean, maxInflation?: number) {\n        defineProperties<Reader>(this, { allowLoose: !!allowLoose });\n\n        this.#data = getBytesCopy(data);\n        this.#bytesRead = 0;\n        this.#parent = null;\n        this.#maxInflation = (maxInflation != null) ? maxInflation: 1024;\n\n        this.#offset = 0;\n    }\n\n    get data(): string { return hexlify(this.#data); }\n    get dataLength(): number { return this.#data.length; }\n    get consumed(): number { return this.#offset; }\n    get bytes(): Uint8Array { return new Uint8Array(this.#data); }\n\n    #incrementBytesRead(count: number): void {\n        if (this.#parent) { return this.#parent.#incrementBytesRead(count); }\n\n        this.#bytesRead += count;\n\n        // Check for excessive inflation (see: #4537)\n        assert(this.#maxInflation < 1 || this.#bytesRead <= this.#maxInflation * this.dataLength, `compressed ABI data exceeds inflation ratio of ${ this.#maxInflation } ( see: https:/\\/github.com/ethers-io/ethers.js/issues/4537 )`,  \"BUFFER_OVERRUN\", {\n            buffer: getBytesCopy(this.#data), offset: this.#offset,\n            length: count, info: {\n                bytesRead: this.#bytesRead,\n                dataLength: this.dataLength\n            }\n        });\n    }\n\n    #peekBytes(offset: number, length: number, loose?: boolean): Uint8Array {\n        let alignedLength = Math.ceil(length / WordSize) * WordSize;\n        if (this.#offset + alignedLength > this.#data.length) {\n            if (this.allowLoose && loose && this.#offset + length <= this.#data.length) {\n                alignedLength = length;\n            } else {\n                assert(false, \"data out-of-bounds\", \"BUFFER_OVERRUN\", {\n                    buffer: getBytesCopy(this.#data),\n                    length: this.#data.length,\n                    offset: this.#offset + alignedLength\n                });\n            }\n        }\n        return this.#data.slice(this.#offset, this.#offset + alignedLength)\n    }\n\n    // Create a sub-reader with the same underlying data, but offset\n    subReader(offset: number): Reader {\n        const reader = new Reader(this.#data.slice(this.#offset + offset), this.allowLoose, this.#maxInflation);\n        reader.#parent = this;\n        return reader;\n    }\n\n    // Read bytes\n    readBytes(length: number, loose?: boolean): Uint8Array {\n        let bytes = this.#peekBytes(0, length, !!loose);\n        this.#incrementBytesRead(length);\n        this.#offset += bytes.length;\n        // @TODO: Make sure the length..end bytes are all 0?\n        return bytes.slice(0, length);\n    }\n\n    // Read a numeric values\n    readValue(): bigint {\n        return toBigInt(this.readBytes(WordSize));\n    }\n\n    readIndex(): number {\n        return toNumber(this.readBytes(WordSize));\n    }\n}\n"], "mappings": "AACA,SACIA,gBAAgB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,OAAO,EAC1DC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAC7BC,MAAM,EAAEC;AACR,qBACG,sBAAsB;AAI7B;;;AAGA,OAAO,MAAMC,QAAQ,GAAW,EAAE;AAClC,MAAMC,OAAO,GAAG,IAAIC,UAAU,CAACF,QAAQ,CAAC;AAExC;AACA;AACA,MAAMG,cAAc,GAAG,CAAE,MAAM,CAAE;AAEjC,MAAMC,MAAM,GAAG,EAAG;AAElB,MAAMC,WAAW,GAAkD,IAAIC,OAAO,EAAE;AAEhF,SAASC,QAAQA,CAACC,MAAc;EAC5B,OAAOH,WAAW,CAACI,GAAG,CAACD,MAAM,CAAE;AACnC;AACA,SAASE,QAAQA,CAACF,MAAc,EAAEG,KAAmC;EACjEN,WAAW,CAACO,GAAG,CAACJ,MAAM,EAAEG,KAAK,CAAC;AAClC;AAEA,SAASE,UAAUA,CAACC,IAAY,EAAEC,KAAY;EAC1C,MAAMC,OAAO,GAAG,IAAIC,KAAK,CAAC,0DAA2DH,IAAK,EAAE,CAAC;EACvFE,OAAQ,CAACD,KAAK,GAAGA,KAAK;EAC5B,MAAMC,OAAO;AACjB;AAEA,SAASE,QAAQA,CAACP,KAAmC,EAAEQ,KAAa,EAAEC,IAAc;EAChF,IAAIT,KAAK,CAACU,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;IAC1B,OAAOF,KAAK,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MAC7B,IAAID,IAAI,YAAYE,MAAM,EAAE;QACxB,OAAOP,QAAQ,CAACX,QAAQ,CAACgB,IAAI,CAAC,EAAEA,IAAI,EAAEH,IAAI,CAAC;;MAE/C,OAAOG,IAAI;IACf,CAAC,CAAC;;EAGN,OAAuBZ,KAAM,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEb,IAAI,EAAEU,KAAK,KAAI;IACxD,IAAID,IAAI,GAAGJ,KAAK,CAACS,QAAQ,CAACd,IAAI,CAAC;IAC/B,IAAI,EAAEA,IAAI,IAAIa,KAAK,CAAC,EAAE;MAClB,IAAIP,IAAI,IAAIG,IAAI,YAAYE,MAAM,EAAE;QAChCF,IAAI,GAAGL,QAAQ,CAACX,QAAQ,CAACgB,IAAI,CAAC,EAAEA,IAAI,EAAEH,IAAI,CAAC;;MAE/CO,KAAK,CAACb,IAAI,CAAC,GAAGS,IAAI;;IAEtB,OAAOI,KAAK;EAChB,CAAC,EAAuB,EAAG,CAAC;AAChC;AAGA;;;;;;;AAOA,OAAM,MAAOF,MAAO,SAAQI,KAAU;EAClC;EACA;EACA;EACS,CAAAlB,KAAM;EAIf;;;EAGAmB,YAAY,GAAGC,IAAgB;IAC3B;IACA;IACA;IACA;IACA;IAEA;IACA,MAAMC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;IACrB,IAAIZ,KAAK,GAAeY,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAIpB,KAAK,GAAyB,CAACoB,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,EAAEE,KAAK,EAAE;IAE1D,IAAIC,IAAI,GAAG,IAAI;IACf,IAAIF,KAAK,KAAK5B,MAAM,EAAE;MAClBe,KAAK,GAAGY,IAAI;MACZpB,KAAK,GAAG,EAAG;MACXuB,IAAI,GAAG,KAAK;;IAGhB;IACA;IACA,KAAK,CAACf,KAAK,CAACgB,MAAM,CAAC;IACnBhB,KAAK,CAACiB,OAAO,CAAC,CAACb,IAAI,EAAEC,KAAK,KAAI;MAAG,IAAI,CAACA,KAAK,CAAC,GAAGD,IAAI;IAAE,CAAC,CAAC;IAEvD;IACA,MAAMc,UAAU,GAAG1B,KAAK,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEb,IAAI,KAAI;MAC5C,IAAI,OAAOA,IAAK,KAAK,QAAQ,EAAE;QAC3Ba,KAAK,CAACf,GAAG,CAACE,IAAI,EAAE,CAACa,KAAK,CAAClB,GAAG,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;MAE/C,OAAOa,KAAK;IAChB,CAAC,EAAwB,IAAIW,GAAG,EAAG,CAAC;IAEpC;IACA5B,QAAQ,CAAC,IAAI,EAAE6B,MAAM,CAACC,MAAM,CAACrB,KAAK,CAACG,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;MACnD,MAAMV,IAAI,GAAGH,KAAK,CAACa,KAAK,CAAC;MACzB,IAAIV,IAAI,IAAI,IAAI,IAAIuB,UAAU,CAAC5B,GAAG,CAACK,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAOA,IAAI;;MAEf,OAAO,IAAI;IACf,CAAC,CAAC,CAAC,CAAC;IAEJ;IACA,IAAI,CAAC,CAAAH,KAAM,GAAG,EAAG;IACjB,IAAI,IAAI,CAAC,CAAAA,KAAM,IAAI,IAAI,EAAE;MAAE,KAAK,IAAI,CAAC,CAAAA,KAAO;;IAE5C,IAAI,CAACuB,IAAI,EAAE;MAAE;;IAEb;IACAK,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAEnB;IACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,IAAI,EAAE;MAC1BjC,GAAG,EAAEA,CAACkC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,KAAI;QAC5B,IAAI,OAAOD,IAAK,KAAK,QAAQ,EAAE;UAE3B;UACA,IAAIA,IAAI,CAACE,KAAK,CAAC,UAAU,CAAC,EAAE;YACxB,MAAMtB,KAAK,GAAG/B,SAAS,CAACmD,IAAI,EAAE,QAAQ,CAAC;YACvC,IAAIpB,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACW,MAAM,EAAE;cACnC,MAAM,IAAIY,UAAU,CAAC,qBAAqB,CAAC;;YAG/C,MAAMxB,IAAI,GAAGoB,MAAM,CAACnB,KAAK,CAAC;YAC1B,IAAID,IAAI,YAAYN,KAAK,EAAE;cACvBJ,UAAU,CAAC,SAAUW,KAAM,EAAE,EAAED,IAAI,CAAC;;YAExC,OAAOA,IAAI;;UAGf;UACA,IAAIpB,cAAc,CAACkB,OAAO,CAACuB,IAAI,CAAC,IAAI,CAAC,EAAE;YACnC,OAAOI,OAAO,CAACvC,GAAG,CAACkC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;;UAG9C,MAAMI,KAAK,GAAGN,MAAM,CAACC,IAAI,CAAC;UAC1B,IAAIK,KAAK,YAAYC,QAAQ,EAAE;YAC3B;YACA;YACA,OAAO,UAAoB,GAAGnB,IAAgB;cAC1C,OAAOkB,KAAK,CAACE,KAAK,CAAE,IAAI,KAAKN,QAAQ,GAAIF,MAAM,GAAE,IAAI,EAAEZ,IAAI,CAAC;YAChE,CAAC;WAEJ,MAAM,IAAI,EAAEa,IAAI,IAAID,MAAM,CAAC,EAAE;YAC1B;YACA,OAAOA,MAAM,CAACf,QAAQ,CAACuB,KAAK,CAAE,IAAI,KAAKN,QAAQ,GAAIF,MAAM,GAAE,IAAI,EAAE,CAAEC,IAAI,CAAE,CAAC;;;QAIlF,OAAOI,OAAO,CAACvC,GAAG,CAACkC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;MAC9C;KACH,CAAC;IACFnC,QAAQ,CAAC+B,KAAK,EAAElC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC/B,OAAOkC,KAAK;EAChB;EAEA;;;;;;;EAOAW,OAAOA,CAAChC,IAAc;IAClB,MAAMZ,MAAM,GAAe,EAAG;IAC9B,IAAI,CAAC4B,OAAO,CAAC,CAACb,IAAI,EAAEC,KAAK,KAAI;MACzB,IAAID,IAAI,YAAYN,KAAK,EAAE;QAAEJ,UAAU,CAAC,SAAUW,KAAM,EAAE,EAAED,IAAI,CAAC;;MACjE,IAAIH,IAAI,IAAIG,IAAI,YAAYE,MAAM,EAAE;QAChCF,IAAI,GAAGA,IAAI,CAAC6B,OAAO,CAAChC,IAAI,CAAC;;MAE7BZ,MAAM,CAAC6C,IAAI,CAAC9B,IAAI,CAAC;IACrB,CAAC,CAAC;IACF,OAAOf,MAAM;EACjB;EAEA;;;;;;;;EAQAU,QAAQA,CAACE,IAAc;IACnB,MAAMT,KAAK,GAAGJ,QAAQ,CAAC,IAAI,CAAC;IAC5B,OAAOI,KAAK,CAACe,MAAM,CAAC,CAACC,KAAK,EAAEb,IAAI,EAAEU,KAAK,KAAI;MAEvC1B,MAAM,CAACgB,IAAI,IAAI,IAAI,EAAE,kBAAmBU,KAAM,UAAU,EAAE,uBAAuB,EAAE;QAC/E8B,SAAS,EAAE;OACd,CAAC;MAEF,OAAOpC,QAAQ,CAACP,KAAK,EAAE,IAAI,EAAES,IAAI,CAAC;IACtC,CAAC,EAAuB,EAAE,CAAC;EAC/B;EAEA;;;EAGAa,KAAKA,CAACsB,KAA0B,EAAEC,GAAwB;IACtD,IAAID,KAAK,IAAI,IAAI,EAAE;MAAEA,KAAK,GAAG,CAAC;;IAC9B,IAAIA,KAAK,GAAG,CAAC,EAAE;MACXA,KAAK,IAAI,IAAI,CAACpB,MAAM;MACpB,IAAIoB,KAAK,GAAG,CAAC,EAAE;QAAEA,KAAK,GAAG,CAAC;;;IAG9B,IAAIC,GAAG,IAAI,IAAI,EAAE;MAAEA,GAAG,GAAG,IAAI,CAACrB,MAAM;;IACpC,IAAIqB,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,IAAI,IAAI,CAACrB,MAAM;MAClB,IAAIqB,GAAG,GAAG,CAAC,EAAE;QAAEA,GAAG,GAAG,CAAC;;;IAE1B,IAAIA,GAAG,GAAG,IAAI,CAACrB,MAAM,EAAE;MAAEqB,GAAG,GAAG,IAAI,CAACrB,MAAM;;IAE1C,MAAMsB,MAAM,GAAGlD,QAAQ,CAAC,IAAI,CAAC;IAE7B,MAAMC,MAAM,GAAe,EAAG;MAAEG,KAAK,GAAyB,EAAG;IACjE,KAAK,IAAI+C,CAAC,GAAGH,KAAK,EAAEG,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC9BlD,MAAM,CAAC6C,IAAI,CAAC,IAAI,CAACK,CAAC,CAAC,CAAC;MACpB/C,KAAK,CAAC0C,IAAI,CAACI,MAAM,CAACC,CAAC,CAAC,CAAC;;IAGzB,OAAO,IAAIjC,MAAM,CAACrB,MAAM,EAAEI,MAAM,EAAEG,KAAK,CAAC;EAC5C;EAEA;;;EAGAgD,MAAMA,CAACC,QAA4D,EAAEC,OAAa;IAC9E,MAAMJ,MAAM,GAAGlD,QAAQ,CAAC,IAAI,CAAC;IAE7B,MAAMC,MAAM,GAAe,EAAG;MAAEG,KAAK,GAAyB,EAAG;IACjE,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,MAAM,EAAEuB,CAAC,EAAE,EAAE;MAClC,MAAMnC,IAAI,GAAG,IAAI,CAACmC,CAAC,CAAC;MACpB,IAAInC,IAAI,YAAYN,KAAK,EAAE;QACvBJ,UAAU,CAAC,SAAU6C,CAAE,EAAE,EAAEnC,IAAI,CAAC;;MAGpC,IAAIqC,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAEtC,IAAI,EAAEmC,CAAC,EAAE,IAAI,CAAC,EAAE;QACvClD,MAAM,CAAC6C,IAAI,CAAC9B,IAAI,CAAC;QACjBZ,KAAK,CAAC0C,IAAI,CAACI,MAAM,CAACC,CAAC,CAAC,CAAC;;;IAI7B,OAAO,IAAIjC,MAAM,CAACrB,MAAM,EAAEI,MAAM,EAAEG,KAAK,CAAC;EAC5C;EAEA;;;EAGAW,GAAGA,CAAsBsC,QAAsD,EAAEC,OAAa;IAC1F,MAAMrD,MAAM,GAAa,EAAG;IAC5B,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,MAAM,EAAEuB,CAAC,EAAE,EAAE;MAClC,MAAMnC,IAAI,GAAG,IAAI,CAACmC,CAAC,CAAC;MACpB,IAAInC,IAAI,YAAYN,KAAK,EAAE;QACvBJ,UAAU,CAAC,SAAU6C,CAAE,EAAE,EAAEnC,IAAI,CAAC;;MAGpCf,MAAM,CAAC6C,IAAI,CAACO,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAEtC,IAAI,EAAEmC,CAAC,EAAE,IAAI,CAAC,CAAC;;IAGtD,OAAOlD,MAAM;EACjB;EAGA;;;;;;;;EAQAoB,QAAQA,CAACd,IAAY;IACjB,MAAMU,KAAK,GAAGjB,QAAQ,CAAC,IAAI,CAAC,CAACc,OAAO,CAACP,IAAI,CAAC;IAC1C,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;MAAE,OAAOuC,SAAS;;IAEpC,MAAMd,KAAK,GAAG,IAAI,CAACzB,KAAK,CAAC;IAEzB,IAAIyB,KAAK,YAAYhC,KAAK,EAAE;MACxBJ,UAAU,CAAC,YAAamD,IAAI,CAACC,SAAS,CAACnD,IAAI,CAAE,EAAE,EAAQmC,KAAM,CAAClC,KAAK,CAAC;;IAGxE,OAAOkC,KAAK;EAChB;EAEA;;;;EAIA,OAAOiB,SAASA,CAAC/C,KAAiB,EAAEgD,IAA2B;IAC3D,OAAO,IAAI1C,MAAM,CAACrB,MAAM,EAAEe,KAAK,EAAEgD,IAAI,CAAC;EAC1C;;AAGJ;;;;;;;;;;;;;AAaA,OAAM,SAAUC,iBAAiBA,CAAC5D,MAAc;EAC5C;EACA,MAAM6D,MAAM,GAA0D,EAAG;EAEzE,MAAMC,WAAW,GAAG,SAAAA,CAASC,IAA4B,EAAEC,MAAW;IAClE,IAAI,CAAC3C,KAAK,CAAC4C,OAAO,CAACD,MAAM,CAAC,EAAE;MAAE;;IAC9B,KAAK,IAAIE,GAAG,IAAIF,MAAM,EAAE;MACpB,MAAMG,SAAS,GAAGJ,IAAI,CAACtC,KAAK,EAAE;MAC9B0C,SAAS,CAACtB,IAAI,CAACqB,GAAG,CAAC;MAEnB,IAAI;QACCJ,WAAW,CAACK,SAAS,EAAEH,MAAM,CAACE,GAAG,CAAC,CAAC;OACvC,CAAC,OAAO3D,KAAU,EAAE;QACjBsD,MAAM,CAAChB,IAAI,CAAC;UAAEkB,IAAI,EAAEI,SAAS;UAAE5D,KAAK,EAAEA;QAAK,CAAE,CAAC;;;EAG1D,CAAC;EACDuD,WAAW,CAAC,EAAG,EAAE9D,MAAM,CAAC;EAExB,OAAO6D,MAAM;AAEjB;AAEA,SAASzC,QAAQA,CAACqB,KAAmB;EACjC,IAAI2B,KAAK,GAAGjF,SAAS,CAACsD,KAAK,CAAC;EAE5BnD,MAAM,CAAE8E,KAAK,CAACzC,MAAM,IAAInC,QAAQ,EAAE,qBAAqB,EACnD,gBAAgB,EAAE;IAAE6E,MAAM,EAAED,KAAK;IAAEzC,MAAM,EAAEnC,QAAQ;IAAE8E,MAAM,EAAEF,KAAK,CAACzC;EAAM,CAAE,CAAC;EAEhF,IAAIyC,KAAK,CAACzC,MAAM,KAAKnC,QAAQ,EAAE;IAC3B4E,KAAK,GAAGpF,YAAY,CAACD,MAAM,CAAC,CAAEU,OAAO,CAACgC,KAAK,CAAC2C,KAAK,CAACzC,MAAM,GAAGnC,QAAQ,CAAC,EAAE4E,KAAK,CAAE,CAAC,CAAC;;EAGnF,OAAOA,KAAK;AAChB;AAEA;;;AAGA,OAAM,MAAgBG,KAAK;EAEvB;EACA;EACSjE,IAAI;EAEb;EACA;EACSkE,IAAI;EAEb;EACA;EACSC,SAAS;EAElB;EACA;EACA;EACSC,OAAO;EAEhBpD,YAAYhB,IAAY,EAAEkE,IAAY,EAAEC,SAAiB,EAAEC,OAAgB;IACvE5F,gBAAgB,CAAQ,IAAI,EAAE;MAAEwB,IAAI;MAAEkE,IAAI;MAAEC,SAAS;MAAEC;IAAO,CAAE,EAAE;MAC9DpE,IAAI,EAAE,QAAQ;MAAEkE,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAE,QAAQ;MAAEC,OAAO,EAAE;KACjE,CAAC;EACN;EAEAC,WAAWA,CAACC,OAAe,EAAEnC,KAAU;IACnClD,cAAc,CAAC,KAAK,EAAEqF,OAAO,EAAE,IAAI,CAACH,SAAS,EAAEhC,KAAK,CAAC;EACzD;;AAQJ;;;AAGA,OAAM,MAAOoC,MAAM;EACf;EACA,CAAAC,IAAK;EACL,CAAAC,UAAW;EAEXzD,YAAA;IACI,IAAI,CAAC,CAAAwD,IAAK,GAAG,EAAG;IAChB,IAAI,CAAC,CAAAC,UAAW,GAAG,CAAC;EACxB;EAEA,IAAID,IAAIA,CAAA;IACJ,OAAO/F,MAAM,CAAC,IAAI,CAAC,CAAA+F,IAAK,CAAC;EAC7B;EACA,IAAInD,MAAMA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAoD,UAAW;EAAE;EAEhD,CAAAC,SAAUC,CAACH,IAAgB;IACvB,IAAI,CAAC,CAAAA,IAAK,CAACjC,IAAI,CAACiC,IAAI,CAAC;IACrB,IAAI,CAAC,CAAAC,UAAW,IAAID,IAAI,CAACnD,MAAM;IAC/B,OAAOmD,IAAI,CAACnD,MAAM;EACtB;EAEAuD,YAAYA,CAACC,MAAc;IACvB,OAAO,IAAI,CAAC,CAAAH,SAAU,CAAChG,YAAY,CAACmG,MAAM,CAACL,IAAI,CAAC,CAAC;EACrD;EAEA;EACAM,UAAUA,CAAC3C,KAAgB;IACvB,IAAI2B,KAAK,GAAGpF,YAAY,CAACyD,KAAK,CAAC;IAC/B,MAAM4C,aAAa,GAAGjB,KAAK,CAACzC,MAAM,GAAGnC,QAAQ;IAC7C,IAAI6F,aAAa,EAAE;MACfjB,KAAK,GAAGpF,YAAY,CAACD,MAAM,CAAC,CAAEqF,KAAK,EAAE3E,OAAO,CAACgC,KAAK,CAAC4D,aAAa,CAAC,CAAE,CAAC,CAAC;;IAEzE,OAAO,IAAI,CAAC,CAAAL,SAAU,CAACZ,KAAK,CAAC;EACjC;EAEA;EACAkB,UAAUA,CAAC7C,KAAmB;IAC1B,OAAO,IAAI,CAAC,CAAAuC,SAAU,CAAC5D,QAAQ,CAACqB,KAAK,CAAC,CAAC;EAC3C;EAEA;EACA;EACA8C,mBAAmBA,CAAA;IACf,MAAMjB,MAAM,GAAG,IAAI,CAAC,CAAAQ,IAAK,CAACnD,MAAM;IAChC,IAAI,CAAC,CAAAmD,IAAK,CAACjC,IAAI,CAACpD,OAAO,CAAC;IACxB,IAAI,CAAC,CAAAsF,UAAW,IAAIvF,QAAQ;IAC5B,OAAQiD,KAAmB,IAAI;MAC3B,IAAI,CAAC,CAAAqC,IAAK,CAACR,MAAM,CAAC,GAAGlD,QAAQ,CAACqB,KAAK,CAAC;IACxC,CAAC;EACL;;AAGJ;;;AAGA,OAAM,MAAO+C,MAAM;EACf;EACA;EACA;EACA;EACSC,UAAU;EAEV,CAAAX,IAAK;EACd,CAAAR,MAAO;EAEP,CAAAoB,SAAU;EACV,CAAAC,MAAO;EACP,CAAAC,YAAa;EAEbtE,YAAYwD,IAAe,EAAEW,UAAoB,EAAEG,YAAqB;IACpE9G,gBAAgB,CAAS,IAAI,EAAE;MAAE2G,UAAU,EAAE,CAAC,CAACA;IAAU,CAAE,CAAC;IAE5D,IAAI,CAAC,CAAAX,IAAK,GAAG9F,YAAY,CAAC8F,IAAI,CAAC;IAC/B,IAAI,CAAC,CAAAY,SAAU,GAAG,CAAC;IACnB,IAAI,CAAC,CAAAC,MAAO,GAAG,IAAI;IACnB,IAAI,CAAC,CAAAC,YAAa,GAAIA,YAAY,IAAI,IAAI,GAAIA,YAAY,GAAE,IAAI;IAEhE,IAAI,CAAC,CAAAtB,MAAO,GAAG,CAAC;EACpB;EAEA,IAAIQ,IAAIA,CAAA;IAAa,OAAO5F,OAAO,CAAC,IAAI,CAAC,CAAA4F,IAAK,CAAC;EAAE;EACjD,IAAIC,UAAUA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAD,IAAK,CAACnD,MAAM;EAAE;EACrD,IAAIkE,QAAQA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAvB,MAAO;EAAE;EAC9C,IAAIF,KAAKA,CAAA;IAAiB,OAAO,IAAI1E,UAAU,CAAC,IAAI,CAAC,CAAAoF,IAAK,CAAC;EAAE;EAE7D,CAAAgB,kBAAmBC,CAACC,KAAa;IAC7B,IAAI,IAAI,CAAC,CAAAL,MAAO,EAAE;MAAE,OAAO,IAAI,CAAC,CAAAA,MAAO,CAAC,CAAAG,kBAAmB,CAACE,KAAK,CAAC;;IAElE,IAAI,CAAC,CAAAN,SAAU,IAAIM,KAAK;IAExB;IACA1G,MAAM,CAAC,IAAI,CAAC,CAAAsG,YAAa,GAAG,CAAC,IAAI,IAAI,CAAC,CAAAF,SAAU,IAAI,IAAI,CAAC,CAAAE,YAAa,GAAG,IAAI,CAACb,UAAU,EAAE,kDAAmD,IAAI,CAAC,CAAAa,YAAc,+DAA+D,EAAG,gBAAgB,EAAE;MAChPvB,MAAM,EAAErF,YAAY,CAAC,IAAI,CAAC,CAAA8F,IAAK,CAAC;MAAER,MAAM,EAAE,IAAI,CAAC,CAAAA,MAAO;MACtD3C,MAAM,EAAEqE,KAAK;MAAEC,IAAI,EAAE;QACjBP,SAAS,EAAE,IAAI,CAAC,CAAAA,SAAU;QAC1BX,UAAU,EAAE,IAAI,CAACA;;KAExB,CAAC;EACN;EAEA,CAAAmB,SAAUC,CAAC7B,MAAc,EAAE3C,MAAc,EAAEyE,KAAe;IACtD,IAAIC,aAAa,GAAGC,IAAI,CAACC,IAAI,CAAC5E,MAAM,GAAGnC,QAAQ,CAAC,GAAGA,QAAQ;IAC3D,IAAI,IAAI,CAAC,CAAA8E,MAAO,GAAG+B,aAAa,GAAG,IAAI,CAAC,CAAAvB,IAAK,CAACnD,MAAM,EAAE;MAClD,IAAI,IAAI,CAAC8D,UAAU,IAAIW,KAAK,IAAI,IAAI,CAAC,CAAA9B,MAAO,GAAG3C,MAAM,IAAI,IAAI,CAAC,CAAAmD,IAAK,CAACnD,MAAM,EAAE;QACxE0E,aAAa,GAAG1E,MAAM;OACzB,MAAM;QACHrC,MAAM,CAAC,KAAK,EAAE,oBAAoB,EAAE,gBAAgB,EAAE;UAClD+E,MAAM,EAAErF,YAAY,CAAC,IAAI,CAAC,CAAA8F,IAAK,CAAC;UAChCnD,MAAM,EAAE,IAAI,CAAC,CAAAmD,IAAK,CAACnD,MAAM;UACzB2C,MAAM,EAAE,IAAI,CAAC,CAAAA,MAAO,GAAG+B;SAC1B,CAAC;;;IAGV,OAAO,IAAI,CAAC,CAAAvB,IAAK,CAACrD,KAAK,CAAC,IAAI,CAAC,CAAA6C,MAAO,EAAE,IAAI,CAAC,CAAAA,MAAO,GAAG+B,aAAa,CAAC;EACvE;EAEA;EACAG,SAASA,CAAClC,MAAc;IACpB,MAAMmC,MAAM,GAAG,IAAIjB,MAAM,CAAC,IAAI,CAAC,CAAAV,IAAK,CAACrD,KAAK,CAAC,IAAI,CAAC,CAAA6C,MAAO,GAAGA,MAAM,CAAC,EAAE,IAAI,CAACmB,UAAU,EAAE,IAAI,CAAC,CAAAG,YAAa,CAAC;IACvGa,MAAM,CAAC,CAAAd,MAAO,GAAG,IAAI;IACrB,OAAOc,MAAM;EACjB;EAEA;EACAC,SAASA,CAAC/E,MAAc,EAAEyE,KAAe;IACrC,IAAIhC,KAAK,GAAG,IAAI,CAAC,CAAA8B,SAAU,CAAC,CAAC,EAAEvE,MAAM,EAAE,CAAC,CAACyE,KAAK,CAAC;IAC/C,IAAI,CAAC,CAAAN,kBAAmB,CAACnE,MAAM,CAAC;IAChC,IAAI,CAAC,CAAA2C,MAAO,IAAIF,KAAK,CAACzC,MAAM;IAC5B;IACA,OAAOyC,KAAK,CAAC3C,KAAK,CAAC,CAAC,EAAEE,MAAM,CAAC;EACjC;EAEA;EACAgF,SAASA,CAAA;IACL,OAAOvH,QAAQ,CAAC,IAAI,CAACsH,SAAS,CAAClH,QAAQ,CAAC,CAAC;EAC7C;EAEAoH,SAASA,CAAA;IACL,OAAOvH,QAAQ,CAAC,IAAI,CAACqH,SAAS,CAAClH,QAAQ,CAAC,CAAC;EAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}