{"ast": null, "code": "/**\n *  A constant for the zero address.\n *\n *  (**i.e.** ``\"******************************************\"``)\n */\nexport const ZeroAddress = \"******************************************\";", "map": {"version": 3, "names": ["ZeroAddress"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\constants\\addresses.ts"], "sourcesContent": ["\n/**\n *  A constant for the zero address.\n *\n *  (**i.e.** ``\"******************************************\"``)\n */\nexport const ZeroAddress: string = \"******************************************\";\n\n"], "mappings": "AACA;;;;;AAKA,OAAO,MAAMA,WAAW,GAAW,4CAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}