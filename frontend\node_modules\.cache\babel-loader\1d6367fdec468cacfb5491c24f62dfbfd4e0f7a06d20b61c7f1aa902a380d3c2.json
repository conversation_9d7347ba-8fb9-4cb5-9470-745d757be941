{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { keccak256 } from \"../crypto/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport { assertArgument, concat, encodeRlp, toBeArray } from \"../utils/index.js\";\n/**\n *  Computes the [[link-eip-7702]] authorization digest to sign.\n */\nexport function hashAuthorization(auth) {\n  assertArgument(typeof auth.address === \"string\", \"invalid address for hashAuthorization\", \"auth.address\", auth);\n  return keccak256(concat([\"0x05\", encodeRlp([auth.chainId != null ? toBeArray(auth.chainId) : \"0x\", getAddress(auth.address), auth.nonce != null ? toBeArray(auth.nonce) : \"0x\"])]));\n}\n/**\n *  Return the address of the private key that produced\n *  the signature %%sig%% during signing for %%message%%.\n */\nexport function verifyAuthorization(auth, sig) {\n  return recoverAddress(hashAuthorization(auth), sig);\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "keccak256", "recoverAddress", "assertArgument", "concat", "encodeRlp", "toBeArray", "hashAuthorization", "auth", "address", "chainId", "nonce", "verifyAuthorization", "sig"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\authorization.ts"], "sourcesContent": ["import { getAddress } from \"../address/index.js\";\nimport { keccak256 } from \"../crypto/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport {\n    assertArgument, concat, encodeRlp, toBeArray\n} from \"../utils/index.js\";\n\nimport type { Addressable } from \"../address/index.js\";\nimport type { SignatureLike } from \"../crypto/index.js\";\nimport type { BigNumberish, Numeric } from \"../utils/index.js\";\n\nexport interface AuthorizationRequest {\n    address: string | Addressable;\n    nonce?: Numeric;\n    chainId?: BigNumberish;\n}\n\n/**\n *  Computes the [[link-eip-7702]] authorization digest to sign.\n */\nexport function hashAuthorization(auth: AuthorizationRequest): string {\n    assertArgument(typeof(auth.address) === \"string\", \"invalid address for hashAuthorization\", \"auth.address\", auth);\n    return keccak256(concat([\n        \"0x05\", encodeRlp([\n            (auth.chainId != null) ? toBeArray(auth.chainId): \"0x\",\n            getAddress(auth.address),\n            (auth.nonce != null) ? toBeArray(auth.nonce): \"0x\",\n        ])\n    ]));\n}\n\n/**\n *  Return the address of the private key that produced\n *  the signature %%sig%% during signing for %%message%%.\n */\nexport function verifyAuthorization(auth: AuthorizationRequest, sig: SignatureLike): string {\n    return recoverAddress(hashAuthorization(auth), sig);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SACIC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,QACzC,mBAAmB;AAY1B;;;AAGA,OAAM,SAAUC,iBAAiBA,CAACC,IAA0B;EACxDL,cAAc,CAAC,OAAOK,IAAI,CAACC,OAAQ,KAAK,QAAQ,EAAE,uCAAuC,EAAE,cAAc,EAAED,IAAI,CAAC;EAChH,OAAOP,SAAS,CAACG,MAAM,CAAC,CACpB,MAAM,EAAEC,SAAS,CAAC,CACbG,IAAI,CAACE,OAAO,IAAI,IAAI,GAAIJ,SAAS,CAACE,IAAI,CAACE,OAAO,CAAC,GAAE,IAAI,EACtDV,UAAU,CAACQ,IAAI,CAACC,OAAO,CAAC,EACvBD,IAAI,CAACG,KAAK,IAAI,IAAI,GAAIL,SAAS,CAACE,IAAI,CAACG,KAAK,CAAC,GAAE,IAAI,CACrD,CAAC,CACL,CAAC,CAAC;AACP;AAEA;;;;AAIA,OAAM,SAAUC,mBAAmBA,CAACJ,IAA0B,EAAEK,GAAkB;EAC9E,OAAOX,cAAc,CAACK,iBAAiB,CAACC,IAAI,CAAC,EAAEK,GAAG,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}