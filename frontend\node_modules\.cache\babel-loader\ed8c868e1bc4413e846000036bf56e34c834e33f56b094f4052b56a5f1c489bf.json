{"ast": null, "code": "/**\n *  A **Network** encapsulates the various properties required to\n *  interact with a specific chain.\n *\n *  @_subsection: api/providers:Networks  [networks]\n */\nimport { accessListify } from \"../transaction/index.js\";\nimport { getBigInt, assert, assertArgument } from \"../utils/index.js\";\nimport { EnsPlugin, FetchUrlFeeDataNetworkPlugin, GasCostPlugin } from \"./plugins-network.js\";\n/* * * *\n// Networks which operation against an L2 can use this plugin to\n// specify how to access L1, for the purpose of resolving ENS,\n// for example.\nexport class LayerOneConnectionPlugin extends NetworkPlugin {\n    readonly provider!: Provider;\n// @TODO: Rename to ChainAccess and allow for connecting to any chain\n    constructor(provider: Provider) {\n        super(\"org.ethers.plugins.layer-one-connection\");\n        defineProperties<LayerOneConnectionPlugin>(this, { provider });\n    }\n\n    clone(): LayerOneConnectionPlugin {\n        return new LayerOneConnectionPlugin(this.provider);\n    }\n}\n*/\nconst Networks = new Map();\n/**\n *  A **Network** provides access to a chain's properties and allows\n *  for plug-ins to extend functionality.\n */\nexport class Network {\n  #name;\n  #chainId;\n  #plugins;\n  /**\n   *  Creates a new **Network** for %%name%% and %%chainId%%.\n   */\n  constructor(name, chainId) {\n    this.#name = name;\n    this.#chainId = getBigInt(chainId);\n    this.#plugins = new Map();\n  }\n  /**\n   *  Returns a JSON-compatible representation of a Network.\n   */\n  toJSON() {\n    return {\n      name: this.name,\n      chainId: String(this.chainId)\n    };\n  }\n  /**\n   *  The network common name.\n   *\n   *  This is the canonical name, as networks migh have multiple\n   *  names.\n   */\n  get name() {\n    return this.#name;\n  }\n  set name(value) {\n    this.#name = value;\n  }\n  /**\n   *  The network chain ID.\n   */\n  get chainId() {\n    return this.#chainId;\n  }\n  set chainId(value) {\n    this.#chainId = getBigInt(value, \"chainId\");\n  }\n  /**\n   *  Returns true if %%other%% matches this network. Any chain ID\n   *  must match, and if no chain ID is present, the name must match.\n   *\n   *  This method does not currently check for additional properties,\n   *  such as ENS address or plug-in compatibility.\n   */\n  matches(other) {\n    if (other == null) {\n      return false;\n    }\n    if (typeof other === \"string\") {\n      try {\n        return this.chainId === getBigInt(other);\n      } catch (error) {}\n      return this.name === other;\n    }\n    if (typeof other === \"number\" || typeof other === \"bigint\") {\n      try {\n        return this.chainId === getBigInt(other);\n      } catch (error) {}\n      return false;\n    }\n    if (typeof other === \"object\") {\n      if (other.chainId != null) {\n        try {\n          return this.chainId === getBigInt(other.chainId);\n        } catch (error) {}\n        return false;\n      }\n      if (other.name != null) {\n        return this.name === other.name;\n      }\n      return false;\n    }\n    return false;\n  }\n  /**\n   *  Returns the list of plugins currently attached to this Network.\n   */\n  get plugins() {\n    return Array.from(this.#plugins.values());\n  }\n  /**\n   *  Attach a new %%plugin%% to this Network. The network name\n   *  must be unique, excluding any fragment.\n   */\n  attachPlugin(plugin) {\n    if (this.#plugins.get(plugin.name)) {\n      throw new Error(`cannot replace existing plugin: ${plugin.name} `);\n    }\n    this.#plugins.set(plugin.name, plugin.clone());\n    return this;\n  }\n  /**\n   *  Return the plugin, if any, matching %%name%% exactly. Plugins\n   *  with fragments will not be returned unless %%name%% includes\n   *  a fragment.\n   */\n  getPlugin(name) {\n    return this.#plugins.get(name) || null;\n  }\n  /**\n   *  Gets a list of all plugins that match %%name%%, with otr without\n   *  a fragment.\n   */\n  getPlugins(basename) {\n    return this.plugins.filter(p => p.name.split(\"#\")[0] === basename);\n  }\n  /**\n   *  Create a copy of this Network.\n   */\n  clone() {\n    const clone = new Network(this.name, this.chainId);\n    this.plugins.forEach(plugin => {\n      clone.attachPlugin(plugin.clone());\n    });\n    return clone;\n  }\n  /**\n   *  Compute the intrinsic gas required for a transaction.\n   *\n   *  A GasCostPlugin can be attached to override the default\n   *  values.\n   */\n  computeIntrinsicGas(tx) {\n    const costs = this.getPlugin(\"org.ethers.plugins.network.GasCost\") || new GasCostPlugin();\n    let gas = costs.txBase;\n    if (tx.to == null) {\n      gas += costs.txCreate;\n    }\n    if (tx.data) {\n      for (let i = 2; i < tx.data.length; i += 2) {\n        if (tx.data.substring(i, i + 2) === \"00\") {\n          gas += costs.txDataZero;\n        } else {\n          gas += costs.txDataNonzero;\n        }\n      }\n    }\n    if (tx.accessList) {\n      const accessList = accessListify(tx.accessList);\n      for (const addr in accessList) {\n        gas += costs.txAccessListAddress + costs.txAccessListStorageKey * accessList[addr].storageKeys.length;\n      }\n    }\n    return gas;\n  }\n  /**\n   *  Returns a new Network for the %%network%% name or chainId.\n   */\n  static from(network) {\n    injectCommonNetworks();\n    // Default network\n    if (network == null) {\n      return Network.from(\"mainnet\");\n    }\n    // Canonical name or chain ID\n    if (typeof network === \"number\") {\n      network = BigInt(network);\n    }\n    if (typeof network === \"string\" || typeof network === \"bigint\") {\n      const networkFunc = Networks.get(network);\n      if (networkFunc) {\n        return networkFunc();\n      }\n      if (typeof network === \"bigint\") {\n        return new Network(\"unknown\", network);\n      }\n      assertArgument(false, \"unknown network\", \"network\", network);\n    }\n    // Clonable with network-like abilities\n    if (typeof network.clone === \"function\") {\n      const clone = network.clone();\n      //if (typeof(network.name) !== \"string\" || typeof(network.chainId) !== \"number\") {\n      //}\n      return clone;\n    }\n    // Networkish\n    if (typeof network === \"object\") {\n      assertArgument(typeof network.name === \"string\" && typeof network.chainId === \"number\", \"invalid network object name or chainId\", \"network\", network);\n      const custom = new Network(network.name, network.chainId);\n      if (network.ensAddress || network.ensNetwork != null) {\n        custom.attachPlugin(new EnsPlugin(network.ensAddress, network.ensNetwork));\n      }\n      //if ((<any>network).layerOneConnection) {\n      //    custom.attachPlugin(new LayerOneConnectionPlugin((<any>network).layerOneConnection));\n      //}\n      return custom;\n    }\n    assertArgument(false, \"invalid network\", \"network\", network);\n  }\n  /**\n   *  Register %%nameOrChainId%% with a function which returns\n   *  an instance of a Network representing that chain.\n   */\n  static register(nameOrChainId, networkFunc) {\n    if (typeof nameOrChainId === \"number\") {\n      nameOrChainId = BigInt(nameOrChainId);\n    }\n    const existing = Networks.get(nameOrChainId);\n    if (existing) {\n      assertArgument(false, `conflicting network for ${JSON.stringify(existing.name)}`, \"nameOrChainId\", nameOrChainId);\n    }\n    Networks.set(nameOrChainId, networkFunc);\n  }\n}\n// We don't want to bring in formatUnits because it is backed by\n// FixedNumber and we want to keep Networks tiny. The values\n// included by the Gas Stations are also IEEE 754 with lots of\n// rounding issues and exceed the strict checks formatUnits has.\nfunction parseUnits(_value, decimals) {\n  const value = String(_value);\n  if (!value.match(/^[0-9.]+$/)) {\n    throw new Error(`invalid gwei value: ${_value}`);\n  }\n  // Break into [ whole, fraction ]\n  const comps = value.split(\".\");\n  if (comps.length === 1) {\n    comps.push(\"\");\n  }\n  // More than 1 decimal point or too many fractional positions\n  if (comps.length !== 2) {\n    throw new Error(`invalid gwei value: ${_value}`);\n  }\n  // Pad the fraction to 9 decimalplaces\n  while (comps[1].length < decimals) {\n    comps[1] += \"0\";\n  }\n  // Too many decimals and some non-zero ending, take the ceiling\n  if (comps[1].length > 9) {\n    let frac = BigInt(comps[1].substring(0, 9));\n    if (!comps[1].substring(9).match(/^0+$/)) {\n      frac++;\n    }\n    comps[1] = frac.toString();\n  }\n  return BigInt(comps[0] + comps[1]);\n}\n// Used by Polygon to use a gas station for fee data\nfunction getGasStationPlugin(url) {\n  return new FetchUrlFeeDataNetworkPlugin(url, async (fetchFeeData, provider, request) => {\n    // Prevent Cloudflare from blocking our request in node.js\n    request.setHeader(\"User-Agent\", \"ethers\");\n    let response;\n    try {\n      const [_response, _feeData] = await Promise.all([request.send(), fetchFeeData()]);\n      response = _response;\n      const payload = response.bodyJson.standard;\n      const feeData = {\n        gasPrice: _feeData.gasPrice,\n        maxFeePerGas: parseUnits(payload.maxFee, 9),\n        maxPriorityFeePerGas: parseUnits(payload.maxPriorityFee, 9)\n      };\n      return feeData;\n    } catch (error) {\n      assert(false, `error encountered with polygon gas station (${JSON.stringify(request.url)})`, \"SERVER_ERROR\", {\n        request,\n        response,\n        error\n      });\n    }\n  });\n}\n// See: https://chainlist.org\nlet injected = false;\nfunction injectCommonNetworks() {\n  if (injected) {\n    return;\n  }\n  injected = true;\n  /// Register popular Ethereum networks\n  function registerEth(name, chainId, options) {\n    const func = function () {\n      const network = new Network(name, chainId);\n      // We use 0 to disable ENS\n      if (options.ensNetwork != null) {\n        network.attachPlugin(new EnsPlugin(null, options.ensNetwork));\n      }\n      network.attachPlugin(new GasCostPlugin());\n      (options.plugins || []).forEach(plugin => {\n        network.attachPlugin(plugin);\n      });\n      return network;\n    };\n    // Register the network by name and chain ID\n    Network.register(name, func);\n    Network.register(chainId, func);\n    if (options.altNames) {\n      options.altNames.forEach(name => {\n        Network.register(name, func);\n      });\n    }\n  }\n  registerEth(\"mainnet\", 1, {\n    ensNetwork: 1,\n    altNames: [\"homestead\"]\n  });\n  registerEth(\"ropsten\", 3, {\n    ensNetwork: 3\n  });\n  registerEth(\"rinkeby\", 4, {\n    ensNetwork: 4\n  });\n  registerEth(\"goerli\", 5, {\n    ensNetwork: 5\n  });\n  registerEth(\"kovan\", 42, {\n    ensNetwork: 42\n  });\n  registerEth(\"sepolia\", 11155111, {\n    ensNetwork: 11155111\n  });\n  registerEth(\"holesky\", 17000, {\n    ensNetwork: 17000\n  });\n  registerEth(\"classic\", 61, {});\n  registerEth(\"classicKotti\", 6, {});\n  registerEth(\"arbitrum\", 42161, {\n    ensNetwork: 1\n  });\n  registerEth(\"arbitrum-goerli\", 421613, {});\n  registerEth(\"arbitrum-sepolia\", 421614, {});\n  registerEth(\"base\", 8453, {\n    ensNetwork: 1\n  });\n  registerEth(\"base-goerli\", 84531, {});\n  registerEth(\"base-sepolia\", 84532, {});\n  registerEth(\"bnb\", 56, {\n    ensNetwork: 1\n  });\n  registerEth(\"bnbt\", 97, {});\n  registerEth(\"linea\", 59144, {\n    ensNetwork: 1\n  });\n  registerEth(\"linea-goerli\", 59140, {});\n  registerEth(\"linea-sepolia\", 59141, {});\n  registerEth(\"matic\", 137, {\n    ensNetwork: 1,\n    plugins: [getGasStationPlugin(\"https:/\\/gasstation.polygon.technology/v2\")]\n  });\n  registerEth(\"matic-amoy\", 80002, {});\n  registerEth(\"matic-mumbai\", 80001, {\n    altNames: [\"maticMumbai\", \"maticmum\"],\n    plugins: [getGasStationPlugin(\"https:/\\/gasstation-testnet.polygon.technology/v2\")]\n  });\n  registerEth(\"optimism\", 10, {\n    ensNetwork: 1,\n    plugins: []\n  });\n  registerEth(\"optimism-goerli\", 420, {});\n  registerEth(\"optimism-sepolia\", 11155420, {});\n  registerEth(\"xdai\", 100, {\n    ensNetwork: 1\n  });\n}", "map": {"version": 3, "names": ["accessListify", "getBigInt", "assert", "assertArgument", "EnsPlugin", "FetchUrlFeeDataNetworkPlugin", "GasCostPlugin", "Networks", "Map", "Network", "name", "chainId", "plugins", "constructor", "toJSON", "String", "value", "matches", "other", "error", "Array", "from", "values", "attachPlugin", "plugin", "get", "Error", "set", "clone", "getPlugin", "getPlugins", "basename", "filter", "p", "split", "for<PERSON>ach", "computeIntrinsicGas", "tx", "costs", "gas", "txBase", "to", "txCreate", "data", "i", "length", "substring", "txDataZero", "txDataNonzero", "accessList", "addr", "txAccessListAddress", "txAccessListStorageKey", "storageKeys", "network", "injectCommonNetworks", "BigInt", "networkFunc", "custom", "ensAddress", "ensNetwork", "register", "nameOrChainId", "existing", "JSON", "stringify", "parseUnits", "_value", "decimals", "match", "comps", "push", "frac", "toString", "getGasStationPlugin", "url", "fetchFeeData", "provider", "request", "<PERSON><PERSON><PERSON><PERSON>", "response", "_response", "_feeData", "Promise", "all", "send", "payload", "bodyJson", "standard", "feeData", "gasPrice", "maxFeePer<PERSON>as", "maxFee", "maxPriorityFeePerGas", "maxPriorityFee", "injected", "registerEth", "options", "func", "altNames"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\network.ts"], "sourcesContent": ["/**\n *  A **Network** encapsulates the various properties required to\n *  interact with a specific chain.\n *\n *  @_subsection: api/providers:Networks  [networks]\n */\n\nimport { accessListify } from \"../transaction/index.js\";\nimport { getBigInt, assert, assertArgument } from \"../utils/index.js\";\n\nimport {\n    EnsPlugin, FetchUrlFeeDataNetworkPlugin, GasCostPlugin\n} from \"./plugins-network.js\";\n\nimport type { BigNumberish } from \"../utils/index.js\";\nimport type { TransactionLike } from \"../transaction/index.js\";\n\nimport type { NetworkPlugin } from \"./plugins-network.js\";\n\n\n/**\n *  A Networkish can be used to allude to a Network, by specifing:\n *  - a [[Network]] object\n *  - a well-known (or registered) network name\n *  - a well-known (or registered) chain ID\n *  - an object with sufficient details to describe a network\n */\nexport type Networkish = Network | number | bigint | string | {\n    name?: string,\n    chainId?: number,\n    //layerOneConnection?: Provider,\n    ensAddress?: string,\n    ensNetwork?: number\n};\n\n\n\n\n/* * * *\n// Networks which operation against an L2 can use this plugin to\n// specify how to access L1, for the purpose of resolving ENS,\n// for example.\nexport class LayerOneConnectionPlugin extends NetworkPlugin {\n    readonly provider!: Provider;\n// @TODO: Rename to ChainAccess and allow for connecting to any chain\n    constructor(provider: Provider) {\n        super(\"org.ethers.plugins.layer-one-connection\");\n        defineProperties<LayerOneConnectionPlugin>(this, { provider });\n    }\n\n    clone(): LayerOneConnectionPlugin {\n        return new LayerOneConnectionPlugin(this.provider);\n    }\n}\n*/\n\n\nconst Networks: Map<string | bigint, () => Network> = new Map();\n\n\n/**\n *  A **Network** provides access to a chain's properties and allows\n *  for plug-ins to extend functionality.\n */\nexport class Network {\n    #name: string;\n    #chainId: bigint;\n\n    #plugins: Map<string, NetworkPlugin>;\n\n    /**\n     *  Creates a new **Network** for %%name%% and %%chainId%%.\n     */\n    constructor(name: string, chainId: BigNumberish) {\n        this.#name = name;\n        this.#chainId = getBigInt(chainId);\n        this.#plugins = new Map();\n    }\n\n    /**\n     *  Returns a JSON-compatible representation of a Network.\n     */\n    toJSON(): any {\n        return { name: this.name, chainId: String(this.chainId) };\n    }\n\n    /**\n     *  The network common name.\n     *\n     *  This is the canonical name, as networks migh have multiple\n     *  names.\n     */\n    get name(): string { return this.#name; }\n    set name(value: string) { this.#name =  value; }\n\n    /**\n     *  The network chain ID.\n     */\n    get chainId(): bigint { return this.#chainId; }\n    set chainId(value: BigNumberish) { this.#chainId = getBigInt(value, \"chainId\"); }\n\n    /**\n     *  Returns true if %%other%% matches this network. Any chain ID\n     *  must match, and if no chain ID is present, the name must match.\n     *\n     *  This method does not currently check for additional properties,\n     *  such as ENS address or plug-in compatibility.\n     */\n    matches(other: Networkish): boolean {\n        if (other == null) { return false; }\n\n        if (typeof(other) === \"string\") {\n            try {\n                return (this.chainId === getBigInt(other));\n            } catch (error) { }\n            return (this.name === other);\n        }\n\n        if (typeof(other) === \"number\" || typeof(other) === \"bigint\") {\n            try {\n                return (this.chainId === getBigInt(other));\n            } catch (error) { }\n            return false;\n        }\n\n        if (typeof(other) === \"object\") {\n            if (other.chainId != null) {\n                try {\n                    return (this.chainId === getBigInt(other.chainId));\n                } catch (error) { }\n                return false;\n            }\n            if (other.name != null) {\n                return (this.name === other.name);\n            }\n            return false;\n        }\n\n        return false;\n    }\n\n    /**\n     *  Returns the list of plugins currently attached to this Network.\n     */\n    get plugins(): Array<NetworkPlugin> {\n        return Array.from(this.#plugins.values());\n    }\n\n    /**\n     *  Attach a new %%plugin%% to this Network. The network name\n     *  must be unique, excluding any fragment.\n     */\n    attachPlugin(plugin: NetworkPlugin): this {\n        if (this.#plugins.get(plugin.name)) {\n            throw new Error(`cannot replace existing plugin: ${ plugin.name } `);\n        }\n        this.#plugins.set(plugin.name, plugin.clone());\n        return this;\n    }\n\n    /**\n     *  Return the plugin, if any, matching %%name%% exactly. Plugins\n     *  with fragments will not be returned unless %%name%% includes\n     *  a fragment.\n     */\n    getPlugin<T extends NetworkPlugin = NetworkPlugin>(name: string): null | T {\n        return <T>(this.#plugins.get(name)) || null;\n    }\n\n    /**\n     *  Gets a list of all plugins that match %%name%%, with otr without\n     *  a fragment.\n     */\n    getPlugins<T extends NetworkPlugin = NetworkPlugin>(basename: string): Array<T> {\n        return <Array<T>>(this.plugins.filter((p) => (p.name.split(\"#\")[0] === basename)));\n    }\n\n    /**\n     *  Create a copy of this Network.\n     */\n    clone(): Network {\n        const clone = new Network(this.name, this.chainId);\n        this.plugins.forEach((plugin) => {\n            clone.attachPlugin(plugin.clone());\n        });\n        return clone;\n    }\n\n    /**\n     *  Compute the intrinsic gas required for a transaction.\n     *\n     *  A GasCostPlugin can be attached to override the default\n     *  values.\n     */\n    computeIntrinsicGas(tx: TransactionLike): number {\n        const costs = this.getPlugin<GasCostPlugin>(\"org.ethers.plugins.network.GasCost\") || (new GasCostPlugin());\n\n        let gas = costs.txBase;\n        if (tx.to == null) { gas += costs.txCreate; }\n        if (tx.data) {\n            for (let i = 2; i < tx.data.length; i += 2) {\n                if (tx.data.substring(i, i + 2) === \"00\") {\n                    gas += costs.txDataZero;\n                } else {\n                    gas += costs.txDataNonzero;\n                }\n            }\n        }\n\n        if (tx.accessList) {\n            const accessList = accessListify(tx.accessList);\n            for (const addr in accessList) {\n                gas += costs.txAccessListAddress + costs.txAccessListStorageKey * accessList[addr].storageKeys.length;\n            }\n        }\n\n        return gas;\n    }\n\n    /**\n     *  Returns a new Network for the %%network%% name or chainId.\n     */\n    static from(network?: Networkish): Network {\n        injectCommonNetworks();\n\n        // Default network\n        if (network == null) { return Network.from(\"mainnet\"); }\n\n        // Canonical name or chain ID\n        if (typeof(network) === \"number\") { network = BigInt(network); }\n        if (typeof(network) === \"string\" || typeof(network) === \"bigint\") {\n            const networkFunc = Networks.get(network);\n            if (networkFunc) { return networkFunc(); }\n            if (typeof(network) === \"bigint\") {\n                return new Network(\"unknown\", network);\n            }\n\n            assertArgument(false, \"unknown network\", \"network\", network);\n        }\n\n        // Clonable with network-like abilities\n        if (typeof((<Network>network).clone) === \"function\") {\n            const clone = (<Network>network).clone();\n            //if (typeof(network.name) !== \"string\" || typeof(network.chainId) !== \"number\") {\n            //}\n            return clone;\n        }\n\n        // Networkish\n        if (typeof(network) === \"object\") {\n            assertArgument(typeof(network.name) === \"string\" && typeof(network.chainId) === \"number\",\n                \"invalid network object name or chainId\", \"network\", network);\n\n            const custom = new Network(<string>(network.name), <number>(network.chainId));\n\n            if ((<any>network).ensAddress || (<any>network).ensNetwork != null) {\n                custom.attachPlugin(new EnsPlugin((<any>network).ensAddress, (<any>network).ensNetwork));\n            }\n\n            //if ((<any>network).layerOneConnection) {\n            //    custom.attachPlugin(new LayerOneConnectionPlugin((<any>network).layerOneConnection));\n            //}\n\n            return custom;\n        }\n\n        assertArgument(false, \"invalid network\", \"network\", network);\n    }\n\n    /**\n     *  Register %%nameOrChainId%% with a function which returns\n     *  an instance of a Network representing that chain.\n     */\n    static register(nameOrChainId: string | number | bigint, networkFunc: () => Network): void {\n        if (typeof(nameOrChainId) === \"number\") { nameOrChainId = BigInt(nameOrChainId); }\n        const existing = Networks.get(nameOrChainId);\n        if (existing) {\n            assertArgument(false, `conflicting network for ${ JSON.stringify(existing.name) }`, \"nameOrChainId\", nameOrChainId);\n        }\n        Networks.set(nameOrChainId, networkFunc);\n    }\n}\n\n\ntype Options = {\n    ensNetwork?: number;\n    altNames?: Array<string>;\n    plugins?: Array<NetworkPlugin>;\n};\n\n// We don't want to bring in formatUnits because it is backed by\n// FixedNumber and we want to keep Networks tiny. The values\n// included by the Gas Stations are also IEEE 754 with lots of\n// rounding issues and exceed the strict checks formatUnits has.\nfunction parseUnits(_value: number | string, decimals: number): bigint {\n    const value = String(_value);\n    if (!value.match(/^[0-9.]+$/)) {\n        throw new Error(`invalid gwei value: ${ _value }`);\n    }\n\n    // Break into [ whole, fraction ]\n    const comps = value.split(\".\");\n    if (comps.length === 1) { comps.push(\"\"); }\n\n    // More than 1 decimal point or too many fractional positions\n    if (comps.length !== 2) {\n        throw new Error(`invalid gwei value: ${ _value }`);\n    }\n\n    // Pad the fraction to 9 decimalplaces\n    while (comps[1].length < decimals) { comps[1] += \"0\"; }\n\n    // Too many decimals and some non-zero ending, take the ceiling\n    if (comps[1].length > 9) {\n        let frac = BigInt(comps[1].substring(0, 9));\n        if (!comps[1].substring(9).match(/^0+$/)) { frac++; }\n        comps[1] = frac.toString();\n    }\n\n    return BigInt(comps[0] + comps[1]);\n}\n\n// Used by Polygon to use a gas station for fee data\nfunction getGasStationPlugin(url: string) {\n    return new FetchUrlFeeDataNetworkPlugin(url, async (fetchFeeData, provider, request) => {\n\n        // Prevent Cloudflare from blocking our request in node.js\n        request.setHeader(\"User-Agent\", \"ethers\");\n\n        let response;\n        try {\n            const [ _response, _feeData ] = await Promise.all([\n                request.send(), fetchFeeData()\n            ]);\n            response = _response;\n            const payload = response.bodyJson.standard;\n            const feeData = {\n                gasPrice: _feeData.gasPrice,\n                maxFeePerGas: parseUnits(payload.maxFee, 9),\n                maxPriorityFeePerGas: parseUnits(payload.maxPriorityFee, 9),\n            };\n            return feeData;\n        } catch (error: any) {\n            assert(false, `error encountered with polygon gas station (${ JSON.stringify(request.url) })`, \"SERVER_ERROR\", { request, response, error });\n        }\n    });\n}\n\n// See: https://chainlist.org\nlet injected = false;\nfunction injectCommonNetworks(): void {\n    if (injected) { return; }\n    injected = true;\n\n    /// Register popular Ethereum networks\n    function registerEth(name: string, chainId: number, options: Options): void {\n        const func = function() {\n            const network = new Network(name, chainId);\n\n            // We use 0 to disable ENS\n            if (options.ensNetwork != null) {\n                network.attachPlugin(new EnsPlugin(null, options.ensNetwork));\n            }\n\n            network.attachPlugin(new GasCostPlugin());\n\n            (options.plugins || []).forEach((plugin) => {\n                network.attachPlugin(plugin);\n            });\n\n            return network;\n        };\n\n        // Register the network by name and chain ID\n        Network.register(name, func);\n        Network.register(chainId, func);\n\n        if (options.altNames) {\n            options.altNames.forEach((name) => {\n                Network.register(name, func);\n            });\n        }\n    }\n\n    registerEth(\"mainnet\", 1, { ensNetwork: 1, altNames: [ \"homestead\" ] });\n    registerEth(\"ropsten\", 3, { ensNetwork: 3 });\n    registerEth(\"rinkeby\", 4, { ensNetwork: 4 });\n    registerEth(\"goerli\", 5, { ensNetwork: 5 });\n    registerEth(\"kovan\", 42, { ensNetwork: 42 });\n    registerEth(\"sepolia\", 11155111, { ensNetwork: 11155111 });\n    registerEth(\"holesky\", 17000, { ensNetwork: 17000 });\n\n    registerEth(\"classic\", 61, { });\n    registerEth(\"classicKotti\", 6, { });\n\n    registerEth(\"arbitrum\", 42161, {\n        ensNetwork: 1,\n    });\n    registerEth(\"arbitrum-goerli\", 421613, { });\n    registerEth(\"arbitrum-sepolia\", 421614, { });\n\n    registerEth(\"base\", 8453, { ensNetwork: 1 });\n    registerEth(\"base-goerli\", 84531, { });\n    registerEth(\"base-sepolia\", 84532, { });\n\n    registerEth(\"bnb\", 56, { ensNetwork: 1 });\n    registerEth(\"bnbt\", 97, { });\n\n    registerEth(\"linea\", 59144, { ensNetwork: 1 });\n    registerEth(\"linea-goerli\", 59140, { });\n    registerEth(\"linea-sepolia\", 59141, { });\n\n    registerEth(\"matic\", 137, {\n        ensNetwork: 1,\n        plugins: [\n            getGasStationPlugin(\"https:/\\/gasstation.polygon.technology/v2\")\n        ]\n    });\n    registerEth(\"matic-amoy\", 80002, { });\n    registerEth(\"matic-mumbai\", 80001, {\n        altNames: [ \"maticMumbai\", \"maticmum\" ],  // @TODO: Future remove these alts\n        plugins: [\n            getGasStationPlugin(\"https:/\\/gasstation-testnet.polygon.technology/v2\")\n        ]\n    });\n\n    registerEth(\"optimism\", 10, {\n        ensNetwork: 1,\n        plugins: [ ]\n    });\n    registerEth(\"optimism-goerli\", 420, { });\n    registerEth(\"optimism-sepolia\", 11155420, { });\n\n    registerEth(\"xdai\", 100, { ensNetwork: 1 });\n}\n"], "mappings": "AAAA;;;;;;AAOA,SAASA,aAAa,QAAQ,yBAAyB;AACvD,SAASC,SAAS,EAAEC,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AAErE,SACIC,SAAS,EAAEC,4BAA4B,EAAEC,aAAa,QACnD,sBAAsB;AA0B7B;;;;;;;;;;;;;;;;;AAmBA,MAAMC,QAAQ,GAAwC,IAAIC,GAAG,EAAE;AAG/D;;;;AAIA,OAAM,MAAOC,OAAO;EAChB,CAAAC,IAAK;EACL,CAAAC,OAAQ;EAER,CAAAC,OAAQ;EAER;;;EAGAC,YAAYH,IAAY,EAAEC,OAAqB;IAC3C,IAAI,CAAC,CAAAD,IAAK,GAAGA,IAAI;IACjB,IAAI,CAAC,CAAAC,OAAQ,GAAGV,SAAS,CAACU,OAAO,CAAC;IAClC,IAAI,CAAC,CAAAC,OAAQ,GAAG,IAAIJ,GAAG,EAAE;EAC7B;EAEA;;;EAGAM,MAAMA,CAAA;IACF,OAAO;MAAEJ,IAAI,EAAE,IAAI,CAACA,IAAI;MAAEC,OAAO,EAAEI,MAAM,CAAC,IAAI,CAACJ,OAAO;IAAC,CAAE;EAC7D;EAEA;;;;;;EAMA,IAAID,IAAIA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,IAAK;EAAE;EACxC,IAAIA,IAAIA,CAACM,KAAa;IAAI,IAAI,CAAC,CAAAN,IAAK,GAAIM,KAAK;EAAE;EAE/C;;;EAGA,IAAIL,OAAOA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,OAAQ;EAAE;EAC9C,IAAIA,OAAOA,CAACK,KAAmB;IAAI,IAAI,CAAC,CAAAL,OAAQ,GAAGV,SAAS,CAACe,KAAK,EAAE,SAAS,CAAC;EAAE;EAEhF;;;;;;;EAOAC,OAAOA,CAACC,KAAiB;IACrB,IAAIA,KAAK,IAAI,IAAI,EAAE;MAAE,OAAO,KAAK;;IAEjC,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;MAC5B,IAAI;QACA,OAAQ,IAAI,CAACP,OAAO,KAAKV,SAAS,CAACiB,KAAK,CAAC;OAC5C,CAAC,OAAOC,KAAK,EAAE;MAChB,OAAQ,IAAI,CAACT,IAAI,KAAKQ,KAAK;;IAG/B,IAAI,OAAOA,KAAM,KAAK,QAAQ,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;MAC1D,IAAI;QACA,OAAQ,IAAI,CAACP,OAAO,KAAKV,SAAS,CAACiB,KAAK,CAAC;OAC5C,CAAC,OAAOC,KAAK,EAAE;MAChB,OAAO,KAAK;;IAGhB,IAAI,OAAOD,KAAM,KAAK,QAAQ,EAAE;MAC5B,IAAIA,KAAK,CAACP,OAAO,IAAI,IAAI,EAAE;QACvB,IAAI;UACA,OAAQ,IAAI,CAACA,OAAO,KAAKV,SAAS,CAACiB,KAAK,CAACP,OAAO,CAAC;SACpD,CAAC,OAAOQ,KAAK,EAAE;QAChB,OAAO,KAAK;;MAEhB,IAAID,KAAK,CAACR,IAAI,IAAI,IAAI,EAAE;QACpB,OAAQ,IAAI,CAACA,IAAI,KAAKQ,KAAK,CAACR,IAAI;;MAEpC,OAAO,KAAK;;IAGhB,OAAO,KAAK;EAChB;EAEA;;;EAGA,IAAIE,OAAOA,CAAA;IACP,OAAOQ,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,CAAAT,OAAQ,CAACU,MAAM,EAAE,CAAC;EAC7C;EAEA;;;;EAIAC,YAAYA,CAACC,MAAqB;IAC9B,IAAI,IAAI,CAAC,CAAAZ,OAAQ,CAACa,GAAG,CAACD,MAAM,CAACd,IAAI,CAAC,EAAE;MAChC,MAAM,IAAIgB,KAAK,CAAC,mCAAoCF,MAAM,CAACd,IAAK,GAAG,CAAC;;IAExE,IAAI,CAAC,CAAAE,OAAQ,CAACe,GAAG,CAACH,MAAM,CAACd,IAAI,EAAEc,MAAM,CAACI,KAAK,EAAE,CAAC;IAC9C,OAAO,IAAI;EACf;EAEA;;;;;EAKAC,SAASA,CAA0CnB,IAAY;IAC3D,OAAW,IAAI,CAAC,CAAAE,OAAQ,CAACa,GAAG,CAACf,IAAI,CAAC,IAAK,IAAI;EAC/C;EAEA;;;;EAIAoB,UAAUA,CAA0CC,QAAgB;IAChE,OAAkB,IAAI,CAACnB,OAAO,CAACoB,MAAM,CAAEC,CAAC,IAAMA,CAAC,CAACvB,IAAI,CAACwB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKH,QAAS,CAAC;EACrF;EAEA;;;EAGAH,KAAKA,CAAA;IACD,MAAMA,KAAK,GAAG,IAAInB,OAAO,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC;IAClD,IAAI,CAACC,OAAO,CAACuB,OAAO,CAAEX,MAAM,IAAI;MAC5BI,KAAK,CAACL,YAAY,CAACC,MAAM,CAACI,KAAK,EAAE,CAAC;IACtC,CAAC,CAAC;IACF,OAAOA,KAAK;EAChB;EAEA;;;;;;EAMAQ,mBAAmBA,CAACC,EAAmB;IACnC,MAAMC,KAAK,GAAG,IAAI,CAACT,SAAS,CAAgB,oCAAoC,CAAC,IAAK,IAAIvB,aAAa,EAAG;IAE1G,IAAIiC,GAAG,GAAGD,KAAK,CAACE,MAAM;IACtB,IAAIH,EAAE,CAACI,EAAE,IAAI,IAAI,EAAE;MAAEF,GAAG,IAAID,KAAK,CAACI,QAAQ;;IAC1C,IAAIL,EAAE,CAACM,IAAI,EAAE;MACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,EAAE,CAACM,IAAI,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;QACxC,IAAIP,EAAE,CAACM,IAAI,CAACG,SAAS,CAACF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;UACtCL,GAAG,IAAID,KAAK,CAACS,UAAU;SAC1B,MAAM;UACHR,GAAG,IAAID,KAAK,CAACU,aAAa;;;;IAKtC,IAAIX,EAAE,CAACY,UAAU,EAAE;MACf,MAAMA,UAAU,GAAGjD,aAAa,CAACqC,EAAE,CAACY,UAAU,CAAC;MAC/C,KAAK,MAAMC,IAAI,IAAID,UAAU,EAAE;QAC3BV,GAAG,IAAID,KAAK,CAACa,mBAAmB,GAAGb,KAAK,CAACc,sBAAsB,GAAGH,UAAU,CAACC,IAAI,CAAC,CAACG,WAAW,CAACR,MAAM;;;IAI7G,OAAON,GAAG;EACd;EAEA;;;EAGA,OAAOlB,IAAIA,CAACiC,OAAoB;IAC5BC,oBAAoB,EAAE;IAEtB;IACA,IAAID,OAAO,IAAI,IAAI,EAAE;MAAE,OAAO7C,OAAO,CAACY,IAAI,CAAC,SAAS,CAAC;;IAErD;IACA,IAAI,OAAOiC,OAAQ,KAAK,QAAQ,EAAE;MAAEA,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC;;IAC7D,IAAI,OAAOA,OAAQ,KAAK,QAAQ,IAAI,OAAOA,OAAQ,KAAK,QAAQ,EAAE;MAC9D,MAAMG,WAAW,GAAGlD,QAAQ,CAACkB,GAAG,CAAC6B,OAAO,CAAC;MACzC,IAAIG,WAAW,EAAE;QAAE,OAAOA,WAAW,EAAE;;MACvC,IAAI,OAAOH,OAAQ,KAAK,QAAQ,EAAE;QAC9B,OAAO,IAAI7C,OAAO,CAAC,SAAS,EAAE6C,OAAO,CAAC;;MAG1CnD,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAEmD,OAAO,CAAC;;IAGhE;IACA,IAAI,OAAiBA,OAAQ,CAAC1B,KAAM,KAAK,UAAU,EAAE;MACjD,MAAMA,KAAK,GAAa0B,OAAQ,CAAC1B,KAAK,EAAE;MACxC;MACA;MACA,OAAOA,KAAK;;IAGhB;IACA,IAAI,OAAO0B,OAAQ,KAAK,QAAQ,EAAE;MAC9BnD,cAAc,CAAC,OAAOmD,OAAO,CAAC5C,IAAK,KAAK,QAAQ,IAAI,OAAO4C,OAAO,CAAC3C,OAAQ,KAAK,QAAQ,EACpF,wCAAwC,EAAE,SAAS,EAAE2C,OAAO,CAAC;MAEjE,MAAMI,MAAM,GAAG,IAAIjD,OAAO,CAAU6C,OAAO,CAAC5C,IAAI,EAAY4C,OAAO,CAAC3C,OAAQ,CAAC;MAE7E,IAAU2C,OAAQ,CAACK,UAAU,IAAUL,OAAQ,CAACM,UAAU,IAAI,IAAI,EAAE;QAChEF,MAAM,CAACnC,YAAY,CAAC,IAAInB,SAAS,CAAOkD,OAAQ,CAACK,UAAU,EAAQL,OAAQ,CAACM,UAAU,CAAC,CAAC;;MAG5F;MACA;MACA;MAEA,OAAOF,MAAM;;IAGjBvD,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAEmD,OAAO,CAAC;EAChE;EAEA;;;;EAIA,OAAOO,QAAQA,CAACC,aAAuC,EAAEL,WAA0B;IAC/E,IAAI,OAAOK,aAAc,KAAK,QAAQ,EAAE;MAAEA,aAAa,GAAGN,MAAM,CAACM,aAAa,CAAC;;IAC/E,MAAMC,QAAQ,GAAGxD,QAAQ,CAACkB,GAAG,CAACqC,aAAa,CAAC;IAC5C,IAAIC,QAAQ,EAAE;MACV5D,cAAc,CAAC,KAAK,EAAE,2BAA4B6D,IAAI,CAACC,SAAS,CAACF,QAAQ,CAACrD,IAAI,CAAE,EAAE,EAAE,eAAe,EAAEoD,aAAa,CAAC;;IAEvHvD,QAAQ,CAACoB,GAAG,CAACmC,aAAa,EAAEL,WAAW,CAAC;EAC5C;;AAUJ;AACA;AACA;AACA;AACA,SAASS,UAAUA,CAACC,MAAuB,EAAEC,QAAgB;EACzD,MAAMpD,KAAK,GAAGD,MAAM,CAACoD,MAAM,CAAC;EAC5B,IAAI,CAACnD,KAAK,CAACqD,KAAK,CAAC,WAAW,CAAC,EAAE;IAC3B,MAAM,IAAI3C,KAAK,CAAC,uBAAwByC,MAAO,EAAE,CAAC;;EAGtD;EACA,MAAMG,KAAK,GAAGtD,KAAK,CAACkB,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAIoC,KAAK,CAACzB,MAAM,KAAK,CAAC,EAAE;IAAEyB,KAAK,CAACC,IAAI,CAAC,EAAE,CAAC;;EAExC;EACA,IAAID,KAAK,CAACzB,MAAM,KAAK,CAAC,EAAE;IACpB,MAAM,IAAInB,KAAK,CAAC,uBAAwByC,MAAO,EAAE,CAAC;;EAGtD;EACA,OAAOG,KAAK,CAAC,CAAC,CAAC,CAACzB,MAAM,GAAGuB,QAAQ,EAAE;IAAEE,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;;EAEpD;EACA,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACzB,MAAM,GAAG,CAAC,EAAE;IACrB,IAAI2B,IAAI,GAAGhB,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,CAACxB,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACwB,KAAK,CAAC,CAAC,CAAC,CAACxB,SAAS,CAAC,CAAC,CAAC,CAACuB,KAAK,CAAC,MAAM,CAAC,EAAE;MAAEG,IAAI,EAAE;;IAClDF,KAAK,CAAC,CAAC,CAAC,GAAGE,IAAI,CAACC,QAAQ,EAAE;;EAG9B,OAAOjB,MAAM,CAACc,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC;AACtC;AAEA;AACA,SAASI,mBAAmBA,CAACC,GAAW;EACpC,OAAO,IAAItE,4BAA4B,CAACsE,GAAG,EAAE,OAAOC,YAAY,EAAEC,QAAQ,EAAEC,OAAO,KAAI;IAEnF;IACAA,OAAO,CAACC,SAAS,CAAC,YAAY,EAAE,QAAQ,CAAC;IAEzC,IAAIC,QAAQ;IACZ,IAAI;MACA,MAAM,CAAEC,SAAS,EAAEC,QAAQ,CAAE,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC9CN,OAAO,CAACO,IAAI,EAAE,EAAET,YAAY,EAAE,CACjC,CAAC;MACFI,QAAQ,GAAGC,SAAS;MACpB,MAAMK,OAAO,GAAGN,QAAQ,CAACO,QAAQ,CAACC,QAAQ;MAC1C,MAAMC,OAAO,GAAG;QACZC,QAAQ,EAAER,QAAQ,CAACQ,QAAQ;QAC3BC,YAAY,EAAEzB,UAAU,CAACoB,OAAO,CAACM,MAAM,EAAE,CAAC,CAAC;QAC3CC,oBAAoB,EAAE3B,UAAU,CAACoB,OAAO,CAACQ,cAAc,EAAE,CAAC;OAC7D;MACD,OAAOL,OAAO;KACjB,CAAC,OAAOtE,KAAU,EAAE;MACjBjB,MAAM,CAAC,KAAK,EAAE,+CAAgD8D,IAAI,CAACC,SAAS,CAACa,OAAO,CAACH,GAAG,CAAE,GAAG,EAAE,cAAc,EAAE;QAAEG,OAAO;QAAEE,QAAQ;QAAE7D;MAAK,CAAE,CAAC;;EAEpJ,CAAC,CAAC;AACN;AAEA;AACA,IAAI4E,QAAQ,GAAG,KAAK;AACpB,SAASxC,oBAAoBA,CAAA;EACzB,IAAIwC,QAAQ,EAAE;IAAE;;EAChBA,QAAQ,GAAG,IAAI;EAEf;EACA,SAASC,WAAWA,CAACtF,IAAY,EAAEC,OAAe,EAAEsF,OAAgB;IAChE,MAAMC,IAAI,GAAG,SAAAA,CAAA;MACT,MAAM5C,OAAO,GAAG,IAAI7C,OAAO,CAACC,IAAI,EAAEC,OAAO,CAAC;MAE1C;MACA,IAAIsF,OAAO,CAACrC,UAAU,IAAI,IAAI,EAAE;QAC5BN,OAAO,CAAC/B,YAAY,CAAC,IAAInB,SAAS,CAAC,IAAI,EAAE6F,OAAO,CAACrC,UAAU,CAAC,CAAC;;MAGjEN,OAAO,CAAC/B,YAAY,CAAC,IAAIjB,aAAa,EAAE,CAAC;MAEzC,CAAC2F,OAAO,CAACrF,OAAO,IAAI,EAAE,EAAEuB,OAAO,CAAEX,MAAM,IAAI;QACvC8B,OAAO,CAAC/B,YAAY,CAACC,MAAM,CAAC;MAChC,CAAC,CAAC;MAEF,OAAO8B,OAAO;IAClB,CAAC;IAED;IACA7C,OAAO,CAACoD,QAAQ,CAACnD,IAAI,EAAEwF,IAAI,CAAC;IAC5BzF,OAAO,CAACoD,QAAQ,CAAClD,OAAO,EAAEuF,IAAI,CAAC;IAE/B,IAAID,OAAO,CAACE,QAAQ,EAAE;MAClBF,OAAO,CAACE,QAAQ,CAAChE,OAAO,CAAEzB,IAAI,IAAI;QAC9BD,OAAO,CAACoD,QAAQ,CAACnD,IAAI,EAAEwF,IAAI,CAAC;MAChC,CAAC,CAAC;;EAEV;EAEAF,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE;IAAEpC,UAAU,EAAE,CAAC;IAAEuC,QAAQ,EAAE,CAAE,WAAW;EAAE,CAAE,CAAC;EACvEH,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EAC5CoC,WAAW,CAAC,SAAS,EAAE,CAAC,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EAC5CoC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EAC3CoC,WAAW,CAAC,OAAO,EAAE,EAAE,EAAE;IAAEpC,UAAU,EAAE;EAAE,CAAE,CAAC;EAC5CoC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE;IAAEpC,UAAU,EAAE;EAAQ,CAAE,CAAC;EAC1DoC,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE;IAAEpC,UAAU,EAAE;EAAK,CAAE,CAAC;EAEpDoC,WAAW,CAAC,SAAS,EAAE,EAAE,EAAE,EAAG,CAAC;EAC/BA,WAAW,CAAC,cAAc,EAAE,CAAC,EAAE,EAAG,CAAC;EAEnCA,WAAW,CAAC,UAAU,EAAE,KAAK,EAAE;IAC3BpC,UAAU,EAAE;GACf,CAAC;EACFoC,WAAW,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAG,CAAC;EAC3CA,WAAW,CAAC,kBAAkB,EAAE,MAAM,EAAE,EAAG,CAAC;EAE5CA,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EAC5CoC,WAAW,CAAC,aAAa,EAAE,KAAK,EAAE,EAAG,CAAC;EACtCA,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,EAAG,CAAC;EAEvCA,WAAW,CAAC,KAAK,EAAE,EAAE,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EACzCoC,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,EAAG,CAAC;EAE5BA,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;EAC9CoC,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE,EAAG,CAAC;EACvCA,WAAW,CAAC,eAAe,EAAE,KAAK,EAAE,EAAG,CAAC;EAExCA,WAAW,CAAC,OAAO,EAAE,GAAG,EAAE;IACtBpC,UAAU,EAAE,CAAC;IACbhD,OAAO,EAAE,CACL8D,mBAAmB,CAAC,2CAA2C,CAAC;GAEvE,CAAC;EACFsB,WAAW,CAAC,YAAY,EAAE,KAAK,EAAE,EAAG,CAAC;EACrCA,WAAW,CAAC,cAAc,EAAE,KAAK,EAAE;IAC/BG,QAAQ,EAAE,CAAE,aAAa,EAAE,UAAU,CAAE;IACvCvF,OAAO,EAAE,CACL8D,mBAAmB,CAAC,mDAAmD,CAAC;GAE/E,CAAC;EAEFsB,WAAW,CAAC,UAAU,EAAE,EAAE,EAAE;IACxBpC,UAAU,EAAE,CAAC;IACbhD,OAAO,EAAE;GACZ,CAAC;EACFoF,WAAW,CAAC,iBAAiB,EAAE,GAAG,EAAE,EAAG,CAAC;EACxCA,WAAW,CAAC,kBAAkB,EAAE,QAAQ,EAAE,EAAG,CAAC;EAE9CA,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE;IAAEpC,UAAU,EAAE;EAAC,CAAE,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}