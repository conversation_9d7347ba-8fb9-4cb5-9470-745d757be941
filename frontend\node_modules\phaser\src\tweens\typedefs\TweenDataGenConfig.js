/**
 * @typedef {object} Phaser.Types.Tweens.TweenDataGenConfig
 * @since 3.0.0
 *
 * @property {function} delay - Time in milliseconds before tween will start.
 * @property {function} duration - Duration of the tween in milliseconds, excludes time for yoyo or repeats.
 * @property {function} hold - Time in milliseconds the tween will pause before running the yoyo or starting a repeat.
 * @property {function} repeat - Number of times to repeat the tween. The tween will always run once regardless, so a repeat value of '1' will play the tween twice.
 * @property {function} repeatDelay - Time in milliseconds before the repeat will start.
 */
