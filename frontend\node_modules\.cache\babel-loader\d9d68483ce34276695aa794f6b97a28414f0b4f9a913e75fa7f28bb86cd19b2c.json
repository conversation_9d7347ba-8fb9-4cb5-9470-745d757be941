{"ast": null, "code": "/**\n *  A **FallbackProvider** provides resilience, security and performance\n *  in a way that is customizable and configurable.\n *\n *  @_section: api/providers/fallback-provider:Fallback Provider [about-fallback-provider]\n */\nimport { assert, assertArgument, getBigInt, getNumber, isError } from \"../utils/index.js\";\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { Network } from \"./network.js\";\nconst BN_1 = BigInt(\"1\");\nconst BN_2 = BigInt(\"2\");\nfunction shuffle(array) {\n  for (let i = array.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    const tmp = array[i];\n    array[i] = array[j];\n    array[j] = tmp;\n  }\n}\nfunction stall(duration) {\n  return new Promise(resolve => {\n    setTimeout(resolve, duration);\n  });\n}\nfunction getTime() {\n  return new Date().getTime();\n}\nfunction stringify(value) {\n  return JSON.stringify(value, (key, value) => {\n    if (typeof value === \"bigint\") {\n      return {\n        type: \"bigint\",\n        value: value.toString()\n      };\n    }\n    return value;\n  });\n}\n;\nconst defaultConfig = {\n  stallTimeout: 400,\n  priority: 1,\n  weight: 1\n};\nconst defaultState = {\n  blockNumber: -2,\n  requests: 0,\n  lateResponses: 0,\n  errorResponses: 0,\n  outOfSync: -1,\n  unsupportedEvents: 0,\n  rollingDuration: 0,\n  score: 0,\n  _network: null,\n  _updateNumber: null,\n  _totalTime: 0,\n  _lastFatalError: null,\n  _lastFatalErrorTimestamp: 0\n};\nasync function waitForSync(config, blockNumber) {\n  while (config.blockNumber < 0 || config.blockNumber < blockNumber) {\n    if (!config._updateNumber) {\n      config._updateNumber = (async () => {\n        try {\n          const blockNumber = await config.provider.getBlockNumber();\n          if (blockNumber > config.blockNumber) {\n            config.blockNumber = blockNumber;\n          }\n        } catch (error) {\n          config.blockNumber = -2;\n          config._lastFatalError = error;\n          config._lastFatalErrorTimestamp = getTime();\n        }\n        config._updateNumber = null;\n      })();\n    }\n    await config._updateNumber;\n    config.outOfSync++;\n    if (config._lastFatalError) {\n      break;\n    }\n  }\n}\nfunction _normalize(value) {\n  if (value == null) {\n    return \"null\";\n  }\n  if (Array.isArray(value)) {\n    return \"[\" + value.map(_normalize).join(\",\") + \"]\";\n  }\n  if (typeof value === \"object\" && typeof value.toJSON === \"function\") {\n    return _normalize(value.toJSON());\n  }\n  switch (typeof value) {\n    case \"boolean\":\n    case \"symbol\":\n      return value.toString();\n    case \"bigint\":\n    case \"number\":\n      return BigInt(value).toString();\n    case \"string\":\n      return JSON.stringify(value);\n    case \"object\":\n      {\n        const keys = Object.keys(value);\n        keys.sort();\n        return \"{\" + keys.map(k => `${JSON.stringify(k)}:${_normalize(value[k])}`).join(\",\") + \"}\";\n      }\n  }\n  console.log(\"Could not serialize\", value);\n  throw new Error(\"Hmm...\");\n}\nfunction normalizeResult(method, value) {\n  if (\"error\" in value) {\n    const error = value.error;\n    let tag;\n    if (isError(error, \"CALL_EXCEPTION\")) {\n      tag = _normalize(Object.assign({}, error, {\n        shortMessage: undefined,\n        reason: undefined,\n        info: undefined\n      }));\n    } else {\n      tag = _normalize(error);\n    }\n    return {\n      tag,\n      value: error\n    };\n  }\n  const result = value.result;\n  return {\n    tag: _normalize(result),\n    value: result\n  };\n}\n// This strategy picks the highest weight result, as long as the weight is\n// equal to or greater than quorum\nfunction checkQuorum(quorum, results) {\n  const tally = new Map();\n  for (const {\n    value,\n    tag,\n    weight\n  } of results) {\n    const t = tally.get(tag) || {\n      value,\n      weight: 0\n    };\n    t.weight += weight;\n    tally.set(tag, t);\n  }\n  let best = null;\n  for (const r of tally.values()) {\n    if (r.weight >= quorum && (!best || r.weight > best.weight)) {\n      best = r;\n    }\n  }\n  if (best) {\n    return best.value;\n  }\n  return undefined;\n}\nfunction getMedian(quorum, results) {\n  let resultWeight = 0;\n  const errorMap = new Map();\n  let bestError = null;\n  const values = [];\n  for (const {\n    value,\n    tag,\n    weight\n  } of results) {\n    if (value instanceof Error) {\n      const e = errorMap.get(tag) || {\n        value,\n        weight: 0\n      };\n      e.weight += weight;\n      errorMap.set(tag, e);\n      if (bestError == null || e.weight > bestError.weight) {\n        bestError = e;\n      }\n    } else {\n      values.push(BigInt(value));\n      resultWeight += weight;\n    }\n  }\n  if (resultWeight < quorum) {\n    // We have quorum for an error\n    if (bestError && bestError.weight >= quorum) {\n      return bestError.value;\n    }\n    // We do not have quorum for a result\n    return undefined;\n  }\n  // Get the sorted values\n  values.sort((a, b) => a < b ? -1 : b > a ? 1 : 0);\n  const mid = Math.floor(values.length / 2);\n  // Odd-length; take the middle value\n  if (values.length % 2) {\n    return values[mid];\n  }\n  // Even length; take the ceiling of the mean of the center two values\n  return (values[mid - 1] + values[mid] + BN_1) / BN_2;\n}\nfunction getAnyResult(quorum, results) {\n  // If any value or error meets quorum, that is our preferred result\n  const result = checkQuorum(quorum, results);\n  if (result !== undefined) {\n    return result;\n  }\n  // Otherwise, do we have any result?\n  for (const r of results) {\n    if (r.value) {\n      return r.value;\n    }\n  }\n  // Nope!\n  return undefined;\n}\nfunction getFuzzyMode(quorum, results) {\n  if (quorum === 1) {\n    return getNumber(getMedian(quorum, results), \"%internal\");\n  }\n  const tally = new Map();\n  const add = (result, weight) => {\n    const t = tally.get(result) || {\n      result,\n      weight: 0\n    };\n    t.weight += weight;\n    tally.set(result, t);\n  };\n  for (const {\n    weight,\n    value\n  } of results) {\n    const r = getNumber(value);\n    add(r - 1, weight);\n    add(r, weight);\n    add(r + 1, weight);\n  }\n  let bestWeight = 0;\n  let bestResult = undefined;\n  for (const {\n    weight,\n    result\n  } of tally.values()) {\n    // Use this result, if this result meets quorum and has either:\n    // - a better weight\n    // - or equal weight, but the result is larger\n    if (weight >= quorum && (weight > bestWeight || bestResult != null && weight === bestWeight && result > bestResult)) {\n      bestWeight = weight;\n      bestResult = result;\n    }\n  }\n  return bestResult;\n}\n/**\n *  A **FallbackProvider** manages several [[Providers]] providing\n *  resilience by switching between slow or misbehaving nodes, security\n *  by requiring multiple backends to aggree and performance by allowing\n *  faster backends to respond earlier.\n *\n */\nexport class FallbackProvider extends AbstractProvider {\n  /**\n   *  The number of backends that must agree on a value before it is\n   *  accpeted.\n   */\n  quorum;\n  /**\n   *  @_ignore:\n   */\n  eventQuorum;\n  /**\n   *  @_ignore:\n   */\n  eventWorkers;\n  #configs;\n  #height;\n  #initialSyncPromise;\n  /**\n   *  Creates a new **FallbackProvider** with %%providers%% connected to\n   *  %%network%%.\n   *\n   *  If a [[Provider]] is included in %%providers%%, defaults are used\n   *  for the configuration.\n   */\n  constructor(providers, network, options) {\n    super(network, options);\n    this.#configs = providers.map(p => {\n      if (p instanceof AbstractProvider) {\n        return Object.assign({\n          provider: p\n        }, defaultConfig, defaultState);\n      } else {\n        return Object.assign({}, defaultConfig, p, defaultState);\n      }\n    });\n    this.#height = -2;\n    this.#initialSyncPromise = null;\n    if (options && options.quorum != null) {\n      this.quorum = options.quorum;\n    } else {\n      this.quorum = Math.ceil(this.#configs.reduce((accum, config) => {\n        accum += config.weight;\n        return accum;\n      }, 0) / 2);\n    }\n    this.eventQuorum = 1;\n    this.eventWorkers = 1;\n    assertArgument(this.quorum <= this.#configs.reduce((a, c) => a + c.weight, 0), \"quorum exceed provider weight\", \"quorum\", this.quorum);\n  }\n  get providerConfigs() {\n    return this.#configs.map(c => {\n      const result = Object.assign({}, c);\n      for (const key in result) {\n        if (key[0] === \"_\") {\n          delete result[key];\n        }\n      }\n      return result;\n    });\n  }\n  async _detectNetwork() {\n    return Network.from(getBigInt(await this._perform({\n      method: \"chainId\"\n    })));\n  }\n  // @TODO: Add support to select providers to be the event subscriber\n  //_getSubscriber(sub: Subscription): Subscriber {\n  //    throw new Error(\"@TODO\");\n  //}\n  /**\n   *  Transforms a %%req%% into the correct method call on %%provider%%.\n   */\n  async _translatePerform(provider, req) {\n    switch (req.method) {\n      case \"broadcastTransaction\":\n        return await provider.broadcastTransaction(req.signedTransaction);\n      case \"call\":\n        return await provider.call(Object.assign({}, req.transaction, {\n          blockTag: req.blockTag\n        }));\n      case \"chainId\":\n        return (await provider.getNetwork()).chainId;\n      case \"estimateGas\":\n        return await provider.estimateGas(req.transaction);\n      case \"getBalance\":\n        return await provider.getBalance(req.address, req.blockTag);\n      case \"getBlock\":\n        {\n          const block = \"blockHash\" in req ? req.blockHash : req.blockTag;\n          return await provider.getBlock(block, req.includeTransactions);\n        }\n      case \"getBlockNumber\":\n        return await provider.getBlockNumber();\n      case \"getCode\":\n        return await provider.getCode(req.address, req.blockTag);\n      case \"getGasPrice\":\n        return (await provider.getFeeData()).gasPrice;\n      case \"getPriorityFee\":\n        return (await provider.getFeeData()).maxPriorityFeePerGas;\n      case \"getLogs\":\n        return await provider.getLogs(req.filter);\n      case \"getStorage\":\n        return await provider.getStorage(req.address, req.position, req.blockTag);\n      case \"getTransaction\":\n        return await provider.getTransaction(req.hash);\n      case \"getTransactionCount\":\n        return await provider.getTransactionCount(req.address, req.blockTag);\n      case \"getTransactionReceipt\":\n        return await provider.getTransactionReceipt(req.hash);\n      case \"getTransactionResult\":\n        return await provider.getTransactionResult(req.hash);\n    }\n  }\n  // Grab the next (random) config that is not already part of\n  // the running set\n  #getNextConfig(running) {\n    // @TODO: Maybe do a check here to favour (heavily) providers that\n    //        do not require waitForSync and disfavour providers that\n    //        seem down-ish or are behaving slowly\n    const configs = Array.from(running).map(r => r.config);\n    // Shuffle the states, sorted by priority\n    const allConfigs = this.#configs.slice();\n    shuffle(allConfigs);\n    allConfigs.sort((a, b) => a.priority - b.priority);\n    for (const config of allConfigs) {\n      if (config._lastFatalError) {\n        continue;\n      }\n      if (configs.indexOf(config) === -1) {\n        return config;\n      }\n    }\n    return null;\n  }\n  // Adds a new runner (if available) to running.\n  #addRunner(running, req) {\n    const config = this.#getNextConfig(running);\n    // No runners available\n    if (config == null) {\n      return null;\n    }\n    // Create a new runner\n    const runner = {\n      config,\n      result: null,\n      didBump: false,\n      perform: null,\n      staller: null\n    };\n    const now = getTime();\n    // Start performing this operation\n    runner.perform = (async () => {\n      try {\n        config.requests++;\n        const result = await this._translatePerform(config.provider, req);\n        runner.result = {\n          result\n        };\n      } catch (error) {\n        config.errorResponses++;\n        runner.result = {\n          error\n        };\n      }\n      const dt = getTime() - now;\n      config._totalTime += dt;\n      config.rollingDuration = 0.95 * config.rollingDuration + 0.05 * dt;\n      runner.perform = null;\n    })();\n    // Start a staller; when this times out, it's time to force\n    // kicking off another runner because we are taking too long\n    runner.staller = (async () => {\n      await stall(config.stallTimeout);\n      runner.staller = null;\n    })();\n    running.add(runner);\n    return runner;\n  }\n  // Initializes the blockNumber and network for each runner and\n  // blocks until initialized\n  async #initialSync() {\n    let initialSync = this.#initialSyncPromise;\n    if (!initialSync) {\n      const promises = [];\n      this.#configs.forEach(config => {\n        promises.push((async () => {\n          await waitForSync(config, 0);\n          if (!config._lastFatalError) {\n            config._network = await config.provider.getNetwork();\n          }\n        })());\n      });\n      this.#initialSyncPromise = initialSync = (async () => {\n        // Wait for all providers to have a block number and network\n        await Promise.all(promises);\n        // Check all the networks match\n        let chainId = null;\n        for (const config of this.#configs) {\n          if (config._lastFatalError) {\n            continue;\n          }\n          const network = config._network;\n          if (chainId == null) {\n            chainId = network.chainId;\n          } else if (network.chainId !== chainId) {\n            assert(false, \"cannot mix providers on different networks\", \"UNSUPPORTED_OPERATION\", {\n              operation: \"new FallbackProvider\"\n            });\n          }\n        }\n      })();\n    }\n    await initialSync;\n  }\n  async #checkQuorum(running, req) {\n    // Get all the result objects\n    const results = [];\n    for (const runner of running) {\n      if (runner.result != null) {\n        const {\n          tag,\n          value\n        } = normalizeResult(req.method, runner.result);\n        results.push({\n          tag,\n          value,\n          weight: runner.config.weight\n        });\n      }\n    }\n    // Are there enough results to event meet quorum?\n    if (results.reduce((a, r) => a + r.weight, 0) < this.quorum) {\n      return undefined;\n    }\n    switch (req.method) {\n      case \"getBlockNumber\":\n        {\n          // We need to get the bootstrap block height\n          if (this.#height === -2) {\n            this.#height = Math.ceil(getNumber(getMedian(this.quorum, this.#configs.filter(c => !c._lastFatalError).map(c => ({\n              value: c.blockNumber,\n              tag: getNumber(c.blockNumber).toString(),\n              weight: c.weight\n            })))));\n          }\n          // Find the mode across all the providers, allowing for\n          // a little drift between block heights\n          const mode = getFuzzyMode(this.quorum, results);\n          if (mode === undefined) {\n            return undefined;\n          }\n          if (mode > this.#height) {\n            this.#height = mode;\n          }\n          return this.#height;\n        }\n      case \"getGasPrice\":\n      case \"getPriorityFee\":\n      case \"estimateGas\":\n        return getMedian(this.quorum, results);\n      case \"getBlock\":\n        // Pending blocks are in the mempool and already\n        // quite untrustworthy; just grab anything\n        if (\"blockTag\" in req && req.blockTag === \"pending\") {\n          return getAnyResult(this.quorum, results);\n        }\n        return checkQuorum(this.quorum, results);\n      case \"call\":\n      case \"chainId\":\n      case \"getBalance\":\n      case \"getTransactionCount\":\n      case \"getCode\":\n      case \"getStorage\":\n      case \"getTransaction\":\n      case \"getTransactionReceipt\":\n      case \"getLogs\":\n        return checkQuorum(this.quorum, results);\n      case \"broadcastTransaction\":\n        return getAnyResult(this.quorum, results);\n    }\n    assert(false, \"unsupported method\", \"UNSUPPORTED_OPERATION\", {\n      operation: `_perform(${stringify(req.method)})`\n    });\n  }\n  async #waitForQuorum(running, req) {\n    if (running.size === 0) {\n      throw new Error(\"no runners?!\");\n    }\n    // Any promises that are interesting to watch for; an expired stall\n    // or a successful perform\n    const interesting = [];\n    let newRunners = 0;\n    for (const runner of running) {\n      // No responses, yet; keep an eye on it\n      if (runner.perform) {\n        interesting.push(runner.perform);\n      }\n      // Still stalling...\n      if (runner.staller) {\n        interesting.push(runner.staller);\n        continue;\n      }\n      // This runner has already triggered another runner\n      if (runner.didBump) {\n        continue;\n      }\n      // Got a response (result or error) or stalled; kick off another runner\n      runner.didBump = true;\n      newRunners++;\n    }\n    // Check if we have reached quorum on a result (or error)\n    const value = await this.#checkQuorum(running, req);\n    if (value !== undefined) {\n      if (value instanceof Error) {\n        throw value;\n      }\n      return value;\n    }\n    // Add any new runners, because a staller timed out or a result\n    // or error response came in.\n    for (let i = 0; i < newRunners; i++) {\n      this.#addRunner(running, req);\n    }\n    // All providers have returned, and we have no result\n    assert(interesting.length > 0, \"quorum not met\", \"SERVER_ERROR\", {\n      request: \"%sub-requests\",\n      info: {\n        request: req,\n        results: Array.from(running).map(r => stringify(r.result))\n      }\n    });\n    // Wait for someone to either complete its perform or stall out\n    await Promise.race(interesting);\n    // This is recursive, but at worst case the depth is 2x the\n    // number of providers (each has a perform and a staller)\n    return await this.#waitForQuorum(running, req);\n  }\n  async _perform(req) {\n    // Broadcasting a transaction is rare (ish) and already incurs\n    // a cost on the user, so spamming is safe-ish. Just send it to\n    // every backend.\n    if (req.method === \"broadcastTransaction\") {\n      // Once any broadcast provides a positive result, use it. No\n      // need to wait for anyone else\n      const results = this.#configs.map(c => null);\n      const broadcasts = this.#configs.map(async ({\n        provider,\n        weight\n      }, index) => {\n        try {\n          const result = await provider._perform(req);\n          results[index] = Object.assign(normalizeResult(req.method, {\n            result\n          }), {\n            weight\n          });\n        } catch (error) {\n          results[index] = Object.assign(normalizeResult(req.method, {\n            error\n          }), {\n            weight\n          });\n        }\n      });\n      // As each promise finishes...\n      while (true) {\n        // Check for a valid broadcast result\n        const done = results.filter(r => r != null);\n        for (const {\n          value\n        } of done) {\n          if (!(value instanceof Error)) {\n            return value;\n          }\n        }\n        // Check for a legit broadcast error (one which we cannot\n        // recover from; some nodes may return the following red\n        // herring events:\n        // - alredy seend (UNKNOWN_ERROR)\n        // - NONCE_EXPIRED\n        // - REPLACEMENT_UNDERPRICED\n        const result = checkQuorum(this.quorum, results.filter(r => r != null));\n        if (isError(result, \"INSUFFICIENT_FUNDS\")) {\n          throw result;\n        }\n        // Kick off the next provider (if any)\n        const waiting = broadcasts.filter((b, i) => results[i] == null);\n        if (waiting.length === 0) {\n          break;\n        }\n        await Promise.race(waiting);\n      }\n      // Use standard quorum results; any result was returned above,\n      // so this will find any error that met quorum if any\n      const result = getAnyResult(this.quorum, results);\n      assert(result !== undefined, \"problem multi-broadcasting\", \"SERVER_ERROR\", {\n        request: \"%sub-requests\",\n        info: {\n          request: req,\n          results: results.map(stringify)\n        }\n      });\n      if (result instanceof Error) {\n        throw result;\n      }\n      return result;\n    }\n    await this.#initialSync();\n    // Bootstrap enough runners to meet quorum\n    const running = new Set();\n    let inflightQuorum = 0;\n    while (true) {\n      const runner = this.#addRunner(running, req);\n      if (runner == null) {\n        break;\n      }\n      inflightQuorum += runner.config.weight;\n      if (inflightQuorum >= this.quorum) {\n        break;\n      }\n    }\n    const result = await this.#waitForQuorum(running, req);\n    // Track requests sent to a provider that are still\n    // outstanding after quorum has been otherwise found\n    for (const runner of running) {\n      if (runner.perform && runner.result == null) {\n        runner.config.lateResponses++;\n      }\n    }\n    return result;\n  }\n  async destroy() {\n    for (const {\n      provider\n    } of this.#configs) {\n      provider.destroy();\n    }\n    super.destroy();\n  }\n}", "map": {"version": 3, "names": ["assert", "assertArgument", "getBigInt", "getNumber", "isError", "AbstractProvider", "Network", "BN_1", "BigInt", "BN_2", "shuffle", "array", "i", "length", "j", "Math", "floor", "random", "tmp", "stall", "duration", "Promise", "resolve", "setTimeout", "getTime", "Date", "stringify", "value", "JSON", "key", "type", "toString", "defaultConfig", "stallTimeout", "priority", "weight", "defaultState", "blockNumber", "requests", "lateResponses", "errorResponses", "outOfSync", "unsupportedEvents", "rollingDuration", "score", "_network", "_updateNumber", "_totalTime", "_lastFatalError", "_lastFatalErrorTimestamp", "waitForSync", "config", "provider", "getBlockNumber", "error", "_normalize", "Array", "isArray", "map", "join", "toJSON", "keys", "Object", "sort", "k", "console", "log", "Error", "normalizeResult", "method", "tag", "assign", "shortMessage", "undefined", "reason", "info", "result", "checkQuorum", "quorum", "results", "tally", "Map", "t", "get", "set", "best", "r", "values", "getMedian", "resultWeight", "errorMap", "bestError", "e", "push", "a", "b", "mid", "getAnyResult", "getFuzzyMode", "add", "bestWeight", "bestResult", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "eventQuorum", "eventWorkers", "configs", "height", "initialSyncPromise", "constructor", "providers", "network", "options", "p", "ceil", "reduce", "accum", "c", "providerConfigs", "_detectNetwork", "from", "_perform", "_translatePerform", "req", "broadcastTransaction", "signedTransaction", "call", "transaction", "blockTag", "getNetwork", "chainId", "estimateGas", "getBalance", "address", "block", "blockHash", "getBlock", "includeTransactions", "getCode", "getFeeData", "gasPrice", "maxPriorityFeePerGas", "getLogs", "filter", "getStorage", "position", "getTransaction", "hash", "getTransactionCount", "getTransactionReceipt", "getTransactionResult", "getNextConfig", "#getNextConfig", "running", "allConfigs", "slice", "indexOf", "add<PERSON><PERSON><PERSON>", "#addRunner", "runner", "didBump", "perform", "staller", "now", "dt", "initialSync", "#initialSync", "promises", "for<PERSON>ach", "all", "operation", "#checkQuorum", "mode", "waitForQuorum", "#waitForQuorum", "size", "interesting", "newRunners", "request", "race", "broadcasts", "index", "done", "waiting", "Set", "inflightQuorum", "destroy"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-fallback.ts"], "sourcesContent": ["/**\n *  A **FallbackProvider** provides resilience, security and performance\n *  in a way that is customizable and configurable.\n *\n *  @_section: api/providers/fallback-provider:Fallback Provider [about-fallback-provider]\n */\nimport {\n    assert, assertArgument, getBigInt, getNumber, isError\n} from \"../utils/index.js\";\n\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { Network } from \"./network.js\"\n\nimport type { PerformActionRequest } from \"./abstract-provider.js\";\nimport type { Networkish } from \"./network.js\"\n\nconst BN_1 = BigInt(\"1\");\nconst BN_2 = BigInt(\"2\");\n\nfunction shuffle<T = any>(array: Array<T>): void {\n    for (let i = array.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        const tmp = array[i];\n        array[i] = array[j];\n        array[j] = tmp;\n    }\n}\n\nfunction stall(duration: number): Promise<void> {\n    return new Promise((resolve) => { setTimeout(resolve, duration); });\n}\n\nfunction getTime(): number { return (new Date()).getTime(); }\n\nfunction stringify(value: any): string {\n    return JSON.stringify(value, (key, value) => {\n        if (typeof(value) === \"bigint\") {\n            return { type: \"bigint\", value: value.toString() };\n        }\n        return value;\n    });\n}\n\n/**\n *  A configuration entry for how to use a [[Provider]].\n */\nexport interface FallbackProviderConfig {\n\n    /**\n     *  The provider.\n     */\n    provider: AbstractProvider;\n\n    /**\n     *  The amount of time to wait before kicking off the next provider.\n     *\n     *  Any providers that have not responded can still respond and be\n     *  counted, but this ensures new providers start.\n     */\n    stallTimeout?: number;\n\n    /**\n     *  The priority. Lower priority providers are dispatched first.\n     */\n    priority?: number;\n\n    /**\n     *  The amount of weight a provider is given against the quorum.\n     */\n    weight?: number;\n};\n\nconst defaultConfig = { stallTimeout: 400, priority: 1, weight: 1 };\n\n// We track a bunch of extra stuff that might help debug problems or\n// optimize infrastructure later on.\n/**\n *  The statistics and state maintained for a [[Provider]].\n */\nexport interface FallbackProviderState extends Required<FallbackProviderConfig> {\n\n    /**\n     *  The most recent blockNumber this provider has reported (-2 if none).\n     */\n    blockNumber: number;\n\n    /**\n     *  The number of total requests ever sent to this provider.\n     */\n    requests: number;\n\n    /**\n     *  The number of responses that errored.\n     */\n    errorResponses: number;\n\n    /**\n     *  The number of responses that occured after the result resolved.\n     */\n    lateResponses: number;\n\n    /**\n     *  How many times syncing was required to catch up the expected block.\n     */\n    outOfSync: number;\n\n    /**\n     *  The number of requests which reported unsupported operation.\n     */\n    unsupportedEvents: number;\n\n    /**\n     *  A rolling average (5% current duration) for response time.\n     */\n    rollingDuration: number;\n\n    /**\n     *  The ratio of quorum-agreed results to total.\n     */\n    score: number;\n}\n\ninterface Config extends FallbackProviderState {\n    _updateNumber: null | Promise<any>;\n    _network: null | Network;\n    _totalTime: number;\n    _lastFatalError: null | Error;\n    _lastFatalErrorTimestamp: number;\n}\n\nconst defaultState = {\n    blockNumber: -2, requests: 0, lateResponses: 0, errorResponses: 0,\n    outOfSync: -1, unsupportedEvents: 0, rollingDuration: 0, score: 0,\n    _network: null, _updateNumber: null, _totalTime: 0,\n    _lastFatalError: null, _lastFatalErrorTimestamp: 0\n};\n\n\nasync function waitForSync(config: Config, blockNumber: number): Promise<void> {\n    while (config.blockNumber < 0 || config.blockNumber < blockNumber) {\n        if (!config._updateNumber) {\n            config._updateNumber = (async () => {\n                try {\n                    const blockNumber = await config.provider.getBlockNumber();\n                    if (blockNumber > config.blockNumber) {\n                        config.blockNumber = blockNumber;\n                    }\n                } catch (error: any) {\n                    config.blockNumber = -2;\n                    config._lastFatalError = error;\n                    config._lastFatalErrorTimestamp = getTime();\n                }\n                config._updateNumber = null;\n            })();\n        }\n        await config._updateNumber;\n        config.outOfSync++;\n        if (config._lastFatalError) { break; }\n    }\n}\n\n/**\n *  Additional options to configure a [[FallbackProvider]].\n */\nexport type FallbackProviderOptions = {\n    // How many providers must agree on a value before reporting\n    // back the response\n    quorum?: number;\n\n    // How many providers must have reported the same event\n    // for it to be emitted (currently unimplmented)\n    eventQuorum?: number;\n\n    // How many providers to dispatch each event to simultaneously.\n    // Set this to 0 to use getLog polling, which implies eventQuorum\n    // is equal to quorum. (currently unimplemented)\n    eventWorkers?: number;\n\n    cacheTimeout?: number;\n\n    pollingInterval?: number;\n};\n\ntype RunnerResult = { result: any } | { error: Error };\n\ntype RunnerState = {\n    config: Config;\n    staller: null | Promise<void>;\n    didBump: boolean;\n    perform: null | Promise<any>;\n    result: null | RunnerResult;\n}\n\nfunction _normalize(value: any): string {\n    if (value == null) { return \"null\"; }\n\n    if (Array.isArray(value)) {\n        return \"[\" + (value.map(_normalize)).join(\",\") + \"]\";\n    }\n\n    if (typeof(value) === \"object\" && typeof(value.toJSON) === \"function\") {\n        return _normalize(value.toJSON());\n    }\n\n    switch (typeof(value)) {\n        case \"boolean\": case \"symbol\":\n            return value.toString();\n        case \"bigint\": case \"number\":\n            return BigInt(value).toString();\n        case \"string\":\n            return JSON.stringify(value);\n        case \"object\": {\n            const keys = Object.keys(value);\n            keys.sort();\n            return \"{\" + keys.map((k) => `${ JSON.stringify(k) }:${ _normalize(value[k]) }`).join(\",\") + \"}\";\n        }\n    }\n\n    console.log(\"Could not serialize\", value);\n    throw new Error(\"Hmm...\");\n}\n\nfunction normalizeResult(method: string, value: RunnerResult): { tag: string, value: any } {\n\n    if (\"error\" in value) {\n        const error = value.error;\n\n        let tag: string;\n        if (isError(error, \"CALL_EXCEPTION\")) {\n            tag = _normalize(Object.assign({ }, error, {\n                shortMessage: undefined, reason: undefined, info: undefined\n            }));\n        } else {\n            tag = _normalize(error)\n        }\n\n        return { tag, value: error };\n    }\n\n    const result = value.result;\n    return { tag: _normalize(result), value: result };\n}\n\ntype TallyResult = {\n    tag: string;\n    value: any;\n    weight: number;\n};\n\n// This strategy picks the highest weight result, as long as the weight is\n// equal to or greater than quorum\nfunction checkQuorum(quorum: number, results: Array<TallyResult>): any | Error {\n    const tally: Map<string, { value: any, weight: number }> = new Map();\n    for (const { value, tag, weight } of results) {\n        const t = tally.get(tag) || { value, weight: 0 };\n        t.weight += weight;\n        tally.set(tag, t);\n    }\n\n    let best: null | { value: any, weight: number } = null;\n    for (const r of tally.values()) {\n        if (r.weight >= quorum && (!best || r.weight > best.weight)) {\n            best = r;\n        }\n    }\n\n    if (best) { return best.value; }\n\n    return undefined;\n}\n\nfunction getMedian(quorum: number, results: Array<TallyResult>): undefined | bigint | Error {\n    let resultWeight = 0;\n\n    const errorMap: Map<string, { weight: number, value: Error }> = new Map();\n    let bestError: null | { weight: number, value: Error } = null;\n\n    const values: Array<bigint> = [ ];\n    for (const { value, tag, weight } of results) {\n        if (value instanceof Error) {\n            const e = errorMap.get(tag) || { value, weight: 0 };\n            e.weight += weight;\n            errorMap.set(tag, e);\n\n            if (bestError == null || e.weight > bestError.weight) { bestError = e; }\n        } else {\n            values.push(BigInt(value));\n            resultWeight += weight;\n        }\n    }\n\n    if (resultWeight < quorum) {\n        // We have quorum for an error\n        if (bestError && bestError.weight >= quorum) { return bestError.value; }\n\n        // We do not have quorum for a result\n        return undefined;\n    }\n\n    // Get the sorted values\n    values.sort((a, b) => ((a < b) ? -1: (b > a) ? 1: 0));\n\n    const mid = Math.floor(values.length / 2);\n\n    // Odd-length; take the middle value\n    if (values.length % 2) { return values[mid]; }\n\n    // Even length; take the ceiling of the mean of the center two values\n    return (values[mid - 1] + values[mid] + BN_1) / BN_2;\n}\n\nfunction getAnyResult(quorum: number, results: Array<TallyResult>): undefined | any | Error {\n    // If any value or error meets quorum, that is our preferred result\n    const result = checkQuorum(quorum, results);\n    if (result !== undefined) { return result; }\n\n    // Otherwise, do we have any result?\n    for (const r of results) {\n        if (r.value) { return r.value; }\n    }\n\n    // Nope!\n    return undefined;\n}\n\nfunction getFuzzyMode(quorum: number, results: Array<TallyResult>): undefined | number {\n    if (quorum === 1) { return getNumber(<bigint>getMedian(quorum, results), \"%internal\"); }\n\n    const tally: Map<number, { result: number, weight: number }> = new Map();\n    const add = (result: number, weight: number) => {\n        const t = tally.get(result) || { result, weight: 0 };\n        t.weight += weight;\n        tally.set(result, t);\n    };\n\n    for (const { weight, value } of results) {\n        const r = getNumber(value);\n        add(r - 1, weight);\n        add(r, weight);\n        add(r + 1, weight);\n    }\n\n    let bestWeight = 0;\n    let bestResult: undefined | number = undefined;\n\n    for (const { weight, result } of tally.values()) {\n        // Use this result, if this result meets quorum and has either:\n        // - a better weight\n        // - or equal weight, but the result is larger\n        if (weight >= quorum && (weight > bestWeight || (bestResult != null && weight === bestWeight && result > bestResult))) {\n            bestWeight = weight;\n            bestResult = result;\n        }\n    }\n\n    return bestResult;\n}\n\n/**\n *  A **FallbackProvider** manages several [[Providers]] providing\n *  resilience by switching between slow or misbehaving nodes, security\n *  by requiring multiple backends to aggree and performance by allowing\n *  faster backends to respond earlier.\n *\n */\nexport class FallbackProvider extends AbstractProvider {\n\n    /**\n     *  The number of backends that must agree on a value before it is\n     *  accpeted.\n     */\n    readonly quorum: number;\n\n    /**\n     *  @_ignore:\n     */\n    readonly eventQuorum: number;\n\n    /**\n     *  @_ignore:\n     */\n    readonly eventWorkers: number;\n\n    readonly #configs: Array<Config>;\n\n    #height: number;\n    #initialSyncPromise: null | Promise<void>;\n\n    /**\n     *  Creates a new **FallbackProvider** with %%providers%% connected to\n     *  %%network%%.\n     *\n     *  If a [[Provider]] is included in %%providers%%, defaults are used\n     *  for the configuration.\n     */\n    constructor(providers: Array<AbstractProvider | FallbackProviderConfig>, network?: Networkish, options?: FallbackProviderOptions) {\n        super(network, options);\n\n        this.#configs = providers.map((p) => {\n            if (p instanceof AbstractProvider) {\n                return Object.assign({ provider: p }, defaultConfig, defaultState );\n            } else {\n                return Object.assign({ }, defaultConfig, p, defaultState );\n            }\n        });\n\n        this.#height = -2;\n        this.#initialSyncPromise = null;\n\n        if (options && options.quorum != null) {\n            this.quorum = options.quorum;\n        } else {\n            this.quorum = Math.ceil(this.#configs.reduce((accum, config) => {\n                accum += config.weight;\n                return accum;\n            }, 0) / 2);\n        }\n\n        this.eventQuorum = 1;\n        this.eventWorkers = 1;\n\n        assertArgument(this.quorum <= this.#configs.reduce((a, c) => (a + c.weight), 0),\n            \"quorum exceed provider weight\", \"quorum\", this.quorum);\n    }\n\n    get providerConfigs(): Array<FallbackProviderState> {\n        return this.#configs.map((c) => {\n            const result: any = Object.assign({ }, c);\n            for (const key in result) {\n                if (key[0] === \"_\") { delete result[key]; }\n            }\n            return result;\n        });\n    }\n\n    async _detectNetwork(): Promise<Network> {\n        return Network.from(getBigInt(await this._perform({ method: \"chainId\" })));\n    }\n\n    // @TODO: Add support to select providers to be the event subscriber\n    //_getSubscriber(sub: Subscription): Subscriber {\n    //    throw new Error(\"@TODO\");\n    //}\n\n    /**\n     *  Transforms a %%req%% into the correct method call on %%provider%%.\n     */\n    async _translatePerform(provider: AbstractProvider, req: PerformActionRequest): Promise<any> {\n        switch (req.method) {\n            case \"broadcastTransaction\":\n                return await provider.broadcastTransaction(req.signedTransaction);\n            case \"call\":\n                return await provider.call(Object.assign({ }, req.transaction, { blockTag: req.blockTag }));\n            case \"chainId\":\n                return (await provider.getNetwork()).chainId;\n            case \"estimateGas\":\n                return await provider.estimateGas(req.transaction);\n            case \"getBalance\":\n                return await provider.getBalance(req.address, req.blockTag);\n            case \"getBlock\": {\n                const block = (\"blockHash\" in req) ? req.blockHash: req.blockTag;\n                return await provider.getBlock(block, req.includeTransactions);\n            }\n            case \"getBlockNumber\":\n                return await provider.getBlockNumber();\n            case \"getCode\":\n                return await provider.getCode(req.address, req.blockTag);\n            case \"getGasPrice\":\n                return (await provider.getFeeData()).gasPrice;\n            case \"getPriorityFee\":\n                return (await provider.getFeeData()).maxPriorityFeePerGas;\n            case \"getLogs\":\n                return await provider.getLogs(req.filter);\n            case \"getStorage\":\n                return await provider.getStorage(req.address, req.position, req.blockTag);\n            case \"getTransaction\":\n                return await provider.getTransaction(req.hash);\n            case \"getTransactionCount\":\n                return await provider.getTransactionCount(req.address, req.blockTag);\n            case \"getTransactionReceipt\":\n                return await provider.getTransactionReceipt(req.hash);\n            case \"getTransactionResult\":\n                return await provider.getTransactionResult(req.hash);\n        }\n    }\n\n    // Grab the next (random) config that is not already part of\n    // the running set\n    #getNextConfig(running: Set<RunnerState>): null | Config {\n        // @TODO: Maybe do a check here to favour (heavily) providers that\n        //        do not require waitForSync and disfavour providers that\n        //        seem down-ish or are behaving slowly\n\n        const configs = Array.from(running).map((r) => r.config)\n\n        // Shuffle the states, sorted by priority\n        const allConfigs = this.#configs.slice();\n        shuffle(allConfigs);\n        allConfigs.sort((a, b) => (a.priority - b.priority));\n\n        for (const config of allConfigs) {\n            if (config._lastFatalError) { continue; }\n            if (configs.indexOf(config) === -1) { return config; }\n        }\n\n        return null;\n    }\n\n    // Adds a new runner (if available) to running.\n    #addRunner(running: Set<RunnerState>, req: PerformActionRequest): null | RunnerState {\n        const config = this.#getNextConfig(running);\n\n        // No runners available\n        if (config == null) { return null; }\n\n        // Create a new runner\n        const runner: RunnerState = {\n            config, result: null, didBump: false,\n            perform: null, staller: null\n        };\n\n        const now = getTime();\n\n        // Start performing this operation\n        runner.perform = (async () => {\n            try {\n                config.requests++;\n                const result = await this._translatePerform(config.provider, req);\n                runner.result = { result };\n            } catch (error: any) {\n                config.errorResponses++;\n                runner.result = { error };\n            }\n\n            const dt = (getTime() - now);\n            config._totalTime += dt;\n\n            config.rollingDuration = 0.95 * config.rollingDuration + 0.05 * dt;\n\n            runner.perform = null;\n        })();\n\n        // Start a staller; when this times out, it's time to force\n        // kicking off another runner because we are taking too long\n        runner.staller = (async () => {\n            await stall(config.stallTimeout);\n            runner.staller = null;\n        })();\n\n        running.add(runner);\n        return runner;\n    }\n\n    // Initializes the blockNumber and network for each runner and\n    // blocks until initialized\n    async #initialSync(): Promise<void> {\n        let initialSync = this.#initialSyncPromise;\n        if (!initialSync) {\n            const promises: Array<Promise<any>> = [ ];\n            this.#configs.forEach((config) => {\n                promises.push((async () => {\n                    await waitForSync(config, 0);\n                    if (!config._lastFatalError) {\n                        config._network = await config.provider.getNetwork();\n                    }\n                })());\n            });\n\n            this.#initialSyncPromise = initialSync = (async () => {\n                // Wait for all providers to have a block number and network\n                await Promise.all(promises);\n\n                // Check all the networks match\n                let chainId: null | bigint = null;\n                for (const config of this.#configs) {\n                    if (config._lastFatalError) { continue; }\n                    const network = <Network>(config._network);\n                    if (chainId == null) {\n                        chainId = network.chainId;\n                    } else if (network.chainId !== chainId) {\n                        assert(false, \"cannot mix providers on different networks\", \"UNSUPPORTED_OPERATION\", {\n                            operation: \"new FallbackProvider\"\n                        });\n                    }\n                }\n            })();\n        }\n\n        await initialSync\n    }\n\n\n    async #checkQuorum(running: Set<RunnerState>, req: PerformActionRequest): Promise<any> {\n        // Get all the result objects\n        const results: Array<TallyResult> = [ ];\n        for (const runner of running) {\n            if (runner.result != null) {\n                const { tag, value } = normalizeResult(req.method, runner.result);\n                results.push({ tag, value, weight: runner.config.weight });\n            }\n        }\n\n        // Are there enough results to event meet quorum?\n        if (results.reduce((a, r) => (a + r.weight), 0) < this.quorum) {\n            return undefined;\n        }\n\n        switch (req.method) {\n            case \"getBlockNumber\": {\n                // We need to get the bootstrap block height\n                if (this.#height === -2) {\n                    this.#height = Math.ceil(getNumber(<bigint>getMedian(this.quorum, this.#configs.filter((c) => (!c._lastFatalError)).map((c) => ({\n                        value: c.blockNumber,\n                        tag: getNumber(c.blockNumber).toString(),\n                        weight: c.weight\n                    })))));\n                }\n\n                // Find the mode across all the providers, allowing for\n                // a little drift between block heights\n                const mode = getFuzzyMode(this.quorum, results);\n                if (mode === undefined) { return undefined; }\n                if (mode > this.#height) { this.#height = mode; }\n                return this.#height;\n            }\n\n            case \"getGasPrice\":\n            case \"getPriorityFee\":\n            case \"estimateGas\":\n                return getMedian(this.quorum, results);\n\n            case \"getBlock\":\n                // Pending blocks are in the mempool and already\n                // quite untrustworthy; just grab anything\n                if (\"blockTag\" in req && req.blockTag === \"pending\") {\n                    return getAnyResult(this.quorum, results);\n                }\n                return checkQuorum(this.quorum, results);\n\n            case \"call\":\n            case \"chainId\":\n            case \"getBalance\":\n            case \"getTransactionCount\":\n            case \"getCode\":\n            case \"getStorage\":\n            case \"getTransaction\":\n            case \"getTransactionReceipt\":\n            case \"getLogs\":\n                return checkQuorum(this.quorum, results);\n\n            case \"broadcastTransaction\":\n                return getAnyResult(this.quorum, results);\n        }\n\n        assert(false, \"unsupported method\", \"UNSUPPORTED_OPERATION\", {\n            operation: `_perform(${ stringify((<any>req).method) })`\n        });\n    }\n\n    async #waitForQuorum(running: Set<RunnerState>, req: PerformActionRequest): Promise<any> {\n        if (running.size === 0) { throw new Error(\"no runners?!\"); }\n\n        // Any promises that are interesting to watch for; an expired stall\n        // or a successful perform\n        const interesting: Array<Promise<void>> = [ ];\n\n        let newRunners = 0;\n        for (const runner of running) {\n\n            // No responses, yet; keep an eye on it\n            if (runner.perform) {\n                interesting.push(runner.perform);\n            }\n\n            // Still stalling...\n            if (runner.staller) {\n                interesting.push(runner.staller);\n                continue;\n            }\n\n            // This runner has already triggered another runner\n            if (runner.didBump) { continue; }\n\n            // Got a response (result or error) or stalled; kick off another runner\n            runner.didBump = true;\n            newRunners++;\n        }\n\n        // Check if we have reached quorum on a result (or error)\n        const value = await this.#checkQuorum(running, req);\n        if (value !== undefined) {\n            if (value instanceof Error) { throw value; }\n            return value;\n        }\n\n        // Add any new runners, because a staller timed out or a result\n        // or error response came in.\n        for (let i = 0; i < newRunners; i++) {\n            this.#addRunner(running, req);\n        }\n\n        // All providers have returned, and we have no result\n\n        assert(interesting.length > 0, \"quorum not met\", \"SERVER_ERROR\", {\n            request: \"%sub-requests\",\n            info: { request: req, results: Array.from(running).map((r) => stringify(r.result)) }\n        });\n\n        // Wait for someone to either complete its perform or stall out\n        await Promise.race(interesting);\n\n        // This is recursive, but at worst case the depth is 2x the\n        // number of providers (each has a perform and a staller)\n        return await this.#waitForQuorum(running, req);\n    }\n\n    async _perform<T = any>(req: PerformActionRequest): Promise<T> {\n        // Broadcasting a transaction is rare (ish) and already incurs\n        // a cost on the user, so spamming is safe-ish. Just send it to\n        // every backend.\n        if (req.method === \"broadcastTransaction\") {\n            // Once any broadcast provides a positive result, use it. No\n            // need to wait for anyone else\n            const results: Array<null | TallyResult> = this.#configs.map((c) => null);\n            const broadcasts = this.#configs.map(async ({ provider, weight }, index) => {\n                try {\n                    const result = await provider._perform(req);\n                    results[index] = Object.assign(normalizeResult(req.method, { result }), { weight });\n                } catch (error: any) {\n                    results[index] = Object.assign(normalizeResult(req.method, { error }), { weight });\n                }\n            });\n\n            // As each promise finishes...\n            while (true) {\n                // Check for a valid broadcast result\n                const done = <Array<any>>results.filter((r) => (r != null));\n                for (const { value } of done) {\n                    if (!(value instanceof Error)) { return value; }\n                }\n\n                // Check for a legit broadcast error (one which we cannot\n                // recover from; some nodes may return the following red\n                // herring events:\n                // - alredy seend (UNKNOWN_ERROR)\n                // - NONCE_EXPIRED\n                // - REPLACEMENT_UNDERPRICED\n                const result = checkQuorum(this.quorum, <Array<any>>results.filter((r) => (r != null)));\n                if (isError(result, \"INSUFFICIENT_FUNDS\")) {\n                    throw result;\n                }\n\n                // Kick off the next provider (if any)\n                const waiting = broadcasts.filter((b, i) => (results[i] == null));\n                if (waiting.length === 0) { break; }\n                await Promise.race(waiting);\n            }\n\n            // Use standard quorum results; any result was returned above,\n            // so this will find any error that met quorum if any\n            const result = getAnyResult(this.quorum, <Array<any>>results);\n            assert(result !== undefined, \"problem multi-broadcasting\", \"SERVER_ERROR\", {\n                request: \"%sub-requests\",\n                info: { request: req, results: results.map(stringify) }\n            })\n            if (result instanceof Error) { throw result; }\n            return result;\n        }\n\n        await this.#initialSync();\n\n        // Bootstrap enough runners to meet quorum\n        const running: Set<RunnerState> = new Set();\n        let inflightQuorum = 0;\n        while (true) {\n            const runner = this.#addRunner(running, req);\n            if (runner == null) { break; }\n            inflightQuorum += runner.config.weight;\n            if (inflightQuorum >= this.quorum) { break; }\n        }\n\n        const result = await this.#waitForQuorum(running, req);\n\n        // Track requests sent to a provider that are still\n        // outstanding after quorum has been otherwise found\n        for (const runner of running) {\n            if (runner.perform && runner.result == null) {\n                runner.config.lateResponses++;\n            }\n        }\n\n        return result;\n    }\n\n    async destroy(): Promise<void> {\n        for (const { provider } of this.#configs) {\n            provider.destroy();\n        }\n        super.destroy();\n    }\n}\n"], "mappings": "AAAA;;;;;;AAMA,SACIA,MAAM,EAAEC,cAAc,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,QAClD,mBAAmB;AAE1B,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,OAAO,QAAQ,cAAc;AAKtC,MAAMC,IAAI,GAAGC,MAAM,CAAC,GAAG,CAAC;AACxB,MAAMC,IAAI,GAAGD,MAAM,CAAC,GAAG,CAAC;AAExB,SAASE,OAAOA,CAAUC,KAAe;EACrC,KAAK,IAAIC,CAAC,GAAGD,KAAK,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACvC,MAAME,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,IAAIL,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7C,MAAMM,GAAG,GAAGP,KAAK,CAACC,CAAC,CAAC;IACpBD,KAAK,CAACC,CAAC,CAAC,GAAGD,KAAK,CAACG,CAAC,CAAC;IACnBH,KAAK,CAACG,CAAC,CAAC,GAAGI,GAAG;;AAEtB;AAEA,SAASC,KAAKA,CAACC,QAAgB;EAC3B,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;IAAGC,UAAU,CAACD,OAAO,EAAEF,QAAQ,CAAC;EAAE,CAAC,CAAC;AACvE;AAEA,SAASI,OAAOA,CAAA;EAAa,OAAQ,IAAIC,IAAI,EAAE,CAAED,OAAO,EAAE;AAAE;AAE5D,SAASE,SAASA,CAACC,KAAU;EACzB,OAAOC,IAAI,CAACF,SAAS,CAACC,KAAK,EAAE,CAACE,GAAG,EAAEF,KAAK,KAAI;IACxC,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;MAC5B,OAAO;QAAEG,IAAI,EAAE,QAAQ;QAAEH,KAAK,EAAEA,KAAK,CAACI,QAAQ;MAAE,CAAE;;IAEtD,OAAOJ,KAAK;EAChB,CAAC,CAAC;AACN;AA6BC;AAED,MAAMK,aAAa,GAAG;EAAEC,YAAY,EAAE,GAAG;EAAEC,QAAQ,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAC,CAAE;AA0DnE,MAAMC,YAAY,GAAG;EACjBC,WAAW,EAAE,CAAC,CAAC;EAAEC,QAAQ,EAAE,CAAC;EAAEC,aAAa,EAAE,CAAC;EAAEC,cAAc,EAAE,CAAC;EACjEC,SAAS,EAAE,CAAC,CAAC;EAAEC,iBAAiB,EAAE,CAAC;EAAEC,eAAe,EAAE,CAAC;EAAEC,KAAK,EAAE,CAAC;EACjEC,QAAQ,EAAE,IAAI;EAAEC,aAAa,EAAE,IAAI;EAAEC,UAAU,EAAE,CAAC;EAClDC,eAAe,EAAE,IAAI;EAAEC,wBAAwB,EAAE;CACpD;AAGD,eAAeC,WAAWA,CAACC,MAAc,EAAEd,WAAmB;EAC1D,OAAOc,MAAM,CAACd,WAAW,GAAG,CAAC,IAAIc,MAAM,CAACd,WAAW,GAAGA,WAAW,EAAE;IAC/D,IAAI,CAACc,MAAM,CAACL,aAAa,EAAE;MACvBK,MAAM,CAACL,aAAa,GAAG,CAAC,YAAW;QAC/B,IAAI;UACA,MAAMT,WAAW,GAAG,MAAMc,MAAM,CAACC,QAAQ,CAACC,cAAc,EAAE;UAC1D,IAAIhB,WAAW,GAAGc,MAAM,CAACd,WAAW,EAAE;YAClCc,MAAM,CAACd,WAAW,GAAGA,WAAW;;SAEvC,CAAC,OAAOiB,KAAU,EAAE;UACjBH,MAAM,CAACd,WAAW,GAAG,CAAC,CAAC;UACvBc,MAAM,CAACH,eAAe,GAAGM,KAAK;UAC9BH,MAAM,CAACF,wBAAwB,GAAGzB,OAAO,EAAE;;QAE/C2B,MAAM,CAACL,aAAa,GAAG,IAAI;MAC/B,CAAC,EAAC,CAAE;;IAER,MAAMK,MAAM,CAACL,aAAa;IAC1BK,MAAM,CAACV,SAAS,EAAE;IAClB,IAAIU,MAAM,CAACH,eAAe,EAAE;MAAE;;;AAEtC;AAkCA,SAASO,UAAUA,CAAC5B,KAAU;EAC1B,IAAIA,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,MAAM;;EAElC,IAAI6B,KAAK,CAACC,OAAO,CAAC9B,KAAK,CAAC,EAAE;IACtB,OAAO,GAAG,GAAIA,KAAK,CAAC+B,GAAG,CAACH,UAAU,CAAC,CAAEI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;;EAGxD,IAAI,OAAOhC,KAAM,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACiC,MAAO,KAAK,UAAU,EAAE;IACnE,OAAOL,UAAU,CAAC5B,KAAK,CAACiC,MAAM,EAAE,CAAC;;EAGrC,QAAQ,OAAOjC,KAAM;IACjB,KAAK,SAAS;IAAE,KAAK,QAAQ;MACzB,OAAOA,KAAK,CAACI,QAAQ,EAAE;IAC3B,KAAK,QAAQ;IAAE,KAAK,QAAQ;MACxB,OAAOvB,MAAM,CAACmB,KAAK,CAAC,CAACI,QAAQ,EAAE;IACnC,KAAK,QAAQ;MACT,OAAOH,IAAI,CAACF,SAAS,CAACC,KAAK,CAAC;IAChC,KAAK,QAAQ;MAAE;QACX,MAAMkC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAClC,KAAK,CAAC;QAC/BkC,IAAI,CAACE,IAAI,EAAE;QACX,OAAO,GAAG,GAAGF,IAAI,CAACH,GAAG,CAAEM,CAAC,IAAK,GAAIpC,IAAI,CAACF,SAAS,CAACsC,CAAC,CAAE,IAAKT,UAAU,CAAC5B,KAAK,CAACqC,CAAC,CAAC,CAAE,EAAE,CAAC,CAACL,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;;;EAIxGM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEvC,KAAK,CAAC;EACzC,MAAM,IAAIwC,KAAK,CAAC,QAAQ,CAAC;AAC7B;AAEA,SAASC,eAAeA,CAACC,MAAc,EAAE1C,KAAmB;EAExD,IAAI,OAAO,IAAIA,KAAK,EAAE;IAClB,MAAM2B,KAAK,GAAG3B,KAAK,CAAC2B,KAAK;IAEzB,IAAIgB,GAAW;IACf,IAAIlE,OAAO,CAACkD,KAAK,EAAE,gBAAgB,CAAC,EAAE;MAClCgB,GAAG,GAAGf,UAAU,CAACO,MAAM,CAACS,MAAM,CAAC,EAAG,EAAEjB,KAAK,EAAE;QACvCkB,YAAY,EAAEC,SAAS;QAAEC,MAAM,EAAED,SAAS;QAAEE,IAAI,EAAEF;OACrD,CAAC,CAAC;KACN,MAAM;MACHH,GAAG,GAAGf,UAAU,CAACD,KAAK,CAAC;;IAG3B,OAAO;MAAEgB,GAAG;MAAE3C,KAAK,EAAE2B;IAAK,CAAE;;EAGhC,MAAMsB,MAAM,GAAGjD,KAAK,CAACiD,MAAM;EAC3B,OAAO;IAAEN,GAAG,EAAEf,UAAU,CAACqB,MAAM,CAAC;IAAEjD,KAAK,EAAEiD;EAAM,CAAE;AACrD;AAQA;AACA;AACA,SAASC,WAAWA,CAACC,MAAc,EAAEC,OAA2B;EAC5D,MAAMC,KAAK,GAAgD,IAAIC,GAAG,EAAE;EACpE,KAAK,MAAM;IAAEtD,KAAK;IAAE2C,GAAG;IAAEnC;EAAM,CAAE,IAAI4C,OAAO,EAAE;IAC1C,MAAMG,CAAC,GAAGF,KAAK,CAACG,GAAG,CAACb,GAAG,CAAC,IAAI;MAAE3C,KAAK;MAAEQ,MAAM,EAAE;IAAC,CAAE;IAChD+C,CAAC,CAAC/C,MAAM,IAAIA,MAAM;IAClB6C,KAAK,CAACI,GAAG,CAACd,GAAG,EAAEY,CAAC,CAAC;;EAGrB,IAAIG,IAAI,GAA0C,IAAI;EACtD,KAAK,MAAMC,CAAC,IAAIN,KAAK,CAACO,MAAM,EAAE,EAAE;IAC5B,IAAID,CAAC,CAACnD,MAAM,IAAI2C,MAAM,KAAK,CAACO,IAAI,IAAIC,CAAC,CAACnD,MAAM,GAAGkD,IAAI,CAAClD,MAAM,CAAC,EAAE;MACzDkD,IAAI,GAAGC,CAAC;;;EAIhB,IAAID,IAAI,EAAE;IAAE,OAAOA,IAAI,CAAC1D,KAAK;;EAE7B,OAAO8C,SAAS;AACpB;AAEA,SAASe,SAASA,CAACV,MAAc,EAAEC,OAA2B;EAC1D,IAAIU,YAAY,GAAG,CAAC;EAEpB,MAAMC,QAAQ,GAAkD,IAAIT,GAAG,EAAE;EACzE,IAAIU,SAAS,GAA4C,IAAI;EAE7D,MAAMJ,MAAM,GAAkB,EAAG;EACjC,KAAK,MAAM;IAAE5D,KAAK;IAAE2C,GAAG;IAAEnC;EAAM,CAAE,IAAI4C,OAAO,EAAE;IAC1C,IAAIpD,KAAK,YAAYwC,KAAK,EAAE;MACxB,MAAMyB,CAAC,GAAGF,QAAQ,CAACP,GAAG,CAACb,GAAG,CAAC,IAAI;QAAE3C,KAAK;QAAEQ,MAAM,EAAE;MAAC,CAAE;MACnDyD,CAAC,CAACzD,MAAM,IAAIA,MAAM;MAClBuD,QAAQ,CAACN,GAAG,CAACd,GAAG,EAAEsB,CAAC,CAAC;MAEpB,IAAID,SAAS,IAAI,IAAI,IAAIC,CAAC,CAACzD,MAAM,GAAGwD,SAAS,CAACxD,MAAM,EAAE;QAAEwD,SAAS,GAAGC,CAAC;;KACxE,MAAM;MACHL,MAAM,CAACM,IAAI,CAACrF,MAAM,CAACmB,KAAK,CAAC,CAAC;MAC1B8D,YAAY,IAAItD,MAAM;;;EAI9B,IAAIsD,YAAY,GAAGX,MAAM,EAAE;IACvB;IACA,IAAIa,SAAS,IAAIA,SAAS,CAACxD,MAAM,IAAI2C,MAAM,EAAE;MAAE,OAAOa,SAAS,CAAChE,KAAK;;IAErE;IACA,OAAO8C,SAAS;;EAGpB;EACAc,MAAM,CAACxB,IAAI,CAAC,CAAC+B,CAAC,EAAEC,CAAC,KAAOD,CAAC,GAAGC,CAAC,GAAI,CAAC,CAAC,GAAGA,CAAC,GAAGD,CAAC,GAAI,CAAC,GAAE,CAAE,CAAC;EAErD,MAAME,GAAG,GAAGjF,IAAI,CAACC,KAAK,CAACuE,MAAM,CAAC1E,MAAM,GAAG,CAAC,CAAC;EAEzC;EACA,IAAI0E,MAAM,CAAC1E,MAAM,GAAG,CAAC,EAAE;IAAE,OAAO0E,MAAM,CAACS,GAAG,CAAC;;EAE3C;EACA,OAAO,CAACT,MAAM,CAACS,GAAG,GAAG,CAAC,CAAC,GAAGT,MAAM,CAACS,GAAG,CAAC,GAAGzF,IAAI,IAAIE,IAAI;AACxD;AAEA,SAASwF,YAAYA,CAACnB,MAAc,EAAEC,OAA2B;EAC7D;EACA,MAAMH,MAAM,GAAGC,WAAW,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC3C,IAAIH,MAAM,KAAKH,SAAS,EAAE;IAAE,OAAOG,MAAM;;EAEzC;EACA,KAAK,MAAMU,CAAC,IAAIP,OAAO,EAAE;IACrB,IAAIO,CAAC,CAAC3D,KAAK,EAAE;MAAE,OAAO2D,CAAC,CAAC3D,KAAK;;;EAGjC;EACA,OAAO8C,SAAS;AACpB;AAEA,SAASyB,YAAYA,CAACpB,MAAc,EAAEC,OAA2B;EAC7D,IAAID,MAAM,KAAK,CAAC,EAAE;IAAE,OAAO3E,SAAS,CAASqF,SAAS,CAACV,MAAM,EAAEC,OAAO,CAAC,EAAE,WAAW,CAAC;;EAErF,MAAMC,KAAK,GAAoD,IAAIC,GAAG,EAAE;EACxE,MAAMkB,GAAG,GAAGA,CAACvB,MAAc,EAAEzC,MAAc,KAAI;IAC3C,MAAM+C,CAAC,GAAGF,KAAK,CAACG,GAAG,CAACP,MAAM,CAAC,IAAI;MAAEA,MAAM;MAAEzC,MAAM,EAAE;IAAC,CAAE;IACpD+C,CAAC,CAAC/C,MAAM,IAAIA,MAAM;IAClB6C,KAAK,CAACI,GAAG,CAACR,MAAM,EAAEM,CAAC,CAAC;EACxB,CAAC;EAED,KAAK,MAAM;IAAE/C,MAAM;IAAER;EAAK,CAAE,IAAIoD,OAAO,EAAE;IACrC,MAAMO,CAAC,GAAGnF,SAAS,CAACwB,KAAK,CAAC;IAC1BwE,GAAG,CAACb,CAAC,GAAG,CAAC,EAAEnD,MAAM,CAAC;IAClBgE,GAAG,CAACb,CAAC,EAAEnD,MAAM,CAAC;IACdgE,GAAG,CAACb,CAAC,GAAG,CAAC,EAAEnD,MAAM,CAAC;;EAGtB,IAAIiE,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAuB5B,SAAS;EAE9C,KAAK,MAAM;IAAEtC,MAAM;IAAEyC;EAAM,CAAE,IAAII,KAAK,CAACO,MAAM,EAAE,EAAE;IAC7C;IACA;IACA;IACA,IAAIpD,MAAM,IAAI2C,MAAM,KAAK3C,MAAM,GAAGiE,UAAU,IAAKC,UAAU,IAAI,IAAI,IAAIlE,MAAM,KAAKiE,UAAU,IAAIxB,MAAM,GAAGyB,UAAW,CAAC,EAAE;MACnHD,UAAU,GAAGjE,MAAM;MACnBkE,UAAU,GAAGzB,MAAM;;;EAI3B,OAAOyB,UAAU;AACrB;AAEA;;;;;;;AAOA,OAAM,MAAOC,gBAAiB,SAAQjG,gBAAgB;EAElD;;;;EAISyE,MAAM;EAEf;;;EAGSyB,WAAW;EAEpB;;;EAGSC,YAAY;EAEZ,CAAAC,OAAQ;EAEjB,CAAAC,MAAO;EACP,CAAAC,kBAAmB;EAEnB;;;;;;;EAOAC,YAAYC,SAA2D,EAAEC,OAAoB,EAAEC,OAAiC;IAC5H,KAAK,CAACD,OAAO,EAAEC,OAAO,CAAC;IAEvB,IAAI,CAAC,CAAAN,OAAQ,GAAGI,SAAS,CAACnD,GAAG,CAAEsD,CAAC,IAAI;MAChC,IAAIA,CAAC,YAAY3G,gBAAgB,EAAE;QAC/B,OAAOyD,MAAM,CAACS,MAAM,CAAC;UAAEnB,QAAQ,EAAE4D;QAAC,CAAE,EAAEhF,aAAa,EAAEI,YAAY,CAAE;OACtE,MAAM;QACH,OAAO0B,MAAM,CAACS,MAAM,CAAC,EAAG,EAAEvC,aAAa,EAAEgF,CAAC,EAAE5E,YAAY,CAAE;;IAElE,CAAC,CAAC;IAEF,IAAI,CAAC,CAAAsE,MAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC,CAAAC,kBAAmB,GAAG,IAAI;IAE/B,IAAII,OAAO,IAAIA,OAAO,CAACjC,MAAM,IAAI,IAAI,EAAE;MACnC,IAAI,CAACA,MAAM,GAAGiC,OAAO,CAACjC,MAAM;KAC/B,MAAM;MACH,IAAI,CAACA,MAAM,GAAG/D,IAAI,CAACkG,IAAI,CAAC,IAAI,CAAC,CAAAR,OAAQ,CAACS,MAAM,CAAC,CAACC,KAAK,EAAEhE,MAAM,KAAI;QAC3DgE,KAAK,IAAIhE,MAAM,CAAChB,MAAM;QACtB,OAAOgF,KAAK;MAChB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;;IAGd,IAAI,CAACZ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,YAAY,GAAG,CAAC;IAErBvG,cAAc,CAAC,IAAI,CAAC6E,MAAM,IAAI,IAAI,CAAC,CAAA2B,OAAQ,CAACS,MAAM,CAAC,CAACpB,CAAC,EAAEsB,CAAC,KAAMtB,CAAC,GAAGsB,CAAC,CAACjF,MAAO,EAAE,CAAC,CAAC,EAC3E,+BAA+B,EAAE,QAAQ,EAAE,IAAI,CAAC2C,MAAM,CAAC;EAC/D;EAEA,IAAIuC,eAAeA,CAAA;IACf,OAAO,IAAI,CAAC,CAAAZ,OAAQ,CAAC/C,GAAG,CAAE0D,CAAC,IAAI;MAC3B,MAAMxC,MAAM,GAAQd,MAAM,CAACS,MAAM,CAAC,EAAG,EAAE6C,CAAC,CAAC;MACzC,KAAK,MAAMvF,GAAG,IAAI+C,MAAM,EAAE;QACtB,IAAI/C,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAAE,OAAO+C,MAAM,CAAC/C,GAAG,CAAC;;;MAE5C,OAAO+C,MAAM;IACjB,CAAC,CAAC;EACN;EAEA,MAAM0C,cAAcA,CAAA;IAChB,OAAOhH,OAAO,CAACiH,IAAI,CAACrH,SAAS,CAAC,MAAM,IAAI,CAACsH,QAAQ,CAAC;MAAEnD,MAAM,EAAE;IAAS,CAAE,CAAC,CAAC,CAAC;EAC9E;EAEA;EACA;EACA;EACA;EAEA;;;EAGA,MAAMoD,iBAAiBA,CAACrE,QAA0B,EAAEsE,GAAyB;IACzE,QAAQA,GAAG,CAACrD,MAAM;MACd,KAAK,sBAAsB;QACvB,OAAO,MAAMjB,QAAQ,CAACuE,oBAAoB,CAACD,GAAG,CAACE,iBAAiB,CAAC;MACrE,KAAK,MAAM;QACP,OAAO,MAAMxE,QAAQ,CAACyE,IAAI,CAAC/D,MAAM,CAACS,MAAM,CAAC,EAAG,EAAEmD,GAAG,CAACI,WAAW,EAAE;UAAEC,QAAQ,EAAEL,GAAG,CAACK;QAAQ,CAAE,CAAC,CAAC;MAC/F,KAAK,SAAS;QACV,OAAO,CAAC,MAAM3E,QAAQ,CAAC4E,UAAU,EAAE,EAAEC,OAAO;MAChD,KAAK,aAAa;QACd,OAAO,MAAM7E,QAAQ,CAAC8E,WAAW,CAACR,GAAG,CAACI,WAAW,CAAC;MACtD,KAAK,YAAY;QACb,OAAO,MAAM1E,QAAQ,CAAC+E,UAAU,CAACT,GAAG,CAACU,OAAO,EAAEV,GAAG,CAACK,QAAQ,CAAC;MAC/D,KAAK,UAAU;QAAE;UACb,MAAMM,KAAK,GAAI,WAAW,IAAIX,GAAG,GAAIA,GAAG,CAACY,SAAS,GAAEZ,GAAG,CAACK,QAAQ;UAChE,OAAO,MAAM3E,QAAQ,CAACmF,QAAQ,CAACF,KAAK,EAAEX,GAAG,CAACc,mBAAmB,CAAC;;MAElE,KAAK,gBAAgB;QACjB,OAAO,MAAMpF,QAAQ,CAACC,cAAc,EAAE;MAC1C,KAAK,SAAS;QACV,OAAO,MAAMD,QAAQ,CAACqF,OAAO,CAACf,GAAG,CAACU,OAAO,EAAEV,GAAG,CAACK,QAAQ,CAAC;MAC5D,KAAK,aAAa;QACd,OAAO,CAAC,MAAM3E,QAAQ,CAACsF,UAAU,EAAE,EAAEC,QAAQ;MACjD,KAAK,gBAAgB;QACjB,OAAO,CAAC,MAAMvF,QAAQ,CAACsF,UAAU,EAAE,EAAEE,oBAAoB;MAC7D,KAAK,SAAS;QACV,OAAO,MAAMxF,QAAQ,CAACyF,OAAO,CAACnB,GAAG,CAACoB,MAAM,CAAC;MAC7C,KAAK,YAAY;QACb,OAAO,MAAM1F,QAAQ,CAAC2F,UAAU,CAACrB,GAAG,CAACU,OAAO,EAAEV,GAAG,CAACsB,QAAQ,EAAEtB,GAAG,CAACK,QAAQ,CAAC;MAC7E,KAAK,gBAAgB;QACjB,OAAO,MAAM3E,QAAQ,CAAC6F,cAAc,CAACvB,GAAG,CAACwB,IAAI,CAAC;MAClD,KAAK,qBAAqB;QACtB,OAAO,MAAM9F,QAAQ,CAAC+F,mBAAmB,CAACzB,GAAG,CAACU,OAAO,EAAEV,GAAG,CAACK,QAAQ,CAAC;MACxE,KAAK,uBAAuB;QACxB,OAAO,MAAM3E,QAAQ,CAACgG,qBAAqB,CAAC1B,GAAG,CAACwB,IAAI,CAAC;MACzD,KAAK,sBAAsB;QACvB,OAAO,MAAM9F,QAAQ,CAACiG,oBAAoB,CAAC3B,GAAG,CAACwB,IAAI,CAAC;;EAEhE;EAEA;EACA;EACA,CAAAI,aAAcC,CAACC,OAAyB;IACpC;IACA;IACA;IAEA,MAAM/C,OAAO,GAAGjD,KAAK,CAAC+D,IAAI,CAACiC,OAAO,CAAC,CAAC9F,GAAG,CAAE4B,CAAC,IAAKA,CAAC,CAACnC,MAAM,CAAC;IAExD;IACA,MAAMsG,UAAU,GAAG,IAAI,CAAC,CAAAhD,OAAQ,CAACiD,KAAK,EAAE;IACxChJ,OAAO,CAAC+I,UAAU,CAAC;IACnBA,UAAU,CAAC1F,IAAI,CAAC,CAAC+B,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAAC5D,QAAQ,GAAG6D,CAAC,CAAC7D,QAAS,CAAC;IAEpD,KAAK,MAAMiB,MAAM,IAAIsG,UAAU,EAAE;MAC7B,IAAItG,MAAM,CAACH,eAAe,EAAE;QAAE;;MAC9B,IAAIyD,OAAO,CAACkD,OAAO,CAACxG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;QAAE,OAAOA,MAAM;;;IAGvD,OAAO,IAAI;EACf;EAEA;EACA,CAAAyG,SAAUC,CAACL,OAAyB,EAAE9B,GAAyB;IAC3D,MAAMvE,MAAM,GAAG,IAAI,CAAC,CAAAmG,aAAc,CAACE,OAAO,CAAC;IAE3C;IACA,IAAIrG,MAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEjC;IACA,MAAM2G,MAAM,GAAgB;MACxB3G,MAAM;MAAEyB,MAAM,EAAE,IAAI;MAAEmF,OAAO,EAAE,KAAK;MACpCC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE;KAC3B;IAED,MAAMC,GAAG,GAAG1I,OAAO,EAAE;IAErB;IACAsI,MAAM,CAACE,OAAO,GAAG,CAAC,YAAW;MACzB,IAAI;QACA7G,MAAM,CAACb,QAAQ,EAAE;QACjB,MAAMsC,MAAM,GAAG,MAAM,IAAI,CAAC6C,iBAAiB,CAACtE,MAAM,CAACC,QAAQ,EAAEsE,GAAG,CAAC;QACjEoC,MAAM,CAAClF,MAAM,GAAG;UAAEA;QAAM,CAAE;OAC7B,CAAC,OAAOtB,KAAU,EAAE;QACjBH,MAAM,CAACX,cAAc,EAAE;QACvBsH,MAAM,CAAClF,MAAM,GAAG;UAAEtB;QAAK,CAAE;;MAG7B,MAAM6G,EAAE,GAAI3I,OAAO,EAAE,GAAG0I,GAAI;MAC5B/G,MAAM,CAACJ,UAAU,IAAIoH,EAAE;MAEvBhH,MAAM,CAACR,eAAe,GAAG,IAAI,GAAGQ,MAAM,CAACR,eAAe,GAAG,IAAI,GAAGwH,EAAE;MAElEL,MAAM,CAACE,OAAO,GAAG,IAAI;IACzB,CAAC,EAAC,CAAE;IAEJ;IACA;IACAF,MAAM,CAACG,OAAO,GAAG,CAAC,YAAW;MACzB,MAAM9I,KAAK,CAACgC,MAAM,CAAClB,YAAY,CAAC;MAChC6H,MAAM,CAACG,OAAO,GAAG,IAAI;IACzB,CAAC,EAAC,CAAE;IAEJT,OAAO,CAACrD,GAAG,CAAC2D,MAAM,CAAC;IACnB,OAAOA,MAAM;EACjB;EAEA;EACA;EACA,MAAM,CAAAM,WAAYC,CAAA;IACd,IAAID,WAAW,GAAG,IAAI,CAAC,CAAAzD,kBAAmB;IAC1C,IAAI,CAACyD,WAAW,EAAE;MACd,MAAME,QAAQ,GAAwB,EAAG;MACzC,IAAI,CAAC,CAAA7D,OAAQ,CAAC8D,OAAO,CAAEpH,MAAM,IAAI;QAC7BmH,QAAQ,CAACzE,IAAI,CAAC,CAAC,YAAW;UACtB,MAAM3C,WAAW,CAACC,MAAM,EAAE,CAAC,CAAC;UAC5B,IAAI,CAACA,MAAM,CAACH,eAAe,EAAE;YACzBG,MAAM,CAACN,QAAQ,GAAG,MAAMM,MAAM,CAACC,QAAQ,CAAC4E,UAAU,EAAE;;QAE5D,CAAC,EAAC,CAAE,CAAC;MACT,CAAC,CAAC;MAEF,IAAI,CAAC,CAAArB,kBAAmB,GAAGyD,WAAW,GAAG,CAAC,YAAW;QACjD;QACA,MAAM/I,OAAO,CAACmJ,GAAG,CAACF,QAAQ,CAAC;QAE3B;QACA,IAAIrC,OAAO,GAAkB,IAAI;QACjC,KAAK,MAAM9E,MAAM,IAAI,IAAI,CAAC,CAAAsD,OAAQ,EAAE;UAChC,IAAItD,MAAM,CAACH,eAAe,EAAE;YAAE;;UAC9B,MAAM8D,OAAO,GAAa3D,MAAM,CAACN,QAAS;UAC1C,IAAIoF,OAAO,IAAI,IAAI,EAAE;YACjBA,OAAO,GAAGnB,OAAO,CAACmB,OAAO;WAC5B,MAAM,IAAInB,OAAO,CAACmB,OAAO,KAAKA,OAAO,EAAE;YACpCjI,MAAM,CAAC,KAAK,EAAE,4CAA4C,EAAE,uBAAuB,EAAE;cACjFyK,SAAS,EAAE;aACd,CAAC;;;MAGd,CAAC,EAAC,CAAE;;IAGR,MAAML,WAAW;EACrB;EAGA,MAAM,CAAAvF,WAAY6F,CAAClB,OAAyB,EAAE9B,GAAyB;IACnE;IACA,MAAM3C,OAAO,GAAuB,EAAG;IACvC,KAAK,MAAM+E,MAAM,IAAIN,OAAO,EAAE;MAC1B,IAAIM,MAAM,CAAClF,MAAM,IAAI,IAAI,EAAE;QACvB,MAAM;UAAEN,GAAG;UAAE3C;QAAK,CAAE,GAAGyC,eAAe,CAACsD,GAAG,CAACrD,MAAM,EAAEyF,MAAM,CAAClF,MAAM,CAAC;QACjEG,OAAO,CAACc,IAAI,CAAC;UAAEvB,GAAG;UAAE3C,KAAK;UAAEQ,MAAM,EAAE2H,MAAM,CAAC3G,MAAM,CAAChB;QAAM,CAAE,CAAC;;;IAIlE;IACA,IAAI4C,OAAO,CAACmC,MAAM,CAAC,CAACpB,CAAC,EAAER,CAAC,KAAMQ,CAAC,GAAGR,CAAC,CAACnD,MAAO,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC2C,MAAM,EAAE;MAC3D,OAAOL,SAAS;;IAGpB,QAAQiD,GAAG,CAACrD,MAAM;MACd,KAAK,gBAAgB;QAAE;UACnB;UACA,IAAI,IAAI,CAAC,CAAAqC,MAAO,KAAK,CAAC,CAAC,EAAE;YACrB,IAAI,CAAC,CAAAA,MAAO,GAAG3F,IAAI,CAACkG,IAAI,CAAC9G,SAAS,CAASqF,SAAS,CAAC,IAAI,CAACV,MAAM,EAAE,IAAI,CAAC,CAAA2B,OAAQ,CAACqC,MAAM,CAAE1B,CAAC,IAAM,CAACA,CAAC,CAACpE,eAAgB,CAAC,CAACU,GAAG,CAAE0D,CAAC,KAAM;cAC5HzF,KAAK,EAAEyF,CAAC,CAAC/E,WAAW;cACpBiC,GAAG,EAAEnE,SAAS,CAACiH,CAAC,CAAC/E,WAAW,CAAC,CAACN,QAAQ,EAAE;cACxCI,MAAM,EAAEiF,CAAC,CAACjF;aACb,CAAC,CAAC,CAAC,CAAC,CAAC;;UAGV;UACA;UACA,MAAMwI,IAAI,GAAGzE,YAAY,CAAC,IAAI,CAACpB,MAAM,EAAEC,OAAO,CAAC;UAC/C,IAAI4F,IAAI,KAAKlG,SAAS,EAAE;YAAE,OAAOA,SAAS;;UAC1C,IAAIkG,IAAI,GAAG,IAAI,CAAC,CAAAjE,MAAO,EAAE;YAAE,IAAI,CAAC,CAAAA,MAAO,GAAGiE,IAAI;;UAC9C,OAAO,IAAI,CAAC,CAAAjE,MAAO;;MAGvB,KAAK,aAAa;MAClB,KAAK,gBAAgB;MACrB,KAAK,aAAa;QACd,OAAOlB,SAAS,CAAC,IAAI,CAACV,MAAM,EAAEC,OAAO,CAAC;MAE1C,KAAK,UAAU;QACX;QACA;QACA,IAAI,UAAU,IAAI2C,GAAG,IAAIA,GAAG,CAACK,QAAQ,KAAK,SAAS,EAAE;UACjD,OAAO9B,YAAY,CAAC,IAAI,CAACnB,MAAM,EAAEC,OAAO,CAAC;;QAE7C,OAAOF,WAAW,CAAC,IAAI,CAACC,MAAM,EAAEC,OAAO,CAAC;MAE5C,KAAK,MAAM;MACX,KAAK,SAAS;MACd,KAAK,YAAY;MACjB,KAAK,qBAAqB;MAC1B,KAAK,SAAS;MACd,KAAK,YAAY;MACjB,KAAK,gBAAgB;MACrB,KAAK,uBAAuB;MAC5B,KAAK,SAAS;QACV,OAAOF,WAAW,CAAC,IAAI,CAACC,MAAM,EAAEC,OAAO,CAAC;MAE5C,KAAK,sBAAsB;QACvB,OAAOkB,YAAY,CAAC,IAAI,CAACnB,MAAM,EAAEC,OAAO,CAAC;;IAGjD/E,MAAM,CAAC,KAAK,EAAE,oBAAoB,EAAE,uBAAuB,EAAE;MACzDyK,SAAS,EAAE,YAAa/I,SAAS,CAAOgG,GAAI,CAACrD,MAAM,CAAE;KACxD,CAAC;EACN;EAEA,MAAM,CAAAuG,aAAcC,CAACrB,OAAyB,EAAE9B,GAAyB;IACrE,IAAI8B,OAAO,CAACsB,IAAI,KAAK,CAAC,EAAE;MAAE,MAAM,IAAI3G,KAAK,CAAC,cAAc,CAAC;;IAEzD;IACA;IACA,MAAM4G,WAAW,GAAyB,EAAG;IAE7C,IAAIC,UAAU,GAAG,CAAC;IAClB,KAAK,MAAMlB,MAAM,IAAIN,OAAO,EAAE;MAE1B;MACA,IAAIM,MAAM,CAACE,OAAO,EAAE;QAChBe,WAAW,CAAClF,IAAI,CAACiE,MAAM,CAACE,OAAO,CAAC;;MAGpC;MACA,IAAIF,MAAM,CAACG,OAAO,EAAE;QAChBc,WAAW,CAAClF,IAAI,CAACiE,MAAM,CAACG,OAAO,CAAC;QAChC;;MAGJ;MACA,IAAIH,MAAM,CAACC,OAAO,EAAE;QAAE;;MAEtB;MACAD,MAAM,CAACC,OAAO,GAAG,IAAI;MACrBiB,UAAU,EAAE;;IAGhB;IACA,MAAMrJ,KAAK,GAAG,MAAM,IAAI,CAAC,CAAAkD,WAAY,CAAC2E,OAAO,EAAE9B,GAAG,CAAC;IACnD,IAAI/F,KAAK,KAAK8C,SAAS,EAAE;MACrB,IAAI9C,KAAK,YAAYwC,KAAK,EAAE;QAAE,MAAMxC,KAAK;;MACzC,OAAOA,KAAK;;IAGhB;IACA;IACA,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,UAAU,EAAEpK,CAAC,EAAE,EAAE;MACjC,IAAI,CAAC,CAAAgJ,SAAU,CAACJ,OAAO,EAAE9B,GAAG,CAAC;;IAGjC;IAEA1H,MAAM,CAAC+K,WAAW,CAAClK,MAAM,GAAG,CAAC,EAAE,gBAAgB,EAAE,cAAc,EAAE;MAC7DoK,OAAO,EAAE,eAAe;MACxBtG,IAAI,EAAE;QAAEsG,OAAO,EAAEvD,GAAG;QAAE3C,OAAO,EAAEvB,KAAK,CAAC+D,IAAI,CAACiC,OAAO,CAAC,CAAC9F,GAAG,CAAE4B,CAAC,IAAK5D,SAAS,CAAC4D,CAAC,CAACV,MAAM,CAAC;MAAC;KACrF,CAAC;IAEF;IACA,MAAMvD,OAAO,CAAC6J,IAAI,CAACH,WAAW,CAAC;IAE/B;IACA;IACA,OAAO,MAAM,IAAI,CAAC,CAAAH,aAAc,CAACpB,OAAO,EAAE9B,GAAG,CAAC;EAClD;EAEA,MAAMF,QAAQA,CAAUE,GAAyB;IAC7C;IACA;IACA;IACA,IAAIA,GAAG,CAACrD,MAAM,KAAK,sBAAsB,EAAE;MACvC;MACA;MACA,MAAMU,OAAO,GAA8B,IAAI,CAAC,CAAA0B,OAAQ,CAAC/C,GAAG,CAAE0D,CAAC,IAAK,IAAI,CAAC;MACzE,MAAM+D,UAAU,GAAG,IAAI,CAAC,CAAA1E,OAAQ,CAAC/C,GAAG,CAAC,OAAO;QAAEN,QAAQ;QAAEjB;MAAM,CAAE,EAAEiJ,KAAK,KAAI;QACvE,IAAI;UACA,MAAMxG,MAAM,GAAG,MAAMxB,QAAQ,CAACoE,QAAQ,CAACE,GAAG,CAAC;UAC3C3C,OAAO,CAACqG,KAAK,CAAC,GAAGtH,MAAM,CAACS,MAAM,CAACH,eAAe,CAACsD,GAAG,CAACrD,MAAM,EAAE;YAAEO;UAAM,CAAE,CAAC,EAAE;YAAEzC;UAAM,CAAE,CAAC;SACtF,CAAC,OAAOmB,KAAU,EAAE;UACjByB,OAAO,CAACqG,KAAK,CAAC,GAAGtH,MAAM,CAACS,MAAM,CAACH,eAAe,CAACsD,GAAG,CAACrD,MAAM,EAAE;YAAEf;UAAK,CAAE,CAAC,EAAE;YAAEnB;UAAM,CAAE,CAAC;;MAE1F,CAAC,CAAC;MAEF;MACA,OAAO,IAAI,EAAE;QACT;QACA,MAAMkJ,IAAI,GAAetG,OAAO,CAAC+D,MAAM,CAAExD,CAAC,IAAMA,CAAC,IAAI,IAAK,CAAC;QAC3D,KAAK,MAAM;UAAE3D;QAAK,CAAE,IAAI0J,IAAI,EAAE;UAC1B,IAAI,EAAE1J,KAAK,YAAYwC,KAAK,CAAC,EAAE;YAAE,OAAOxC,KAAK;;;QAGjD;QACA;QACA;QACA;QACA;QACA;QACA,MAAMiD,MAAM,GAAGC,WAAW,CAAC,IAAI,CAACC,MAAM,EAAcC,OAAO,CAAC+D,MAAM,CAAExD,CAAC,IAAMA,CAAC,IAAI,IAAK,CAAC,CAAC;QACvF,IAAIlF,OAAO,CAACwE,MAAM,EAAE,oBAAoB,CAAC,EAAE;UACvC,MAAMA,MAAM;;QAGhB;QACA,MAAM0G,OAAO,GAAGH,UAAU,CAACrC,MAAM,CAAC,CAAC/C,CAAC,EAAEnF,CAAC,KAAMmE,OAAO,CAACnE,CAAC,CAAC,IAAI,IAAK,CAAC;QACjE,IAAI0K,OAAO,CAACzK,MAAM,KAAK,CAAC,EAAE;UAAE;;QAC5B,MAAMQ,OAAO,CAAC6J,IAAI,CAACI,OAAO,CAAC;;MAG/B;MACA;MACA,MAAM1G,MAAM,GAAGqB,YAAY,CAAC,IAAI,CAACnB,MAAM,EAAcC,OAAO,CAAC;MAC7D/E,MAAM,CAAC4E,MAAM,KAAKH,SAAS,EAAE,4BAA4B,EAAE,cAAc,EAAE;QACvEwG,OAAO,EAAE,eAAe;QACxBtG,IAAI,EAAE;UAAEsG,OAAO,EAAEvD,GAAG;UAAE3C,OAAO,EAAEA,OAAO,CAACrB,GAAG,CAAChC,SAAS;QAAC;OACxD,CAAC;MACF,IAAIkD,MAAM,YAAYT,KAAK,EAAE;QAAE,MAAMS,MAAM;;MAC3C,OAAOA,MAAM;;IAGjB,MAAM,IAAI,CAAC,CAAAwF,WAAY,EAAE;IAEzB;IACA,MAAMZ,OAAO,GAAqB,IAAI+B,GAAG,EAAE;IAC3C,IAAIC,cAAc,GAAG,CAAC;IACtB,OAAO,IAAI,EAAE;MACT,MAAM1B,MAAM,GAAG,IAAI,CAAC,CAAAF,SAAU,CAACJ,OAAO,EAAE9B,GAAG,CAAC;MAC5C,IAAIoC,MAAM,IAAI,IAAI,EAAE;QAAE;;MACtB0B,cAAc,IAAI1B,MAAM,CAAC3G,MAAM,CAAChB,MAAM;MACtC,IAAIqJ,cAAc,IAAI,IAAI,CAAC1G,MAAM,EAAE;QAAE;;;IAGzC,MAAMF,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAgG,aAAc,CAACpB,OAAO,EAAE9B,GAAG,CAAC;IAEtD;IACA;IACA,KAAK,MAAMoC,MAAM,IAAIN,OAAO,EAAE;MAC1B,IAAIM,MAAM,CAACE,OAAO,IAAIF,MAAM,CAAClF,MAAM,IAAI,IAAI,EAAE;QACzCkF,MAAM,CAAC3G,MAAM,CAACZ,aAAa,EAAE;;;IAIrC,OAAOqC,MAAM;EACjB;EAEA,MAAM6G,OAAOA,CAAA;IACT,KAAK,MAAM;MAAErI;IAAQ,CAAE,IAAI,IAAI,CAAC,CAAAqD,OAAQ,EAAE;MACtCrD,QAAQ,CAACqI,OAAO,EAAE;;IAEtB,KAAK,CAACA,OAAO,EAAE;EACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}