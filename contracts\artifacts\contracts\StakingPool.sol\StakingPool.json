{"_format": "hh-sol-artifact-1", "contractName": "StakingPool", "sourceName": "contracts/StakingPool.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_cqtToken", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RewardsClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Unstaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "XPBoostActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}], "name": "XPBoostDeactivated", "type": "event"}, {"inputs": [], "name": "LOCK_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MINIMUM_STAKE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "annualRewardRate", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "calculatePendingRewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "cqtToken", "outputs": [{"internalType": "contract CQTToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyUnstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getPoolStats", "outputs": [{"internalType": "uint256", "name": "_totalStaked", "type": "uint256"}, {"internalType": "uint256", "name": "_totalRewardsDistributed", "type": "uint256"}, {"internalType": "uint256", "name": "_currentAPY", "type": "uint256"}, {"internalType": "uint256", "name": "_poolBalance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "getStakeInfo", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "stakeTime", "type": "uint256"}, {"internalType": "uint256", "name": "pendingRewards", "type": "uint256"}, {"internalType": "uint256", "name": "accumulatedRewards", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "bool", "name": "xpBoost", "type": "bool"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_user", "type": "address"}], "name": "hasStakingBoost", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "hasXPBoost", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakes", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "stakeTime", "type": "uint256"}, {"internalType": "uint256", "name": "lastRewardTime", "type": "uint256"}, {"internalType": "uint256", "name": "accumulatedRewards", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalRewardsDistributed", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_newRate", "type": "uint256"}], "name": "updateRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b50600436106101585760003560e01c8063715018a6116100c3578063a694fc3a1161007c578063a694fc3a146102fc578063bbb851881461030f578063c345315314610337578063e489a70e14610383578063ee172546146103a6578063f2fde38b146103af57600080fd5b8063715018a6146102b75780637589cf2f146102bf578063817b1cd2146102c75780638456cb59146102d05780638da5cb5b146102d85780639ef3a261146102e957600080fd5b806334aab0641161011557806334aab0641461021b578063372500ab146102465780633f4ba83a1461024e578063479b8c6d146102565780635312ea8e146102925780635c975abb146102a557600080fd5b806308dbbb031461015d578063097aad101461018057806316934fc4146101935780631715d015146101fe5780631820cabb146102075780632def662014610211575b600080fd5b61016d68056bc75e2d6310000081565b6040519081526020015b60405180910390f35b61016d61018e366004611023565b6103c2565b6101d46101a1366004611023565b60046020819052600091825260409091208054600182015460028301546003840154939094015491939092909160ff1685565b6040805195865260208601949094529284019190915260608301521515608082015260a001610177565b61016d60035481565b61016d62093a8081565b61021961048e565b005b60025461022e906001600160a01b031681565b6040516001600160a01b039091168152602001610177565b610219610656565b6102196106a3565b610282610264366004611023565b6001600160a01b031660009081526005602052604090205460ff1690565b6040519015158152602001610177565b6102196102a0366004611053565b6106b3565b600054600160a01b900460ff16610282565b610219610805565b610219610817565b61016d60065481565b610219610879565b6000546001600160a01b031661022e565b6102196102f7366004611053565b610889565b61021961030a366004611053565b6108d8565b610317610bdc565b604080519485526020850193909352918301526060820152608001610177565b61034a610345366004611023565b610c6e565b60408051978852602088019690965294860193909352606085019190915215156080840152151560a083015260c082015260e001610177565b610282610391366004611023565b60056020526000908152604090205460ff1681565b61016d60075481565b6102196103bd366004611023565b610d30565b6001600160a01b0381166000908152600460208181526040808420815160a08101835281548152600182015493810193909352600281015491830191909152600381015460608301529091015460ff1615801560808301528061042457508051155b156104325750600092915050565b60008160400151426104449190611082565b90506000612710600354846000015161045d919061109b565b61046791906110b2565b905060006301e1338061047a848461109b565b61048491906110b2565b9695505050505050565b610496610d6b565b3360009081526004602081905260409091209081015460ff166104d45760405162461bcd60e51b81526004016104cb906110d4565b60405180910390fd5b62093a8081600101546104e791906110fd565b42101561052e5760405162461bcd60e51b815260206004820152601560248201527414dd185ad9481a5cc81cdd1a5b1b081b1bd8dad959605a1b60448201526064016104cb565b805461053933610d95565b60048201805460ff1916905560008083556006805483929061055c908490611082565b909155505033600081815260056020526040808220805460ff19169055517f79e4b280b7571de34f7c60d8687ec328685adb33bde8b1056983d60c1ce2be2e9190a260025460405163a9059cbb60e01b8152336004820152602481018390526001600160a01b039091169063a9059cbb906044016020604051808303816000875af11580156105ef573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106139190611110565b5060405181815233907f0f5bb82176feb1b5e747e28471aa92156a04d9f3ab9f45f28e2d704232b93f759060200160405180910390a2505061065460018055565b565b61065e610d6b565b336000908152600460208190526040909120015460ff166106915760405162461bcd60e51b81526004016104cb906110d4565b61069a33610d95565b61065460018055565b6106ab610eb9565b610654610ee6565b6106bb610eb9565b6002546040516370a0823160e01b81523060048201526001600160a01b03909116906370a0823190602401602060405180830381865afa158015610703573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906107279190611132565b81111561076d5760405162461bcd60e51b8152602060048201526014602482015273496e73756666696369656e742062616c616e636560601b60448201526064016104cb565b6002546001600160a01b031663a9059cbb6107906000546001600160a01b031690565b6040516001600160e01b031960e084901b1681526001600160a01b039091166004820152602481018490526044016020604051808303816000875af11580156107dd573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906108019190611110565b5050565b61080d610eb9565b6106546000610f3b565b61081f610d6b565b3360009081526004602081905260409091209081015460ff166108545760405162461bcd60e51b81526004016104cb906110d4565b805460048201805460ff1916905560008083556006805483929061055c908490611082565b610881610eb9565b610654610f8b565b610891610eb9565b6113888111156108d35760405162461bcd60e51b815260206004820152600d60248201526c0a4c2e8ca40e8dede40d0d2ced609b1b60448201526064016104cb565b600355565b6108e0610fce565b6108e8610d6b565b68056bc75e2d631000008110156109415760405162461bcd60e51b815260206004820152601a60248201527f416d6f756e742062656c6f77206d696e696d756d207374616b6500000000000060448201526064016104cb565b6002546040516370a0823160e01b815233600482015282916001600160a01b0316906370a0823190602401602060405180830381865afa158015610989573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906109ad9190611132565b10156109f25760405162461bcd60e51b8152602060048201526014602482015273496e73756666696369656e742062616c616e636560601b60448201526064016104cb565b336000908152600460208190526040909120015460ff1615610a4157610a1733610d95565b3360009081526004602052604081208054839290610a369084906110fd565b90915550610aa89050565b6040805160a0810182528281524260208083018281528385019283526000606085018181526001608087018181523384526004958690529790922095518655915190850155915160028401559051600383015591519101805460ff19169115159190911790555b8060066000828254610aba91906110fd565b90915550506002546040516323b872dd60e01b8152336004820152306024820152604481018390526001600160a01b03909116906323b872dd906064016020604051808303816000875af1158015610b16573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610b3a9190611110565b5033600090815260046020526040902054683635c9adc5dea0000011610b9b5733600081815260056020526040808220805460ff19166001179055517fc90ea9466c3178d695308f186d8b7a4534189bb114df18fd519bdb76136a22599190a25b60405181815233907f9e71bc8eea02a63969f509818f2dafb9254532904319f9dbda79b67bd34a5f3d9060200160405180910390a2610bd960018055565b50565b6006546007546003546002546040516370a0823160e01b81523060048201526000948594859485949293919290916001600160a01b03909116906370a0823190602401602060405180830381865afa158015610c3c573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610c609190611132565b935093509350935090919293565b6001600160a01b0381166000908152600460208181526040808420815160a081018352815480825260018301549482018590526002830154938201939093526003820154606082015293015460ff161515608084015283928392839283928392839290610cda8b6103c2565b606084015160808501516001600160a01b038e166000908152600560209081526040909120549087015160ff90911690610d189062093a80906110fd565b959f949e50929c50909a509850965090945092505050565b610d38610eb9565b6001600160a01b038116610d6257604051631e4fbdf760e01b8152600060048201526024016104cb565b610bd981610f3b565b600260015403610d8e57604051633ee5aeb560e01b815260040160405180910390fd5b6002600155565b6001600160a01b038116600090815260046020526040812090610db7836103c2565b90508015610eb45780826003016000828254610dd391906110fd565b909155505042600283015560078054829190600090610df39084906110fd565b909155505060025460405163a9059cbb60e01b81526001600160a01b038581166004830152602482018490529091169063a9059cbb906044016020604051808303816000875af1158015610e4b573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610e6f9190611110565b50826001600160a01b03167ffc30cddea38e2bf4d6ea7d3f9ed3b6ad7f176419f4963bd81318067a4aee73fe82604051610eab91815260200190565b60405180910390a25b505050565b6000546001600160a01b031633146106545760405163118cdaa760e01b81523360048201526024016104cb565b610eee610ff9565b6000805460ff60a01b191690557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a1565b600080546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b610f93610fce565b6000805460ff60a01b1916600160a01b1790557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258610f1e3390565b600054600160a01b900460ff16156106545760405163d93c066560e01b815260040160405180910390fd5b600054600160a01b900460ff1661065457604051638dfc202b60e01b815260040160405180910390fd5b60006020828403121561103557600080fd5b81356001600160a01b038116811461104c57600080fd5b9392505050565b60006020828403121561106557600080fd5b5035919050565b634e487b7160e01b600052601160045260246000fd5b818103818111156110955761109561106c565b92915050565b80820281158282048414176110955761109561106c565b6000826110cf57634e487b7160e01b600052601260045260246000fd5b500490565b6020808252600f908201526e4e6f20616374697665207374616b6560881b604082015260600190565b808201808211156110955761109561106c565b60006020828403121561112257600080fd5b8151801515811461104c57600080fd5b60006020828403121561114457600080fd5b505191905056fea264697066735822122015538b8c865c0493eb84b92776aaa284e798cfa037c0871e0c79e674ec51446264736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}