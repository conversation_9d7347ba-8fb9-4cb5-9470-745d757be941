{"ast": null, "code": "import { defineProperties } from \"../utils/index.js\";\n/**\n *  A Wordlist represents a collection of language-specific\n *  words used to encode and devoce [[link-bip-39]] encoded data\n *  by mapping words to 11-bit values and vice versa.\n */\nexport class Wordlist {\n  locale;\n  /**\n   *  Creates a new Wordlist instance.\n   *\n   *  Sub-classes MUST call this if they provide their own constructor,\n   *  passing in the locale string of the language.\n   *\n   *  Generally there is no need to create instances of a Wordlist,\n   *  since each language-specific Wordlist creates an instance and\n   *  there is no state kept internally, so they are safe to share.\n   */\n  constructor(locale) {\n    defineProperties(this, {\n      locale\n    });\n  }\n  /**\n   *  Sub-classes may override this to provide a language-specific\n   *  method for spliting %%phrase%% into individual words.\n   *\n   *  By default, %%phrase%% is split using any sequences of\n   *  white-space as defined by regular expressions (i.e. ``/\\s+/``).\n   */\n  split(phrase) {\n    return phrase.toLowerCase().split(/\\s+/g);\n  }\n  /**\n   *  Sub-classes may override this to provider a language-specific\n   *  method for joining %%words%% into a phrase.\n   *\n   *  By default, %%words%% are joined by a single space.\n   */\n  join(words) {\n    return words.join(\" \");\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "Wordlist", "locale", "constructor", "split", "phrase", "toLowerCase", "join", "words"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\wordlist.ts"], "sourcesContent": ["import { defineProperties } from \"../utils/index.js\";\n\n/**\n *  A Wordlist represents a collection of language-specific\n *  words used to encode and devoce [[link-bip-39]] encoded data\n *  by mapping words to 11-bit values and vice versa.\n */\nexport abstract class Wordlist {\n    locale!: string;\n\n    /**\n     *  Creates a new Wordlist instance.\n     *\n     *  Sub-classes MUST call this if they provide their own constructor,\n     *  passing in the locale string of the language.\n     *\n     *  Generally there is no need to create instances of a Wordlist,\n     *  since each language-specific Wordlist creates an instance and\n     *  there is no state kept internally, so they are safe to share.\n     */\n    constructor(locale: string) {\n        defineProperties<Wordlist>(this, { locale });\n    }\n\n    /**\n     *  Sub-classes may override this to provide a language-specific\n     *  method for spliting %%phrase%% into individual words.\n     *\n     *  By default, %%phrase%% is split using any sequences of\n     *  white-space as defined by regular expressions (i.e. ``/\\s+/``).\n     */\n    split(phrase: string): Array<string> {\n        return phrase.toLowerCase().split(/\\s+/g)\n    }\n\n    /**\n     *  Sub-classes may override this to provider a language-specific\n     *  method for joining %%words%% into a phrase.\n     *\n     *  By default, %%words%% are joined by a single space.\n     */\n    join(words: Array<string>): string {\n        return words.join(\" \");\n    }\n\n    /**\n     *  Maps an 11-bit value into its coresponding word in the list.\n     *\n     *  Sub-classes MUST override this.\n     */\n    abstract getWord(index: number): string;\n\n    /**\n     *  Maps a word to its corresponding 11-bit value.\n     *\n     *  Sub-classes MUST override this.\n     */\n    abstract getWordIndex(word: string): number;\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,mBAAmB;AAEpD;;;;;AAKA,OAAM,MAAgBC,QAAQ;EAC1BC,MAAM;EAEN;;;;;;;;;;EAUAC,YAAYD,MAAc;IACtBF,gBAAgB,CAAW,IAAI,EAAE;MAAEE;IAAM,CAAE,CAAC;EAChD;EAEA;;;;;;;EAOAE,KAAKA,CAACC,MAAc;IAChB,OAAOA,MAAM,CAACC,WAAW,EAAE,CAACF,KAAK,CAAC,MAAM,CAAC;EAC7C;EAEA;;;;;;EAMAG,IAAIA,CAACC,KAAoB;IACrB,OAAOA,KAAK,CAACD,IAAI,CAAC,GAAG,CAAC;EAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}