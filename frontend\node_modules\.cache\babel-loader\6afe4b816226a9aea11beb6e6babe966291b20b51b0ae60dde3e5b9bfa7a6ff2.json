{"ast": null, "code": "/**\n *  A **Contract** object is a meta-class (a class whose definition is\n *  defined at runtime), which communicates with a deployed smart contract\n *  on the blockchain and provides a simple JavaScript interface to call\n *  methods, send transaction, query historic logs and listen for its events.\n *\n *  @_section: api/contract:Contracts  [about-contracts]\n */\nexport { BaseContract, Contract } from \"./contract.js\";\nexport { ContractFactory } from \"./factory.js\";\nexport { ContractEventPayload, ContractUnknownEventPayload, ContractTransactionReceipt, ContractTransactionResponse, EventLog, UndecodedEventLog } from \"./wrappers.js\";", "map": {"version": 3, "names": ["BaseContract", "Contract", "ContractFactory", "ContractEventPayload", "ContractUnknownEventPayload", "ContractTransactionReceipt", "ContractTransactionResponse", "EventLog", "UndecodedEventLog"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\contract\\index.ts"], "sourcesContent": ["/**\n *  A **Contract** object is a meta-class (a class whose definition is\n *  defined at runtime), which communicates with a deployed smart contract\n *  on the blockchain and provides a simple JavaScript interface to call\n *  methods, send transaction, query historic logs and listen for its events.\n *\n *  @_section: api/contract:Contracts  [about-contracts]\n */\nexport {\n    BaseContract, Contract\n} from \"./contract.js\";\n\nexport {\n    ContractFactory\n} from \"./factory.js\";\n\nexport {\n    ContractEventPayload, ContractUnknownEventPayload,\n    ContractTransactionReceipt, ContractTransactionResponse,\n    EventLog, UndecodedEventLog\n} from \"./wrappers.js\";\n\nexport type {\n    BaseContractMethod, ConstantContractMethod,\n    PostfixOverrides,\n    ContractEvent, ContractEventArgs, ContractEventName,\n    ContractDeployTransaction,\n    ContractInterface, ContractMethod, ContractMethodArgs, ContractTransaction,\n    DeferredTopicFilter, Overrides,\n    WrappedFallback\n} from \"./types.js\";\n"], "mappings": "AAAA;;;;;;;;AAQA,SACIA,YAAY,EAAEC,QAAQ,QACnB,eAAe;AAEtB,SACIC,eAAe,QACZ,cAAc;AAErB,SACIC,oBAAoB,EAAEC,2BAA2B,EACjDC,0BAA0B,EAAEC,2BAA2B,EACvDC,QAAQ,EAAEC,iBAAiB,QACxB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}