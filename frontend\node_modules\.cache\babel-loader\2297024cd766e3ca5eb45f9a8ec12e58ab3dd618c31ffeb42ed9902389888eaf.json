{"ast": null, "code": "import { defineProperties, getBytesCopy, hexlify } from \"../../utils/index.js\";\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n/**\n *  @_ignore\n */\nexport class FixedBytesCoder extends Coder {\n  size;\n  constructor(size, localName) {\n    let name = \"bytes\" + String(size);\n    super(name, name, localName, false);\n    defineProperties(this, {\n      size\n    }, {\n      size: \"number\"\n    });\n  }\n  defaultValue() {\n    return \"0x0000000000000000000000000000000000000000000000000000000000000000\".substring(0, 2 + this.size * 2);\n  }\n  encode(writer, _value) {\n    let data = getBytesCopy(Typed.dereference(_value, this.type));\n    if (data.length !== this.size) {\n      this._throwError(\"incorrect data length\", _value);\n    }\n    return writer.writeBytes(data);\n  }\n  decode(reader) {\n    return hexlify(reader.readBytes(this.size));\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "getBytesCopy", "hexlify", "Typed", "Coder", "FixedBytesCoder", "size", "constructor", "localName", "name", "String", "defaultValue", "substring", "encode", "writer", "_value", "data", "dereference", "type", "length", "_throwError", "writeBytes", "decode", "reader", "readBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\fixed-bytes.ts"], "sourcesContent": ["\nimport { defineProperties, getBytesCopy, hexlify } from \"../../utils/index.js\";\n\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n\nimport type { BytesLike } from \"../../utils/index.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n\n/**\n *  @_ignore\n */\nexport class FixedBytesCoder extends Coder {\n    readonly size!: number;\n\n    constructor(size: number, localName: string) {\n        let name = \"bytes\" + String(size);\n        super(name, name, localName, false);\n        defineProperties<FixedBytesCoder>(this, { size }, { size: \"number\" });\n    }\n\n    defaultValue(): string {\n        return (\"0x0000000000000000000000000000000000000000000000000000000000000000\").substring(0, 2 + this.size * 2);\n    }\n\n    encode(writer: Writer, _value: BytesLike | Typed): number {\n        let data = getBytesCopy(Typed.dereference(_value, this.type));\n        if (data.length !== this.size) { this._throwError(\"incorrect data length\", _value); }\n        return writer.writeBytes(data);\n    }\n\n    decode(reader: Reader): any {\n        return hexlify(reader.readBytes(this.size));\n    }\n}\n"], "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAY,EAAEC,OAAO,QAAQ,sBAAsB;AAE9E,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,qBAAqB;AAO3C;;;AAGA,OAAM,MAAOC,eAAgB,SAAQD,KAAK;EAC7BE,IAAI;EAEbC,YAAYD,IAAY,EAAEE,SAAiB;IACvC,IAAIC,IAAI,GAAG,OAAO,GAAGC,MAAM,CAACJ,IAAI,CAAC;IACjC,KAAK,CAACG,IAAI,EAAEA,IAAI,EAAED,SAAS,EAAE,KAAK,CAAC;IACnCR,gBAAgB,CAAkB,IAAI,EAAE;MAAEM;IAAI,CAAE,EAAE;MAAEA,IAAI,EAAE;IAAQ,CAAE,CAAC;EACzE;EAEAK,YAAYA,CAAA;IACR,OAAQ,oEAAoE,CAAEC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC;EACjH;EAEAO,MAAMA,CAACC,MAAc,EAAEC,MAAyB;IAC5C,IAAIC,IAAI,GAAGf,YAAY,CAACE,KAAK,CAACc,WAAW,CAACF,MAAM,EAAE,IAAI,CAACG,IAAI,CAAC,CAAC;IAC7D,IAAIF,IAAI,CAACG,MAAM,KAAK,IAAI,CAACb,IAAI,EAAE;MAAE,IAAI,CAACc,WAAW,CAAC,uBAAuB,EAAEL,MAAM,CAAC;;IAClF,OAAOD,MAAM,CAACO,UAAU,CAACL,IAAI,CAAC;EAClC;EAEAM,MAAMA,CAACC,MAAc;IACjB,OAAOrB,OAAO,CAACqB,MAAM,CAACC,SAAS,CAAC,IAAI,CAAClB,IAAI,CAAC,CAAC;EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}