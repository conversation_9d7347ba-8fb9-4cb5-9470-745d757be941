{"ast": null, "code": "// Cipher Feedback\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _CFB_instances, _CFB_iv, _CFB_shiftRegister, _CFB_shift;\nimport { ModeOfOperation } from \"./mode.js\";\nexport class CFB extends ModeOfOperation {\n  constructor(key, iv, segmentSize = 8) {\n    super(\"CFB\", key, CFB);\n    _CFB_instances.add(this);\n    _CFB_iv.set(this, void 0);\n    _CFB_shiftRegister.set(this, void 0);\n    // This library currently only handles byte-aligned segmentSize\n    if (!Number.isInteger(segmentSize) || segmentSize % 8) {\n      throw new TypeError(\"invalid segmentSize\");\n    }\n    Object.defineProperties(this, {\n      segmentSize: {\n        enumerable: true,\n        value: segmentSize\n      }\n    });\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      __classPrivateFieldSet(this, _CFB_iv, new Uint8Array(iv), \"f\");\n    } else {\n      __classPrivateFieldSet(this, _CFB_iv, new Uint8Array(16), \"f\");\n    }\n    __classPrivateFieldSet(this, _CFB_shiftRegister, this.iv, \"f\");\n  }\n  get iv() {\n    return new Uint8Array(__classPrivateFieldGet(this, _CFB_iv, \"f\"));\n  }\n  encrypt(plaintext) {\n    if (8 * plaintext.length % this.segmentSize) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of segmentSize bytes)\");\n    }\n    const segmentSize = this.segmentSize / 8;\n    const ciphertext = new Uint8Array(plaintext);\n    for (let i = 0; i < ciphertext.length; i += segmentSize) {\n      const xorSegment = this.aes.encrypt(__classPrivateFieldGet(this, _CFB_shiftRegister, \"f\"));\n      for (let j = 0; j < segmentSize; j++) {\n        ciphertext[i + j] ^= xorSegment[j];\n      }\n      __classPrivateFieldGet(this, _CFB_instances, \"m\", _CFB_shift).call(this, ciphertext.subarray(i));\n    }\n    return ciphertext;\n  }\n  decrypt(ciphertext) {\n    if (8 * ciphertext.length % this.segmentSize) {\n      throw new TypeError(\"invalid ciphertext size (must be multiple of segmentSize bytes)\");\n    }\n    const segmentSize = this.segmentSize / 8;\n    const plaintext = new Uint8Array(ciphertext);\n    for (let i = 0; i < plaintext.length; i += segmentSize) {\n      const xorSegment = this.aes.encrypt(__classPrivateFieldGet(this, _CFB_shiftRegister, \"f\"));\n      for (let j = 0; j < segmentSize; j++) {\n        plaintext[i + j] ^= xorSegment[j];\n      }\n      __classPrivateFieldGet(this, _CFB_instances, \"m\", _CFB_shift).call(this, ciphertext.subarray(i));\n    }\n    return plaintext;\n  }\n}\n_CFB_iv = new WeakMap(), _CFB_shiftRegister = new WeakMap(), _CFB_instances = new WeakSet(), _CFB_shift = function _CFB_shift(data) {\n  const segmentSize = this.segmentSize / 8;\n  // Shift the register\n  __classPrivateFieldGet(this, _CFB_shiftRegister, \"f\").set(__classPrivateFieldGet(this, _CFB_shiftRegister, \"f\").subarray(segmentSize));\n  __classPrivateFieldGet(this, _CFB_shiftRegister, \"f\").set(data.subarray(0, segmentSize), 16 - segmentSize);\n};", "map": {"version": 3, "names": ["ModeOfOperation", "CFB", "constructor", "key", "iv", "segmentSize", "_CFB_iv", "set", "_CFB_shiftRegister", "Number", "isInteger", "TypeError", "Object", "defineProperties", "enumerable", "value", "length", "__classPrivateFieldSet", "Uint8Array", "__classPrivateFieldGet", "encrypt", "plaintext", "ciphertext", "i", "xorSegment", "aes", "j", "_CFB_instances", "_CFB_shift", "call", "subarray", "decrypt", "data"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode-cfb.ts"], "sourcesContent": ["// Cipher Feedback\n\nimport { ModeOfOperation } from \"./mode.js\";\n\nexport class CFB extends ModeOfOperation {\n  #iv: Uint8Array;\n  #shiftRegister: Uint8Array;\n\n  readonly segmentSize!: number;\n\n  constructor(key: Uint8Array, iv?: Uint8Array, segmentSize: number = 8) {\n    super(\"CFB\", key, CFB);\n\n    // This library currently only handles byte-aligned segmentSize\n    if (!Number.isInteger(segmentSize) || (segmentSize % 8)) {\n      throw new TypeError(\"invalid segmentSize\");\n    }\n\n    Object.defineProperties(this, {\n      segmentSize: { enumerable: true, value: segmentSize }\n    });\n\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      this.#iv = new Uint8Array(iv);\n    } else {\n      this.#iv = new Uint8Array(16);\n    }\n\n    this.#shiftRegister = this.iv;\n  }\n\n  get iv(): Uint8Array { return new Uint8Array(this.#iv); }\n\n  #shift(data: Uint8Array): void {\n    const segmentSize = this.segmentSize / 8;\n\n    // Shift the register\n    this.#shiftRegister.set(this.#shiftRegister.subarray(segmentSize));\n    this.#shiftRegister.set(data.subarray(0, segmentSize), 16 - segmentSize);\n  }\n\n  encrypt(plaintext: Uint8Array): Uint8Array {\n    if (8 * plaintext.length % this.segmentSize) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of segmentSize bytes)\");\n    }\n\n    const segmentSize = this.segmentSize / 8;\n\n    const ciphertext = new Uint8Array(plaintext);\n\n    for (let i = 0; i < ciphertext.length; i += segmentSize) {\n      const xorSegment = this.aes.encrypt(this.#shiftRegister);\n      for (let j = 0; j < segmentSize; j++) {\n        ciphertext[i + j] ^= xorSegment[j];\n      }\n\n      this.#shift(ciphertext.subarray(i));\n    }\n\n    return ciphertext;\n  }\n\n  decrypt(ciphertext: Uint8Array): Uint8Array {\n    if (8 * ciphertext.length % this.segmentSize) {\n        throw new TypeError(\"invalid ciphertext size (must be multiple of segmentSize bytes)\");\n    }\n\n    const segmentSize = this.segmentSize / 8;\n\n    const plaintext = new Uint8Array(ciphertext);\n\n    for (let i = 0; i < plaintext.length; i += segmentSize) {\n      const xorSegment = this.aes.encrypt(this.#shiftRegister);\n      for (let j = 0; j < segmentSize; j++) {\n        plaintext[i + j] ^= xorSegment[j];\n      }\n\n      this.#shift(ciphertext.subarray(i));\n    }\n\n    return plaintext;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAEA,SAASA,eAAe,QAAQ,WAAW;AAE3C,OAAM,MAAOC,GAAI,SAAQD,eAAe;EAMtCE,YAAYC,GAAe,EAAEC,EAAe,EAAEC,WAAA,GAAsB,CAAC;IACnE,KAAK,CAAC,KAAK,EAAEF,GAAG,EAAEF,GAAG,CAAC;;IANxBK,OAAA,CAAAC,GAAA;IACAC,kBAAA,CAAAD,GAAA;IAOE;IACA,IAAI,CAACE,MAAM,CAACC,SAAS,CAACL,WAAW,CAAC,IAAKA,WAAW,GAAG,CAAE,EAAE;MACvD,MAAM,IAAIM,SAAS,CAAC,qBAAqB,CAAC;;IAG5CC,MAAM,CAACC,gBAAgB,CAAC,IAAI,EAAE;MAC5BR,WAAW,EAAE;QAAES,UAAU,EAAE,IAAI;QAAEC,KAAK,EAAEV;MAAW;KACpD,CAAC;IAEF,IAAID,EAAE,EAAE;MACN,IAAIA,EAAE,CAACY,MAAM,GAAG,EAAE,EAAE;QAClB,MAAM,IAAIL,SAAS,CAAC,oCAAoC,CAAC;;MAE3DM,sBAAA,KAAI,EAAAX,OAAA,EAAO,IAAIY,UAAU,CAACd,EAAE,CAAC;KAC9B,MAAM;MACLa,sBAAA,KAAI,EAAAX,OAAA,EAAO,IAAIY,UAAU,CAAC,EAAE,CAAC;;IAG/BD,sBAAA,KAAI,EAAAT,kBAAA,EAAkB,IAAI,CAACJ,EAAE;EAC/B;EAEA,IAAIA,EAAEA,CAAA;IAAiB,OAAO,IAAIc,UAAU,CAACC,sBAAA,KAAI,EAAAb,OAAA,MAAI,CAAC;EAAE;EAUxDc,OAAOA,CAACC,SAAqB;IAC3B,IAAI,CAAC,GAAGA,SAAS,CAACL,MAAM,GAAG,IAAI,CAACX,WAAW,EAAE;MAC3C,MAAM,IAAIM,SAAS,CAAC,gEAAgE,CAAC;;IAGvF,MAAMN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;IAExC,MAAMiB,UAAU,GAAG,IAAIJ,UAAU,CAACG,SAAS,CAAC;IAE5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACN,MAAM,EAAEO,CAAC,IAAIlB,WAAW,EAAE;MACvD,MAAMmB,UAAU,GAAG,IAAI,CAACC,GAAG,CAACL,OAAO,CAACD,sBAAA,KAAI,EAAAX,kBAAA,MAAe,CAAC;MACxD,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,EAAEqB,CAAC,EAAE,EAAE;QACpCJ,UAAU,CAACC,CAAC,GAAGG,CAAC,CAAC,IAAIF,UAAU,CAACE,CAAC,CAAC;;MAGpCP,sBAAA,KAAI,EAAAQ,cAAA,OAAAC,UAAA,CAAO,CAAAC,IAAA,CAAX,IAAI,EAAQP,UAAU,CAACQ,QAAQ,CAACP,CAAC,CAAC,CAAC;;IAGrC,OAAOD,UAAU;EACnB;EAEAS,OAAOA,CAACT,UAAsB;IAC5B,IAAI,CAAC,GAAGA,UAAU,CAACN,MAAM,GAAG,IAAI,CAACX,WAAW,EAAE;MAC1C,MAAM,IAAIM,SAAS,CAAC,iEAAiE,CAAC;;IAG1F,MAAMN,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;IAExC,MAAMgB,SAAS,GAAG,IAAIH,UAAU,CAACI,UAAU,CAAC;IAE5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACL,MAAM,EAAEO,CAAC,IAAIlB,WAAW,EAAE;MACtD,MAAMmB,UAAU,GAAG,IAAI,CAACC,GAAG,CAACL,OAAO,CAACD,sBAAA,KAAI,EAAAX,kBAAA,MAAe,CAAC;MACxD,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,WAAW,EAAEqB,CAAC,EAAE,EAAE;QACpCL,SAAS,CAACE,CAAC,GAAGG,CAAC,CAAC,IAAIF,UAAU,CAACE,CAAC,CAAC;;MAGnCP,sBAAA,KAAI,EAAAQ,cAAA,OAAAC,UAAA,CAAO,CAAAC,IAAA,CAAX,IAAI,EAAQP,UAAU,CAACQ,QAAQ,CAACP,CAAC,CAAC,CAAC;;IAGrC,OAAOF,SAAS;EAClB;;8HAhDOW,IAAgB;EACrB,MAAM3B,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;EAExC;EACAc,sBAAA,KAAI,EAAAX,kBAAA,MAAe,CAACD,GAAG,CAACY,sBAAA,KAAI,EAAAX,kBAAA,MAAe,CAACsB,QAAQ,CAACzB,WAAW,CAAC,CAAC;EAClEc,sBAAA,KAAI,EAAAX,kBAAA,MAAe,CAACD,GAAG,CAACyB,IAAI,CAACF,QAAQ,CAAC,CAAC,EAAEzB,WAAW,CAAC,EAAE,EAAE,GAAGA,WAAW,CAAC;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}