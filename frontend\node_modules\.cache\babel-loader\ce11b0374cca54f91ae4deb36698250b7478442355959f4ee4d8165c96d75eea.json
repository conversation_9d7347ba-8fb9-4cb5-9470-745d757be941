{"ast": null, "code": "import { toUtf8Bytes, toUtf8String } from \"../../utils/utf8.js\";\nimport { Typed } from \"../typed.js\";\nimport { DynamicBytesCoder } from \"./bytes.js\";\n/**\n *  @_ignore\n */\nexport class StringCoder extends DynamicBytesCoder {\n  constructor(localName) {\n    super(\"string\", localName);\n  }\n  defaultValue() {\n    return \"\";\n  }\n  encode(writer, _value) {\n    return super.encode(writer, toUtf8Bytes(Typed.dereference(_value, \"string\")));\n  }\n  decode(reader) {\n    return toUtf8String(super.decode(reader));\n  }\n}", "map": {"version": 3, "names": ["toUtf8Bytes", "toUtf8String", "Typed", "DynamicBytesCoder", "StringCoder", "constructor", "localName", "defaultValue", "encode", "writer", "_value", "dereference", "decode", "reader"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\string.ts"], "sourcesContent": ["import { toUtf8Bytes, toUtf8String } from \"../../utils/utf8.js\";\n\nimport { Typed } from \"../typed.js\";\nimport { DynamicBytesCoder } from \"./bytes.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n\n/**\n *  @_ignore\n */\nexport class StringCoder extends DynamicBytesCoder {\n\n    constructor(localName: string) {\n        super(\"string\", localName);\n    }\n\n    defaultValue(): string {\n        return \"\";\n    }\n\n    encode(writer: Writer, _value: string | Typed): number {\n        return super.encode(writer, toUtf8Bytes(Typed.dereference(_value, \"string\")));\n    }\n\n    decode(reader: Reader): any {\n        return toUtf8String(super.decode(reader));\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,YAAY,QAAQ,qBAAqB;AAE/D,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,iBAAiB,QAAQ,YAAY;AAK9C;;;AAGA,OAAM,MAAOC,WAAY,SAAQD,iBAAiB;EAE9CE,YAAYC,SAAiB;IACzB,KAAK,CAAC,QAAQ,EAAEA,SAAS,CAAC;EAC9B;EAEAC,YAAYA,CAAA;IACR,OAAO,EAAE;EACb;EAEAC,MAAMA,CAACC,MAAc,EAAEC,MAAsB;IACzC,OAAO,KAAK,CAACF,MAAM,CAACC,MAAM,EAAET,WAAW,CAACE,KAAK,CAACS,WAAW,CAACD,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;EACjF;EAEAE,MAAMA,CAACC,MAAc;IACjB,OAAOZ,YAAY,CAAC,KAAK,CAACW,MAAM,CAACC,MAAM,CAAC,CAAC;EAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}