{"ast": null, "code": "import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n// Choice: a ? b : c\nconst Chi = (a, b, c) => a & b ^ ~a & c;\n// Majority function, true if any two inpust is true\nconst Maj = (a, b, c) => a & b ^ a & c ^ b & c;\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */new Uint32Array([0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2]);\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */new Uint32Array([0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19]);\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */new Uint32Array(64);\nclass SHA256 extends SHA2 {\n  constructor() {\n    super(64, 32, 8, false);\n    // We cannot use array here since array allows indexing by variable\n    // which means optimizer/compiler cannot use registers.\n    this.A = IV[0] | 0;\n    this.B = IV[1] | 0;\n    this.C = IV[2] | 0;\n    this.D = IV[3] | 0;\n    this.E = IV[4] | 0;\n    this.F = IV[5] | 0;\n    this.G = IV[6] | 0;\n    this.H = IV[7] | 0;\n  }\n  get() {\n    const {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  set(A, B, C, D, E, F, G, H) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  process(view, offset) {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ W15 >>> 3;\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ W2 >>> 10;\n      SHA256_W[i] = s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16] | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let {\n      A,\n      B,\n      C,\n      D,\n      E,\n      F,\n      G,\n      H\n    } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i] | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = sigma0 + Maj(A, B, C) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = D + T1 | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = T1 + T2 | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = A + this.A | 0;\n    B = B + this.B | 0;\n    C = C + this.C | 0;\n    D = D + this.D | 0;\n    E = E + this.E | 0;\n    F = F + this.F | 0;\n    G = G + this.G | 0;\n    H = H + this.H | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  roundClean() {\n    SHA256_W.fill(0);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n  constructor() {\n    super();\n    this.A = 0xc1059ed8 | 0;\n    this.B = 0x367cd507 | 0;\n    this.C = 0x3070dd17 | 0;\n    this.D = 0xf70e5939 | 0;\n    this.E = 0xffc00b31 | 0;\n    this.F = 0x68581511 | 0;\n    this.G = 0x64f98fa7 | 0;\n    this.H = 0xbefa4fa4 | 0;\n    this.outputLen = 28;\n  }\n}\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */wrapConstructor(() => new SHA224());", "map": {"version": 3, "names": ["SHA2", "rotr", "wrapConstructor", "<PERSON>", "a", "b", "c", "Maj", "SHA256_K", "Uint32Array", "IV", "SHA256_W", "SHA256", "constructor", "A", "B", "C", "D", "E", "F", "G", "H", "get", "set", "process", "view", "offset", "i", "getUint32", "W15", "W2", "s0", "s1", "sigma1", "T1", "sigma0", "T2", "roundClean", "fill", "destroy", "buffer", "SHA224", "outputLen", "sha256", "sha224"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\sha256.ts"], "sourcesContent": ["import { SHA2 } from './_sha2.js';\nimport { rotr, wrapConstructor } from './utils.js';\n\n// SHA2-256 need to try 2^128 hashes to execute birthday attack.\n// BTC network is doing 2^67 hashes/sec as per early 2023.\n\n// Choice: a ? b : c\nconst Chi = (a: number, b: number, c: number) => (a & b) ^ (~a & c);\n// Majority function, true if any two inpust is true\nconst Maj = (a: number, b: number, c: number) => (a & b) ^ (a & c) ^ (b & c);\n\n// Round constants:\n// first 32 bits of the fractional parts of the cube roots of the first 64 primes 2..311)\n// prettier-ignore\nconst SHA256_K = /* @__PURE__ */new Uint32Array([\n  0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,\n  0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,\n  0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,\n  0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,\n  0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,\n  0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,\n  0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,\n  0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2\n]);\n\n// Initial state (first 32 bits of the fractional parts of the square roots of the first 8 primes 2..19):\n// prettier-ignore\nconst IV = /* @__PURE__ */new Uint32Array([\n  0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19\n]);\n\n// Temporary buffer, not used to store anything between runs\n// Named this way because it matches specification.\nconst SHA256_W = /* @__PURE__ */ new Uint32Array(64);\nclass SHA256 extends SHA2<SHA256> {\n  // We cannot use array here since array allows indexing by variable\n  // which means optimizer/compiler cannot use registers.\n  A = IV[0] | 0;\n  B = IV[1] | 0;\n  C = IV[2] | 0;\n  D = IV[3] | 0;\n  E = IV[4] | 0;\n  F = IV[5] | 0;\n  G = IV[6] | 0;\n  H = IV[7] | 0;\n\n  constructor() {\n    super(64, 32, 8, false);\n  }\n  protected get(): [number, number, number, number, number, number, number, number] {\n    const { A, B, C, D, E, F, G, H } = this;\n    return [A, B, C, D, E, F, G, H];\n  }\n  // prettier-ignore\n  protected set(\n    A: number, B: number, C: number, D: number, E: number, F: number, G: number, H: number\n  ) {\n    this.A = A | 0;\n    this.B = B | 0;\n    this.C = C | 0;\n    this.D = D | 0;\n    this.E = E | 0;\n    this.F = F | 0;\n    this.G = G | 0;\n    this.H = H | 0;\n  }\n  protected process(view: DataView, offset: number): void {\n    // Extend the first 16 words into the remaining 48 words w[16..63] of the message schedule array\n    for (let i = 0; i < 16; i++, offset += 4) SHA256_W[i] = view.getUint32(offset, false);\n    for (let i = 16; i < 64; i++) {\n      const W15 = SHA256_W[i - 15];\n      const W2 = SHA256_W[i - 2];\n      const s0 = rotr(W15, 7) ^ rotr(W15, 18) ^ (W15 >>> 3);\n      const s1 = rotr(W2, 17) ^ rotr(W2, 19) ^ (W2 >>> 10);\n      SHA256_W[i] = (s1 + SHA256_W[i - 7] + s0 + SHA256_W[i - 16]) | 0;\n    }\n    // Compression function main loop, 64 rounds\n    let { A, B, C, D, E, F, G, H } = this;\n    for (let i = 0; i < 64; i++) {\n      const sigma1 = rotr(E, 6) ^ rotr(E, 11) ^ rotr(E, 25);\n      const T1 = (H + sigma1 + Chi(E, F, G) + SHA256_K[i] + SHA256_W[i]) | 0;\n      const sigma0 = rotr(A, 2) ^ rotr(A, 13) ^ rotr(A, 22);\n      const T2 = (sigma0 + Maj(A, B, C)) | 0;\n      H = G;\n      G = F;\n      F = E;\n      E = (D + T1) | 0;\n      D = C;\n      C = B;\n      B = A;\n      A = (T1 + T2) | 0;\n    }\n    // Add the compressed chunk to the current hash value\n    A = (A + this.A) | 0;\n    B = (B + this.B) | 0;\n    C = (C + this.C) | 0;\n    D = (D + this.D) | 0;\n    E = (E + this.E) | 0;\n    F = (F + this.F) | 0;\n    G = (G + this.G) | 0;\n    H = (H + this.H) | 0;\n    this.set(A, B, C, D, E, F, G, H);\n  }\n  protected roundClean() {\n    SHA256_W.fill(0);\n  }\n  destroy() {\n    this.set(0, 0, 0, 0, 0, 0, 0, 0);\n    this.buffer.fill(0);\n  }\n}\n// Constants from https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf\nclass SHA224 extends SHA256 {\n  A = 0xc1059ed8 | 0;\n  B = 0x367cd507 | 0;\n  C = 0x3070dd17 | 0;\n  D = 0xf70e5939 | 0;\n  E = 0xffc00b31 | 0;\n  F = 0x68581511 | 0;\n  G = 0x64f98fa7 | 0;\n  H = 0xbefa4fa4 | 0;\n  constructor() {\n    super();\n    this.outputLen = 28;\n  }\n}\n\n/**\n * SHA2-256 hash function\n * @param message - data that would be hashed\n */\nexport const sha256 = /* @__PURE__ */ wrapConstructor(() => new SHA256());\nexport const sha224 = /* @__PURE__ */ wrapConstructor(() => new SHA224());\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,YAAY;AACjC,SAASC,IAAI,EAAEC,eAAe,QAAQ,YAAY;AAElD;AACA;AAEA;AACA,MAAMC,GAAG,GAAGA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMF,CAAC,GAAGC,CAAC,GAAK,CAACD,CAAC,GAAGE,CAAE;AACnE;AACA,MAAMC,GAAG,GAAGA,CAACH,CAAS,EAAEC,CAAS,EAAEC,CAAS,KAAMF,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGE,CAAE,GAAID,CAAC,GAAGC,CAAE;AAE5E;AACA;AACA;AACA,MAAME,QAAQ,GAAG,eAAe,IAAIC,WAAW,CAAC,CAC9C,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAC9F,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F,CAAC;AAEF;AACA;AACA,MAAMC,EAAE,GAAG,eAAe,IAAID,WAAW,CAAC,CACxC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAC/F,CAAC;AAEF;AACA;AACA,MAAME,QAAQ,GAAG,eAAgB,IAAIF,WAAW,CAAC,EAAE,CAAC;AACpD,MAAMG,MAAO,SAAQZ,IAAY;EAY/Ba,YAAA;IACE,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC;IAZzB;IACA;IACA,KAAAC,CAAC,GAAGJ,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAK,CAAC,GAAGL,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAM,CAAC,GAAGN,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAO,CAAC,GAAGP,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAQ,CAAC,GAAGR,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAS,CAAC,GAAGT,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAU,CAAC,GAAGV,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;IACb,KAAAW,CAAC,GAAGX,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;EAIb;EACUY,GAAGA,CAAA;IACX,MAAM;MAAER,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IACvC,OAAO,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EACjC;EACA;EACUE,GAAGA,CACXT,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS;IAEtF,IAAI,CAACP,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC;EAChB;EACUG,OAAOA,CAACC,IAAc,EAAEC,MAAc;IAC9C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAED,MAAM,IAAI,CAAC,EAAEf,QAAQ,CAACgB,CAAC,CAAC,GAAGF,IAAI,CAACG,SAAS,CAACF,MAAM,EAAE,KAAK,CAAC;IACrF,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC5B,MAAME,GAAG,GAAGlB,QAAQ,CAACgB,CAAC,GAAG,EAAE,CAAC;MAC5B,MAAMG,EAAE,GAAGnB,QAAQ,CAACgB,CAAC,GAAG,CAAC,CAAC;MAC1B,MAAMI,EAAE,GAAG9B,IAAI,CAAC4B,GAAG,EAAE,CAAC,CAAC,GAAG5B,IAAI,CAAC4B,GAAG,EAAE,EAAE,CAAC,GAAIA,GAAG,KAAK,CAAE;MACrD,MAAMG,EAAE,GAAG/B,IAAI,CAAC6B,EAAE,EAAE,EAAE,CAAC,GAAG7B,IAAI,CAAC6B,EAAE,EAAE,EAAE,CAAC,GAAIA,EAAE,KAAK,EAAG;MACpDnB,QAAQ,CAACgB,CAAC,CAAC,GAAIK,EAAE,GAAGrB,QAAQ,CAACgB,CAAC,GAAG,CAAC,CAAC,GAAGI,EAAE,GAAGpB,QAAQ,CAACgB,CAAC,GAAG,EAAE,CAAC,GAAI,CAAC;;IAElE;IACA,IAAI;MAAEb,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAC,CAAE,GAAG,IAAI;IACrC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMM,MAAM,GAAGhC,IAAI,CAACiB,CAAC,EAAE,CAAC,CAAC,GAAGjB,IAAI,CAACiB,CAAC,EAAE,EAAE,CAAC,GAAGjB,IAAI,CAACiB,CAAC,EAAE,EAAE,CAAC;MACrD,MAAMgB,EAAE,GAAIb,CAAC,GAAGY,MAAM,GAAG9B,GAAG,CAACe,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAGZ,QAAQ,CAACmB,CAAC,CAAC,GAAGhB,QAAQ,CAACgB,CAAC,CAAC,GAAI,CAAC;MACtE,MAAMQ,MAAM,GAAGlC,IAAI,CAACa,CAAC,EAAE,CAAC,CAAC,GAAGb,IAAI,CAACa,CAAC,EAAE,EAAE,CAAC,GAAGb,IAAI,CAACa,CAAC,EAAE,EAAE,CAAC;MACrD,MAAMsB,EAAE,GAAID,MAAM,GAAG5B,GAAG,CAACO,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAI,CAAC;MACtCK,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAID,CAAC,GAAGiB,EAAE,GAAI,CAAC;MAChBjB,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAIoB,EAAE,GAAGE,EAAE,GAAI,CAAC;;IAEnB;IACAtB,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpBC,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACA,CAAC,GAAI,CAAC;IACpB,IAAI,CAACE,GAAG,CAACT,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;EAClC;EACUgB,UAAUA,CAAA;IAClB1B,QAAQ,CAAC2B,IAAI,CAAC,CAAC,CAAC;EAClB;EACAC,OAAOA,CAAA;IACL,IAAI,CAAChB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAChC,IAAI,CAACiB,MAAM,CAACF,IAAI,CAAC,CAAC,CAAC;EACrB;;AAEF;AACA,MAAMG,MAAO,SAAQ7B,MAAM;EASzBC,YAAA;IACE,KAAK,EAAE;IATT,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAClB,KAAAC,CAAC,GAAG,UAAU,GAAG,CAAC;IAGhB,IAAI,CAACqB,SAAS,GAAG,EAAE;EACrB;;AAGF;;;;AAIA,OAAO,MAAMC,MAAM,GAAG,eAAgBzC,eAAe,CAAC,MAAM,IAAIU,MAAM,EAAE,CAAC;AACzE,OAAO,MAAMgC,MAAM,GAAG,eAAgB1C,eAAe,CAAC,MAAM,IAAIuC,MAAM,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}