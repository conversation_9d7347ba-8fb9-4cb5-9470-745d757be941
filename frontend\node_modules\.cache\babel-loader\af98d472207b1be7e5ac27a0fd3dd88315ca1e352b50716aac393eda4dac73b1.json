{"ast": null, "code": "//See: https://github.com/ethereum/wiki/wiki/RLP\nimport { hexlify } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\nimport { getBytes } from \"./data.js\";\nfunction hexlifyByte(value) {\n  let result = value.toString(16);\n  while (result.length < 2) {\n    result = \"0\" + result;\n  }\n  return \"0x\" + result;\n}\nfunction unarrayifyInteger(data, offset, length) {\n  let result = 0;\n  for (let i = 0; i < length; i++) {\n    result = result * 256 + data[offset + i];\n  }\n  return result;\n}\nfunction _decodeChildren(data, offset, childOffset, length) {\n  const result = [];\n  while (childOffset < offset + 1 + length) {\n    const decoded = _decode(data, childOffset);\n    result.push(decoded.result);\n    childOffset += decoded.consumed;\n    assert(childOffset <= offset + 1 + length, \"child data too short\", \"BUFFER_OVERRUN\", {\n      buffer: data,\n      length,\n      offset\n    });\n  }\n  return {\n    consumed: 1 + length,\n    result: result\n  };\n}\n// returns { consumed: number, result: Object }\nfunction _decode(data, offset) {\n  assert(data.length !== 0, \"data too short\", \"BUFFER_OVERRUN\", {\n    buffer: data,\n    length: 0,\n    offset: 1\n  });\n  const checkOffset = offset => {\n    assert(offset <= data.length, \"data short segment too short\", \"BUFFER_OVERRUN\", {\n      buffer: data,\n      length: data.length,\n      offset\n    });\n  };\n  // Array with extra length prefix\n  if (data[offset] >= 0xf8) {\n    const lengthLength = data[offset] - 0xf7;\n    checkOffset(offset + 1 + lengthLength);\n    const length = unarrayifyInteger(data, offset + 1, lengthLength);\n    checkOffset(offset + 1 + lengthLength + length);\n    return _decodeChildren(data, offset, offset + 1 + lengthLength, lengthLength + length);\n  } else if (data[offset] >= 0xc0) {\n    const length = data[offset] - 0xc0;\n    checkOffset(offset + 1 + length);\n    return _decodeChildren(data, offset, offset + 1, length);\n  } else if (data[offset] >= 0xb8) {\n    const lengthLength = data[offset] - 0xb7;\n    checkOffset(offset + 1 + lengthLength);\n    const length = unarrayifyInteger(data, offset + 1, lengthLength);\n    checkOffset(offset + 1 + lengthLength + length);\n    const result = hexlify(data.slice(offset + 1 + lengthLength, offset + 1 + lengthLength + length));\n    return {\n      consumed: 1 + lengthLength + length,\n      result: result\n    };\n  } else if (data[offset] >= 0x80) {\n    const length = data[offset] - 0x80;\n    checkOffset(offset + 1 + length);\n    const result = hexlify(data.slice(offset + 1, offset + 1 + length));\n    return {\n      consumed: 1 + length,\n      result: result\n    };\n  }\n  return {\n    consumed: 1,\n    result: hexlifyByte(data[offset])\n  };\n}\n/**\n *  Decodes %%data%% into the structured data it represents.\n */\nexport function decodeRlp(_data) {\n  const data = getBytes(_data, \"data\");\n  const decoded = _decode(data, 0);\n  assertArgument(decoded.consumed === data.length, \"unexpected junk after rlp payload\", \"data\", _data);\n  return decoded.result;\n}", "map": {"version": 3, "names": ["hexlify", "assert", "assertArgument", "getBytes", "hexlifyByte", "value", "result", "toString", "length", "unarrayifyInteger", "data", "offset", "i", "_decodeC<PERSON>dren", "childOffset", "decoded", "_decode", "push", "consumed", "buffer", "checkOffset", "lengthLength", "slice", "decodeRlp", "_data"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\rlp-decode.ts"], "sourcesContent": ["//See: https://github.com/ethereum/wiki/wiki/RLP\n\nimport { hexlify } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\nimport { getBytes } from \"./data.js\";\n\nimport type { BytesLike, RlpStructuredData } from \"./index.js\";\n\n\nfunction hexlifyByte(value: number): string {\n    let result = value.toString(16);\n    while (result.length < 2) { result = \"0\" + result; }\n    return \"0x\" + result;\n}\n\nfunction unarrayifyInteger(data: Uint8Array, offset: number, length: number): number {\n    let result = 0;\n    for (let i = 0; i < length; i++) {\n        result = (result * 256) + data[offset + i];\n    }\n    return result;\n}\n\ntype Decoded = {\n    result: any;\n    consumed: number;\n};\n\nfunction _decodeChildren(data: Uint8Array, offset: number, childOffset: number, length: number): Decoded {\n    const result: Array<any> = [];\n\n    while (childOffset < offset + 1 + length) {\n        const decoded = _decode(data, childOffset);\n\n        result.push(decoded.result);\n\n        childOffset += decoded.consumed;\n        assert(childOffset <= offset + 1 + length, \"child data too short\", \"BUFFER_OVERRUN\", {\n            buffer: data, length, offset\n        });\n    }\n\n    return {consumed: (1 + length), result: result};\n}\n\n// returns { consumed: number, result: Object }\nfunction _decode(data: Uint8Array, offset: number): { consumed: number, result: any } {\n    assert(data.length !== 0, \"data too short\", \"BUFFER_OVERRUN\", {\n        buffer: data, length: 0, offset: 1\n    });\n\n    const checkOffset = (offset: number) => {\n        assert(offset <= data.length, \"data short segment too short\", \"BUFFER_OVERRUN\", {\n            buffer: data, length: data.length, offset\n        });\n    };\n\n    // Array with extra length prefix\n    if (data[offset] >= 0xf8) {\n        const lengthLength = data[offset] - 0xf7;\n        checkOffset(offset + 1 + lengthLength);\n\n        const length = unarrayifyInteger(data, offset + 1, lengthLength);\n        checkOffset(offset + 1 + lengthLength + length);\n\n        return _decodeChildren(data, offset, offset + 1 + lengthLength, lengthLength + length);\n\n    } else if (data[offset] >= 0xc0) {\n        const length = data[offset] - 0xc0;\n        checkOffset(offset + 1 + length);\n\n        return _decodeChildren(data, offset, offset + 1, length);\n\n    } else if (data[offset] >= 0xb8) {\n        const lengthLength = data[offset] - 0xb7;\n        checkOffset(offset + 1 + lengthLength);\n\n        const length = unarrayifyInteger(data, offset + 1, lengthLength);\n        checkOffset(offset + 1 + lengthLength + length);\n\n        const result = hexlify(data.slice(offset + 1 + lengthLength, offset + 1 + lengthLength + length));\n        return { consumed: (1 + lengthLength + length), result: result }\n\n    } else if (data[offset] >= 0x80) {\n        const length = data[offset] - 0x80;\n        checkOffset(offset + 1 + length);\n\n        const result = hexlify(data.slice(offset + 1, offset + 1 + length));\n        return { consumed: (1 + length), result: result }\n    }\n\n    return { consumed: 1, result: hexlifyByte(data[offset]) };\n}\n\n/**\n *  Decodes %%data%% into the structured data it represents.\n */\nexport function decodeRlp(_data: BytesLike): RlpStructuredData {\n    const data = getBytes(_data, \"data\");\n    const decoded = _decode(data, 0);\n    assertArgument(decoded.consumed === data.length, \"unexpected junk after rlp payload\", \"data\", _data);\n    return decoded.result;\n}\n\n"], "mappings": "AAAA;AAEA,SAASA,OAAO,QAAQ,WAAW;AACnC,SAASC,MAAM,EAAEC,cAAc,QAAQ,aAAa;AACpD,SAASC,QAAQ,QAAQ,WAAW;AAKpC,SAASC,WAAWA,CAACC,KAAa;EAC9B,IAAIC,MAAM,GAAGD,KAAK,CAACE,QAAQ,CAAC,EAAE,CAAC;EAC/B,OAAOD,MAAM,CAACE,MAAM,GAAG,CAAC,EAAE;IAAEF,MAAM,GAAG,GAAG,GAAGA,MAAM;;EACjD,OAAO,IAAI,GAAGA,MAAM;AACxB;AAEA,SAASG,iBAAiBA,CAACC,IAAgB,EAAEC,MAAc,EAAEH,MAAc;EACvE,IAAIF,MAAM,GAAG,CAAC;EACd,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC7BN,MAAM,GAAIA,MAAM,GAAG,GAAG,GAAII,IAAI,CAACC,MAAM,GAAGC,CAAC,CAAC;;EAE9C,OAAON,MAAM;AACjB;AAOA,SAASO,eAAeA,CAACH,IAAgB,EAAEC,MAAc,EAAEG,WAAmB,EAAEN,MAAc;EAC1F,MAAMF,MAAM,GAAe,EAAE;EAE7B,OAAOQ,WAAW,GAAGH,MAAM,GAAG,CAAC,GAAGH,MAAM,EAAE;IACtC,MAAMO,OAAO,GAAGC,OAAO,CAACN,IAAI,EAAEI,WAAW,CAAC;IAE1CR,MAAM,CAACW,IAAI,CAACF,OAAO,CAACT,MAAM,CAAC;IAE3BQ,WAAW,IAAIC,OAAO,CAACG,QAAQ;IAC/BjB,MAAM,CAACa,WAAW,IAAIH,MAAM,GAAG,CAAC,GAAGH,MAAM,EAAE,sBAAsB,EAAE,gBAAgB,EAAE;MACjFW,MAAM,EAAET,IAAI;MAAEF,MAAM;MAAEG;KACzB,CAAC;;EAGN,OAAO;IAACO,QAAQ,EAAG,CAAC,GAAGV,MAAO;IAAEF,MAAM,EAAEA;EAAM,CAAC;AACnD;AAEA;AACA,SAASU,OAAOA,CAACN,IAAgB,EAAEC,MAAc;EAC7CV,MAAM,CAACS,IAAI,CAACF,MAAM,KAAK,CAAC,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;IAC1DW,MAAM,EAAET,IAAI;IAAEF,MAAM,EAAE,CAAC;IAAEG,MAAM,EAAE;GACpC,CAAC;EAEF,MAAMS,WAAW,GAAIT,MAAc,IAAI;IACnCV,MAAM,CAACU,MAAM,IAAID,IAAI,CAACF,MAAM,EAAE,8BAA8B,EAAE,gBAAgB,EAAE;MAC5EW,MAAM,EAAET,IAAI;MAAEF,MAAM,EAAEE,IAAI,CAACF,MAAM;MAAEG;KACtC,CAAC;EACN,CAAC;EAED;EACA,IAAID,IAAI,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;IACtB,MAAMU,YAAY,GAAGX,IAAI,CAACC,MAAM,CAAC,GAAG,IAAI;IACxCS,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGU,YAAY,CAAC;IAEtC,MAAMb,MAAM,GAAGC,iBAAiB,CAACC,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAEU,YAAY,CAAC;IAChED,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGU,YAAY,GAAGb,MAAM,CAAC;IAE/C,OAAOK,eAAe,CAACH,IAAI,EAAEC,MAAM,EAAEA,MAAM,GAAG,CAAC,GAAGU,YAAY,EAAEA,YAAY,GAAGb,MAAM,CAAC;GAEzF,MAAM,IAAIE,IAAI,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;IAC7B,MAAMH,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC,GAAG,IAAI;IAClCS,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGH,MAAM,CAAC;IAEhC,OAAOK,eAAe,CAACH,IAAI,EAAEC,MAAM,EAAEA,MAAM,GAAG,CAAC,EAAEH,MAAM,CAAC;GAE3D,MAAM,IAAIE,IAAI,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;IAC7B,MAAMU,YAAY,GAAGX,IAAI,CAACC,MAAM,CAAC,GAAG,IAAI;IACxCS,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGU,YAAY,CAAC;IAEtC,MAAMb,MAAM,GAAGC,iBAAiB,CAACC,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAEU,YAAY,CAAC;IAChED,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGU,YAAY,GAAGb,MAAM,CAAC;IAE/C,MAAMF,MAAM,GAAGN,OAAO,CAACU,IAAI,CAACY,KAAK,CAACX,MAAM,GAAG,CAAC,GAAGU,YAAY,EAAEV,MAAM,GAAG,CAAC,GAAGU,YAAY,GAAGb,MAAM,CAAC,CAAC;IACjG,OAAO;MAAEU,QAAQ,EAAG,CAAC,GAAGG,YAAY,GAAGb,MAAO;MAAEF,MAAM,EAAEA;IAAM,CAAE;GAEnE,MAAM,IAAII,IAAI,CAACC,MAAM,CAAC,IAAI,IAAI,EAAE;IAC7B,MAAMH,MAAM,GAAGE,IAAI,CAACC,MAAM,CAAC,GAAG,IAAI;IAClCS,WAAW,CAACT,MAAM,GAAG,CAAC,GAAGH,MAAM,CAAC;IAEhC,MAAMF,MAAM,GAAGN,OAAO,CAACU,IAAI,CAACY,KAAK,CAACX,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,GAAGH,MAAM,CAAC,CAAC;IACnE,OAAO;MAAEU,QAAQ,EAAG,CAAC,GAAGV,MAAO;MAAEF,MAAM,EAAEA;IAAM,CAAE;;EAGrD,OAAO;IAAEY,QAAQ,EAAE,CAAC;IAAEZ,MAAM,EAAEF,WAAW,CAACM,IAAI,CAACC,MAAM,CAAC;EAAC,CAAE;AAC7D;AAEA;;;AAGA,OAAM,SAAUY,SAASA,CAACC,KAAgB;EACtC,MAAMd,IAAI,GAAGP,QAAQ,CAACqB,KAAK,EAAE,MAAM,CAAC;EACpC,MAAMT,OAAO,GAAGC,OAAO,CAACN,IAAI,EAAE,CAAC,CAAC;EAChCR,cAAc,CAACa,OAAO,CAACG,QAAQ,KAAKR,IAAI,CAACF,MAAM,EAAE,mCAAmC,EAAE,MAAM,EAAEgB,KAAK,CAAC;EACpG,OAAOT,OAAO,CAACT,MAAM;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}