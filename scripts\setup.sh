#!/bin/bash

# CryptoQuest Setup Script
# This script automates the setup process for the CryptoQuest game

set -e  # Exit on any error

echo "🚀 Starting CryptoQuest Setup..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    print_status "Checking Node.js installation..."
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js is installed: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js v16 or higher."
        exit 1
    fi
}

# Check if MySQL is installed and running
check_mysql() {
    print_status "Checking MySQL installation..."
    if command -v mysql &> /dev/null; then
        print_success "MySQL is installed"
        
        # Try to connect to MySQL
        if mysql -u root -e "SELECT 1;" &> /dev/null; then
            print_success "MySQL is running and accessible"
        else
            print_warning "MySQL is installed but may not be running or accessible with root user"
            print_status "Please ensure MySQL is running and accessible with the root user"
        fi
    else
        print_error "MySQL is not installed. Please install MySQL v8.0 or higher."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing project dependencies..."
    
    # Root dependencies
    print_status "Installing root dependencies..."
    npm install
    
    # Contract dependencies
    print_status "Installing contract dependencies..."
    cd contracts
    npm install
    cd ..
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    print_success "All dependencies installed successfully!"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Create database
    print_status "Creating database 'cq_db'..."
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS cq_db;" || {
        print_error "Failed to create database. Please check MySQL connection."
        exit 1
    }
    
    # Run migrations
    print_status "Running database migrations..."
    cd backend
    npm run migrate || {
        print_error "Database migration failed."
        exit 1
    }
    cd ..
    
    print_success "Database setup completed!"
}

# Deploy smart contracts
deploy_contracts() {
    print_status "Deploying smart contracts..."
    
    cd contracts
    
    # Check if Hardhat node is running
    if ! curl -s http://127.0.0.1:8545 &> /dev/null; then
        print_status "Starting Hardhat node..."
        npx hardhat node &
        HARDHAT_PID=$!
        sleep 5
        
        # Save PID for cleanup
        echo $HARDHAT_PID > ../hardhat.pid
    fi
    
    # Compile contracts
    print_status "Compiling contracts..."
    npx hardhat compile
    
    # Deploy contracts
    print_status "Deploying contracts to local network..."
    npx hardhat run scripts/deploy.js --network localhost > ../deployment.log 2>&1
    
    if [ $? -eq 0 ]; then
        print_success "Contracts deployed successfully!"
        
        # Extract contract addresses from deployment log
        CQT_TOKEN_ADDRESS=$(grep "CQTToken deployed to:" ../deployment.log | awk '{print $4}')
        GAME_TREASURY_ADDRESS=$(grep "GameTreasury deployed to:" ../deployment.log | awk '{print $4}')
        STAKING_POOL_ADDRESS=$(grep "StakingPool deployed to:" ../deployment.log | awk '{print $4}')
        
        print_status "Contract addresses:"
        echo "  CQTToken: $CQT_TOKEN_ADDRESS"
        echo "  GameTreasury: $GAME_TREASURY_ADDRESS"
        echo "  StakingPool: $STAKING_POOL_ADDRESS"
        
        # Update backend .env file
        print_status "Updating backend configuration..."
        cd ../backend
        
        # Update .env file with contract addresses
        sed -i.bak "s/CQT_TOKEN_ADDRESS=.*/CQT_TOKEN_ADDRESS=$CQT_TOKEN_ADDRESS/" .env
        sed -i.bak "s/GAME_TREASURY_ADDRESS=.*/GAME_TREASURY_ADDRESS=$GAME_TREASURY_ADDRESS/" .env
        sed -i.bak "s/STAKING_POOL_ADDRESS=.*/STAKING_POOL_ADDRESS=$STAKING_POOL_ADDRESS/" .env
        
        # Add a default private key (first account from Hardhat)
        DEFAULT_PRIVATE_KEY="0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
        sed -i.bak "s/PRIVATE_KEY=.*/PRIVATE_KEY=$DEFAULT_PRIVATE_KEY/" .env
        
        cd ..
        
    else
        print_error "Contract deployment failed. Check deployment.log for details."
        exit 1
    fi
    
    cd ..
}

# Create startup script
create_startup_script() {
    print_status "Creating startup script..."
    
    cat > start.sh << 'EOF'
#!/bin/bash

# CryptoQuest Startup Script

echo "🎮 Starting CryptoQuest..."

# Function to cleanup on exit
cleanup() {
    echo "🛑 Shutting down services..."
    if [ -f hardhat.pid ]; then
        kill $(cat hardhat.pid) 2>/dev/null
        rm hardhat.pid
    fi
    exit 0
}

# Set up cleanup trap
trap cleanup SIGINT SIGTERM

# Start Hardhat node if not running
if ! curl -s http://127.0.0.1:8545 &> /dev/null; then
    echo "🔗 Starting blockchain node..."
    cd contracts
    npx hardhat node &
    HARDHAT_PID=$!
    echo $HARDHAT_PID > ../hardhat.pid
    cd ..
    sleep 3
fi

# Start backend and frontend
echo "🚀 Starting backend and frontend..."
npm run dev

EOF
    
    chmod +x start.sh
    print_success "Startup script created: start.sh"
}

# Main setup function
main() {
    echo "🎮 CryptoQuest - Web3 Play-to-Earn Game Setup"
    echo "=============================================="
    echo ""
    
    # Check prerequisites
    check_nodejs
    check_mysql
    
    echo ""
    print_status "Prerequisites check passed! Starting setup..."
    echo ""
    
    # Install dependencies
    install_dependencies
    echo ""
    
    # Setup database
    setup_database
    echo ""
    
    # Deploy contracts
    deploy_contracts
    echo ""
    
    # Create startup script
    create_startup_script
    echo ""
    
    print_success "🎉 CryptoQuest setup completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure MetaMask:"
    echo "   - Add network: Localhost 8545 (http://127.0.0.1:8545, Chain ID: 31337)"
    echo "   - Import account with private key: 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
    echo "   - Add CQT token: $CQT_TOKEN_ADDRESS"
    echo ""
    echo "2. Start the application:"
    echo "   ./start.sh"
    echo ""
    echo "3. Open your browser and go to: http://localhost:3000"
    echo ""
    echo "🎮 Happy gaming!"
}

# Run main function
main "$@"
