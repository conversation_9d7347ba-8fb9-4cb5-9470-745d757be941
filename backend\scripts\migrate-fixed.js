const mysql = require('mysql2/promise');
require('dotenv').config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 3306
};

async function createDatabase() {
  let connection;
  
  try {
    // First connect without database to create it
    connection = await mysql.createConnection(dbConfig);
    
    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'cq_db'}`);
    console.log('✅ Database created successfully');
    
    // Close connection and reconnect to the specific database
    await connection.end();
    
    // Create new connection with database specified
    connection = await mysql.createConnection({
      ...dbConfig,
      database: process.env.DB_NAME || 'cq_db'
    });
    
    console.log('✅ Connected to database');
    
    // Create all tables
    await createTables(connection);
    
    // Insert default data
    await insertDefaultData(connection);
    
    console.log('🎉 Database migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function createTables(connection) {
  // Create users table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      wallet_address VARCHAR(42) UNIQUE NOT NULL,
      email VARCHAR(255),
      username VARCHAR(50),
      xp INT DEFAULT 0,
      level INT DEFAULT 1,
      rank_points INT DEFAULT 1000,
      total_battles INT DEFAULT 0,
      wins INT DEFAULT 0,
      losses INT DEFAULT 0,
      last_daily_quest DATE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_wallet (wallet_address),
      INDEX idx_rank (rank_points),
      INDEX idx_level (level)
    )
  `);
  console.log('✅ Users table created');

  // Create heroes table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS heroes (
      id INT AUTO_INCREMENT PRIMARY KEY,
      owner_address VARCHAR(42) NOT NULL,
      hero_type ENUM('warrior', 'mage', 'archer', 'assassin', 'tank') NOT NULL,
      name VARCHAR(50) NOT NULL,
      level INT DEFAULT 1,
      hp INT DEFAULT 100,
      atk INT DEFAULT 20,
      def INT DEFAULT 15,
      spd INT DEFAULT 10,
      luk INT DEFAULT 5,
      experience INT DEFAULT 0,
      is_active BOOLEAN DEFAULT TRUE,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_owner (owner_address),
      INDEX idx_type (hero_type),
      INDEX idx_level (level),
      FOREIGN KEY (owner_address) REFERENCES users(wallet_address) ON DELETE CASCADE
    )
  `);
  console.log('✅ Heroes table created');

  // Create battles table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS battles (
      id INT AUTO_INCREMENT PRIMARY KEY,
      battle_type ENUM('pve', 'pvp', 'tournament') NOT NULL,
      player1_address VARCHAR(42) NOT NULL,
      player2_address VARCHAR(42),
      player1_heroes JSON NOT NULL,
      player2_heroes JSON,
      winner_address VARCHAR(42),
      battle_log JSON,
      reward_amount DECIMAL(20, 18) DEFAULT 0,
      duration_seconds INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_player1 (player1_address),
      INDEX idx_player2 (player2_address),
      INDEX idx_type (battle_type),
      INDEX idx_winner (winner_address),
      INDEX idx_created (created_at),
      FOREIGN KEY (player1_address) REFERENCES users(wallet_address) ON DELETE CASCADE
    )
  `);
  console.log('✅ Battles table created');

  // Create transactions table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS transactions (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_address VARCHAR(42) NOT NULL,
      transaction_type ENUM('earn', 'spend', 'stake', 'unstake', 'reward') NOT NULL,
      amount DECIMAL(20, 18) NOT NULL,
      description TEXT,
      tx_hash VARCHAR(66),
      block_number INT,
      status ENUM('pending', 'confirmed', 'failed') DEFAULT 'pending',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_user (user_address),
      INDEX idx_type (transaction_type),
      INDEX idx_status (status),
      INDEX idx_created (created_at),
      FOREIGN KEY (user_address) REFERENCES users(wallet_address) ON DELETE CASCADE
    )
  `);
  console.log('✅ Transactions table created');

  // Create tournaments table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS tournaments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(100) NOT NULL,
      entry_fee DECIMAL(20, 18) NOT NULL,
      prize_pool DECIMAL(20, 18) DEFAULT 0,
      max_participants INT DEFAULT 64,
      current_participants INT DEFAULT 0,
      status ENUM('upcoming', 'registration', 'active', 'completed') DEFAULT 'upcoming',
      start_time TIMESTAMP,
      end_time TIMESTAMP,
      winner_address VARCHAR(42),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_status (status),
      INDEX idx_start (start_time)
    )
  `);
  console.log('✅ Tournaments table created');

  // Create tournament_participants table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS tournament_participants (
      id INT AUTO_INCREMENT PRIMARY KEY,
      tournament_id INT NOT NULL,
      user_address VARCHAR(42) NOT NULL,
      heroes JSON NOT NULL,
      score INT DEFAULT 0,
      rank_position INT,
      eliminated BOOLEAN DEFAULT FALSE,
      joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY unique_participation (tournament_id, user_address),
      INDEX idx_tournament (tournament_id),
      INDEX idx_user (user_address),
      INDEX idx_score (score),
      FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
      FOREIGN KEY (user_address) REFERENCES users(wallet_address) ON DELETE CASCADE
    )
  `);
  console.log('✅ Tournament participants table created');

  // Create hero_skills table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS hero_skills (
      id INT AUTO_INCREMENT PRIMARY KEY,
      hero_id INT NOT NULL,
      skill_name VARCHAR(50) NOT NULL,
      skill_type ENUM('attack', 'defense', 'support', 'ultimate') NOT NULL,
      damage INT DEFAULT 0,
      cooldown INT DEFAULT 1,
      mana_cost INT DEFAULT 0,
      is_unlocked BOOLEAN DEFAULT FALSE,
      unlock_level INT DEFAULT 1,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_hero (hero_id),
      INDEX idx_type (skill_type),
      FOREIGN KEY (hero_id) REFERENCES heroes(id) ON DELETE CASCADE
    )
  `);
  console.log('✅ Hero skills table created');

  // Create daily_quests table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS daily_quests (
      id INT AUTO_INCREMENT PRIMARY KEY,
      user_address VARCHAR(42) NOT NULL,
      quest_type ENUM('win_battles', 'upgrade_hero', 'complete_pve', 'earn_tokens') NOT NULL,
      target_value INT NOT NULL,
      current_progress INT DEFAULT 0,
      reward_amount DECIMAL(20, 18) NOT NULL,
      is_completed BOOLEAN DEFAULT FALSE,
      quest_date DATE NOT NULL,
      completed_at TIMESTAMP NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE KEY unique_daily_quest (user_address, quest_type, quest_date),
      INDEX idx_user (user_address),
      INDEX idx_date (quest_date),
      INDEX idx_completed (is_completed),
      FOREIGN KEY (user_address) REFERENCES users(wallet_address) ON DELETE CASCADE
    )
  `);
  console.log('✅ Daily quests table created');

  // Create default_hero_skills table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS default_hero_skills (
      id INT AUTO_INCREMENT PRIMARY KEY,
      hero_type ENUM('warrior', 'mage', 'archer', 'assassin', 'tank') NOT NULL,
      skill_name VARCHAR(50) NOT NULL,
      skill_type ENUM('attack', 'defense', 'support', 'ultimate') NOT NULL,
      damage INT DEFAULT 0,
      cooldown INT DEFAULT 1,
      mana_cost INT DEFAULT 0,
      unlock_level INT DEFAULT 1,
      INDEX idx_hero_type (hero_type)
    )
  `);
  console.log('✅ Default hero skills table created');
}

async function insertDefaultData(connection) {
  console.log('📝 Inserting default hero skills...');
  
  // Insert sample hero skills for each hero type
  const heroSkills = [
    // Warrior skills
    { hero_type: 'warrior', skill_name: 'Sword Strike', skill_type: 'attack', damage: 25, cooldown: 1 },
    { hero_type: 'warrior', skill_name: 'Shield Bash', skill_type: 'attack', damage: 20, cooldown: 2 },
    { hero_type: 'warrior', skill_name: 'Berserker Rage', skill_type: 'ultimate', damage: 50, cooldown: 5 },
    
    // Mage skills
    { hero_type: 'mage', skill_name: 'Fireball', skill_type: 'attack', damage: 30, cooldown: 2 },
    { hero_type: 'mage', skill_name: 'Ice Shield', skill_type: 'defense', damage: 0, cooldown: 3 },
    { hero_type: 'mage', skill_name: 'Meteor', skill_type: 'ultimate', damage: 60, cooldown: 6 },
    
    // Archer skills
    { hero_type: 'archer', skill_name: 'Quick Shot', skill_type: 'attack', damage: 22, cooldown: 1 },
    { hero_type: 'archer', skill_name: 'Poison Arrow', skill_type: 'attack', damage: 18, cooldown: 3 },
    { hero_type: 'archer', skill_name: 'Rain of Arrows', skill_type: 'ultimate', damage: 45, cooldown: 5 },
    
    // Assassin skills
    { hero_type: 'assassin', skill_name: 'Backstab', skill_type: 'attack', damage: 35, cooldown: 2 },
    { hero_type: 'assassin', skill_name: 'Stealth', skill_type: 'support', damage: 0, cooldown: 4 },
    { hero_type: 'assassin', skill_name: 'Shadow Strike', skill_type: 'ultimate', damage: 55, cooldown: 5 },
    
    // Tank skills
    { hero_type: 'tank', skill_name: 'Taunt', skill_type: 'support', damage: 0, cooldown: 2 },
    { hero_type: 'tank', skill_name: 'Shield Wall', skill_type: 'defense', damage: 0, cooldown: 3 },
    { hero_type: 'tank', skill_name: 'Ground Slam', skill_type: 'ultimate', damage: 40, cooldown: 4 }
  ];

  for (const skill of heroSkills) {
    await connection.query(`
      INSERT IGNORE INTO default_hero_skills 
      (hero_type, skill_name, skill_type, damage, cooldown, mana_cost, unlock_level)
      VALUES (?, ?, ?, ?, ?, 0, 1)
    `, [skill.hero_type, skill.skill_name, skill.skill_type, skill.damage, skill.cooldown]);
  }

  console.log('✅ Default hero skills inserted');
}

// Run migration
if (require.main === module) {
  createDatabase()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { createDatabase };
