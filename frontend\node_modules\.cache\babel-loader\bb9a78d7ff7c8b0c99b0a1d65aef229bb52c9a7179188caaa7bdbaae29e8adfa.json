{"ast": null, "code": "/**\n *  A constant for the zero hash.\n *\n *  (**i.e.** ``\"0x0000000000000000000000000000000000000000000000000000000000000000\"``)\n */\nexport const ZeroHash = \"0x0000000000000000000000000000000000000000000000000000000000000000\";", "map": {"version": 3, "names": ["ZeroHash"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\constants\\hashes.ts"], "sourcesContent": ["/**\n *  A constant for the zero hash.\n *\n *  (**i.e.** ``\"0x0000000000000000000000000000000000000000000000000000000000000000\"``)\n */\nexport const ZeroHash: string = \"0x0000000000000000000000000000000000000000000000000000000000000000\";\n\n"], "mappings": "AAAA;;;;;AAKA,OAAO,MAAMA,QAAQ,GAAW,oEAAoE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}