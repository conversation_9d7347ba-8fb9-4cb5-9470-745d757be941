{"ast": null, "code": "import { keccak256 } from \"../crypto/index.js\";\nimport { toUtf8Bytes } from \"../utils/index.js\";\n/**\n *  A simple hashing function which operates on UTF-8 strings to\n *  compute an 32-byte identifier.\n *\n *  This simply computes the [UTF-8 bytes](toUtf8Bytes) and computes\n *  the [[keccak256]].\n *\n *  @example:\n *    id(\"hello world\")\n *    //_result:\n */\nexport function id(value) {\n  return keccak256(toUtf8Bytes(value));\n}", "map": {"version": 3, "names": ["keccak256", "toUtf8Bytes", "id", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\id.ts"], "sourcesContent": ["import { keccak256 } from \"../crypto/index.js\";\nimport { toUtf8Bytes } from \"../utils/index.js\";\n\n/**\n *  A simple hashing function which operates on UTF-8 strings to\n *  compute an 32-byte identifier.\n *\n *  This simply computes the [UTF-8 bytes](toUtf8Bytes) and computes\n *  the [[keccak256]].\n *\n *  @example:\n *    id(\"hello world\")\n *    //_result:\n */\nexport function id(value: string): string {\n    return keccak256(toUtf8Bytes(value));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,WAAW,QAAQ,mBAAmB;AAE/C;;;;;;;;;;;AAWA,OAAM,SAAUC,EAAEA,CAACC,KAAa;EAC5B,OAAOH,SAAS,CAACC,WAAW,CAACE,KAAK,CAAC,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}