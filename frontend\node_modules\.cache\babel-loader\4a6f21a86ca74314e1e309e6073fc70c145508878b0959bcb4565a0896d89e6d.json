{"ast": null, "code": "/**\n *  A **Password-Based Key-Derivation Function** is designed to create\n *  a sequence of bytes suitible as a **key** from a human-rememberable\n *  password.\n *\n *  @_subsection: api/crypto:Passwords  [about-pbkdf]\n */\nimport { pbkdf2Sync } from \"./crypto.js\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\nlet locked = false;\nconst _pbkdf2 = function (password, salt, iterations, keylen, algo) {\n  return pbkdf2Sync(password, salt, iterations, keylen, algo);\n};\nlet __pbkdf2 = _pbkdf2;\n/**\n *  Return the [[link-pbkdf2]] for %%keylen%% bytes for %%password%% using\n *  the %%salt%% and using %%iterations%% of %%algo%%.\n *\n *  This PBKDF is outdated and should not be used in new projects, but is\n *  required to decrypt older files.\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the PBKDF2\n *    pbkdf2(passwordBytes, salt, 1024, 16, \"sha256\")\n *    //_result:\n */\nexport function pbkdf2(_password, _salt, iterations, keylen, algo) {\n  const password = getBytes(_password, \"password\");\n  const salt = getBytes(_salt, \"salt\");\n  return hexlify(__pbkdf2(password, salt, iterations, keylen, algo));\n}\npbkdf2._ = _pbkdf2;\npbkdf2.lock = function () {\n  locked = true;\n};\npbkdf2.register = function (func) {\n  if (locked) {\n    throw new Error(\"pbkdf2 is locked\");\n  }\n  __pbkdf2 = func;\n};\nObject.freeze(pbkdf2);", "map": {"version": 3, "names": ["pbkdf2Sync", "getBytes", "hexlify", "locked", "_pbkdf2", "password", "salt", "iterations", "keylen", "algo", "__pbkdf2", "pbkdf2", "_password", "_salt", "_", "lock", "register", "func", "Error", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\pbkdf2.ts"], "sourcesContent": ["/**\n *  A **Password-Based Key-Derivation Function** is designed to create\n *  a sequence of bytes suitible as a **key** from a human-rememberable\n *  password.\n *\n *  @_subsection: api/crypto:Passwords  [about-pbkdf]\n */\n\nimport { pbkdf2Sync } from \"./crypto.js\";\n\nimport { getBytes, hexlify } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n\nlet locked = false;\n\nconst _pbkdf2 = function(password: Uint8Array, salt: Uint8Array, iterations: number, keylen: number, algo: \"sha256\" | \"sha512\"): BytesLike {\n    return pbkdf2Sync(password, salt, iterations, keylen, algo);\n}\n\nlet __pbkdf2 = _pbkdf2;\n\n/**\n *  Return the [[link-pbkdf2]] for %%keylen%% bytes for %%password%% using\n *  the %%salt%% and using %%iterations%% of %%algo%%.\n *\n *  This PBKDF is outdated and should not be used in new projects, but is\n *  required to decrypt older files.\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the PBKDF2\n *    pbkdf2(passwordBytes, salt, 1024, 16, \"sha256\")\n *    //_result:\n */\nexport function pbkdf2(_password: BytesLike, _salt: BytesLike, iterations: number, keylen: number, algo: \"sha256\" | \"sha512\"): string {\n    const password = getBytes(_password, \"password\");\n    const salt = getBytes(_salt, \"salt\");\n    return hexlify(__pbkdf2(password, salt, iterations, keylen, algo));\n}\npbkdf2._ = _pbkdf2;\npbkdf2.lock = function(): void { locked = true; }\npbkdf2.register = function(func: (password: Uint8Array, salt: Uint8Array, iterations: number, keylen: number, algo: \"sha256\" | \"sha512\") => BytesLike) {\n    if (locked) { throw new Error(\"pbkdf2 is locked\"); }\n    __pbkdf2 = func;\n}\nObject.freeze(pbkdf2);\n"], "mappings": "AAAA;;;;;;;AAQA,SAASA,UAAU,QAAQ,aAAa;AAExC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mBAAmB;AAKrD,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,OAAO,GAAG,SAAAA,CAASC,QAAoB,EAAEC,IAAgB,EAAEC,UAAkB,EAAEC,MAAc,EAAEC,IAAyB;EAC1H,OAAOT,UAAU,CAACK,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,CAAC;AAC/D,CAAC;AAED,IAAIC,QAAQ,GAAGN,OAAO;AAEtB;;;;;;;;;;;;;;;;;;;;AAoBA,OAAM,SAAUO,MAAMA,CAACC,SAAoB,EAAEC,KAAgB,EAAEN,UAAkB,EAAEC,MAAc,EAAEC,IAAyB;EACxH,MAAMJ,QAAQ,GAAGJ,QAAQ,CAACW,SAAS,EAAE,UAAU,CAAC;EAChD,MAAMN,IAAI,GAAGL,QAAQ,CAACY,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOX,OAAO,CAACQ,QAAQ,CAACL,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,IAAI,CAAC,CAAC;AACtE;AACAE,MAAM,CAACG,CAAC,GAAGV,OAAO;AAClBO,MAAM,CAACI,IAAI,GAAG;EAAmBZ,MAAM,GAAG,IAAI;AAAE,CAAC;AACjDQ,MAAM,CAACK,QAAQ,GAAG,UAASC,IAA0H;EACjJ,IAAId,MAAM,EAAE;IAAE,MAAM,IAAIe,KAAK,CAAC,kBAAkB,CAAC;;EACjDR,QAAQ,GAAGO,IAAI;AACnB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACT,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}