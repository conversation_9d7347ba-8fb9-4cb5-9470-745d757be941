{"ast": null, "code": "/**\n *  An **HMAC** enables verification that a given key was used\n *  to authenticate a payload.\n *\n *  See: [[link-wiki-hmac]]\n *\n *  @_subsection: api/crypto:HMAC  [about-hmac]\n */\nimport { createHmac } from \"./crypto.js\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\nlet locked = false;\nconst _computeHmac = function (algorithm, key, data) {\n  return createHmac(algorithm, key).update(data).digest();\n};\nlet __computeHmac = _computeHmac;\n/**\n *  Return the HMAC for %%data%% using the %%key%% key with the underlying\n *  %%algo%% used for compression.\n *\n *  @example:\n *    key = id(\"some-secret\")\n *\n *    // Compute the HMAC\n *    computeHmac(\"sha256\", key, \"0x1337\")\n *    //_result:\n *\n *    // To compute the HMAC of UTF-8 data, the data must be\n *    // converted to UTF-8 bytes\n *    computeHmac(\"sha256\", key, toUtf8Bytes(\"Hello World\"))\n *    //_result:\n *\n */\nexport function computeHmac(algorithm, _key, _data) {\n  const key = getBytes(_key, \"key\");\n  const data = getBytes(_data, \"data\");\n  return hexlify(__computeHmac(algorithm, key, data));\n}\ncomputeHmac._ = _computeHmac;\ncomputeHmac.lock = function () {\n  locked = true;\n};\ncomputeHmac.register = function (func) {\n  if (locked) {\n    throw new Error(\"computeHmac is locked\");\n  }\n  __computeHmac = func;\n};\nObject.freeze(computeHmac);", "map": {"version": 3, "names": ["createHmac", "getBytes", "hexlify", "locked", "_computeHmac", "algorithm", "key", "data", "update", "digest", "__computeHmac", "computeHmac", "_key", "_data", "_", "lock", "register", "func", "Error", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\hmac.ts"], "sourcesContent": ["/**\n *  An **HMAC** enables verification that a given key was used\n *  to authenticate a payload.\n *\n *  See: [[link-wiki-hmac]]\n *\n *  @_subsection: api/crypto:HMAC  [about-hmac]\n */\nimport { createHmac } from \"./crypto.js\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n\nlet locked = false;\n\nconst _computeHmac = function(algorithm: \"sha256\" | \"sha512\", key: Uint8Array, data: Uint8Array): BytesLike {\n    return createHmac(algorithm, key).update(data).digest();\n}\n\nlet __computeHmac = _computeHmac;\n\n/**\n *  Return the HMAC for %%data%% using the %%key%% key with the underlying\n *  %%algo%% used for compression.\n *\n *  @example:\n *    key = id(\"some-secret\")\n *\n *    // Compute the HMAC\n *    computeHmac(\"sha256\", key, \"0x1337\")\n *    //_result:\n *\n *    // To compute the HMAC of UTF-8 data, the data must be\n *    // converted to UTF-8 bytes\n *    computeHmac(\"sha256\", key, toUtf8Bytes(\"Hello World\"))\n *    //_result:\n *\n */\nexport function computeHmac(algorithm: \"sha256\" | \"sha512\", _key: BytesLike, _data: BytesLike): string {\n    const key = getBytes(_key, \"key\");\n    const data = getBytes(_data, \"data\");\n    return hexlify(__computeHmac(algorithm, key, data));\n}\ncomputeHmac._ = _computeHmac;\ncomputeHmac.lock =  function() { locked = true; }\ncomputeHmac.register = function(func: (algorithm: \"sha256\" | \"sha512\", key: Uint8Array, data: Uint8Array) => BytesLike) {\n    if (locked) { throw new Error(\"computeHmac is locked\"); }\n    __computeHmac = func;\n}\nObject.freeze(computeHmac);\n"], "mappings": "AAAA;;;;;;;;AAQA,SAASA,UAAU,QAAQ,aAAa;AACxC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mBAAmB;AAKrD,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,YAAY,GAAG,SAAAA,CAASC,SAA8B,EAAEC,GAAe,EAAEC,IAAgB;EAC3F,OAAOP,UAAU,CAACK,SAAS,EAAEC,GAAG,CAAC,CAACE,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,EAAE;AAC3D,CAAC;AAED,IAAIC,aAAa,GAAGN,YAAY;AAEhC;;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAUO,WAAWA,CAACN,SAA8B,EAAEO,IAAe,EAAEC,KAAgB;EACzF,MAAMP,GAAG,GAAGL,QAAQ,CAACW,IAAI,EAAE,KAAK,CAAC;EACjC,MAAML,IAAI,GAAGN,QAAQ,CAACY,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOX,OAAO,CAACQ,aAAa,CAACL,SAAS,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC;AACvD;AACAI,WAAW,CAACG,CAAC,GAAGV,YAAY;AAC5BO,WAAW,CAACI,IAAI,GAAI;EAAaZ,MAAM,GAAG,IAAI;AAAE,CAAC;AACjDQ,WAAW,CAACK,QAAQ,GAAG,UAASC,IAAsF;EAClH,IAAId,MAAM,EAAE;IAAE,MAAM,IAAIe,KAAK,CAAC,uBAAuB,CAAC;;EACtDR,aAAa,GAAGO,IAAI;AACxB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACT,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}