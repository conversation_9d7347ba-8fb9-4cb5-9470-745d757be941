{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass } from './abstract/weierstrass.js';\n// connects noble-curves to noble-hashes\nexport function getHash(hash) {\n  return {\n    hash,\n    hmac: (key, ...msgs) => hmac(hash, key, concatBytes(...msgs)),\n    randomBytes\n  };\n}\nexport function createCurve(curveDef, defHash) {\n  const create = hash => weierstrass({\n    ...curveDef,\n    ...getHash(hash)\n  });\n  return Object.freeze({\n    ...create(defHash),\n    create\n  });\n}", "map": {"version": 3, "names": ["hmac", "concatBytes", "randomBytes", "<PERSON><PERSON><PERSON><PERSON>", "getHash", "hash", "key", "msgs", "createCurve", "curveDef", "defHash", "create", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\_shortw_utils.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { hmac } from '@noble/hashes/hmac';\nimport { concatBytes, randomBytes } from '@noble/hashes/utils';\nimport { weierstrass, CurveType } from './abstract/weierstrass.js';\nimport { CHash } from './abstract/utils.js';\n\n// connects noble-curves to noble-hashes\nexport function getHash(hash: CHash) {\n  return {\n    hash,\n    hmac: (key: Uint8Array, ...msgs: Uint8Array[]) => hmac(hash, key, concatBytes(...msgs)),\n    randomBytes,\n  };\n}\n// Same API as @noble/hashes, with ability to create curve with custom hash\ntype CurveDef = Readonly<Omit<CurveType, 'hash' | 'hmac' | 'randomBytes'>>;\nexport function createCurve(curveDef: CurveDef, defHash: CHash) {\n  const create = (hash: CHash) => weierstrass({ ...curveDef, ...getHash(hash) });\n  return Object.freeze({ ...create(defHash), create });\n}\n"], "mappings": "AAAA;AACA,SAASA,IAAI,QAAQ,oBAAoB;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,qBAAqB;AAC9D,SAASC,WAAW,QAAmB,2BAA2B;AAGlE;AACA,OAAM,SAAUC,OAAOA,CAACC,IAAW;EACjC,OAAO;IACLA,IAAI;IACJL,IAAI,EAAEA,CAACM,GAAe,EAAE,GAAGC,IAAkB,KAAKP,IAAI,CAACK,IAAI,EAAEC,GAAG,EAAEL,WAAW,CAAC,GAAGM,IAAI,CAAC,CAAC;IACvFL;GACD;AACH;AAGA,OAAM,SAAUM,WAAWA,CAACC,QAAkB,EAAEC,OAAc;EAC5D,MAAMC,MAAM,GAAIN,IAAW,IAAKF,WAAW,CAAC;IAAE,GAAGM,QAAQ;IAAE,GAAGL,OAAO,CAACC,IAAI;EAAC,CAAE,CAAC;EAC9E,OAAOO,MAAM,CAACC,MAAM,CAAC;IAAE,GAAGF,MAAM,CAACD,OAAO,CAAC;IAAEC;EAAM,CAAE,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}