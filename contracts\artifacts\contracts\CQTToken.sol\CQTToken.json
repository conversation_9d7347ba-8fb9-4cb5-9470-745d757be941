{"_format": "hh-sol-artifact-1", "contractName": "CQTToken", "sourceName": "contracts/CQTToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_playToEarnPool", "type": "address"}, {"internalType": "address", "name": "_ecosystemFund", "type": "address"}, {"internalType": "address", "name": "_teamWallet", "type": "address"}, {"internalType": "address", "name": "_marketingWallet", "type": "address"}, {"internalType": "address", "name": "_stakingPool", "type": "address"}, {"internalType": "address", "name": "_liquidityPool", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "gameContract", "type": "address"}], "name": "GameContractAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "gameContract", "type": "address"}], "name": "GameContractRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "TokensDistributed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "ECOSYSTEM_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LIQUIDITY_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MARKETING_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PLAY_TO_EARN_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "STAKING_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TEAM_ALLOCATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_gameContract", "type": "address"}], "name": "addGameContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_from", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "burnFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_player", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "string", "name": "_reason", "type": "string"}], "name": "distributeReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "ecosystemFund", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "gameContracts", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPlayToEarnBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "liquidityPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "marketingWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "playToEarnPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_gameContract", "type": "address"}], "name": "removeGameContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakingPool", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "teamWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60806040523480156200001157600080fd5b506040516200185e3803806200185e83398101604081905262000034916200046a565b336040518060400160405280601181526020017021b93cb83a37a8bab2b9ba102a37b5b2b760791b8152506040518060400160405280600381526020016210d45560ea1b81525081600390816200008c91906200058f565b5060046200009b82826200058f565b5050506001600160a01b038116620000ce57604051631e4fbdf760e01b8152600060048201526024015b60405180910390fd5b620000d98162000239565b50600680546001600160a01b03199081166001600160a01b0389811691909117909255600780548216888416179055600880548216878416179055600980548216868416179055600a80548216858416179055600b805490911691831691909117905562000154866b014adf4b7320334b900000006200028b565b6200016b856aa56fa5b99019a5c80000006200028b565b62000182846a7c13bc4b2c133c560000006200028b565b62000199836a52b7d2dcc80cd2e40000006200028b565b620001b0826a52b7d2dcc80cd2e40000006200028b565b620001c7816a295be96e640669720000006200028b565b6b033b2e3c9fd0803ce8000000620001de60025490565b146200022d5760405162461bcd60e51b815260206004820152601560248201527f546f74616c20737570706c79206d69736d6174636800000000000000000000006044820152606401620000c5565b50505050505062000683565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b6001600160a01b038216620002b75760405163ec442f0560e01b815260006004820152602401620000c5565b620002c560008383620002c9565b5050565b620002d3620002e5565b620002e08383836200031a565b505050565b620002f9600554600160a01b900460ff1690565b15620003185760405163d93c066560e01b815260040160405180910390fd5b565b6001600160a01b038316620003495780600260008282546200033d91906200065b565b90915550620003bd9050565b6001600160a01b038316600090815260208190526040902054818110156200039e5760405163391434e360e21b81526001600160a01b03851660048201526024810182905260448101839052606401620000c5565b6001600160a01b03841660009081526020819052604090209082900390555b6001600160a01b038216620003db57600280548290039055620003fa565b6001600160a01b03821660009081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef836040516200044091815260200190565b60405180910390a3505050565b80516001600160a01b03811681146200046557600080fd5b919050565b60008060008060008060c087890312156200048457600080fd5b6200048f876200044d565b95506200049f602088016200044d565b9450620004af604088016200044d565b9350620004bf606088016200044d565b9250620004cf608088016200044d565b9150620004df60a088016200044d565b90509295509295509295565b634e487b7160e01b600052604160045260246000fd5b600181811c908216806200051657607f821691505b6020821081036200053757634e487b7160e01b600052602260045260246000fd5b50919050565b601f821115620002e057600081815260208120601f850160051c81016020861015620005665750805b601f850160051c820191505b81811015620005875782815560010162000572565b505050505050565b81516001600160401b03811115620005ab57620005ab620004eb565b620005c381620005bc845462000501565b846200053d565b602080601f831160018114620005fb5760008415620005e25750858301515b600019600386901b1c1916600185901b17855562000587565b600085815260208120601f198616915b828110156200062c578886015182559484019460019091019084016200060b565b50858210156200064b5787850151600019600388901b60f8161c191681555b5050505050600190811b01905550565b808201808211156200067d57634e487b7160e01b600052601160045260246000fd5b92915050565b6111cb80620006936000396000f3fe608060405234801561001057600080fd5b506004361061021c5760003560e01c806379cc679011610125578063b99dcae5116100ad578063e25a04c81161007c578063e25a04c8146104b3578063e63ea408146104c6578063f2fde38b146104d9578063f9428f38146104ec578063fd99cbed146104fe57600080fd5b8063b99dcae5146103f5578063db01351114610447578063dd62ed3e1461045a578063df7ec2761461049357600080fd5b80638e80bd39116100f45780638e80bd39146103f5578063902d55a51461040757806390534f241461041a57806395d89b411461042c578063a9059cbb1461043457600080fd5b806379cc6790146103b657806381cd08ee146103c95780638456cb59146103dc5780638da5cb5b146103e457600080fd5b806342966c68116101a8578063665a11ca11610177578063665a11ca1461033c5780636ddd474d1461034f57806370a0823114610372578063715018a61461039b57806375f0a874146103a357600080fd5b806342966c68146102f157806359927044146103045780635c74e025146103175780635c975abb1461032a57600080fd5b80631d0cf683116101ef5780631d0cf6831461029f57806323b872dd146102b25780632a7ea9d2146102c5578063313ce567146102da5780633f4ba83a146102e957600080fd5b806306fdde0314610221578063095ea7b31461023f5780630c56ae3b1461026257806318160ddd1461028d575b600080fd5b610229610510565b6040516102369190610f13565b60405180910390f35b61025261024d366004610f49565b6105a2565b6040519015158152602001610236565b600a54610275906001600160a01b031681565b6040516001600160a01b039091168152602001610236565b6002545b604051908152602001610236565b600754610275906001600160a01b031681565b6102526102c0366004610f73565b6105bc565b6102d86102d3366004610fc5565b6105e0565b005b60405160128152602001610236565b6102d8610742565b6102d86102ff366004611090565b610754565b600854610275906001600160a01b031681565b6102d86103253660046110a9565b610761565b600554600160a01b900460ff16610252565b600b54610275906001600160a01b031681565b61025261035d3660046110a9565b600c6020526000908152604090205460ff1681565b6102916103803660046110a9565b6001600160a01b031660009081526020819052604090205490565b6102d86107b2565b600954610275906001600160a01b031681565b6102d86103c4366004610f49565b6107c4565b6102d86103d73660046110a9565b61083d565b6102d86108d9565b6005546001600160a01b0316610275565b6102916a52b7d2dcc80cd2e400000081565b6102916b033b2e3c9fd0803ce800000081565b6102916aa56fa5b99019a5c800000081565b6102296108e9565b610252610442366004610f49565b6108f8565b600654610275906001600160a01b031681565b6102916104683660046110c4565b6001600160a01b03918216600090815260016020908152604080832093909416825291909152205490565b6006546001600160a01b0316600090815260208190526040902054610291565b6102916b014adf4b7320334b9000000081565b6102d86104d4366004610f73565b610906565b6102d86104e73660046110a9565b6109df565b6102916a295be96e6406697200000081565b6102916a7c13bc4b2c133c5600000081565b60606003805461051f906110f7565b80601f016020809104026020016040519081016040528092919081815260200182805461054b906110f7565b80156105985780601f1061056d57610100808354040283529160200191610598565b820191906000526020600020905b81548152906001019060200180831161057b57829003601f168201915b5050505050905090565b6000336105b0818585610a1a565b60019150505b92915050565b6000336105ca858285610a2c565b6105d5858585610aa5565b506001949350505050565b336000908152600c602052604090205460ff166106445760405162461bcd60e51b815260206004820152601c60248201527f4e6f7420617574686f72697a65642067616d6520636f6e74726163740000000060448201526064015b60405180910390fd5b6001600160a01b0383166106935760405162461bcd60e51b8152602060048201526016602482015275496e76616c696420706c61796572206164647265737360501b604482015260640161063b565b600082116106e35760405162461bcd60e51b815260206004820152601d60248201527f416d6f756e74206d7573742062652067726561746572207468616e2030000000604482015260640161063b565b6006546106fa906001600160a01b03168484610aa5565b826001600160a01b03167f56c502c6e99329b8657fcef46fe4aaa9cd5d10463c1bd534ec0a09e31c09ae398383604051610735929190611131565b60405180910390a2505050565b61074a610b04565b610752610b31565b565b61075e3382610b86565b50565b610769610b04565b6001600160a01b0381166000818152600c6020526040808220805460ff19169055517f2787914e393eae017c5d63972ae40b8a6e095f82f7c2df7b8cc0947364f44c3b9190a250565b6107ba610b04565b6107526000610bbc565b336000908152600c602052604090205460ff16806107ea57506001600160a01b03821633145b61082f5760405162461bcd60e51b81526020600482015260166024820152752737ba1030baba3437b934bd32b2103a3790313ab93760511b604482015260640161063b565b6108398282610c0e565b5050565b610845610b04565b6001600160a01b03811661088d5760405162461bcd60e51b815260206004820152600f60248201526e496e76616c6964206164647265737360881b604482015260640161063b565b6001600160a01b0381166000818152600c6020526040808220805460ff19166001179055517f6c714ab98f337e5ba3203dce1dd577e948f63b10c5300a075c35c4df3a2700679190a250565b6108e1610b04565b610752610c23565b60606004805461051f906110f7565b6000336105b0818585610aa5565b61090e610b04565b306001600160a01b038416036109665760405162461bcd60e51b815260206004820152601a60248201527f43616e6e6f742077697468647261772043515420746f6b656e73000000000000604482015260640161063b565b60405163a9059cbb60e01b81526001600160a01b0383811660048301526024820183905284169063a9059cbb906044016020604051808303816000875af11580156109b5573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906109d99190611152565b50505050565b6109e7610b04565b6001600160a01b038116610a1157604051631e4fbdf760e01b81526000600482015260240161063b565b61075e81610bbc565b610a278383836001610c66565b505050565b6001600160a01b038381166000908152600160209081526040808320938616835292905220546000198110156109d95781811015610a9657604051637dc7a0d960e11b81526001600160a01b0384166004820152602481018290526044810183905260640161063b565b6109d984848484036000610c66565b6001600160a01b038316610acf57604051634b637e8f60e11b81526000600482015260240161063b565b6001600160a01b038216610af95760405163ec442f0560e01b81526000600482015260240161063b565b610a27838383610d3b565b6005546001600160a01b031633146107525760405163118cdaa760e01b815233600482015260240161063b565b610b39610d4e565b6005805460ff60a01b191690557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a1565b6001600160a01b038216610bb057604051634b637e8f60e11b81526000600482015260240161063b565b61083982600083610d3b565b600580546001600160a01b038381166001600160a01b0319831681179093556040519116919082907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e090600090a35050565b610c19823383610a2c565b6108398282610b86565b610c2b610d78565b6005805460ff60a01b1916600160a01b1790557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258610b693390565b6001600160a01b038416610c905760405163e602df0560e01b81526000600482015260240161063b565b6001600160a01b038316610cba57604051634a1406b160e11b81526000600482015260240161063b565b6001600160a01b03808516600090815260016020908152604080832093871683529290522082905580156109d957826001600160a01b0316846001600160a01b03167f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b92584604051610d2d91815260200190565b60405180910390a350505050565b610d43610d78565b610a27838383610da3565b600554600160a01b900460ff1661075257604051638dfc202b60e01b815260040160405180910390fd5b600554600160a01b900460ff16156107525760405163d93c066560e01b815260040160405180910390fd5b6001600160a01b038316610dce578060026000828254610dc39190611174565b90915550610e409050565b6001600160a01b03831660009081526020819052604090205481811015610e215760405163391434e360e21b81526001600160a01b0385166004820152602481018290526044810183905260640161063b565b6001600160a01b03841660009081526020819052604090209082900390555b6001600160a01b038216610e5c57600280548290039055610e7b565b6001600160a01b03821660009081526020819052604090208054820190555b816001600160a01b0316836001600160a01b03167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef83604051610ec091815260200190565b60405180910390a3505050565b6000815180845260005b81811015610ef357602081850181015186830182015201610ed7565b506000602082860101526020601f19601f83011685010191505092915050565b602081526000610f266020830184610ecd565b9392505050565b80356001600160a01b0381168114610f4457600080fd5b919050565b60008060408385031215610f5c57600080fd5b610f6583610f2d565b946020939093013593505050565b600080600060608486031215610f8857600080fd5b610f9184610f2d565b9250610f9f60208501610f2d565b9150604084013590509250925092565b634e487b7160e01b600052604160045260246000fd5b600080600060608486031215610fda57600080fd5b610fe384610f2d565b925060208401359150604084013567ffffffffffffffff8082111561100757600080fd5b818601915086601f83011261101b57600080fd5b81358181111561102d5761102d610faf565b604051601f8201601f19908116603f0116810190838211818310171561105557611055610faf565b8160405282815289602084870101111561106e57600080fd5b8260208601602083013760006020848301015280955050505050509250925092565b6000602082840312156110a257600080fd5b5035919050565b6000602082840312156110bb57600080fd5b610f2682610f2d565b600080604083850312156110d757600080fd5b6110e083610f2d565b91506110ee60208401610f2d565b90509250929050565b600181811c9082168061110b57607f821691505b60208210810361112b57634e487b7160e01b600052602260045260246000fd5b50919050565b82815260406020820152600061114a6040830184610ecd565b949350505050565b60006020828403121561116457600080fd5b81518015158114610f2657600080fd5b808201808211156105b657634e487b7160e01b600052601160045260246000fdfea2646970667358221220506a4e2855106e855247190ceb07bb668b904a2f312dc23a9737748da4db7c7464736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}