{"ast": null, "code": "// Cipher Block Chaining\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _CBC_iv, _CBC_lastBlock;\nimport { ModeOfOperation } from \"./mode.js\";\nexport class CBC extends ModeOfOperation {\n  constructor(key, iv) {\n    super(\"ECC\", key, CBC);\n    _CBC_iv.set(this, void 0);\n    _CBC_lastBlock.set(this, void 0);\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      __classPrivateFieldSet(this, _CBC_iv, new Uint8Array(iv), \"f\");\n    } else {\n      __classPrivateFieldSet(this, _CBC_iv, new Uint8Array(16), \"f\");\n    }\n    __classPrivateFieldSet(this, _CBC_lastBlock, this.iv, \"f\");\n  }\n  get iv() {\n    return new Uint8Array(__classPrivateFieldGet(this, _CBC_iv, \"f\"));\n  }\n  encrypt(plaintext) {\n    if (plaintext.length % 16) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n    const ciphertext = new Uint8Array(plaintext.length);\n    for (let i = 0; i < plaintext.length; i += 16) {\n      for (let j = 0; j < 16; j++) {\n        __classPrivateFieldGet(this, _CBC_lastBlock, \"f\")[j] ^= plaintext[i + j];\n      }\n      __classPrivateFieldSet(this, _CBC_lastBlock, this.aes.encrypt(__classPrivateFieldGet(this, _CBC_lastBlock, \"f\")), \"f\");\n      ciphertext.set(__classPrivateFieldGet(this, _CBC_lastBlock, \"f\"), i);\n    }\n    return ciphertext;\n  }\n  decrypt(ciphertext) {\n    if (ciphertext.length % 16) {\n      throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n    const plaintext = new Uint8Array(ciphertext.length);\n    for (let i = 0; i < ciphertext.length; i += 16) {\n      const block = this.aes.decrypt(ciphertext.subarray(i, i + 16));\n      for (let j = 0; j < 16; j++) {\n        plaintext[i + j] = block[j] ^ __classPrivateFieldGet(this, _CBC_lastBlock, \"f\")[j];\n        __classPrivateFieldGet(this, _CBC_lastBlock, \"f\")[j] = ciphertext[i + j];\n      }\n    }\n    return plaintext;\n  }\n}\n_CBC_iv = new WeakMap(), _CBC_lastBlock = new WeakMap();", "map": {"version": 3, "names": ["ModeOfOperation", "CBC", "constructor", "key", "iv", "_CBC_iv", "set", "_CBC_lastBlock", "length", "TypeError", "__classPrivateFieldSet", "Uint8Array", "__classPrivateFieldGet", "encrypt", "plaintext", "ciphertext", "i", "j", "aes", "decrypt", "block", "subarray"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode-cbc.ts"], "sourcesContent": ["// Cipher Block Chaining\n\nimport { ModeOfOperation } from \"./mode.js\";\n\nexport class CBC extends ModeOfOperation {\n  #iv: Uint8Array;\n  #lastBlock: Uint8Array;\n\n  constructor(key: Uint8Array, iv?: Uint8Array) {\n    super(\"ECC\", key, CBC);\n\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      this.#iv = new Uint8Array(iv);\n    } else {\n      this.#iv = new Uint8Array(16);\n    }\n\n    this.#lastBlock = this.iv;\n  }\n\n  get iv(): Uint8Array { return new Uint8Array(this.#iv); }\n\n  encrypt(plaintext: Uint8Array): Uint8Array {\n    if (plaintext.length % 16) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n\n    const ciphertext = new Uint8Array(plaintext.length);\n    for (let i = 0; i < plaintext.length; i += 16) {\n      for (let j = 0; j < 16; j++) {\n        this.#lastBlock[j] ^= plaintext[i + j];\n      }\n\n      this.#lastBlock = this.aes.encrypt(this.#lastBlock);\n      ciphertext.set(this.#lastBlock, i);\n    }\n\n    return ciphertext;\n  }\n\n  decrypt(ciphertext: Uint8Array): Uint8Array {\n    if (ciphertext.length % 16) {\n        throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n\n    const plaintext = new Uint8Array(ciphertext.length);\n    for (let i = 0; i < ciphertext.length; i += 16) {\n        const block = this.aes.decrypt(ciphertext.subarray(i, i + 16));\n\n        for (let j = 0; j < 16; j++) {\n          plaintext[i + j] = block[j] ^ this.#lastBlock[j];\n          this.#lastBlock[j] = ciphertext[i + j];\n        }\n    }\n\n    return plaintext;\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAEA,SAASA,eAAe,QAAQ,WAAW;AAE3C,OAAM,MAAOC,GAAI,SAAQD,eAAe;EAItCE,YAAYC,GAAe,EAAEC,EAAe;IAC1C,KAAK,CAAC,KAAK,EAAED,GAAG,EAAEF,GAAG,CAAC;IAJxBI,OAAA,CAAAC,GAAA;IACAC,cAAA,CAAAD,GAAA;IAKE,IAAIF,EAAE,EAAE;MACN,IAAIA,EAAE,CAACI,MAAM,GAAG,EAAE,EAAE;QAClB,MAAM,IAAIC,SAAS,CAAC,oCAAoC,CAAC;;MAE3DC,sBAAA,KAAI,EAAAL,OAAA,EAAO,IAAIM,UAAU,CAACP,EAAE,CAAC;KAC9B,MAAM;MACLM,sBAAA,KAAI,EAAAL,OAAA,EAAO,IAAIM,UAAU,CAAC,EAAE,CAAC;;IAG/BD,sBAAA,KAAI,EAAAH,cAAA,EAAc,IAAI,CAACH,EAAE;EAC3B;EAEA,IAAIA,EAAEA,CAAA;IAAiB,OAAO,IAAIO,UAAU,CAACC,sBAAA,KAAI,EAAAP,OAAA,MAAI,CAAC;EAAE;EAExDQ,OAAOA,CAACC,SAAqB;IAC3B,IAAIA,SAAS,CAACN,MAAM,GAAG,EAAE,EAAE;MACzB,MAAM,IAAIC,SAAS,CAAC,uDAAuD,CAAC;;IAG9E,MAAMM,UAAU,GAAG,IAAIJ,UAAU,CAACG,SAAS,CAACN,MAAM,CAAC;IACnD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACN,MAAM,EAAEQ,CAAC,IAAI,EAAE,EAAE;MAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BL,sBAAA,KAAI,EAAAL,cAAA,MAAW,CAACU,CAAC,CAAC,IAAIH,SAAS,CAACE,CAAC,GAAGC,CAAC,CAAC;;MAGxCP,sBAAA,KAAI,EAAAH,cAAA,EAAc,IAAI,CAACW,GAAG,CAACL,OAAO,CAACD,sBAAA,KAAI,EAAAL,cAAA,MAAW,CAAC;MACnDQ,UAAU,CAACT,GAAG,CAACM,sBAAA,KAAI,EAAAL,cAAA,MAAW,EAAES,CAAC,CAAC;;IAGpC,OAAOD,UAAU;EACnB;EAEAI,OAAOA,CAACJ,UAAsB;IAC5B,IAAIA,UAAU,CAACP,MAAM,GAAG,EAAE,EAAE;MACxB,MAAM,IAAIC,SAAS,CAAC,wDAAwD,CAAC;;IAGjF,MAAMK,SAAS,GAAG,IAAIH,UAAU,CAACI,UAAU,CAACP,MAAM,CAAC;IACnD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACP,MAAM,EAAEQ,CAAC,IAAI,EAAE,EAAE;MAC5C,MAAMI,KAAK,GAAG,IAAI,CAACF,GAAG,CAACC,OAAO,CAACJ,UAAU,CAACM,QAAQ,CAACL,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC;MAE9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BH,SAAS,CAACE,CAAC,GAAGC,CAAC,CAAC,GAAGG,KAAK,CAACH,CAAC,CAAC,GAAGL,sBAAA,KAAI,EAAAL,cAAA,MAAW,CAACU,CAAC,CAAC;QAChDL,sBAAA,KAAI,EAAAL,cAAA,MAAW,CAACU,CAAC,CAAC,GAAGF,UAAU,CAACC,CAAC,GAAGC,CAAC,CAAC;;;IAI5C,OAAOH,SAAS;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}