const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying CryptoQuest contracts...");
  
  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with account:", deployer.address);
  console.log("Account balance:", (await deployer.provider.getBalance(deployer.address)).toString());

  // Define allocation addresses (using deployer for demo, should be different addresses in production)
  const playToEarnPool = deployer.address;
  const ecosystemFund = deployer.address;
  const teamWallet = deployer.address;
  const marketingWallet = deployer.address;
  const stakingPoolAddress = deployer.address; // Will be updated after StakingPool deployment
  const liquidityPool = deployer.address;

  // Deploy CQTToken
  console.log("\n📄 Deploying CQTToken...");
  const CQTToken = await ethers.getContractFactory("CQTToken");
  const cqtToken = await CQTToken.deploy(
    playToEarnPool,
    ecosystemFund,
    teamWallet,
    marketingWallet,
    stakingPoolAddress,
    liquidityPool
  );
  await cqtToken.waitForDeployment();
  const cqtTokenAddress = await cqtToken.getAddress();
  console.log("✅ CQTToken deployed to:", cqtTokenAddress);

  // Deploy GameTreasury
  console.log("\n🏛️ Deploying GameTreasury...");
  const GameTreasury = await ethers.getContractFactory("GameTreasury");
  const gameTreasury = await GameTreasury.deploy(cqtTokenAddress);
  await gameTreasury.waitForDeployment();
  const gameTreasuryAddress = await gameTreasury.getAddress();
  console.log("✅ GameTreasury deployed to:", gameTreasuryAddress);

  // Deploy StakingPool
  console.log("\n💰 Deploying StakingPool...");
  const StakingPool = await ethers.getContractFactory("StakingPool");
  const stakingPool = await StakingPool.deploy(cqtTokenAddress);
  await stakingPool.waitForDeployment();
  const stakingPoolDeployedAddress = await stakingPool.getAddress();
  console.log("✅ StakingPool deployed to:", stakingPoolDeployedAddress);

  // Configure contracts
  console.log("\n⚙️ Configuring contracts...");
  
  // Add GameTreasury as authorized game contract
  await cqtToken.addGameContract(gameTreasuryAddress);
  console.log("✅ GameTreasury added as authorized game contract");

  // Authorize deployer as game server (for testing)
  await gameTreasury.authorizeServer(deployer.address);
  console.log("✅ Deployer authorized as game server");

  // Transfer some tokens to StakingPool for rewards
  const stakingRewards = ethers.parseEther("100000000"); // 100M tokens for staking rewards
  await cqtToken.transfer(stakingPoolDeployedAddress, stakingRewards);
  console.log("✅ Transferred staking rewards to StakingPool");

  // Display deployment summary
  console.log("\n🎉 Deployment completed successfully!");
  console.log("=====================================");
  console.log("📄 CQTToken:", cqtTokenAddress);
  console.log("🏛️ GameTreasury:", gameTreasuryAddress);
  console.log("💰 StakingPool:", stakingPoolDeployedAddress);
  console.log("=====================================");

  // Display token allocations
  console.log("\n💎 Token Allocations:");
  const playToEarnBalance = await cqtToken.balanceOf(playToEarnPool);
  const ecosystemBalance = await cqtToken.balanceOf(ecosystemFund);
  const teamBalance = await cqtToken.balanceOf(teamWallet);
  const marketingBalance = await cqtToken.balanceOf(marketingWallet);
  const stakingBalance = await cqtToken.balanceOf(stakingPoolDeployedAddress);
  const liquidityBalance = await cqtToken.balanceOf(liquidityPool);

  console.log("🎮 Play-to-Earn Pool:", ethers.formatEther(playToEarnBalance), "CQT");
  console.log("🌱 Ecosystem Fund:", ethers.formatEther(ecosystemBalance), "CQT");
  console.log("👥 Team Wallet:", ethers.formatEther(teamBalance), "CQT");
  console.log("📢 Marketing Wallet:", ethers.formatEther(marketingBalance), "CQT");
  console.log("💰 Staking Pool:", ethers.formatEther(stakingBalance), "CQT");
  console.log("💧 Liquidity Pool:", ethers.formatEther(liquidityBalance), "CQT");

  // Save deployment addresses to file
  const deploymentInfo = {
    network: "localhost",
    deployer: deployer.address,
    contracts: {
      CQTToken: cqtTokenAddress,
      GameTreasury: gameTreasuryAddress,
      StakingPool: stakingPoolDeployedAddress
    },
    allocations: {
      playToEarnPool,
      ecosystemFund,
      teamWallet,
      marketingWallet,
      stakingPool: stakingPoolDeployedAddress,
      liquidityPool
    }
  };

  const fs = require('fs');
  fs.writeFileSync(
    './deployment.json',
    JSON.stringify(deploymentInfo, null, 2)
  );
  console.log("\n📝 Deployment info saved to deployment.json");

  // Verify contracts (optional)
  if (process.env.ETHERSCAN_API_KEY) {
    console.log("\n🔍 Verifying contracts...");
    try {
      await hre.run("verify:verify", {
        address: cqtTokenAddress,
        constructorArguments: [
          playToEarnPool,
          ecosystemFund,
          teamWallet,
          marketingWallet,
          stakingPoolDeployedAddress,
          liquidityPool
        ],
      });
      console.log("✅ CQTToken verified");
    } catch (error) {
      console.log("❌ Verification failed:", error.message);
    }
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
