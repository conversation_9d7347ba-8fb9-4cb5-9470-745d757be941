{"ast": null, "code": "import { keccak256 } from \"../crypto/index.js\";\nimport { concat, dataSlice, getBigInt, getBytes, encodeRlp, assertArgument } from \"../utils/index.js\";\nimport { getAddress } from \"./address.js\";\n// http://ethereum.stackexchange.com/questions/760/how-is-the-address-of-an-ethereum-contract-computed\n/**\n *  Returns the address that would result from a ``CREATE`` for %%tx%%.\n *\n *  This can be used to compute the address a contract will be\n *  deployed to by an EOA when sending a deployment transaction (i.e.\n *  when the ``to`` address is ``null``).\n *\n *  This can also be used to compute the address a contract will be\n *  deployed to by a contract, by using the contract's address as the\n *  ``to`` and the contract's nonce.\n *\n *  @example\n *    from = \"******************************************\";\n *    nonce = 5;\n *\n *    getCreateAddress({ from, nonce });\n *    //_result:\n */\nexport function getCreateAddress(tx) {\n  const from = getAddress(tx.from);\n  const nonce = getBigInt(tx.nonce, \"tx.nonce\");\n  let nonceHex = nonce.toString(16);\n  if (nonceHex === \"0\") {\n    nonceHex = \"0x\";\n  } else if (nonceHex.length % 2) {\n    nonceHex = \"0x0\" + nonceHex;\n  } else {\n    nonceHex = \"0x\" + nonceHex;\n  }\n  return getAddress(dataSlice(keccak256(encodeRlp([from, nonceHex])), 12));\n}\n/**\n *  Returns the address that would result from a ``CREATE2`` operation\n *  with the given %%from%%, %%salt%% and %%initCodeHash%%.\n *\n *  To compute the %%initCodeHash%% from a contract's init code, use\n *  the [[keccak256]] function.\n *\n *  For a quick overview and example of ``CREATE2``, see [[link-ricmoo-wisps]].\n *\n *  @example\n *    // The address of the contract\n *    from = \"******************************************\"\n *\n *    // The salt\n *    salt = id(\"HelloWorld\")\n *\n *    // The hash of the initCode\n *    initCode = \"0x6394198df16000526103ff60206004601c335afa6040516060f3\";\n *    initCodeHash = keccak256(initCode)\n *\n *    getCreate2Address(from, salt, initCodeHash)\n *    //_result:\n */\nexport function getCreate2Address(_from, _salt, _initCodeHash) {\n  const from = getAddress(_from);\n  const salt = getBytes(_salt, \"salt\");\n  const initCodeHash = getBytes(_initCodeHash, \"initCodeHash\");\n  assertArgument(salt.length === 32, \"salt must be 32 bytes\", \"salt\", _salt);\n  assertArgument(initCodeHash.length === 32, \"initCodeHash must be 32 bytes\", \"initCodeHash\", _initCodeHash);\n  return getAddress(dataSlice(keccak256(concat([\"0xff\", from, salt, initCodeHash])), 12));\n}", "map": {"version": 3, "names": ["keccak256", "concat", "dataSlice", "getBigInt", "getBytes", "encodeRlp", "assertArgument", "get<PERSON><PERSON><PERSON>", "getCreateAddress", "tx", "from", "nonce", "nonceHex", "toString", "length", "getCreate2Address", "_from", "_salt", "_initCodeHash", "salt", "initCodeHash"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\address\\contract-address.ts"], "sourcesContent": ["import { keccak256 } from \"../crypto/index.js\";\nimport {\n    concat, dataSlice, getBigInt, getBytes, encodeRlp, assertArgument\n} from \"../utils/index.js\";\n\nimport { getAddress } from \"./address.js\";\n\nimport type { BigNumberish, BytesLike } from \"../utils/index.js\";\n\n\n// http://ethereum.stackexchange.com/questions/760/how-is-the-address-of-an-ethereum-contract-computed\n\n/**\n *  Returns the address that would result from a ``CREATE`` for %%tx%%.\n *\n *  This can be used to compute the address a contract will be\n *  deployed to by an EOA when sending a deployment transaction (i.e.\n *  when the ``to`` address is ``null``).\n *\n *  This can also be used to compute the address a contract will be\n *  deployed to by a contract, by using the contract's address as the\n *  ``to`` and the contract's nonce.\n *\n *  @example\n *    from = \"******************************************\";\n *    nonce = 5;\n *\n *    getCreateAddress({ from, nonce });\n *    //_result:\n */\nexport function getCreateAddress(tx: { from: string, nonce: BigNumberish }): string {\n    const from = getAddress(tx.from);\n    const nonce = getBigInt(tx.nonce, \"tx.nonce\");\n\n    let nonceHex = nonce.toString(16);\n    if (nonceHex === \"0\") {\n        nonceHex = \"0x\";\n    } else if (nonceHex.length % 2) {\n        nonceHex = \"0x0\" + nonceHex;\n    } else {\n        nonceHex = \"0x\" + nonceHex;\n    }\n\n    return getAddress(dataSlice(keccak256(encodeRlp([ from, nonceHex ])), 12));\n}\n\n/**\n *  Returns the address that would result from a ``CREATE2`` operation\n *  with the given %%from%%, %%salt%% and %%initCodeHash%%.\n *\n *  To compute the %%initCodeHash%% from a contract's init code, use\n *  the [[keccak256]] function.\n *\n *  For a quick overview and example of ``CREATE2``, see [[link-ricmoo-wisps]].\n *\n *  @example\n *    // The address of the contract\n *    from = \"******************************************\"\n *\n *    // The salt\n *    salt = id(\"HelloWorld\")\n *\n *    // The hash of the initCode\n *    initCode = \"0x6394198df16000526103ff60206004601c335afa6040516060f3\";\n *    initCodeHash = keccak256(initCode)\n *\n *    getCreate2Address(from, salt, initCodeHash)\n *    //_result:\n */\nexport function getCreate2Address(_from: string, _salt: BytesLike, _initCodeHash: BytesLike): string {\n    const from = getAddress(_from);\n    const salt = getBytes(_salt, \"salt\");\n    const initCodeHash = getBytes(_initCodeHash, \"initCodeHash\");\n\n    assertArgument(salt.length === 32, \"salt must be 32 bytes\", \"salt\", _salt);\n\n    assertArgument(initCodeHash.length === 32, \"initCodeHash must be 32 bytes\", \"initCodeHash\", _initCodeHash);\n\n    return getAddress(dataSlice(keccak256(concat([ \"0xff\", from, salt, initCodeHash ])), 12))\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SACIC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,cAAc,QAC9D,mBAAmB;AAE1B,SAASC,UAAU,QAAQ,cAAc;AAKzC;AAEA;;;;;;;;;;;;;;;;;;AAkBA,OAAM,SAAUC,gBAAgBA,CAACC,EAAyC;EACtE,MAAMC,IAAI,GAAGH,UAAU,CAACE,EAAE,CAACC,IAAI,CAAC;EAChC,MAAMC,KAAK,GAAGR,SAAS,CAACM,EAAE,CAACE,KAAK,EAAE,UAAU,CAAC;EAE7C,IAAIC,QAAQ,GAAGD,KAAK,CAACE,QAAQ,CAAC,EAAE,CAAC;EACjC,IAAID,QAAQ,KAAK,GAAG,EAAE;IAClBA,QAAQ,GAAG,IAAI;GAClB,MAAM,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;IAC5BF,QAAQ,GAAG,KAAK,GAAGA,QAAQ;GAC9B,MAAM;IACHA,QAAQ,GAAG,IAAI,GAAGA,QAAQ;;EAG9B,OAAOL,UAAU,CAACL,SAAS,CAACF,SAAS,CAACK,SAAS,CAAC,CAAEK,IAAI,EAAEE,QAAQ,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9E;AAEA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAM,SAAUG,iBAAiBA,CAACC,KAAa,EAAEC,KAAgB,EAAEC,aAAwB;EACvF,MAAMR,IAAI,GAAGH,UAAU,CAACS,KAAK,CAAC;EAC9B,MAAMG,IAAI,GAAGf,QAAQ,CAACa,KAAK,EAAE,MAAM,CAAC;EACpC,MAAMG,YAAY,GAAGhB,QAAQ,CAACc,aAAa,EAAE,cAAc,CAAC;EAE5DZ,cAAc,CAACa,IAAI,CAACL,MAAM,KAAK,EAAE,EAAE,uBAAuB,EAAE,MAAM,EAAEG,KAAK,CAAC;EAE1EX,cAAc,CAACc,YAAY,CAACN,MAAM,KAAK,EAAE,EAAE,+BAA+B,EAAE,cAAc,EAAEI,aAAa,CAAC;EAE1G,OAAOX,UAAU,CAACL,SAAS,CAACF,SAAS,CAACC,MAAM,CAAC,CAAE,MAAM,EAAES,IAAI,EAAES,IAAI,EAAEC,YAAY,CAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}