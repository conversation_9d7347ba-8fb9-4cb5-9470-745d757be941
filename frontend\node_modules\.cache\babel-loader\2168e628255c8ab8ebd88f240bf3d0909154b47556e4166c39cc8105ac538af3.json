{"ast": null, "code": "import { mod } from './modular.js';\nimport { bytesToNumberBE, concatBytes, utf8ToBytes, validateObject } from './utils.js';\nfunction validateDST(dst) {\n  if (dst instanceof Uint8Array) return dst;\n  if (typeof dst === 'string') return utf8ToBytes(dst);\n  throw new Error('DST must be Uint8Array or string');\n}\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value, length) {\n  if (value < 0 || value >= 1 << 8 * length) {\n    throw new Error(`bad I2OSP call: value=${value} length=${length}`);\n  }\n  const res = Array.from({\n    length\n  }).fill(0);\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\nfunction strxor(a, b) {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\nfunction isBytes(item) {\n  if (!(item instanceof Uint8Array)) throw new Error('Uint8Array expected');\n}\nfunction isNum(item) {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\n// Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1\nexport function expand_message_xmd(msg, DST, lenInBytes, H) {\n  isBytes(msg);\n  isBytes(DST);\n  isNum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const {\n    outputLen: b_in_bytes,\n    blockLen: r_in_bytes\n  } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (ell > 255) throw new Error('Invalid xmd length');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n// Produces a uniformly random byte string using an extendable-output function (XOF) H.\n// 1. The collision resistance of H MUST be at least k bits.\n// 2. H MUST be an XOF that has been proved indifferentiable from\n//    a random oracle under a reasonable cryptographic assumption.\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2\nexport function expand_message_xof(msg, DST, lenInBytes, k, H) {\n  isBytes(msg);\n  isBytes(DST);\n  isNum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil(2 * k / 8);\n    DST = H.create({\n      dkLen\n    }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255) throw new Error('expand_message_xof: invalid lenInBytes');\n  return H.create({\n    dkLen: lenInBytes\n  }).update(msg).update(i2osp(lenInBytes, 2))\n  // 2. DST_prime = DST || I2OSP(len(DST), 1)\n  .update(DST).update(i2osp(DST.length, 1)).digest();\n}\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F\n * https://www.rfc-editor.org/rfc/rfc9380#section-5.2\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg, count, options) {\n  validateObject(options, {\n    DST: 'stringOrUint8Array',\n    p: 'bigint',\n    m: 'isSafeInteger',\n    k: 'isSafeInteger',\n    hash: 'hash'\n  });\n  const {\n    p,\n    k,\n    m,\n    hash,\n    expand,\n    DST: _DST\n  } = options;\n  isBytes(msg);\n  isNum(count);\n  const DST = validateDST(_DST);\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\nexport function isogenyMap(field, map) {\n  // Make same order as in spec\n  const COEFF = map.map(i => Array.from(i).reverse());\n  return (x, y) => {\n    const [xNum, xDen, yNum, yDen] = COEFF.map(val => val.reduce((acc, i) => field.add(field.mul(acc, x), i)));\n    x = field.div(xNum, xDen); // xNum / xDen\n    y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n    return {\n      x,\n      y\n    };\n  };\n}\nexport function createHasher(Point, mapToCurve, def) {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  return {\n    // Encodes byte string to elliptic curve.\n    // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    hashToCurve(msg, options) {\n      const u = hash_to_field(msg, 2, {\n        ...def,\n        DST: def.DST,\n        ...options\n      });\n      const u0 = Point.fromAffine(mapToCurve(u[0]));\n      const u1 = Point.fromAffine(mapToCurve(u[1]));\n      const P = u0.add(u1).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n    // Encodes byte string to elliptic curve.\n    // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    encodeToCurve(msg, options) {\n      const u = hash_to_field(msg, 1, {\n        ...def,\n        DST: def.encodeDST,\n        ...options\n      });\n      const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n      P.assertValidity();\n      return P;\n    }\n  };\n}", "map": {"version": 3, "names": ["mod", "bytesToNumberBE", "concatBytes", "utf8ToBytes", "validateObject", "validateDST", "dst", "Uint8Array", "Error", "os2ip", "i2osp", "value", "length", "res", "Array", "from", "fill", "i", "strxor", "a", "b", "arr", "isBytes", "item", "isNum", "Number", "isSafeInteger", "expand_message_xmd", "msg", "DST", "lenInBytes", "H", "outputLen", "b_in_bytes", "blockLen", "r_in_bytes", "ell", "Math", "ceil", "DST_prime", "Z_pad", "l_i_b_str", "b_0", "args", "pseudo_random_bytes", "slice", "expand_message_xof", "k", "dkLen", "create", "update", "digest", "hash_to_field", "count", "options", "p", "m", "hash", "expand", "_DST", "log2p", "toString", "L", "len_in_bytes", "prb", "u", "e", "j", "elm_offset", "tv", "subarray", "isogenyMap", "field", "map", "COEFF", "reverse", "x", "y", "xNum", "xDen", "yNum", "yDen", "val", "reduce", "acc", "add", "mul", "div", "createHasher", "Point", "mapToCurve", "def", "hashToCurve", "u0", "fromAffine", "u1", "P", "clearCofactor", "assertValidity", "encodeToCurve", "encodeDST"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\abstract\\hash-to-curve.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport type { Group, GroupConstructor, AffinePoint } from './curve.js';\nimport { mod, IField } from './modular.js';\nimport { bytesToNumberBE, CHash, concatBytes, utf8ToBytes, validateObject } from './utils.js';\n\n/**\n * * `DST` is a domain separation tag, defined in section 2.2.5\n * * `p` characteristic of F, where F is a finite field of characteristic p and order q = p^m\n * * `m` is extension degree (1 for prime fields)\n * * `k` is the target security target in bits (e.g. 128), from section 5.1\n * * `expand` is `xmd` (SHA2, SHA3, BLAKE) or `xof` (SHAKE, BLAKE-XOF)\n * * `hash` conforming to `utils.CHash` interface, with `outputLen` / `blockLen` props\n */\ntype UnicodeOrBytes = string | Uint8Array;\nexport type Opts = {\n  DST: UnicodeOrBytes;\n  p: bigint;\n  m: number;\n  k: number;\n  expand: 'xmd' | 'xof';\n  hash: CHash;\n};\n\nfunction validateDST(dst: UnicodeOrBytes): Uint8Array {\n  if (dst instanceof Uint8Array) return dst;\n  if (typeof dst === 'string') return utf8ToBytes(dst);\n  throw new Error('DST must be Uint8Array or string');\n}\n\n// Octet Stream to Integer. \"spec\" implementation of os2ip is 2.5x slower vs bytesToNumberBE.\nconst os2ip = bytesToNumberBE;\n\n// Integer to Octet Stream (numberToBytesBE)\nfunction i2osp(value: number, length: number): Uint8Array {\n  if (value < 0 || value >= 1 << (8 * length)) {\n    throw new Error(`bad I2OSP call: value=${value} length=${length}`);\n  }\n  const res = Array.from({ length }).fill(0) as number[];\n  for (let i = length - 1; i >= 0; i--) {\n    res[i] = value & 0xff;\n    value >>>= 8;\n  }\n  return new Uint8Array(res);\n}\n\nfunction strxor(a: Uint8Array, b: Uint8Array): Uint8Array {\n  const arr = new Uint8Array(a.length);\n  for (let i = 0; i < a.length; i++) {\n    arr[i] = a[i] ^ b[i];\n  }\n  return arr;\n}\n\nfunction isBytes(item: unknown): void {\n  if (!(item instanceof Uint8Array)) throw new Error('Uint8Array expected');\n}\nfunction isNum(item: unknown): void {\n  if (!Number.isSafeInteger(item)) throw new Error('number expected');\n}\n\n// Produces a uniformly random byte string using a cryptographic hash function H that outputs b bits\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.1\nexport function expand_message_xmd(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  H: CHash\n): Uint8Array {\n  isBytes(msg);\n  isBytes(DST);\n  isNum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  if (DST.length > 255) DST = H(concatBytes(utf8ToBytes('H2C-OVERSIZE-DST-'), DST));\n  const { outputLen: b_in_bytes, blockLen: r_in_bytes } = H;\n  const ell = Math.ceil(lenInBytes / b_in_bytes);\n  if (ell > 255) throw new Error('Invalid xmd length');\n  const DST_prime = concatBytes(DST, i2osp(DST.length, 1));\n  const Z_pad = i2osp(0, r_in_bytes);\n  const l_i_b_str = i2osp(lenInBytes, 2); // len_in_bytes_str\n  const b = new Array<Uint8Array>(ell);\n  const b_0 = H(concatBytes(Z_pad, msg, l_i_b_str, i2osp(0, 1), DST_prime));\n  b[0] = H(concatBytes(b_0, i2osp(1, 1), DST_prime));\n  for (let i = 1; i <= ell; i++) {\n    const args = [strxor(b_0, b[i - 1]), i2osp(i + 1, 1), DST_prime];\n    b[i] = H(concatBytes(...args));\n  }\n  const pseudo_random_bytes = concatBytes(...b);\n  return pseudo_random_bytes.slice(0, lenInBytes);\n}\n\n// Produces a uniformly random byte string using an extendable-output function (XOF) H.\n// 1. The collision resistance of H MUST be at least k bits.\n// 2. H MUST be an XOF that has been proved indifferentiable from\n//    a random oracle under a reasonable cryptographic assumption.\n// https://www.rfc-editor.org/rfc/rfc9380#section-5.3.2\nexport function expand_message_xof(\n  msg: Uint8Array,\n  DST: Uint8Array,\n  lenInBytes: number,\n  k: number,\n  H: CHash\n): Uint8Array {\n  isBytes(msg);\n  isBytes(DST);\n  isNum(lenInBytes);\n  // https://www.rfc-editor.org/rfc/rfc9380#section-5.3.3\n  // DST = H('H2C-OVERSIZE-DST-' || a_very_long_DST, Math.ceil((lenInBytes * k) / 8));\n  if (DST.length > 255) {\n    const dkLen = Math.ceil((2 * k) / 8);\n    DST = H.create({ dkLen }).update(utf8ToBytes('H2C-OVERSIZE-DST-')).update(DST).digest();\n  }\n  if (lenInBytes > 65535 || DST.length > 255)\n    throw new Error('expand_message_xof: invalid lenInBytes');\n  return (\n    H.create({ dkLen: lenInBytes })\n      .update(msg)\n      .update(i2osp(lenInBytes, 2))\n      // 2. DST_prime = DST || I2OSP(len(DST), 1)\n      .update(DST)\n      .update(i2osp(DST.length, 1))\n      .digest()\n  );\n}\n\n/**\n * Hashes arbitrary-length byte strings to a list of one or more elements of a finite field F\n * https://www.rfc-editor.org/rfc/rfc9380#section-5.2\n * @param msg a byte string containing the message to hash\n * @param count the number of elements of F to output\n * @param options `{DST: string, p: bigint, m: number, k: number, expand: 'xmd' | 'xof', hash: H}`, see above\n * @returns [u_0, ..., u_(count - 1)], a list of field elements.\n */\nexport function hash_to_field(msg: Uint8Array, count: number, options: Opts): bigint[][] {\n  validateObject(options, {\n    DST: 'stringOrUint8Array',\n    p: 'bigint',\n    m: 'isSafeInteger',\n    k: 'isSafeInteger',\n    hash: 'hash',\n  });\n  const { p, k, m, hash, expand, DST: _DST } = options;\n  isBytes(msg);\n  isNum(count);\n  const DST = validateDST(_DST);\n  const log2p = p.toString(2).length;\n  const L = Math.ceil((log2p + k) / 8); // section 5.1 of ietf draft link above\n  const len_in_bytes = count * m * L;\n  let prb; // pseudo_random_bytes\n  if (expand === 'xmd') {\n    prb = expand_message_xmd(msg, DST, len_in_bytes, hash);\n  } else if (expand === 'xof') {\n    prb = expand_message_xof(msg, DST, len_in_bytes, k, hash);\n  } else if (expand === '_internal_pass') {\n    // for internal tests only\n    prb = msg;\n  } else {\n    throw new Error('expand must be \"xmd\" or \"xof\"');\n  }\n  const u = new Array(count);\n  for (let i = 0; i < count; i++) {\n    const e = new Array(m);\n    for (let j = 0; j < m; j++) {\n      const elm_offset = L * (j + i * m);\n      const tv = prb.subarray(elm_offset, elm_offset + L);\n      e[j] = mod(os2ip(tv), p);\n    }\n    u[i] = e;\n  }\n  return u;\n}\n\nexport function isogenyMap<T, F extends IField<T>>(field: F, map: [T[], T[], T[], T[]]) {\n  // Make same order as in spec\n  const COEFF = map.map((i) => Array.from(i).reverse());\n  return (x: T, y: T) => {\n    const [xNum, xDen, yNum, yDen] = COEFF.map((val) =>\n      val.reduce((acc, i) => field.add(field.mul(acc, x), i))\n    );\n    x = field.div(xNum, xDen); // xNum / xDen\n    y = field.mul(y, field.div(yNum, yDen)); // y * (yNum / yDev)\n    return { x, y };\n  };\n}\n\nexport interface H2CPoint<T> extends Group<H2CPoint<T>> {\n  add(rhs: H2CPoint<T>): H2CPoint<T>;\n  toAffine(iz?: bigint): AffinePoint<T>;\n  clearCofactor(): H2CPoint<T>;\n  assertValidity(): void;\n}\n\nexport interface H2CPointConstructor<T> extends GroupConstructor<H2CPoint<T>> {\n  fromAffine(ap: AffinePoint<T>): H2CPoint<T>;\n}\n\nexport type MapToCurve<T> = (scalar: bigint[]) => AffinePoint<T>;\n\n// Separated from initialization opts, so users won't accidentally change per-curve parameters\n// (changing DST is ok!)\nexport type htfBasicOpts = { DST: UnicodeOrBytes };\n\nexport function createHasher<T>(\n  Point: H2CPointConstructor<T>,\n  mapToCurve: MapToCurve<T>,\n  def: Opts & { encodeDST?: UnicodeOrBytes }\n) {\n  if (typeof mapToCurve !== 'function') throw new Error('mapToCurve() must be defined');\n  return {\n    // Encodes byte string to elliptic curve.\n    // hash_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    hashToCurve(msg: Uint8Array, options?: htfBasicOpts) {\n      const u = hash_to_field(msg, 2, { ...def, DST: def.DST, ...options } as Opts);\n      const u0 = Point.fromAffine(mapToCurve(u[0]));\n      const u1 = Point.fromAffine(mapToCurve(u[1]));\n      const P = u0.add(u1).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n\n    // Encodes byte string to elliptic curve.\n    // encode_to_curve from https://www.rfc-editor.org/rfc/rfc9380#section-3\n    encodeToCurve(msg: Uint8Array, options?: htfBasicOpts) {\n      const u = hash_to_field(msg, 1, { ...def, DST: def.encodeDST, ...options } as Opts);\n      const P = Point.fromAffine(mapToCurve(u[0])).clearCofactor();\n      P.assertValidity();\n      return P;\n    },\n  };\n}\n"], "mappings": "AAEA,SAASA,GAAG,QAAgB,cAAc;AAC1C,SAASC,eAAe,EAASC,WAAW,EAAEC,WAAW,EAAEC,cAAc,QAAQ,YAAY;AAoB7F,SAASC,WAAWA,CAACC,GAAmB;EACtC,IAAIA,GAAG,YAAYC,UAAU,EAAE,OAAOD,GAAG;EACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAOH,WAAW,CAACG,GAAG,CAAC;EACpD,MAAM,IAAIE,KAAK,CAAC,kCAAkC,CAAC;AACrD;AAEA;AACA,MAAMC,KAAK,GAAGR,eAAe;AAE7B;AACA,SAASS,KAAKA,CAACC,KAAa,EAAEC,MAAc;EAC1C,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,CAAC,IAAK,CAAC,GAAGC,MAAO,EAAE;IAC3C,MAAM,IAAIJ,KAAK,CAAC,yBAAyBG,KAAK,WAAWC,MAAM,EAAE,CAAC;;EAEpE,MAAMC,GAAG,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEH;EAAM,CAAE,CAAC,CAACI,IAAI,CAAC,CAAC,CAAa;EACtD,KAAK,IAAIC,CAAC,GAAGL,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACpCJ,GAAG,CAACI,CAAC,CAAC,GAAGN,KAAK,GAAG,IAAI;IACrBA,KAAK,MAAM,CAAC;;EAEd,OAAO,IAAIJ,UAAU,CAACM,GAAG,CAAC;AAC5B;AAEA,SAASK,MAAMA,CAACC,CAAa,EAAEC,CAAa;EAC1C,MAAMC,GAAG,GAAG,IAAId,UAAU,CAACY,CAAC,CAACP,MAAM,CAAC;EACpC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,CAACP,MAAM,EAAEK,CAAC,EAAE,EAAE;IACjCI,GAAG,CAACJ,CAAC,CAAC,GAAGE,CAAC,CAACF,CAAC,CAAC,GAAGG,CAAC,CAACH,CAAC,CAAC;;EAEtB,OAAOI,GAAG;AACZ;AAEA,SAASC,OAAOA,CAACC,IAAa;EAC5B,IAAI,EAAEA,IAAI,YAAYhB,UAAU,CAAC,EAAE,MAAM,IAAIC,KAAK,CAAC,qBAAqB,CAAC;AAC3E;AACA,SAASgB,KAAKA,CAACD,IAAa;EAC1B,IAAI,CAACE,MAAM,CAACC,aAAa,CAACH,IAAI,CAAC,EAAE,MAAM,IAAIf,KAAK,CAAC,iBAAiB,CAAC;AACrE;AAEA;AACA;AACA,OAAM,SAAUmB,kBAAkBA,CAChCC,GAAe,EACfC,GAAe,EACfC,UAAkB,EAClBC,CAAQ;EAERT,OAAO,CAACM,GAAG,CAAC;EACZN,OAAO,CAACO,GAAG,CAAC;EACZL,KAAK,CAACM,UAAU,CAAC;EACjB;EACA,IAAID,GAAG,CAACjB,MAAM,GAAG,GAAG,EAAEiB,GAAG,GAAGE,CAAC,CAAC7B,WAAW,CAACC,WAAW,CAAC,mBAAmB,CAAC,EAAE0B,GAAG,CAAC,CAAC;EACjF,MAAM;IAAEG,SAAS,EAAEC,UAAU;IAAEC,QAAQ,EAAEC;EAAU,CAAE,GAAGJ,CAAC;EACzD,MAAMK,GAAG,GAAGC,IAAI,CAACC,IAAI,CAACR,UAAU,GAAGG,UAAU,CAAC;EAC9C,IAAIG,GAAG,GAAG,GAAG,EAAE,MAAM,IAAI5B,KAAK,CAAC,oBAAoB,CAAC;EACpD,MAAM+B,SAAS,GAAGrC,WAAW,CAAC2B,GAAG,EAAEnB,KAAK,CAACmB,GAAG,CAACjB,MAAM,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM4B,KAAK,GAAG9B,KAAK,CAAC,CAAC,EAAEyB,UAAU,CAAC;EAClC,MAAMM,SAAS,GAAG/B,KAAK,CAACoB,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMV,CAAC,GAAG,IAAIN,KAAK,CAAasB,GAAG,CAAC;EACpC,MAAMM,GAAG,GAAGX,CAAC,CAAC7B,WAAW,CAACsC,KAAK,EAAEZ,GAAG,EAAEa,SAAS,EAAE/B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE6B,SAAS,CAAC,CAAC;EACzEnB,CAAC,CAAC,CAAC,CAAC,GAAGW,CAAC,CAAC7B,WAAW,CAACwC,GAAG,EAAEhC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE6B,SAAS,CAAC,CAAC;EAClD,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImB,GAAG,EAAEnB,CAAC,EAAE,EAAE;IAC7B,MAAM0B,IAAI,GAAG,CAACzB,MAAM,CAACwB,GAAG,EAAEtB,CAAC,CAACH,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEP,KAAK,CAACO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEsB,SAAS,CAAC;IAChEnB,CAAC,CAACH,CAAC,CAAC,GAAGc,CAAC,CAAC7B,WAAW,CAAC,GAAGyC,IAAI,CAAC,CAAC;;EAEhC,MAAMC,mBAAmB,GAAG1C,WAAW,CAAC,GAAGkB,CAAC,CAAC;EAC7C,OAAOwB,mBAAmB,CAACC,KAAK,CAAC,CAAC,EAAEf,UAAU,CAAC;AACjD;AAEA;AACA;AACA;AACA;AACA;AACA,OAAM,SAAUgB,kBAAkBA,CAChClB,GAAe,EACfC,GAAe,EACfC,UAAkB,EAClBiB,CAAS,EACThB,CAAQ;EAERT,OAAO,CAACM,GAAG,CAAC;EACZN,OAAO,CAACO,GAAG,CAAC;EACZL,KAAK,CAACM,UAAU,CAAC;EACjB;EACA;EACA,IAAID,GAAG,CAACjB,MAAM,GAAG,GAAG,EAAE;IACpB,MAAMoC,KAAK,GAAGX,IAAI,CAACC,IAAI,CAAE,CAAC,GAAGS,CAAC,GAAI,CAAC,CAAC;IACpClB,GAAG,GAAGE,CAAC,CAACkB,MAAM,CAAC;MAAED;IAAK,CAAE,CAAC,CAACE,MAAM,CAAC/C,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC+C,MAAM,CAACrB,GAAG,CAAC,CAACsB,MAAM,EAAE;;EAEzF,IAAIrB,UAAU,GAAG,KAAK,IAAID,GAAG,CAACjB,MAAM,GAAG,GAAG,EACxC,MAAM,IAAIJ,KAAK,CAAC,wCAAwC,CAAC;EAC3D,OACEuB,CAAC,CAACkB,MAAM,CAAC;IAAED,KAAK,EAAElB;EAAU,CAAE,CAAC,CAC5BoB,MAAM,CAACtB,GAAG,CAAC,CACXsB,MAAM,CAACxC,KAAK,CAACoB,UAAU,EAAE,CAAC,CAAC;EAC5B;EAAA,CACCoB,MAAM,CAACrB,GAAG,CAAC,CACXqB,MAAM,CAACxC,KAAK,CAACmB,GAAG,CAACjB,MAAM,EAAE,CAAC,CAAC,CAAC,CAC5BuC,MAAM,EAAE;AAEf;AAEA;;;;;;;;AAQA,OAAM,SAAUC,aAAaA,CAACxB,GAAe,EAAEyB,KAAa,EAAEC,OAAa;EACzElD,cAAc,CAACkD,OAAO,EAAE;IACtBzB,GAAG,EAAE,oBAAoB;IACzB0B,CAAC,EAAE,QAAQ;IACXC,CAAC,EAAE,eAAe;IAClBT,CAAC,EAAE,eAAe;IAClBU,IAAI,EAAE;GACP,CAAC;EACF,MAAM;IAAEF,CAAC;IAAER,CAAC;IAAES,CAAC;IAAEC,IAAI;IAAEC,MAAM;IAAE7B,GAAG,EAAE8B;EAAI,CAAE,GAAGL,OAAO;EACpDhC,OAAO,CAACM,GAAG,CAAC;EACZJ,KAAK,CAAC6B,KAAK,CAAC;EACZ,MAAMxB,GAAG,GAAGxB,WAAW,CAACsD,IAAI,CAAC;EAC7B,MAAMC,KAAK,GAAGL,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC,CAACjD,MAAM;EAClC,MAAMkD,CAAC,GAAGzB,IAAI,CAACC,IAAI,CAAC,CAACsB,KAAK,GAAGb,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EACtC,MAAMgB,YAAY,GAAGV,KAAK,GAAGG,CAAC,GAAGM,CAAC;EAClC,IAAIE,GAAG,CAAC,CAAC;EACT,IAAIN,MAAM,KAAK,KAAK,EAAE;IACpBM,GAAG,GAAGrC,kBAAkB,CAACC,GAAG,EAAEC,GAAG,EAAEkC,YAAY,EAAEN,IAAI,CAAC;GACvD,MAAM,IAAIC,MAAM,KAAK,KAAK,EAAE;IAC3BM,GAAG,GAAGlB,kBAAkB,CAAClB,GAAG,EAAEC,GAAG,EAAEkC,YAAY,EAAEhB,CAAC,EAAEU,IAAI,CAAC;GAC1D,MAAM,IAAIC,MAAM,KAAK,gBAAgB,EAAE;IACtC;IACAM,GAAG,GAAGpC,GAAG;GACV,MAAM;IACL,MAAM,IAAIpB,KAAK,CAAC,+BAA+B,CAAC;;EAElD,MAAMyD,CAAC,GAAG,IAAInD,KAAK,CAACuC,KAAK,CAAC;EAC1B,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,KAAK,EAAEpC,CAAC,EAAE,EAAE;IAC9B,MAAMiD,CAAC,GAAG,IAAIpD,KAAK,CAAC0C,CAAC,CAAC;IACtB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,CAAC,EAAEW,CAAC,EAAE,EAAE;MAC1B,MAAMC,UAAU,GAAGN,CAAC,IAAIK,CAAC,GAAGlD,CAAC,GAAGuC,CAAC,CAAC;MAClC,MAAMa,EAAE,GAAGL,GAAG,CAACM,QAAQ,CAACF,UAAU,EAAEA,UAAU,GAAGN,CAAC,CAAC;MACnDI,CAAC,CAACC,CAAC,CAAC,GAAGnE,GAAG,CAACS,KAAK,CAAC4D,EAAE,CAAC,EAAEd,CAAC,CAAC;;IAE1BU,CAAC,CAAChD,CAAC,CAAC,GAAGiD,CAAC;;EAEV,OAAOD,CAAC;AACV;AAEA,OAAM,SAAUM,UAAUA,CAAyBC,KAAQ,EAAEC,GAAyB;EACpF;EACA,MAAMC,KAAK,GAAGD,GAAG,CAACA,GAAG,CAAExD,CAAC,IAAKH,KAAK,CAACC,IAAI,CAACE,CAAC,CAAC,CAAC0D,OAAO,EAAE,CAAC;EACrD,OAAO,CAACC,CAAI,EAAEC,CAAI,KAAI;IACpB,MAAM,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC,GAAGP,KAAK,CAACD,GAAG,CAAES,GAAG,IAC7CA,GAAG,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEnE,CAAC,KAAKuD,KAAK,CAACa,GAAG,CAACb,KAAK,CAACc,GAAG,CAACF,GAAG,EAAER,CAAC,CAAC,EAAE3D,CAAC,CAAC,CAAC,CACxD;IACD2D,CAAC,GAAGJ,KAAK,CAACe,GAAG,CAACT,IAAI,EAAEC,IAAI,CAAC,CAAC,CAAC;IAC3BF,CAAC,GAAGL,KAAK,CAACc,GAAG,CAACT,CAAC,EAAEL,KAAK,CAACe,GAAG,CAACP,IAAI,EAAEC,IAAI,CAAC,CAAC,CAAC,CAAC;IACzC,OAAO;MAAEL,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC;AACH;AAmBA,OAAM,SAAUW,YAAYA,CAC1BC,KAA6B,EAC7BC,UAAyB,EACzBC,GAA0C;EAE1C,IAAI,OAAOD,UAAU,KAAK,UAAU,EAAE,MAAM,IAAIlF,KAAK,CAAC,8BAA8B,CAAC;EACrF,OAAO;IACL;IACA;IACAoF,WAAWA,CAAChE,GAAe,EAAE0B,OAAsB;MACjD,MAAMW,CAAC,GAAGb,aAAa,CAACxB,GAAG,EAAE,CAAC,EAAE;QAAE,GAAG+D,GAAG;QAAE9D,GAAG,EAAE8D,GAAG,CAAC9D,GAAG;QAAE,GAAGyB;MAAO,CAAU,CAAC;MAC7E,MAAMuC,EAAE,GAAGJ,KAAK,CAACK,UAAU,CAACJ,UAAU,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAM8B,EAAE,GAAGN,KAAK,CAACK,UAAU,CAACJ,UAAU,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7C,MAAM+B,CAAC,GAAGH,EAAE,CAACR,GAAG,CAACU,EAAE,CAAC,CAACE,aAAa,EAAE;MACpCD,CAAC,CAACE,cAAc,EAAE;MAClB,OAAOF,CAAC;IACV,CAAC;IAED;IACA;IACAG,aAAaA,CAACvE,GAAe,EAAE0B,OAAsB;MACnD,MAAMW,CAAC,GAAGb,aAAa,CAACxB,GAAG,EAAE,CAAC,EAAE;QAAE,GAAG+D,GAAG;QAAE9D,GAAG,EAAE8D,GAAG,CAACS,SAAS;QAAE,GAAG9C;MAAO,CAAU,CAAC;MACnF,MAAM0C,CAAC,GAAGP,KAAK,CAACK,UAAU,CAACJ,UAAU,CAACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgC,aAAa,EAAE;MAC5DD,CAAC,CAACE,cAAc,EAAE;MAClB,OAAOF,CAAC;IACV;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}