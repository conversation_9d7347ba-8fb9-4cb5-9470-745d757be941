{"ast": null, "code": "import { WebSocket as _WebSocket } from \"./ws.js\"; /*-browser*/\nimport { SocketProvider } from \"./provider-socket.js\";\n/**\n *  A JSON-RPC provider which is backed by a WebSocket.\n *\n *  WebSockets are often preferred because they retain a live connection\n *  to a server, which permits more instant access to events.\n *\n *  However, this incurs higher server infrasturture costs, so additional\n *  resources may be required to host your own WebSocket nodes and many\n *  third-party services charge additional fees for WebSocket endpoints.\n */\nexport class WebSocketProvider extends SocketProvider {\n  #connect;\n  #websocket;\n  get websocket() {\n    if (this.#websocket == null) {\n      throw new Error(\"websocket closed\");\n    }\n    return this.#websocket;\n  }\n  constructor(url, network, options) {\n    super(network, options);\n    if (typeof url === \"string\") {\n      this.#connect = () => {\n        return new _WebSocket(url);\n      };\n      this.#websocket = this.#connect();\n    } else if (typeof url === \"function\") {\n      this.#connect = url;\n      this.#websocket = url();\n    } else {\n      this.#connect = null;\n      this.#websocket = url;\n    }\n    this.websocket.onopen = async () => {\n      try {\n        await this._start();\n        this.resume();\n      } catch (error) {\n        console.log(\"failed to start WebsocketProvider\", error);\n        // @TODO: now what? Attempt reconnect?\n      }\n    };\n    this.websocket.onmessage = message => {\n      this._processMessage(message.data);\n    };\n    /*\n            this.websocket.onclose = (event) => {\n                // @TODO: What event.code should we reconnect on?\n                const reconnect = false;\n                if (reconnect) {\n                    this.pause(true);\n                    if (this.#connect) {\n                        this.#websocket = this.#connect();\n                        this.#websocket.onopen = ...\n                        // @TODO: this requires the super class to rebroadcast; move it there\n                    }\n                    this._reconnect();\n                }\n            };\n    */\n  }\n  async _write(message) {\n    this.websocket.send(message);\n  }\n  async destroy() {\n    if (this.#websocket != null) {\n      this.#websocket.close();\n      this.#websocket = null;\n    }\n    super.destroy();\n  }\n}", "map": {"version": 3, "names": ["WebSocket", "_WebSocket", "SocketProvider", "WebSocketProvider", "connect", "websocket", "Error", "constructor", "url", "network", "options", "onopen", "_start", "resume", "error", "console", "log", "onmessage", "message", "_processMessage", "data", "_write", "send", "destroy", "close"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-websocket.ts"], "sourcesContent": ["\n\nimport { WebSocket as _WebSocket } from \"./ws.js\"; /*-browser*/\n\nimport { SocketProvider } from \"./provider-socket.js\";\n\nimport type { JsonRpcApiProviderOptions} from \"./provider-jsonrpc.js\";\nimport type { Networkish } from \"./network.js\";\n\n/**\n *  A generic interface to a Websocket-like object.\n */\nexport interface WebSocketLike {\n    onopen: null | ((...args: Array<any>) => any);\n    onmessage: null | ((...args: Array<any>) => any);\n    onerror: null | ((...args: Array<any>) => any);\n\n    readyState: number;\n\n    send(payload: any): void;\n    close(code?: number, reason?: string): void;\n}\n\n/**\n *  A function which can be used to re-create a WebSocket connection\n *  on disconnect.\n */\nexport type WebSocketCreator = () => WebSocketLike;\n\n/**\n *  A JSON-RPC provider which is backed by a WebSocket.\n *\n *  WebSockets are often preferred because they retain a live connection\n *  to a server, which permits more instant access to events.\n *\n *  However, this incurs higher server infrasturture costs, so additional\n *  resources may be required to host your own WebSocket nodes and many\n *  third-party services charge additional fees for WebSocket endpoints.\n */\nexport class WebSocketProvider extends SocketProvider {\n    #connect: null | WebSocketCreator;\n\n    #websocket: null | WebSocketLike;\n    get websocket(): WebSocketLike {\n        if (this.#websocket == null) { throw new Error(\"websocket closed\"); }\n        return this.#websocket;\n    }\n\n    constructor(url: string | WebSocketLike | WebSocketCreator, network?: Networkish, options?: JsonRpcApiProviderOptions) {\n        super(network, options);\n        if (typeof(url) === \"string\") {\n            this.#connect = () => { return new _WebSocket(url); };\n            this.#websocket = this.#connect();\n        } else if (typeof(url) === \"function\") {\n            this.#connect = url;\n            this.#websocket = url();\n        } else {\n            this.#connect = null;\n            this.#websocket = url;\n        }\n\n        this.websocket.onopen = async () => {\n            try {\n                await this._start()\n                this.resume();\n            } catch (error) {\n                console.log(\"failed to start WebsocketProvider\", error);\n                // @TODO: now what? Attempt reconnect?\n            }\n        };\n\n        this.websocket.onmessage = (message: { data: string }) => {\n            this._processMessage(message.data);\n        };\n/*\n        this.websocket.onclose = (event) => {\n            // @TODO: What event.code should we reconnect on?\n            const reconnect = false;\n            if (reconnect) {\n                this.pause(true);\n                if (this.#connect) {\n                    this.#websocket = this.#connect();\n                    this.#websocket.onopen = ...\n                    // @TODO: this requires the super class to rebroadcast; move it there\n                }\n                this._reconnect();\n            }\n        };\n*/\n    }\n\n    async _write(message: string): Promise<void> {\n        this.websocket.send(message);\n    }\n\n    async destroy(): Promise<void> {\n        if (this.#websocket != null) {\n            this.#websocket.close();\n            this.#websocket = null;\n        }\n        super.destroy();\n    }\n}\n"], "mappings": "AAEA,SAASA,SAAS,IAAIC,UAAU,QAAQ,SAAS,CAAC,CAAC;AAEnD,SAASC,cAAc,QAAQ,sBAAsB;AAyBrD;;;;;;;;;;AAUA,OAAM,MAAOC,iBAAkB,SAAQD,cAAc;EACjD,CAAAE,OAAQ;EAER,CAAAC,SAAU;EACV,IAAIA,SAASA,CAAA;IACT,IAAI,IAAI,CAAC,CAAAA,SAAU,IAAI,IAAI,EAAE;MAAE,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;;IAClE,OAAO,IAAI,CAAC,CAAAD,SAAU;EAC1B;EAEAE,YAAYC,GAA8C,EAAEC,OAAoB,EAAEC,OAAmC;IACjH,KAAK,CAACD,OAAO,EAAEC,OAAO,CAAC;IACvB,IAAI,OAAOF,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI,CAAC,CAAAJ,OAAQ,GAAG,MAAK;QAAG,OAAO,IAAIH,UAAU,CAACO,GAAG,CAAC;MAAE,CAAC;MACrD,IAAI,CAAC,CAAAH,SAAU,GAAG,IAAI,CAAC,CAAAD,OAAQ,EAAE;KACpC,MAAM,IAAI,OAAOI,GAAI,KAAK,UAAU,EAAE;MACnC,IAAI,CAAC,CAAAJ,OAAQ,GAAGI,GAAG;MACnB,IAAI,CAAC,CAAAH,SAAU,GAAGG,GAAG,EAAE;KAC1B,MAAM;MACH,IAAI,CAAC,CAAAJ,OAAQ,GAAG,IAAI;MACpB,IAAI,CAAC,CAAAC,SAAU,GAAGG,GAAG;;IAGzB,IAAI,CAACH,SAAS,CAACM,MAAM,GAAG,YAAW;MAC/B,IAAI;QACA,MAAM,IAAI,CAACC,MAAM,EAAE;QACnB,IAAI,CAACC,MAAM,EAAE;OAChB,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,KAAK,CAAC;QACvD;;IAER,CAAC;IAED,IAAI,CAACT,SAAS,CAACY,SAAS,GAAIC,OAAyB,IAAI;MACrD,IAAI,CAACC,eAAe,CAACD,OAAO,CAACE,IAAI,CAAC;IACtC,CAAC;IACT;;;;;;;;;;;;;;;EAeI;EAEA,MAAMC,MAAMA,CAACH,OAAe;IACxB,IAAI,CAACb,SAAS,CAACiB,IAAI,CAACJ,OAAO,CAAC;EAChC;EAEA,MAAMK,OAAOA,CAAA;IACT,IAAI,IAAI,CAAC,CAAAlB,SAAU,IAAI,IAAI,EAAE;MACzB,IAAI,CAAC,CAAAA,SAAU,CAACmB,KAAK,EAAE;MACvB,IAAI,CAAC,CAAAnB,SAAU,GAAG,IAAI;;IAE1B,KAAK,CAACkB,OAAO,EAAE;EACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}