{"ast": null, "code": "/**\n *  [[link-alchemy]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:Alchemy  [providers-alchemy]\n */\nimport { defineProperties, resolveProperties, assert, assertArgument, FetchRequest } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nconst defaultApiKey = \"_gg7wSSi0KMBsdKnGVfHDueq6xMB9EkC\";\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"eth-mainnet.alchemyapi.io\";\n    case \"goerli\":\n      return \"eth-goerli.g.alchemy.com\";\n    case \"sepolia\":\n      return \"eth-sepolia.g.alchemy.com\";\n    case \"arbitrum\":\n      return \"arb-mainnet.g.alchemy.com\";\n    case \"arbitrum-goerli\":\n      return \"arb-goerli.g.alchemy.com\";\n    case \"arbitrum-sepolia\":\n      return \"arb-sepolia.g.alchemy.com\";\n    case \"base\":\n      return \"base-mainnet.g.alchemy.com\";\n    case \"base-goerli\":\n      return \"base-goerli.g.alchemy.com\";\n    case \"base-sepolia\":\n      return \"base-sepolia.g.alchemy.com\";\n    case \"matic\":\n      return \"polygon-mainnet.g.alchemy.com\";\n    case \"matic-amoy\":\n      return \"polygon-amoy.g.alchemy.com\";\n    case \"matic-mumbai\":\n      return \"polygon-mumbai.g.alchemy.com\";\n    case \"optimism\":\n      return \"opt-mainnet.g.alchemy.com\";\n    case \"optimism-goerli\":\n      return \"opt-goerli.g.alchemy.com\";\n    case \"optimism-sepolia\":\n      return \"opt-sepolia.g.alchemy.com\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **AlchemyProvider** connects to the [[link-alchemy]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-alchemy-signup).\n *\n *  @_docloc: api/providers/thirdparty\n */\nexport class AlchemyProvider extends JsonRpcProvider {\n  apiKey;\n  constructor(_network, apiKey) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (apiKey == null) {\n      apiKey = defaultApiKey;\n    }\n    const request = AlchemyProvider.getRequest(network, apiKey);\n    super(request, network, {\n      staticNetwork: network\n    });\n    defineProperties(this, {\n      apiKey\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new AlchemyProvider(chainId, this.apiKey);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  async _perform(req) {\n    // https://docs.alchemy.com/reference/trace-transaction\n    if (req.method === \"getTransactionResult\") {\n      const {\n        trace,\n        tx\n      } = await resolveProperties({\n        trace: this.send(\"trace_transaction\", [req.hash]),\n        tx: this.getTransaction(req.hash)\n      });\n      if (trace == null || tx == null) {\n        return null;\n      }\n      let data;\n      let error = false;\n      try {\n        data = trace[0].result.output;\n        error = trace[0].error === \"Reverted\";\n      } catch (error) {}\n      if (data) {\n        assert(!error, \"an error occurred during transaction executions\", \"CALL_EXCEPTION\", {\n          action: \"getTransactionResult\",\n          data,\n          reason: null,\n          transaction: tx,\n          invocation: null,\n          revert: null // @TODO\n        });\n        return data;\n      }\n      assert(false, \"could not parse trace result\", \"BAD_DATA\", {\n        value: trace\n      });\n    }\n    return await super._perform(req);\n  }\n  isCommunityResource() {\n    return this.apiKey === defaultApiKey;\n  }\n  static getRequest(network, apiKey) {\n    if (apiKey == null) {\n      apiKey = defaultApiKey;\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/v2/${apiKey}`);\n    request.allowGzip = true;\n    if (apiKey === defaultApiKey) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"alchemy\");\n        return true;\n      };\n    }\n    return request;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "resolveProperties", "assert", "assertArgument", "FetchRequest", "showThrottleMessage", "Network", "JsonRpcProvider", "defaultApiKey", "getHost", "name", "AlchemyProvider", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_network", "network", "from", "request", "getRequest", "staticNetwork", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "_perform", "req", "method", "trace", "tx", "send", "hash", "getTransaction", "data", "result", "output", "action", "reason", "transaction", "invocation", "revert", "value", "isCommunityResource", "allowGzip", "retryFunc", "response", "attempt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-alchemy.ts"], "sourcesContent": ["/**\n *  [[link-alchemy]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:Alchemy  [providers-alchemy]\n */\n\nimport {\n    defineProperties, resolveProperties, assert, assertArgument,\n    FetchRequest\n} from \"../utils/index.js\";\n\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { AbstractProvider, PerformActionRequest } from \"./abstract-provider.js\";\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\n\n\nconst defaultApiKey = \"_gg7wSSi0KMBsdKnGVfHDueq6xMB9EkC\"\n\nfunction getHost(name: string): string {\n    switch(name) {\n        case \"mainnet\":\n            return \"eth-mainnet.alchemyapi.io\";\n        case \"goerli\":\n            return \"eth-goerli.g.alchemy.com\";\n        case \"sepolia\":\n            return \"eth-sepolia.g.alchemy.com\";\n\n        case \"arbitrum\":\n            return \"arb-mainnet.g.alchemy.com\";\n        case \"arbitrum-goerli\":\n            return \"arb-goerli.g.alchemy.com\";\n        case \"arbitrum-sepolia\":\n            return \"arb-sepolia.g.alchemy.com\";\n        case \"base\":\n            return \"base-mainnet.g.alchemy.com\";\n        case \"base-goerli\":\n            return \"base-goerli.g.alchemy.com\";\n        case \"base-sepolia\":\n            return \"base-sepolia.g.alchemy.com\";\n        case \"matic\":\n            return \"polygon-mainnet.g.alchemy.com\";\n        case \"matic-amoy\":\n            return \"polygon-amoy.g.alchemy.com\";\n        case \"matic-mumbai\":\n            return \"polygon-mumbai.g.alchemy.com\";\n        case \"optimism\":\n            return \"opt-mainnet.g.alchemy.com\";\n        case \"optimism-goerli\":\n            return \"opt-goerli.g.alchemy.com\";\n        case \"optimism-sepolia\":\n            return \"opt-sepolia.g.alchemy.com\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n/**\n *  The **AlchemyProvider** connects to the [[link-alchemy]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-alchemy-signup).\n *\n *  @_docloc: api/providers/thirdparty\n */\nexport class AlchemyProvider extends JsonRpcProvider implements CommunityResourcable {\n    readonly apiKey!: string;\n\n    constructor(_network?: Networkish, apiKey?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        if (apiKey == null) { apiKey = defaultApiKey; }\n\n        const request = AlchemyProvider.getRequest(network, apiKey);\n        super(request, network, { staticNetwork: network });\n\n        defineProperties<AlchemyProvider>(this, { apiKey });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new AlchemyProvider(chainId, this.apiKey);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    async _perform(req: PerformActionRequest): Promise<any> {\n\n        // https://docs.alchemy.com/reference/trace-transaction\n        if (req.method === \"getTransactionResult\") {\n            const { trace, tx } = await resolveProperties({\n                trace: this.send(\"trace_transaction\", [ req.hash ]),\n                tx: this.getTransaction(req.hash)\n            });\n            if (trace == null || tx == null) { return null; }\n\n            let data: undefined | string;\n            let error = false;\n            try {\n                data = trace[0].result.output;\n                error = (trace[0].error === \"Reverted\");\n            } catch (error) { }\n\n            if (data) {\n                assert(!error, \"an error occurred during transaction executions\", \"CALL_EXCEPTION\", {\n                    action: \"getTransactionResult\",\n                    data,\n                    reason: null,\n                    transaction: tx,\n                    invocation: null,\n                    revert: null // @TODO\n                });\n                return data;\n            }\n\n            assert(false, \"could not parse trace result\", \"BAD_DATA\", { value: trace });\n        }\n\n        return await super._perform(req);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.apiKey === defaultApiKey);\n    }\n\n    static getRequest(network: Network, apiKey?: string): FetchRequest {\n        if (apiKey == null) { apiKey = defaultApiKey; }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/v2/${ apiKey }`);\n        request.allowGzip = true;\n\n        if (apiKey === defaultApiKey) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"alchemy\");\n                return true;\n            }\n        }\n\n        return request;\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,SACIA,gBAAgB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,cAAc,EAC3DC,YAAY,QACT,mBAAmB;AAE1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAOvD,MAAMC,aAAa,GAAG,kCAAkC;AAExD,SAASC,OAAOA,CAACC,IAAY;EACzB,QAAOA,IAAI;IACP,KAAK,SAAS;MACV,OAAO,2BAA2B;IACtC,KAAK,QAAQ;MACT,OAAO,0BAA0B;IACrC,KAAK,SAAS;MACV,OAAO,2BAA2B;IAEtC,KAAK,UAAU;MACX,OAAO,2BAA2B;IACtC,KAAK,iBAAiB;MAClB,OAAO,0BAA0B;IACrC,KAAK,kBAAkB;MACnB,OAAO,2BAA2B;IACtC,KAAK,MAAM;MACP,OAAO,4BAA4B;IACvC,KAAK,aAAa;MACd,OAAO,2BAA2B;IACtC,KAAK,cAAc;MACf,OAAO,4BAA4B;IACvC,KAAK,OAAO;MACR,OAAO,+BAA+B;IAC1C,KAAK,YAAY;MACb,OAAO,4BAA4B;IACvC,KAAK,cAAc;MACf,OAAO,8BAA8B;IACzC,KAAK,UAAU;MACX,OAAO,2BAA2B;IACtC,KAAK,iBAAiB;MAClB,OAAO,0BAA0B;IACrC,KAAK,kBAAkB;MACnB,OAAO,2BAA2B;;EAG1CP,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEO,IAAI,CAAC;AACjE;AAEA;;;;;;;;;;;AAWA,OAAM,MAAOC,eAAgB,SAAQJ,eAAe;EACvCK,MAAM;EAEfC,YAAYC,QAAqB,EAAEF,MAAsB;IACrD,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGT,OAAO,CAACU,IAAI,CAACF,QAAQ,CAAC;IACtC,IAAIF,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,aAAa;;IAE5C,MAAMS,OAAO,GAAGN,eAAe,CAACO,UAAU,CAACH,OAAO,EAAEH,MAAM,CAAC;IAC3D,KAAK,CAACK,OAAO,EAAEF,OAAO,EAAE;MAAEI,aAAa,EAAEJ;IAAO,CAAE,CAAC;IAEnDf,gBAAgB,CAAkB,IAAI,EAAE;MAAEY;IAAM,CAAE,CAAC;EACvD;EAEAQ,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIV,eAAe,CAACU,OAAO,EAAE,IAAI,CAACT,MAAM,CAAC;KACnD,CAAC,OAAOU,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEA,MAAME,QAAQA,CAACC,GAAyB;IAEpC;IACA,IAAIA,GAAG,CAACC,MAAM,KAAK,sBAAsB,EAAE;MACvC,MAAM;QAAEC,KAAK;QAAEC;MAAE,CAAE,GAAG,MAAM1B,iBAAiB,CAAC;QAC1CyB,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC,mBAAmB,EAAE,CAAEJ,GAAG,CAACK,IAAI,CAAE,CAAC;QACnDF,EAAE,EAAE,IAAI,CAACG,cAAc,CAACN,GAAG,CAACK,IAAI;OACnC,CAAC;MACF,IAAIH,KAAK,IAAI,IAAI,IAAIC,EAAE,IAAI,IAAI,EAAE;QAAE,OAAO,IAAI;;MAE9C,IAAII,IAAwB;MAC5B,IAAIT,KAAK,GAAG,KAAK;MACjB,IAAI;QACAS,IAAI,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACM,MAAM,CAACC,MAAM;QAC7BX,KAAK,GAAII,KAAK,CAAC,CAAC,CAAC,CAACJ,KAAK,KAAK,UAAW;OAC1C,CAAC,OAAOA,KAAK,EAAE;MAEhB,IAAIS,IAAI,EAAE;QACN7B,MAAM,CAAC,CAACoB,KAAK,EAAE,iDAAiD,EAAE,gBAAgB,EAAE;UAChFY,MAAM,EAAE,sBAAsB;UAC9BH,IAAI;UACJI,MAAM,EAAE,IAAI;UACZC,WAAW,EAAET,EAAE;UACfU,UAAU,EAAE,IAAI;UAChBC,MAAM,EAAE,IAAI,CAAC;SAChB,CAAC;QACF,OAAOP,IAAI;;MAGf7B,MAAM,CAAC,KAAK,EAAE,8BAA8B,EAAE,UAAU,EAAE;QAAEqC,KAAK,EAAEb;MAAK,CAAE,CAAC;;IAG/E,OAAO,MAAM,KAAK,CAACH,QAAQ,CAACC,GAAG,CAAC;EACpC;EAEAgB,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAAC5B,MAAM,KAAKJ,aAAa;EACzC;EAEA,OAAOU,UAAUA,CAACH,OAAgB,EAAEH,MAAe;IAC/C,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,aAAa;;IAE5C,MAAMS,OAAO,GAAG,IAAIb,YAAY,CAAC,YAAaK,OAAO,CAACM,OAAO,CAACL,IAAI,CAAE,OAAQE,MAAO,EAAE,CAAC;IACtFK,OAAO,CAACwB,SAAS,GAAG,IAAI;IAExB,IAAI7B,MAAM,KAAKJ,aAAa,EAAE;MAC1BS,OAAO,CAACyB,SAAS,GAAG,OAAOzB,OAAO,EAAE0B,QAAQ,EAAEC,OAAO,KAAI;QACrDvC,mBAAmB,CAAC,SAAS,CAAC;QAC9B,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOY,OAAO;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}