// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title CQTToken
 * @dev CryptoQuest Token (CQT) - ERC20 token for the CryptoQuest game ecosystem
 * 
 * Features:
 * - Fixed total supply of 1 billion tokens
 * - Burnable tokens (for upgrades and game mechanics)
 * - Pausable transfers (emergency control)
 * - Owner-controlled minting (only during deployment)
 * - Game reward distribution mechanics
 */
contract CQTToken is ERC20, ERC20Burnable, Ownable, Pausable {
    
    // Total supply: 1 billion CQT tokens (18 decimals)
    uint256 public constant TOTAL_SUPPLY = 1_000_000_000 * 10**18;
    
    // Token allocation percentages
    uint256 public constant PLAY_TO_EARN_ALLOCATION = 400_000_000 * 10**18; // 40%
    uint256 public constant ECOSYSTEM_ALLOCATION = 200_000_000 * 10**18;    // 20%
    uint256 public constant TEAM_ALLOCATION = 150_000_000 * 10**18;         // 15%
    uint256 public constant MARKETING_ALLOCATION = 100_000_000 * 10**18;    // 10%
    uint256 public constant STAKING_ALLOCATION = 100_000_000 * 10**18;      // 10%
    uint256 public constant LIQUIDITY_ALLOCATION = 50_000_000 * 10**18;     // 5%
    
    // Addresses for token allocation
    address public playToEarnPool;
    address public ecosystemFund;
    address public teamWallet;
    address public marketingWallet;
    address public stakingPool;
    address public liquidityPool;
    
    // Game contracts that can distribute rewards
    mapping(address => bool) public gameContracts;
    
    // Events
    event GameContractAdded(address indexed gameContract);
    event GameContractRemoved(address indexed gameContract);
    event TokensDistributed(address indexed to, uint256 amount, string reason);
    
    constructor(
        address _playToEarnPool,
        address _ecosystemFund,
        address _teamWallet,
        address _marketingWallet,
        address _stakingPool,
        address _liquidityPool
    ) ERC20("CryptoQuest Token", "CQT") Ownable(msg.sender) {
        
        // Set allocation addresses
        playToEarnPool = _playToEarnPool;
        ecosystemFund = _ecosystemFund;
        teamWallet = _teamWallet;
        marketingWallet = _marketingWallet;
        stakingPool = _stakingPool;
        liquidityPool = _liquidityPool;
        
        // Mint tokens to respective pools
        _mint(_playToEarnPool, PLAY_TO_EARN_ALLOCATION);
        _mint(_ecosystemFund, ECOSYSTEM_ALLOCATION);
        _mint(_teamWallet, TEAM_ALLOCATION);
        _mint(_marketingWallet, MARKETING_ALLOCATION);
        _mint(_stakingPool, STAKING_ALLOCATION);
        _mint(_liquidityPool, LIQUIDITY_ALLOCATION);
        
        // Verify total supply
        require(totalSupply() == TOTAL_SUPPLY, "Total supply mismatch");
    }
    
    /**
     * @dev Add a game contract that can distribute rewards
     */
    function addGameContract(address _gameContract) external onlyOwner {
        require(_gameContract != address(0), "Invalid address");
        gameContracts[_gameContract] = true;
        emit GameContractAdded(_gameContract);
    }
    
    /**
     * @dev Remove a game contract
     */
    function removeGameContract(address _gameContract) external onlyOwner {
        gameContracts[_gameContract] = false;
        emit GameContractRemoved(_gameContract);
    }
    
    /**
     * @dev Distribute rewards to players (only callable by game contracts)
     */
    function distributeReward(address _player, uint256 _amount, string memory _reason) external {
        require(gameContracts[msg.sender], "Not authorized game contract");
        require(_player != address(0), "Invalid player address");
        require(_amount > 0, "Amount must be greater than 0");
        
        // Transfer from play-to-earn pool to player
        _transfer(playToEarnPool, _player, _amount);
        
        emit TokensDistributed(_player, _amount, _reason);
    }
    
    /**
     * @dev Burn tokens from a specific address (for game upgrades)
     */
    function burnFrom(address _from, uint256 _amount) public override {
        require(gameContracts[msg.sender] || _from == msg.sender, "Not authorized to burn");
        super.burnFrom(_from, _amount);
    }
    
    /**
     * @dev Pause token transfers (emergency function)
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev Unpause token transfers
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev Override transfer to include pause functionality
     */
    function _update(address from, address to, uint256 value) internal override whenNotPaused {
        super._update(from, to, value);
    }
    
    /**
     * @dev Get remaining tokens in play-to-earn pool
     */
    function getPlayToEarnBalance() external view returns (uint256) {
        return balanceOf(playToEarnPool);
    }
    
    /**
     * @dev Emergency withdrawal function (only owner)
     */
    function emergencyWithdraw(address _token, address _to, uint256 _amount) external onlyOwner {
        require(_token != address(this), "Cannot withdraw CQT tokens");
        IERC20(_token).transfer(_to, _amount);
    }
}
