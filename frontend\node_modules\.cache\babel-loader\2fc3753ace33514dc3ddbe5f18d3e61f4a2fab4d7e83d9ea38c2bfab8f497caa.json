{"ast": null, "code": "//import { TypedDataDomain, TypedDataField } from \"@ethersproject/providerabstract-signer\";\nimport { getAddress } from \"../address/index.js\";\nimport { keccak256 } from \"../crypto/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport { concat, defineProperties, getBigInt, getBytes, hexlify, isHexString, mask, toBeHex, toQuantity, toTwos, zeroPadValue, assertArgument } from \"../utils/index.js\";\nimport { id } from \"./id.js\";\nconst padding = new Uint8Array(32);\npadding.fill(0);\nconst BN__1 = BigInt(-1);\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_MAX_UINT256 = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n;\n;\nfunction hexPadRight(value) {\n  const bytes = getBytes(value);\n  const padOffset = bytes.length % 32;\n  if (padOffset) {\n    return concat([bytes, padding.slice(padOffset)]);\n  }\n  return hexlify(bytes);\n}\nconst hexTrue = toBeHex(BN_1, 32);\nconst hexFalse = toBeHex(BN_0, 32);\nconst domainFieldTypes = {\n  name: \"string\",\n  version: \"string\",\n  chainId: \"uint256\",\n  verifyingContract: \"address\",\n  salt: \"bytes32\"\n};\nconst domainFieldNames = [\"name\", \"version\", \"chainId\", \"verifyingContract\", \"salt\"];\nfunction checkString(key) {\n  return function (value) {\n    assertArgument(typeof value === \"string\", `invalid domain value for ${JSON.stringify(key)}`, `domain.${key}`, value);\n    return value;\n  };\n}\nconst domainChecks = {\n  name: checkString(\"name\"),\n  version: checkString(\"version\"),\n  chainId: function (_value) {\n    const value = getBigInt(_value, \"domain.chainId\");\n    assertArgument(value >= 0, \"invalid chain ID\", \"domain.chainId\", _value);\n    if (Number.isSafeInteger(value)) {\n      return Number(value);\n    }\n    return toQuantity(value);\n  },\n  verifyingContract: function (value) {\n    try {\n      return getAddress(value).toLowerCase();\n    } catch (error) {}\n    assertArgument(false, `invalid domain value \"verifyingContract\"`, \"domain.verifyingContract\", value);\n  },\n  salt: function (value) {\n    const bytes = getBytes(value, \"domain.salt\");\n    assertArgument(bytes.length === 32, `invalid domain value \"salt\"`, \"domain.salt\", value);\n    return hexlify(bytes);\n  }\n};\nfunction getBaseEncoder(type) {\n  // intXX and uintXX\n  {\n    const match = type.match(/^(u?)int(\\d+)$/);\n    if (match) {\n      const signed = match[1] === \"\";\n      const width = parseInt(match[2]);\n      assertArgument(width % 8 === 0 && width !== 0 && width <= 256 && match[2] === String(width), \"invalid numeric width\", \"type\", type);\n      const boundsUpper = mask(BN_MAX_UINT256, signed ? width - 1 : width);\n      const boundsLower = signed ? (boundsUpper + BN_1) * BN__1 : BN_0;\n      return function (_value) {\n        const value = getBigInt(_value, \"value\");\n        assertArgument(value >= boundsLower && value <= boundsUpper, `value out-of-bounds for ${type}`, \"value\", value);\n        return toBeHex(signed ? toTwos(value, 256) : value, 32);\n      };\n    }\n  }\n  // bytesXX\n  {\n    const match = type.match(/^bytes(\\d+)$/);\n    if (match) {\n      const width = parseInt(match[1]);\n      assertArgument(width !== 0 && width <= 32 && match[1] === String(width), \"invalid bytes width\", \"type\", type);\n      return function (value) {\n        const bytes = getBytes(value);\n        assertArgument(bytes.length === width, `invalid length for ${type}`, \"value\", value);\n        return hexPadRight(value);\n      };\n    }\n  }\n  switch (type) {\n    case \"address\":\n      return function (value) {\n        return zeroPadValue(getAddress(value), 32);\n      };\n    case \"bool\":\n      return function (value) {\n        return !value ? hexFalse : hexTrue;\n      };\n    case \"bytes\":\n      return function (value) {\n        return keccak256(value);\n      };\n    case \"string\":\n      return function (value) {\n        return id(value);\n      };\n  }\n  return null;\n}\nfunction encodeType(name, fields) {\n  return `${name}(${fields.map(({\n    name,\n    type\n  }) => type + \" \" + name).join(\",\")})`;\n}\n// foo[][3] => { base: \"foo\", index: \"[][3]\", array: {\n//     base: \"foo\", prefix: \"foo[]\", count: 3 } }\nfunction splitArray(type) {\n  const match = type.match(/^([^\\x5b]*)((\\x5b\\d*\\x5d)*)(\\x5b(\\d*)\\x5d)$/);\n  if (match) {\n    return {\n      base: match[1],\n      index: match[2] + match[4],\n      array: {\n        base: match[1],\n        prefix: match[1] + match[2],\n        count: match[5] ? parseInt(match[5]) : -1\n      }\n    };\n  }\n  return {\n    base: type\n  };\n}\n/**\n *  A **TypedDataEncode** prepares and encodes [[link-eip-712]] payloads\n *  for signed typed data.\n *\n *  This is useful for those that wish to compute various components of a\n *  typed data hash, primary types, or sub-components, but generally the\n *  higher level [[Signer-signTypedData]] is more useful.\n */\nexport class TypedDataEncoder {\n  /**\n   *  The primary type for the structured [[types]].\n   *\n   *  This is derived automatically from the [[types]], since no\n   *  recursion is possible, once the DAG for the types is consturcted\n   *  internally, the primary type must be the only remaining type with\n   *  no parent nodes.\n   */\n  primaryType;\n  #types;\n  /**\n   *  The types.\n   */\n  get types() {\n    return JSON.parse(this.#types);\n  }\n  #fullTypes;\n  #encoderCache;\n  /**\n   *  Create a new **TypedDataEncoder** for %%types%%.\n   *\n   *  This performs all necessary checking that types are valid and\n   *  do not violate the [[link-eip-712]] structural constraints as\n   *  well as computes the [[primaryType]].\n   */\n  constructor(_types) {\n    this.#fullTypes = new Map();\n    this.#encoderCache = new Map();\n    // Link struct types to their direct child structs\n    const links = new Map();\n    // Link structs to structs which contain them as a child\n    const parents = new Map();\n    // Link all subtypes within a given struct\n    const subtypes = new Map();\n    const types = {};\n    Object.keys(_types).forEach(type => {\n      types[type] = _types[type].map(({\n        name,\n        type\n      }) => {\n        // Normalize the base type (unless name conflict)\n        let {\n          base,\n          index\n        } = splitArray(type);\n        if (base === \"int\" && !_types[\"int\"]) {\n          base = \"int256\";\n        }\n        if (base === \"uint\" && !_types[\"uint\"]) {\n          base = \"uint256\";\n        }\n        return {\n          name,\n          type: base + (index || \"\")\n        };\n      });\n      links.set(type, new Set());\n      parents.set(type, []);\n      subtypes.set(type, new Set());\n    });\n    this.#types = JSON.stringify(types);\n    for (const name in types) {\n      const uniqueNames = new Set();\n      for (const field of types[name]) {\n        // Check each field has a unique name\n        assertArgument(!uniqueNames.has(field.name), `duplicate variable name ${JSON.stringify(field.name)} in ${JSON.stringify(name)}`, \"types\", _types);\n        uniqueNames.add(field.name);\n        // Get the base type (drop any array specifiers)\n        const baseType = splitArray(field.type).base;\n        assertArgument(baseType !== name, `circular type reference to ${JSON.stringify(baseType)}`, \"types\", _types);\n        // Is this a base encoding type?\n        const encoder = getBaseEncoder(baseType);\n        if (encoder) {\n          continue;\n        }\n        assertArgument(parents.has(baseType), `unknown type ${JSON.stringify(baseType)}`, \"types\", _types);\n        // Add linkage\n        parents.get(baseType).push(name);\n        links.get(name).add(baseType);\n      }\n    }\n    // Deduce the primary type\n    const primaryTypes = Array.from(parents.keys()).filter(n => parents.get(n).length === 0);\n    assertArgument(primaryTypes.length !== 0, \"missing primary type\", \"types\", _types);\n    assertArgument(primaryTypes.length === 1, `ambiguous primary types or unused types: ${primaryTypes.map(t => JSON.stringify(t)).join(\", \")}`, \"types\", _types);\n    defineProperties(this, {\n      primaryType: primaryTypes[0]\n    });\n    // Check for circular type references\n    function checkCircular(type, found) {\n      assertArgument(!found.has(type), `circular type reference to ${JSON.stringify(type)}`, \"types\", _types);\n      found.add(type);\n      for (const child of links.get(type)) {\n        if (!parents.has(child)) {\n          continue;\n        }\n        // Recursively check children\n        checkCircular(child, found);\n        // Mark all ancestors as having this decendant\n        for (const subtype of found) {\n          subtypes.get(subtype).add(child);\n        }\n      }\n      found.delete(type);\n    }\n    checkCircular(this.primaryType, new Set());\n    // Compute each fully describe type\n    for (const [name, set] of subtypes) {\n      const st = Array.from(set);\n      st.sort();\n      this.#fullTypes.set(name, encodeType(name, types[name]) + st.map(t => encodeType(t, types[t])).join(\"\"));\n    }\n  }\n  /**\n   *  Returnthe encoder for the specific %%type%%.\n   */\n  getEncoder(type) {\n    let encoder = this.#encoderCache.get(type);\n    if (!encoder) {\n      encoder = this.#getEncoder(type);\n      this.#encoderCache.set(type, encoder);\n    }\n    return encoder;\n  }\n  #getEncoder(type) {\n    // Basic encoder type (address, bool, uint256, etc)\n    {\n      const encoder = getBaseEncoder(type);\n      if (encoder) {\n        return encoder;\n      }\n    }\n    // Array\n    const array = splitArray(type).array;\n    if (array) {\n      const subtype = array.prefix;\n      const subEncoder = this.getEncoder(subtype);\n      return value => {\n        assertArgument(array.count === -1 || array.count === value.length, `array length mismatch; expected length ${array.count}`, \"value\", value);\n        let result = value.map(subEncoder);\n        if (this.#fullTypes.has(subtype)) {\n          result = result.map(keccak256);\n        }\n        return keccak256(concat(result));\n      };\n    }\n    // Struct\n    const fields = this.types[type];\n    if (fields) {\n      const encodedType = id(this.#fullTypes.get(type));\n      return value => {\n        const values = fields.map(({\n          name,\n          type\n        }) => {\n          const result = this.getEncoder(type)(value[name]);\n          if (this.#fullTypes.has(type)) {\n            return keccak256(result);\n          }\n          return result;\n        });\n        values.unshift(encodedType);\n        return concat(values);\n      };\n    }\n    assertArgument(false, `unknown type: ${type}`, \"type\", type);\n  }\n  /**\n   *  Return the full type for %%name%%.\n   */\n  encodeType(name) {\n    const result = this.#fullTypes.get(name);\n    assertArgument(result, `unknown type: ${JSON.stringify(name)}`, \"name\", name);\n    return result;\n  }\n  /**\n   *  Return the encoded %%value%% for the %%type%%.\n   */\n  encodeData(type, value) {\n    return this.getEncoder(type)(value);\n  }\n  /**\n   *  Returns the hash of %%value%% for the type of %%name%%.\n   */\n  hashStruct(name, value) {\n    return keccak256(this.encodeData(name, value));\n  }\n  /**\n   *  Return the fulled encoded %%value%% for the [[types]].\n   */\n  encode(value) {\n    return this.encodeData(this.primaryType, value);\n  }\n  /**\n   *  Return the hash of the fully encoded %%value%% for the [[types]].\n   */\n  hash(value) {\n    return this.hashStruct(this.primaryType, value);\n  }\n  /**\n   *  @_ignore:\n   */\n  _visit(type, value, callback) {\n    // Basic encoder type (address, bool, uint256, etc)\n    {\n      const encoder = getBaseEncoder(type);\n      if (encoder) {\n        return callback(type, value);\n      }\n    }\n    // Array\n    const array = splitArray(type).array;\n    if (array) {\n      assertArgument(array.count === -1 || array.count === value.length, `array length mismatch; expected length ${array.count}`, \"value\", value);\n      return value.map(v => this._visit(array.prefix, v, callback));\n    }\n    // Struct\n    const fields = this.types[type];\n    if (fields) {\n      return fields.reduce((accum, {\n        name,\n        type\n      }) => {\n        accum[name] = this._visit(type, value[name], callback);\n        return accum;\n      }, {});\n    }\n    assertArgument(false, `unknown type: ${type}`, \"type\", type);\n  }\n  /**\n   *  Call %%calback%% for each value in %%value%%, passing the type and\n   *  component within %%value%%.\n   *\n   *  This is useful for replacing addresses or other transformation that\n   *  may be desired on each component, based on its type.\n   */\n  visit(value, callback) {\n    return this._visit(this.primaryType, value, callback);\n  }\n  /**\n   *  Create a new **TypedDataEncoder** for %%types%%.\n   */\n  static from(types) {\n    return new TypedDataEncoder(types);\n  }\n  /**\n   *  Return the primary type for %%types%%.\n   */\n  static getPrimaryType(types) {\n    return TypedDataEncoder.from(types).primaryType;\n  }\n  /**\n   *  Return the hashed struct for %%value%% using %%types%% and %%name%%.\n   */\n  static hashStruct(name, types, value) {\n    return TypedDataEncoder.from(types).hashStruct(name, value);\n  }\n  /**\n   *  Return the domain hash for %%domain%%.\n   */\n  static hashDomain(domain) {\n    const domainFields = [];\n    for (const name in domain) {\n      if (domain[name] == null) {\n        continue;\n      }\n      const type = domainFieldTypes[name];\n      assertArgument(type, `invalid typed-data domain key: ${JSON.stringify(name)}`, \"domain\", domain);\n      domainFields.push({\n        name,\n        type\n      });\n    }\n    domainFields.sort((a, b) => {\n      return domainFieldNames.indexOf(a.name) - domainFieldNames.indexOf(b.name);\n    });\n    return TypedDataEncoder.hashStruct(\"EIP712Domain\", {\n      EIP712Domain: domainFields\n    }, domain);\n  }\n  /**\n   *  Return the fully encoded [[link-eip-712]] %%value%% for %%types%% with %%domain%%.\n   */\n  static encode(domain, types, value) {\n    return concat([\"0x1901\", TypedDataEncoder.hashDomain(domain), TypedDataEncoder.from(types).hash(value)]);\n  }\n  /**\n   *  Return the hash of the fully encoded [[link-eip-712]] %%value%% for %%types%% with %%domain%%.\n   */\n  static hash(domain, types, value) {\n    return keccak256(TypedDataEncoder.encode(domain, types, value));\n  }\n  // Replaces all address types with ENS names with their looked up address\n  /**\n   * Resolves to the value from resolving all addresses in %%value%% for\n   * %%types%% and the %%domain%%.\n   */\n  static async resolveNames(domain, types, value, resolveName) {\n    // Make a copy to isolate it from the object passed in\n    domain = Object.assign({}, domain);\n    // Allow passing null to ignore value\n    for (const key in domain) {\n      if (domain[key] == null) {\n        delete domain[key];\n      }\n    }\n    // Look up all ENS names\n    const ensCache = {};\n    // Do we need to look up the domain's verifyingContract?\n    if (domain.verifyingContract && !isHexString(domain.verifyingContract, 20)) {\n      ensCache[domain.verifyingContract] = \"0x\";\n    }\n    // We are going to use the encoder to visit all the base values\n    const encoder = TypedDataEncoder.from(types);\n    // Get a list of all the addresses\n    encoder.visit(value, (type, value) => {\n      if (type === \"address\" && !isHexString(value, 20)) {\n        ensCache[value] = \"0x\";\n      }\n      return value;\n    });\n    // Lookup each name\n    for (const name in ensCache) {\n      ensCache[name] = await resolveName(name);\n    }\n    // Replace the domain verifyingContract if needed\n    if (domain.verifyingContract && ensCache[domain.verifyingContract]) {\n      domain.verifyingContract = ensCache[domain.verifyingContract];\n    }\n    // Replace all ENS names with their address\n    value = encoder.visit(value, (type, value) => {\n      if (type === \"address\" && ensCache[value]) {\n        return ensCache[value];\n      }\n      return value;\n    });\n    return {\n      domain,\n      value\n    };\n  }\n  /**\n   *  Returns the JSON-encoded payload expected by nodes which implement\n   *  the JSON-RPC [[link-eip-712]] method.\n   */\n  static getPayload(domain, types, value) {\n    // Validate the domain fields\n    TypedDataEncoder.hashDomain(domain);\n    // Derive the EIP712Domain Struct reference type\n    const domainValues = {};\n    const domainTypes = [];\n    domainFieldNames.forEach(name => {\n      const value = domain[name];\n      if (value == null) {\n        return;\n      }\n      domainValues[name] = domainChecks[name](value);\n      domainTypes.push({\n        name,\n        type: domainFieldTypes[name]\n      });\n    });\n    const encoder = TypedDataEncoder.from(types);\n    // Get the normalized types\n    types = encoder.types;\n    const typesWithDomain = Object.assign({}, types);\n    assertArgument(typesWithDomain.EIP712Domain == null, \"types must not contain EIP712Domain type\", \"types.EIP712Domain\", types);\n    typesWithDomain.EIP712Domain = domainTypes;\n    // Validate the data structures and types\n    encoder.encode(value);\n    return {\n      types: typesWithDomain,\n      domain: domainValues,\n      primaryType: encoder.primaryType,\n      message: encoder.visit(value, (type, value) => {\n        // bytes\n        if (type.match(/^bytes(\\d*)/)) {\n          return hexlify(getBytes(value));\n        }\n        // uint or int\n        if (type.match(/^u?int/)) {\n          return getBigInt(value).toString();\n        }\n        switch (type) {\n          case \"address\":\n            return value.toLowerCase();\n          case \"bool\":\n            return !!value;\n          case \"string\":\n            assertArgument(typeof value === \"string\", \"invalid string\", \"value\", value);\n            return value;\n        }\n        assertArgument(false, \"unsupported type\", \"type\", type);\n      })\n    };\n  }\n}\n/**\n *  Compute the address used to sign the typed data for the %%signature%%.\n */\nexport function verifyTypedData(domain, types, value, signature) {\n  return recoverAddress(TypedDataEncoder.hash(domain, types, value), signature);\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "keccak256", "recoverAddress", "concat", "defineProperties", "getBigInt", "getBytes", "hexlify", "isHexString", "mask", "toBeHex", "toQuantity", "toTwos", "zeroPadValue", "assertArgument", "id", "padding", "Uint8Array", "fill", "BN__1", "BigInt", "BN_0", "BN_1", "BN_MAX_UINT256", "hexPadRight", "value", "bytes", "padOffset", "length", "slice", "hexTrue", "hexFalse", "domainFieldTypes", "name", "version", "chainId", "verifyingContract", "salt", "domainFieldNames", "checkString", "key", "JSON", "stringify", "domainChecks", "_value", "Number", "isSafeInteger", "toLowerCase", "error", "getBaseEncoder", "type", "match", "signed", "width", "parseInt", "String", "boundsUpper", "boundsLower", "encodeType", "fields", "map", "join", "splitArray", "base", "index", "array", "prefix", "count", "TypedDataEncoder", "primaryType", "types", "parse", "fullTypes", "encoderCache", "constructor", "_types", "Map", "links", "parents", "subtypes", "Object", "keys", "for<PERSON>ach", "set", "Set", "uniqueNames", "field", "has", "add", "baseType", "encoder", "get", "push", "primaryTypes", "Array", "from", "filter", "n", "t", "checkCircular", "found", "child", "subtype", "delete", "st", "sort", "get<PERSON>ncoder", "#getEncoder", "subEncoder", "result", "encodedType", "values", "unshift", "encodeData", "hashStruct", "encode", "hash", "_visit", "callback", "v", "reduce", "accum", "visit", "getPrimaryType", "hashDomain", "domain", "domain<PERSON>ields", "a", "b", "indexOf", "EIP712Domain", "resolveNames", "resolveName", "assign", "ensCache", "getPayload", "domainValues", "domainTypes", "typesWithDomain", "message", "toString", "verifyTypedData", "signature"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\typed-data.ts"], "sourcesContent": ["//import { TypedDataDomain, TypedDataField } from \"@ethersproject/providerabstract-signer\";\nimport { getAddress } from \"../address/index.js\";\nimport { keccak256 } from \"../crypto/index.js\";\nimport { recoverAddress } from \"../transaction/index.js\";\nimport {\n    concat, defineProperties, getBigInt, getBytes, hexlify, isHexString, mask, toBeHex, toQuantity, toTwos, zeroPadValue,\n    assertArgument\n} from \"../utils/index.js\";\n\nimport { id } from \"./id.js\";\n\nimport type { SignatureLike } from \"../crypto/index.js\";\nimport type { BigNumberish, BytesLike } from \"../utils/index.js\";\n\n\nconst padding = new Uint8Array(32);\npadding.fill(0);\n\nconst BN__1 = BigInt(-1);\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_MAX_UINT256 = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n\n// @TODO: in v7, verifyingContract should be an AddressLike and use resolveAddress\n\n/**\n *  The domain for an [[link-eip-712]] payload.\n */\nexport interface TypedDataDomain {\n    /**\n     *  The human-readable name of the signing domain.\n     */\n    name?: null | string;\n\n    /**\n     *  The major version of the signing domain.\n     */\n    version?: null | string;\n\n    /**\n     *  The chain ID of the signing domain.\n     */\n    chainId?: null | BigNumberish;\n\n    /**\n     *  The the address of the contract that will verify the signature.\n     */\n    verifyingContract?: null | string;\n\n    /**\n     *  A salt used for purposes decided by the specific domain.\n     */\n    salt?: null | BytesLike;\n};\n\n/**\n *  A specific field of a structured [[link-eip-712]] type.\n */\nexport interface TypedDataField {\n    /**\n     *  The field name.\n     */\n    name: string;\n\n    /**\n     *  The type of the field.\n     */\n    type: string;\n};\n\nfunction hexPadRight(value: BytesLike): string {\n    const bytes = getBytes(value);\n    const padOffset = bytes.length % 32\n    if (padOffset) {\n        return concat([ bytes, padding.slice(padOffset) ]);\n    }\n    return hexlify(bytes);\n}\n\nconst hexTrue = toBeHex(BN_1, 32);\nconst hexFalse = toBeHex(BN_0, 32);\n\nconst domainFieldTypes: Record<string, string> = {\n    name: \"string\",\n    version: \"string\",\n    chainId: \"uint256\",\n    verifyingContract: \"address\",\n    salt: \"bytes32\"\n};\n\nconst domainFieldNames: Array<string> = [\n    \"name\", \"version\", \"chainId\", \"verifyingContract\", \"salt\"\n];\n\nfunction checkString(key: string): (value: any) => string {\n    return function (value: any){\n        assertArgument(typeof(value) === \"string\", `invalid domain value for ${ JSON.stringify(key) }`, `domain.${ key }`, value);\n        return value;\n    }\n}\n\nconst domainChecks: Record<string, (value: any) => any> = {\n    name: checkString(\"name\"),\n    version: checkString(\"version\"),\n    chainId: function(_value: any) {\n        const value = getBigInt(_value, \"domain.chainId\");\n        assertArgument(value >= 0, \"invalid chain ID\", \"domain.chainId\", _value);\n        if (Number.isSafeInteger(value)) { return Number(value); }\n        return toQuantity(value);\n    },\n    verifyingContract: function(value: any) {\n        try {\n            return getAddress(value).toLowerCase();\n        } catch (error) { }\n        assertArgument(false, `invalid domain value \"verifyingContract\"`, \"domain.verifyingContract\", value);\n    },\n    salt: function(value: any) {\n        const bytes = getBytes(value, \"domain.salt\");\n        assertArgument(bytes.length === 32, `invalid domain value \"salt\"`, \"domain.salt\", value);\n        return hexlify(bytes);\n    }\n}\n\nfunction getBaseEncoder(type: string): null | ((value: any) => string) {\n    // intXX and uintXX\n    {\n        const match = type.match(/^(u?)int(\\d+)$/);\n        if (match) {\n            const signed = (match[1] === \"\");\n\n            const width = parseInt(match[2]);\n            assertArgument(width % 8 === 0 && width !== 0 && width <= 256 && match[2] === String(width), \"invalid numeric width\", \"type\", type);\n\n            const boundsUpper = mask(BN_MAX_UINT256, signed ? (width - 1): width);\n            const boundsLower = signed ? ((boundsUpper + BN_1) * BN__1): BN_0;\n\n            return function(_value: BigNumberish) {\n                const value = getBigInt(_value, \"value\");\n\n                assertArgument(value >= boundsLower && value <= boundsUpper, `value out-of-bounds for ${ type }`, \"value\", value);\n\n                return toBeHex(signed ? toTwos(value, 256): value, 32);\n            };\n        }\n    }\n\n    // bytesXX\n    {\n        const match = type.match(/^bytes(\\d+)$/);\n        if (match) {\n            const width = parseInt(match[1]);\n            assertArgument(width !== 0 && width <= 32 && match[1] === String(width), \"invalid bytes width\", \"type\", type);\n\n            return function(value: BytesLike) {\n                const bytes = getBytes(value);\n                assertArgument(bytes.length === width, `invalid length for ${ type }`, \"value\", value);\n                return hexPadRight(value);\n            };\n        }\n    }\n\n    switch (type) {\n        case \"address\": return function(value: string) {\n            return zeroPadValue(getAddress(value), 32);\n        };\n        case \"bool\": return function(value: boolean) {\n            return ((!value) ? hexFalse: hexTrue);\n        };\n        case \"bytes\": return function(value: BytesLike) {\n            return keccak256(value);\n        };\n        case \"string\": return function(value: string) {\n            return id(value);\n        };\n    }\n\n    return null;\n}\n\nfunction encodeType(name: string, fields: Array<TypedDataField>): string {\n    return `${ name }(${ fields.map(({ name, type }) => (type + \" \" + name)).join(\",\") })`;\n}\n\ntype ArrayResult = {\n    base: string;         // The base type\n    index?: string;       // the full Index (if any)\n    array?: {             // The Array... (if index)\n        base: string;     // ...base type (same as above)\n        prefix: string;   // ...sans the final Index\n        count: number;    // ...the final Index (-1 for dynamic)\n    }\n};\n\n// foo[][3] => { base: \"foo\", index: \"[][3]\", array: {\n//     base: \"foo\", prefix: \"foo[]\", count: 3 } }\nfunction splitArray(type: string): ArrayResult {\n    const match = type.match(/^([^\\x5b]*)((\\x5b\\d*\\x5d)*)(\\x5b(\\d*)\\x5d)$/);\n    if (match) {\n        return {\n            base: match[1],\n            index: (match[2] + match[4]),\n            array: {\n                base: match[1],\n                prefix: (match[1] + match[2]),\n                count: (match[5] ? parseInt(match[5]): -1),\n            }\n        };\n    }\n\n    return { base: type };\n}\n\n/**\n *  A **TypedDataEncode** prepares and encodes [[link-eip-712]] payloads\n *  for signed typed data.\n *\n *  This is useful for those that wish to compute various components of a\n *  typed data hash, primary types, or sub-components, but generally the\n *  higher level [[Signer-signTypedData]] is more useful.\n */\nexport class TypedDataEncoder {\n    /**\n     *  The primary type for the structured [[types]].\n     *\n     *  This is derived automatically from the [[types]], since no\n     *  recursion is possible, once the DAG for the types is consturcted\n     *  internally, the primary type must be the only remaining type with\n     *  no parent nodes.\n     */\n    readonly primaryType!: string;\n\n    readonly #types: string;\n\n    /**\n     *  The types.\n     */\n    get types(): Record<string, Array<TypedDataField>> {\n        return JSON.parse(this.#types);\n    }\n\n    readonly #fullTypes: Map<string, string>\n\n    readonly #encoderCache: Map<string, (value: any) => string>;\n\n    /**\n     *  Create a new **TypedDataEncoder** for %%types%%.\n     *\n     *  This performs all necessary checking that types are valid and\n     *  do not violate the [[link-eip-712]] structural constraints as\n     *  well as computes the [[primaryType]].\n     */\n    constructor(_types: Record<string, Array<TypedDataField>>) {\n        this.#fullTypes = new Map();\n        this.#encoderCache = new Map();\n\n        // Link struct types to their direct child structs\n        const links: Map<string, Set<string>> = new Map();\n\n        // Link structs to structs which contain them as a child\n        const parents: Map<string, Array<string>> = new Map();\n\n        // Link all subtypes within a given struct\n        const subtypes: Map<string, Set<string>> = new Map();\n\n        const types: Record<string, Array<TypedDataField>> = { };\n        Object.keys(_types).forEach((type) => {\n            types[type] = _types[type].map(({ name, type }) => {\n\n                // Normalize the base type (unless name conflict)\n                let { base, index } = splitArray(type);\n                if (base === \"int\" && !_types[\"int\"]) { base = \"int256\"; }\n                if (base === \"uint\" && !_types[\"uint\"]) { base = \"uint256\"; }\n\n                return { name, type: (base + (index || \"\")) };\n            });\n\n            links.set(type, new Set());\n            parents.set(type, [ ]);\n            subtypes.set(type, new Set());\n        });\n        this.#types = JSON.stringify(types);\n\n        for (const name in types) {\n            const uniqueNames: Set<string> = new Set();\n\n            for (const field of types[name]) {\n\n                // Check each field has a unique name\n                assertArgument(!uniqueNames.has(field.name), `duplicate variable name ${ JSON.stringify(field.name) } in ${ JSON.stringify(name) }`, \"types\", _types);\n                uniqueNames.add(field.name);\n\n                // Get the base type (drop any array specifiers)\n                const baseType = splitArray(field.type).base;\n                assertArgument(baseType !== name, `circular type reference to ${ JSON.stringify(baseType) }`, \"types\", _types);\n\n                // Is this a base encoding type?\n                const encoder = getBaseEncoder(baseType);\n                if (encoder) { continue; }\n\n                assertArgument(parents.has(baseType), `unknown type ${ JSON.stringify(baseType) }`, \"types\", _types);\n\n                // Add linkage\n                (parents.get(baseType) as Array<string>).push(name);\n                (links.get(name) as Set<string>).add(baseType);\n            }\n        }\n\n        // Deduce the primary type\n        const primaryTypes = Array.from(parents.keys()).filter((n) => ((parents.get(n) as Array<string>).length === 0));\n        assertArgument(primaryTypes.length !== 0, \"missing primary type\", \"types\", _types);\n        assertArgument(primaryTypes.length === 1, `ambiguous primary types or unused types: ${ primaryTypes.map((t) => (JSON.stringify(t))).join(\", \") }`, \"types\", _types);\n\n        defineProperties<TypedDataEncoder>(this, { primaryType: primaryTypes[0] });\n\n        // Check for circular type references\n        function checkCircular(type: string, found: Set<string>) {\n            assertArgument(!found.has(type), `circular type reference to ${ JSON.stringify(type) }`, \"types\", _types);\n\n            found.add(type);\n\n            for (const child of (links.get(type) as Set<string>)) {\n                if (!parents.has(child)) { continue; }\n\n                // Recursively check children\n                checkCircular(child, found);\n\n                // Mark all ancestors as having this decendant\n                for (const subtype of found) {\n                    (subtypes.get(subtype) as Set<string>).add(child);\n                }\n            }\n\n            found.delete(type);\n        }\n        checkCircular(this.primaryType, new Set());\n\n        // Compute each fully describe type\n        for (const [ name, set ] of subtypes) {\n            const st = Array.from(set);\n            st.sort();\n            this.#fullTypes.set(name, encodeType(name, types[name]) + st.map((t) => encodeType(t, types[t])).join(\"\"));\n        }\n    }\n\n    /**\n     *  Returnthe encoder for the specific %%type%%.\n     */\n    getEncoder(type: string): (value: any) => string {\n        let encoder = this.#encoderCache.get(type);\n        if (!encoder) {\n            encoder = this.#getEncoder(type);\n            this.#encoderCache.set(type, encoder);\n        }\n        return encoder;\n    }\n\n    #getEncoder(type: string): (value: any) => string {\n\n        // Basic encoder type (address, bool, uint256, etc)\n        {\n            const encoder = getBaseEncoder(type);\n            if (encoder) { return encoder; }\n        }\n\n        // Array\n        const array = splitArray(type).array;\n        if (array) {\n            const subtype = array.prefix;\n            const subEncoder = this.getEncoder(subtype);\n            return (value: Array<any>) => {\n                assertArgument(array.count === -1 || array.count === value.length, `array length mismatch; expected length ${ array.count }`, \"value\", value);\n\n                let result = value.map(subEncoder);\n                if (this.#fullTypes.has(subtype)) {\n                    result = result.map(keccak256);\n                }\n\n                return keccak256(concat(result));\n            };\n        }\n\n        // Struct\n        const fields = this.types[type];\n        if (fields) {\n            const encodedType = id(this.#fullTypes.get(type) as string);\n            return (value: Record<string, any>) => {\n                const values = fields.map(({ name, type }) => {\n                    const result = this.getEncoder(type)(value[name]);\n                    if (this.#fullTypes.has(type)) { return keccak256(result); }\n                    return result;\n                });\n                values.unshift(encodedType);\n                return concat(values);\n            }\n        }\n\n        assertArgument(false, `unknown type: ${ type }`, \"type\", type);\n    }\n\n    /**\n     *  Return the full type for %%name%%.\n     */\n    encodeType(name: string): string {\n        const result = this.#fullTypes.get(name);\n        assertArgument(result, `unknown type: ${ JSON.stringify(name) }`, \"name\", name);\n        return result;\n    }\n\n    /**\n     *  Return the encoded %%value%% for the %%type%%.\n     */\n    encodeData(type: string, value: any): string {\n        return this.getEncoder(type)(value);\n    }\n\n    /**\n     *  Returns the hash of %%value%% for the type of %%name%%.\n     */\n    hashStruct(name: string, value: Record<string, any>): string {\n        return keccak256(this.encodeData(name, value));\n    }\n\n    /**\n     *  Return the fulled encoded %%value%% for the [[types]].\n     */\n    encode(value: Record<string, any>): string {\n        return this.encodeData(this.primaryType, value);\n    }\n\n    /**\n     *  Return the hash of the fully encoded %%value%% for the [[types]].\n     */\n    hash(value: Record<string, any>): string {\n        return this.hashStruct(this.primaryType, value);\n    }\n\n    /**\n     *  @_ignore:\n     */\n    _visit(type: string, value: any, callback: (type: string, data: any) => any): any {\n        // Basic encoder type (address, bool, uint256, etc)\n        {\n            const encoder = getBaseEncoder(type);\n            if (encoder) { return callback(type, value); }\n        }\n\n        // Array\n        const array = splitArray(type).array;\n        if (array) {\n            assertArgument(array.count === -1 || array.count === value.length, `array length mismatch; expected length ${ array.count }`, \"value\", value);\n            return value.map((v: any) => this._visit(array.prefix, v, callback));\n        }\n\n        // Struct\n        const fields = this.types[type];\n        if (fields) {\n            return fields.reduce((accum, { name, type }) => {\n                accum[name] = this._visit(type, value[name], callback);\n                return accum;\n            }, <Record<string, any>>{});\n        }\n\n        assertArgument(false, `unknown type: ${ type }`, \"type\", type);\n    }\n\n    /**\n     *  Call %%calback%% for each value in %%value%%, passing the type and\n     *  component within %%value%%.\n     *\n     *  This is useful for replacing addresses or other transformation that\n     *  may be desired on each component, based on its type.\n     */\n    visit(value: Record<string, any>, callback: (type: string, data: any) => any): any {\n        return this._visit(this.primaryType, value, callback);\n    }\n\n    /**\n     *  Create a new **TypedDataEncoder** for %%types%%.\n     */\n    static from(types: Record<string, Array<TypedDataField>>): TypedDataEncoder {\n        return new TypedDataEncoder(types);\n    }\n\n    /**\n     *  Return the primary type for %%types%%.\n     */\n    static getPrimaryType(types: Record<string, Array<TypedDataField>>): string {\n        return TypedDataEncoder.from(types).primaryType;\n    }\n\n    /**\n     *  Return the hashed struct for %%value%% using %%types%% and %%name%%.\n     */\n    static hashStruct(name: string, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): string {\n        return TypedDataEncoder.from(types).hashStruct(name, value);\n    }\n\n    /**\n     *  Return the domain hash for %%domain%%.\n     */\n    static hashDomain(domain: TypedDataDomain): string {\n        const domainFields: Array<TypedDataField> = [ ];\n        for (const name in domain) {\n            if ((<Record<string, any>>domain)[name] == null) { continue; }\n            const type = domainFieldTypes[name];\n            assertArgument(type, `invalid typed-data domain key: ${ JSON.stringify(name) }`, \"domain\", domain);\n            domainFields.push({ name, type });\n        }\n\n        domainFields.sort((a, b) => {\n            return domainFieldNames.indexOf(a.name) - domainFieldNames.indexOf(b.name);\n        });\n\n        return TypedDataEncoder.hashStruct(\"EIP712Domain\", { EIP712Domain: domainFields }, domain);\n    }\n\n    /**\n     *  Return the fully encoded [[link-eip-712]] %%value%% for %%types%% with %%domain%%.\n     */\n    static encode(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): string {\n        return concat([\n            \"0x1901\",\n            TypedDataEncoder.hashDomain(domain),\n            TypedDataEncoder.from(types).hash(value)\n        ]);\n    }\n\n    /**\n     *  Return the hash of the fully encoded [[link-eip-712]] %%value%% for %%types%% with %%domain%%.\n     */\n    static hash(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): string {\n        return keccak256(TypedDataEncoder.encode(domain, types, value));\n    }\n\n    // Replaces all address types with ENS names with their looked up address\n    /**\n     * Resolves to the value from resolving all addresses in %%value%% for\n     * %%types%% and the %%domain%%.\n     */\n    static async resolveNames(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>, resolveName: (name: string) => Promise<string>): Promise<{ domain: TypedDataDomain, value: any }> {\n        // Make a copy to isolate it from the object passed in\n        domain = Object.assign({ }, domain);\n\n        // Allow passing null to ignore value\n        for (const key in domain) {\n            if ((<Record<string, any>>domain)[key] == null) {\n                delete (<Record<string, any>>domain)[key];\n            }\n        }\n\n        // Look up all ENS names\n        const ensCache: Record<string, string> = { };\n\n        // Do we need to look up the domain's verifyingContract?\n        if (domain.verifyingContract && !isHexString(domain.verifyingContract, 20)) {\n            ensCache[domain.verifyingContract] = \"0x\";\n        }\n\n        // We are going to use the encoder to visit all the base values\n        const encoder = TypedDataEncoder.from(types);\n\n        // Get a list of all the addresses\n        encoder.visit(value, (type: string, value: any) => {\n            if (type === \"address\" && !isHexString(value, 20)) {\n                ensCache[value] = \"0x\";\n            }\n            return value;\n        });\n\n        // Lookup each name\n        for (const name in ensCache) {\n            ensCache[name] = await resolveName(name);\n        }\n\n        // Replace the domain verifyingContract if needed\n        if (domain.verifyingContract && ensCache[domain.verifyingContract]) {\n            domain.verifyingContract = ensCache[domain.verifyingContract];\n        }\n\n        // Replace all ENS names with their address\n        value = encoder.visit(value, (type: string, value: any) => {\n            if (type === \"address\" && ensCache[value]) { return ensCache[value]; }\n            return value;\n        });\n\n        return { domain, value };\n    }\n\n    /**\n     *  Returns the JSON-encoded payload expected by nodes which implement\n     *  the JSON-RPC [[link-eip-712]] method.\n     */\n    static getPayload(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): any {\n        // Validate the domain fields\n        TypedDataEncoder.hashDomain(domain);\n\n        // Derive the EIP712Domain Struct reference type\n        const domainValues: Record<string, any> = { };\n        const domainTypes: Array<{ name: string, type:string }> = [ ];\n\n        domainFieldNames.forEach((name) => {\n            const value = (<any>domain)[name];\n            if (value == null) { return; }\n            domainValues[name] = domainChecks[name](value);\n            domainTypes.push({ name, type: domainFieldTypes[name] });\n        });\n\n        const encoder = TypedDataEncoder.from(types);\n\n        // Get the normalized types\n        types = encoder.types;\n\n        const typesWithDomain = Object.assign({ }, types);\n        assertArgument(typesWithDomain.EIP712Domain == null, \"types must not contain EIP712Domain type\", \"types.EIP712Domain\", types);\n\n        typesWithDomain.EIP712Domain = domainTypes;\n\n        // Validate the data structures and types\n        encoder.encode(value);\n\n        return {\n            types: typesWithDomain,\n            domain: domainValues,\n            primaryType: encoder.primaryType,\n            message: encoder.visit(value, (type: string, value: any) => {\n\n                // bytes\n                if (type.match(/^bytes(\\d*)/)) {\n                    return hexlify(getBytes(value));\n                }\n\n                // uint or int\n                if (type.match(/^u?int/)) {\n                    return getBigInt(value).toString();\n                }\n\n                switch (type) {\n                    case \"address\":\n                        return value.toLowerCase();\n                    case \"bool\":\n                        return !!value;\n                    case \"string\":\n                        assertArgument(typeof(value) === \"string\", \"invalid string\", \"value\", value);\n                        return value;\n                }\n\n                assertArgument(false, \"unsupported type\", \"type\", type);\n            })\n        };\n    }\n}\n\n/**\n *  Compute the address used to sign the typed data for the %%signature%%.\n */\nexport function verifyTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>, signature: SignatureLike): string {\n    return recoverAddress(TypedDataEncoder.hash(domain, types, value), signature);\n}\n"], "mappings": "AAAA;AACA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SACIC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,IAAI,EAAEC,OAAO,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EACpHC,cAAc,QACX,mBAAmB;AAE1B,SAASC,EAAE,QAAQ,SAAS;AAM5B,MAAMC,OAAO,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;AAClCD,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;AAEf,MAAMC,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AACtB,MAAME,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMG,cAAc,GAAGH,MAAM,CAAC,oEAAoE,CAAC;AAgClG;AAeA;AAED,SAASI,WAAWA,CAACC,KAAgB;EACjC,MAAMC,KAAK,GAAGpB,QAAQ,CAACmB,KAAK,CAAC;EAC7B,MAAME,SAAS,GAAGD,KAAK,CAACE,MAAM,GAAG,EAAE;EACnC,IAAID,SAAS,EAAE;IACX,OAAOxB,MAAM,CAAC,CAAEuB,KAAK,EAAEV,OAAO,CAACa,KAAK,CAACF,SAAS,CAAC,CAAE,CAAC;;EAEtD,OAAOpB,OAAO,CAACmB,KAAK,CAAC;AACzB;AAEA,MAAMI,OAAO,GAAGpB,OAAO,CAACY,IAAI,EAAE,EAAE,CAAC;AACjC,MAAMS,QAAQ,GAAGrB,OAAO,CAACW,IAAI,EAAE,EAAE,CAAC;AAElC,MAAMW,gBAAgB,GAA2B;EAC7CC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,OAAO,EAAE,SAAS;EAClBC,iBAAiB,EAAE,SAAS;EAC5BC,IAAI,EAAE;CACT;AAED,MAAMC,gBAAgB,GAAkB,CACpC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,EAAE,MAAM,CAC5D;AAED,SAASC,WAAWA,CAACC,GAAW;EAC5B,OAAO,UAAUf,KAAU;IACvBX,cAAc,CAAC,OAAOW,KAAM,KAAK,QAAQ,EAAE,4BAA6BgB,IAAI,CAACC,SAAS,CAACF,GAAG,CAAE,EAAE,EAAE,UAAWA,GAAI,EAAE,EAAEf,KAAK,CAAC;IACzH,OAAOA,KAAK;EAChB,CAAC;AACL;AAEA,MAAMkB,YAAY,GAAwC;EACtDV,IAAI,EAAEM,WAAW,CAAC,MAAM,CAAC;EACzBL,OAAO,EAAEK,WAAW,CAAC,SAAS,CAAC;EAC/BJ,OAAO,EAAE,SAAAA,CAASS,MAAW;IACzB,MAAMnB,KAAK,GAAGpB,SAAS,CAACuC,MAAM,EAAE,gBAAgB,CAAC;IACjD9B,cAAc,CAACW,KAAK,IAAI,CAAC,EAAE,kBAAkB,EAAE,gBAAgB,EAAEmB,MAAM,CAAC;IACxE,IAAIC,MAAM,CAACC,aAAa,CAACrB,KAAK,CAAC,EAAE;MAAE,OAAOoB,MAAM,CAACpB,KAAK,CAAC;;IACvD,OAAOd,UAAU,CAACc,KAAK,CAAC;EAC5B,CAAC;EACDW,iBAAiB,EAAE,SAAAA,CAASX,KAAU;IAClC,IAAI;MACA,OAAOzB,UAAU,CAACyB,KAAK,CAAC,CAACsB,WAAW,EAAE;KACzC,CAAC,OAAOC,KAAK,EAAE;IAChBlC,cAAc,CAAC,KAAK,EAAE,0CAA0C,EAAE,0BAA0B,EAAEW,KAAK,CAAC;EACxG,CAAC;EACDY,IAAI,EAAE,SAAAA,CAASZ,KAAU;IACrB,MAAMC,KAAK,GAAGpB,QAAQ,CAACmB,KAAK,EAAE,aAAa,CAAC;IAC5CX,cAAc,CAACY,KAAK,CAACE,MAAM,KAAK,EAAE,EAAE,6BAA6B,EAAE,aAAa,EAAEH,KAAK,CAAC;IACxF,OAAOlB,OAAO,CAACmB,KAAK,CAAC;EACzB;CACH;AAED,SAASuB,cAAcA,CAACC,IAAY;EAChC;EACA;IACI,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC1C,IAAIA,KAAK,EAAE;MACP,MAAMC,MAAM,GAAID,KAAK,CAAC,CAAC,CAAC,KAAK,EAAG;MAEhC,MAAME,KAAK,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MAChCrC,cAAc,CAACuC,KAAK,GAAG,CAAC,KAAK,CAAC,IAAIA,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,GAAG,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAKI,MAAM,CAACF,KAAK,CAAC,EAAE,uBAAuB,EAAE,MAAM,EAAEH,IAAI,CAAC;MAEnI,MAAMM,WAAW,GAAG/C,IAAI,CAACc,cAAc,EAAE6B,MAAM,GAAIC,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC;MACrE,MAAMI,WAAW,GAAGL,MAAM,GAAI,CAACI,WAAW,GAAGlC,IAAI,IAAIH,KAAK,GAAGE,IAAI;MAEjE,OAAO,UAASuB,MAAoB;QAChC,MAAMnB,KAAK,GAAGpB,SAAS,CAACuC,MAAM,EAAE,OAAO,CAAC;QAExC9B,cAAc,CAACW,KAAK,IAAIgC,WAAW,IAAIhC,KAAK,IAAI+B,WAAW,EAAE,2BAA4BN,IAAK,EAAE,EAAE,OAAO,EAAEzB,KAAK,CAAC;QAEjH,OAAOf,OAAO,CAAC0C,MAAM,GAAGxC,MAAM,CAACa,KAAK,EAAE,GAAG,CAAC,GAAEA,KAAK,EAAE,EAAE,CAAC;MAC1D,CAAC;;;EAIT;EACA;IACI,MAAM0B,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,cAAc,CAAC;IACxC,IAAIA,KAAK,EAAE;MACP,MAAME,KAAK,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MAChCrC,cAAc,CAACuC,KAAK,KAAK,CAAC,IAAIA,KAAK,IAAI,EAAE,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAKI,MAAM,CAACF,KAAK,CAAC,EAAE,qBAAqB,EAAE,MAAM,EAAEH,IAAI,CAAC;MAE7G,OAAO,UAASzB,KAAgB;QAC5B,MAAMC,KAAK,GAAGpB,QAAQ,CAACmB,KAAK,CAAC;QAC7BX,cAAc,CAACY,KAAK,CAACE,MAAM,KAAKyB,KAAK,EAAE,sBAAuBH,IAAK,EAAE,EAAE,OAAO,EAAEzB,KAAK,CAAC;QACtF,OAAOD,WAAW,CAACC,KAAK,CAAC;MAC7B,CAAC;;;EAIT,QAAQyB,IAAI;IACR,KAAK,SAAS;MAAE,OAAO,UAASzB,KAAa;QACzC,OAAOZ,YAAY,CAACb,UAAU,CAACyB,KAAK,CAAC,EAAE,EAAE,CAAC;MAC9C,CAAC;IACD,KAAK,MAAM;MAAE,OAAO,UAASA,KAAc;QACvC,OAAS,CAACA,KAAK,GAAIM,QAAQ,GAAED,OAAO;MACxC,CAAC;IACD,KAAK,OAAO;MAAE,OAAO,UAASL,KAAgB;QAC1C,OAAOxB,SAAS,CAACwB,KAAK,CAAC;MAC3B,CAAC;IACD,KAAK,QAAQ;MAAE,OAAO,UAASA,KAAa;QACxC,OAAOV,EAAE,CAACU,KAAK,CAAC;MACpB,CAAC;;EAGL,OAAO,IAAI;AACf;AAEA,SAASiC,UAAUA,CAACzB,IAAY,EAAE0B,MAA6B;EAC3D,OAAO,GAAI1B,IAAK,IAAK0B,MAAM,CAACC,GAAG,CAAC,CAAC;IAAE3B,IAAI;IAAEiB;EAAI,CAAE,KAAMA,IAAI,GAAG,GAAG,GAAGjB,IAAK,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAE,GAAG;AAC1F;AAYA;AACA;AACA,SAASC,UAAUA,CAACZ,IAAY;EAC5B,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAAC,6CAA6C,CAAC;EACvE,IAAIA,KAAK,EAAE;IACP,OAAO;MACHY,IAAI,EAAEZ,KAAK,CAAC,CAAC,CAAC;MACda,KAAK,EAAGb,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAE;MAC5Bc,KAAK,EAAE;QACHF,IAAI,EAAEZ,KAAK,CAAC,CAAC,CAAC;QACde,MAAM,EAAGf,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAE;QAC7BgB,KAAK,EAAGhB,KAAK,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAE,CAAC;;KAE/C;;EAGL,OAAO;IAAEY,IAAI,EAAEb;EAAI,CAAE;AACzB;AAEA;;;;;;;;AAQA,OAAM,MAAOkB,gBAAgB;EACzB;;;;;;;;EAQSC,WAAW;EAEX,CAAAC,KAAM;EAEf;;;EAGA,IAAIA,KAAKA,CAAA;IACL,OAAO7B,IAAI,CAAC8B,KAAK,CAAC,IAAI,CAAC,CAAAD,KAAM,CAAC;EAClC;EAES,CAAAE,SAAU;EAEV,CAAAC,YAAa;EAEtB;;;;;;;EAOAC,YAAYC,MAA6C;IACrD,IAAI,CAAC,CAAAH,SAAU,GAAG,IAAII,GAAG,EAAE;IAC3B,IAAI,CAAC,CAAAH,YAAa,GAAG,IAAIG,GAAG,EAAE;IAE9B;IACA,MAAMC,KAAK,GAA6B,IAAID,GAAG,EAAE;IAEjD;IACA,MAAME,OAAO,GAA+B,IAAIF,GAAG,EAAE;IAErD;IACA,MAAMG,QAAQ,GAA6B,IAAIH,GAAG,EAAE;IAEpD,MAAMN,KAAK,GAA0C,EAAG;IACxDU,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,OAAO,CAAEhC,IAAI,IAAI;MACjCoB,KAAK,CAACpB,IAAI,CAAC,GAAGyB,MAAM,CAACzB,IAAI,CAAC,CAACU,GAAG,CAAC,CAAC;QAAE3B,IAAI;QAAEiB;MAAI,CAAE,KAAI;QAE9C;QACA,IAAI;UAAEa,IAAI;UAAEC;QAAK,CAAE,GAAGF,UAAU,CAACZ,IAAI,CAAC;QACtC,IAAIa,IAAI,KAAK,KAAK,IAAI,CAACY,MAAM,CAAC,KAAK,CAAC,EAAE;UAAEZ,IAAI,GAAG,QAAQ;;QACvD,IAAIA,IAAI,KAAK,MAAM,IAAI,CAACY,MAAM,CAAC,MAAM,CAAC,EAAE;UAAEZ,IAAI,GAAG,SAAS;;QAE1D,OAAO;UAAE9B,IAAI;UAAEiB,IAAI,EAAGa,IAAI,IAAIC,KAAK,IAAI,EAAE;QAAE,CAAE;MACjD,CAAC,CAAC;MAEFa,KAAK,CAACM,GAAG,CAACjC,IAAI,EAAE,IAAIkC,GAAG,EAAE,CAAC;MAC1BN,OAAO,CAACK,GAAG,CAACjC,IAAI,EAAE,EAAG,CAAC;MACtB6B,QAAQ,CAACI,GAAG,CAACjC,IAAI,EAAE,IAAIkC,GAAG,EAAE,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAAC,CAAAd,KAAM,GAAG7B,IAAI,CAACC,SAAS,CAAC4B,KAAK,CAAC;IAEnC,KAAK,MAAMrC,IAAI,IAAIqC,KAAK,EAAE;MACtB,MAAMe,WAAW,GAAgB,IAAID,GAAG,EAAE;MAE1C,KAAK,MAAME,KAAK,IAAIhB,KAAK,CAACrC,IAAI,CAAC,EAAE;QAE7B;QACAnB,cAAc,CAAC,CAACuE,WAAW,CAACE,GAAG,CAACD,KAAK,CAACrD,IAAI,CAAC,EAAE,2BAA4BQ,IAAI,CAACC,SAAS,CAAC4C,KAAK,CAACrD,IAAI,CAAE,OAAQQ,IAAI,CAACC,SAAS,CAACT,IAAI,CAAE,EAAE,EAAE,OAAO,EAAE0C,MAAM,CAAC;QACrJU,WAAW,CAACG,GAAG,CAACF,KAAK,CAACrD,IAAI,CAAC;QAE3B;QACA,MAAMwD,QAAQ,GAAG3B,UAAU,CAACwB,KAAK,CAACpC,IAAI,CAAC,CAACa,IAAI;QAC5CjD,cAAc,CAAC2E,QAAQ,KAAKxD,IAAI,EAAE,8BAA+BQ,IAAI,CAACC,SAAS,CAAC+C,QAAQ,CAAE,EAAE,EAAE,OAAO,EAAEd,MAAM,CAAC;QAE9G;QACA,MAAMe,OAAO,GAAGzC,cAAc,CAACwC,QAAQ,CAAC;QACxC,IAAIC,OAAO,EAAE;UAAE;;QAEf5E,cAAc,CAACgE,OAAO,CAACS,GAAG,CAACE,QAAQ,CAAC,EAAE,gBAAiBhD,IAAI,CAACC,SAAS,CAAC+C,QAAQ,CAAE,EAAE,EAAE,OAAO,EAAEd,MAAM,CAAC;QAEpG;QACCG,OAAO,CAACa,GAAG,CAACF,QAAQ,CAAmB,CAACG,IAAI,CAAC3D,IAAI,CAAC;QAClD4C,KAAK,CAACc,GAAG,CAAC1D,IAAI,CAAiB,CAACuD,GAAG,CAACC,QAAQ,CAAC;;;IAItD;IACA,MAAMI,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACjB,OAAO,CAACG,IAAI,EAAE,CAAC,CAACe,MAAM,CAAEC,CAAC,IAAOnB,OAAO,CAACa,GAAG,CAACM,CAAC,CAAmB,CAACrE,MAAM,KAAK,CAAE,CAAC;IAC/Gd,cAAc,CAAC+E,YAAY,CAACjE,MAAM,KAAK,CAAC,EAAE,sBAAsB,EAAE,OAAO,EAAE+C,MAAM,CAAC;IAClF7D,cAAc,CAAC+E,YAAY,CAACjE,MAAM,KAAK,CAAC,EAAE,4CAA6CiE,YAAY,CAACjC,GAAG,CAAEsC,CAAC,IAAMzD,IAAI,CAACC,SAAS,CAACwD,CAAC,CAAE,CAAC,CAACrC,IAAI,CAAC,IAAI,CAAE,EAAE,EAAE,OAAO,EAAEc,MAAM,CAAC;IAEnKvE,gBAAgB,CAAmB,IAAI,EAAE;MAAEiE,WAAW,EAAEwB,YAAY,CAAC,CAAC;IAAC,CAAE,CAAC;IAE1E;IACA,SAASM,aAAaA,CAACjD,IAAY,EAAEkD,KAAkB;MACnDtF,cAAc,CAAC,CAACsF,KAAK,CAACb,GAAG,CAACrC,IAAI,CAAC,EAAE,8BAA+BT,IAAI,CAACC,SAAS,CAACQ,IAAI,CAAE,EAAE,EAAE,OAAO,EAAEyB,MAAM,CAAC;MAEzGyB,KAAK,CAACZ,GAAG,CAACtC,IAAI,CAAC;MAEf,KAAK,MAAMmD,KAAK,IAAKxB,KAAK,CAACc,GAAG,CAACzC,IAAI,CAAiB,EAAE;QAClD,IAAI,CAAC4B,OAAO,CAACS,GAAG,CAACc,KAAK,CAAC,EAAE;UAAE;;QAE3B;QACAF,aAAa,CAACE,KAAK,EAAED,KAAK,CAAC;QAE3B;QACA,KAAK,MAAME,OAAO,IAAIF,KAAK,EAAE;UACxBrB,QAAQ,CAACY,GAAG,CAACW,OAAO,CAAiB,CAACd,GAAG,CAACa,KAAK,CAAC;;;MAIzDD,KAAK,CAACG,MAAM,CAACrD,IAAI,CAAC;IACtB;IACAiD,aAAa,CAAC,IAAI,CAAC9B,WAAW,EAAE,IAAIe,GAAG,EAAE,CAAC;IAE1C;IACA,KAAK,MAAM,CAAEnD,IAAI,EAAEkD,GAAG,CAAE,IAAIJ,QAAQ,EAAE;MAClC,MAAMyB,EAAE,GAAGV,KAAK,CAACC,IAAI,CAACZ,GAAG,CAAC;MAC1BqB,EAAE,CAACC,IAAI,EAAE;MACT,IAAI,CAAC,CAAAjC,SAAU,CAACW,GAAG,CAAClD,IAAI,EAAEyB,UAAU,CAACzB,IAAI,EAAEqC,KAAK,CAACrC,IAAI,CAAC,CAAC,GAAGuE,EAAE,CAAC5C,GAAG,CAAEsC,CAAC,IAAKxC,UAAU,CAACwC,CAAC,EAAE5B,KAAK,CAAC4B,CAAC,CAAC,CAAC,CAAC,CAACrC,IAAI,CAAC,EAAE,CAAC,CAAC;;EAElH;EAEA;;;EAGA6C,UAAUA,CAACxD,IAAY;IACnB,IAAIwC,OAAO,GAAG,IAAI,CAAC,CAAAjB,YAAa,CAACkB,GAAG,CAACzC,IAAI,CAAC;IAC1C,IAAI,CAACwC,OAAO,EAAE;MACVA,OAAO,GAAG,IAAI,CAAC,CAAAgB,UAAW,CAACxD,IAAI,CAAC;MAChC,IAAI,CAAC,CAAAuB,YAAa,CAACU,GAAG,CAACjC,IAAI,EAAEwC,OAAO,CAAC;;IAEzC,OAAOA,OAAO;EAClB;EAEA,CAAAgB,UAAWC,CAACzD,IAAY;IAEpB;IACA;MACI,MAAMwC,OAAO,GAAGzC,cAAc,CAACC,IAAI,CAAC;MACpC,IAAIwC,OAAO,EAAE;QAAE,OAAOA,OAAO;;;IAGjC;IACA,MAAMzB,KAAK,GAAGH,UAAU,CAACZ,IAAI,CAAC,CAACe,KAAK;IACpC,IAAIA,KAAK,EAAE;MACP,MAAMqC,OAAO,GAAGrC,KAAK,CAACC,MAAM;MAC5B,MAAM0C,UAAU,GAAG,IAAI,CAACF,UAAU,CAACJ,OAAO,CAAC;MAC3C,OAAQ7E,KAAiB,IAAI;QACzBX,cAAc,CAACmD,KAAK,CAACE,KAAK,KAAK,CAAC,CAAC,IAAIF,KAAK,CAACE,KAAK,KAAK1C,KAAK,CAACG,MAAM,EAAE,0CAA2CqC,KAAK,CAACE,KAAM,EAAE,EAAE,OAAO,EAAE1C,KAAK,CAAC;QAE7I,IAAIoF,MAAM,GAAGpF,KAAK,CAACmC,GAAG,CAACgD,UAAU,CAAC;QAClC,IAAI,IAAI,CAAC,CAAApC,SAAU,CAACe,GAAG,CAACe,OAAO,CAAC,EAAE;UAC9BO,MAAM,GAAGA,MAAM,CAACjD,GAAG,CAAC3D,SAAS,CAAC;;QAGlC,OAAOA,SAAS,CAACE,MAAM,CAAC0G,MAAM,CAAC,CAAC;MACpC,CAAC;;IAGL;IACA,MAAMlD,MAAM,GAAG,IAAI,CAACW,KAAK,CAACpB,IAAI,CAAC;IAC/B,IAAIS,MAAM,EAAE;MACR,MAAMmD,WAAW,GAAG/F,EAAE,CAAC,IAAI,CAAC,CAAAyD,SAAU,CAACmB,GAAG,CAACzC,IAAI,CAAW,CAAC;MAC3D,OAAQzB,KAA0B,IAAI;QAClC,MAAMsF,MAAM,GAAGpD,MAAM,CAACC,GAAG,CAAC,CAAC;UAAE3B,IAAI;UAAEiB;QAAI,CAAE,KAAI;UACzC,MAAM2D,MAAM,GAAG,IAAI,CAACH,UAAU,CAACxD,IAAI,CAAC,CAACzB,KAAK,CAACQ,IAAI,CAAC,CAAC;UACjD,IAAI,IAAI,CAAC,CAAAuC,SAAU,CAACe,GAAG,CAACrC,IAAI,CAAC,EAAE;YAAE,OAAOjD,SAAS,CAAC4G,MAAM,CAAC;;UACzD,OAAOA,MAAM;QACjB,CAAC,CAAC;QACFE,MAAM,CAACC,OAAO,CAACF,WAAW,CAAC;QAC3B,OAAO3G,MAAM,CAAC4G,MAAM,CAAC;MACzB,CAAC;;IAGLjG,cAAc,CAAC,KAAK,EAAE,iBAAkBoC,IAAK,EAAE,EAAE,MAAM,EAAEA,IAAI,CAAC;EAClE;EAEA;;;EAGAQ,UAAUA,CAACzB,IAAY;IACnB,MAAM4E,MAAM,GAAG,IAAI,CAAC,CAAArC,SAAU,CAACmB,GAAG,CAAC1D,IAAI,CAAC;IACxCnB,cAAc,CAAC+F,MAAM,EAAE,iBAAkBpE,IAAI,CAACC,SAAS,CAACT,IAAI,CAAE,EAAE,EAAE,MAAM,EAAEA,IAAI,CAAC;IAC/E,OAAO4E,MAAM;EACjB;EAEA;;;EAGAI,UAAUA,CAAC/D,IAAY,EAAEzB,KAAU;IAC/B,OAAO,IAAI,CAACiF,UAAU,CAACxD,IAAI,CAAC,CAACzB,KAAK,CAAC;EACvC;EAEA;;;EAGAyF,UAAUA,CAACjF,IAAY,EAAER,KAA0B;IAC/C,OAAOxB,SAAS,CAAC,IAAI,CAACgH,UAAU,CAAChF,IAAI,EAAER,KAAK,CAAC,CAAC;EAClD;EAEA;;;EAGA0F,MAAMA,CAAC1F,KAA0B;IAC7B,OAAO,IAAI,CAACwF,UAAU,CAAC,IAAI,CAAC5C,WAAW,EAAE5C,KAAK,CAAC;EACnD;EAEA;;;EAGA2F,IAAIA,CAAC3F,KAA0B;IAC3B,OAAO,IAAI,CAACyF,UAAU,CAAC,IAAI,CAAC7C,WAAW,EAAE5C,KAAK,CAAC;EACnD;EAEA;;;EAGA4F,MAAMA,CAACnE,IAAY,EAAEzB,KAAU,EAAE6F,QAA0C;IACvE;IACA;MACI,MAAM5B,OAAO,GAAGzC,cAAc,CAACC,IAAI,CAAC;MACpC,IAAIwC,OAAO,EAAE;QAAE,OAAO4B,QAAQ,CAACpE,IAAI,EAAEzB,KAAK,CAAC;;;IAG/C;IACA,MAAMwC,KAAK,GAAGH,UAAU,CAACZ,IAAI,CAAC,CAACe,KAAK;IACpC,IAAIA,KAAK,EAAE;MACPnD,cAAc,CAACmD,KAAK,CAACE,KAAK,KAAK,CAAC,CAAC,IAAIF,KAAK,CAACE,KAAK,KAAK1C,KAAK,CAACG,MAAM,EAAE,0CAA2CqC,KAAK,CAACE,KAAM,EAAE,EAAE,OAAO,EAAE1C,KAAK,CAAC;MAC7I,OAAOA,KAAK,CAACmC,GAAG,CAAE2D,CAAM,IAAK,IAAI,CAACF,MAAM,CAACpD,KAAK,CAACC,MAAM,EAAEqD,CAAC,EAAED,QAAQ,CAAC,CAAC;;IAGxE;IACA,MAAM3D,MAAM,GAAG,IAAI,CAACW,KAAK,CAACpB,IAAI,CAAC;IAC/B,IAAIS,MAAM,EAAE;MACR,OAAOA,MAAM,CAAC6D,MAAM,CAAC,CAACC,KAAK,EAAE;QAAExF,IAAI;QAAEiB;MAAI,CAAE,KAAI;QAC3CuE,KAAK,CAACxF,IAAI,CAAC,GAAG,IAAI,CAACoF,MAAM,CAACnE,IAAI,EAAEzB,KAAK,CAACQ,IAAI,CAAC,EAAEqF,QAAQ,CAAC;QACtD,OAAOG,KAAK;MAChB,CAAC,EAAuB,EAAE,CAAC;;IAG/B3G,cAAc,CAAC,KAAK,EAAE,iBAAkBoC,IAAK,EAAE,EAAE,MAAM,EAAEA,IAAI,CAAC;EAClE;EAEA;;;;;;;EAOAwE,KAAKA,CAACjG,KAA0B,EAAE6F,QAA0C;IACxE,OAAO,IAAI,CAACD,MAAM,CAAC,IAAI,CAAChD,WAAW,EAAE5C,KAAK,EAAE6F,QAAQ,CAAC;EACzD;EAEA;;;EAGA,OAAOvB,IAAIA,CAACzB,KAA4C;IACpD,OAAO,IAAIF,gBAAgB,CAACE,KAAK,CAAC;EACtC;EAEA;;;EAGA,OAAOqD,cAAcA,CAACrD,KAA4C;IAC9D,OAAOF,gBAAgB,CAAC2B,IAAI,CAACzB,KAAK,CAAC,CAACD,WAAW;EACnD;EAEA;;;EAGA,OAAO6C,UAAUA,CAACjF,IAAY,EAAEqC,KAA4C,EAAE7C,KAA0B;IACpG,OAAO2C,gBAAgB,CAAC2B,IAAI,CAACzB,KAAK,CAAC,CAAC4C,UAAU,CAACjF,IAAI,EAAER,KAAK,CAAC;EAC/D;EAEA;;;EAGA,OAAOmG,UAAUA,CAACC,MAAuB;IACrC,MAAMC,YAAY,GAA0B,EAAG;IAC/C,KAAK,MAAM7F,IAAI,IAAI4F,MAAM,EAAE;MACvB,IAA0BA,MAAO,CAAC5F,IAAI,CAAC,IAAI,IAAI,EAAE;QAAE;;MACnD,MAAMiB,IAAI,GAAGlB,gBAAgB,CAACC,IAAI,CAAC;MACnCnB,cAAc,CAACoC,IAAI,EAAE,kCAAmCT,IAAI,CAACC,SAAS,CAACT,IAAI,CAAE,EAAE,EAAE,QAAQ,EAAE4F,MAAM,CAAC;MAClGC,YAAY,CAAClC,IAAI,CAAC;QAAE3D,IAAI;QAAEiB;MAAI,CAAE,CAAC;;IAGrC4E,YAAY,CAACrB,IAAI,CAAC,CAACsB,CAAC,EAAEC,CAAC,KAAI;MACvB,OAAO1F,gBAAgB,CAAC2F,OAAO,CAACF,CAAC,CAAC9F,IAAI,CAAC,GAAGK,gBAAgB,CAAC2F,OAAO,CAACD,CAAC,CAAC/F,IAAI,CAAC;IAC9E,CAAC,CAAC;IAEF,OAAOmC,gBAAgB,CAAC8C,UAAU,CAAC,cAAc,EAAE;MAAEgB,YAAY,EAAEJ;IAAY,CAAE,EAAED,MAAM,CAAC;EAC9F;EAEA;;;EAGA,OAAOV,MAAMA,CAACU,MAAuB,EAAEvD,KAA4C,EAAE7C,KAA0B;IAC3G,OAAOtB,MAAM,CAAC,CACV,QAAQ,EACRiE,gBAAgB,CAACwD,UAAU,CAACC,MAAM,CAAC,EACnCzD,gBAAgB,CAAC2B,IAAI,CAACzB,KAAK,CAAC,CAAC8C,IAAI,CAAC3F,KAAK,CAAC,CAC3C,CAAC;EACN;EAEA;;;EAGA,OAAO2F,IAAIA,CAACS,MAAuB,EAAEvD,KAA4C,EAAE7C,KAA0B;IACzG,OAAOxB,SAAS,CAACmE,gBAAgB,CAAC+C,MAAM,CAACU,MAAM,EAAEvD,KAAK,EAAE7C,KAAK,CAAC,CAAC;EACnE;EAEA;EACA;;;;EAIA,aAAa0G,YAAYA,CAACN,MAAuB,EAAEvD,KAA4C,EAAE7C,KAA0B,EAAE2G,WAA8C;IACvK;IACAP,MAAM,GAAG7C,MAAM,CAACqD,MAAM,CAAC,EAAG,EAAER,MAAM,CAAC;IAEnC;IACA,KAAK,MAAMrF,GAAG,IAAIqF,MAAM,EAAE;MACtB,IAA0BA,MAAO,CAACrF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC5C,OAA6BqF,MAAO,CAACrF,GAAG,CAAC;;;IAIjD;IACA,MAAM8F,QAAQ,GAA2B,EAAG;IAE5C;IACA,IAAIT,MAAM,CAACzF,iBAAiB,IAAI,CAAC5B,WAAW,CAACqH,MAAM,CAACzF,iBAAiB,EAAE,EAAE,CAAC,EAAE;MACxEkG,QAAQ,CAACT,MAAM,CAACzF,iBAAiB,CAAC,GAAG,IAAI;;IAG7C;IACA,MAAMsD,OAAO,GAAGtB,gBAAgB,CAAC2B,IAAI,CAACzB,KAAK,CAAC;IAE5C;IACAoB,OAAO,CAACgC,KAAK,CAACjG,KAAK,EAAE,CAACyB,IAAY,EAAEzB,KAAU,KAAI;MAC9C,IAAIyB,IAAI,KAAK,SAAS,IAAI,CAAC1C,WAAW,CAACiB,KAAK,EAAE,EAAE,CAAC,EAAE;QAC/C6G,QAAQ,CAAC7G,KAAK,CAAC,GAAG,IAAI;;MAE1B,OAAOA,KAAK;IAChB,CAAC,CAAC;IAEF;IACA,KAAK,MAAMQ,IAAI,IAAIqG,QAAQ,EAAE;MACzBA,QAAQ,CAACrG,IAAI,CAAC,GAAG,MAAMmG,WAAW,CAACnG,IAAI,CAAC;;IAG5C;IACA,IAAI4F,MAAM,CAACzF,iBAAiB,IAAIkG,QAAQ,CAACT,MAAM,CAACzF,iBAAiB,CAAC,EAAE;MAChEyF,MAAM,CAACzF,iBAAiB,GAAGkG,QAAQ,CAACT,MAAM,CAACzF,iBAAiB,CAAC;;IAGjE;IACAX,KAAK,GAAGiE,OAAO,CAACgC,KAAK,CAACjG,KAAK,EAAE,CAACyB,IAAY,EAAEzB,KAAU,KAAI;MACtD,IAAIyB,IAAI,KAAK,SAAS,IAAIoF,QAAQ,CAAC7G,KAAK,CAAC,EAAE;QAAE,OAAO6G,QAAQ,CAAC7G,KAAK,CAAC;;MACnE,OAAOA,KAAK;IAChB,CAAC,CAAC;IAEF,OAAO;MAAEoG,MAAM;MAAEpG;IAAK,CAAE;EAC5B;EAEA;;;;EAIA,OAAO8G,UAAUA,CAACV,MAAuB,EAAEvD,KAA4C,EAAE7C,KAA0B;IAC/G;IACA2C,gBAAgB,CAACwD,UAAU,CAACC,MAAM,CAAC;IAEnC;IACA,MAAMW,YAAY,GAAwB,EAAG;IAC7C,MAAMC,WAAW,GAAyC,EAAG;IAE7DnG,gBAAgB,CAAC4C,OAAO,CAAEjD,IAAI,IAAI;MAC9B,MAAMR,KAAK,GAASoG,MAAO,CAAC5F,IAAI,CAAC;MACjC,IAAIR,KAAK,IAAI,IAAI,EAAE;QAAE;;MACrB+G,YAAY,CAACvG,IAAI,CAAC,GAAGU,YAAY,CAACV,IAAI,CAAC,CAACR,KAAK,CAAC;MAC9CgH,WAAW,CAAC7C,IAAI,CAAC;QAAE3D,IAAI;QAAEiB,IAAI,EAAElB,gBAAgB,CAACC,IAAI;MAAC,CAAE,CAAC;IAC5D,CAAC,CAAC;IAEF,MAAMyD,OAAO,GAAGtB,gBAAgB,CAAC2B,IAAI,CAACzB,KAAK,CAAC;IAE5C;IACAA,KAAK,GAAGoB,OAAO,CAACpB,KAAK;IAErB,MAAMoE,eAAe,GAAG1D,MAAM,CAACqD,MAAM,CAAC,EAAG,EAAE/D,KAAK,CAAC;IACjDxD,cAAc,CAAC4H,eAAe,CAACR,YAAY,IAAI,IAAI,EAAE,0CAA0C,EAAE,oBAAoB,EAAE5D,KAAK,CAAC;IAE7HoE,eAAe,CAACR,YAAY,GAAGO,WAAW;IAE1C;IACA/C,OAAO,CAACyB,MAAM,CAAC1F,KAAK,CAAC;IAErB,OAAO;MACH6C,KAAK,EAAEoE,eAAe;MACtBb,MAAM,EAAEW,YAAY;MACpBnE,WAAW,EAAEqB,OAAO,CAACrB,WAAW;MAChCsE,OAAO,EAAEjD,OAAO,CAACgC,KAAK,CAACjG,KAAK,EAAE,CAACyB,IAAY,EAAEzB,KAAU,KAAI;QAEvD;QACA,IAAIyB,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,EAAE;UAC3B,OAAO5C,OAAO,CAACD,QAAQ,CAACmB,KAAK,CAAC,CAAC;;QAGnC;QACA,IAAIyB,IAAI,CAACC,KAAK,CAAC,QAAQ,CAAC,EAAE;UACtB,OAAO9C,SAAS,CAACoB,KAAK,CAAC,CAACmH,QAAQ,EAAE;;QAGtC,QAAQ1F,IAAI;UACR,KAAK,SAAS;YACV,OAAOzB,KAAK,CAACsB,WAAW,EAAE;UAC9B,KAAK,MAAM;YACP,OAAO,CAAC,CAACtB,KAAK;UAClB,KAAK,QAAQ;YACTX,cAAc,CAAC,OAAOW,KAAM,KAAK,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAEA,KAAK,CAAC;YAC5E,OAAOA,KAAK;;QAGpBX,cAAc,CAAC,KAAK,EAAE,kBAAkB,EAAE,MAAM,EAAEoC,IAAI,CAAC;MAC3D,CAAC;KACJ;EACL;;AAGJ;;;AAGA,OAAM,SAAU2F,eAAeA,CAAChB,MAAuB,EAAEvD,KAA4C,EAAE7C,KAA0B,EAAEqH,SAAwB;EACvJ,OAAO5I,cAAc,CAACkE,gBAAgB,CAACgD,IAAI,CAACS,MAAM,EAAEvD,KAAK,EAAE7C,KAAK,CAAC,EAAEqH,SAAS,CAAC;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}