{"ast": null, "code": "import { defineProperties } from \"../utils/properties.js\";\nimport { assertArgument } from \"../utils/index.js\";\nconst EnsAddress = \"******************************************\";\n/**\n *  A **NetworkPlugin** provides additional functionality on a [[Network]].\n */\nexport class NetworkPlugin {\n  /**\n   *  The name of the plugin.\n   *\n   *  It is recommended to use reverse-domain-notation, which permits\n   *  unique names with a known authority as well as hierarchal entries.\n   */\n  name;\n  /**\n   *  Creates a new **NetworkPlugin**.\n   */\n  constructor(name) {\n    defineProperties(this, {\n      name\n    });\n  }\n  /**\n   *  Creates a copy of this plugin.\n   */\n  clone() {\n    return new NetworkPlugin(this.name);\n  }\n}\n/**\n *  A **GasCostPlugin** allows a network to provide alternative values when\n *  computing the intrinsic gas required for a transaction.\n */\nexport class GasCostPlugin extends NetworkPlugin {\n  /**\n   *  The block number to treat these values as valid from.\n   *\n   *  This allows a hardfork to have updated values included as well as\n   *  mulutiple hardforks to be supported.\n   */\n  effectiveBlock;\n  /**\n   *  The transactions base fee.\n   */\n  txBase;\n  /**\n   *  The fee for creating a new account.\n   */\n  txCreate;\n  /**\n   *  The fee per zero-byte in the data.\n   */\n  txDataZero;\n  /**\n   *  The fee per non-zero-byte in the data.\n   */\n  txDataNonzero;\n  /**\n   *  The fee per storage key in the [[link-eip-2930]] access list.\n   */\n  txAccessListStorageKey;\n  /**\n   *  The fee per address in the [[link-eip-2930]] access list.\n   */\n  txAccessListAddress;\n  /**\n   *  Creates a new GasCostPlugin from %%effectiveBlock%% until the\n   *  latest block or another GasCostPlugin supercedes that block number,\n   *  with the associated %%costs%%.\n   */\n  constructor(effectiveBlock, costs) {\n    if (effectiveBlock == null) {\n      effectiveBlock = 0;\n    }\n    super(`org.ethers.network.plugins.GasCost#${effectiveBlock || 0}`);\n    const props = {\n      effectiveBlock\n    };\n    function set(name, nullish) {\n      let value = (costs || {})[name];\n      if (value == null) {\n        value = nullish;\n      }\n      assertArgument(typeof value === \"number\", `invalud value for ${name}`, \"costs\", costs);\n      props[name] = value;\n    }\n    set(\"txBase\", 21000);\n    set(\"txCreate\", 32000);\n    set(\"txDataZero\", 4);\n    set(\"txDataNonzero\", 16);\n    set(\"txAccessListStorageKey\", 1900);\n    set(\"txAccessListAddress\", 2400);\n    defineProperties(this, props);\n  }\n  clone() {\n    return new GasCostPlugin(this.effectiveBlock, this);\n  }\n}\n/**\n *  An **EnsPlugin** allows a [[Network]] to specify the ENS Registry\n *  Contract address and the target network to use when using that\n *  contract.\n *\n *  Various testnets have their own instance of the contract to use, but\n *  in general, the mainnet instance supports multi-chain addresses and\n *  should be used.\n */\nexport class EnsPlugin extends NetworkPlugin {\n  /**\n   *  The ENS Registrty Contract address.\n   */\n  address;\n  /**\n   *  The chain ID that the ENS contract lives on.\n   */\n  targetNetwork;\n  /**\n   *  Creates a new **EnsPlugin** connected to %%address%% on the\n   *  %%targetNetwork%%. The default ENS address and mainnet is used\n   *  if unspecified.\n   */\n  constructor(address, targetNetwork) {\n    super(\"org.ethers.plugins.network.Ens\");\n    defineProperties(this, {\n      address: address || EnsAddress,\n      targetNetwork: targetNetwork == null ? 1 : targetNetwork\n    });\n  }\n  clone() {\n    return new EnsPlugin(this.address, this.targetNetwork);\n  }\n}\n/**\n *  A **FeeDataNetworkPlugin** allows a network to provide and alternate\n *  means to specify its fee data.\n *\n *  For example, a network which does not support [[link-eip-1559]] may\n *  choose to use a Gas Station site to approximate the gas price.\n */\nexport class FeeDataNetworkPlugin extends NetworkPlugin {\n  #feeDataFunc;\n  /**\n   *  The fee data function provided to the constructor.\n   */\n  get feeDataFunc() {\n    return this.#feeDataFunc;\n  }\n  /**\n   *  Creates a new **FeeDataNetworkPlugin**.\n   */\n  constructor(feeDataFunc) {\n    super(\"org.ethers.plugins.network.FeeData\");\n    this.#feeDataFunc = feeDataFunc;\n  }\n  /**\n   *  Resolves to the fee data.\n   */\n  async getFeeData(provider) {\n    return await this.#feeDataFunc(provider);\n  }\n  clone() {\n    return new FeeDataNetworkPlugin(this.#feeDataFunc);\n  }\n}\nexport class FetchUrlFeeDataNetworkPlugin extends NetworkPlugin {\n  #url;\n  #processFunc;\n  /**\n   *  The URL to initialize the FetchRequest with in %%processFunc%%.\n   */\n  get url() {\n    return this.#url;\n  }\n  /**\n   *  The callback to use when computing the FeeData.\n   */\n  get processFunc() {\n    return this.#processFunc;\n  }\n  /**\n   *  Creates a new **FetchUrlFeeDataNetworkPlugin** which will\n   *  be used when computing the fee data for the network.\n   */\n  constructor(url, processFunc) {\n    super(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\");\n    this.#url = url;\n    this.#processFunc = processFunc;\n  }\n  // We are immutable, so we can serve as our own clone\n  clone() {\n    return this;\n  }\n}\n/*\nexport class CustomBlockNetworkPlugin extends NetworkPlugin {\n    readonly #blockFunc: (provider: Provider, block: BlockParams<string>) => Block<string>;\n    readonly #blockWithTxsFunc: (provider: Provider, block: BlockParams<TransactionResponseParams>) => Block<TransactionResponse>;\n\n    constructor(blockFunc: (provider: Provider, block: BlockParams<string>) => Block<string>, blockWithTxsFunc: (provider: Provider, block: BlockParams<TransactionResponseParams>) => Block<TransactionResponse>) {\n        super(\"org.ethers.network-plugins.custom-block\");\n        this.#blockFunc = blockFunc;\n        this.#blockWithTxsFunc = blockWithTxsFunc;\n    }\n\n    async getBlock(provider: Provider, block: BlockParams<string>): Promise<Block<string>> {\n        return await this.#blockFunc(provider, block);\n    }\n\n    async getBlockions(provider: Provider, block: BlockParams<TransactionResponseParams>): Promise<Block<TransactionResponse>> {\n        return await this.#blockWithTxsFunc(provider, block);\n    }\n\n    clone(): CustomBlockNetworkPlugin {\n        return new CustomBlockNetworkPlugin(this.#blockFunc, this.#blockWithTxsFunc);\n    }\n}\n*/", "map": {"version": 3, "names": ["defineProperties", "assertArgument", "EnsAddress", "NetworkPlugin", "name", "constructor", "clone", "GasCostPlugin", "effectiveBlock", "txBase", "txCreate", "txDataZero", "txDataNonzero", "txAccessListStorageKey", "txAccessListAddress", "costs", "props", "set", "nullish", "value", "EnsPlugin", "address", "targetNetwork", "FeeDataNetworkPlugin", "feeDataFunc", "getFeeData", "provider", "FetchUrlFeeDataNetworkPlugin", "url", "processFunc"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\plugins-network.ts"], "sourcesContent": ["import { defineProperties } from \"../utils/properties.js\";\n\nimport { assertArgument } from \"../utils/index.js\";\n\nimport type { FeeData, Provider } from \"./provider.js\";\nimport type { FetchRequest } from \"../utils/fetch.js\";\n\n\nconst EnsAddress = \"******************************************\";\n\n/**\n *  A **NetworkPlugin** provides additional functionality on a [[Network]].\n */\nexport class NetworkPlugin {\n    /**\n     *  The name of the plugin.\n     *\n     *  It is recommended to use reverse-domain-notation, which permits\n     *  unique names with a known authority as well as hierarchal entries.\n     */\n    readonly name!: string;\n\n    /**\n     *  Creates a new **NetworkPlugin**.\n     */\n    constructor(name: string) {\n        defineProperties<NetworkPlugin>(this, { name });\n    }\n\n    /**\n     *  Creates a copy of this plugin.\n     */\n    clone(): NetworkPlugin {\n        return new NetworkPlugin(this.name);\n    }\n\n//    validate(network: Network): NetworkPlugin {\n//        return this;\n//    }\n}\n\n\n/**\n *  The gas cost parameters for a [[GasCostPlugin]].\n */\nexport type GasCostParameters = {\n    /**\n     *  The transactions base fee.\n     */\n    txBase?: number;\n\n    /**\n     *  The fee for creating a new account.\n     */\n    txCreate?: number;\n\n    /**\n     *  The fee per zero-byte in the data.\n     */\n    txDataZero?: number;\n\n    /**\n     *  The fee per non-zero-byte in the data.\n     */\n    txDataNonzero?: number;\n\n    /**\n     *  The fee per storage key in the [[link-eip-2930]] access list.\n     */\n    txAccessListStorageKey?: number;\n\n    /**\n     *  The fee per address in the [[link-eip-2930]] access list.\n     */\n    txAccessListAddress?: number;\n};\n\n/**\n *  A **GasCostPlugin** allows a network to provide alternative values when\n *  computing the intrinsic gas required for a transaction.\n */\nexport class GasCostPlugin extends NetworkPlugin implements GasCostParameters {\n    /**\n     *  The block number to treat these values as valid from.\n     *\n     *  This allows a hardfork to have updated values included as well as\n     *  mulutiple hardforks to be supported.\n     */\n    readonly effectiveBlock!: number;\n\n    /**\n     *  The transactions base fee.\n     */\n    readonly txBase!: number;\n\n    /**\n     *  The fee for creating a new account.\n     */\n    readonly txCreate!: number;\n\n    /**\n     *  The fee per zero-byte in the data.\n     */\n    readonly txDataZero!: number;\n\n    /**\n     *  The fee per non-zero-byte in the data.\n     */\n    readonly txDataNonzero!: number;\n\n    /**\n     *  The fee per storage key in the [[link-eip-2930]] access list.\n     */\n    readonly txAccessListStorageKey!: number;\n\n    /**\n     *  The fee per address in the [[link-eip-2930]] access list.\n     */\n    readonly txAccessListAddress!: number;\n\n\n    /**\n     *  Creates a new GasCostPlugin from %%effectiveBlock%% until the\n     *  latest block or another GasCostPlugin supercedes that block number,\n     *  with the associated %%costs%%.\n     */\n    constructor(effectiveBlock?: number, costs?: GasCostParameters) {\n        if (effectiveBlock == null) { effectiveBlock = 0; }\n        super(`org.ethers.network.plugins.GasCost#${ (effectiveBlock || 0) }`);\n\n        const props: Record<string, number> = { effectiveBlock };\n        function set(name: keyof GasCostParameters, nullish: number): void {\n            let value = (costs || { })[name];\n            if (value == null) { value = nullish; }\n            assertArgument(typeof(value) === \"number\", `invalud value for ${ name }`, \"costs\", costs);\n            props[name] = value;\n        }\n\n        set(\"txBase\", 21000);\n        set(\"txCreate\", 32000);\n        set(\"txDataZero\", 4);\n        set(\"txDataNonzero\", 16);\n        set(\"txAccessListStorageKey\", 1900);\n        set(\"txAccessListAddress\", 2400);\n\n        defineProperties<GasCostPlugin>(this, props);\n    }\n\n    clone(): GasCostPlugin {\n        return new GasCostPlugin(this.effectiveBlock, this);\n    }\n}\n\n/**\n *  An **EnsPlugin** allows a [[Network]] to specify the ENS Registry\n *  Contract address and the target network to use when using that\n *  contract.\n *\n *  Various testnets have their own instance of the contract to use, but\n *  in general, the mainnet instance supports multi-chain addresses and\n *  should be used.\n */\nexport class EnsPlugin extends NetworkPlugin {\n\n    /**\n     *  The ENS Registrty Contract address.\n     */\n    readonly address!: string;\n\n    /**\n     *  The chain ID that the ENS contract lives on.\n     */\n    readonly targetNetwork!: number;\n\n    /**\n     *  Creates a new **EnsPlugin** connected to %%address%% on the\n     *  %%targetNetwork%%. The default ENS address and mainnet is used\n     *  if unspecified.\n     */\n    constructor(address?: null | string, targetNetwork?: null | number) {\n        super(\"org.ethers.plugins.network.Ens\");\n        defineProperties<EnsPlugin>(this, {\n            address: (address || EnsAddress),\n            targetNetwork: ((targetNetwork == null) ? 1: targetNetwork)\n        });\n    }\n\n    clone(): EnsPlugin {\n        return new EnsPlugin(this.address, this.targetNetwork);\n    }\n}\n\n/**\n *  A **FeeDataNetworkPlugin** allows a network to provide and alternate\n *  means to specify its fee data.\n *\n *  For example, a network which does not support [[link-eip-1559]] may\n *  choose to use a Gas Station site to approximate the gas price.\n */\nexport class FeeDataNetworkPlugin extends NetworkPlugin {\n    readonly #feeDataFunc: (provider: Provider) => Promise<FeeData>;\n\n    /**\n     *  The fee data function provided to the constructor.\n     */\n    get feeDataFunc(): (provider: Provider) => Promise<FeeData> {\n        return this.#feeDataFunc;\n    }\n\n    /**\n     *  Creates a new **FeeDataNetworkPlugin**.\n     */\n    constructor(feeDataFunc: (provider: Provider) => Promise<FeeData>) {\n        super(\"org.ethers.plugins.network.FeeData\");\n        this.#feeDataFunc = feeDataFunc;\n    }\n\n    /**\n     *  Resolves to the fee data.\n     */\n    async getFeeData(provider: Provider): Promise<FeeData> {\n        return await this.#feeDataFunc(provider);\n    }\n\n    clone(): FeeDataNetworkPlugin {\n        return new FeeDataNetworkPlugin(this.#feeDataFunc);\n    }\n}\n\nexport class FetchUrlFeeDataNetworkPlugin extends NetworkPlugin {\n    readonly #url: string;\n    readonly #processFunc: (f: () => Promise<FeeData>, p: Provider, r: FetchRequest) => Promise<{ gasPrice?: null | bigint, maxFeePerGas?: null | bigint, maxPriorityFeePerGas?: null | bigint }>;\n\n    /**\n     *  The URL to initialize the FetchRequest with in %%processFunc%%.\n     */\n    get url(): string { return this.#url; }\n\n    /**\n     *  The callback to use when computing the FeeData.\n     */\n    get processFunc(): (f: () => Promise<FeeData>, p: Provider, r: FetchRequest) => Promise<{ gasPrice?: null | bigint, maxFeePerGas?: null | bigint, maxPriorityFeePerGas?: null | bigint }> { return this.#processFunc; }\n\n    /**\n     *  Creates a new **FetchUrlFeeDataNetworkPlugin** which will\n     *  be used when computing the fee data for the network.\n     */\n    constructor(url: string, processFunc: (f: () => Promise<FeeData>, p: Provider, r: FetchRequest) => Promise<{ gasPrice?: null | bigint, maxFeePerGas?: null | bigint, maxPriorityFeePerGas?: null | bigint }>) {\n        super(\"org.ethers.plugins.network.FetchUrlFeeDataPlugin\");\n        this.#url = url;\n        this.#processFunc = processFunc;\n    }\n\n    // We are immutable, so we can serve as our own clone\n    clone(): FetchUrlFeeDataNetworkPlugin { return this; }\n}\n\n/*\nexport class CustomBlockNetworkPlugin extends NetworkPlugin {\n    readonly #blockFunc: (provider: Provider, block: BlockParams<string>) => Block<string>;\n    readonly #blockWithTxsFunc: (provider: Provider, block: BlockParams<TransactionResponseParams>) => Block<TransactionResponse>;\n\n    constructor(blockFunc: (provider: Provider, block: BlockParams<string>) => Block<string>, blockWithTxsFunc: (provider: Provider, block: BlockParams<TransactionResponseParams>) => Block<TransactionResponse>) {\n        super(\"org.ethers.network-plugins.custom-block\");\n        this.#blockFunc = blockFunc;\n        this.#blockWithTxsFunc = blockWithTxsFunc;\n    }\n\n    async getBlock(provider: Provider, block: BlockParams<string>): Promise<Block<string>> {\n        return await this.#blockFunc(provider, block);\n    }\n\n    async getBlockions(provider: Provider, block: BlockParams<TransactionResponseParams>): Promise<Block<TransactionResponse>> {\n        return await this.#blockWithTxsFunc(provider, block);\n    }\n\n    clone(): CustomBlockNetworkPlugin {\n        return new CustomBlockNetworkPlugin(this.#blockFunc, this.#blockWithTxsFunc);\n    }\n}\n*/\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,wBAAwB;AAEzD,SAASC,cAAc,QAAQ,mBAAmB;AAMlD,MAAMC,UAAU,GAAG,4CAA4C;AAE/D;;;AAGA,OAAM,MAAOC,aAAa;EACtB;;;;;;EAMSC,IAAI;EAEb;;;EAGAC,YAAYD,IAAY;IACpBJ,gBAAgB,CAAgB,IAAI,EAAE;MAAEI;IAAI,CAAE,CAAC;EACnD;EAEA;;;EAGAE,KAAKA,CAAA;IACD,OAAO,IAAIH,aAAa,CAAC,IAAI,CAACC,IAAI,CAAC;EACvC;;AA2CJ;;;;AAIA,OAAM,MAAOG,aAAc,SAAQJ,aAAa;EAC5C;;;;;;EAMSK,cAAc;EAEvB;;;EAGSC,MAAM;EAEf;;;EAGSC,QAAQ;EAEjB;;;EAGSC,UAAU;EAEnB;;;EAGSC,aAAa;EAEtB;;;EAGSC,sBAAsB;EAE/B;;;EAGSC,mBAAmB;EAG5B;;;;;EAKAT,YAAYG,cAAuB,EAAEO,KAAyB;IAC1D,IAAIP,cAAc,IAAI,IAAI,EAAE;MAAEA,cAAc,GAAG,CAAC;;IAChD,KAAK,CAAC,sCAAwCA,cAAc,IAAI,CAAC,EAAI,CAAC;IAEtE,MAAMQ,KAAK,GAA2B;MAAER;IAAc,CAAE;IACxD,SAASS,GAAGA,CAACb,IAA6B,EAAEc,OAAe;MACvD,IAAIC,KAAK,GAAG,CAACJ,KAAK,IAAI,EAAG,EAAEX,IAAI,CAAC;MAChC,IAAIe,KAAK,IAAI,IAAI,EAAE;QAAEA,KAAK,GAAGD,OAAO;;MACpCjB,cAAc,CAAC,OAAOkB,KAAM,KAAK,QAAQ,EAAE,qBAAsBf,IAAK,EAAE,EAAE,OAAO,EAAEW,KAAK,CAAC;MACzFC,KAAK,CAACZ,IAAI,CAAC,GAAGe,KAAK;IACvB;IAEAF,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;IACpBA,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC;IACtBA,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC;IACpBA,GAAG,CAAC,eAAe,EAAE,EAAE,CAAC;IACxBA,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC;IACnCA,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC;IAEhCjB,gBAAgB,CAAgB,IAAI,EAAEgB,KAAK,CAAC;EAChD;EAEAV,KAAKA,CAAA;IACD,OAAO,IAAIC,aAAa,CAAC,IAAI,CAACC,cAAc,EAAE,IAAI,CAAC;EACvD;;AAGJ;;;;;;;;;AASA,OAAM,MAAOY,SAAU,SAAQjB,aAAa;EAExC;;;EAGSkB,OAAO;EAEhB;;;EAGSC,aAAa;EAEtB;;;;;EAKAjB,YAAYgB,OAAuB,EAAEC,aAA6B;IAC9D,KAAK,CAAC,gCAAgC,CAAC;IACvCtB,gBAAgB,CAAY,IAAI,EAAE;MAC9BqB,OAAO,EAAGA,OAAO,IAAInB,UAAW;MAChCoB,aAAa,EAAIA,aAAa,IAAI,IAAI,GAAI,CAAC,GAAEA;KAChD,CAAC;EACN;EAEAhB,KAAKA,CAAA;IACD,OAAO,IAAIc,SAAS,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,aAAa,CAAC;EAC1D;;AAGJ;;;;;;;AAOA,OAAM,MAAOC,oBAAqB,SAAQpB,aAAa;EAC1C,CAAAqB,WAAY;EAErB;;;EAGA,IAAIA,WAAWA,CAAA;IACX,OAAO,IAAI,CAAC,CAAAA,WAAY;EAC5B;EAEA;;;EAGAnB,YAAYmB,WAAqD;IAC7D,KAAK,CAAC,oCAAoC,CAAC;IAC3C,IAAI,CAAC,CAAAA,WAAY,GAAGA,WAAW;EACnC;EAEA;;;EAGA,MAAMC,UAAUA,CAACC,QAAkB;IAC/B,OAAO,MAAM,IAAI,CAAC,CAAAF,WAAY,CAACE,QAAQ,CAAC;EAC5C;EAEApB,KAAKA,CAAA;IACD,OAAO,IAAIiB,oBAAoB,CAAC,IAAI,CAAC,CAAAC,WAAY,CAAC;EACtD;;AAGJ,OAAM,MAAOG,4BAA6B,SAAQxB,aAAa;EAClD,CAAAyB,GAAI;EACJ,CAAAC,WAAY;EAErB;;;EAGA,IAAID,GAAGA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,GAAI;EAAE;EAEtC;;;EAGA,IAAIC,WAAWA,CAAA;IAA6K,OAAO,IAAI,CAAC,CAAAA,WAAY;EAAE;EAEtN;;;;EAIAxB,YAAYuB,GAAW,EAAEC,WAAmL;IACxM,KAAK,CAAC,kDAAkD,CAAC;IACzD,IAAI,CAAC,CAAAD,GAAI,GAAGA,GAAG;IACf,IAAI,CAAC,CAAAC,WAAY,GAAGA,WAAW;EACnC;EAEA;EACAvB,KAAKA,CAAA;IAAmC,OAAO,IAAI;EAAE;;AAGzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}