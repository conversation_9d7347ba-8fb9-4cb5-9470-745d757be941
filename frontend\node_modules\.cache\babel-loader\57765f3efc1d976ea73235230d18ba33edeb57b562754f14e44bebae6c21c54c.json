{"ast": null, "code": "//See: https://github.com/ethereum/wiki/wiki/RLP\nimport { getBytes } from \"./data.js\";\nfunction arrayifyInteger(value) {\n  const result = [];\n  while (value) {\n    result.unshift(value & 0xff);\n    value >>= 8;\n  }\n  return result;\n}\nfunction _encode(object) {\n  if (Array.isArray(object)) {\n    let payload = [];\n    object.forEach(function (child) {\n      payload = payload.concat(_encode(child));\n    });\n    if (payload.length <= 55) {\n      payload.unshift(0xc0 + payload.length);\n      return payload;\n    }\n    const length = arrayifyInteger(payload.length);\n    length.unshift(0xf7 + length.length);\n    return length.concat(payload);\n  }\n  const data = Array.prototype.slice.call(getBytes(object, \"object\"));\n  if (data.length === 1 && data[0] <= 0x7f) {\n    return data;\n  } else if (data.length <= 55) {\n    data.unshift(0x80 + data.length);\n    return data;\n  }\n  const length = arrayifyInteger(data.length);\n  length.unshift(0xb7 + length.length);\n  return length.concat(data);\n}\nconst nibbles = \"0123456789abcdef\";\n/**\n *  Encodes %%object%% as an RLP-encoded [[DataHexString]].\n */\nexport function encodeRlp(object) {\n  let result = \"0x\";\n  for (const v of _encode(object)) {\n    result += nibbles[v >> 4];\n    result += nibbles[v & 0xf];\n  }\n  return result;\n}", "map": {"version": 3, "names": ["getBytes", "arrayifyInteger", "value", "result", "unshift", "_encode", "object", "Array", "isArray", "payload", "for<PERSON>ach", "child", "concat", "length", "data", "prototype", "slice", "call", "nibbles", "encodeRlp", "v"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\rlp-encode.ts"], "sourcesContent": ["//See: https://github.com/ethereum/wiki/wiki/RLP\n\nimport { getBytes } from \"./data.js\";\n\nimport type { RlpStructuredDataish } from \"./rlp.js\";\n\n\nfunction arrayifyInteger(value: number): Array<number> {\n    const result: Array<number> = [];\n    while (value) {\n        result.unshift(value & 0xff);\n        value >>= 8;\n    }\n    return result;\n}\n\nfunction _encode(object: Array<any> | string | Uint8Array): Array<number> {\n    if (Array.isArray(object)) {\n        let payload: Array<number> = [];\n        object.forEach(function(child) {\n            payload = payload.concat(_encode(child));\n        });\n\n        if (payload.length <= 55) {\n            payload.unshift(0xc0 + payload.length)\n            return payload;\n        }\n\n        const length = arrayifyInteger(payload.length);\n        length.unshift(0xf7 + length.length);\n\n        return length.concat(payload);\n\n    }\n\n    const data: Array<number> = Array.prototype.slice.call(getBytes(object, \"object\"));\n\n    if (data.length === 1 && data[0] <= 0x7f) {\n        return data;\n\n    } else if (data.length <= 55) {\n        data.unshift(0x80 + data.length);\n        return data;\n    }\n\n    const length = arrayifyInteger(data.length);\n    length.unshift(0xb7 + length.length);\n\n    return length.concat(data);\n}\n\nconst nibbles = \"0123456789abcdef\";\n\n/**\n *  Encodes %%object%% as an RLP-encoded [[DataHexString]].\n */\nexport function encodeRlp(object: RlpStructuredDataish): string {\n    let result = \"0x\";\n    for (const v of _encode(object)) {\n        result += nibbles[v >> 4];\n        result += nibbles[v & 0xf];\n    }\n    return result;\n}\n"], "mappings": "AAAA;AAEA,SAASA,QAAQ,QAAQ,WAAW;AAKpC,SAASC,eAAeA,CAACC,KAAa;EAClC,MAAMC,MAAM,GAAkB,EAAE;EAChC,OAAOD,KAAK,EAAE;IACVC,MAAM,CAACC,OAAO,CAACF,KAAK,GAAG,IAAI,CAAC;IAC5BA,KAAK,KAAK,CAAC;;EAEf,OAAOC,MAAM;AACjB;AAEA,SAASE,OAAOA,CAACC,MAAwC;EACrD,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACvB,IAAIG,OAAO,GAAkB,EAAE;IAC/BH,MAAM,CAACI,OAAO,CAAC,UAASC,KAAK;MACzBF,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACP,OAAO,CAACM,KAAK,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAIF,OAAO,CAACI,MAAM,IAAI,EAAE,EAAE;MACtBJ,OAAO,CAACL,OAAO,CAAC,IAAI,GAAGK,OAAO,CAACI,MAAM,CAAC;MACtC,OAAOJ,OAAO;;IAGlB,MAAMI,MAAM,GAAGZ,eAAe,CAACQ,OAAO,CAACI,MAAM,CAAC;IAC9CA,MAAM,CAACT,OAAO,CAAC,IAAI,GAAGS,MAAM,CAACA,MAAM,CAAC;IAEpC,OAAOA,MAAM,CAACD,MAAM,CAACH,OAAO,CAAC;;EAIjC,MAAMK,IAAI,GAAkBP,KAAK,CAACQ,SAAS,CAACC,KAAK,CAACC,IAAI,CAACjB,QAAQ,CAACM,MAAM,EAAE,QAAQ,CAAC,CAAC;EAElF,IAAIQ,IAAI,CAACD,MAAM,KAAK,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACtC,OAAOA,IAAI;GAEd,MAAM,IAAIA,IAAI,CAACD,MAAM,IAAI,EAAE,EAAE;IAC1BC,IAAI,CAACV,OAAO,CAAC,IAAI,GAAGU,IAAI,CAACD,MAAM,CAAC;IAChC,OAAOC,IAAI;;EAGf,MAAMD,MAAM,GAAGZ,eAAe,CAACa,IAAI,CAACD,MAAM,CAAC;EAC3CA,MAAM,CAACT,OAAO,CAAC,IAAI,GAAGS,MAAM,CAACA,MAAM,CAAC;EAEpC,OAAOA,MAAM,CAACD,MAAM,CAACE,IAAI,CAAC;AAC9B;AAEA,MAAMI,OAAO,GAAG,kBAAkB;AAElC;;;AAGA,OAAM,SAAUC,SAASA,CAACb,MAA4B;EAClD,IAAIH,MAAM,GAAG,IAAI;EACjB,KAAK,MAAMiB,CAAC,IAAIf,OAAO,CAACC,MAAM,CAAC,EAAE;IAC7BH,MAAM,IAAIe,OAAO,CAACE,CAAC,IAAI,CAAC,CAAC;IACzBjB,MAAM,IAAIe,OAAO,CAACE,CAAC,GAAG,GAAG,CAAC;;EAE9B,OAAOjB,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}