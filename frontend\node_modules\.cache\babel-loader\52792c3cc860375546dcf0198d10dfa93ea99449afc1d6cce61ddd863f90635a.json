{"ast": null, "code": "/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\nconst u8a = a => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = arr => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = arr => new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n// Cast array to view\nexport const createView = arr => new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word, shift) => word << 32 - shift | word >>> shift;\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE) throw new Error('Non little-endian hardware is not supported');\nconst hexes = /* @__PURE__ */Array.from({\n  length: 256\n}, (_, i) => i.toString(16).padStart(2, '0'));\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes) {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex) {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const len = hex.length;\n  if (len % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n  const array = new Uint8Array(len / 2);\n  for (let i = 0; i < array.length; i++) {\n    const j = i * 2;\n    const hexByte = hex.slice(j, j + 2);\n    const byte = Number.parseInt(hexByte, 16);\n    if (Number.isNaN(byte) || byte < 0) throw new Error('Invalid byte sequence');\n    array[i] = byte;\n  }\n  return array;\n}\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => {};\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters, tick, cb) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str) {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data) {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  if (!u8a(data)) throw new Error(`expected Uint8Array, got ${typeof data}`);\n  return data;\n}\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays) {\n  const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n  let pad = 0; // walk through each item, ensure they have proper type\n  arrays.forEach(a => {\n    if (!u8a(a)) throw new Error('Uint8Array expected');\n    r.set(a, pad);\n    pad += a.length;\n  });\n  return r;\n}\n// For runtime check if class implements interface\nexport class Hash {\n  // Safe version that clones internal state\n  clone() {\n    return this._cloneInto();\n  }\n}\nconst toStr = {}.toString;\nexport function checkOpts(defaults, opts) {\n  if (opts !== undefined && toStr.call(opts) !== '[object Object]') throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged;\n}\nexport function wrapConstructor(hashCons) {\n  const hashC = msg => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\nexport function wrapConstructorWithOpts(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\nexport function wrapXOFConstructorWithOpts(hashCons) {\n  const hashC = (msg, opts) => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({});\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = opts => hashCons(opts);\n  return hashC;\n}\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32) {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}", "map": {"version": 3, "names": ["crypto", "u8a", "a", "Uint8Array", "u8", "arr", "buffer", "byteOffset", "byteLength", "u32", "Uint32Array", "Math", "floor", "createView", "DataView", "rotr", "word", "shift", "isLE", "Error", "hexes", "Array", "from", "length", "_", "i", "toString", "padStart", "bytesToHex", "bytes", "hex", "hexToBytes", "len", "array", "j", "hexByte", "slice", "byte", "Number", "parseInt", "isNaN", "nextTick", "asyncLoop", "iters", "tick", "cb", "ts", "Date", "now", "diff", "utf8ToBytes", "str", "TextEncoder", "encode", "toBytes", "data", "concatBytes", "arrays", "r", "reduce", "sum", "pad", "for<PERSON>ach", "set", "Hash", "clone", "_cloneInto", "toStr", "checkOpts", "defaults", "opts", "undefined", "call", "merged", "Object", "assign", "wrapConstructor", "hashCons", "hashC", "msg", "update", "digest", "tmp", "outputLen", "blockLen", "create", "wrapConstructorWithOpts", "wrapXOFConstructorWithOpts", "randomBytes", "bytesLength", "getRandomValues"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\utils.ts"], "sourcesContent": ["/*! noble-hashes - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n\n// We use WebCrypto aka globalThis.crypto, which exists in browsers and node.js 16+.\n// node.js versions earlier than v19 don't declare it in global scope.\n// For node.js, package.json#exports field mapping rewrites import\n// from `crypto` to `cryptoNode`, which imports native module.\n// Makes the utils un-importable in browsers without a bundler.\n// Once node.js 18 is deprecated, we can just drop the import.\nimport { crypto } from '@noble/hashes/crypto';\n\n// prettier-ignore\nexport type TypedArray = Int8Array | Uint8ClampedArray | Uint8Array |\n  Uint16Array | Int16Array | Uint32Array | Int32Array;\n\nconst u8a = (a: any): a is Uint8Array => a instanceof Uint8Array;\n// Cast array to different type\nexport const u8 = (arr: TypedArray) => new Uint8Array(arr.buffer, arr.byteOffset, arr.byteLength);\nexport const u32 = (arr: TypedArray) =>\n  new Uint32Array(arr.buffer, arr.byteOffset, Math.floor(arr.byteLength / 4));\n\n// Cast array to view\nexport const createView = (arr: TypedArray) =>\n  new DataView(arr.buffer, arr.byteOffset, arr.byteLength);\n\n// The rotate right (circular right shift) operation for uint32\nexport const rotr = (word: number, shift: number) => (word << (32 - shift)) | (word >>> shift);\n\n// big-endian hardware is rare. Just in case someone still decides to run hashes:\n// early-throw an error because we don't support BE yet.\nexport const isLE = new Uint8Array(new Uint32Array([0x11223344]).buffer)[0] === 0x44;\nif (!isLE) throw new Error('Non little-endian hardware is not supported');\n\nconst hexes = /* @__PURE__ */ Array.from({ length: 256 }, (_, i) =>\n  i.toString(16).padStart(2, '0')\n);\n/**\n * @example bytesToHex(Uint8Array.from([0xca, 0xfe, 0x01, 0x23])) // 'cafe0123'\n */\nexport function bytesToHex(bytes: Uint8Array): string {\n  if (!u8a(bytes)) throw new Error('Uint8Array expected');\n  // pre-caching improves the speed 6x\n  let hex = '';\n  for (let i = 0; i < bytes.length; i++) {\n    hex += hexes[bytes[i]];\n  }\n  return hex;\n}\n\n/**\n * @example hexToBytes('cafe0123') // Uint8Array.from([0xca, 0xfe, 0x01, 0x23])\n */\nexport function hexToBytes(hex: string): Uint8Array {\n  if (typeof hex !== 'string') throw new Error('hex string expected, got ' + typeof hex);\n  const len = hex.length;\n  if (len % 2) throw new Error('padded hex string expected, got unpadded hex of length ' + len);\n  const array = new Uint8Array(len / 2);\n  for (let i = 0; i < array.length; i++) {\n    const j = i * 2;\n    const hexByte = hex.slice(j, j + 2);\n    const byte = Number.parseInt(hexByte, 16);\n    if (Number.isNaN(byte) || byte < 0) throw new Error('Invalid byte sequence');\n    array[i] = byte;\n  }\n  return array;\n}\n\n// There is no setImmediate in browser and setTimeout is slow.\n// call of async fn will return Promise, which will be fullfiled only on\n// next scheduler queue processing step and this is exactly what we need.\nexport const nextTick = async () => {};\n\n// Returns control to thread each 'tick' ms to avoid blocking\nexport async function asyncLoop(iters: number, tick: number, cb: (i: number) => void) {\n  let ts = Date.now();\n  for (let i = 0; i < iters; i++) {\n    cb(i);\n    // Date.now() is not monotonic, so in case if clock goes backwards we return return control too\n    const diff = Date.now() - ts;\n    if (diff >= 0 && diff < tick) continue;\n    await nextTick();\n    ts += diff;\n  }\n}\n\n// Global symbols in both browsers and Node.js since v11\n// See https://github.com/microsoft/TypeScript/issues/31535\ndeclare const TextEncoder: any;\n\n/**\n * @example utf8ToBytes('abc') // new Uint8Array([97, 98, 99])\n */\nexport function utf8ToBytes(str: string): Uint8Array {\n  if (typeof str !== 'string') throw new Error(`utf8ToBytes expected string, got ${typeof str}`);\n  return new Uint8Array(new TextEncoder().encode(str)); // https://bugzil.la/1681809\n}\n\nexport type Input = Uint8Array | string;\n/**\n * Normalizes (non-hex) string or Uint8Array to Uint8Array.\n * Warning: when Uint8Array is passed, it would NOT get copied.\n * Keep in mind for future mutable operations.\n */\nexport function toBytes(data: Input): Uint8Array {\n  if (typeof data === 'string') data = utf8ToBytes(data);\n  if (!u8a(data)) throw new Error(`expected Uint8Array, got ${typeof data}`);\n  return data;\n}\n\n/**\n * Copies several Uint8Arrays into one.\n */\nexport function concatBytes(...arrays: Uint8Array[]): Uint8Array {\n  const r = new Uint8Array(arrays.reduce((sum, a) => sum + a.length, 0));\n  let pad = 0; // walk through each item, ensure they have proper type\n  arrays.forEach((a) => {\n    if (!u8a(a)) throw new Error('Uint8Array expected');\n    r.set(a, pad);\n    pad += a.length;\n  });\n  return r;\n}\n\n// For runtime check if class implements interface\nexport abstract class Hash<T extends Hash<T>> {\n  abstract blockLen: number; // Bytes per block\n  abstract outputLen: number; // Bytes in output\n  abstract update(buf: Input): this;\n  // Writes digest into buf\n  abstract digestInto(buf: Uint8Array): void;\n  abstract digest(): Uint8Array;\n  /**\n   * Resets internal state. Makes Hash instance unusable.\n   * Reset is impossible for keyed hashes if key is consumed into state. If digest is not consumed\n   * by user, they will need to manually call `destroy()` when zeroing is necessary.\n   */\n  abstract destroy(): void;\n  /**\n   * Clones hash instance. Unsafe: doesn't check whether `to` is valid. Can be used as `clone()`\n   * when no options are passed.\n   * Reasons to use `_cloneInto` instead of clone: 1) performance 2) reuse instance => all internal\n   * buffers are overwritten => causes buffer overwrite which is used for digest in some cases.\n   * There are no guarantees for clean-up because it's impossible in JS.\n   */\n  abstract _cloneInto(to?: T): T;\n  // Safe version that clones internal state\n  clone(): T {\n    return this._cloneInto();\n  }\n}\n\n/**\n * XOF: streaming API to read digest in chunks.\n * Same as 'squeeze' in keccak/k12 and 'seek' in blake3, but more generic name.\n * When hash used in XOF mode it is up to user to call '.destroy' afterwards, since we cannot\n * destroy state, next call can require more bytes.\n */\nexport type HashXOF<T extends Hash<T>> = Hash<T> & {\n  xof(bytes: number): Uint8Array; // Read 'bytes' bytes from digest stream\n  xofInto(buf: Uint8Array): Uint8Array; // read buf.length bytes from digest stream into buf\n};\n\nconst toStr = {}.toString;\ntype EmptyObj = {};\nexport function checkOpts<T1 extends EmptyObj, T2 extends EmptyObj>(\n  defaults: T1,\n  opts?: T2\n): T1 & T2 {\n  if (opts !== undefined && toStr.call(opts) !== '[object Object]')\n    throw new Error('Options should be object or undefined');\n  const merged = Object.assign(defaults, opts);\n  return merged as T1 & T2;\n}\n\nexport type CHash = ReturnType<typeof wrapConstructor>;\n\nexport function wrapConstructor<T extends Hash<T>>(hashCons: () => Hash<T>) {\n  const hashC = (msg: Input): Uint8Array => hashCons().update(toBytes(msg)).digest();\n  const tmp = hashCons();\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = () => hashCons();\n  return hashC;\n}\n\nexport function wrapConstructorWithOpts<H extends Hash<H>, T extends Object>(\n  hashCons: (opts?: T) => Hash<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\nexport function wrapXOFConstructorWithOpts<H extends HashXOF<H>, T extends Object>(\n  hashCons: (opts?: T) => HashXOF<H>\n) {\n  const hashC = (msg: Input, opts?: T): Uint8Array => hashCons(opts).update(toBytes(msg)).digest();\n  const tmp = hashCons({} as T);\n  hashC.outputLen = tmp.outputLen;\n  hashC.blockLen = tmp.blockLen;\n  hashC.create = (opts: T) => hashCons(opts);\n  return hashC;\n}\n\n/**\n * Secure PRNG. Uses `crypto.getRandomValues`, which defers to OS.\n */\nexport function randomBytes(bytesLength = 32): Uint8Array {\n  if (crypto && typeof crypto.getRandomValues === 'function') {\n    return crypto.getRandomValues(new Uint8Array(bytesLength));\n  }\n  throw new Error('crypto.getRandomValues must be defined');\n}\n"], "mappings": "AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAM,QAAQ,sBAAsB;AAM7C,MAAMC,GAAG,GAAIC,CAAM,IAAsBA,CAAC,YAAYC,UAAU;AAChE;AACA,OAAO,MAAMC,EAAE,GAAIC,GAAe,IAAK,IAAIF,UAAU,CAACE,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEF,GAAG,CAACG,UAAU,CAAC;AACjG,OAAO,MAAMC,GAAG,GAAIJ,GAAe,IACjC,IAAIK,WAAW,CAACL,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEI,IAAI,CAACC,KAAK,CAACP,GAAG,CAACG,UAAU,GAAG,CAAC,CAAC,CAAC;AAE7E;AACA,OAAO,MAAMK,UAAU,GAAIR,GAAe,IACxC,IAAIS,QAAQ,CAACT,GAAG,CAACC,MAAM,EAAED,GAAG,CAACE,UAAU,EAAEF,GAAG,CAACG,UAAU,CAAC;AAE1D;AACA,OAAO,MAAMO,IAAI,GAAGA,CAACC,IAAY,EAAEC,KAAa,KAAMD,IAAI,IAAK,EAAE,GAAGC,KAAM,GAAKD,IAAI,KAAKC,KAAM;AAE9F;AACA;AACA,OAAO,MAAMC,IAAI,GAAG,IAAIf,UAAU,CAAC,IAAIO,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI;AACpF,IAAI,CAACY,IAAI,EAAE,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;AAEzE,MAAMC,KAAK,GAAG,eAAgBC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,KAC7DA,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAChC;AACD;;;AAGA,OAAM,SAAUC,UAAUA,CAACC,KAAiB;EAC1C,IAAI,CAAC5B,GAAG,CAAC4B,KAAK,CAAC,EAAE,MAAM,IAAIV,KAAK,CAAC,qBAAqB,CAAC;EACvD;EACA,IAAIW,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACN,MAAM,EAAEE,CAAC,EAAE,EAAE;IACrCK,GAAG,IAAIV,KAAK,CAACS,KAAK,CAACJ,CAAC,CAAC,CAAC;;EAExB,OAAOK,GAAG;AACZ;AAEA;;;AAGA,OAAM,SAAUC,UAAUA,CAACD,GAAW;EACpC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIX,KAAK,CAAC,2BAA2B,GAAG,OAAOW,GAAG,CAAC;EACtF,MAAME,GAAG,GAAGF,GAAG,CAACP,MAAM;EACtB,IAAIS,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIb,KAAK,CAAC,yDAAyD,GAAGa,GAAG,CAAC;EAC7F,MAAMC,KAAK,GAAG,IAAI9B,UAAU,CAAC6B,GAAG,GAAG,CAAC,CAAC;EACrC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,KAAK,CAACV,MAAM,EAAEE,CAAC,EAAE,EAAE;IACrC,MAAMS,CAAC,GAAGT,CAAC,GAAG,CAAC;IACf,MAAMU,OAAO,GAAGL,GAAG,CAACM,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;IACnC,MAAMG,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACJ,OAAO,EAAE,EAAE,CAAC;IACzC,IAAIG,MAAM,CAACE,KAAK,CAACH,IAAI,CAAC,IAAIA,IAAI,GAAG,CAAC,EAAE,MAAM,IAAIlB,KAAK,CAAC,uBAAuB,CAAC;IAC5Ec,KAAK,CAACR,CAAC,CAAC,GAAGY,IAAI;;EAEjB,OAAOJ,KAAK;AACd;AAEA;AACA;AACA;AACA,OAAO,MAAMQ,QAAQ,GAAG,MAAAA,CAAA,KAAW,CAAE,CAAC;AAEtC;AACA,OAAO,eAAeC,SAASA,CAACC,KAAa,EAAEC,IAAY,EAAEC,EAAuB;EAClF,IAAIC,EAAE,GAAGC,IAAI,CAACC,GAAG,EAAE;EACnB,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,KAAK,EAAElB,CAAC,EAAE,EAAE;IAC9BoB,EAAE,CAACpB,CAAC,CAAC;IACL;IACA,MAAMwB,IAAI,GAAGF,IAAI,CAACC,GAAG,EAAE,GAAGF,EAAE;IAC5B,IAAIG,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGL,IAAI,EAAE;IAC9B,MAAMH,QAAQ,EAAE;IAChBK,EAAE,IAAIG,IAAI;;AAEd;AAMA;;;AAGA,OAAM,SAAUC,WAAWA,CAACC,GAAW;EACrC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIhC,KAAK,CAAC,oCAAoC,OAAOgC,GAAG,EAAE,CAAC;EAC9F,OAAO,IAAIhD,UAAU,CAAC,IAAIiD,WAAW,EAAE,CAACC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;AACxD;AAGA;;;;;AAKA,OAAM,SAAUG,OAAOA,CAACC,IAAW;EACjC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAGL,WAAW,CAACK,IAAI,CAAC;EACtD,IAAI,CAACtD,GAAG,CAACsD,IAAI,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,4BAA4B,OAAOoC,IAAI,EAAE,CAAC;EAC1E,OAAOA,IAAI;AACb;AAEA;;;AAGA,OAAM,SAAUC,WAAWA,CAAC,GAAGC,MAAoB;EACjD,MAAMC,CAAC,GAAG,IAAIvD,UAAU,CAACsD,MAAM,CAACE,MAAM,CAAC,CAACC,GAAG,EAAE1D,CAAC,KAAK0D,GAAG,GAAG1D,CAAC,CAACqB,MAAM,EAAE,CAAC,CAAC,CAAC;EACtE,IAAIsC,GAAG,GAAG,CAAC,CAAC,CAAC;EACbJ,MAAM,CAACK,OAAO,CAAE5D,CAAC,IAAI;IACnB,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,EAAE,MAAM,IAAIiB,KAAK,CAAC,qBAAqB,CAAC;IACnDuC,CAAC,CAACK,GAAG,CAAC7D,CAAC,EAAE2D,GAAG,CAAC;IACbA,GAAG,IAAI3D,CAAC,CAACqB,MAAM;EACjB,CAAC,CAAC;EACF,OAAOmC,CAAC;AACV;AAEA;AACA,OAAM,MAAgBM,IAAI;EAqBxB;EACAC,KAAKA,CAAA;IACH,OAAO,IAAI,CAACC,UAAU,EAAE;EAC1B;;AAcF,MAAMC,KAAK,GAAG,EAAE,CAACzC,QAAQ;AAEzB,OAAM,SAAU0C,SAASA,CACvBC,QAAY,EACZC,IAAS;EAET,IAAIA,IAAI,KAAKC,SAAS,IAAIJ,KAAK,CAACK,IAAI,CAACF,IAAI,CAAC,KAAK,iBAAiB,EAC9D,MAAM,IAAInD,KAAK,CAAC,uCAAuC,CAAC;EAC1D,MAAMsD,MAAM,GAAGC,MAAM,CAACC,MAAM,CAACN,QAAQ,EAAEC,IAAI,CAAC;EAC5C,OAAOG,MAAiB;AAC1B;AAIA,OAAM,SAAUG,eAAeA,CAAoBC,QAAuB;EACxE,MAAMC,KAAK,GAAIC,GAAU,IAAiBF,QAAQ,EAAE,CAACG,MAAM,CAAC1B,OAAO,CAACyB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAClF,MAAMC,GAAG,GAAGL,QAAQ,EAAE;EACtBC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAG,MAAMR,QAAQ,EAAE;EAC/B,OAAOC,KAAK;AACd;AAEA,OAAM,SAAUQ,uBAAuBA,CACrCT,QAA+B;EAE/B,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAET,IAAQ,KAAiBO,QAAQ,CAACP,IAAI,CAAC,CAACU,MAAM,CAAC1B,OAAO,CAACyB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAIf,IAAO,IAAKO,QAAQ,CAACP,IAAI,CAAC;EAC1C,OAAOQ,KAAK;AACd;AAEA,OAAM,SAAUS,0BAA0BA,CACxCV,QAAkC;EAElC,MAAMC,KAAK,GAAGA,CAACC,GAAU,EAAET,IAAQ,KAAiBO,QAAQ,CAACP,IAAI,CAAC,CAACU,MAAM,CAAC1B,OAAO,CAACyB,GAAG,CAAC,CAAC,CAACE,MAAM,EAAE;EAChG,MAAMC,GAAG,GAAGL,QAAQ,CAAC,EAAO,CAAC;EAC7BC,KAAK,CAACK,SAAS,GAAGD,GAAG,CAACC,SAAS;EAC/BL,KAAK,CAACM,QAAQ,GAAGF,GAAG,CAACE,QAAQ;EAC7BN,KAAK,CAACO,MAAM,GAAIf,IAAO,IAAKO,QAAQ,CAACP,IAAI,CAAC;EAC1C,OAAOQ,KAAK;AACd;AAEA;;;AAGA,OAAM,SAAUU,WAAWA,CAACC,WAAW,GAAG,EAAE;EAC1C,IAAIzF,MAAM,IAAI,OAAOA,MAAM,CAAC0F,eAAe,KAAK,UAAU,EAAE;IAC1D,OAAO1F,MAAM,CAAC0F,eAAe,CAAC,IAAIvF,UAAU,CAACsF,WAAW,CAAC,CAAC;;EAE5D,MAAM,IAAItE,KAAK,CAAC,wCAAwC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}