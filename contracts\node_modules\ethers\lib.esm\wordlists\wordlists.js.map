{"version": 3, "file": "wordlists.js", "sourceRoot": "", "sources": ["../../src.ts/wordlists/wordlists.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAC;AAItC;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,SAAS,GAA6B;IACjD,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE;IACrB,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;IAC5B,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;CAC7B,CAAC"}