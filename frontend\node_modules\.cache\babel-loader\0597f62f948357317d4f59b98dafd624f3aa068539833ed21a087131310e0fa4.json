{"ast": null, "code": "import { assertArgument, makeError } from \"../utils/index.js\";\nimport { JsonRpcApiPollingProvider } from \"./provider-jsonrpc.js\";\n;\n/**\n *  A **BrowserProvider** is intended to wrap an injected provider which\n *  adheres to the [[link-eip-1193]] standard, which most (if not all)\n *  currently do.\n */\nexport class BrowserProvider extends JsonRpcApiPollingProvider {\n  #request;\n  #providerInfo;\n  /**\n   *  Connect to the %%ethereum%% provider, optionally forcing the\n   *  %%network%%.\n   */\n  constructor(ethereum, network, _options) {\n    // Copy the options\n    const options = Object.assign({}, _options != null ? _options : {}, {\n      batchMaxCount: 1\n    });\n    assertArgument(ethereum && ethereum.request, \"invalid EIP-1193 provider\", \"ethereum\", ethereum);\n    super(network, options);\n    this.#providerInfo = null;\n    if (_options && _options.providerInfo) {\n      this.#providerInfo = _options.providerInfo;\n    }\n    this.#request = async (method, params) => {\n      const payload = {\n        method,\n        params\n      };\n      this.emit(\"debug\", {\n        action: \"sendEip1193Request\",\n        payload\n      });\n      try {\n        const result = await ethereum.request(payload);\n        this.emit(\"debug\", {\n          action: \"receiveEip1193Result\",\n          result\n        });\n        return result;\n      } catch (e) {\n        const error = new Error(e.message);\n        error.code = e.code;\n        error.data = e.data;\n        error.payload = payload;\n        this.emit(\"debug\", {\n          action: \"receiveEip1193Error\",\n          error\n        });\n        throw error;\n      }\n    };\n  }\n  get providerInfo() {\n    return this.#providerInfo;\n  }\n  async send(method, params) {\n    await this._start();\n    return await super.send(method, params);\n  }\n  async _send(payload) {\n    assertArgument(!Array.isArray(payload), \"EIP-1193 does not support batch request\", \"payload\", payload);\n    try {\n      const result = await this.#request(payload.method, payload.params || []);\n      return [{\n        id: payload.id,\n        result\n      }];\n    } catch (e) {\n      return [{\n        id: payload.id,\n        error: {\n          code: e.code,\n          data: e.data,\n          message: e.message\n        }\n      }];\n    }\n  }\n  getRpcError(payload, error) {\n    error = JSON.parse(JSON.stringify(error));\n    // EIP-1193 gives us some machine-readable error codes, so rewrite\n    // them into Ethers standard errors.\n    switch (error.error.code || -1) {\n      case 4001:\n        error.error.message = `ethers-user-denied: ${error.error.message}`;\n        break;\n      case 4200:\n        error.error.message = `ethers-unsupported: ${error.error.message}`;\n        break;\n    }\n    return super.getRpcError(payload, error);\n  }\n  /**\n   *  Resolves to ``true`` if the provider manages the %%address%%.\n   */\n  async hasSigner(address) {\n    if (address == null) {\n      address = 0;\n    }\n    const accounts = await this.send(\"eth_accounts\", []);\n    if (typeof address === \"number\") {\n      return accounts.length > address;\n    }\n    address = address.toLowerCase();\n    return accounts.filter(a => a.toLowerCase() === address).length !== 0;\n  }\n  async getSigner(address) {\n    if (address == null) {\n      address = 0;\n    }\n    if (!(await this.hasSigner(address))) {\n      try {\n        await this.#request(\"eth_requestAccounts\", []);\n      } catch (error) {\n        const payload = error.payload;\n        throw this.getRpcError(payload, {\n          id: payload.id,\n          error\n        });\n      }\n    }\n    return await super.getSigner(address);\n  }\n  /**\n   *  Discover and connect to a Provider in the Browser using the\n   *  [[link-eip-6963]] discovery mechanism. If no providers are\n   *  present, ``null`` is resolved.\n   */\n  static async discover(options) {\n    if (options == null) {\n      options = {};\n    }\n    if (options.provider) {\n      return new BrowserProvider(options.provider);\n    }\n    const context = options.window ? options.window : typeof window !== \"undefined\" ? window : null;\n    if (context == null) {\n      return null;\n    }\n    const anyProvider = options.anyProvider;\n    if (anyProvider && context.ethereum) {\n      return new BrowserProvider(context.ethereum);\n    }\n    if (!(\"addEventListener\" in context && \"dispatchEvent\" in context && \"removeEventListener\" in context)) {\n      return null;\n    }\n    const timeout = options.timeout ? options.timeout : 300;\n    if (timeout === 0) {\n      return null;\n    }\n    return await new Promise((resolve, reject) => {\n      let found = [];\n      const addProvider = event => {\n        found.push(event.detail);\n        if (anyProvider) {\n          finalize();\n        }\n      };\n      const finalize = () => {\n        clearTimeout(timer);\n        if (found.length) {\n          // If filtering is provided:\n          if (options && options.filter) {\n            // Call filter, with a copies of found provider infos\n            const filtered = options.filter(found.map(i => Object.assign({}, i.info)));\n            if (filtered == null) {\n              // No provider selected\n              resolve(null);\n            } else if (filtered instanceof BrowserProvider) {\n              // Custom provider created\n              resolve(filtered);\n            } else {\n              // Find the matching provider\n              let match = null;\n              if (filtered.uuid) {\n                const matches = found.filter(f => filtered.uuid === f.info.uuid);\n                // @TODO: What should happen if multiple values\n                //        for the same UUID?\n                match = matches[0];\n              }\n              if (match) {\n                const {\n                  provider,\n                  info\n                } = match;\n                resolve(new BrowserProvider(provider, undefined, {\n                  providerInfo: info\n                }));\n              } else {\n                reject(makeError(\"filter returned unknown info\", \"UNSUPPORTED_OPERATION\", {\n                  value: filtered\n                }));\n              }\n            }\n          } else {\n            // Pick the first found provider\n            const {\n              provider,\n              info\n            } = found[0];\n            resolve(new BrowserProvider(provider, undefined, {\n              providerInfo: info\n            }));\n          }\n        } else {\n          // Nothing found\n          resolve(null);\n        }\n        context.removeEventListener(\"eip6963:announceProvider\", addProvider);\n      };\n      const timer = setTimeout(() => {\n        finalize();\n      }, timeout);\n      context.addEventListener(\"eip6963:announceProvider\", addProvider);\n      context.dispatchEvent(new Event(\"eip6963:requestProvider\"));\n    });\n  }\n}", "map": {"version": 3, "names": ["assertArgument", "makeError", "JsonRpcApiPollingProvider", "Browser<PERSON>rovider", "request", "providerInfo", "constructor", "ethereum", "network", "_options", "options", "Object", "assign", "batchMaxCount", "method", "params", "payload", "emit", "action", "result", "e", "error", "Error", "message", "code", "data", "send", "_start", "_send", "Array", "isArray", "id", "getRpcError", "JSON", "parse", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "address", "accounts", "length", "toLowerCase", "filter", "a", "<PERSON><PERSON><PERSON><PERSON>", "discover", "provider", "context", "window", "anyProvider", "timeout", "Promise", "resolve", "reject", "found", "addProvider", "event", "push", "detail", "finalize", "clearTimeout", "timer", "filtered", "map", "i", "info", "match", "uuid", "matches", "f", "undefined", "value", "removeEventListener", "setTimeout", "addEventListener", "dispatchEvent", "Event"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-browser.ts"], "sourcesContent": ["\nimport { assertArgument, makeError } from \"../utils/index.js\";\n\nimport { JsonRpcApiPollingProvider } from \"./provider-jsonrpc.js\";\n\nimport type {\n    JsonRpcApiProviderOptions,\n    JsonRpcError, <PERSON>sonRpc<PERSON><PERSON><PERSON>, <PERSON>sonRpc<PERSON><PERSON>ult,\n    <PERSON>son<PERSON>p<PERSON><PERSON><PERSON><PERSON>\n} from \"./provider-jsonrpc.js\";\nimport type { Network, Networkish } from \"./network.js\";\n\n/**\n *  The interface to an [[link-eip-1193]] provider, which is a standard\n *  used by most injected providers, which the [[BrowserProvider]] accepts\n *  and exposes the API of.\n */\nexport interface Eip1193Provider {\n    /**\n     *  See [[link-eip-1193]] for details on this method.\n     */\n    request(request: { method: string, params?: Array<any> | Record<string, any> }): Promise<any>;\n};\n\n/**\n *  The possible additional events dispatched when using the ``\"debug\"``\n *  event on a [[BrowserProvider]].\n */\nexport type DebugEventBrowserProvider = {\n    action: \"sendEip1193Payload\",\n    payload: { method: string, params: Array<any> }\n} | {\n    action: \"receiveEip1193Result\",\n    result: any\n} | {\n    action: \"receiveEip1193Error\",\n    error: Error\n};\n\n/**\n *  Provider info provided by the [[link-eip-6963]] discovery mechanism.\n */\nexport interface Eip6963ProviderInfo {\n    uuid: string;\n    name: string;\n    icon: string;\n    rdns: string;\n}\n\ninterface Eip6963ProviderDetail {\n    info: Eip6963ProviderInfo;\n    provider: Eip1193Provider;\n}\n\ninterface Eip6963Announcement {\n    type: \"eip6963:announceProvider\";\n    detail: Eip6963ProviderDetail\n}\n\nexport type BrowserProviderOptions = {\n    polling?: boolean;\n    staticNetwork?: null | boolean | Network;\n\n    cacheTimeout?: number;\n    pollingInterval?: number;\n\n    providerInfo?: Eip6963ProviderInfo;\n};\n\n/**\n *  Specifies how [[link-eip-6963]] discovery should proceed.\n *\n *  See: [[BrowserProvider-discover]]\n */\nexport interface BrowserDiscoverOptions {\n    /**\n     *  Override provider detection with this provider.\n     */\n    provider?: Eip1193Provider;\n\n    /**\n     *  Duration to wait to detect providers. (default: 300ms)\n     */\n    timeout?: number;\n\n    /**\n     *  Return the first detected provider. Otherwise wait for %%timeout%%\n     *  and allowing filtering before selecting the desired provider.\n     */\n    anyProvider?: boolean;\n\n    /**\n     *  Use the provided window context. Useful in non-standard\n     *  environments or to hijack where a provider comes from.\n     */\n    window?: any;\n\n    /**\n     *  Explicitly choose which provider to used once scanning is complete.\n     */\n    filter?: (found: Array<Eip6963ProviderInfo>) => null | BrowserProvider |\n      Eip6963ProviderInfo;\n}\n\n\n/**\n *  A **BrowserProvider** is intended to wrap an injected provider which\n *  adheres to the [[link-eip-1193]] standard, which most (if not all)\n *  currently do.\n */\nexport class BrowserProvider extends JsonRpcApiPollingProvider {\n    #request: (method: string, params: Array<any> | Record<string, any>) => Promise<any>;\n\n    #providerInfo: null | Eip6963ProviderInfo;\n\n    /**\n     *  Connect to the %%ethereum%% provider, optionally forcing the\n     *  %%network%%.\n     */\n    constructor(ethereum: Eip1193Provider, network?: Networkish, _options?: BrowserProviderOptions) {\n\n        // Copy the options\n        const options: JsonRpcApiProviderOptions = Object.assign({ },\n          ((_options != null) ? _options: { }),\n          { batchMaxCount: 1 });\n\n        assertArgument(ethereum && ethereum.request, \"invalid EIP-1193 provider\", \"ethereum\", ethereum);\n\n        super(network, options);\n\n        this.#providerInfo = null;\n        if (_options && _options.providerInfo) {\n            this.#providerInfo = _options.providerInfo;\n        }\n\n        this.#request = async (method: string, params: Array<any> | Record<string, any>) => {\n            const payload = { method, params };\n            this.emit(\"debug\", { action: \"sendEip1193Request\", payload });\n            try {\n                const result = await ethereum.request(payload);\n                this.emit(\"debug\", { action: \"receiveEip1193Result\", result });\n                return result;\n            } catch (e: any) {\n                const error = new Error(e.message);\n                (<any>error).code = e.code;\n                (<any>error).data = e.data;\n                (<any>error).payload = payload;\n                this.emit(\"debug\", { action: \"receiveEip1193Error\", error });\n                throw error;\n            }\n        };\n    }\n\n    get providerInfo(): null | Eip6963ProviderInfo {\n        return this.#providerInfo;\n    }\n\n    async send(method: string, params: Array<any> | Record<string, any>): Promise<any> {\n        await this._start();\n\n        return await super.send(method, params);\n    }\n\n    async _send(payload: JsonRpcPayload | Array<JsonRpcPayload>): Promise<Array<JsonRpcResult | JsonRpcError>> {\n        assertArgument(!Array.isArray(payload), \"EIP-1193 does not support batch request\", \"payload\", payload);\n\n        try {\n            const result = await this.#request(payload.method, payload.params || [ ]);\n            return [ { id: payload.id, result } ];\n        } catch (e: any) {\n            return [ {\n                id: payload.id,\n                error: { code: e.code, data: e.data, message: e.message }\n            } ];\n        }\n    }\n\n    getRpcError(payload: JsonRpcPayload, error: JsonRpcError): Error {\n\n        error = JSON.parse(JSON.stringify(error));\n\n        // EIP-1193 gives us some machine-readable error codes, so rewrite\n        // them into Ethers standard errors.\n        switch (error.error.code || -1) {\n            case 4001:\n                error.error.message = `ethers-user-denied: ${ error.error.message }`;\n                break;\n            case 4200:\n                error.error.message = `ethers-unsupported: ${ error.error.message }`;\n                break;\n        }\n\n        return super.getRpcError(payload, error);\n    }\n\n    /**\n     *  Resolves to ``true`` if the provider manages the %%address%%.\n     */\n    async hasSigner(address: number | string): Promise<boolean> {\n        if (address == null) { address = 0; }\n\n        const accounts = await this.send(\"eth_accounts\", [ ]);\n        if (typeof(address) === \"number\") {\n            return (accounts.length > address);\n        }\n\n        address = address.toLowerCase();\n        return accounts.filter((a: string) => (a.toLowerCase() === address)).length !== 0;\n    }\n\n    async getSigner(address?: number | string): Promise<JsonRpcSigner> {\n        if (address == null) { address = 0; }\n\n        if (!(await this.hasSigner(address))) {\n            try {\n                await this.#request(\"eth_requestAccounts\", [ ]);\n\n            } catch (error: any) {\n                const payload = error.payload;\n                throw this.getRpcError(payload, { id: payload.id, error });\n            }\n        }\n\n        return await super.getSigner(address);\n    }\n\n    /**\n     *  Discover and connect to a Provider in the Browser using the\n     *  [[link-eip-6963]] discovery mechanism. If no providers are\n     *  present, ``null`` is resolved.\n     */\n    static async discover(options?: BrowserDiscoverOptions): Promise<null | BrowserProvider> {\n        if (options == null) { options = { }; }\n\n        if (options.provider) {\n            return new BrowserProvider(options.provider);\n        }\n\n        const context = options.window ? options.window:\n            (typeof(window) !== \"undefined\") ? window: null;\n\n        if (context == null) { return null; }\n\n        const anyProvider = options.anyProvider;\n        if (anyProvider && context.ethereum) {\n            return new BrowserProvider(context.ethereum);\n        }\n\n        if (!(\"addEventListener\" in context && \"dispatchEvent\" in context\n          && \"removeEventListener\" in context)) {\n            return null;\n        }\n\n        const timeout = options.timeout ? options.timeout: 300;\n        if (timeout === 0) { return null; }\n\n        return await (new Promise((resolve, reject) => {\n            let found: Array<Eip6963ProviderDetail> = [ ];\n\n            const addProvider = (event: Eip6963Announcement) => {\n                found.push(event.detail);\n                if (anyProvider) { finalize(); }\n            };\n\n            const finalize = () => {\n                clearTimeout(timer);\n\n                if (found.length) {\n\n                    // If filtering is provided:\n                    if (options && options.filter) {\n\n                        // Call filter, with a copies of found provider infos\n                        const filtered = options.filter(found.map(i =>\n                          Object.assign({ }, (i.info))));\n\n                        if (filtered == null) {\n                            // No provider selected\n                            resolve(null);\n\n                        } else if (filtered instanceof BrowserProvider) {\n                            // Custom provider created\n                            resolve(filtered);\n\n                        } else {\n                            // Find the matching provider\n                            let match: null | Eip6963ProviderDetail = null;\n                            if (filtered.uuid) {\n                                const matches = found.filter(f =>\n                                  (filtered.uuid === f.info.uuid));\n                                // @TODO: What should happen if multiple values\n                                //        for the same UUID?\n                                match = matches[0];\n                            }\n\n                            if (match) {\n                                const { provider, info } = match;\n                                resolve(new BrowserProvider(provider, undefined, {\n                                    providerInfo: info\n                                }));\n                            } else {\n                                reject(makeError(\"filter returned unknown info\", \"UNSUPPORTED_OPERATION\", {\n                                    value: filtered\n                                }));\n                            }\n                        }\n\n                    } else {\n\n                        // Pick the first found provider\n                        const { provider, info } = found[0];\n                        resolve(new BrowserProvider(provider, undefined, {\n                            providerInfo: info\n                        }));\n                    }\n\n                } else {\n                    // Nothing found\n                    resolve(null);\n                }\n\n                context.removeEventListener(<any>\"eip6963:announceProvider\",\n                  addProvider);\n            };\n\n            const timer = setTimeout(() => { finalize(); }, timeout);\n\n            context.addEventListener(<any>\"eip6963:announceProvider\",\n              addProvider);\n\n            context.dispatchEvent(new Event(\"eip6963:requestProvider\"));\n        }));\n    }\n}\n"], "mappings": "AACA,SAASA,cAAc,EAAEC,SAAS,QAAQ,mBAAmB;AAE7D,SAASC,yBAAyB,QAAQ,uBAAuB;AAmBhE;AAmFD;;;;;AAKA,OAAM,MAAOC,eAAgB,SAAQD,yBAAyB;EAC1D,CAAAE,OAAQ;EAER,CAAAC,YAAa;EAEb;;;;EAIAC,YAAYC,QAAyB,EAAEC,OAAoB,EAAEC,QAAiC;IAE1F;IACA,MAAMC,OAAO,GAA8BC,MAAM,CAACC,MAAM,CAAC,EAAG,EACxDH,QAAQ,IAAI,IAAI,GAAIA,QAAQ,GAAE,EAAG,EACnC;MAAEI,aAAa,EAAE;IAAC,CAAE,CAAC;IAEvBb,cAAc,CAACO,QAAQ,IAAIA,QAAQ,CAACH,OAAO,EAAE,2BAA2B,EAAE,UAAU,EAAEG,QAAQ,CAAC;IAE/F,KAAK,CAACC,OAAO,EAAEE,OAAO,CAAC;IAEvB,IAAI,CAAC,CAAAL,YAAa,GAAG,IAAI;IACzB,IAAII,QAAQ,IAAIA,QAAQ,CAACJ,YAAY,EAAE;MACnC,IAAI,CAAC,CAAAA,YAAa,GAAGI,QAAQ,CAACJ,YAAY;;IAG9C,IAAI,CAAC,CAAAD,OAAQ,GAAG,OAAOU,MAAc,EAAEC,MAAwC,KAAI;MAC/E,MAAMC,OAAO,GAAG;QAAEF,MAAM;QAAEC;MAAM,CAAE;MAClC,IAAI,CAACE,IAAI,CAAC,OAAO,EAAE;QAAEC,MAAM,EAAE,oBAAoB;QAAEF;MAAO,CAAE,CAAC;MAC7D,IAAI;QACA,MAAMG,MAAM,GAAG,MAAMZ,QAAQ,CAACH,OAAO,CAACY,OAAO,CAAC;QAC9C,IAAI,CAACC,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,sBAAsB;UAAEC;QAAM,CAAE,CAAC;QAC9D,OAAOA,MAAM;OAChB,CAAC,OAAOC,CAAM,EAAE;QACb,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,CAAC,CAACG,OAAO,CAAC;QAC5BF,KAAM,CAACG,IAAI,GAAGJ,CAAC,CAACI,IAAI;QACpBH,KAAM,CAACI,IAAI,GAAGL,CAAC,CAACK,IAAI;QACpBJ,KAAM,CAACL,OAAO,GAAGA,OAAO;QAC9B,IAAI,CAACC,IAAI,CAAC,OAAO,EAAE;UAAEC,MAAM,EAAE,qBAAqB;UAAEG;QAAK,CAAE,CAAC;QAC5D,MAAMA,KAAK;;IAEnB,CAAC;EACL;EAEA,IAAIhB,YAAYA,CAAA;IACZ,OAAO,IAAI,CAAC,CAAAA,YAAa;EAC7B;EAEA,MAAMqB,IAAIA,CAACZ,MAAc,EAAEC,MAAwC;IAC/D,MAAM,IAAI,CAACY,MAAM,EAAE;IAEnB,OAAO,MAAM,KAAK,CAACD,IAAI,CAACZ,MAAM,EAAEC,MAAM,CAAC;EAC3C;EAEA,MAAMa,KAAKA,CAACZ,OAA+C;IACvDhB,cAAc,CAAC,CAAC6B,KAAK,CAACC,OAAO,CAACd,OAAO,CAAC,EAAE,yCAAyC,EAAE,SAAS,EAAEA,OAAO,CAAC;IAEtG,IAAI;MACA,MAAMG,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAf,OAAQ,CAACY,OAAO,CAACF,MAAM,EAAEE,OAAO,CAACD,MAAM,IAAI,EAAG,CAAC;MACzE,OAAO,CAAE;QAAEgB,EAAE,EAAEf,OAAO,CAACe,EAAE;QAAEZ;MAAM,CAAE,CAAE;KACxC,CAAC,OAAOC,CAAM,EAAE;MACb,OAAO,CAAE;QACLW,EAAE,EAAEf,OAAO,CAACe,EAAE;QACdV,KAAK,EAAE;UAAEG,IAAI,EAAEJ,CAAC,CAACI,IAAI;UAAEC,IAAI,EAAEL,CAAC,CAACK,IAAI;UAAEF,OAAO,EAAEH,CAAC,CAACG;QAAO;OAC1D,CAAE;;EAEX;EAEAS,WAAWA,CAAChB,OAAuB,EAAEK,KAAmB;IAEpDA,KAAK,GAAGY,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACd,KAAK,CAAC,CAAC;IAEzC;IACA;IACA,QAAQA,KAAK,CAACA,KAAK,CAACG,IAAI,IAAI,CAAC,CAAC;MAC1B,KAAK,IAAI;QACLH,KAAK,CAACA,KAAK,CAACE,OAAO,GAAG,uBAAwBF,KAAK,CAACA,KAAK,CAACE,OAAQ,EAAE;QACpE;MACJ,KAAK,IAAI;QACLF,KAAK,CAACA,KAAK,CAACE,OAAO,GAAG,uBAAwBF,KAAK,CAACA,KAAK,CAACE,OAAQ,EAAE;QACpE;;IAGR,OAAO,KAAK,CAACS,WAAW,CAAChB,OAAO,EAAEK,KAAK,CAAC;EAC5C;EAEA;;;EAGA,MAAMe,SAASA,CAACC,OAAwB;IACpC,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,CAAC;;IAElC,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACZ,IAAI,CAAC,cAAc,EAAE,EAAG,CAAC;IACrD,IAAI,OAAOW,OAAQ,KAAK,QAAQ,EAAE;MAC9B,OAAQC,QAAQ,CAACC,MAAM,GAAGF,OAAO;;IAGrCA,OAAO,GAAGA,OAAO,CAACG,WAAW,EAAE;IAC/B,OAAOF,QAAQ,CAACG,MAAM,CAAEC,CAAS,IAAMA,CAAC,CAACF,WAAW,EAAE,KAAKH,OAAQ,CAAC,CAACE,MAAM,KAAK,CAAC;EACrF;EAEA,MAAMI,SAASA,CAACN,OAAyB;IACrC,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,CAAC;;IAElC,IAAI,EAAE,MAAM,IAAI,CAACD,SAAS,CAACC,OAAO,CAAC,CAAC,EAAE;MAClC,IAAI;QACA,MAAM,IAAI,CAAC,CAAAjC,OAAQ,CAAC,qBAAqB,EAAE,EAAG,CAAC;OAElD,CAAC,OAAOiB,KAAU,EAAE;QACjB,MAAML,OAAO,GAAGK,KAAK,CAACL,OAAO;QAC7B,MAAM,IAAI,CAACgB,WAAW,CAAChB,OAAO,EAAE;UAAEe,EAAE,EAAEf,OAAO,CAACe,EAAE;UAAEV;QAAK,CAAE,CAAC;;;IAIlE,OAAO,MAAM,KAAK,CAACsB,SAAS,CAACN,OAAO,CAAC;EACzC;EAEA;;;;;EAKA,aAAaO,QAAQA,CAAClC,OAAgC;IAClD,IAAIA,OAAO,IAAI,IAAI,EAAE;MAAEA,OAAO,GAAG,EAAG;;IAEpC,IAAIA,OAAO,CAACmC,QAAQ,EAAE;MAClB,OAAO,IAAI1C,eAAe,CAACO,OAAO,CAACmC,QAAQ,CAAC;;IAGhD,MAAMC,OAAO,GAAGpC,OAAO,CAACqC,MAAM,GAAGrC,OAAO,CAACqC,MAAM,GAC1C,OAAOA,MAAO,KAAK,WAAW,GAAIA,MAAM,GAAE,IAAI;IAEnD,IAAID,OAAO,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAElC,MAAME,WAAW,GAAGtC,OAAO,CAACsC,WAAW;IACvC,IAAIA,WAAW,IAAIF,OAAO,CAACvC,QAAQ,EAAE;MACjC,OAAO,IAAIJ,eAAe,CAAC2C,OAAO,CAACvC,QAAQ,CAAC;;IAGhD,IAAI,EAAE,kBAAkB,IAAIuC,OAAO,IAAI,eAAe,IAAIA,OAAO,IAC5D,qBAAqB,IAAIA,OAAO,CAAC,EAAE;MACpC,OAAO,IAAI;;IAGf,MAAMG,OAAO,GAAGvC,OAAO,CAACuC,OAAO,GAAGvC,OAAO,CAACuC,OAAO,GAAE,GAAG;IACtD,IAAIA,OAAO,KAAK,CAAC,EAAE;MAAE,OAAO,IAAI;;IAEhC,OAAO,MAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC1C,IAAIC,KAAK,GAAiC,EAAG;MAE7C,MAAMC,WAAW,GAAIC,KAA0B,IAAI;QAC/CF,KAAK,CAACG,IAAI,CAACD,KAAK,CAACE,MAAM,CAAC;QACxB,IAAIT,WAAW,EAAE;UAAEU,QAAQ,EAAE;;MACjC,CAAC;MAED,MAAMA,QAAQ,GAAGA,CAAA,KAAK;QAClBC,YAAY,CAACC,KAAK,CAAC;QAEnB,IAAIP,KAAK,CAACd,MAAM,EAAE;UAEd;UACA,IAAI7B,OAAO,IAAIA,OAAO,CAAC+B,MAAM,EAAE;YAE3B;YACA,MAAMoB,QAAQ,GAAGnD,OAAO,CAAC+B,MAAM,CAACY,KAAK,CAACS,GAAG,CAACC,CAAC,IACzCpD,MAAM,CAACC,MAAM,CAAC,EAAG,EAAGmD,CAAC,CAACC,IAAK,CAAC,CAAC,CAAC;YAEhC,IAAIH,QAAQ,IAAI,IAAI,EAAE;cAClB;cACAV,OAAO,CAAC,IAAI,CAAC;aAEhB,MAAM,IAAIU,QAAQ,YAAY1D,eAAe,EAAE;cAC5C;cACAgD,OAAO,CAACU,QAAQ,CAAC;aAEpB,MAAM;cACH;cACA,IAAII,KAAK,GAAiC,IAAI;cAC9C,IAAIJ,QAAQ,CAACK,IAAI,EAAE;gBACf,MAAMC,OAAO,GAAGd,KAAK,CAACZ,MAAM,CAAC2B,CAAC,IAC3BP,QAAQ,CAACK,IAAI,KAAKE,CAAC,CAACJ,IAAI,CAACE,IAAK,CAAC;gBAClC;gBACA;gBACAD,KAAK,GAAGE,OAAO,CAAC,CAAC,CAAC;;cAGtB,IAAIF,KAAK,EAAE;gBACP,MAAM;kBAAEpB,QAAQ;kBAAEmB;gBAAI,CAAE,GAAGC,KAAK;gBAChCd,OAAO,CAAC,IAAIhD,eAAe,CAAC0C,QAAQ,EAAEwB,SAAS,EAAE;kBAC7ChE,YAAY,EAAE2D;iBACjB,CAAC,CAAC;eACN,MAAM;gBACHZ,MAAM,CAACnD,SAAS,CAAC,8BAA8B,EAAE,uBAAuB,EAAE;kBACtEqE,KAAK,EAAET;iBACV,CAAC,CAAC;;;WAId,MAAM;YAEH;YACA,MAAM;cAAEhB,QAAQ;cAAEmB;YAAI,CAAE,GAAGX,KAAK,CAAC,CAAC,CAAC;YACnCF,OAAO,CAAC,IAAIhD,eAAe,CAAC0C,QAAQ,EAAEwB,SAAS,EAAE;cAC7ChE,YAAY,EAAE2D;aACjB,CAAC,CAAC;;SAGV,MAAM;UACH;UACAb,OAAO,CAAC,IAAI,CAAC;;QAGjBL,OAAO,CAACyB,mBAAmB,CAAM,0BAA0B,EACzDjB,WAAW,CAAC;MAClB,CAAC;MAED,MAAMM,KAAK,GAAGY,UAAU,CAAC,MAAK;QAAGd,QAAQ,EAAE;MAAE,CAAC,EAAET,OAAO,CAAC;MAExDH,OAAO,CAAC2B,gBAAgB,CAAM,0BAA0B,EACtDnB,WAAW,CAAC;MAEdR,OAAO,CAAC4B,aAAa,CAAC,IAAIC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC/D,CAAC,CAAE;EACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}