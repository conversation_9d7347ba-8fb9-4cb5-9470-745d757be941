{"ast": null, "code": "// NFKC (composed)             // (decomposed)\n/**\n *  A constant for the ether symbol (normalized using NFKC).\n *\n *  (**i.e.** ``\"\\\\u039e\"``)\n */\nexport const EtherSymbol = \"\\u039e\"; // \"\\uD835\\uDF63\";\n/**\n *  A constant for the [[link-eip-191]] personal message prefix.\n *\n *  (**i.e.** ``\"\\\\x19Ethereum Signed Message:\\\\n\"``)\n */\nexport const MessagePrefix = \"\\x19Ethereum Signed Message:\\n\";", "map": {"version": 3, "names": ["EtherSymbol", "MessagePrefix"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\constants\\strings.ts"], "sourcesContent": ["// NFKC (composed)             // (decomposed)\n\n/**\n *  A constant for the ether symbol (normalized using NFKC).\n *\n *  (**i.e.** ``\"\\\\u039e\"``)\n */\nexport const EtherSymbol: string = \"\\u039e\";  // \"\\uD835\\uDF63\";\n\n\n/**\n *  A constant for the [[link-eip-191]] personal message prefix.\n *\n *  (**i.e.** ``\"\\\\x19Ethereum Signed Message:\\\\n\"``)\n */\nexport const MessagePrefix: string = \"\\x19Ethereum Signed Message:\\n\";\n"], "mappings": "AAAA;AAEA;;;;;AAKA,OAAO,MAAMA,WAAW,GAAW,QAAQ,CAAC,CAAE;AAG9C;;;;;AAKA,OAAO,MAAMC,aAAa,GAAW,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}