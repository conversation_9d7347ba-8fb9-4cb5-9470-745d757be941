{"ast": null, "code": "import { isError } from \"../utils/index.js\";\nimport { PollingEventSubscriber } from \"./subscriber-polling.js\";\nfunction copy(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}\n/**\n *  Some backends support subscribing to events using a Filter ID.\n *\n *  When subscribing with this technique, the node issues a unique\n *  //Filter ID//. At this point the node dedicates resources to\n *  the filter, so that periodic calls to follow up on the //Filter ID//\n *  will receive any events since the last call.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdSubscriber {\n  #provider;\n  #filterIdPromise;\n  #poller;\n  #running;\n  #network;\n  #hault;\n  /**\n   *  Creates a new **FilterIdSubscriber** which will used [[_subscribe]]\n   *  and [[_emitResults]] to setup the subscription and provide the event\n   *  to the %%provider%%.\n   */\n  constructor(provider) {\n    this.#provider = provider;\n    this.#filterIdPromise = null;\n    this.#poller = this.#poll.bind(this);\n    this.#running = false;\n    this.#network = null;\n    this.#hault = false;\n  }\n  /**\n   *  Sub-classes **must** override this to begin the subscription.\n   */\n  _subscribe(provider) {\n    throw new Error(\"subclasses must override this\");\n  }\n  /**\n   *  Sub-classes **must** override this handle the events.\n   */\n  _emitResults(provider, result) {\n    throw new Error(\"subclasses must override this\");\n  }\n  /**\n   *  Sub-classes **must** override this handle recovery on errors.\n   */\n  _recover(provider) {\n    throw new Error(\"subclasses must override this\");\n  }\n  async #poll(blockNumber) {\n    try {\n      // Subscribe if necessary\n      if (this.#filterIdPromise == null) {\n        this.#filterIdPromise = this._subscribe(this.#provider);\n      }\n      // Get the Filter ID\n      let filterId = null;\n      try {\n        filterId = await this.#filterIdPromise;\n      } catch (error) {\n        if (!isError(error, \"UNSUPPORTED_OPERATION\") || error.operation !== \"eth_newFilter\") {\n          throw error;\n        }\n      }\n      // The backend does not support Filter ID; downgrade to\n      // polling\n      if (filterId == null) {\n        this.#filterIdPromise = null;\n        this.#provider._recoverSubscriber(this, this._recover(this.#provider));\n        return;\n      }\n      const network = await this.#provider.getNetwork();\n      if (!this.#network) {\n        this.#network = network;\n      }\n      if (this.#network.chainId !== network.chainId) {\n        throw new Error(\"chaid changed\");\n      }\n      if (this.#hault) {\n        return;\n      }\n      const result = await this.#provider.send(\"eth_getFilterChanges\", [filterId]);\n      await this._emitResults(this.#provider, result);\n    } catch (error) {\n      console.log(\"@TODO\", error);\n    }\n    this.#provider.once(\"block\", this.#poller);\n  }\n  #teardown() {\n    const filterIdPromise = this.#filterIdPromise;\n    if (filterIdPromise) {\n      this.#filterIdPromise = null;\n      filterIdPromise.then(filterId => {\n        if (this.#provider.destroyed) {\n          return;\n        }\n        this.#provider.send(\"eth_uninstallFilter\", [filterId]);\n      });\n    }\n  }\n  start() {\n    if (this.#running) {\n      return;\n    }\n    this.#running = true;\n    this.#poll(-2);\n  }\n  stop() {\n    if (!this.#running) {\n      return;\n    }\n    this.#running = false;\n    this.#hault = true;\n    this.#teardown();\n    this.#provider.off(\"block\", this.#poller);\n  }\n  pause(dropWhilePaused) {\n    if (dropWhilePaused) {\n      this.#teardown();\n    }\n    this.#provider.off(\"block\", this.#poller);\n  }\n  resume() {\n    this.start();\n  }\n}\n/**\n *  A **FilterIdSubscriber** for receiving contract events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdEventSubscriber extends FilterIdSubscriber {\n  #event;\n  /**\n   *  Creates a new **FilterIdEventSubscriber** attached to %%provider%%\n   *  listening for %%filter%%.\n   */\n  constructor(provider, filter) {\n    super(provider);\n    this.#event = copy(filter);\n  }\n  _recover(provider) {\n    return new PollingEventSubscriber(provider, this.#event);\n  }\n  async _subscribe(provider) {\n    const filterId = await provider.send(\"eth_newFilter\", [this.#event]);\n    return filterId;\n  }\n  async _emitResults(provider, results) {\n    for (const result of results) {\n      provider.emit(this.#event, provider._wrapLog(result, provider._network));\n    }\n  }\n}\n/**\n *  A **FilterIdSubscriber** for receiving pending transactions events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdPendingSubscriber extends FilterIdSubscriber {\n  async _subscribe(provider) {\n    return await provider.send(\"eth_newPendingTransactionFilter\", []);\n  }\n  async _emitResults(provider, results) {\n    for (const result of results) {\n      provider.emit(\"pending\", result);\n    }\n  }\n}", "map": {"version": 3, "names": ["isError", "PollingEventSubscriber", "copy", "obj", "JSON", "parse", "stringify", "FilterIdSubscriber", "provider", "filterIdPromise", "poller", "running", "network", "hault", "constructor", "poll", "bind", "_subscribe", "Error", "_emitResults", "result", "_recover", "#poll", "blockNumber", "filterId", "error", "operation", "_recoverSubscriber", "getNetwork", "chainId", "send", "console", "log", "once", "teardown", "#teardown", "then", "destroyed", "start", "stop", "off", "pause", "dropWhilePaused", "resume", "FilterIdEventSubscriber", "event", "filter", "results", "emit", "_wrapLog", "_network", "FilterIdPendingSubscriber"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\subscriber-filterid.ts"], "sourcesContent": ["import { isError } from \"../utils/index.js\";\n\nimport { PollingEventSubscriber } from \"./subscriber-polling.js\";\n\nimport type { AbstractProvider, Subscriber } from \"./abstract-provider.js\";\nimport type { Network } from \"./network.js\";\nimport type { EventFilter } from \"./provider.js\";\nimport type { JsonRpcApiProvider } from \"./provider-jsonrpc.js\";\n\nfunction copy(obj: any): any {\n    return JSON.parse(JSON.stringify(obj));\n}\n\n/**\n *  Some backends support subscribing to events using a Filter ID.\n *\n *  When subscribing with this technique, the node issues a unique\n *  //Filter ID//. At this point the node dedicates resources to\n *  the filter, so that periodic calls to follow up on the //Filter ID//\n *  will receive any events since the last call.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdSubscriber implements Subscriber {\n    #provider: JsonRpcApiProvider;\n\n    #filterIdPromise: null | Promise<string>;\n    #poller: (b: number) => Promise<void>;\n\n    #running: boolean;\n\n    #network: null | Network;\n\n    #hault: boolean;\n\n    /**\n     *  Creates a new **FilterIdSubscriber** which will used [[_subscribe]]\n     *  and [[_emitResults]] to setup the subscription and provide the event\n     *  to the %%provider%%.\n     */\n    constructor(provider: JsonRpcApiProvider) {\n        this.#provider = provider;\n\n        this.#filterIdPromise = null;\n        this.#poller = this.#poll.bind(this);\n\n        this.#running = false;\n\n        this.#network = null;\n\n        this.#hault = false;\n    }\n\n    /**\n     *  Sub-classes **must** override this to begin the subscription.\n     */\n    _subscribe(provider: JsonRpcApiProvider): Promise<string> {\n        throw new Error(\"subclasses must override this\");\n    }\n\n    /**\n     *  Sub-classes **must** override this handle the events.\n     */\n    _emitResults(provider: AbstractProvider, result: Array<any>): Promise<void> {\n        throw new Error(\"subclasses must override this\");\n    }\n\n    /**\n     *  Sub-classes **must** override this handle recovery on errors.\n     */\n    _recover(provider: AbstractProvider): Subscriber {\n        throw new Error(\"subclasses must override this\");\n    }\n\n    async #poll(blockNumber: number): Promise<void> {\n        try {\n            // Subscribe if necessary\n            if (this.#filterIdPromise == null) {\n                this.#filterIdPromise = this._subscribe(this.#provider);\n            }\n\n            // Get the Filter ID\n            let filterId: null | string = null;\n            try {\n                filterId = await this.#filterIdPromise;\n            } catch (error) {\n                if (!isError(error, \"UNSUPPORTED_OPERATION\") || error.operation !== \"eth_newFilter\") {\n                    throw error;\n                }\n            }\n\n            // The backend does not support Filter ID; downgrade to\n            // polling\n            if (filterId == null) {\n                this.#filterIdPromise = null;\n                this.#provider._recoverSubscriber(this, this._recover(this.#provider));\n                return;\n            }\n\n            const network = await this.#provider.getNetwork();\n            if (!this.#network) { this.#network = network; }\n\n            if ((this.#network as Network).chainId !== network.chainId) {\n                throw new Error(\"chaid changed\");\n            }\n\n            if (this.#hault) { return; }\n\n            const result = await this.#provider.send(\"eth_getFilterChanges\", [ filterId ]);\n            await this._emitResults(this.#provider, result);\n        } catch (error) { console.log(\"@TODO\", error); }\n\n        this.#provider.once(\"block\", this.#poller);\n    }\n\n    #teardown(): void {\n        const filterIdPromise = this.#filterIdPromise;\n        if (filterIdPromise) {\n            this.#filterIdPromise = null;\n            filterIdPromise.then((filterId) => {\n                if (this.#provider.destroyed) { return; }\n                this.#provider.send(\"eth_uninstallFilter\", [ filterId ]);\n            });\n        }\n    }\n\n    start(): void {\n        if (this.#running) { return; }\n        this.#running = true;\n\n        this.#poll(-2);\n    }\n\n    stop(): void {\n        if (!this.#running) { return; }\n        this.#running = false;\n\n        this.#hault = true;\n        this.#teardown();\n        this.#provider.off(\"block\", this.#poller);\n    }\n\n    pause(dropWhilePaused?: boolean): void {\n        if (dropWhilePaused){ this.#teardown(); }\n        this.#provider.off(\"block\", this.#poller);\n    }\n\n    resume(): void { this.start(); }\n}\n\n/**\n *  A **FilterIdSubscriber** for receiving contract events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdEventSubscriber extends FilterIdSubscriber {\n    #event: EventFilter;\n\n    /**\n     *  Creates a new **FilterIdEventSubscriber** attached to %%provider%%\n     *  listening for %%filter%%.\n     */\n    constructor(provider: JsonRpcApiProvider, filter: EventFilter) {\n        super(provider);\n        this.#event = copy(filter);\n    }\n\n    _recover(provider: AbstractProvider): Subscriber {\n        return new PollingEventSubscriber(provider, this.#event);\n    }\n\n    async _subscribe(provider: JsonRpcApiProvider): Promise<string> {\n        const filterId = await provider.send(\"eth_newFilter\", [ this.#event ]);\n        return filterId;\n    }\n\n    async _emitResults(provider: JsonRpcApiProvider, results: Array<any>): Promise<void> {\n        for (const result of results) {\n            provider.emit(this.#event, provider._wrapLog(result, provider._network));\n        }\n    }\n}\n\n/**\n *  A **FilterIdSubscriber** for receiving pending transactions events.\n *\n *  @_docloc: api/providers/abstract-provider\n */\nexport class FilterIdPendingSubscriber extends FilterIdSubscriber {\n    async _subscribe(provider: JsonRpcApiProvider): Promise<string> {\n        return await provider.send(\"eth_newPendingTransactionFilter\", [ ]);\n    }\n\n    async _emitResults(provider: JsonRpcApiProvider, results: Array<any>): Promise<void> {\n        for (const result of results) {\n            provider.emit(\"pending\", result);\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB;AAE3C,SAASC,sBAAsB,QAAQ,yBAAyB;AAOhE,SAASC,IAAIA,CAACC,GAAQ;EAClB,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACH,GAAG,CAAC,CAAC;AAC1C;AAEA;;;;;;;;;;AAUA,OAAM,MAAOI,kBAAkB;EAC3B,CAAAC,QAAS;EAET,CAAAC,eAAgB;EAChB,CAAAC,MAAO;EAEP,CAAAC,OAAQ;EAER,CAAAC,OAAQ;EAER,CAAAC,KAAM;EAEN;;;;;EAKAC,YAAYN,QAA4B;IACpC,IAAI,CAAC,CAAAA,QAAS,GAAGA,QAAQ;IAEzB,IAAI,CAAC,CAAAC,eAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC,CAAAC,MAAO,GAAG,IAAI,CAAC,CAAAK,IAAK,CAACC,IAAI,CAAC,IAAI,CAAC;IAEpC,IAAI,CAAC,CAAAL,OAAQ,GAAG,KAAK;IAErB,IAAI,CAAC,CAAAC,OAAQ,GAAG,IAAI;IAEpB,IAAI,CAAC,CAAAC,KAAM,GAAG,KAAK;EACvB;EAEA;;;EAGAI,UAAUA,CAACT,QAA4B;IACnC,MAAM,IAAIU,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEA;;;EAGAC,YAAYA,CAACX,QAA0B,EAAEY,MAAkB;IACvD,MAAM,IAAIF,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEA;;;EAGAG,QAAQA,CAACb,QAA0B;IAC/B,MAAM,IAAIU,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEA,MAAM,CAAAH,IAAKO,CAACC,WAAmB;IAC3B,IAAI;MACA;MACA,IAAI,IAAI,CAAC,CAAAd,eAAgB,IAAI,IAAI,EAAE;QAC/B,IAAI,CAAC,CAAAA,eAAgB,GAAG,IAAI,CAACQ,UAAU,CAAC,IAAI,CAAC,CAAAT,QAAS,CAAC;;MAG3D;MACA,IAAIgB,QAAQ,GAAkB,IAAI;MAClC,IAAI;QACAA,QAAQ,GAAG,MAAM,IAAI,CAAC,CAAAf,eAAgB;OACzC,CAAC,OAAOgB,KAAK,EAAE;QACZ,IAAI,CAACzB,OAAO,CAACyB,KAAK,EAAE,uBAAuB,CAAC,IAAIA,KAAK,CAACC,SAAS,KAAK,eAAe,EAAE;UACjF,MAAMD,KAAK;;;MAInB;MACA;MACA,IAAID,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAAC,CAAAf,eAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC,CAAAD,QAAS,CAACmB,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAACN,QAAQ,CAAC,IAAI,CAAC,CAAAb,QAAS,CAAC,CAAC;QACtE;;MAGJ,MAAMI,OAAO,GAAG,MAAM,IAAI,CAAC,CAAAJ,QAAS,CAACoB,UAAU,EAAE;MACjD,IAAI,CAAC,IAAI,CAAC,CAAAhB,OAAQ,EAAE;QAAE,IAAI,CAAC,CAAAA,OAAQ,GAAGA,OAAO;;MAE7C,IAAK,IAAI,CAAC,CAAAA,OAAoB,CAACiB,OAAO,KAAKjB,OAAO,CAACiB,OAAO,EAAE;QACxD,MAAM,IAAIX,KAAK,CAAC,eAAe,CAAC;;MAGpC,IAAI,IAAI,CAAC,CAAAL,KAAM,EAAE;QAAE;;MAEnB,MAAMO,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAZ,QAAS,CAACsB,IAAI,CAAC,sBAAsB,EAAE,CAAEN,QAAQ,CAAE,CAAC;MAC9E,MAAM,IAAI,CAACL,YAAY,CAAC,IAAI,CAAC,CAAAX,QAAS,EAAEY,MAAM,CAAC;KAClD,CAAC,OAAOK,KAAK,EAAE;MAAEM,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEP,KAAK,CAAC;;IAE7C,IAAI,CAAC,CAAAjB,QAAS,CAACyB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAAvB,MAAO,CAAC;EAC9C;EAEA,CAAAwB,QAASC,CAAA;IACL,MAAM1B,eAAe,GAAG,IAAI,CAAC,CAAAA,eAAgB;IAC7C,IAAIA,eAAe,EAAE;MACjB,IAAI,CAAC,CAAAA,eAAgB,GAAG,IAAI;MAC5BA,eAAe,CAAC2B,IAAI,CAAEZ,QAAQ,IAAI;QAC9B,IAAI,IAAI,CAAC,CAAAhB,QAAS,CAAC6B,SAAS,EAAE;UAAE;;QAChC,IAAI,CAAC,CAAA7B,QAAS,CAACsB,IAAI,CAAC,qBAAqB,EAAE,CAAEN,QAAQ,CAAE,CAAC;MAC5D,CAAC,CAAC;;EAEV;EAEAc,KAAKA,CAAA;IACD,IAAI,IAAI,CAAC,CAAA3B,OAAQ,EAAE;MAAE;;IACrB,IAAI,CAAC,CAAAA,OAAQ,GAAG,IAAI;IAEpB,IAAI,CAAC,CAAAI,IAAK,CAAC,CAAC,CAAC,CAAC;EAClB;EAEAwB,IAAIA,CAAA;IACA,IAAI,CAAC,IAAI,CAAC,CAAA5B,OAAQ,EAAE;MAAE;;IACtB,IAAI,CAAC,CAAAA,OAAQ,GAAG,KAAK;IAErB,IAAI,CAAC,CAAAE,KAAM,GAAG,IAAI;IAClB,IAAI,CAAC,CAAAqB,QAAS,EAAE;IAChB,IAAI,CAAC,CAAA1B,QAAS,CAACgC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA9B,MAAO,CAAC;EAC7C;EAEA+B,KAAKA,CAACC,eAAyB;IAC3B,IAAIA,eAAe,EAAC;MAAE,IAAI,CAAC,CAAAR,QAAS,EAAE;;IACtC,IAAI,CAAC,CAAA1B,QAAS,CAACgC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA9B,MAAO,CAAC;EAC7C;EAEAiC,MAAMA,CAAA;IAAW,IAAI,CAACL,KAAK,EAAE;EAAE;;AAGnC;;;;;AAKA,OAAM,MAAOM,uBAAwB,SAAQrC,kBAAkB;EAC3D,CAAAsC,KAAM;EAEN;;;;EAIA/B,YAAYN,QAA4B,EAAEsC,MAAmB;IACzD,KAAK,CAACtC,QAAQ,CAAC;IACf,IAAI,CAAC,CAAAqC,KAAM,GAAG3C,IAAI,CAAC4C,MAAM,CAAC;EAC9B;EAEAzB,QAAQA,CAACb,QAA0B;IAC/B,OAAO,IAAIP,sBAAsB,CAACO,QAAQ,EAAE,IAAI,CAAC,CAAAqC,KAAM,CAAC;EAC5D;EAEA,MAAM5B,UAAUA,CAACT,QAA4B;IACzC,MAAMgB,QAAQ,GAAG,MAAMhB,QAAQ,CAACsB,IAAI,CAAC,eAAe,EAAE,CAAE,IAAI,CAAC,CAAAe,KAAM,CAAE,CAAC;IACtE,OAAOrB,QAAQ;EACnB;EAEA,MAAML,YAAYA,CAACX,QAA4B,EAAEuC,OAAmB;IAChE,KAAK,MAAM3B,MAAM,IAAI2B,OAAO,EAAE;MAC1BvC,QAAQ,CAACwC,IAAI,CAAC,IAAI,CAAC,CAAAH,KAAM,EAAErC,QAAQ,CAACyC,QAAQ,CAAC7B,MAAM,EAAEZ,QAAQ,CAAC0C,QAAQ,CAAC,CAAC;;EAEhF;;AAGJ;;;;;AAKA,OAAM,MAAOC,yBAA0B,SAAQ5C,kBAAkB;EAC7D,MAAMU,UAAUA,CAACT,QAA4B;IACzC,OAAO,MAAMA,QAAQ,CAACsB,IAAI,CAAC,iCAAiC,EAAE,EAAG,CAAC;EACtE;EAEA,MAAMX,YAAYA,CAACX,QAA4B,EAAEuC,OAAmB;IAChE,KAAK,MAAM3B,MAAM,IAAI2B,OAAO,EAAE;MAC1BvC,QAAQ,CAACwC,IAAI,CAAC,SAAS,EAAE5B,MAAM,CAAC;;EAExC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}