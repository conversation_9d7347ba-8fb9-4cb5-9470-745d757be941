import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
  TrophyIcon, 
  ChartBarIcon,
  ShieldCheckIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Leaderboard = () => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState('rank');

  useEffect(() => {
    loadLeaderboard();
  }, [selectedType]);

  const loadLeaderboard = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/users/leaderboard?type=${selectedType}&limit=50`);
      setLeaderboard(response.data.leaderboard);
    } catch (error) {
      console.error('Failed to load leaderboard:', error);
    } finally {
      setLoading(false);
    }
  };

  const leaderboardTypes = [
    { key: 'rank', label: 'Rank Points', icon: TrophyIcon },
    { key: 'level', label: 'Level', icon: ChartBarIcon },
    { key: 'wins', label: 'Wins', icon: ShieldCheckIcon },
  ];

  const getRankIcon = (rank) => {
    if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    return `#${rank}`;
  };

  const getRankColor = (rank) => {
    if (rank === 1) return 'text-yellow-400';
    if (rank === 2) return 'text-gray-300';
    if (rank === 3) return 'text-orange-400';
    return 'text-dark-400';
  };

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">Leaderboard</h1>
        <p className="text-dark-300">
          See how you rank against other players
        </p>
      </div>

      {/* Leaderboard Type Selection */}
      <div className="flex justify-center">
        <div className="flex space-x-2 bg-dark-800 p-2 rounded-lg">
          {leaderboardTypes.map((type) => (
            <button
              key={type.key}
              onClick={() => setSelectedType(type.key)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all ${
                selectedType === type.key
                  ? 'bg-primary-600 text-white'
                  : 'text-dark-300 hover:text-white hover:bg-dark-700'
              }`}
            >
              <type.icon className="w-4 h-4" />
              <span>{type.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Leaderboard */}
      <div className="game-card">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="loading-spinner mx-auto mb-4"></div>
              <p className="text-dark-300">Loading leaderboard...</p>
            </div>
          </div>
        ) : leaderboard.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-dark-700">
                  <th className="text-left py-4 px-6 text-dark-300 font-medium">Rank</th>
                  <th className="text-left py-4 px-6 text-dark-300 font-medium">Player</th>
                  <th className="text-center py-4 px-6 text-dark-300 font-medium">Level</th>
                  <th className="text-center py-4 px-6 text-dark-300 font-medium">
                    {selectedType === 'rank' && 'Rank Points'}
                    {selectedType === 'level' && 'XP'}
                    {selectedType === 'wins' && 'Wins'}
                  </th>
                  <th className="text-center py-4 px-6 text-dark-300 font-medium">Win Rate</th>
                  <th className="text-center py-4 px-6 text-dark-300 font-medium">Battles</th>
                </tr>
              </thead>
              <tbody>
                {leaderboard.map((player, index) => (
                  <tr
                    key={player.walletAddress}
                    className={`border-b border-dark-700/50 hover:bg-dark-800/50 ${
                      player.rank <= 3 ? 'bg-gradient-to-r from-transparent to-primary-900/10' : ''
                    }`}
                  >
                    <td className="py-4 px-6">
                      <div className={`text-2xl font-bold ${getRankColor(player.rank)}`}>
                        {getRankIcon(player.rank)}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-white">
                          {player.username || `${player.walletAddress.slice(0, 6)}...${player.walletAddress.slice(-4)}`}
                        </div>
                        <div className="text-sm text-dark-400">
                          {player.walletAddress.slice(0, 10)}...
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <StarIcon className="w-4 h-4 text-yellow-400" />
                        <span className="text-white font-medium">{player.level}</span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <span className="text-primary-400 font-bold">
                        {selectedType === 'rank' && player.rankPoints}
                        {selectedType === 'level' && player.xp}
                        {selectedType === 'wins' && player.wins}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <span className={`font-medium ${
                        player.winRate >= 70 ? 'text-green-400' :
                        player.winRate >= 50 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {player.winRate}%
                      </span>
                    </td>
                    <td className="py-4 px-6 text-center">
                      <span className="text-dark-300">{player.totalBattles}</span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <TrophyIcon className="w-16 h-16 text-dark-400 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-white mb-2">No Players Yet</h2>
            <p className="text-dark-400">
              Be the first to appear on the leaderboard!
            </p>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="game-card p-4">
        <h3 className="text-lg font-semibold text-white mb-3">How Rankings Work</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-primary-400 mb-1">Rank Points</h4>
            <p className="text-dark-300">
              Earned through PvP victories and tournament performance. Lost through defeats.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-green-400 mb-1">Level</h4>
            <p className="text-dark-300">
              Based on total XP gained from battles, quests, and achievements.
            </p>
          </div>
          <div>
            <h4 className="font-medium text-yellow-400 mb-1">Win Rate</h4>
            <p className="text-dark-300">
              Percentage of battles won out of total battles fought.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;
