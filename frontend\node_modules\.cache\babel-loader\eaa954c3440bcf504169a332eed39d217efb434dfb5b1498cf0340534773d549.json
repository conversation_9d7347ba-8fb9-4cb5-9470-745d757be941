{"ast": null, "code": "import { number as assertNumber } from './_assert.js';\nimport { sha256 } from './sha256.js';\nimport { pbkdf2 } from './pbkdf2.js';\nimport { asyncLoop, checkOpts, u32 } from './utils.js';\n// RFC 7914 Scrypt KDF\n// Left rotate for uint32\nconst rotl = (a, b) => a << b | a >>> 32 - b;\n// The main Scrypt loop: uses Salsa extensively.\n// Six versions of the function were tried, this is the fastest one.\n// prettier-ignore\nfunction XorAndSalsa(prev, pi, input, ii, out, oi) {\n  // Based on https://cr.yp.to/salsa20.html\n  // Xor blocks\n  let y00 = prev[pi++] ^ input[ii++],\n    y01 = prev[pi++] ^ input[ii++];\n  let y02 = prev[pi++] ^ input[ii++],\n    y03 = prev[pi++] ^ input[ii++];\n  let y04 = prev[pi++] ^ input[ii++],\n    y05 = prev[pi++] ^ input[ii++];\n  let y06 = prev[pi++] ^ input[ii++],\n    y07 = prev[pi++] ^ input[ii++];\n  let y08 = prev[pi++] ^ input[ii++],\n    y09 = prev[pi++] ^ input[ii++];\n  let y10 = prev[pi++] ^ input[ii++],\n    y11 = prev[pi++] ^ input[ii++];\n  let y12 = prev[pi++] ^ input[ii++],\n    y13 = prev[pi++] ^ input[ii++];\n  let y14 = prev[pi++] ^ input[ii++],\n    y15 = prev[pi++] ^ input[ii++];\n  // Save state to temporary variables (salsa)\n  let x00 = y00,\n    x01 = y01,\n    x02 = y02,\n    x03 = y03,\n    x04 = y04,\n    x05 = y05,\n    x06 = y06,\n    x07 = y07,\n    x08 = y08,\n    x09 = y09,\n    x10 = y10,\n    x11 = y11,\n    x12 = y12,\n    x13 = y13,\n    x14 = y14,\n    x15 = y15;\n  // Main loop (salsa)\n  for (let i = 0; i < 8; i += 2) {\n    x04 ^= rotl(x00 + x12 | 0, 7);\n    x08 ^= rotl(x04 + x00 | 0, 9);\n    x12 ^= rotl(x08 + x04 | 0, 13);\n    x00 ^= rotl(x12 + x08 | 0, 18);\n    x09 ^= rotl(x05 + x01 | 0, 7);\n    x13 ^= rotl(x09 + x05 | 0, 9);\n    x01 ^= rotl(x13 + x09 | 0, 13);\n    x05 ^= rotl(x01 + x13 | 0, 18);\n    x14 ^= rotl(x10 + x06 | 0, 7);\n    x02 ^= rotl(x14 + x10 | 0, 9);\n    x06 ^= rotl(x02 + x14 | 0, 13);\n    x10 ^= rotl(x06 + x02 | 0, 18);\n    x03 ^= rotl(x15 + x11 | 0, 7);\n    x07 ^= rotl(x03 + x15 | 0, 9);\n    x11 ^= rotl(x07 + x03 | 0, 13);\n    x15 ^= rotl(x11 + x07 | 0, 18);\n    x01 ^= rotl(x00 + x03 | 0, 7);\n    x02 ^= rotl(x01 + x00 | 0, 9);\n    x03 ^= rotl(x02 + x01 | 0, 13);\n    x00 ^= rotl(x03 + x02 | 0, 18);\n    x06 ^= rotl(x05 + x04 | 0, 7);\n    x07 ^= rotl(x06 + x05 | 0, 9);\n    x04 ^= rotl(x07 + x06 | 0, 13);\n    x05 ^= rotl(x04 + x07 | 0, 18);\n    x11 ^= rotl(x10 + x09 | 0, 7);\n    x08 ^= rotl(x11 + x10 | 0, 9);\n    x09 ^= rotl(x08 + x11 | 0, 13);\n    x10 ^= rotl(x09 + x08 | 0, 18);\n    x12 ^= rotl(x15 + x14 | 0, 7);\n    x13 ^= rotl(x12 + x15 | 0, 9);\n    x14 ^= rotl(x13 + x12 | 0, 13);\n    x15 ^= rotl(x14 + x13 | 0, 18);\n  }\n  // Write output (salsa)\n  out[oi++] = y00 + x00 | 0;\n  out[oi++] = y01 + x01 | 0;\n  out[oi++] = y02 + x02 | 0;\n  out[oi++] = y03 + x03 | 0;\n  out[oi++] = y04 + x04 | 0;\n  out[oi++] = y05 + x05 | 0;\n  out[oi++] = y06 + x06 | 0;\n  out[oi++] = y07 + x07 | 0;\n  out[oi++] = y08 + x08 | 0;\n  out[oi++] = y09 + x09 | 0;\n  out[oi++] = y10 + x10 | 0;\n  out[oi++] = y11 + x11 | 0;\n  out[oi++] = y12 + x12 | 0;\n  out[oi++] = y13 + x13 | 0;\n  out[oi++] = y14 + x14 | 0;\n  out[oi++] = y15 + x15 | 0;\n}\nfunction BlockMix(input, ii, out, oi, r) {\n  // The block B is r 128-byte chunks (which is equivalent of 2r 64-byte chunks)\n  let head = oi + 0;\n  let tail = oi + 16 * r;\n  for (let i = 0; i < 16; i++) out[tail + i] = input[ii + (2 * r - 1) * 16 + i]; // X ← B[2r−1]\n  for (let i = 0; i < r; i++, head += 16, ii += 16) {\n    // We write odd & even Yi at same time. Even: 0bXXXXX0 Odd:  0bXXXXX1\n    XorAndSalsa(out, tail, input, ii, out, head); // head[i] = Salsa(blockIn[2*i] ^ tail[i-1])\n    if (i > 0) tail += 16; // First iteration overwrites tmp value in tail\n    XorAndSalsa(out, head, input, ii += 16, out, tail); // tail[i] = Salsa(blockIn[2*i+1] ^ head[i])\n  }\n}\n// Common prologue and epilogue for sync/async functions\nfunction scryptInit(password, salt, _opts) {\n  // Maxmem - 1GB+1KB by default\n  const opts = checkOpts({\n    dkLen: 32,\n    asyncTick: 10,\n    maxmem: 1024 ** 3 + 1024\n  }, _opts);\n  const {\n    N,\n    r,\n    p,\n    dkLen,\n    asyncTick,\n    maxmem,\n    onProgress\n  } = opts;\n  assertNumber(N);\n  assertNumber(r);\n  assertNumber(p);\n  assertNumber(dkLen);\n  assertNumber(asyncTick);\n  assertNumber(maxmem);\n  if (onProgress !== undefined && typeof onProgress !== 'function') throw new Error('progressCb should be function');\n  const blockSize = 128 * r;\n  const blockSize32 = blockSize / 4;\n  if (N <= 1 || (N & N - 1) !== 0 || N >= 2 ** (blockSize / 8) || N > 2 ** 32) {\n    // NOTE: we limit N to be less than 2**32 because of 32 bit variant of Integrify function\n    // There is no JS engines that allows alocate more than 4GB per single Uint8Array for now, but can change in future.\n    throw new Error('Scrypt: N must be larger than 1, a power of 2, less than 2^(128 * r / 8) and less than 2^32');\n  }\n  if (p < 0 || p > (2 ** 32 - 1) * 32 / blockSize) {\n    throw new Error('Scrypt: p must be a positive integer less than or equal to ((2^32 - 1) * 32) / (128 * r)');\n  }\n  if (dkLen < 0 || dkLen > (2 ** 32 - 1) * 32) {\n    throw new Error('Scrypt: dkLen should be positive integer less than or equal to (2^32 - 1) * 32');\n  }\n  const memUsed = blockSize * (N + p);\n  if (memUsed > maxmem) {\n    throw new Error(`Scrypt: parameters too large, ${memUsed} (128 * r * (N + p)) > ${maxmem} (maxmem)`);\n  }\n  // [B0...Bp−1] ← PBKDF2HMAC-SHA256(Passphrase, Salt, 1, blockSize*ParallelizationFactor)\n  // Since it has only one iteration there is no reason to use async variant\n  const B = pbkdf2(sha256, password, salt, {\n    c: 1,\n    dkLen: blockSize * p\n  });\n  const B32 = u32(B);\n  // Re-used between parallel iterations. Array(iterations) of B\n  const V = u32(new Uint8Array(blockSize * N));\n  const tmp = u32(new Uint8Array(blockSize));\n  let blockMixCb = () => {};\n  if (onProgress) {\n    const totalBlockMix = 2 * N * p;\n    // Invoke callback if progress changes from 10.01 to 10.02\n    // Allows to draw smooth progress bar on up to 8K screen\n    const callbackPer = Math.max(Math.floor(totalBlockMix / 10000), 1);\n    let blockMixCnt = 0;\n    blockMixCb = () => {\n      blockMixCnt++;\n      if (onProgress && (!(blockMixCnt % callbackPer) || blockMixCnt === totalBlockMix)) onProgress(blockMixCnt / totalBlockMix);\n    };\n  }\n  return {\n    N,\n    r,\n    p,\n    dkLen,\n    blockSize32,\n    V,\n    B32,\n    B,\n    tmp,\n    blockMixCb,\n    asyncTick\n  };\n}\nfunction scryptOutput(password, dkLen, B, V, tmp) {\n  const res = pbkdf2(sha256, password, B, {\n    c: 1,\n    dkLen\n  });\n  B.fill(0);\n  V.fill(0);\n  tmp.fill(0);\n  return res;\n}\n/**\n * Scrypt KDF from RFC 7914.\n * @param password - pass\n * @param salt - salt\n * @param opts - parameters\n * - `N` is cpu/mem work factor (power of 2 e.g. 2**18)\n * - `r` is block size (8 is common), fine-tunes sequential memory read size and performance\n * - `p` is parallelization factor (1 is common)\n * - `dkLen` is output key length in bytes e.g. 32.\n * - `asyncTick` - (default: 10) max time in ms for which async function can block execution\n * - `maxmem` - (default: `1024 ** 3 + 1024` aka 1GB+1KB). A limit that the app could use for scrypt\n * - `onProgress` - callback function that would be executed for progress report\n * @returns Derived key\n */\nexport function scrypt(password, salt, opts) {\n  const {\n    N,\n    r,\n    p,\n    dkLen,\n    blockSize32,\n    V,\n    B32,\n    B,\n    tmp,\n    blockMixCb\n  } = scryptInit(password, salt, opts);\n  for (let pi = 0; pi < p; pi++) {\n    const Pi = blockSize32 * pi;\n    for (let i = 0; i < blockSize32; i++) V[i] = B32[Pi + i]; // V[0] = B[i]\n    for (let i = 0, pos = 0; i < N - 1; i++) {\n      BlockMix(V, pos, V, pos += blockSize32, r); // V[i] = BlockMix(V[i-1]);\n      blockMixCb();\n    }\n    BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n    blockMixCb();\n    for (let i = 0; i < N; i++) {\n      // First u32 of the last 64-byte block (u32 is LE)\n      const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n      for (let k = 0; k < blockSize32; k++) tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n      BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n      blockMixCb();\n    }\n  }\n  return scryptOutput(password, dkLen, B, V, tmp);\n}\n/**\n * Scrypt KDF from RFC 7914.\n */\nexport async function scryptAsync(password, salt, opts) {\n  const {\n    N,\n    r,\n    p,\n    dkLen,\n    blockSize32,\n    V,\n    B32,\n    B,\n    tmp,\n    blockMixCb,\n    asyncTick\n  } = scryptInit(password, salt, opts);\n  for (let pi = 0; pi < p; pi++) {\n    const Pi = blockSize32 * pi;\n    for (let i = 0; i < blockSize32; i++) V[i] = B32[Pi + i]; // V[0] = B[i]\n    let pos = 0;\n    await asyncLoop(N - 1, asyncTick, () => {\n      BlockMix(V, pos, V, pos += blockSize32, r); // V[i] = BlockMix(V[i-1]);\n      blockMixCb();\n    });\n    BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n    blockMixCb();\n    await asyncLoop(N, asyncTick, () => {\n      // First u32 of the last 64-byte block (u32 is LE)\n      const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n      for (let k = 0; k < blockSize32; k++) tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n      BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n      blockMixCb();\n    });\n  }\n  return scryptOutput(password, dkLen, B, V, tmp);\n}", "map": {"version": 3, "names": ["number", "assertNumber", "sha256", "pbkdf2", "asyncLoop", "checkOpts", "u32", "rotl", "a", "b", "XorAndSalsa", "prev", "pi", "input", "ii", "out", "oi", "y00", "y01", "y02", "y03", "y04", "y05", "y06", "y07", "y08", "y09", "y10", "y11", "y12", "y13", "y14", "y15", "x00", "x01", "x02", "x03", "x04", "x05", "x06", "x07", "x08", "x09", "x10", "x11", "x12", "x13", "x14", "x15", "i", "BlockMix", "r", "head", "tail", "scryptInit", "password", "salt", "_opts", "opts", "dkLen", "asyncTick", "maxmem", "N", "p", "onProgress", "undefined", "Error", "blockSize", "blockSize32", "memUsed", "B", "c", "B32", "V", "Uint8Array", "tmp", "blockMixCb", "totalBlockMix", "callback<PERSON><PERSON>", "Math", "max", "floor", "blockMixCnt", "scryptOutput", "res", "fill", "scrypt", "Pi", "pos", "j", "k", "scryptAsync"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\scrypt.ts"], "sourcesContent": ["import { number as assertNumber } from './_assert.js';\nimport { sha256 } from './sha256.js';\nimport { pbkdf2 } from './pbkdf2.js';\nimport { asyncLoop, checkOpts, Input, u32 } from './utils.js';\n\n// RFC 7914 Scrypt KDF\n\n// Left rotate for uint32\nconst rotl = (a: number, b: number) => (a << b) | (a >>> (32 - b));\n\n// The main Scrypt loop: uses Salsa extensively.\n// Six versions of the function were tried, this is the fastest one.\n// prettier-ignore\nfunction XorAndSalsa(\n  prev: Uint32Array,\n  pi: number,\n  input: Uint32Array,\n  ii: number,\n  out: Uint32Array,\n  oi: number\n) {\n  // Based on https://cr.yp.to/salsa20.html\n  // Xor blocks\n  let y00 = prev[pi++] ^ input[ii++], y01 = prev[pi++] ^ input[ii++];\n  let y02 = prev[pi++] ^ input[ii++], y03 = prev[pi++] ^ input[ii++];\n  let y04 = prev[pi++] ^ input[ii++], y05 = prev[pi++] ^ input[ii++];\n  let y06 = prev[pi++] ^ input[ii++], y07 = prev[pi++] ^ input[ii++];\n  let y08 = prev[pi++] ^ input[ii++], y09 = prev[pi++] ^ input[ii++];\n  let y10 = prev[pi++] ^ input[ii++], y11 = prev[pi++] ^ input[ii++];\n  let y12 = prev[pi++] ^ input[ii++], y13 = prev[pi++] ^ input[ii++];\n  let y14 = prev[pi++] ^ input[ii++], y15 = prev[pi++] ^ input[ii++];\n  // Save state to temporary variables (salsa)\n  let x00 = y00, x01 = y01, x02 = y02, x03 = y03,\n      x04 = y04, x05 = y05, x06 = y06, x07 = y07,\n      x08 = y08, x09 = y09, x10 = y10, x11 = y11,\n      x12 = y12, x13 = y13, x14 = y14, x15 = y15;\n  // Main loop (salsa)\n  for (let i = 0; i < 8; i += 2) {\n    x04 ^= rotl(x00 + x12 | 0,  7); x08 ^= rotl(x04 + x00 | 0,  9);\n    x12 ^= rotl(x08 + x04 | 0, 13); x00 ^= rotl(x12 + x08 | 0, 18);\n    x09 ^= rotl(x05 + x01 | 0,  7); x13 ^= rotl(x09 + x05 | 0,  9);\n    x01 ^= rotl(x13 + x09 | 0, 13); x05 ^= rotl(x01 + x13 | 0, 18);\n    x14 ^= rotl(x10 + x06 | 0,  7); x02 ^= rotl(x14 + x10 | 0,  9);\n    x06 ^= rotl(x02 + x14 | 0, 13); x10 ^= rotl(x06 + x02 | 0, 18);\n    x03 ^= rotl(x15 + x11 | 0,  7); x07 ^= rotl(x03 + x15 | 0,  9);\n    x11 ^= rotl(x07 + x03 | 0, 13); x15 ^= rotl(x11 + x07 | 0, 18);\n    x01 ^= rotl(x00 + x03 | 0,  7); x02 ^= rotl(x01 + x00 | 0,  9);\n    x03 ^= rotl(x02 + x01 | 0, 13); x00 ^= rotl(x03 + x02 | 0, 18);\n    x06 ^= rotl(x05 + x04 | 0,  7); x07 ^= rotl(x06 + x05 | 0,  9);\n    x04 ^= rotl(x07 + x06 | 0, 13); x05 ^= rotl(x04 + x07 | 0, 18);\n    x11 ^= rotl(x10 + x09 | 0,  7); x08 ^= rotl(x11 + x10 | 0,  9);\n    x09 ^= rotl(x08 + x11 | 0, 13); x10 ^= rotl(x09 + x08 | 0, 18);\n    x12 ^= rotl(x15 + x14 | 0,  7); x13 ^= rotl(x12 + x15 | 0,  9);\n    x14 ^= rotl(x13 + x12 | 0, 13); x15 ^= rotl(x14 + x13 | 0, 18);\n  }\n  // Write output (salsa)\n  out[oi++] = (y00 + x00) | 0; out[oi++] = (y01 + x01) | 0;\n  out[oi++] = (y02 + x02) | 0; out[oi++] = (y03 + x03) | 0;\n  out[oi++] = (y04 + x04) | 0; out[oi++] = (y05 + x05) | 0;\n  out[oi++] = (y06 + x06) | 0; out[oi++] = (y07 + x07) | 0;\n  out[oi++] = (y08 + x08) | 0; out[oi++] = (y09 + x09) | 0;\n  out[oi++] = (y10 + x10) | 0; out[oi++] = (y11 + x11) | 0;\n  out[oi++] = (y12 + x12) | 0; out[oi++] = (y13 + x13) | 0;\n  out[oi++] = (y14 + x14) | 0; out[oi++] = (y15 + x15) | 0;\n}\n\nfunction BlockMix(input: Uint32Array, ii: number, out: Uint32Array, oi: number, r: number) {\n  // The block B is r 128-byte chunks (which is equivalent of 2r 64-byte chunks)\n  let head = oi + 0;\n  let tail = oi + 16 * r;\n  for (let i = 0; i < 16; i++) out[tail + i] = input[ii + (2 * r - 1) * 16 + i]; // X ← B[2r−1]\n  for (let i = 0; i < r; i++, head += 16, ii += 16) {\n    // We write odd & even Yi at same time. Even: 0bXXXXX0 Odd:  0bXXXXX1\n    XorAndSalsa(out, tail, input, ii, out, head); // head[i] = Salsa(blockIn[2*i] ^ tail[i-1])\n    if (i > 0) tail += 16; // First iteration overwrites tmp value in tail\n    XorAndSalsa(out, head, input, (ii += 16), out, tail); // tail[i] = Salsa(blockIn[2*i+1] ^ head[i])\n  }\n}\n\nexport type ScryptOpts = {\n  N: number; // cost factor\n  r: number; // block size\n  p: number; // parallelization\n  dkLen?: number; // key length\n  asyncTick?: number; // block execution max time\n  maxmem?: number;\n  onProgress?: (progress: number) => void;\n};\n\n// Common prologue and epilogue for sync/async functions\nfunction scryptInit(password: Input, salt: Input, _opts?: ScryptOpts) {\n  // Maxmem - 1GB+1KB by default\n  const opts = checkOpts(\n    {\n      dkLen: 32,\n      asyncTick: 10,\n      maxmem: 1024 ** 3 + 1024,\n    },\n    _opts\n  );\n  const { N, r, p, dkLen, asyncTick, maxmem, onProgress } = opts;\n  assertNumber(N);\n  assertNumber(r);\n  assertNumber(p);\n  assertNumber(dkLen);\n  assertNumber(asyncTick);\n  assertNumber(maxmem);\n  if (onProgress !== undefined && typeof onProgress !== 'function')\n    throw new Error('progressCb should be function');\n  const blockSize = 128 * r;\n  const blockSize32 = blockSize / 4;\n  if (N <= 1 || (N & (N - 1)) !== 0 || N >= 2 ** (blockSize / 8) || N > 2 ** 32) {\n    // NOTE: we limit N to be less than 2**32 because of 32 bit variant of Integrify function\n    // There is no JS engines that allows alocate more than 4GB per single Uint8Array for now, but can change in future.\n    throw new Error(\n      'Scrypt: N must be larger than 1, a power of 2, less than 2^(128 * r / 8) and less than 2^32'\n    );\n  }\n  if (p < 0 || p > ((2 ** 32 - 1) * 32) / blockSize) {\n    throw new Error(\n      'Scrypt: p must be a positive integer less than or equal to ((2^32 - 1) * 32) / (128 * r)'\n    );\n  }\n  if (dkLen < 0 || dkLen > (2 ** 32 - 1) * 32) {\n    throw new Error(\n      'Scrypt: dkLen should be positive integer less than or equal to (2^32 - 1) * 32'\n    );\n  }\n  const memUsed = blockSize * (N + p);\n  if (memUsed > maxmem) {\n    throw new Error(\n      `Scrypt: parameters too large, ${memUsed} (128 * r * (N + p)) > ${maxmem} (maxmem)`\n    );\n  }\n  // [B0...Bp−1] ← PBKDF2HMAC-SHA256(Passphrase, Salt, 1, blockSize*ParallelizationFactor)\n  // Since it has only one iteration there is no reason to use async variant\n  const B = pbkdf2(sha256, password, salt, { c: 1, dkLen: blockSize * p });\n  const B32 = u32(B);\n  // Re-used between parallel iterations. Array(iterations) of B\n  const V = u32(new Uint8Array(blockSize * N));\n  const tmp = u32(new Uint8Array(blockSize));\n  let blockMixCb = () => {};\n  if (onProgress) {\n    const totalBlockMix = 2 * N * p;\n    // Invoke callback if progress changes from 10.01 to 10.02\n    // Allows to draw smooth progress bar on up to 8K screen\n    const callbackPer = Math.max(Math.floor(totalBlockMix / 10000), 1);\n    let blockMixCnt = 0;\n    blockMixCb = () => {\n      blockMixCnt++;\n      if (onProgress && (!(blockMixCnt % callbackPer) || blockMixCnt === totalBlockMix))\n        onProgress(blockMixCnt / totalBlockMix);\n    };\n  }\n  return { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb, asyncTick };\n}\n\nfunction scryptOutput(\n  password: Input,\n  dkLen: number,\n  B: Uint8Array,\n  V: Uint32Array,\n  tmp: Uint32Array\n) {\n  const res = pbkdf2(sha256, password, B, { c: 1, dkLen });\n  B.fill(0);\n  V.fill(0);\n  tmp.fill(0);\n  return res;\n}\n\n/**\n * Scrypt KDF from RFC 7914.\n * @param password - pass\n * @param salt - salt\n * @param opts - parameters\n * - `N` is cpu/mem work factor (power of 2 e.g. 2**18)\n * - `r` is block size (8 is common), fine-tunes sequential memory read size and performance\n * - `p` is parallelization factor (1 is common)\n * - `dkLen` is output key length in bytes e.g. 32.\n * - `asyncTick` - (default: 10) max time in ms for which async function can block execution\n * - `maxmem` - (default: `1024 ** 3 + 1024` aka 1GB+1KB). A limit that the app could use for scrypt\n * - `onProgress` - callback function that would be executed for progress report\n * @returns Derived key\n */\nexport function scrypt(password: Input, salt: Input, opts: ScryptOpts) {\n  const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb } = scryptInit(\n    password,\n    salt,\n    opts\n  );\n  for (let pi = 0; pi < p; pi++) {\n    const Pi = blockSize32 * pi;\n    for (let i = 0; i < blockSize32; i++) V[i] = B32[Pi + i]; // V[0] = B[i]\n    for (let i = 0, pos = 0; i < N - 1; i++) {\n      BlockMix(V, pos, V, (pos += blockSize32), r); // V[i] = BlockMix(V[i-1]);\n      blockMixCb();\n    }\n    BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n    blockMixCb();\n    for (let i = 0; i < N; i++) {\n      // First u32 of the last 64-byte block (u32 is LE)\n      const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n      for (let k = 0; k < blockSize32; k++) tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n      BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n      blockMixCb();\n    }\n  }\n  return scryptOutput(password, dkLen, B, V, tmp);\n}\n\n/**\n * Scrypt KDF from RFC 7914.\n */\nexport async function scryptAsync(password: Input, salt: Input, opts: ScryptOpts) {\n  const { N, r, p, dkLen, blockSize32, V, B32, B, tmp, blockMixCb, asyncTick } = scryptInit(\n    password,\n    salt,\n    opts\n  );\n  for (let pi = 0; pi < p; pi++) {\n    const Pi = blockSize32 * pi;\n    for (let i = 0; i < blockSize32; i++) V[i] = B32[Pi + i]; // V[0] = B[i]\n    let pos = 0;\n    await asyncLoop(N - 1, asyncTick, () => {\n      BlockMix(V, pos, V, (pos += blockSize32), r); // V[i] = BlockMix(V[i-1]);\n      blockMixCb();\n    });\n    BlockMix(V, (N - 1) * blockSize32, B32, Pi, r); // Process last element\n    blockMixCb();\n    await asyncLoop(N, asyncTick, () => {\n      // First u32 of the last 64-byte block (u32 is LE)\n      const j = B32[Pi + blockSize32 - 16] % N; // j = Integrify(X) % iterations\n      for (let k = 0; k < blockSize32; k++) tmp[k] = B32[Pi + k] ^ V[j * blockSize32 + k]; // tmp = B ^ V[j]\n      BlockMix(tmp, 0, B32, Pi, r); // B = BlockMix(B ^ V[j])\n      blockMixCb();\n    });\n  }\n  return scryptOutput(password, dkLen, B, V, tmp);\n}\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,YAAY,QAAQ,cAAc;AACrD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,SAAS,EAAEC,SAAS,EAASC,GAAG,QAAQ,YAAY;AAE7D;AAEA;AACA,MAAMC,IAAI,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAMD,CAAC,IAAIC,CAAC,GAAKD,CAAC,KAAM,EAAE,GAAGC,CAAG;AAElE;AACA;AACA;AACA,SAASC,WAAWA,CAClBC,IAAiB,EACjBC,EAAU,EACVC,KAAkB,EAClBC,EAAU,EACVC,GAAgB,EAChBC,EAAU;EAEV;EACA;EACA,IAAIC,GAAG,GAAGN,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEI,GAAG,GAAGP,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIK,GAAG,GAAGR,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEM,GAAG,GAAGT,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIO,GAAG,GAAGV,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEQ,GAAG,GAAGX,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIS,GAAG,GAAGZ,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEU,GAAG,GAAGb,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIW,GAAG,GAAGd,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEY,GAAG,GAAGf,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIa,GAAG,GAAGhB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEc,GAAG,GAAGjB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIe,GAAG,GAAGlB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEgB,GAAG,GAAGnB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE,IAAIiB,GAAG,GAAGpB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;IAAEkB,GAAG,GAAGrB,IAAI,CAACC,EAAE,EAAE,CAAC,GAAGC,KAAK,CAACC,EAAE,EAAE,CAAC;EAClE;EACA,IAAImB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAC1CiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAC1CiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAC1CiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;IAAEiB,GAAG,GAAGhB,GAAG;EAC9C;EACA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC7BZ,GAAG,IAAI9B,IAAI,CAAC0B,GAAG,GAAGY,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAEJ,GAAG,IAAIlC,IAAI,CAAC8B,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DY,GAAG,IAAItC,IAAI,CAACkC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAEJ,GAAG,IAAI1B,IAAI,CAACsC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DC,GAAG,IAAInC,IAAI,CAAC+B,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAEY,GAAG,IAAIvC,IAAI,CAACmC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DJ,GAAG,IAAI3B,IAAI,CAACuC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAEJ,GAAG,IAAI/B,IAAI,CAAC2B,GAAG,GAAGY,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DC,GAAG,IAAIxC,IAAI,CAACoC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAEJ,GAAG,IAAI5B,IAAI,CAACwC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DJ,GAAG,IAAIhC,IAAI,CAAC4B,GAAG,GAAGY,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAEJ,GAAG,IAAIpC,IAAI,CAACgC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DC,GAAG,IAAI7B,IAAI,CAACyC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAEJ,GAAG,IAAIjC,IAAI,CAAC6B,GAAG,GAAGY,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DJ,GAAG,IAAIrC,IAAI,CAACiC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAEY,GAAG,IAAIzC,IAAI,CAACqC,GAAG,GAAGJ,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DN,GAAG,IAAI3B,IAAI,CAAC0B,GAAG,GAAGG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAED,GAAG,IAAI5B,IAAI,CAAC2B,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DG,GAAG,IAAI7B,IAAI,CAAC4B,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAED,GAAG,IAAI1B,IAAI,CAAC6B,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DI,GAAG,IAAIhC,IAAI,CAAC+B,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAEG,GAAG,IAAIjC,IAAI,CAACgC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DD,GAAG,IAAI9B,IAAI,CAACiC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAED,GAAG,IAAI/B,IAAI,CAAC8B,GAAG,GAAGG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DI,GAAG,IAAIrC,IAAI,CAACoC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAED,GAAG,IAAIlC,IAAI,CAACqC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DD,GAAG,IAAInC,IAAI,CAACkC,GAAG,GAAGG,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAED,GAAG,IAAIpC,IAAI,CAACmC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAC9DI,GAAG,IAAItC,IAAI,CAACyC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAAED,GAAG,IAAIvC,IAAI,CAACsC,GAAG,GAAGG,GAAG,GAAG,CAAC,EAAG,CAAC,CAAC;IAC9DD,GAAG,IAAIxC,IAAI,CAACuC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;IAAEG,GAAG,IAAIzC,IAAI,CAACwC,GAAG,GAAGD,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;;EAEhE;EACA/B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIC,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAElB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIE,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxDnB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIG,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAEpB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAII,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxDrB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIK,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAEtB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIM,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxDvB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIO,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAExB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIQ,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxDzB,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIS,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAE1B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIU,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxD3B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIW,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAE5B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIY,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxD7B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIa,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAE9B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIc,GAAG,GAAGgB,GAAG,GAAI,CAAC;EACxD/B,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIe,GAAG,GAAGgB,GAAG,GAAI,CAAC;EAAEhC,GAAG,CAACC,EAAE,EAAE,CAAC,GAAIgB,GAAG,GAAGgB,GAAG,GAAI,CAAC;AAC1D;AAEA,SAASE,QAAQA,CAACrC,KAAkB,EAAEC,EAAU,EAAEC,GAAgB,EAAEC,EAAU,EAAEmC,CAAS;EACvF;EACA,IAAIC,IAAI,GAAGpC,EAAE,GAAG,CAAC;EACjB,IAAIqC,IAAI,GAAGrC,EAAE,GAAG,EAAE,GAAGmC,CAAC;EACtB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAElC,GAAG,CAACsC,IAAI,GAAGJ,CAAC,CAAC,GAAGpC,KAAK,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGqC,CAAC,GAAG,CAAC,IAAI,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;EAC/E,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,CAAC,EAAEF,CAAC,EAAE,EAAEG,IAAI,IAAI,EAAE,EAAEtC,EAAE,IAAI,EAAE,EAAE;IAChD;IACAJ,WAAW,CAACK,GAAG,EAAEsC,IAAI,EAAExC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAEqC,IAAI,CAAC,CAAC,CAAC;IAC9C,IAAIH,CAAC,GAAG,CAAC,EAAEI,IAAI,IAAI,EAAE,CAAC,CAAC;IACvB3C,WAAW,CAACK,GAAG,EAAEqC,IAAI,EAAEvC,KAAK,EAAGC,EAAE,IAAI,EAAE,EAAGC,GAAG,EAAEsC,IAAI,CAAC,CAAC,CAAC;;AAE1D;AAYA;AACA,SAASC,UAAUA,CAACC,QAAe,EAAEC,IAAW,EAAEC,KAAkB;EAClE;EACA,MAAMC,IAAI,GAAGrD,SAAS,CACpB;IACEsD,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,MAAM,EAAE,IAAI,IAAI,CAAC,GAAG;GACrB,EACDJ,KAAK,CACN;EACD,MAAM;IAAEK,CAAC;IAAEX,CAAC;IAAEY,CAAC;IAAEJ,KAAK;IAAEC,SAAS;IAAEC,MAAM;IAAEG;EAAU,CAAE,GAAGN,IAAI;EAC9DzD,YAAY,CAAC6D,CAAC,CAAC;EACf7D,YAAY,CAACkD,CAAC,CAAC;EACflD,YAAY,CAAC8D,CAAC,CAAC;EACf9D,YAAY,CAAC0D,KAAK,CAAC;EACnB1D,YAAY,CAAC2D,SAAS,CAAC;EACvB3D,YAAY,CAAC4D,MAAM,CAAC;EACpB,IAAIG,UAAU,KAAKC,SAAS,IAAI,OAAOD,UAAU,KAAK,UAAU,EAC9D,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;EAClD,MAAMC,SAAS,GAAG,GAAG,GAAGhB,CAAC;EACzB,MAAMiB,WAAW,GAAGD,SAAS,GAAG,CAAC;EACjC,IAAIL,CAAC,IAAI,CAAC,IAAI,CAACA,CAAC,GAAIA,CAAC,GAAG,CAAE,MAAM,CAAC,IAAIA,CAAC,IAAI,CAAC,KAAKK,SAAS,GAAG,CAAC,CAAC,IAAIL,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;IAC7E;IACA;IACA,MAAM,IAAII,KAAK,CACb,6FAA6F,CAC9F;;EAEH,IAAIH,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,GAAII,SAAS,EAAE;IACjD,MAAM,IAAID,KAAK,CACb,0FAA0F,CAC3F;;EAEH,IAAIP,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;IAC3C,MAAM,IAAIO,KAAK,CACb,gFAAgF,CACjF;;EAEH,MAAMG,OAAO,GAAGF,SAAS,IAAIL,CAAC,GAAGC,CAAC,CAAC;EACnC,IAAIM,OAAO,GAAGR,MAAM,EAAE;IACpB,MAAM,IAAIK,KAAK,CACb,iCAAiCG,OAAO,0BAA0BR,MAAM,WAAW,CACpF;;EAEH;EACA;EACA,MAAMS,CAAC,GAAGnE,MAAM,CAACD,MAAM,EAAEqD,QAAQ,EAAEC,IAAI,EAAE;IAAEe,CAAC,EAAE,CAAC;IAAEZ,KAAK,EAAEQ,SAAS,GAAGJ;EAAC,CAAE,CAAC;EACxE,MAAMS,GAAG,GAAGlE,GAAG,CAACgE,CAAC,CAAC;EAClB;EACA,MAAMG,CAAC,GAAGnE,GAAG,CAAC,IAAIoE,UAAU,CAACP,SAAS,GAAGL,CAAC,CAAC,CAAC;EAC5C,MAAMa,GAAG,GAAGrE,GAAG,CAAC,IAAIoE,UAAU,CAACP,SAAS,CAAC,CAAC;EAC1C,IAAIS,UAAU,GAAGA,CAAA,KAAK,CAAE,CAAC;EACzB,IAAIZ,UAAU,EAAE;IACd,MAAMa,aAAa,GAAG,CAAC,GAAGf,CAAC,GAAGC,CAAC;IAC/B;IACA;IACA,MAAMe,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACJ,aAAa,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC;IAClE,IAAIK,WAAW,GAAG,CAAC;IACnBN,UAAU,GAAGA,CAAA,KAAK;MAChBM,WAAW,EAAE;MACb,IAAIlB,UAAU,KAAK,EAAEkB,WAAW,GAAGJ,WAAW,CAAC,IAAII,WAAW,KAAKL,aAAa,CAAC,EAC/Eb,UAAU,CAACkB,WAAW,GAAGL,aAAa,CAAC;IAC3C,CAAC;;EAEH,OAAO;IAAEf,CAAC;IAAEX,CAAC;IAAEY,CAAC;IAAEJ,KAAK;IAAES,WAAW;IAAEK,CAAC;IAAED,GAAG;IAAEF,CAAC;IAAEK,GAAG;IAAEC,UAAU;IAAEhB;EAAS,CAAE;AAC/E;AAEA,SAASuB,YAAYA,CACnB5B,QAAe,EACfI,KAAa,EACbW,CAAa,EACbG,CAAc,EACdE,GAAgB;EAEhB,MAAMS,GAAG,GAAGjF,MAAM,CAACD,MAAM,EAAEqD,QAAQ,EAAEe,CAAC,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEZ;EAAK,CAAE,CAAC;EACxDW,CAAC,CAACe,IAAI,CAAC,CAAC,CAAC;EACTZ,CAAC,CAACY,IAAI,CAAC,CAAC,CAAC;EACTV,GAAG,CAACU,IAAI,CAAC,CAAC,CAAC;EACX,OAAOD,GAAG;AACZ;AAEA;;;;;;;;;;;;;;AAcA,OAAM,SAAUE,MAAMA,CAAC/B,QAAe,EAAEC,IAAW,EAAEE,IAAgB;EACnE,MAAM;IAAEI,CAAC;IAAEX,CAAC;IAAEY,CAAC;IAAEJ,KAAK;IAAES,WAAW;IAAEK,CAAC;IAAED,GAAG;IAAEF,CAAC;IAAEK,GAAG;IAAEC;EAAU,CAAE,GAAGtB,UAAU,CAC5EC,QAAQ,EACRC,IAAI,EACJE,IAAI,CACL;EACD,KAAK,IAAI9C,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGmD,CAAC,EAAEnD,EAAE,EAAE,EAAE;IAC7B,MAAM2E,EAAE,GAAGnB,WAAW,GAAGxD,EAAE;IAC3B,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,WAAW,EAAEnB,CAAC,EAAE,EAAEwB,CAAC,CAACxB,CAAC,CAAC,GAAGuB,GAAG,CAACe,EAAE,GAAGtC,CAAC,CAAC,CAAC,CAAC;IAC1D,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEuC,GAAG,GAAG,CAAC,EAAEvC,CAAC,GAAGa,CAAC,GAAG,CAAC,EAAEb,CAAC,EAAE,EAAE;MACvCC,QAAQ,CAACuB,CAAC,EAAEe,GAAG,EAAEf,CAAC,EAAGe,GAAG,IAAIpB,WAAW,EAAGjB,CAAC,CAAC,CAAC,CAAC;MAC9CyB,UAAU,EAAE;;IAEd1B,QAAQ,CAACuB,CAAC,EAAE,CAACX,CAAC,GAAG,CAAC,IAAIM,WAAW,EAAEI,GAAG,EAAEe,EAAE,EAAEpC,CAAC,CAAC,CAAC,CAAC;IAChDyB,UAAU,EAAE;IACZ,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,CAAC,EAAEb,CAAC,EAAE,EAAE;MAC1B;MACA,MAAMwC,CAAC,GAAGjB,GAAG,CAACe,EAAE,GAAGnB,WAAW,GAAG,EAAE,CAAC,GAAGN,CAAC,CAAC,CAAC;MAC1C,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,WAAW,EAAEsB,CAAC,EAAE,EAAEf,GAAG,CAACe,CAAC,CAAC,GAAGlB,GAAG,CAACe,EAAE,GAAGG,CAAC,CAAC,GAAGjB,CAAC,CAACgB,CAAC,GAAGrB,WAAW,GAAGsB,CAAC,CAAC,CAAC,CAAC;MACrFxC,QAAQ,CAACyB,GAAG,EAAE,CAAC,EAAEH,GAAG,EAAEe,EAAE,EAAEpC,CAAC,CAAC,CAAC,CAAC;MAC9ByB,UAAU,EAAE;;;EAGhB,OAAOO,YAAY,CAAC5B,QAAQ,EAAEI,KAAK,EAAEW,CAAC,EAAEG,CAAC,EAAEE,GAAG,CAAC;AACjD;AAEA;;;AAGA,OAAO,eAAegB,WAAWA,CAACpC,QAAe,EAAEC,IAAW,EAAEE,IAAgB;EAC9E,MAAM;IAAEI,CAAC;IAAEX,CAAC;IAAEY,CAAC;IAAEJ,KAAK;IAAES,WAAW;IAAEK,CAAC;IAAED,GAAG;IAAEF,CAAC;IAAEK,GAAG;IAAEC,UAAU;IAAEhB;EAAS,CAAE,GAAGN,UAAU,CACvFC,QAAQ,EACRC,IAAI,EACJE,IAAI,CACL;EACD,KAAK,IAAI9C,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGmD,CAAC,EAAEnD,EAAE,EAAE,EAAE;IAC7B,MAAM2E,EAAE,GAAGnB,WAAW,GAAGxD,EAAE;IAC3B,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,WAAW,EAAEnB,CAAC,EAAE,EAAEwB,CAAC,CAACxB,CAAC,CAAC,GAAGuB,GAAG,CAACe,EAAE,GAAGtC,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAIuC,GAAG,GAAG,CAAC;IACX,MAAMpF,SAAS,CAAC0D,CAAC,GAAG,CAAC,EAAEF,SAAS,EAAE,MAAK;MACrCV,QAAQ,CAACuB,CAAC,EAAEe,GAAG,EAAEf,CAAC,EAAGe,GAAG,IAAIpB,WAAW,EAAGjB,CAAC,CAAC,CAAC,CAAC;MAC9CyB,UAAU,EAAE;IACd,CAAC,CAAC;IACF1B,QAAQ,CAACuB,CAAC,EAAE,CAACX,CAAC,GAAG,CAAC,IAAIM,WAAW,EAAEI,GAAG,EAAEe,EAAE,EAAEpC,CAAC,CAAC,CAAC,CAAC;IAChDyB,UAAU,EAAE;IACZ,MAAMxE,SAAS,CAAC0D,CAAC,EAAEF,SAAS,EAAE,MAAK;MACjC;MACA,MAAM6B,CAAC,GAAGjB,GAAG,CAACe,EAAE,GAAGnB,WAAW,GAAG,EAAE,CAAC,GAAGN,CAAC,CAAC,CAAC;MAC1C,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,WAAW,EAAEsB,CAAC,EAAE,EAAEf,GAAG,CAACe,CAAC,CAAC,GAAGlB,GAAG,CAACe,EAAE,GAAGG,CAAC,CAAC,GAAGjB,CAAC,CAACgB,CAAC,GAAGrB,WAAW,GAAGsB,CAAC,CAAC,CAAC,CAAC;MACrFxC,QAAQ,CAACyB,GAAG,EAAE,CAAC,EAAEH,GAAG,EAAEe,EAAE,EAAEpC,CAAC,CAAC,CAAC,CAAC;MAC9ByB,UAAU,EAAE;IACd,CAAC,CAAC;;EAEJ,OAAOO,YAAY,CAAC5B,QAAQ,EAAEI,KAAK,EAAEW,CAAC,EAAEG,CAAC,EAAEE,GAAG,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}