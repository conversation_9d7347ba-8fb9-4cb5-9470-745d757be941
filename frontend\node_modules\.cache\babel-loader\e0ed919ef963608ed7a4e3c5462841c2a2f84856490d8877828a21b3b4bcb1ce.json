{"ast": null, "code": "/**\n *  The JSON Wallet formats allow a simple way to store the private\n *  keys needed in Ethereum along with related information and allows\n *  for extensible forms of encryption.\n *\n *  These utilities facilitate decrypting and encrypting the most common\n *  JSON Wallet formats.\n *\n *  @_subsection: api/wallet:JSON Wallets  [json-wallets]\n */\nimport { CTR } from \"aes-js\";\nimport { getAddress } from \"../address/index.js\";\nimport { keccak256, pbkdf2, randomBytes, scrypt, scryptSync } from \"../crypto/index.js\";\nimport { computeAddress } from \"../transaction/index.js\";\nimport { concat, getBytes, hexlify, uuidV4, assert, assertArgument } from \"../utils/index.js\";\nimport { getPassword, spelunk, zpad } from \"./utils.js\";\nimport { version } from \"../_version.js\";\nconst defaultPath = \"m/44'/60'/0'/0/0\";\n/**\n *  Returns true if %%json%% is a valid JSON Keystore Wallet.\n */\nexport function isKeystoreJson(json) {\n  try {\n    const data = JSON.parse(json);\n    const version = data.version != null ? parseInt(data.version) : 0;\n    if (version === 3) {\n      return true;\n    }\n  } catch (error) {}\n  return false;\n}\nfunction decrypt(data, key, ciphertext) {\n  const cipher = spelunk(data, \"crypto.cipher:string\");\n  if (cipher === \"aes-128-ctr\") {\n    const iv = spelunk(data, \"crypto.cipherparams.iv:data!\");\n    const aesCtr = new CTR(key, iv);\n    return hexlify(aesCtr.decrypt(ciphertext));\n  }\n  assert(false, \"unsupported cipher\", \"UNSUPPORTED_OPERATION\", {\n    operation: \"decrypt\"\n  });\n}\nfunction getAccount(data, _key) {\n  const key = getBytes(_key);\n  const ciphertext = spelunk(data, \"crypto.ciphertext:data!\");\n  const computedMAC = hexlify(keccak256(concat([key.slice(16, 32), ciphertext]))).substring(2);\n  assertArgument(computedMAC === spelunk(data, \"crypto.mac:string!\").toLowerCase(), \"incorrect password\", \"password\", \"[ REDACTED ]\");\n  const privateKey = decrypt(data, key.slice(0, 16), ciphertext);\n  const address = computeAddress(privateKey);\n  if (data.address) {\n    let check = data.address.toLowerCase();\n    if (!check.startsWith(\"0x\")) {\n      check = \"0x\" + check;\n    }\n    assertArgument(getAddress(check) === address, \"keystore address/privateKey mismatch\", \"address\", data.address);\n  }\n  const account = {\n    address,\n    privateKey\n  };\n  // Version 0.1 x-ethers metadata must contain an encrypted mnemonic phrase\n  const version = spelunk(data, \"x-ethers.version:string\");\n  if (version === \"0.1\") {\n    const mnemonicKey = key.slice(32, 64);\n    const mnemonicCiphertext = spelunk(data, \"x-ethers.mnemonicCiphertext:data!\");\n    const mnemonicIv = spelunk(data, \"x-ethers.mnemonicCounter:data!\");\n    const mnemonicAesCtr = new CTR(mnemonicKey, mnemonicIv);\n    account.mnemonic = {\n      path: spelunk(data, \"x-ethers.path:string\") || defaultPath,\n      locale: spelunk(data, \"x-ethers.locale:string\") || \"en\",\n      entropy: hexlify(getBytes(mnemonicAesCtr.decrypt(mnemonicCiphertext)))\n    };\n  }\n  return account;\n}\nfunction getDecryptKdfParams(data) {\n  const kdf = spelunk(data, \"crypto.kdf:string\");\n  if (kdf && typeof kdf === \"string\") {\n    if (kdf.toLowerCase() === \"scrypt\") {\n      const salt = spelunk(data, \"crypto.kdfparams.salt:data!\");\n      const N = spelunk(data, \"crypto.kdfparams.n:int!\");\n      const r = spelunk(data, \"crypto.kdfparams.r:int!\");\n      const p = spelunk(data, \"crypto.kdfparams.p:int!\");\n      // Make sure N is a power of 2\n      assertArgument(N > 0 && (N & N - 1) === 0, \"invalid kdf.N\", \"kdf.N\", N);\n      assertArgument(r > 0 && p > 0, \"invalid kdf\", \"kdf\", kdf);\n      const dkLen = spelunk(data, \"crypto.kdfparams.dklen:int!\");\n      assertArgument(dkLen === 32, \"invalid kdf.dklen\", \"kdf.dflen\", dkLen);\n      return {\n        name: \"scrypt\",\n        salt,\n        N,\n        r,\n        p,\n        dkLen: 64\n      };\n    } else if (kdf.toLowerCase() === \"pbkdf2\") {\n      const salt = spelunk(data, \"crypto.kdfparams.salt:data!\");\n      const prf = spelunk(data, \"crypto.kdfparams.prf:string!\");\n      const algorithm = prf.split(\"-\").pop();\n      assertArgument(algorithm === \"sha256\" || algorithm === \"sha512\", \"invalid kdf.pdf\", \"kdf.pdf\", prf);\n      const count = spelunk(data, \"crypto.kdfparams.c:int!\");\n      const dkLen = spelunk(data, \"crypto.kdfparams.dklen:int!\");\n      assertArgument(dkLen === 32, \"invalid kdf.dklen\", \"kdf.dklen\", dkLen);\n      return {\n        name: \"pbkdf2\",\n        salt,\n        count,\n        dkLen,\n        algorithm\n      };\n    }\n  }\n  assertArgument(false, \"unsupported key-derivation function\", \"kdf\", kdf);\n}\n/**\n *  Returns the account details for the JSON Keystore Wallet %%json%%\n *  using %%password%%.\n *\n *  It is preferred to use the [async version](decryptKeystoreJson)\n *  instead, which allows a [[ProgressCallback]] to keep the user informed\n *  as to the decryption status.\n *\n *  This method will block the event loop (freezing all UI) until decryption\n *  is complete, which can take quite some time, depending on the wallet\n *  paramters and platform.\n */\nexport function decryptKeystoreJsonSync(json, _password) {\n  const data = JSON.parse(json);\n  const password = getPassword(_password);\n  const params = getDecryptKdfParams(data);\n  if (params.name === \"pbkdf2\") {\n    const {\n      salt,\n      count,\n      dkLen,\n      algorithm\n    } = params;\n    const key = pbkdf2(password, salt, count, dkLen, algorithm);\n    return getAccount(data, key);\n  }\n  assert(params.name === \"scrypt\", \"cannot be reached\", \"UNKNOWN_ERROR\", {\n    params\n  });\n  const {\n    salt,\n    N,\n    r,\n    p,\n    dkLen\n  } = params;\n  const key = scryptSync(password, salt, N, r, p, dkLen);\n  return getAccount(data, key);\n}\nfunction stall(duration) {\n  return new Promise(resolve => {\n    setTimeout(() => {\n      resolve();\n    }, duration);\n  });\n}\n/**\n *  Resolves to the decrypted JSON Keystore Wallet %%json%% using the\n *  %%password%%.\n *\n *  If provided, %%progress%% will be called periodically during the\n *  decrpytion to provide feedback, and if the function returns\n *  ``false`` will halt decryption.\n *\n *  The %%progressCallback%% will **always** receive ``0`` before\n *  decryption begins and ``1`` when complete.\n */\nexport async function decryptKeystoreJson(json, _password, progress) {\n  const data = JSON.parse(json);\n  const password = getPassword(_password);\n  const params = getDecryptKdfParams(data);\n  if (params.name === \"pbkdf2\") {\n    if (progress) {\n      progress(0);\n      await stall(0);\n    }\n    const {\n      salt,\n      count,\n      dkLen,\n      algorithm\n    } = params;\n    const key = pbkdf2(password, salt, count, dkLen, algorithm);\n    if (progress) {\n      progress(1);\n      await stall(0);\n    }\n    return getAccount(data, key);\n  }\n  assert(params.name === \"scrypt\", \"cannot be reached\", \"UNKNOWN_ERROR\", {\n    params\n  });\n  const {\n    salt,\n    N,\n    r,\n    p,\n    dkLen\n  } = params;\n  const key = await scrypt(password, salt, N, r, p, dkLen, progress);\n  return getAccount(data, key);\n}\nfunction getEncryptKdfParams(options) {\n  // Check/generate the salt\n  const salt = options.salt != null ? getBytes(options.salt, \"options.salt\") : randomBytes(32);\n  // Override the scrypt password-based key derivation function parameters\n  let N = 1 << 17,\n    r = 8,\n    p = 1;\n  if (options.scrypt) {\n    if (options.scrypt.N) {\n      N = options.scrypt.N;\n    }\n    if (options.scrypt.r) {\n      r = options.scrypt.r;\n    }\n    if (options.scrypt.p) {\n      p = options.scrypt.p;\n    }\n  }\n  assertArgument(typeof N === \"number\" && N > 0 && Number.isSafeInteger(N) && (BigInt(N) & BigInt(N - 1)) === BigInt(0), \"invalid scrypt N parameter\", \"options.N\", N);\n  assertArgument(typeof r === \"number\" && r > 0 && Number.isSafeInteger(r), \"invalid scrypt r parameter\", \"options.r\", r);\n  assertArgument(typeof p === \"number\" && p > 0 && Number.isSafeInteger(p), \"invalid scrypt p parameter\", \"options.p\", p);\n  return {\n    name: \"scrypt\",\n    dkLen: 32,\n    salt,\n    N,\n    r,\n    p\n  };\n}\nfunction _encryptKeystore(key, kdf, account, options) {\n  const privateKey = getBytes(account.privateKey, \"privateKey\");\n  // Override initialization vector\n  const iv = options.iv != null ? getBytes(options.iv, \"options.iv\") : randomBytes(16);\n  assertArgument(iv.length === 16, \"invalid options.iv length\", \"options.iv\", options.iv);\n  // Override the uuid\n  const uuidRandom = options.uuid != null ? getBytes(options.uuid, \"options.uuid\") : randomBytes(16);\n  assertArgument(uuidRandom.length === 16, \"invalid options.uuid length\", \"options.uuid\", options.iv);\n  // This will be used to encrypt the wallet (as per Web3 secret storage)\n  // - 32 bytes   As normal for the Web3 secret storage (derivedKey, macPrefix)\n  // - 32 bytes   AES key to encrypt mnemonic with (required here to be Ethers Wallet)\n  const derivedKey = key.slice(0, 16);\n  const macPrefix = key.slice(16, 32);\n  // Encrypt the private key\n  const aesCtr = new CTR(derivedKey, iv);\n  const ciphertext = getBytes(aesCtr.encrypt(privateKey));\n  // Compute the message authentication code, used to check the password\n  const mac = keccak256(concat([macPrefix, ciphertext]));\n  // See: https://github.com/ethereum/wiki/wiki/Web3-Secret-Storage-Definition\n  const data = {\n    address: account.address.substring(2).toLowerCase(),\n    id: uuidV4(uuidRandom),\n    version: 3,\n    Crypto: {\n      cipher: \"aes-128-ctr\",\n      cipherparams: {\n        iv: hexlify(iv).substring(2)\n      },\n      ciphertext: hexlify(ciphertext).substring(2),\n      kdf: \"scrypt\",\n      kdfparams: {\n        salt: hexlify(kdf.salt).substring(2),\n        n: kdf.N,\n        dklen: 32,\n        p: kdf.p,\n        r: kdf.r\n      },\n      mac: mac.substring(2)\n    }\n  };\n  // If we have a mnemonic, encrypt it into the JSON wallet\n  if (account.mnemonic) {\n    const client = options.client != null ? options.client : `ethers/${version}`;\n    const path = account.mnemonic.path || defaultPath;\n    const locale = account.mnemonic.locale || \"en\";\n    const mnemonicKey = key.slice(32, 64);\n    const entropy = getBytes(account.mnemonic.entropy, \"account.mnemonic.entropy\");\n    const mnemonicIv = randomBytes(16);\n    const mnemonicAesCtr = new CTR(mnemonicKey, mnemonicIv);\n    const mnemonicCiphertext = getBytes(mnemonicAesCtr.encrypt(entropy));\n    const now = new Date();\n    const timestamp = now.getUTCFullYear() + \"-\" + zpad(now.getUTCMonth() + 1, 2) + \"-\" + zpad(now.getUTCDate(), 2) + \"T\" + zpad(now.getUTCHours(), 2) + \"-\" + zpad(now.getUTCMinutes(), 2) + \"-\" + zpad(now.getUTCSeconds(), 2) + \".0Z\";\n    const gethFilename = \"UTC--\" + timestamp + \"--\" + data.address;\n    data[\"x-ethers\"] = {\n      client,\n      gethFilename,\n      path,\n      locale,\n      mnemonicCounter: hexlify(mnemonicIv).substring(2),\n      mnemonicCiphertext: hexlify(mnemonicCiphertext).substring(2),\n      version: \"0.1\"\n    };\n  }\n  return JSON.stringify(data);\n}\n/**\n *  Return the JSON Keystore Wallet for %%account%% encrypted with\n *  %%password%%.\n *\n *  The %%options%% can be used to tune the password-based key\n *  derivation function parameters, explicitly set the random values\n *  used. Any provided [[ProgressCallback]] is ignord.\n */\nexport function encryptKeystoreJsonSync(account, password, options) {\n  if (options == null) {\n    options = {};\n  }\n  const passwordBytes = getPassword(password);\n  const kdf = getEncryptKdfParams(options);\n  const key = scryptSync(passwordBytes, kdf.salt, kdf.N, kdf.r, kdf.p, 64);\n  return _encryptKeystore(getBytes(key), kdf, account, options);\n}\n/**\n *  Resolved to the JSON Keystore Wallet for %%account%% encrypted\n *  with %%password%%.\n *\n *  The %%options%% can be used to tune the password-based key\n *  derivation function parameters, explicitly set the random values\n *  used and provide a [[ProgressCallback]] to receive periodic updates\n *  on the completion status..\n */\nexport async function encryptKeystoreJson(account, password, options) {\n  if (options == null) {\n    options = {};\n  }\n  const passwordBytes = getPassword(password);\n  const kdf = getEncryptKdfParams(options);\n  const key = await scrypt(passwordBytes, kdf.salt, kdf.N, kdf.r, kdf.p, 64, options.progressCallback);\n  return _encryptKeystore(getBytes(key), kdf, account, options);\n}", "map": {"version": 3, "names": ["CTR", "get<PERSON><PERSON><PERSON>", "keccak256", "pbkdf2", "randomBytes", "scrypt", "scryptSync", "computeAddress", "concat", "getBytes", "hexlify", "uuidV4", "assert", "assertArgument", "getPassword", "spelunk", "zpad", "version", "defaultPath", "isKeystoreJson", "json", "data", "JSON", "parse", "parseInt", "error", "decrypt", "key", "ciphertext", "cipher", "iv", "aesCtr", "operation", "getAccount", "_key", "computedMAC", "slice", "substring", "toLowerCase", "privateKey", "address", "check", "startsWith", "account", "mnemonicKey", "mnemonicCiphertext", "mnemonicIv", "mnemonicAesCtr", "mnemonic", "path", "locale", "entropy", "getDecryptKdfParams", "kdf", "salt", "N", "r", "p", "dkLen", "name", "prf", "algorithm", "split", "pop", "count", "decryptKeystoreJsonSync", "_password", "password", "params", "stall", "duration", "Promise", "resolve", "setTimeout", "decryptKeystoreJson", "progress", "getEncryptKdfParams", "options", "Number", "isSafeInteger", "BigInt", "_encryptKeystore", "length", "uuidRandom", "uuid", "<PERSON><PERSON><PERSON>", "macPrefix", "encrypt", "mac", "id", "Crypto", "cipherparams", "kdfparams", "n", "dklen", "client", "now", "Date", "timestamp", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "gethFilename", "mnemonic<PERSON><PERSON><PERSON>", "stringify", "encryptKeystoreJsonSync", "passwordBytes", "encryptKeystoreJson", "progressCallback"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\json-keystore.ts"], "sourcesContent": ["/**\n *  The JSON Wallet formats allow a simple way to store the private\n *  keys needed in Ethereum along with related information and allows\n *  for extensible forms of encryption.\n *\n *  These utilities facilitate decrypting and encrypting the most common\n *  JSON Wallet formats.\n *\n *  @_subsection: api/wallet:JSON Wallets  [json-wallets]\n */\n\nimport { CTR } from \"aes-js\";\n\nimport { getAddress } from \"../address/index.js\";\nimport { keccak256, pbkdf2, randomBytes, scrypt, scryptSync } from \"../crypto/index.js\";\nimport { computeAddress } from \"../transaction/index.js\";\nimport {\n    concat, getBytes, hexlify, uuidV4, assert, assertArgument\n} from \"../utils/index.js\";\n\nimport { getPassword, spelunk, zpad } from \"./utils.js\";\n\nimport type { ProgressCallback } from \"../crypto/index.js\";\nimport type { BytesLike } from \"../utils/index.js\";\n\nimport { version } from \"../_version.js\";\n\n\nconst defaultPath = \"m/44'/60'/0'/0/0\";\n\n/**\n *  The contents of a JSON Keystore Wallet.\n */\nexport type KeystoreAccount = {\n    address: string;\n    privateKey: string;\n    mnemonic?: {\n        path?: string;\n        locale?: string;\n        entropy: string;\n    }\n};\n\n/**\n *  The parameters to use when encrypting a JSON Keystore Wallet.\n */\nexport type EncryptOptions = {\n   progressCallback?: ProgressCallback;\n   iv?: BytesLike;\n   entropy?: BytesLike;\n   client?: string;\n   salt?: BytesLike;\n   uuid?: string;\n   scrypt?: {\n       N?: number;\n       r?: number;\n       p?: number;\n   }\n}\n\n/**\n *  Returns true if %%json%% is a valid JSON Keystore Wallet.\n */\nexport function isKeystoreJson(json: string): boolean {\n    try {\n        const data = JSON.parse(json);\n        const version = ((data.version != null) ? parseInt(data.version): 0);\n        if (version === 3) { return true; }\n    } catch (error) { }\n    return false;\n}\n\nfunction decrypt(data: any, key: Uint8Array, ciphertext: Uint8Array): string {\n    const cipher = spelunk<string>(data, \"crypto.cipher:string\");\n    if (cipher === \"aes-128-ctr\") {\n        const iv = spelunk<Uint8Array>(data, \"crypto.cipherparams.iv:data!\")\n        const aesCtr = new CTR(key, iv);\n        return hexlify(aesCtr.decrypt(ciphertext));\n    }\n\n    assert(false, \"unsupported cipher\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"decrypt\"\n    });\n}\n\nfunction getAccount(data: any, _key: string): KeystoreAccount {\n    const key = getBytes(_key);\n    const ciphertext = spelunk<Uint8Array>(data, \"crypto.ciphertext:data!\");\n\n    const computedMAC = hexlify(keccak256(concat([ key.slice(16, 32), ciphertext ]))).substring(2);\n    assertArgument(computedMAC === spelunk<string>(data, \"crypto.mac:string!\").toLowerCase(),\n        \"incorrect password\", \"password\", \"[ REDACTED ]\");\n\n    const privateKey = decrypt(data, key.slice(0, 16), ciphertext);\n\n    const address = computeAddress(privateKey);\n    if (data.address) {\n        let check = data.address.toLowerCase();\n        if (!check.startsWith(\"0x\")) { check = \"0x\" + check; }\n\n        assertArgument(getAddress(check) === address, \"keystore address/privateKey mismatch\", \"address\", data.address);\n    }\n\n    const account: KeystoreAccount = { address, privateKey };\n\n    // Version 0.1 x-ethers metadata must contain an encrypted mnemonic phrase\n    const version = spelunk(data, \"x-ethers.version:string\");\n    if (version === \"0.1\") {\n        const mnemonicKey = key.slice(32, 64);\n\n        const mnemonicCiphertext = spelunk<Uint8Array>(data, \"x-ethers.mnemonicCiphertext:data!\");\n        const mnemonicIv = spelunk<Uint8Array>(data, \"x-ethers.mnemonicCounter:data!\");\n\n        const mnemonicAesCtr = new CTR(mnemonicKey, mnemonicIv);\n\n        account.mnemonic = {\n            path: (spelunk<null | string>(data, \"x-ethers.path:string\") || defaultPath),\n            locale: (spelunk<null | string>(data, \"x-ethers.locale:string\") || \"en\"),\n            entropy: hexlify(getBytes(mnemonicAesCtr.decrypt(mnemonicCiphertext)))\n        };\n    }\n\n    return account;\n}\n\ntype ScryptParams = {\n    name: \"scrypt\";\n    salt: Uint8Array;\n    N: number;\n    r: number;\n    p: number;\n    dkLen: number;\n};\n\ntype KdfParams = ScryptParams | {\n    name: \"pbkdf2\";\n    salt: Uint8Array;\n    count: number;\n    dkLen: number;\n    algorithm: \"sha256\" | \"sha512\";\n};\n\nfunction getDecryptKdfParams<T>(data: any): KdfParams {\n    const kdf = spelunk(data, \"crypto.kdf:string\");\n    if (kdf && typeof(kdf) === \"string\") {\n        if (kdf.toLowerCase() === \"scrypt\") {\n            const salt = spelunk<Uint8Array>(data, \"crypto.kdfparams.salt:data!\");\n            const N = spelunk<number>(data, \"crypto.kdfparams.n:int!\");\n            const r = spelunk<number>(data, \"crypto.kdfparams.r:int!\");\n            const p = spelunk<number>(data, \"crypto.kdfparams.p:int!\");\n\n            // Make sure N is a power of 2\n            assertArgument(N > 0 && (N & (N - 1)) === 0, \"invalid kdf.N\", \"kdf.N\", N);\n            assertArgument(r > 0 && p > 0, \"invalid kdf\", \"kdf\", kdf);\n\n            const dkLen = spelunk<number>(data, \"crypto.kdfparams.dklen:int!\");\n            assertArgument(dkLen === 32, \"invalid kdf.dklen\", \"kdf.dflen\", dkLen);\n\n            return { name: \"scrypt\", salt, N, r, p, dkLen: 64 };\n\n        } else if (kdf.toLowerCase() === \"pbkdf2\") {\n\n            const salt = spelunk<Uint8Array>(data, \"crypto.kdfparams.salt:data!\");\n\n            const prf = spelunk<string>(data, \"crypto.kdfparams.prf:string!\");\n            const algorithm = prf.split(\"-\").pop();\n            assertArgument(algorithm === \"sha256\" || algorithm === \"sha512\", \"invalid kdf.pdf\", \"kdf.pdf\", prf);\n\n            const count = spelunk<number>(data, \"crypto.kdfparams.c:int!\");\n\n            const dkLen = spelunk<number>(data, \"crypto.kdfparams.dklen:int!\");\n            assertArgument(dkLen === 32, \"invalid kdf.dklen\", \"kdf.dklen\", dkLen);\n\n            return { name: \"pbkdf2\", salt, count, dkLen, algorithm };\n        }\n    }\n\n    assertArgument(false, \"unsupported key-derivation function\", \"kdf\", kdf);\n}\n\n\n/**\n *  Returns the account details for the JSON Keystore Wallet %%json%%\n *  using %%password%%.\n *\n *  It is preferred to use the [async version](decryptKeystoreJson)\n *  instead, which allows a [[ProgressCallback]] to keep the user informed\n *  as to the decryption status.\n *\n *  This method will block the event loop (freezing all UI) until decryption\n *  is complete, which can take quite some time, depending on the wallet\n *  paramters and platform.\n */\nexport function decryptKeystoreJsonSync(json: string, _password: string | Uint8Array): KeystoreAccount {\n    const data = JSON.parse(json);\n\n    const password = getPassword(_password);\n\n    const params = getDecryptKdfParams(data);\n    if (params.name === \"pbkdf2\") {\n        const { salt, count, dkLen, algorithm } = params;\n        const key = pbkdf2(password, salt, count, dkLen, algorithm);\n        return getAccount(data, key);\n    }\n\n    assert(params.name === \"scrypt\", \"cannot be reached\", \"UNKNOWN_ERROR\", { params })\n\n    const { salt, N, r, p, dkLen } = params;\n    const key = scryptSync(password, salt, N, r, p, dkLen);\n    return getAccount(data, key);\n}\n\nfunction stall(duration: number): Promise<void> {\n    return new Promise((resolve) => { setTimeout(() => { resolve(); }, duration); });\n}\n\n/**\n *  Resolves to the decrypted JSON Keystore Wallet %%json%% using the\n *  %%password%%.\n *\n *  If provided, %%progress%% will be called periodically during the\n *  decrpytion to provide feedback, and if the function returns\n *  ``false`` will halt decryption.\n *\n *  The %%progressCallback%% will **always** receive ``0`` before\n *  decryption begins and ``1`` when complete.\n */\nexport async function decryptKeystoreJson(json: string, _password: string | Uint8Array, progress?: ProgressCallback): Promise<KeystoreAccount> {\n    const data = JSON.parse(json);\n\n    const password = getPassword(_password);\n\n    const params = getDecryptKdfParams(data);\n    if (params.name === \"pbkdf2\") {\n        if (progress) {\n            progress(0);\n            await stall(0);\n        }\n        const { salt, count, dkLen, algorithm } = params;\n        const key = pbkdf2(password, salt, count, dkLen, algorithm);\n        if (progress) {\n            progress(1);\n            await stall(0);\n        }\n        return getAccount(data, key);\n    }\n\n    assert(params.name === \"scrypt\", \"cannot be reached\", \"UNKNOWN_ERROR\", { params })\n\n    const { salt, N, r, p, dkLen } = params;\n    const key = await scrypt(password, salt, N, r, p, dkLen, progress);\n    return getAccount(data, key);\n}\n\nfunction getEncryptKdfParams(options: EncryptOptions): ScryptParams {\n    // Check/generate the salt\n    const salt = (options.salt != null) ? getBytes(options.salt, \"options.salt\"): randomBytes(32);\n\n    // Override the scrypt password-based key derivation function parameters\n    let N = (1 << 17), r = 8, p = 1;\n    if (options.scrypt) {\n        if (options.scrypt.N) { N = options.scrypt.N; }\n        if (options.scrypt.r) { r = options.scrypt.r; }\n        if (options.scrypt.p) { p = options.scrypt.p; }\n    }\n    assertArgument(typeof(N) === \"number\" && N > 0 && Number.isSafeInteger(N) && (BigInt(N) & BigInt(N - 1)) === BigInt(0), \"invalid scrypt N parameter\", \"options.N\", N);\n    assertArgument(typeof(r) === \"number\" && r > 0 && Number.isSafeInteger(r), \"invalid scrypt r parameter\", \"options.r\", r);\n    assertArgument(typeof(p) === \"number\" && p > 0 && Number.isSafeInteger(p), \"invalid scrypt p parameter\", \"options.p\", p);\n\n    return { name: \"scrypt\", dkLen: 32, salt, N, r, p };\n}\n\nfunction _encryptKeystore(key: Uint8Array, kdf: ScryptParams, account: KeystoreAccount, options: EncryptOptions): any {\n\n    const privateKey = getBytes(account.privateKey, \"privateKey\");\n\n    // Override initialization vector\n    const iv = (options.iv != null) ? getBytes(options.iv, \"options.iv\"): randomBytes(16);\n    assertArgument(iv.length === 16, \"invalid options.iv length\", \"options.iv\", options.iv);\n\n    // Override the uuid\n    const uuidRandom = (options.uuid != null) ? getBytes(options.uuid, \"options.uuid\"): randomBytes(16);\n    assertArgument(uuidRandom.length === 16, \"invalid options.uuid length\", \"options.uuid\", options.iv);\n\n    // This will be used to encrypt the wallet (as per Web3 secret storage)\n    // - 32 bytes   As normal for the Web3 secret storage (derivedKey, macPrefix)\n    // - 32 bytes   AES key to encrypt mnemonic with (required here to be Ethers Wallet)\n    const derivedKey = key.slice(0, 16);\n    const macPrefix = key.slice(16, 32);\n\n    // Encrypt the private key\n    const aesCtr = new CTR(derivedKey, iv);\n    const ciphertext = getBytes(aesCtr.encrypt(privateKey));\n\n    // Compute the message authentication code, used to check the password\n    const mac = keccak256(concat([ macPrefix, ciphertext ]))\n\n    // See: https://github.com/ethereum/wiki/wiki/Web3-Secret-Storage-Definition\n    const data: { [key: string]: any } = {\n        address: account.address.substring(2).toLowerCase(),\n        id: uuidV4(uuidRandom),\n        version: 3,\n        Crypto: {\n            cipher: \"aes-128-ctr\",\n            cipherparams: {\n                iv: hexlify(iv).substring(2),\n            },\n            ciphertext: hexlify(ciphertext).substring(2),\n            kdf: \"scrypt\",\n            kdfparams: {\n                salt: hexlify(kdf.salt).substring(2),\n                n: kdf.N,\n                dklen: 32,\n                p: kdf.p,\n                r: kdf.r\n            },\n            mac: mac.substring(2)\n        }\n    };\n\n    // If we have a mnemonic, encrypt it into the JSON wallet\n    if (account.mnemonic) {\n        const client = (options.client != null) ? options.client: `ethers/${ version }`;\n\n        const path = account.mnemonic.path || defaultPath;\n        const locale = account.mnemonic.locale || \"en\";\n\n        const mnemonicKey = key.slice(32, 64);\n\n        const entropy = getBytes(account.mnemonic.entropy, \"account.mnemonic.entropy\");\n        const mnemonicIv = randomBytes(16);\n        const mnemonicAesCtr = new CTR(mnemonicKey, mnemonicIv);\n        const mnemonicCiphertext = getBytes(mnemonicAesCtr.encrypt(entropy));\n\n        const now = new Date();\n        const timestamp = (now.getUTCFullYear() + \"-\" +\n                           zpad(now.getUTCMonth() + 1, 2) + \"-\" +\n                           zpad(now.getUTCDate(), 2) + \"T\" +\n                           zpad(now.getUTCHours(), 2) + \"-\" +\n                           zpad(now.getUTCMinutes(), 2) + \"-\" +\n                           zpad(now.getUTCSeconds(), 2) + \".0Z\");\n        const gethFilename = (\"UTC--\" + timestamp + \"--\" + data.address);\n\n        data[\"x-ethers\"] = {\n            client, gethFilename, path, locale,\n            mnemonicCounter: hexlify(mnemonicIv).substring(2),\n            mnemonicCiphertext: hexlify(mnemonicCiphertext).substring(2),\n            version: \"0.1\"\n        };\n    }\n\n    return JSON.stringify(data);\n}\n\n/**\n *  Return the JSON Keystore Wallet for %%account%% encrypted with\n *  %%password%%.\n *\n *  The %%options%% can be used to tune the password-based key\n *  derivation function parameters, explicitly set the random values\n *  used. Any provided [[ProgressCallback]] is ignord.\n */\nexport function encryptKeystoreJsonSync(account: KeystoreAccount, password: string | Uint8Array, options?: EncryptOptions): string {\n    if (options == null) { options = { }; }\n\n    const passwordBytes = getPassword(password);\n    const kdf = getEncryptKdfParams(options);\n    const key = scryptSync(passwordBytes, kdf.salt, kdf.N, kdf.r, kdf.p, 64);\n    return _encryptKeystore(getBytes(key), kdf, account, options);\n}\n\n/**\n *  Resolved to the JSON Keystore Wallet for %%account%% encrypted\n *  with %%password%%.\n *\n *  The %%options%% can be used to tune the password-based key\n *  derivation function parameters, explicitly set the random values\n *  used and provide a [[ProgressCallback]] to receive periodic updates\n *  on the completion status..\n */\nexport async function encryptKeystoreJson(account: KeystoreAccount, password: string | Uint8Array, options?: EncryptOptions): Promise<string> {\n    if (options == null) { options = { }; }\n\n    const passwordBytes = getPassword(password);\n    const kdf = getEncryptKdfParams(options);\n    const key = await scrypt(passwordBytes, kdf.salt, kdf.N, kdf.r, kdf.p, 64, options.progressCallback);\n    return _encryptKeystore(getBytes(key), kdf, account, options);\n}\n\n"], "mappings": "AAAA;;;;;;;;;;AAWA,SAASA,GAAG,QAAQ,QAAQ;AAE5B,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,MAAM,EAAEC,UAAU,QAAQ,oBAAoB;AACvF,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SACIC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,cAAc,QACtD,mBAAmB;AAE1B,SAASC,WAAW,EAAEC,OAAO,EAAEC,IAAI,QAAQ,YAAY;AAKvD,SAASC,OAAO,QAAQ,gBAAgB;AAGxC,MAAMC,WAAW,GAAG,kBAAkB;AAgCtC;;;AAGA,OAAM,SAAUC,cAAcA,CAACC,IAAY;EACvC,IAAI;IACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IAC7B,MAAMH,OAAO,GAAKI,IAAI,CAACJ,OAAO,IAAI,IAAI,GAAIO,QAAQ,CAACH,IAAI,CAACJ,OAAO,CAAC,GAAE,CAAE;IACpE,IAAIA,OAAO,KAAK,CAAC,EAAE;MAAE,OAAO,IAAI;;GACnC,CAAC,OAAOQ,KAAK,EAAE;EAChB,OAAO,KAAK;AAChB;AAEA,SAASC,OAAOA,CAACL,IAAS,EAAEM,GAAe,EAAEC,UAAsB;EAC/D,MAAMC,MAAM,GAAGd,OAAO,CAASM,IAAI,EAAE,sBAAsB,CAAC;EAC5D,IAAIQ,MAAM,KAAK,aAAa,EAAE;IAC1B,MAAMC,EAAE,GAAGf,OAAO,CAAaM,IAAI,EAAE,8BAA8B,CAAC;IACpE,MAAMU,MAAM,GAAG,IAAI/B,GAAG,CAAC2B,GAAG,EAAEG,EAAE,CAAC;IAC/B,OAAOpB,OAAO,CAACqB,MAAM,CAACL,OAAO,CAACE,UAAU,CAAC,CAAC;;EAG9ChB,MAAM,CAAC,KAAK,EAAE,oBAAoB,EAAE,uBAAuB,EAAE;IACzDoB,SAAS,EAAE;GACd,CAAC;AACN;AAEA,SAASC,UAAUA,CAACZ,IAAS,EAAEa,IAAY;EACvC,MAAMP,GAAG,GAAGlB,QAAQ,CAACyB,IAAI,CAAC;EAC1B,MAAMN,UAAU,GAAGb,OAAO,CAAaM,IAAI,EAAE,yBAAyB,CAAC;EAEvE,MAAMc,WAAW,GAAGzB,OAAO,CAACR,SAAS,CAACM,MAAM,CAAC,CAAEmB,GAAG,CAACS,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAER,UAAU,CAAE,CAAC,CAAC,CAAC,CAACS,SAAS,CAAC,CAAC,CAAC;EAC9FxB,cAAc,CAACsB,WAAW,KAAKpB,OAAO,CAASM,IAAI,EAAE,oBAAoB,CAAC,CAACiB,WAAW,EAAE,EACpF,oBAAoB,EAAE,UAAU,EAAE,cAAc,CAAC;EAErD,MAAMC,UAAU,GAAGb,OAAO,CAACL,IAAI,EAAEM,GAAG,CAACS,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAER,UAAU,CAAC;EAE9D,MAAMY,OAAO,GAAGjC,cAAc,CAACgC,UAAU,CAAC;EAC1C,IAAIlB,IAAI,CAACmB,OAAO,EAAE;IACd,IAAIC,KAAK,GAAGpB,IAAI,CAACmB,OAAO,CAACF,WAAW,EAAE;IACtC,IAAI,CAACG,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;MAAED,KAAK,GAAG,IAAI,GAAGA,KAAK;;IAEnD5B,cAAc,CAACZ,UAAU,CAACwC,KAAK,CAAC,KAAKD,OAAO,EAAE,sCAAsC,EAAE,SAAS,EAAEnB,IAAI,CAACmB,OAAO,CAAC;;EAGlH,MAAMG,OAAO,GAAoB;IAAEH,OAAO;IAAED;EAAU,CAAE;EAExD;EACA,MAAMtB,OAAO,GAAGF,OAAO,CAACM,IAAI,EAAE,yBAAyB,CAAC;EACxD,IAAIJ,OAAO,KAAK,KAAK,EAAE;IACnB,MAAM2B,WAAW,GAAGjB,GAAG,CAACS,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;IAErC,MAAMS,kBAAkB,GAAG9B,OAAO,CAAaM,IAAI,EAAE,mCAAmC,CAAC;IACzF,MAAMyB,UAAU,GAAG/B,OAAO,CAAaM,IAAI,EAAE,gCAAgC,CAAC;IAE9E,MAAM0B,cAAc,GAAG,IAAI/C,GAAG,CAAC4C,WAAW,EAAEE,UAAU,CAAC;IAEvDH,OAAO,CAACK,QAAQ,GAAG;MACfC,IAAI,EAAGlC,OAAO,CAAgBM,IAAI,EAAE,sBAAsB,CAAC,IAAIH,WAAY;MAC3EgC,MAAM,EAAGnC,OAAO,CAAgBM,IAAI,EAAE,wBAAwB,CAAC,IAAI,IAAK;MACxE8B,OAAO,EAAEzC,OAAO,CAACD,QAAQ,CAACsC,cAAc,CAACrB,OAAO,CAACmB,kBAAkB,CAAC,CAAC;KACxE;;EAGL,OAAOF,OAAO;AAClB;AAmBA,SAASS,mBAAmBA,CAAI/B,IAAS;EACrC,MAAMgC,GAAG,GAAGtC,OAAO,CAACM,IAAI,EAAE,mBAAmB,CAAC;EAC9C,IAAIgC,GAAG,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;IACjC,IAAIA,GAAG,CAACf,WAAW,EAAE,KAAK,QAAQ,EAAE;MAChC,MAAMgB,IAAI,GAAGvC,OAAO,CAAaM,IAAI,EAAE,6BAA6B,CAAC;MACrE,MAAMkC,CAAC,GAAGxC,OAAO,CAASM,IAAI,EAAE,yBAAyB,CAAC;MAC1D,MAAMmC,CAAC,GAAGzC,OAAO,CAASM,IAAI,EAAE,yBAAyB,CAAC;MAC1D,MAAMoC,CAAC,GAAG1C,OAAO,CAASM,IAAI,EAAE,yBAAyB,CAAC;MAE1D;MACAR,cAAc,CAAC0C,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC,GAAIA,CAAC,GAAG,CAAE,MAAM,CAAC,EAAE,eAAe,EAAE,OAAO,EAAEA,CAAC,CAAC;MACzE1C,cAAc,CAAC2C,CAAC,GAAG,CAAC,IAAIC,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,KAAK,EAAEJ,GAAG,CAAC;MAEzD,MAAMK,KAAK,GAAG3C,OAAO,CAASM,IAAI,EAAE,6BAA6B,CAAC;MAClER,cAAc,CAAC6C,KAAK,KAAK,EAAE,EAAE,mBAAmB,EAAE,WAAW,EAAEA,KAAK,CAAC;MAErE,OAAO;QAAEC,IAAI,EAAE,QAAQ;QAAEL,IAAI;QAAEC,CAAC;QAAEC,CAAC;QAAEC,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAE;KAEtD,MAAM,IAAIL,GAAG,CAACf,WAAW,EAAE,KAAK,QAAQ,EAAE;MAEvC,MAAMgB,IAAI,GAAGvC,OAAO,CAAaM,IAAI,EAAE,6BAA6B,CAAC;MAErE,MAAMuC,GAAG,GAAG7C,OAAO,CAASM,IAAI,EAAE,8BAA8B,CAAC;MACjE,MAAMwC,SAAS,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;MACtClD,cAAc,CAACgD,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAED,GAAG,CAAC;MAEnG,MAAMI,KAAK,GAAGjD,OAAO,CAASM,IAAI,EAAE,yBAAyB,CAAC;MAE9D,MAAMqC,KAAK,GAAG3C,OAAO,CAASM,IAAI,EAAE,6BAA6B,CAAC;MAClER,cAAc,CAAC6C,KAAK,KAAK,EAAE,EAAE,mBAAmB,EAAE,WAAW,EAAEA,KAAK,CAAC;MAErE,OAAO;QAAEC,IAAI,EAAE,QAAQ;QAAEL,IAAI;QAAEU,KAAK;QAAEN,KAAK;QAAEG;MAAS,CAAE;;;EAIhEhD,cAAc,CAAC,KAAK,EAAE,qCAAqC,EAAE,KAAK,EAAEwC,GAAG,CAAC;AAC5E;AAGA;;;;;;;;;;;;AAYA,OAAM,SAAUY,uBAAuBA,CAAC7C,IAAY,EAAE8C,SAA8B;EAChF,MAAM7C,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EAE7B,MAAM+C,QAAQ,GAAGrD,WAAW,CAACoD,SAAS,CAAC;EAEvC,MAAME,MAAM,GAAGhB,mBAAmB,CAAC/B,IAAI,CAAC;EACxC,IAAI+C,MAAM,CAACT,IAAI,KAAK,QAAQ,EAAE;IAC1B,MAAM;MAAEL,IAAI;MAAEU,KAAK;MAAEN,KAAK;MAAEG;IAAS,CAAE,GAAGO,MAAM;IAChD,MAAMzC,GAAG,GAAGxB,MAAM,CAACgE,QAAQ,EAAEb,IAAI,EAAEU,KAAK,EAAEN,KAAK,EAAEG,SAAS,CAAC;IAC3D,OAAO5B,UAAU,CAACZ,IAAI,EAAEM,GAAG,CAAC;;EAGhCf,MAAM,CAACwD,MAAM,CAACT,IAAI,KAAK,QAAQ,EAAE,mBAAmB,EAAE,eAAe,EAAE;IAAES;EAAM,CAAE,CAAC;EAElF,MAAM;IAAEd,IAAI;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAK,CAAE,GAAGU,MAAM;EACvC,MAAMzC,GAAG,GAAGrB,UAAU,CAAC6D,QAAQ,EAAEb,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;EACtD,OAAOzB,UAAU,CAACZ,IAAI,EAAEM,GAAG,CAAC;AAChC;AAEA,SAAS0C,KAAKA,CAACC,QAAgB;EAC3B,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;IAAGC,UAAU,CAAC,MAAK;MAAGD,OAAO,EAAE;IAAE,CAAC,EAAEF,QAAQ,CAAC;EAAE,CAAC,CAAC;AACpF;AAEA;;;;;;;;;;;AAWA,OAAO,eAAeI,mBAAmBA,CAACtD,IAAY,EAAE8C,SAA8B,EAAES,QAA2B;EAC/G,MAAMtD,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EAE7B,MAAM+C,QAAQ,GAAGrD,WAAW,CAACoD,SAAS,CAAC;EAEvC,MAAME,MAAM,GAAGhB,mBAAmB,CAAC/B,IAAI,CAAC;EACxC,IAAI+C,MAAM,CAACT,IAAI,KAAK,QAAQ,EAAE;IAC1B,IAAIgB,QAAQ,EAAE;MACVA,QAAQ,CAAC,CAAC,CAAC;MACX,MAAMN,KAAK,CAAC,CAAC,CAAC;;IAElB,MAAM;MAAEf,IAAI;MAAEU,KAAK;MAAEN,KAAK;MAAEG;IAAS,CAAE,GAAGO,MAAM;IAChD,MAAMzC,GAAG,GAAGxB,MAAM,CAACgE,QAAQ,EAAEb,IAAI,EAAEU,KAAK,EAAEN,KAAK,EAAEG,SAAS,CAAC;IAC3D,IAAIc,QAAQ,EAAE;MACVA,QAAQ,CAAC,CAAC,CAAC;MACX,MAAMN,KAAK,CAAC,CAAC,CAAC;;IAElB,OAAOpC,UAAU,CAACZ,IAAI,EAAEM,GAAG,CAAC;;EAGhCf,MAAM,CAACwD,MAAM,CAACT,IAAI,KAAK,QAAQ,EAAE,mBAAmB,EAAE,eAAe,EAAE;IAAES;EAAM,CAAE,CAAC;EAElF,MAAM;IAAEd,IAAI;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAK,CAAE,GAAGU,MAAM;EACvC,MAAMzC,GAAG,GAAG,MAAMtB,MAAM,CAAC8D,QAAQ,EAAEb,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEiB,QAAQ,CAAC;EAClE,OAAO1C,UAAU,CAACZ,IAAI,EAAEM,GAAG,CAAC;AAChC;AAEA,SAASiD,mBAAmBA,CAACC,OAAuB;EAChD;EACA,MAAMvB,IAAI,GAAIuB,OAAO,CAACvB,IAAI,IAAI,IAAI,GAAI7C,QAAQ,CAACoE,OAAO,CAACvB,IAAI,EAAE,cAAc,CAAC,GAAElD,WAAW,CAAC,EAAE,CAAC;EAE7F;EACA,IAAImD,CAAC,GAAI,CAAC,IAAI,EAAG;IAAEC,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAG,CAAC;EAC/B,IAAIoB,OAAO,CAACxE,MAAM,EAAE;IAChB,IAAIwE,OAAO,CAACxE,MAAM,CAACkD,CAAC,EAAE;MAAEA,CAAC,GAAGsB,OAAO,CAACxE,MAAM,CAACkD,CAAC;;IAC5C,IAAIsB,OAAO,CAACxE,MAAM,CAACmD,CAAC,EAAE;MAAEA,CAAC,GAAGqB,OAAO,CAACxE,MAAM,CAACmD,CAAC;;IAC5C,IAAIqB,OAAO,CAACxE,MAAM,CAACoD,CAAC,EAAE;MAAEA,CAAC,GAAGoB,OAAO,CAACxE,MAAM,CAACoD,CAAC;;;EAEhD5C,cAAc,CAAC,OAAO0C,CAAE,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAIuB,MAAM,CAACC,aAAa,CAACxB,CAAC,CAAC,IAAI,CAACyB,MAAM,CAACzB,CAAC,CAAC,GAAGyB,MAAM,CAACzB,CAAC,GAAG,CAAC,CAAC,MAAMyB,MAAM,CAAC,CAAC,CAAC,EAAE,4BAA4B,EAAE,WAAW,EAAEzB,CAAC,CAAC;EACrK1C,cAAc,CAAC,OAAO2C,CAAE,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAIsB,MAAM,CAACC,aAAa,CAACvB,CAAC,CAAC,EAAE,4BAA4B,EAAE,WAAW,EAAEA,CAAC,CAAC;EACxH3C,cAAc,CAAC,OAAO4C,CAAE,KAAK,QAAQ,IAAIA,CAAC,GAAG,CAAC,IAAIqB,MAAM,CAACC,aAAa,CAACtB,CAAC,CAAC,EAAE,4BAA4B,EAAE,WAAW,EAAEA,CAAC,CAAC;EAExH,OAAO;IAAEE,IAAI,EAAE,QAAQ;IAAED,KAAK,EAAE,EAAE;IAAEJ,IAAI;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAC,CAAE;AACvD;AAEA,SAASwB,gBAAgBA,CAACtD,GAAe,EAAE0B,GAAiB,EAAEV,OAAwB,EAAEkC,OAAuB;EAE3G,MAAMtC,UAAU,GAAG9B,QAAQ,CAACkC,OAAO,CAACJ,UAAU,EAAE,YAAY,CAAC;EAE7D;EACA,MAAMT,EAAE,GAAI+C,OAAO,CAAC/C,EAAE,IAAI,IAAI,GAAIrB,QAAQ,CAACoE,OAAO,CAAC/C,EAAE,EAAE,YAAY,CAAC,GAAE1B,WAAW,CAAC,EAAE,CAAC;EACrFS,cAAc,CAACiB,EAAE,CAACoD,MAAM,KAAK,EAAE,EAAE,2BAA2B,EAAE,YAAY,EAAEL,OAAO,CAAC/C,EAAE,CAAC;EAEvF;EACA,MAAMqD,UAAU,GAAIN,OAAO,CAACO,IAAI,IAAI,IAAI,GAAI3E,QAAQ,CAACoE,OAAO,CAACO,IAAI,EAAE,cAAc,CAAC,GAAEhF,WAAW,CAAC,EAAE,CAAC;EACnGS,cAAc,CAACsE,UAAU,CAACD,MAAM,KAAK,EAAE,EAAE,6BAA6B,EAAE,cAAc,EAAEL,OAAO,CAAC/C,EAAE,CAAC;EAEnG;EACA;EACA;EACA,MAAMuD,UAAU,GAAG1D,GAAG,CAACS,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EACnC,MAAMkD,SAAS,GAAG3D,GAAG,CAACS,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EAEnC;EACA,MAAML,MAAM,GAAG,IAAI/B,GAAG,CAACqF,UAAU,EAAEvD,EAAE,CAAC;EACtC,MAAMF,UAAU,GAAGnB,QAAQ,CAACsB,MAAM,CAACwD,OAAO,CAAChD,UAAU,CAAC,CAAC;EAEvD;EACA,MAAMiD,GAAG,GAAGtF,SAAS,CAACM,MAAM,CAAC,CAAE8E,SAAS,EAAE1D,UAAU,CAAE,CAAC,CAAC;EAExD;EACA,MAAMP,IAAI,GAA2B;IACjCmB,OAAO,EAAEG,OAAO,CAACH,OAAO,CAACH,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,EAAE;IACnDmD,EAAE,EAAE9E,MAAM,CAACwE,UAAU,CAAC;IACtBlE,OAAO,EAAE,CAAC;IACVyE,MAAM,EAAE;MACJ7D,MAAM,EAAE,aAAa;MACrB8D,YAAY,EAAE;QACV7D,EAAE,EAAEpB,OAAO,CAACoB,EAAE,CAAC,CAACO,SAAS,CAAC,CAAC;OAC9B;MACDT,UAAU,EAAElB,OAAO,CAACkB,UAAU,CAAC,CAACS,SAAS,CAAC,CAAC,CAAC;MAC5CgB,GAAG,EAAE,QAAQ;MACbuC,SAAS,EAAE;QACPtC,IAAI,EAAE5C,OAAO,CAAC2C,GAAG,CAACC,IAAI,CAAC,CAACjB,SAAS,CAAC,CAAC,CAAC;QACpCwD,CAAC,EAAExC,GAAG,CAACE,CAAC;QACRuC,KAAK,EAAE,EAAE;QACTrC,CAAC,EAAEJ,GAAG,CAACI,CAAC;QACRD,CAAC,EAAEH,GAAG,CAACG;OACV;MACDgC,GAAG,EAAEA,GAAG,CAACnD,SAAS,CAAC,CAAC;;GAE3B;EAED;EACA,IAAIM,OAAO,CAACK,QAAQ,EAAE;IAClB,MAAM+C,MAAM,GAAIlB,OAAO,CAACkB,MAAM,IAAI,IAAI,GAAIlB,OAAO,CAACkB,MAAM,GAAE,UAAW9E,OAAQ,EAAE;IAE/E,MAAMgC,IAAI,GAAGN,OAAO,CAACK,QAAQ,CAACC,IAAI,IAAI/B,WAAW;IACjD,MAAMgC,MAAM,GAAGP,OAAO,CAACK,QAAQ,CAACE,MAAM,IAAI,IAAI;IAE9C,MAAMN,WAAW,GAAGjB,GAAG,CAACS,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;IAErC,MAAMe,OAAO,GAAG1C,QAAQ,CAACkC,OAAO,CAACK,QAAQ,CAACG,OAAO,EAAE,0BAA0B,CAAC;IAC9E,MAAML,UAAU,GAAG1C,WAAW,CAAC,EAAE,CAAC;IAClC,MAAM2C,cAAc,GAAG,IAAI/C,GAAG,CAAC4C,WAAW,EAAEE,UAAU,CAAC;IACvD,MAAMD,kBAAkB,GAAGpC,QAAQ,CAACsC,cAAc,CAACwC,OAAO,CAACpC,OAAO,CAAC,CAAC;IAEpE,MAAM6C,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,SAAS,GAAIF,GAAG,CAACG,cAAc,EAAE,GAAG,GAAG,GAC1BnF,IAAI,CAACgF,GAAG,CAACI,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,GAAG,GACpCpF,IAAI,CAACgF,GAAG,CAACK,UAAU,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAC/BrF,IAAI,CAACgF,GAAG,CAACM,WAAW,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAChCtF,IAAI,CAACgF,GAAG,CAACO,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,GAClCvF,IAAI,CAACgF,GAAG,CAACQ,aAAa,EAAE,EAAE,CAAC,CAAC,GAAG,KAAM;IACxD,MAAMC,YAAY,GAAI,OAAO,GAAGP,SAAS,GAAG,IAAI,GAAG7E,IAAI,CAACmB,OAAQ;IAEhEnB,IAAI,CAAC,UAAU,CAAC,GAAG;MACf0E,MAAM;MAAEU,YAAY;MAAExD,IAAI;MAAEC,MAAM;MAClCwD,eAAe,EAAEhG,OAAO,CAACoC,UAAU,CAAC,CAACT,SAAS,CAAC,CAAC,CAAC;MACjDQ,kBAAkB,EAAEnC,OAAO,CAACmC,kBAAkB,CAAC,CAACR,SAAS,CAAC,CAAC,CAAC;MAC5DpB,OAAO,EAAE;KACZ;;EAGL,OAAOK,IAAI,CAACqF,SAAS,CAACtF,IAAI,CAAC;AAC/B;AAEA;;;;;;;;AAQA,OAAM,SAAUuF,uBAAuBA,CAACjE,OAAwB,EAAEwB,QAA6B,EAAEU,OAAwB;EACrH,IAAIA,OAAO,IAAI,IAAI,EAAE;IAAEA,OAAO,GAAG,EAAG;;EAEpC,MAAMgC,aAAa,GAAG/F,WAAW,CAACqD,QAAQ,CAAC;EAC3C,MAAMd,GAAG,GAAGuB,mBAAmB,CAACC,OAAO,CAAC;EACxC,MAAMlD,GAAG,GAAGrB,UAAU,CAACuG,aAAa,EAAExD,GAAG,CAACC,IAAI,EAAED,GAAG,CAACE,CAAC,EAAEF,GAAG,CAACG,CAAC,EAAEH,GAAG,CAACI,CAAC,EAAE,EAAE,CAAC;EACxE,OAAOwB,gBAAgB,CAACxE,QAAQ,CAACkB,GAAG,CAAC,EAAE0B,GAAG,EAAEV,OAAO,EAAEkC,OAAO,CAAC;AACjE;AAEA;;;;;;;;;AASA,OAAO,eAAeiC,mBAAmBA,CAACnE,OAAwB,EAAEwB,QAA6B,EAAEU,OAAwB;EACvH,IAAIA,OAAO,IAAI,IAAI,EAAE;IAAEA,OAAO,GAAG,EAAG;;EAEpC,MAAMgC,aAAa,GAAG/F,WAAW,CAACqD,QAAQ,CAAC;EAC3C,MAAMd,GAAG,GAAGuB,mBAAmB,CAACC,OAAO,CAAC;EACxC,MAAMlD,GAAG,GAAG,MAAMtB,MAAM,CAACwG,aAAa,EAAExD,GAAG,CAACC,IAAI,EAAED,GAAG,CAACE,CAAC,EAAEF,GAAG,CAACG,CAAC,EAAEH,GAAG,CAACI,CAAC,EAAE,EAAE,EAAEoB,OAAO,CAACkC,gBAAgB,CAAC;EACpG,OAAO9B,gBAAgB,CAACxE,QAAQ,CAACkB,GAAG,CAAC,EAAE0B,GAAG,EAAEV,OAAO,EAAEkC,OAAO,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}