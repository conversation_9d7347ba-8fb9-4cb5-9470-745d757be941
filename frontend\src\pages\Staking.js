import React, { useState, useEffect } from 'react';
import { useWeb3 } from '../contexts/Web3Context';
import { useAuth } from '../contexts/AuthContext';
import { 
  CurrencyDollarIcon, 
  ClockIcon,
  TrophyIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const Staking = () => {
  const { user } = useAuth();
  const { getTokenBalance, getStakingInfo, stakeTokens, unstakeTokens, claimStakingRewards } = useWeb3();
  const [tokenBalance, setTokenBalance] = useState('0');
  const [stakingInfo, setStakingInfo] = useState(null);
  const [stakeAmount, setStakeAmount] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStakingData();
  }, [user?.walletAddress]);

  const loadStakingData = async () => {
    if (!user?.walletAddress) return;
    
    try {
      setLoading(true);
      const [balance, info] = await Promise.all([
        getTokenBalance(user.walletAddress),
        getStakingInfo(user.walletAddress)
      ]);
      
      setTokenBalance(balance);
      setStakingInfo(info);
    } catch (error) {
      console.error('Failed to load staking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStake = async () => {
    if (!stakeAmount || parseFloat(stakeAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (parseFloat(stakeAmount) > parseFloat(tokenBalance)) {
      toast.error('Insufficient balance');
      return;
    }

    if (parseFloat(stakeAmount) < 100) {
      toast.error('Minimum stake amount is 100 CQT');
      return;
    }

    try {
      await stakeTokens(stakeAmount);
      setStakeAmount('');
      await loadStakingData();
    } catch (error) {
      console.error('Staking failed:', error);
    }
  };

  const handleUnstake = async () => {
    try {
      await unstakeTokens();
      await loadStakingData();
    } catch (error) {
      console.error('Unstaking failed:', error);
    }
  };

  const handleClaimRewards = async () => {
    try {
      await claimStakingRewards();
      await loadStakingData();
    } catch (error) {
      console.error('Claiming rewards failed:', error);
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp * 1000);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const isUnlockTimeReached = () => {
    if (!stakingInfo?.unlockTime) return false;
    return Date.now() / 1000 >= stakingInfo.unlockTime;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <p className="text-dark-300">Loading staking data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">Staking Pool</h1>
        <p className="text-dark-300">
          Stake your CQT tokens to earn passive rewards and unlock bonuses
        </p>
      </div>

      {/* Staking Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="game-card p-4 text-center">
          <CurrencyDollarIcon className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">{parseFloat(tokenBalance).toFixed(2)}</div>
          <div className="text-sm text-dark-400">Available CQT</div>
        </div>
        <div className="game-card p-4 text-center">
          <TrophyIcon className="w-8 h-8 text-green-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">
            {stakingInfo?.amount ? parseFloat(stakingInfo.amount).toFixed(2) : '0'}
          </div>
          <div className="text-sm text-dark-400">Staked CQT</div>
        </div>
        <div className="game-card p-4 text-center">
          <ArrowUpIcon className="w-8 h-8 text-blue-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">12%</div>
          <div className="text-sm text-dark-400">Annual APY</div>
        </div>
        <div className="game-card p-4 text-center">
          <ClockIcon className="w-8 h-8 text-purple-400 mx-auto mb-2" />
          <div className="text-2xl font-bold text-white">7</div>
          <div className="text-sm text-dark-400">Days Lock Period</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Stake Tokens */}
        <div className="game-card p-6">
          <h2 className="text-2xl font-bold text-white mb-4">Stake Tokens</h2>
          
          {!stakingInfo?.isActive ? (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-white mb-2">
                  Amount to Stake (Minimum: 100 CQT)
                </label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    value={stakeAmount}
                    onChange={(e) => setStakeAmount(e.target.value)}
                    placeholder="Enter amount"
                    className="flex-1 game-input"
                    min="100"
                    max={tokenBalance}
                  />
                  <button
                    onClick={() => setStakeAmount(Math.max(100, parseFloat(tokenBalance)).toString())}
                    className="px-4 py-2 bg-dark-700 text-white rounded-lg hover:bg-dark-600"
                  >
                    Max
                  </button>
                </div>
              </div>
              
              <div className="bg-dark-700 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Staking Benefits</h4>
                <ul className="text-sm text-dark-300 space-y-1">
                  <li>• Earn 12% APY on staked tokens</li>
                  <li>• Get XP boost for battles (1000+ CQT)</li>
                  <li>• Access to exclusive tournaments</li>
                  <li>• Compound rewards automatically</li>
                </ul>
              </div>
              
              <button
                onClick={handleStake}
                disabled={!stakeAmount || parseFloat(stakeAmount) < 100}
                className="w-full game-button flex items-center justify-center space-x-2"
              >
                <ArrowUpIcon className="w-5 h-5" />
                <span>Stake Tokens</span>
              </button>
            </div>
          ) : (
            <div className="text-center py-8">
              <TrophyIcon className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Already Staking</h3>
              <p className="text-dark-300">
                You have {parseFloat(stakingInfo.amount).toFixed(2)} CQT staked
              </p>
            </div>
          )}
        </div>

        {/* Staking Status */}
        <div className="game-card p-6">
          <h2 className="text-2xl font-bold text-white mb-4">Staking Status</h2>
          
          {stakingInfo?.isActive ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-dark-700 rounded-lg">
                  <div className="text-xl font-bold text-green-400">
                    {parseFloat(stakingInfo.amount).toFixed(2)}
                  </div>
                  <div className="text-sm text-dark-400">Staked Amount</div>
                </div>
                <div className="text-center p-3 bg-dark-700 rounded-lg">
                  <div className="text-xl font-bold text-yellow-400">
                    {parseFloat(stakingInfo.pendingRewards).toFixed(4)}
                  </div>
                  <div className="text-sm text-dark-400">Pending Rewards</div>
                </div>
              </div>
              
              <div className="bg-dark-700 rounded-lg p-4">
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-dark-400">Stake Time:</span>
                    <span className="text-white">{formatTime(stakingInfo.stakeTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-dark-400">Unlock Time:</span>
                    <span className="text-white">{formatTime(stakingInfo.unlockTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-dark-400">XP Boost:</span>
                    <span className={stakingInfo.xpBoost ? 'text-green-400' : 'text-red-400'}>
                      {stakingInfo.xpBoost ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <button
                  onClick={handleClaimRewards}
                  disabled={parseFloat(stakingInfo.pendingRewards) <= 0}
                  className="w-full game-button-secondary disabled:opacity-50"
                >
                  Claim Rewards
                </button>
                
                <button
                  onClick={handleUnstake}
                  disabled={!isUnlockTimeReached()}
                  className="w-full game-button flex items-center justify-center space-x-2 disabled:opacity-50"
                >
                  <ArrowDownIcon className="w-5 h-5" />
                  <span>
                    {isUnlockTimeReached() ? 'Unstake Tokens' : 'Locked (7 days)'}
                  </span>
                </button>
              </div>
              
              {!isUnlockTimeReached() && (
                <div className="text-center">
                  <button
                    onClick={handleUnstake}
                    className="text-red-400 hover:text-red-300 text-sm underline"
                  >
                    Emergency Unstake (Forfeit Rewards)
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <ClockIcon className="w-12 h-12 text-dark-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">No Active Stakes</h3>
              <p className="text-dark-300">
                Stake your CQT tokens to start earning rewards
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Staking;
