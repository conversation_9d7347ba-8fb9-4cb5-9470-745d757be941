{"ast": null, "code": "function getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('unable to locate global object');\n}\n;\nconst _WebSocket = getGlobal().WebSocket;\nexport { _WebSocket as WebSocket };", "map": {"version": 3, "names": ["getGlobal", "self", "window", "global", "Error", "_WebSocket", "WebSocket"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\ws-browser.ts"], "sourcesContent": ["\nfunction getGlobal(): any {\n  if (typeof self !== 'undefined') { return self; }\n  if (typeof window !== 'undefined') { return window; }\n  if (typeof global !== 'undefined') { return global; }\n  throw new Error('unable to locate global object');\n};\n\nconst _WebSocket = getGlobal().WebSocket;\n\nexport { _WebSocket as WebSocket };\n"], "mappings": "AACA,SAASA,SAASA,CAAA;EAChB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAAE,OAAOA,IAAI;;EAC9C,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;;EAClD,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAAE,OAAOA,MAAM;;EAClD,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;AACnD;AAAC;AAED,MAAMC,UAAU,GAAGL,SAAS,EAAE,CAACM,SAAS;AAExC,SAASD,UAAU,IAAIC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}