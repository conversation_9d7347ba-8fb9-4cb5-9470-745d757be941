{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { keccak256, Signing<PERSON><PERSON> } from \"../crypto/index.js\";\n/**\n *  Returns the address for the %%key%%.\n *\n *  The key may be any standard form of public key or a private key.\n */\nexport function computeAddress(key) {\n  let pubkey;\n  if (typeof key === \"string\") {\n    pubkey = SigningKey.computePublicKey(key, false);\n  } else {\n    pubkey = key.publicKey;\n  }\n  return getAddress(keccak256(\"0x\" + pubkey.substring(4)).substring(26));\n}\n/**\n *  Returns the recovered address for the private key that was\n *  used to sign %%digest%% that resulted in %%signature%%.\n */\nexport function recoverAddress(digest, signature) {\n  return computeAddress(SigningKey.recoverPublicKey(digest, signature));\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "keccak256", "SigningKey", "computeAddress", "key", "pubkey", "computePublicKey", "public<PERSON>ey", "substring", "recoverAddress", "digest", "signature", "recoverPublicKey"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\transaction\\address.ts"], "sourcesContent": ["import { getAddress } from \"../address/index.js\";\nimport { keccak256, Signing<PERSON><PERSON> } from \"../crypto/index.js\";\n\nimport type { SignatureLike } from \"../crypto/index.js\";\nimport type { BytesLike } from \"../utils/index.js\";\n\n/**\n *  Returns the address for the %%key%%.\n *\n *  The key may be any standard form of public key or a private key.\n */\nexport function computeAddress(key: string | SigningKey): string {\n    let pubkey: string;\n    if (typeof(key) === \"string\") {\n        pubkey = SigningKey.computePublicKey(key, false);\n    } else {\n        pubkey = key.publicKey;\n    }\n    return getAddress(keccak256(\"0x\" + pubkey.substring(4)).substring(26));\n}\n\n/**\n *  Returns the recovered address for the private key that was\n *  used to sign %%digest%% that resulted in %%signature%%.\n */\nexport function recoverAddress(digest: BytesLike, signature: SignatureLike): string {\n    return computeAddress(SigningKey.recoverPublicKey(digest, signature));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,SAAS,EAAEC,UAAU,QAAQ,oBAAoB;AAK1D;;;;;AAKA,OAAM,SAAUC,cAAcA,CAACC,GAAwB;EACnD,IAAIC,MAAc;EAClB,IAAI,OAAOD,GAAI,KAAK,QAAQ,EAAE;IAC1BC,MAAM,GAAGH,UAAU,CAACI,gBAAgB,CAACF,GAAG,EAAE,KAAK,CAAC;GACnD,MAAM;IACHC,MAAM,GAAGD,GAAG,CAACG,SAAS;;EAE1B,OAAOP,UAAU,CAACC,SAAS,CAAC,IAAI,GAAGI,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,CAACA,SAAS,CAAC,EAAE,CAAC,CAAC;AAC1E;AAEA;;;;AAIA,OAAM,SAAUC,cAAcA,CAACC,MAAiB,EAAEC,SAAwB;EACtE,OAAOR,cAAc,CAACD,UAAU,CAACU,gBAAgB,CAACF,MAAM,EAAEC,SAAS,CAAC,CAAC;AACzE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}