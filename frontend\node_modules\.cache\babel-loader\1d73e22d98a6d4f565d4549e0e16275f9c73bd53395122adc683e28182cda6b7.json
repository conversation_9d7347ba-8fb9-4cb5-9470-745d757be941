{"ast": null, "code": "/**\n *  [[link-chainstack]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Arbitrum (``arbitrum``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - Polygon (``matic``)\n *\n *  @_subsection: api/providers/thirdparty:Chainstack  [providers-chainstack]\n */\nimport { defineProperties, FetchRequest, assertArgument } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nfunction getApiKey(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"39f1d67cedf8b7831010a665328c9197\";\n    case \"arbitrum\":\n      return \"0550c209db33c3abf4cc927e1e18cea1\";\n    case \"bnb\":\n      return \"98b5a77e531614387366f6fc5da097f8\";\n    case \"matic\":\n      return \"cd9d4d70377471aa7c142ec4a4205249\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"ethereum-mainnet.core.chainstack.com\";\n    case \"arbitrum\":\n      return \"arbitrum-mainnet.core.chainstack.com\";\n    case \"bnb\":\n      return \"bsc-mainnet.core.chainstack.com\";\n    case \"matic\":\n      return \"polygon-mainnet.core.chainstack.com\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **ChainstackProvider** connects to the [[link-chainstack]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-chainstack).\n */\nexport class ChainstackProvider extends JsonRpcProvider {\n  /**\n   *  The API key for the Chainstack connection.\n   */\n  apiKey;\n  /**\n   *  Creates a new **ChainstackProvider**.\n   */\n  constructor(_network, apiKey) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (apiKey == null) {\n      apiKey = getApiKey(network.name);\n    }\n    const request = ChainstackProvider.getRequest(network, apiKey);\n    super(request, network, {\n      staticNetwork: network\n    });\n    defineProperties(this, {\n      apiKey\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new ChainstackProvider(chainId, this.apiKey);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  isCommunityResource() {\n    return this.apiKey === getApiKey(this._network.name);\n  }\n  /**\n   *  Returns a prepared request for connecting to %%network%%\n   *  with %%apiKey%% and %%projectSecret%%.\n   */\n  static getRequest(network, apiKey) {\n    if (apiKey == null) {\n      apiKey = getApiKey(network.name);\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/${apiKey}`);\n    request.allowGzip = true;\n    if (apiKey === getApiKey(network.name)) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"ChainstackProvider\");\n        return true;\n      };\n    }\n    return request;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "FetchRequest", "assertArgument", "showThrottleMessage", "Network", "JsonRpcProvider", "getApi<PERSON>ey", "name", "getHost", "ChainstackProvider", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_network", "network", "from", "request", "getRequest", "staticNetwork", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "isCommunityResource", "allowGzip", "retryFunc", "response", "attempt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-chainstack.ts"], "sourcesContent": ["/**\n *  [[link-chainstack]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Arbitrum (``arbitrum``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - Polygon (``matic``)\n *\n *  @_subsection: api/providers/thirdparty:Chainstack  [providers-chainstack]\n */\nimport {\n    defineProperties, FetchRequest, assertArgument\n} from \"../utils/index.js\";\n\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { AbstractProvider } from \"./abstract-provider.js\";\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\n\n\nfunction getApiKey(name: string): string {\n    switch (name) {\n        case \"mainnet\": return \"39f1d67cedf8b7831010a665328c9197\";\n        case \"arbitrum\": return \"0550c209db33c3abf4cc927e1e18cea1\"\n        case \"bnb\": return \"98b5a77e531614387366f6fc5da097f8\";\n        case \"matic\": return \"cd9d4d70377471aa7c142ec4a4205249\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\nfunction getHost(name: string): string {\n    switch(name) {\n        case \"mainnet\":\n            return \"ethereum-mainnet.core.chainstack.com\";\n        case \"arbitrum\":\n            return \"arbitrum-mainnet.core.chainstack.com\";\n        case \"bnb\":\n            return \"bsc-mainnet.core.chainstack.com\";\n        case \"matic\":\n            return \"polygon-mainnet.core.chainstack.com\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n/**\n *  The **ChainstackProvider** connects to the [[link-chainstack]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-chainstack).\n */\nexport class ChainstackProvider extends JsonRpcProvider implements CommunityResourcable {\n    /**\n     *  The API key for the Chainstack connection.\n     */\n    readonly apiKey!: string;\n\n    /**\n     *  Creates a new **ChainstackProvider**.\n     */\n    constructor(_network?: Networkish, apiKey?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n\n        if (apiKey == null) { apiKey = getApiKey(network.name); }\n\n        const request = ChainstackProvider.getRequest(network, apiKey);\n        super(request, network, { staticNetwork: network });\n\n        defineProperties<ChainstackProvider>(this, { apiKey });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new ChainstackProvider(chainId, this.apiKey);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.apiKey === getApiKey(this._network.name));\n    }\n\n    /**\n     *  Returns a prepared request for connecting to %%network%%\n     *  with %%apiKey%% and %%projectSecret%%.\n     */\n    static getRequest(network: Network, apiKey?: null | string): FetchRequest {\n        if (apiKey == null) { apiKey = getApiKey(network.name); }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/${ apiKey }`);\n        request.allowGzip = true;\n\n        if (apiKey === getApiKey(network.name)) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"ChainstackProvider\");\n                return true;\n            };\n        }\n\n        return request;\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAaA,SACIA,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAC3C,mBAAmB;AAE1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAOvD,SAASC,SAASA,CAACC,IAAY;EAC3B,QAAQA,IAAI;IACR,KAAK,SAAS;MAAE,OAAO,kCAAkC;IACzD,KAAK,UAAU;MAAE,OAAO,kCAAkC;IAC1D,KAAK,KAAK;MAAE,OAAO,kCAAkC;IACrD,KAAK,OAAO;MAAE,OAAO,kCAAkC;;EAG3DL,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEK,IAAI,CAAC;AACjE;AAEA,SAASC,OAAOA,CAACD,IAAY;EACzB,QAAOA,IAAI;IACP,KAAK,SAAS;MACV,OAAO,sCAAsC;IACjD,KAAK,UAAU;MACX,OAAO,sCAAsC;IACjD,KAAK,KAAK;MACN,OAAO,iCAAiC;IAC5C,KAAK,OAAO;MACR,OAAO,qCAAqC;;EAGpDL,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEK,IAAI,CAAC;AACjE;AAEA;;;;;;;;;AASA,OAAM,MAAOE,kBAAmB,SAAQJ,eAAe;EACnD;;;EAGSK,MAAM;EAEf;;;EAGAC,YAAYC,QAAqB,EAAEF,MAAsB;IACrD,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGT,OAAO,CAACU,IAAI,CAACF,QAAQ,CAAC;IAEtC,IAAIF,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,SAAS,CAACO,OAAO,CAACN,IAAI,CAAC;;IAEtD,MAAMQ,OAAO,GAAGN,kBAAkB,CAACO,UAAU,CAACH,OAAO,EAAEH,MAAM,CAAC;IAC9D,KAAK,CAACK,OAAO,EAAEF,OAAO,EAAE;MAAEI,aAAa,EAAEJ;IAAO,CAAE,CAAC;IAEnDb,gBAAgB,CAAqB,IAAI,EAAE;MAAEU;IAAM,CAAE,CAAC;EAC1D;EAEAQ,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIV,kBAAkB,CAACU,OAAO,EAAE,IAAI,CAACT,MAAM,CAAC;KACtD,CAAC,OAAOU,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEAE,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACX,MAAM,KAAKJ,SAAS,CAAC,IAAI,CAACM,QAAQ,CAACL,IAAI,CAAC;EACzD;EAEA;;;;EAIA,OAAOS,UAAUA,CAACH,OAAgB,EAAEH,MAAsB;IACtD,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,SAAS,CAACO,OAAO,CAACN,IAAI,CAAC;;IAEtD,MAAMQ,OAAO,GAAG,IAAId,YAAY,CAAC,YAAaO,OAAO,CAACK,OAAO,CAACN,IAAI,CAAE,IAAKG,MAAO,EAAE,CAAC;IACnFK,OAAO,CAACO,SAAS,GAAG,IAAI;IAExB,IAAIZ,MAAM,KAAKJ,SAAS,CAACO,OAAO,CAACN,IAAI,CAAC,EAAE;MACpCQ,OAAO,CAACQ,SAAS,GAAG,OAAOR,OAAO,EAAES,QAAQ,EAAEC,OAAO,KAAI;QACrDtB,mBAAmB,CAAC,oBAAoB,CAAC;QACzC,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOY,OAAO;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}