{"ast": null, "code": "/**\n *  Generic long-lived socket provider.\n *\n *  Sub-classing notes\n *  - a sub-class MUST call the `_start()` method once connected\n *  - a sub-class MUST override the `_write(string)` method\n *  - a sub-class MUST call `_processMessage(string)` for each message\n *\n *  @_subsection: api/providers/abstract-provider:Socket Providers  [about-socketProvider]\n */\nimport { UnmanagedSubscriber } from \"./abstract-provider.js\";\nimport { assert, assertArgument, makeError } from \"../utils/index.js\";\nimport { JsonRpcApiProvider } from \"./provider-jsonrpc.js\";\n/**\n *  A **SocketSubscriber** uses a socket transport to handle events and\n *  should use [[_emit]] to manage the events.\n */\nexport class SocketSubscriber {\n  #provider;\n  #filter;\n  /**\n   *  The filter.\n   */\n  get filter() {\n    return JSON.parse(this.#filter);\n  }\n  #filterId;\n  #paused;\n  #emitPromise;\n  /**\n   *  Creates a new **SocketSubscriber** attached to %%provider%% listening\n   *  to %%filter%%.\n   */\n  constructor(provider, filter) {\n    this.#provider = provider;\n    this.#filter = JSON.stringify(filter);\n    this.#filterId = null;\n    this.#paused = null;\n    this.#emitPromise = null;\n  }\n  start() {\n    this.#filterId = this.#provider.send(\"eth_subscribe\", this.filter).then(filterId => {\n      ;\n      this.#provider._register(filterId, this);\n      return filterId;\n    });\n  }\n  stop() {\n    this.#filterId.then(filterId => {\n      if (this.#provider.destroyed) {\n        return;\n      }\n      this.#provider.send(\"eth_unsubscribe\", [filterId]);\n    });\n    this.#filterId = null;\n  }\n  // @TODO: pause should trap the current blockNumber, unsub, and on resume use getLogs\n  //        and resume\n  pause(dropWhilePaused) {\n    assert(dropWhilePaused, \"preserve logs while paused not supported by SocketSubscriber yet\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"pause(false)\"\n    });\n    this.#paused = !!dropWhilePaused;\n  }\n  resume() {\n    this.#paused = null;\n  }\n  /**\n   *  @_ignore:\n   */\n  _handleMessage(message) {\n    if (this.#filterId == null) {\n      return;\n    }\n    if (this.#paused === null) {\n      let emitPromise = this.#emitPromise;\n      if (emitPromise == null) {\n        emitPromise = this._emit(this.#provider, message);\n      } else {\n        emitPromise = emitPromise.then(async () => {\n          await this._emit(this.#provider, message);\n        });\n      }\n      this.#emitPromise = emitPromise.then(() => {\n        if (this.#emitPromise === emitPromise) {\n          this.#emitPromise = null;\n        }\n      });\n    }\n  }\n  /**\n   *  Sub-classes **must** override this to emit the events on the\n   *  provider.\n   */\n  async _emit(provider, message) {\n    throw new Error(\"sub-classes must implemente this; _emit\");\n  }\n}\n/**\n *  A **SocketBlockSubscriber** listens for ``newHeads`` events and emits\n *  ``\"block\"`` events.\n */\nexport class SocketBlockSubscriber extends SocketSubscriber {\n  /**\n   *  @_ignore:\n   */\n  constructor(provider) {\n    super(provider, [\"newHeads\"]);\n  }\n  async _emit(provider, message) {\n    provider.emit(\"block\", parseInt(message.number));\n  }\n}\n/**\n *  A **SocketPendingSubscriber** listens for pending transacitons and emits\n *  ``\"pending\"`` events.\n */\nexport class SocketPendingSubscriber extends SocketSubscriber {\n  /**\n   *  @_ignore:\n   */\n  constructor(provider) {\n    super(provider, [\"newPendingTransactions\"]);\n  }\n  async _emit(provider, message) {\n    provider.emit(\"pending\", message);\n  }\n}\n/**\n *  A **SocketEventSubscriber** listens for event logs.\n */\nexport class SocketEventSubscriber extends SocketSubscriber {\n  #logFilter;\n  /**\n   *  The filter.\n   */\n  get logFilter() {\n    return JSON.parse(this.#logFilter);\n  }\n  /**\n   *  @_ignore:\n   */\n  constructor(provider, filter) {\n    super(provider, [\"logs\", filter]);\n    this.#logFilter = JSON.stringify(filter);\n  }\n  async _emit(provider, message) {\n    provider.emit(this.logFilter, provider._wrapLog(message, provider._network));\n  }\n}\n/**\n *  A **SocketProvider** is backed by a long-lived connection over a\n *  socket, which can subscribe and receive real-time messages over\n *  its communication channel.\n */\nexport class SocketProvider extends JsonRpcApiProvider {\n  #callbacks;\n  // Maps each filterId to its subscriber\n  #subs;\n  // If any events come in before a subscriber has finished\n  // registering, queue them\n  #pending;\n  /**\n   *  Creates a new **SocketProvider** connected to %%network%%.\n   *\n   *  If unspecified, the network will be discovered.\n   */\n  constructor(network, _options) {\n    // Copy the options\n    const options = Object.assign({}, _options != null ? _options : {});\n    // Support for batches is generally not supported for\n    // connection-base providers; if this changes in the future\n    // the _send should be updated to reflect this\n    assertArgument(options.batchMaxCount == null || options.batchMaxCount === 1, \"sockets-based providers do not support batches\", \"options.batchMaxCount\", _options);\n    options.batchMaxCount = 1;\n    // Socket-based Providers (generally) cannot change their network,\n    // since they have a long-lived connection; but let people override\n    // this if they have just cause.\n    if (options.staticNetwork == null) {\n      options.staticNetwork = true;\n    }\n    super(network, options);\n    this.#callbacks = new Map();\n    this.#subs = new Map();\n    this.#pending = new Map();\n  }\n  // This value is only valid after _start has been called\n  /*\n  get _network(): Network {\n      if (this.#network == null) {\n          throw new Error(\"this shouldn't happen\");\n      }\n      return this.#network.clone();\n  }\n  */\n  _getSubscriber(sub) {\n    switch (sub.type) {\n      case \"close\":\n        return new UnmanagedSubscriber(\"close\");\n      case \"block\":\n        return new SocketBlockSubscriber(this);\n      case \"pending\":\n        return new SocketPendingSubscriber(this);\n      case \"event\":\n        return new SocketEventSubscriber(this, sub.filter);\n      case \"orphan\":\n        // Handled auto-matically within AbstractProvider\n        // when the log.removed = true\n        if (sub.filter.orphan === \"drop-log\") {\n          return new UnmanagedSubscriber(\"drop-log\");\n        }\n    }\n    return super._getSubscriber(sub);\n  }\n  /**\n   *  Register a new subscriber. This is used internalled by Subscribers\n   *  and generally is unecessary unless extending capabilities.\n   */\n  _register(filterId, subscriber) {\n    this.#subs.set(filterId, subscriber);\n    const pending = this.#pending.get(filterId);\n    if (pending) {\n      for (const message of pending) {\n        subscriber._handleMessage(message);\n      }\n      this.#pending.delete(filterId);\n    }\n  }\n  async _send(payload) {\n    // WebSocket provider doesn't accept batches\n    assertArgument(!Array.isArray(payload), \"WebSocket does not support batch send\", \"payload\", payload);\n    // @TODO: stringify payloads here and store to prevent mutations\n    // Prepare a promise to respond to\n    const promise = new Promise((resolve, reject) => {\n      this.#callbacks.set(payload.id, {\n        payload,\n        resolve,\n        reject\n      });\n    });\n    // Wait until the socket is connected before writing to it\n    await this._waitUntilReady();\n    // Write the request to the socket\n    await this._write(JSON.stringify(payload));\n    return [await promise];\n  }\n  // Sub-classes must call this once they are connected\n  /*\n  async _start(): Promise<void> {\n      if (this.#ready) { return; }\n       for (const { payload } of this.#callbacks.values()) {\n          await this._write(JSON.stringify(payload));\n      }\n       this.#ready = (async function() {\n          await super._start();\n      })();\n  }\n  */\n  /**\n   *  Sub-classes **must** call this with messages received over their\n   *  transport to be processed and dispatched.\n   */\n  async _processMessage(message) {\n    const result = JSON.parse(message);\n    if (result && typeof result === \"object\" && \"id\" in result) {\n      const callback = this.#callbacks.get(result.id);\n      if (callback == null) {\n        this.emit(\"error\", makeError(\"received result for unknown id\", \"UNKNOWN_ERROR\", {\n          reasonCode: \"UNKNOWN_ID\",\n          result\n        }));\n        return;\n      }\n      this.#callbacks.delete(result.id);\n      callback.resolve(result);\n    } else if (result && result.method === \"eth_subscription\") {\n      const filterId = result.params.subscription;\n      const subscriber = this.#subs.get(filterId);\n      if (subscriber) {\n        subscriber._handleMessage(result.params.result);\n      } else {\n        let pending = this.#pending.get(filterId);\n        if (pending == null) {\n          pending = [];\n          this.#pending.set(filterId, pending);\n        }\n        pending.push(result.params.result);\n      }\n    } else {\n      this.emit(\"error\", makeError(\"received unexpected message\", \"UNKNOWN_ERROR\", {\n        reasonCode: \"UNEXPECTED_MESSAGE\",\n        result\n      }));\n      return;\n    }\n  }\n  /**\n   *  Sub-classes **must** override this to send %%message%% over their\n   *  transport.\n   */\n  async _write(message) {\n    throw new Error(\"sub-classes must override this\");\n  }\n}", "map": {"version": 3, "names": ["UnmanagedSubscriber", "assert", "assertArgument", "makeError", "JsonRpcApiProvider", "SocketSubscriber", "provider", "filter", "JSON", "parse", "filterId", "paused", "emitPromise", "constructor", "stringify", "start", "send", "then", "_register", "stop", "destroyed", "pause", "dropWhilePaused", "operation", "resume", "_handleMessage", "message", "_emit", "Error", "SocketBlockSubscriber", "emit", "parseInt", "number", "SocketPendingSubscriber", "SocketEventSubscriber", "logFilter", "_wrapLog", "_network", "SocketProvider", "callbacks", "subs", "pending", "network", "_options", "options", "Object", "assign", "batchMaxCount", "staticNetwork", "Map", "_getSubscriber", "sub", "type", "orphan", "subscriber", "set", "get", "delete", "_send", "payload", "Array", "isArray", "promise", "Promise", "resolve", "reject", "id", "_waitUntilReady", "_write", "_processMessage", "result", "callback", "reasonCode", "method", "params", "subscription", "push"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-socket.ts"], "sourcesContent": ["/**\n *  Generic long-lived socket provider.\n *\n *  Sub-classing notes\n *  - a sub-class MUST call the `_start()` method once connected\n *  - a sub-class MUST override the `_write(string)` method\n *  - a sub-class MUST call `_processMessage(string)` for each message\n *\n *  @_subsection: api/providers/abstract-provider:Socket Providers  [about-socketProvider]\n */\n\nimport { UnmanagedSubscriber } from \"./abstract-provider.js\";\nimport { assert, assertArgument, makeError } from \"../utils/index.js\";\nimport { JsonRpcApiProvider } from \"./provider-jsonrpc.js\";\n\nimport type { Subscriber, Subscription } from \"./abstract-provider.js\";\nimport type { EventFilter } from \"./provider.js\";\nimport type {\n    JsonRpcApiProviderOptions, JsonRpcError, JsonRpcPayload, JsonRpcResult\n} from \"./provider-jsonrpc.js\";\nimport type { Networkish } from \"./network.js\";\n\n\ntype JsonRpcSubscription = {\n    method: string,\n    params: {\n        result: any,\n        subscription: string\n    }\n};\n\n/**\n *  A **SocketSubscriber** uses a socket transport to handle events and\n *  should use [[_emit]] to manage the events.\n */\nexport class SocketSubscriber implements Subscriber {\n    #provider: SocketProvider;\n\n    #filter: string;\n\n    /**\n     *  The filter.\n     */\n    get filter(): Array<any> { return JSON.parse(this.#filter); }\n\n    #filterId: null | Promise<string |number>;\n    #paused: null | boolean;\n\n    #emitPromise: null | Promise<void>;\n\n    /**\n     *  Creates a new **SocketSubscriber** attached to %%provider%% listening\n     *  to %%filter%%.\n     */\n    constructor(provider: SocketProvider, filter: Array<any>) {\n        this.#provider = provider;\n        this.#filter = JSON.stringify(filter);\n        this.#filterId = null;\n        this.#paused = null;\n        this.#emitPromise = null;\n    }\n\n    start(): void {\n        this.#filterId = this.#provider.send(\"eth_subscribe\", this.filter).then((filterId) => {;\n            this.#provider._register(filterId, this);\n            return filterId;\n        });\n    }\n\n    stop(): void {\n        (<Promise<number>>(this.#filterId)).then((filterId) => {\n            if (this.#provider.destroyed) { return; }\n            this.#provider.send(\"eth_unsubscribe\", [ filterId ]);\n        });\n        this.#filterId = null;\n    }\n\n    // @TODO: pause should trap the current blockNumber, unsub, and on resume use getLogs\n    //        and resume\n    pause(dropWhilePaused?: boolean): void {\n        assert(dropWhilePaused, \"preserve logs while paused not supported by SocketSubscriber yet\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"pause(false)\" });\n        this.#paused = !!dropWhilePaused;\n    }\n\n    resume(): void {\n        this.#paused = null;\n    }\n\n    /**\n     *  @_ignore:\n     */\n    _handleMessage(message: any): void {\n        if (this.#filterId == null) { return; }\n        if (this.#paused === null) {\n            let emitPromise: null | Promise<void> = this.#emitPromise;\n            if (emitPromise == null) {\n                emitPromise = this._emit(this.#provider, message);\n            } else {\n                emitPromise = emitPromise.then(async () => {\n                    await this._emit(this.#provider, message);\n                });\n            }\n            this.#emitPromise = emitPromise.then(() => {\n                if (this.#emitPromise === emitPromise) {\n                    this.#emitPromise = null;\n                }\n            });\n        }\n    }\n\n    /**\n     *  Sub-classes **must** override this to emit the events on the\n     *  provider.\n     */\n    async _emit(provider: SocketProvider, message: any): Promise<void> {\n        throw new Error(\"sub-classes must implemente this; _emit\");\n    }\n}\n\n/**\n *  A **SocketBlockSubscriber** listens for ``newHeads`` events and emits\n *  ``\"block\"`` events.\n */\nexport class SocketBlockSubscriber extends SocketSubscriber {\n    /**\n     *  @_ignore:\n     */\n    constructor(provider: SocketProvider) {\n        super(provider, [ \"newHeads\" ]);\n    }\n\n    async _emit(provider: SocketProvider, message: any): Promise<void> {\n        provider.emit(\"block\", parseInt(message.number));\n    }\n}\n\n/**\n *  A **SocketPendingSubscriber** listens for pending transacitons and emits\n *  ``\"pending\"`` events.\n */\nexport class SocketPendingSubscriber extends SocketSubscriber {\n\n    /**\n     *  @_ignore:\n     */\n    constructor(provider: SocketProvider) {\n        super(provider, [ \"newPendingTransactions\" ]);\n    }\n\n    async _emit(provider: SocketProvider, message: any): Promise<void> {\n        provider.emit(\"pending\", message);\n    }\n}\n\n/**\n *  A **SocketEventSubscriber** listens for event logs.\n */\nexport class SocketEventSubscriber extends SocketSubscriber {\n    #logFilter: string;\n\n    /**\n     *  The filter.\n     */\n    get logFilter(): EventFilter { return JSON.parse(this.#logFilter); }\n\n    /**\n     *  @_ignore:\n     */\n    constructor(provider: SocketProvider, filter: EventFilter) {\n        super(provider, [ \"logs\", filter ]);\n        this.#logFilter = JSON.stringify(filter);\n    }\n\n    async _emit(provider: SocketProvider, message: any): Promise<void> {\n        provider.emit(this.logFilter, provider._wrapLog(message, provider._network));\n    }\n}\n\n/**\n *  A **SocketProvider** is backed by a long-lived connection over a\n *  socket, which can subscribe and receive real-time messages over\n *  its communication channel.\n */\nexport class SocketProvider extends JsonRpcApiProvider {\n    #callbacks: Map<number, { payload: JsonRpcPayload, resolve: (r: any) => void, reject: (e: Error) => void }>;\n\n    // Maps each filterId to its subscriber\n    #subs: Map<number | string, SocketSubscriber>;\n\n    // If any events come in before a subscriber has finished\n    // registering, queue them\n    #pending: Map<number | string, Array<any>>;\n\n    /**\n     *  Creates a new **SocketProvider** connected to %%network%%.\n     *\n     *  If unspecified, the network will be discovered.\n     */\n    constructor(network?: Networkish, _options?: JsonRpcApiProviderOptions) {\n        // Copy the options\n        const options = Object.assign({ }, (_options != null) ? _options: { });\n\n        // Support for batches is generally not supported for\n        // connection-base providers; if this changes in the future\n        // the _send should be updated to reflect this\n        assertArgument(options.batchMaxCount == null || options.batchMaxCount === 1,\n            \"sockets-based providers do not support batches\", \"options.batchMaxCount\", _options);\n        options.batchMaxCount = 1;\n\n        // Socket-based Providers (generally) cannot change their network,\n        // since they have a long-lived connection; but let people override\n        // this if they have just cause.\n        if (options.staticNetwork == null) { options.staticNetwork = true; }\n\n        super(network, options);\n        this.#callbacks = new Map();\n        this.#subs = new Map();\n        this.#pending = new Map();\n    }\n\n    // This value is only valid after _start has been called\n    /*\n    get _network(): Network {\n        if (this.#network == null) {\n            throw new Error(\"this shouldn't happen\");\n        }\n        return this.#network.clone();\n    }\n    */\n\n    _getSubscriber(sub: Subscription): Subscriber {\n        switch (sub.type) {\n            case \"close\":\n                return new UnmanagedSubscriber(\"close\");\n            case \"block\":\n                return new SocketBlockSubscriber(this);\n            case \"pending\":\n                return new SocketPendingSubscriber(this);\n            case \"event\":\n                return new SocketEventSubscriber(this, sub.filter);\n            case \"orphan\":\n                // Handled auto-matically within AbstractProvider\n                // when the log.removed = true\n                if (sub.filter.orphan === \"drop-log\") {\n                    return new UnmanagedSubscriber(\"drop-log\");\n                }\n        }\n        return super._getSubscriber(sub);\n    }\n\n    /**\n     *  Register a new subscriber. This is used internalled by Subscribers\n     *  and generally is unecessary unless extending capabilities.\n     */\n    _register(filterId: number | string, subscriber: SocketSubscriber): void {\n        this.#subs.set(filterId, subscriber);\n        const pending = this.#pending.get(filterId);\n        if (pending) {\n            for (const message of pending) {\n                subscriber._handleMessage(message);\n            }\n            this.#pending.delete(filterId);\n        }\n    }\n\n    async _send(payload: JsonRpcPayload | Array<JsonRpcPayload>): Promise<Array<JsonRpcResult | JsonRpcError>> {\n        // WebSocket provider doesn't accept batches\n        assertArgument(!Array.isArray(payload), \"WebSocket does not support batch send\", \"payload\", payload);\n\n        // @TODO: stringify payloads here and store to prevent mutations\n\n        // Prepare a promise to respond to\n        const promise = new Promise((resolve, reject) => {\n            this.#callbacks.set(payload.id, { payload, resolve, reject });\n        });\n\n        // Wait until the socket is connected before writing to it\n        await this._waitUntilReady();\n\n        // Write the request to the socket\n        await this._write(JSON.stringify(payload));\n\n        return <Array<JsonRpcResult | JsonRpcError>>[ await promise ];\n    }\n\n    // Sub-classes must call this once they are connected\n    /*\n    async _start(): Promise<void> {\n        if (this.#ready) { return; }\n\n        for (const { payload } of this.#callbacks.values()) {\n            await this._write(JSON.stringify(payload));\n        }\n\n        this.#ready = (async function() {\n            await super._start();\n        })();\n    }\n    */\n\n    /**\n     *  Sub-classes **must** call this with messages received over their\n     *  transport to be processed and dispatched.\n     */\n    async _processMessage(message: string): Promise<void> {\n        const result = <JsonRpcResult | JsonRpcError | JsonRpcSubscription>(JSON.parse(message));\n\n        if (result && typeof(result) === \"object\" && \"id\" in result) {\n            const callback = this.#callbacks.get(result.id);\n            if (callback == null) {\n                this.emit(\"error\", makeError(\"received result for unknown id\", \"UNKNOWN_ERROR\", {\n                    reasonCode: \"UNKNOWN_ID\",\n                    result\n                }));\n                return;\n            }\n            this.#callbacks.delete(result.id);\n\n            callback.resolve(result);\n\n        } else if (result && result.method === \"eth_subscription\") {\n            const filterId = result.params.subscription;\n            const subscriber = this.#subs.get(filterId);\n            if (subscriber) {\n                subscriber._handleMessage(result.params.result);\n            } else {\n                let pending = this.#pending.get(filterId);\n                if (pending == null) {\n                    pending = [ ];\n                    this.#pending.set(filterId, pending);\n                }\n                pending.push(result.params.result);\n            }\n\n        } else {\n            this.emit(\"error\", makeError(\"received unexpected message\", \"UNKNOWN_ERROR\", {\n                reasonCode: \"UNEXPECTED_MESSAGE\",\n                result\n            }));\n            return;\n        }\n    }\n\n    /**\n     *  Sub-classes **must** override this to send %%message%% over their\n     *  transport.\n     */\n    async _write(message: string): Promise<void> {\n        throw new Error(\"sub-classes must override this\");\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;AAWA,SAASA,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,MAAM,EAAEC,cAAc,EAAEC,SAAS,QAAQ,mBAAmB;AACrE,SAASC,kBAAkB,QAAQ,uBAAuB;AAkB1D;;;;AAIA,OAAM,MAAOC,gBAAgB;EACzB,CAAAC,QAAS;EAET,CAAAC,MAAO;EAEP;;;EAGA,IAAIA,MAAMA,CAAA;IAAiB,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,CAAAF,MAAO,CAAC;EAAE;EAE5D,CAAAG,QAAS;EACT,CAAAC,MAAO;EAEP,CAAAC,WAAY;EAEZ;;;;EAIAC,YAAYP,QAAwB,EAAEC,MAAkB;IACpD,IAAI,CAAC,CAAAD,QAAS,GAAGA,QAAQ;IACzB,IAAI,CAAC,CAAAC,MAAO,GAAGC,IAAI,CAACM,SAAS,CAACP,MAAM,CAAC;IACrC,IAAI,CAAC,CAAAG,QAAS,GAAG,IAAI;IACrB,IAAI,CAAC,CAAAC,MAAO,GAAG,IAAI;IACnB,IAAI,CAAC,CAAAC,WAAY,GAAG,IAAI;EAC5B;EAEAG,KAAKA,CAAA;IACD,IAAI,CAAC,CAAAL,QAAS,GAAG,IAAI,CAAC,CAAAJ,QAAS,CAACU,IAAI,CAAC,eAAe,EAAE,IAAI,CAACT,MAAM,CAAC,CAACU,IAAI,CAAEP,QAAQ,IAAI;MAAE;MACnF,IAAI,CAAC,CAAAJ,QAAS,CAACY,SAAS,CAACR,QAAQ,EAAE,IAAI,CAAC;MACxC,OAAOA,QAAQ;IACnB,CAAC,CAAC;EACN;EAEAS,IAAIA,CAAA;IACmB,IAAI,CAAC,CAAAT,QAAS,CAAGO,IAAI,CAAEP,QAAQ,IAAI;MAClD,IAAI,IAAI,CAAC,CAAAJ,QAAS,CAACc,SAAS,EAAE;QAAE;;MAChC,IAAI,CAAC,CAAAd,QAAS,CAACU,IAAI,CAAC,iBAAiB,EAAE,CAAEN,QAAQ,CAAE,CAAC;IACxD,CAAC,CAAC;IACF,IAAI,CAAC,CAAAA,QAAS,GAAG,IAAI;EACzB;EAEA;EACA;EACAW,KAAKA,CAACC,eAAyB;IAC3BrB,MAAM,CAACqB,eAAe,EAAE,kEAAkE,EACtF,uBAAuB,EAAE;MAAEC,SAAS,EAAE;IAAc,CAAE,CAAC;IAC3D,IAAI,CAAC,CAAAZ,MAAO,GAAG,CAAC,CAACW,eAAe;EACpC;EAEAE,MAAMA,CAAA;IACF,IAAI,CAAC,CAAAb,MAAO,GAAG,IAAI;EACvB;EAEA;;;EAGAc,cAAcA,CAACC,OAAY;IACvB,IAAI,IAAI,CAAC,CAAAhB,QAAS,IAAI,IAAI,EAAE;MAAE;;IAC9B,IAAI,IAAI,CAAC,CAAAC,MAAO,KAAK,IAAI,EAAE;MACvB,IAAIC,WAAW,GAAyB,IAAI,CAAC,CAAAA,WAAY;MACzD,IAAIA,WAAW,IAAI,IAAI,EAAE;QACrBA,WAAW,GAAG,IAAI,CAACe,KAAK,CAAC,IAAI,CAAC,CAAArB,QAAS,EAAEoB,OAAO,CAAC;OACpD,MAAM;QACHd,WAAW,GAAGA,WAAW,CAACK,IAAI,CAAC,YAAW;UACtC,MAAM,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC,CAAArB,QAAS,EAAEoB,OAAO,CAAC;QAC7C,CAAC,CAAC;;MAEN,IAAI,CAAC,CAAAd,WAAY,GAAGA,WAAW,CAACK,IAAI,CAAC,MAAK;QACtC,IAAI,IAAI,CAAC,CAAAL,WAAY,KAAKA,WAAW,EAAE;UACnC,IAAI,CAAC,CAAAA,WAAY,GAAG,IAAI;;MAEhC,CAAC,CAAC;;EAEV;EAEA;;;;EAIA,MAAMe,KAAKA,CAACrB,QAAwB,EAAEoB,OAAY;IAC9C,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;EAC9D;;AAGJ;;;;AAIA,OAAM,MAAOC,qBAAsB,SAAQxB,gBAAgB;EACvD;;;EAGAQ,YAAYP,QAAwB;IAChC,KAAK,CAACA,QAAQ,EAAE,CAAE,UAAU,CAAE,CAAC;EACnC;EAEA,MAAMqB,KAAKA,CAACrB,QAAwB,EAAEoB,OAAY;IAC9CpB,QAAQ,CAACwB,IAAI,CAAC,OAAO,EAAEC,QAAQ,CAACL,OAAO,CAACM,MAAM,CAAC,CAAC;EACpD;;AAGJ;;;;AAIA,OAAM,MAAOC,uBAAwB,SAAQ5B,gBAAgB;EAEzD;;;EAGAQ,YAAYP,QAAwB;IAChC,KAAK,CAACA,QAAQ,EAAE,CAAE,wBAAwB,CAAE,CAAC;EACjD;EAEA,MAAMqB,KAAKA,CAACrB,QAAwB,EAAEoB,OAAY;IAC9CpB,QAAQ,CAACwB,IAAI,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACrC;;AAGJ;;;AAGA,OAAM,MAAOQ,qBAAsB,SAAQ7B,gBAAgB;EACvD,CAAA8B,SAAU;EAEV;;;EAGA,IAAIA,SAASA,CAAA;IAAkB,OAAO3B,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC,CAAA0B,SAAU,CAAC;EAAE;EAEnE;;;EAGAtB,YAAYP,QAAwB,EAAEC,MAAmB;IACrD,KAAK,CAACD,QAAQ,EAAE,CAAE,MAAM,EAAEC,MAAM,CAAE,CAAC;IACnC,IAAI,CAAC,CAAA4B,SAAU,GAAG3B,IAAI,CAACM,SAAS,CAACP,MAAM,CAAC;EAC5C;EAEA,MAAMoB,KAAKA,CAACrB,QAAwB,EAAEoB,OAAY;IAC9CpB,QAAQ,CAACwB,IAAI,CAAC,IAAI,CAACK,SAAS,EAAE7B,QAAQ,CAAC8B,QAAQ,CAACV,OAAO,EAAEpB,QAAQ,CAAC+B,QAAQ,CAAC,CAAC;EAChF;;AAGJ;;;;;AAKA,OAAM,MAAOC,cAAe,SAAQlC,kBAAkB;EAClD,CAAAmC,SAAU;EAEV;EACA,CAAAC,IAAK;EAEL;EACA;EACA,CAAAC,OAAQ;EAER;;;;;EAKA5B,YAAY6B,OAAoB,EAAEC,QAAoC;IAClE;IACA,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAGH,QAAQ,IAAI,IAAI,GAAIA,QAAQ,GAAE,EAAG,CAAC;IAEtE;IACA;IACA;IACAzC,cAAc,CAAC0C,OAAO,CAACG,aAAa,IAAI,IAAI,IAAIH,OAAO,CAACG,aAAa,KAAK,CAAC,EACvE,gDAAgD,EAAE,uBAAuB,EAAEJ,QAAQ,CAAC;IACxFC,OAAO,CAACG,aAAa,GAAG,CAAC;IAEzB;IACA;IACA;IACA,IAAIH,OAAO,CAACI,aAAa,IAAI,IAAI,EAAE;MAAEJ,OAAO,CAACI,aAAa,GAAG,IAAI;;IAEjE,KAAK,CAACN,OAAO,EAAEE,OAAO,CAAC;IACvB,IAAI,CAAC,CAAAL,SAAU,GAAG,IAAIU,GAAG,EAAE;IAC3B,IAAI,CAAC,CAAAT,IAAK,GAAG,IAAIS,GAAG,EAAE;IACtB,IAAI,CAAC,CAAAR,OAAQ,GAAG,IAAIQ,GAAG,EAAE;EAC7B;EAEA;EACA;;;;;;;;EASAC,cAAcA,CAACC,GAAiB;IAC5B,QAAQA,GAAG,CAACC,IAAI;MACZ,KAAK,OAAO;QACR,OAAO,IAAIpD,mBAAmB,CAAC,OAAO,CAAC;MAC3C,KAAK,OAAO;QACR,OAAO,IAAI6B,qBAAqB,CAAC,IAAI,CAAC;MAC1C,KAAK,SAAS;QACV,OAAO,IAAII,uBAAuB,CAAC,IAAI,CAAC;MAC5C,KAAK,OAAO;QACR,OAAO,IAAIC,qBAAqB,CAAC,IAAI,EAAEiB,GAAG,CAAC5C,MAAM,CAAC;MACtD,KAAK,QAAQ;QACT;QACA;QACA,IAAI4C,GAAG,CAAC5C,MAAM,CAAC8C,MAAM,KAAK,UAAU,EAAE;UAClC,OAAO,IAAIrD,mBAAmB,CAAC,UAAU,CAAC;;;IAGtD,OAAO,KAAK,CAACkD,cAAc,CAACC,GAAG,CAAC;EACpC;EAEA;;;;EAIAjC,SAASA,CAACR,QAAyB,EAAE4C,UAA4B;IAC7D,IAAI,CAAC,CAAAd,IAAK,CAACe,GAAG,CAAC7C,QAAQ,EAAE4C,UAAU,CAAC;IACpC,MAAMb,OAAO,GAAG,IAAI,CAAC,CAAAA,OAAQ,CAACe,GAAG,CAAC9C,QAAQ,CAAC;IAC3C,IAAI+B,OAAO,EAAE;MACT,KAAK,MAAMf,OAAO,IAAIe,OAAO,EAAE;QAC3Ba,UAAU,CAAC7B,cAAc,CAACC,OAAO,CAAC;;MAEtC,IAAI,CAAC,CAAAe,OAAQ,CAACgB,MAAM,CAAC/C,QAAQ,CAAC;;EAEtC;EAEA,MAAMgD,KAAKA,CAACC,OAA+C;IACvD;IACAzD,cAAc,CAAC,CAAC0D,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE,uCAAuC,EAAE,SAAS,EAAEA,OAAO,CAAC;IAEpG;IAEA;IACA,MAAMG,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC5C,IAAI,CAAC,CAAA1B,SAAU,CAACgB,GAAG,CAACI,OAAO,CAACO,EAAE,EAAE;QAAEP,OAAO;QAAEK,OAAO;QAAEC;MAAM,CAAE,CAAC;IACjE,CAAC,CAAC;IAEF;IACA,MAAM,IAAI,CAACE,eAAe,EAAE;IAE5B;IACA,MAAM,IAAI,CAACC,MAAM,CAAC5D,IAAI,CAACM,SAAS,CAAC6C,OAAO,CAAC,CAAC;IAE1C,OAA4C,CAAE,MAAMG,OAAO,CAAE;EACjE;EAEA;EACA;;;;;;;;;;;EAcA;;;;EAIA,MAAMO,eAAeA,CAAC3C,OAAe;IACjC,MAAM4C,MAAM,GAAwD9D,IAAI,CAACC,KAAK,CAACiB,OAAO,CAAE;IAExF,IAAI4C,MAAM,IAAI,OAAOA,MAAO,KAAK,QAAQ,IAAI,IAAI,IAAIA,MAAM,EAAE;MACzD,MAAMC,QAAQ,GAAG,IAAI,CAAC,CAAAhC,SAAU,CAACiB,GAAG,CAACc,MAAM,CAACJ,EAAE,CAAC;MAC/C,IAAIK,QAAQ,IAAI,IAAI,EAAE;QAClB,IAAI,CAACzC,IAAI,CAAC,OAAO,EAAE3B,SAAS,CAAC,gCAAgC,EAAE,eAAe,EAAE;UAC5EqE,UAAU,EAAE,YAAY;UACxBF;SACH,CAAC,CAAC;QACH;;MAEJ,IAAI,CAAC,CAAA/B,SAAU,CAACkB,MAAM,CAACa,MAAM,CAACJ,EAAE,CAAC;MAEjCK,QAAQ,CAACP,OAAO,CAACM,MAAM,CAAC;KAE3B,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,kBAAkB,EAAE;MACvD,MAAM/D,QAAQ,GAAG4D,MAAM,CAACI,MAAM,CAACC,YAAY;MAC3C,MAAMrB,UAAU,GAAG,IAAI,CAAC,CAAAd,IAAK,CAACgB,GAAG,CAAC9C,QAAQ,CAAC;MAC3C,IAAI4C,UAAU,EAAE;QACZA,UAAU,CAAC7B,cAAc,CAAC6C,MAAM,CAACI,MAAM,CAACJ,MAAM,CAAC;OAClD,MAAM;QACH,IAAI7B,OAAO,GAAG,IAAI,CAAC,CAAAA,OAAQ,CAACe,GAAG,CAAC9C,QAAQ,CAAC;QACzC,IAAI+B,OAAO,IAAI,IAAI,EAAE;UACjBA,OAAO,GAAG,EAAG;UACb,IAAI,CAAC,CAAAA,OAAQ,CAACc,GAAG,CAAC7C,QAAQ,EAAE+B,OAAO,CAAC;;QAExCA,OAAO,CAACmC,IAAI,CAACN,MAAM,CAACI,MAAM,CAACJ,MAAM,CAAC;;KAGzC,MAAM;MACH,IAAI,CAACxC,IAAI,CAAC,OAAO,EAAE3B,SAAS,CAAC,6BAA6B,EAAE,eAAe,EAAE;QACzEqE,UAAU,EAAE,oBAAoB;QAChCF;OACH,CAAC,CAAC;MACH;;EAER;EAEA;;;;EAIA,MAAMF,MAAMA,CAAC1C,OAAe;IACxB,MAAM,IAAIE,KAAK,CAAC,gCAAgC,CAAC;EACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}