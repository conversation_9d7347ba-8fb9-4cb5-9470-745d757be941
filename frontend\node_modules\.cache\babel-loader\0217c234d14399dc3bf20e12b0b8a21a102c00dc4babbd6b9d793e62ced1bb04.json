{"ast": null, "code": "import { getBytesCopy, hexlify } from \"../../utils/index.js\";\nimport { Coder } from \"./abstract-coder.js\";\n/**\n *  @_ignore\n */\nexport class DynamicBytesCoder extends Coder {\n  constructor(type, localName) {\n    super(type, type, localName, true);\n  }\n  defaultValue() {\n    return \"0x\";\n  }\n  encode(writer, value) {\n    value = getBytesCopy(value);\n    let length = writer.writeValue(value.length);\n    length += writer.writeBytes(value);\n    return length;\n  }\n  decode(reader) {\n    return reader.readBytes(reader.readIndex(), true);\n  }\n}\n/**\n *  @_ignore\n */\nexport class BytesCoder extends DynamicBytesCoder {\n  constructor(localName) {\n    super(\"bytes\", localName);\n  }\n  decode(reader) {\n    return hexlify(super.decode(reader));\n  }\n}", "map": {"version": 3, "names": ["getBytesCopy", "hexlify", "Coder", "DynamicBytesCoder", "constructor", "type", "localName", "defaultValue", "encode", "writer", "value", "length", "writeValue", "writeBytes", "decode", "reader", "readBytes", "readIndex", "BytesCoder"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\bytes.ts"], "sourcesContent": ["import { getBytesCopy, hexlify } from \"../../utils/index.js\";\n\nimport { Coder } from \"./abstract-coder.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n\n/**\n *  @_ignore\n */\nexport class DynamicBytesCoder extends Coder {\n    constructor(type: string, localName: string) {\n       super(type, type, localName, true);\n    }\n\n    defaultValue(): string {\n        return \"0x\";\n    }\n\n    encode(writer: Writer, value: any): number {\n        value = getBytesCopy(value);\n        let length = writer.writeValue(value.length);\n        length += writer.writeBytes(value);\n        return length;\n    }\n\n    decode(reader: Reader): any {\n        return reader.readBytes(reader.readIndex(), true);\n    }\n}\n\n/**\n *  @_ignore\n */\nexport class BytesCoder extends DynamicBytesCoder {\n    constructor(localName: string) {\n        super(\"bytes\", localName);\n    }\n\n    decode(reader: Reader): any {\n        return hexlify(super.decode(reader));\n    }\n}\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,OAAO,QAAQ,sBAAsB;AAE5D,SAASC,KAAK,QAAQ,qBAAqB;AAK3C;;;AAGA,OAAM,MAAOC,iBAAkB,SAAQD,KAAK;EACxCE,YAAYC,IAAY,EAAEC,SAAiB;IACxC,KAAK,CAACD,IAAI,EAAEA,IAAI,EAAEC,SAAS,EAAE,IAAI,CAAC;EACrC;EAEAC,YAAYA,CAAA;IACR,OAAO,IAAI;EACf;EAEAC,MAAMA,CAACC,MAAc,EAAEC,KAAU;IAC7BA,KAAK,GAAGV,YAAY,CAACU,KAAK,CAAC;IAC3B,IAAIC,MAAM,GAAGF,MAAM,CAACG,UAAU,CAACF,KAAK,CAACC,MAAM,CAAC;IAC5CA,MAAM,IAAIF,MAAM,CAACI,UAAU,CAACH,KAAK,CAAC;IAClC,OAAOC,MAAM;EACjB;EAEAG,MAAMA,CAACC,MAAc;IACjB,OAAOA,MAAM,CAACC,SAAS,CAACD,MAAM,CAACE,SAAS,EAAE,EAAE,IAAI,CAAC;EACrD;;AAGJ;;;AAGA,OAAM,MAAOC,UAAW,SAAQf,iBAAiB;EAC7CC,YAAYE,SAAiB;IACzB,KAAK,CAAC,OAAO,EAAEA,SAAS,CAAC;EAC7B;EAEAQ,MAAMA,CAACC,MAAc;IACjB,OAAOd,OAAO,CAAC,KAAK,CAACa,MAAM,CAACC,MAAM,CAAC,CAAC;EACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}