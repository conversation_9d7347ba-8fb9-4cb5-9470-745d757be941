{"ast": null, "code": "// utils/base64-browser\nimport { getBytes } from \"./data.js\";\nexport function decodeBase64(textData) {\n  textData = atob(textData);\n  const data = new Uint8Array(textData.length);\n  for (let i = 0; i < textData.length; i++) {\n    data[i] = textData.charCodeAt(i);\n  }\n  return getBytes(data);\n}\nexport function encodeBase64(_data) {\n  const data = getBytes(_data);\n  let textData = \"\";\n  for (let i = 0; i < data.length; i++) {\n    textData += String.fromCharCode(data[i]);\n  }\n  return btoa(textData);\n}", "map": {"version": 3, "names": ["getBytes", "decodeBase64", "textData", "atob", "data", "Uint8Array", "length", "i", "charCodeAt", "encodeBase64", "_data", "String", "fromCharCode", "btoa"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\base64-browser.ts"], "sourcesContent": ["\n// utils/base64-browser\n\nimport { getBytes } from \"./data.js\";\n\nimport type { BytesLike } from \"./data.js\";\n\n\nexport function decodeBase64(textData: string): Uint8Array {\n    textData = atob(textData);\n    const data = new Uint8Array(textData.length);\n    for (let i = 0; i < textData.length; i++) {\n        data[i] = textData.charCodeAt(i);\n    }\n    return getBytes(data);\n}\n\nexport function encodeBase64(_data: BytesLike): string {\n    const data = getBytes(_data);\n    let textData = \"\";\n    for (let i = 0; i < data.length; i++) {\n        textData += String.fromCharCode(data[i]);\n    }\n    return btoa(textData);\n}\n"], "mappings": "AACA;AAEA,SAASA,QAAQ,QAAQ,WAAW;AAKpC,OAAM,SAAUC,YAAYA,CAACC,QAAgB;EACzCA,QAAQ,GAAGC,IAAI,CAACD,QAAQ,CAAC;EACzB,MAAME,IAAI,GAAG,IAAIC,UAAU,CAACH,QAAQ,CAACI,MAAM,CAAC;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;IACtCH,IAAI,CAACG,CAAC,CAAC,GAAGL,QAAQ,CAACM,UAAU,CAACD,CAAC,CAAC;;EAEpC,OAAOP,QAAQ,CAACI,IAAI,CAAC;AACzB;AAEA,OAAM,SAAUK,YAAYA,CAACC,KAAgB;EACzC,MAAMN,IAAI,GAAGJ,QAAQ,CAACU,KAAK,CAAC;EAC5B,IAAIR,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAClCL,QAAQ,IAAIS,MAAM,CAACC,YAAY,CAACR,IAAI,CAACG,CAAC,CAAC,CAAC;;EAE5C,OAAOM,IAAI,CAACX,QAAQ,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}