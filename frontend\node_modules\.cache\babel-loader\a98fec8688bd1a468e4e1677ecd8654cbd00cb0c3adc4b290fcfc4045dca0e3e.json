{"ast": null, "code": "/**\n *  Most interactions with Ethereum requires integer values, which use\n *  the smallest magnitude unit.\n *\n *  For example, imagine dealing with dollars and cents. Since dollars\n *  are divisible, non-integer values are possible, such as ``$10.77``.\n *  By using the smallest indivisible unit (i.e. cents), the value can\n *  be kept as the integer ``1077``.\n *\n *  When receiving decimal input from the user (as a decimal string),\n *  the value should be converted to an integer and when showing a user\n *  a value, the integer value should be converted to a decimal string.\n *\n *  This creates a clear distinction, between values to be used by code\n *  (integers) and values used for display logic to users (decimals).\n *\n *  The native unit in Ethereum, //ether// is divisible to 18 decimal places,\n *  where each individual unit is called a //wei//.\n *\n *  @_subsection api/utils:Unit Conversion  [about-units]\n */\nimport { assertArgument } from \"./errors.js\";\nimport { FixedNumber } from \"./fixednumber.js\";\nimport { getNumber } from \"./maths.js\";\nconst names = [\"wei\", \"kwei\", \"mwei\", \"gwei\", \"szabo\", \"finney\", \"ether\"];\n/**\n *  Converts %%value%% into a //decimal string//, assuming %%unit%% decimal\n *  places. The %%unit%% may be the number of decimal places or the name of\n *  a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n *\n */\nexport function formatUnits(value, unit) {\n  let decimals = 18;\n  if (typeof unit === \"string\") {\n    const index = names.indexOf(unit);\n    assertArgument(index >= 0, \"invalid unit\", \"unit\", unit);\n    decimals = 3 * index;\n  } else if (unit != null) {\n    decimals = getNumber(unit, \"unit\");\n  }\n  return FixedNumber.fromValue(value, decimals, {\n    decimals,\n    width: 512\n  }).toString();\n}\n/**\n *  Converts the //decimal string// %%value%% to a BigInt, assuming\n *  %%unit%% decimal places. The %%unit%% may the number of decimal places\n *  or the name of a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n */\nexport function parseUnits(value, unit) {\n  assertArgument(typeof value === \"string\", \"value must be a string\", \"value\", value);\n  let decimals = 18;\n  if (typeof unit === \"string\") {\n    const index = names.indexOf(unit);\n    assertArgument(index >= 0, \"invalid unit\", \"unit\", unit);\n    decimals = 3 * index;\n  } else if (unit != null) {\n    decimals = getNumber(unit, \"unit\");\n  }\n  return FixedNumber.fromString(value, {\n    decimals,\n    width: 512\n  }).value;\n}\n/**\n *  Converts %%value%% into a //decimal string// using 18 decimal places.\n */\nexport function formatEther(wei) {\n  return formatUnits(wei, 18);\n}\n/**\n *  Converts the //decimal string// %%ether%% to a BigInt, using 18\n *  decimal places.\n */\nexport function parseEther(ether) {\n  return parseUnits(ether, 18);\n}", "map": {"version": 3, "names": ["assertArgument", "FixedNumber", "getNumber", "names", "formatUnits", "value", "unit", "decimals", "index", "indexOf", "fromValue", "width", "toString", "parseUnits", "fromString", "formatEther", "wei", "parseEther", "ether"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\units.ts"], "sourcesContent": ["/**\n *  Most interactions with Ethereum requires integer values, which use\n *  the smallest magnitude unit.\n *\n *  For example, imagine dealing with dollars and cents. Since dollars\n *  are divisible, non-integer values are possible, such as ``$10.77``.\n *  By using the smallest indivisible unit (i.e. cents), the value can\n *  be kept as the integer ``1077``.\n *\n *  When receiving decimal input from the user (as a decimal string),\n *  the value should be converted to an integer and when showing a user\n *  a value, the integer value should be converted to a decimal string.\n *\n *  This creates a clear distinction, between values to be used by code\n *  (integers) and values used for display logic to users (decimals).\n *\n *  The native unit in Ethereum, //ether// is divisible to 18 decimal places,\n *  where each individual unit is called a //wei//.\n *\n *  @_subsection api/utils:Unit Conversion  [about-units]\n */\nimport { assertArgument } from \"./errors.js\";\nimport { FixedNumber } from \"./fixednumber.js\";\nimport { getNumber } from \"./maths.js\";\n\nimport type { BigNumberish, Numeric } from \"../utils/index.js\";\n\n\nconst names = [\n    \"wei\",\n    \"kwei\",\n    \"mwei\",\n    \"gwei\",\n    \"szabo\",\n    \"finney\",\n    \"ether\",\n];\n\n/**\n *  Converts %%value%% into a //decimal string//, assuming %%unit%% decimal\n *  places. The %%unit%% may be the number of decimal places or the name of\n *  a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n *\n */\nexport function formatUnits(value: BigNumberish, unit?: string | Numeric): string {\n    let decimals = 18;\n    if (typeof(unit) === \"string\") {\n        const index = names.indexOf(unit);\n        assertArgument(index >= 0, \"invalid unit\", \"unit\", unit);\n        decimals = 3 * index;\n    } else if (unit != null) {\n        decimals = getNumber(unit, \"unit\");\n    }\n\n    return FixedNumber.fromValue(value, decimals, { decimals, width: 512 }).toString();\n}\n\n/**\n *  Converts the //decimal string// %%value%% to a BigInt, assuming\n *  %%unit%% decimal places. The %%unit%% may the number of decimal places\n *  or the name of a unit (e.g. ``\"gwei\"`` for 9 decimal places).\n */\nexport function parseUnits(value: string, unit?: string | Numeric): bigint {\n    assertArgument(typeof(value) === \"string\", \"value must be a string\", \"value\", value);\n\n    let decimals = 18;\n    if (typeof(unit) === \"string\") {\n        const index = names.indexOf(unit);\n        assertArgument(index >= 0, \"invalid unit\", \"unit\", unit);\n        decimals = 3 * index;\n    } else if (unit != null) {\n        decimals = getNumber(unit, \"unit\");\n    }\n\n    return FixedNumber.fromString(value, { decimals, width: 512 }).value;\n}\n\n/**\n *  Converts %%value%% into a //decimal string// using 18 decimal places.\n */\nexport function formatEther(wei: BigNumberish): string {\n    return formatUnits(wei, 18);\n}\n\n/**\n *  Converts the //decimal string// %%ether%% to a BigInt, using 18\n *  decimal places.\n */\nexport function parseEther(ether: string): bigint {\n    return parseUnits(ether, 18);\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;AAqBA,SAASA,cAAc,QAAQ,aAAa;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,YAAY;AAKtC,MAAMC,KAAK,GAAG,CACV,KAAK,EACL,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,CACV;AAED;;;;;;AAMA,OAAM,SAAUC,WAAWA,CAACC,KAAmB,EAAEC,IAAuB;EACpE,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAI,OAAOD,IAAK,KAAK,QAAQ,EAAE;IAC3B,MAAME,KAAK,GAAGL,KAAK,CAACM,OAAO,CAACH,IAAI,CAAC;IACjCN,cAAc,CAACQ,KAAK,IAAI,CAAC,EAAE,cAAc,EAAE,MAAM,EAAEF,IAAI,CAAC;IACxDC,QAAQ,GAAG,CAAC,GAAGC,KAAK;GACvB,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;IACrBC,QAAQ,GAAGL,SAAS,CAACI,IAAI,EAAE,MAAM,CAAC;;EAGtC,OAAOL,WAAW,CAACS,SAAS,CAACL,KAAK,EAAEE,QAAQ,EAAE;IAAEA,QAAQ;IAAEI,KAAK,EAAE;EAAG,CAAE,CAAC,CAACC,QAAQ,EAAE;AACtF;AAEA;;;;;AAKA,OAAM,SAAUC,UAAUA,CAACR,KAAa,EAAEC,IAAuB;EAC7DN,cAAc,CAAC,OAAOK,KAAM,KAAK,QAAQ,EAAE,wBAAwB,EAAE,OAAO,EAAEA,KAAK,CAAC;EAEpF,IAAIE,QAAQ,GAAG,EAAE;EACjB,IAAI,OAAOD,IAAK,KAAK,QAAQ,EAAE;IAC3B,MAAME,KAAK,GAAGL,KAAK,CAACM,OAAO,CAACH,IAAI,CAAC;IACjCN,cAAc,CAACQ,KAAK,IAAI,CAAC,EAAE,cAAc,EAAE,MAAM,EAAEF,IAAI,CAAC;IACxDC,QAAQ,GAAG,CAAC,GAAGC,KAAK;GACvB,MAAM,IAAIF,IAAI,IAAI,IAAI,EAAE;IACrBC,QAAQ,GAAGL,SAAS,CAACI,IAAI,EAAE,MAAM,CAAC;;EAGtC,OAAOL,WAAW,CAACa,UAAU,CAACT,KAAK,EAAE;IAAEE,QAAQ;IAAEI,KAAK,EAAE;EAAG,CAAE,CAAC,CAACN,KAAK;AACxE;AAEA;;;AAGA,OAAM,SAAUU,WAAWA,CAACC,GAAiB;EACzC,OAAOZ,WAAW,CAACY,GAAG,EAAE,EAAE,CAAC;AAC/B;AAEA;;;;AAIA,OAAM,SAAUC,UAAUA,CAACC,KAAa;EACpC,OAAOL,UAAU,CAACK,KAAK,EAAE,EAAE,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}