{"ast": null, "code": "// Output Feedback\nvar __classPrivateFieldSet = this && this.__classPrivateFieldSet || function (receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value), value;\n};\nvar __classPrivateFieldGet = this && this.__classPrivateFieldGet || function (receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _OFB_iv, _OFB_lastPrecipher, _OFB_lastPrecipherIndex;\nimport { ModeOfOperation } from \"./mode.js\";\nexport class OFB extends ModeOfOperation {\n  constructor(key, iv) {\n    super(\"OFB\", key, OFB);\n    _OFB_iv.set(this, void 0);\n    _OFB_lastPrecipher.set(this, void 0);\n    _OFB_lastPrecipherIndex.set(this, void 0);\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      __classPrivateFieldSet(this, _OFB_iv, new Uint8Array(iv), \"f\");\n    } else {\n      __classPrivateFieldSet(this, _OFB_iv, new Uint8Array(16), \"f\");\n    }\n    __classPrivateFieldSet(this, _OFB_lastPrecipher, this.iv, \"f\");\n    __classPrivateFieldSet(this, _OFB_lastPrecipherIndex, 16, \"f\");\n  }\n  get iv() {\n    return new Uint8Array(__classPrivateFieldGet(this, _OFB_iv, \"f\"));\n  }\n  encrypt(plaintext) {\n    var _a, _b;\n    if (plaintext.length % 16) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n    const ciphertext = new Uint8Array(plaintext);\n    for (let i = 0; i < ciphertext.length; i++) {\n      if (__classPrivateFieldGet(this, _OFB_lastPrecipherIndex, \"f\") === 16) {\n        __classPrivateFieldSet(this, _OFB_lastPrecipher, this.aes.encrypt(__classPrivateFieldGet(this, _OFB_lastPrecipher, \"f\")), \"f\");\n        __classPrivateFieldSet(this, _OFB_lastPrecipherIndex, 0, \"f\");\n      }\n      ciphertext[i] ^= __classPrivateFieldGet(this, _OFB_lastPrecipher, \"f\")[__classPrivateFieldSet(this, _OFB_lastPrecipherIndex, (_b = __classPrivateFieldGet(this, _OFB_lastPrecipherIndex, \"f\"), _a = _b++, _b), \"f\"), _a];\n    }\n    return ciphertext;\n  }\n  decrypt(ciphertext) {\n    if (ciphertext.length % 16) {\n      throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n    return this.encrypt(ciphertext);\n  }\n}\n_OFB_iv = new WeakMap(), _OFB_lastPrecipher = new WeakMap(), _OFB_lastPrecipherIndex = new WeakMap();", "map": {"version": 3, "names": ["ModeOfOperation", "OFB", "constructor", "key", "iv", "_OFB_iv", "set", "_OFB_lastPrecipher", "_OFB_lastPrecipherIndex", "length", "TypeError", "__classPrivateFieldSet", "Uint8Array", "__classPrivateFieldGet", "encrypt", "plaintext", "ciphertext", "i", "aes", "_b", "_a", "decrypt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\mode-ofb.ts"], "sourcesContent": ["// Output Feedback\n\nimport { ModeOfOperation } from \"./mode.js\";\n\nexport class OFB extends ModeOfOperation {\n  #iv: Uint8Array;\n  #lastPrecipher: Uint8Array;\n  #lastPrecipherIndex: number;\n\n  constructor(key: Uint8Array, iv?: Uint8Array) {\n    super(\"OFB\", key, OFB);\n\n    if (iv) {\n      if (iv.length % 16) {\n        throw new TypeError(\"invalid iv size (must be 16 bytes)\");\n      }\n      this.#iv = new Uint8Array(iv);\n    } else {\n      this.#iv = new Uint8Array(16);\n    }\n\n    this.#lastPrecipher = this.iv;\n    this.#lastPrecipherIndex = 16;\n  }\n\n  get iv(): Uint8Array { return new Uint8Array(this.#iv); }\n\n  encrypt(plaintext: Uint8Array): Uint8Array {\n    if (plaintext.length % 16) {\n      throw new TypeError(\"invalid plaintext size (must be multiple of 16 bytes)\");\n    }\n\n    const ciphertext = new Uint8Array(plaintext);\n    for (let i = 0; i < ciphertext.length; i++) {\n      if (this.#lastPrecipherIndex === 16) {\n          this.#lastPrecipher = this.aes.encrypt(this.#lastPrecipher);\n          this.#lastPrecipherIndex = 0;\n      }\n      ciphertext[i] ^= this.#lastPrecipher[this.#lastPrecipherIndex++];\n    }\n\n    return ciphertext;\n  }\n\n  decrypt(ciphertext: Uint8Array): Uint8Array {\n    if (ciphertext.length % 16) {\n        throw new TypeError(\"invalid ciphertext size (must be multiple of 16 bytes)\");\n    }\n    return this.encrypt(ciphertext);\n  }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;AAEA,SAASA,eAAe,QAAQ,WAAW;AAE3C,OAAM,MAAOC,GAAI,SAAQD,eAAe;EAKtCE,YAAYC,GAAe,EAAEC,EAAe;IAC1C,KAAK,CAAC,KAAK,EAAED,GAAG,EAAEF,GAAG,CAAC;IALxBI,OAAA,CAAAC,GAAA;IACAC,kBAAA,CAAAD,GAAA;IACAE,uBAAA,CAAAF,GAAA;IAKE,IAAIF,EAAE,EAAE;MACN,IAAIA,EAAE,CAACK,MAAM,GAAG,EAAE,EAAE;QAClB,MAAM,IAAIC,SAAS,CAAC,oCAAoC,CAAC;;MAE3DC,sBAAA,KAAI,EAAAN,OAAA,EAAO,IAAIO,UAAU,CAACR,EAAE,CAAC;KAC9B,MAAM;MACLO,sBAAA,KAAI,EAAAN,OAAA,EAAO,IAAIO,UAAU,CAAC,EAAE,CAAC;;IAG/BD,sBAAA,KAAI,EAAAJ,kBAAA,EAAkB,IAAI,CAACH,EAAE;IAC7BO,sBAAA,KAAI,EAAAH,uBAAA,EAAuB,EAAE;EAC/B;EAEA,IAAIJ,EAAEA,CAAA;IAAiB,OAAO,IAAIQ,UAAU,CAACC,sBAAA,KAAI,EAAAR,OAAA,MAAI,CAAC;EAAE;EAExDS,OAAOA,CAACC,SAAqB;;IAC3B,IAAIA,SAAS,CAACN,MAAM,GAAG,EAAE,EAAE;MACzB,MAAM,IAAIC,SAAS,CAAC,uDAAuD,CAAC;;IAG9E,MAAMM,UAAU,GAAG,IAAIJ,UAAU,CAACG,SAAS,CAAC;IAC5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;MAC1C,IAAIJ,sBAAA,KAAI,EAAAL,uBAAA,MAAoB,KAAK,EAAE,EAAE;QACjCG,sBAAA,KAAI,EAAAJ,kBAAA,EAAkB,IAAI,CAACW,GAAG,CAACJ,OAAO,CAACD,sBAAA,KAAI,EAAAN,kBAAA,MAAe,CAAC;QAC3DI,sBAAA,KAAI,EAAAH,uBAAA,EAAuB,CAAC;;MAEhCQ,UAAU,CAACC,CAAC,CAAC,IAAIJ,sBAAA,KAAI,EAAAN,kBAAA,MAAe,CAACI,sBAAA,OAAAH,uBAAA,GAAAW,EAAA,GAAAN,sBAAA,OAAAL,uBAAA,MAAwB,EAAxBY,EAAA,GAAAD,EAAA,EAA0B,EAAAA,EAAA,SAAAC,EAAA,CAAC;;IAGlE,OAAOJ,UAAU;EACnB;EAEAK,OAAOA,CAACL,UAAsB;IAC5B,IAAIA,UAAU,CAACP,MAAM,GAAG,EAAE,EAAE;MACxB,MAAM,IAAIC,SAAS,CAAC,wDAAwD,CAAC;;IAEjF,OAAO,IAAI,CAACI,OAAO,CAACE,UAAU,CAAC;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}