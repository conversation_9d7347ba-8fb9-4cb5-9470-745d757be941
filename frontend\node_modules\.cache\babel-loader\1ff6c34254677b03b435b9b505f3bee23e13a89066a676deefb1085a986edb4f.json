{"ast": null, "code": "/**\n *  A constant for the order N for the secp256k1 curve.\n *\n *  (**i.e.** ``0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141n``)\n */\nexport const N = BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\");\n/**\n *  A constant for the number of wei in a single ether.\n *\n *  (**i.e.** ``1000000000000000000n``)\n */\nexport const WeiPerEther = BigInt(\"1000000000000000000\");\n/**\n *  A constant for the maximum value for a ``uint256``.\n *\n *  (**i.e.** ``0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn``)\n */\nexport const MaxUint256 = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n/**\n *  A constant for the minimum value for an ``int256``.\n *\n *  (**i.e.** ``-8000000000000000000000000000000000000000000000000000000000000000n``)\n */\nexport const MinInt256 = BigInt(\"0x8000000000000000000000000000000000000000000000000000000000000000\") * BigInt(-1);\n/**\n *  A constant for the maximum value for an ``int256``.\n *\n *  (**i.e.** ``0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn``)\n */\nexport const MaxInt256 = BigInt(\"0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");", "map": {"version": 3, "names": ["N", "BigInt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MaxUint256", "MinInt256", "MaxInt256"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\constants\\numbers.ts"], "sourcesContent": ["\n/**\n *  A constant for the order N for the secp256k1 curve.\n *\n *  (**i.e.** ``0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141n``)\n */\nexport const N: bigint = BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\");\n\n/**\n *  A constant for the number of wei in a single ether.\n *\n *  (**i.e.** ``1000000000000000000n``)\n */\nexport const WeiPerEther: bigint = BigInt(\"1000000000000000000\");\n\n/**\n *  A constant for the maximum value for a ``uint256``.\n *\n *  (**i.e.** ``0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn``)\n */\nexport const MaxUint256: bigint = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n\n/**\n *  A constant for the minimum value for an ``int256``.\n *\n *  (**i.e.** ``-8000000000000000000000000000000000000000000000000000000000000000n``)\n */\nexport const MinInt256: bigint = BigInt(\"0x8000000000000000000000000000000000000000000000000000000000000000\") * BigInt(-1);\n\n/**\n *  A constant for the maximum value for an ``int256``.\n *\n *  (**i.e.** ``0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffn``)\n */\nexport const MaxInt256: bigint = BigInt(\"0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n"], "mappings": "AACA;;;;;AAKA,OAAO,MAAMA,CAAC,GAAWC,MAAM,CAAC,oEAAoE,CAAC;AAErG;;;;;AAKA,OAAO,MAAMC,WAAW,GAAWD,MAAM,CAAC,qBAAqB,CAAC;AAEhE;;;;;AAKA,OAAO,MAAME,UAAU,GAAWF,MAAM,CAAC,oEAAoE,CAAC;AAE9G;;;;;AAKA,OAAO,MAAMG,SAAS,GAAWH,MAAM,CAAC,oEAAoE,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC;AAE1H;;;;;AAKA,OAAO,MAAMI,SAAS,GAAWJ,MAAM,CAAC,oEAAoE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}