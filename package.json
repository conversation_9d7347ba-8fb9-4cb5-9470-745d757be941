{"name": "cryptoquest", "version": "1.0.0", "description": "Web3 Play-to-Earn strategy battle game with $CQT token rewards", "main": "index.js", "scripts": {"install:all": "npm install && cd contracts && npm install && cd ../backend && npm install && cd ../frontend && npm install && cd ..", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "dev:contracts": "cd contracts && npx hardhat node", "build": "cd frontend && npm run build", "test": "npm run test:contracts && npm run test:backend", "test:contracts": "cd contracts && npm test", "test:backend": "cd backend && npm test", "deploy:contracts": "cd contracts && npx hardhat run scripts/deploy.js --network localhost", "migrate:db": "cd backend && npm run migrate", "setup": "npm run install:all && npm run migrate:db", "start:production": "cd backend && npm start", "clean": "npm run clean:contracts && npm run clean:backend && npm run clean:frontend", "clean:contracts": "cd contracts && rm -rf node_modules artifacts cache", "clean:backend": "cd backend && rm -rf node_modules", "clean:frontend": "cd frontend && rm -rf node_modules build", "reset": "npm run clean && npm run install:all"}, "keywords": ["web3", "play-to-earn", "blockchain", "game", "crypto"], "author": "CryptoQuest Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}}