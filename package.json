{"name": "cryptoquest", "version": "1.0.0", "description": "Web3 Play-to-Earn strategy battle game with $CQT token rewards", "main": "index.js", "scripts": {"dev": "concurrently \"npm run backend\" \"npm run frontend\"", "backend": "cd backend && npm run dev", "frontend": "cd frontend && npm run dev", "compile": "cd contracts && npx hardhat compile", "deploy": "cd contracts && npx hardhat run scripts/deploy.js --network localhost", "test": "cd contracts && npx hardhat test", "node": "cd contracts && npx hardhat node"}, "keywords": ["web3", "play-to-earn", "blockchain", "game", "crypto"], "author": "CryptoQuest Team", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0"}}