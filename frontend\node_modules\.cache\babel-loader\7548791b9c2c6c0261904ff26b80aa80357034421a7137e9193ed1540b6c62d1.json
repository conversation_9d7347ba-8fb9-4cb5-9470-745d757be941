{"ast": null, "code": "import { createContext } from 'react';\n\n/**\n * Note: Still used by components generated by old versions of Framer\n *\n * @deprecated\n */\nconst DeprecatedLayoutGroupContext = createContext(null);\nexport { DeprecatedLayoutGroupContext };", "map": {"version": 3, "names": ["createContext", "DeprecatedLayoutGroupContext"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/context/DeprecatedLayoutGroupContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\n/**\n * Note: Still used by components generated by old versions of Framer\n *\n * @deprecated\n */\nconst DeprecatedLayoutGroupContext = createContext(null);\n\nexport { DeprecatedLayoutGroupContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,GAAGD,aAAa,CAAC,IAAI,CAAC;AAExD,SAASC,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}