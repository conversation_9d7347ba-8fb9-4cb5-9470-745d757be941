import React from 'react';
import { motion } from 'framer-motion';
import { 
  GiftIcon, 
  CheckCircleIcon,
  ClockIcon,
  TrophyIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

const QuestCard = ({ quest, onClaim, claiming = false }) => {
  const getQuestIcon = (type) => {
    const icons = {
      win_battles: TrophyIcon,
      upgrade_hero: ShieldCheckIcon,
      complete_pve: TrophyIcon,
      earn_tokens: CurrencyDollarIcon,
    };
    return icons[type] || GiftIcon;
  };

  const getQuestTitle = (type) => {
    const titles = {
      win_battles: 'Win Battles',
      upgrade_hero: 'Upgrade Heroes',
      complete_pve: 'Complete PvE',
      earn_tokens: 'Earn <PERSON>kens',
    };
    return titles[type] || 'Daily Quest';
  };

  const getQuestDescription = (type, target) => {
    const descriptions = {
      win_battles: `Win ${target} battles in any game mode`,
      upgrade_hero: `Upgrade ${target} hero${target > 1 ? 's' : ''}`,
      complete_pve: `Complete ${target} PvE battle${target > 1 ? 's' : ''}`,
      earn_tokens: `Earn ${target} CQT tokens`,
    };
    return descriptions[type] || `Complete ${target} tasks`;
  };

  const progressPercentage = Math.min((quest.current_progress / quest.target_value) * 100, 100);
  const isCompleted = quest.is_completed;
  const isClaimed = quest.completed_at;
  const Icon = getQuestIcon(quest.quest_type);

  return (
    <motion.div
      className={`game-card p-4 ${
        isCompleted && !isClaimed ? 'border-green-500 glow-secondary' : ''
      }`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Quest Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
            isCompleted 
              ? 'bg-green-600' 
              : 'bg-gradient-to-br from-primary-500 to-secondary-500'
          }`}>
            {isCompleted ? (
              <CheckCircleIcon className="w-5 h-5 text-white" />
            ) : (
              <Icon className="w-5 h-5 text-white" />
            )}
          </div>
          <div>
            <h3 className="font-semibold text-white">
              {getQuestTitle(quest.quest_type)}
            </h3>
            <p className="text-sm text-dark-400">
              {getQuestDescription(quest.quest_type, quest.target_value)}
            </p>
          </div>
        </div>
        
        <div className="text-right">
          <div className="flex items-center space-x-1 text-yellow-400">
            <CurrencyDollarIcon className="w-4 h-4" />
            <span className="font-bold">{quest.reward_amount}</span>
          </div>
          <span className="text-xs text-dark-400">CQT Reward</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-dark-400 mb-2">
          <span>Progress</span>
          <span>{quest.current_progress}/{quest.target_value}</span>
        </div>
        <div className="stat-bar h-2">
          <motion.div
            className={`h-full rounded-full ${
              isCompleted 
                ? 'bg-gradient-to-r from-green-500 to-green-600' 
                : 'bg-gradient-to-r from-primary-500 to-secondary-500'
            }`}
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          />
        </div>
      </div>

      {/* Quest Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isClaimed ? (
            <>
              <CheckCircleIcon className="w-4 h-4 text-green-400" />
              <span className="text-sm text-green-400 font-medium">Claimed</span>
            </>
          ) : isCompleted ? (
            <>
              <GiftIcon className="w-4 h-4 text-yellow-400" />
              <span className="text-sm text-yellow-400 font-medium">Ready to Claim</span>
            </>
          ) : (
            <>
              <ClockIcon className="w-4 h-4 text-dark-400" />
              <span className="text-sm text-dark-400">In Progress</span>
            </>
          )}
        </div>

        {/* Claim Button */}
        {isCompleted && !isClaimed && onClaim && (
          <motion.button
            onClick={() => onClaim(quest.id)}
            disabled={claiming}
            className="game-button text-sm px-4 py-2 disabled:opacity-50"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {claiming ? (
              <div className="flex items-center space-x-2">
                <div className="loading-spinner w-3 h-3"></div>
                <span>Claiming...</span>
              </div>
            ) : (
              'Claim Reward'
            )}
          </motion.button>
        )}
      </div>

      {/* Completion Animation */}
      {isCompleted && !isClaimed && (
        <motion.div
          className="absolute inset-0 pointer-events-none"
          initial={{ opacity: 0 }}
          animate={{ opacity: [0, 0.3, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div className="w-full h-full bg-gradient-to-r from-green-500/20 to-yellow-500/20 rounded-lg" />
        </motion.div>
      )}
    </motion.div>
  );
};

export default QuestCard;
