{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ethers } from 'ethers';\nimport api from '../utils/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('cq_token'));\n  const [loading, setLoading] = useState(true);\n  const [walletAddress, setWalletAddress] = useState(null);\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/users/profile');\n          setUser(response.data.user);\n          setWalletAddress(response.data.user.walletAddress);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, [token]);\n  const connectWallet = async () => {\n    try {\n      if (!window.ethereum) {\n        toast.error('MetaMask is not installed. Please install MetaMask to continue.');\n        return null;\n      }\n\n      // Request account access\n      const accounts = await window.ethereum.request({\n        method: 'eth_requestAccounts'\n      });\n      if (accounts.length === 0) {\n        toast.error('No accounts found. Please connect your wallet.');\n        return null;\n      }\n      const address = accounts[0];\n      setWalletAddress(address);\n\n      // Switch to correct network if needed (optional)\n      try {\n        await window.ethereum.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{\n            chainId: '0x89'\n          }] // Polygon mainnet\n        });\n      } catch (switchError) {\n        // If network doesn't exist, add it\n        if (switchError.code === 4902) {\n          try {\n            await window.ethereum.request({\n              method: 'wallet_addEthereumChain',\n              params: [{\n                chainId: '0x89',\n                chainName: 'Polygon Mainnet',\n                nativeCurrency: {\n                  name: 'MATIC',\n                  symbol: 'MATIC',\n                  decimals: 18\n                },\n                rpcUrls: ['https://polygon-rpc.com/'],\n                blockExplorerUrls: ['https://polygonscan.com/']\n              }]\n            });\n          } catch (addError) {\n            console.error('Failed to add network:', addError);\n          }\n        }\n      }\n      return address;\n    } catch (error) {\n      console.error('Wallet connection failed:', error);\n      toast.error('Failed to connect wallet');\n      return null;\n    }\n  };\n  const login = async () => {\n    try {\n      setLoading(true);\n      const address = await connectWallet();\n      if (!address) {\n        setLoading(false);\n        return false;\n      }\n\n      // Get nonce for signing\n      const nonceResponse = await axios.post('/api/auth/nonce', {\n        walletAddress: address\n      });\n      const {\n        message\n      } = nonceResponse.data;\n\n      // Sign message\n      const provider = new ethers.BrowserProvider(window.ethereum);\n      const signer = await provider.getSigner();\n      const signature = await signer.signMessage(message);\n\n      // Login with signature\n      const loginResponse = await axios.post('/api/auth/login', {\n        walletAddress: address,\n        signature,\n        message\n      });\n      const {\n        token: newToken,\n        user: userData\n      } = loginResponse.data;\n\n      // Store token and user data\n      localStorage.setItem('cq_token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      setWalletAddress(address);\n      toast.success('Successfully logged in!');\n      return true;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login failed:', error);\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Login failed');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('cq_token');\n    setToken(null);\n    setUser(null);\n    setWalletAddress(null);\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n  const updateUser = userData => {\n    setUser(prev => ({\n      ...prev,\n      ...userData\n    }));\n  };\n  const refreshUserData = async () => {\n    try {\n      const response = await axios.get('/api/users/profile');\n      setUser(response.data.user);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to refresh user data:', error);\n      return null;\n    }\n  };\n  const value = {\n    user,\n    token,\n    walletAddress,\n    loading,\n    login,\n    logout,\n    connectWallet,\n    updateUser,\n    refreshUserData,\n    isAuthenticated: !!token && !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 194,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"sSCM1cDOUWtlD6RmdOto9Q6OhE4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "ethers", "api", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "loading", "setLoading", "wallet<PERSON>ddress", "setWalletAddress", "axios", "defaults", "headers", "common", "checkAuth", "response", "get", "data", "error", "console", "logout", "connectWallet", "window", "ethereum", "accounts", "request", "method", "length", "address", "params", "chainId", "switchError", "code", "chainName", "nativeCurrency", "name", "symbol", "decimals", "rpcUrls", "blockExplorerUrls", "addError", "login", "nonceResponse", "post", "message", "provider", "Browser<PERSON>rovider", "signer", "<PERSON><PERSON><PERSON><PERSON>", "signature", "signMessage", "loginResponse", "newToken", "userData", "setItem", "success", "_error$response", "_error$response$data", "removeItem", "updateUser", "prev", "refreshUserData", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ethers } from 'ethers';\nimport api from '../utils/api';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('cq_token'));\n  const [loading, setLoading] = useState(true);\n  const [walletAddress, setWalletAddress] = useState(null);\n\n  // Configure axios defaults\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      if (token) {\n        try {\n          const response = await axios.get('/api/users/profile');\n          setUser(response.data.user);\n          setWalletAddress(response.data.user.walletAddress);\n        } catch (error) {\n          console.error('Auth check failed:', error);\n          logout();\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, [token]);\n\n  const connectWallet = async () => {\n    try {\n      if (!window.ethereum) {\n        toast.error('MetaMask is not installed. Please install MetaMask to continue.');\n        return null;\n      }\n\n      // Request account access\n      const accounts = await window.ethereum.request({\n        method: 'eth_requestAccounts',\n      });\n\n      if (accounts.length === 0) {\n        toast.error('No accounts found. Please connect your wallet.');\n        return null;\n      }\n\n      const address = accounts[0];\n      setWalletAddress(address);\n\n      // Switch to correct network if needed (optional)\n      try {\n        await window.ethereum.request({\n          method: 'wallet_switchEthereumChain',\n          params: [{ chainId: '0x89' }], // Polygon mainnet\n        });\n      } catch (switchError) {\n        // If network doesn't exist, add it\n        if (switchError.code === 4902) {\n          try {\n            await window.ethereum.request({\n              method: 'wallet_addEthereumChain',\n              params: [{\n                chainId: '0x89',\n                chainName: 'Polygon Mainnet',\n                nativeCurrency: {\n                  name: 'MATIC',\n                  symbol: 'MATIC',\n                  decimals: 18,\n                },\n                rpcUrls: ['https://polygon-rpc.com/'],\n                blockExplorerUrls: ['https://polygonscan.com/'],\n              }],\n            });\n          } catch (addError) {\n            console.error('Failed to add network:', addError);\n          }\n        }\n      }\n\n      return address;\n    } catch (error) {\n      console.error('Wallet connection failed:', error);\n      toast.error('Failed to connect wallet');\n      return null;\n    }\n  };\n\n  const login = async () => {\n    try {\n      setLoading(true);\n\n      const address = await connectWallet();\n      if (!address) {\n        setLoading(false);\n        return false;\n      }\n\n      // Get nonce for signing\n      const nonceResponse = await axios.post('/api/auth/nonce', {\n        walletAddress: address,\n      });\n\n      const { message } = nonceResponse.data;\n\n      // Sign message\n      const provider = new ethers.BrowserProvider(window.ethereum);\n      const signer = await provider.getSigner();\n      const signature = await signer.signMessage(message);\n\n      // Login with signature\n      const loginResponse = await axios.post('/api/auth/login', {\n        walletAddress: address,\n        signature,\n        message,\n      });\n\n      const { token: newToken, user: userData } = loginResponse.data;\n\n      // Store token and user data\n      localStorage.setItem('cq_token', newToken);\n      setToken(newToken);\n      setUser(userData);\n      setWalletAddress(address);\n\n      toast.success('Successfully logged in!');\n      return true;\n    } catch (error) {\n      console.error('Login failed:', error);\n      toast.error(error.response?.data?.error || 'Login failed');\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('cq_token');\n    setToken(null);\n    setUser(null);\n    setWalletAddress(null);\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n\n  const updateUser = (userData) => {\n    setUser(prev => ({ ...prev, ...userData }));\n  };\n\n  const refreshUserData = async () => {\n    try {\n      const response = await axios.get('/api/users/profile');\n      setUser(response.data.user);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to refresh user data:', error);\n      return null;\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    walletAddress,\n    loading,\n    login,\n    logout,\n    connectWallet,\n    updateUser,\n    refreshUserData,\n    isAuthenticated: !!token && !!user,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,MAAM,QAAQ,QAAQ;AAC/B,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMU,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGX,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAACmB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC;EACpE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgB,KAAK,EAAE;MACTQ,KAAK,CAACC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUX,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOQ,KAAK,CAACC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACX,KAAK,CAAC,CAAC;;EAEX;EACAhB,SAAS,CAAC,MAAM;IACd,MAAM4B,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIZ,KAAK,EAAE;QACT,IAAI;UACF,MAAMa,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,oBAAoB,CAAC;UACtDf,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACjB,IAAI,CAAC;UAC3BS,gBAAgB,CAACM,QAAQ,CAACE,IAAI,CAACjB,IAAI,CAACQ,aAAa,CAAC;QACpD,CAAC,CAAC,OAAOU,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAC1CE,MAAM,CAAC,CAAC;QACV;MACF;MACAb,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDO,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACZ,KAAK,CAAC,CAAC;EAEX,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,IAAI,CAACC,MAAM,CAACC,QAAQ,EAAE;QACpBlC,KAAK,CAAC6B,KAAK,CAAC,iEAAiE,CAAC;QAC9E,OAAO,IAAI;MACb;;MAEA;MACA,MAAMM,QAAQ,GAAG,MAAMF,MAAM,CAACC,QAAQ,CAACE,OAAO,CAAC;QAC7CC,MAAM,EAAE;MACV,CAAC,CAAC;MAEF,IAAIF,QAAQ,CAACG,MAAM,KAAK,CAAC,EAAE;QACzBtC,KAAK,CAAC6B,KAAK,CAAC,gDAAgD,CAAC;QAC7D,OAAO,IAAI;MACb;MAEA,MAAMU,OAAO,GAAGJ,QAAQ,CAAC,CAAC,CAAC;MAC3Bf,gBAAgB,CAACmB,OAAO,CAAC;;MAEzB;MACA,IAAI;QACF,MAAMN,MAAM,CAACC,QAAQ,CAACE,OAAO,CAAC;UAC5BC,MAAM,EAAE,4BAA4B;UACpCG,MAAM,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAO,CAAC,CAAC,CAAE;QACjC,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpB;QACA,IAAIA,WAAW,CAACC,IAAI,KAAK,IAAI,EAAE;UAC7B,IAAI;YACF,MAAMV,MAAM,CAACC,QAAQ,CAACE,OAAO,CAAC;cAC5BC,MAAM,EAAE,yBAAyB;cACjCG,MAAM,EAAE,CAAC;gBACPC,OAAO,EAAE,MAAM;gBACfG,SAAS,EAAE,iBAAiB;gBAC5BC,cAAc,EAAE;kBACdC,IAAI,EAAE,OAAO;kBACbC,MAAM,EAAE,OAAO;kBACfC,QAAQ,EAAE;gBACZ,CAAC;gBACDC,OAAO,EAAE,CAAC,0BAA0B,CAAC;gBACrCC,iBAAiB,EAAE,CAAC,0BAA0B;cAChD,CAAC;YACH,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOC,QAAQ,EAAE;YACjBrB,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEsB,QAAQ,CAAC;UACnD;QACF;MACF;MAEA,OAAOZ,OAAO;IAChB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7B,KAAK,CAAC6B,KAAK,CAAC,0BAA0B,CAAC;MACvC,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAMuB,KAAK,GAAG,MAAAA,CAAA,KAAY;IACxB,IAAI;MACFlC,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMqB,OAAO,GAAG,MAAMP,aAAa,CAAC,CAAC;MACrC,IAAI,CAACO,OAAO,EAAE;QACZrB,UAAU,CAAC,KAAK,CAAC;QACjB,OAAO,KAAK;MACd;;MAEA;MACA,MAAMmC,aAAa,GAAG,MAAMhC,KAAK,CAACiC,IAAI,CAAC,iBAAiB,EAAE;QACxDnC,aAAa,EAAEoB;MACjB,CAAC,CAAC;MAEF,MAAM;QAAEgB;MAAQ,CAAC,GAAGF,aAAa,CAACzB,IAAI;;MAEtC;MACA,MAAM4B,QAAQ,GAAG,IAAI1D,MAAM,CAAC2D,eAAe,CAACxB,MAAM,CAACC,QAAQ,CAAC;MAC5D,MAAMwB,MAAM,GAAG,MAAMF,QAAQ,CAACG,SAAS,CAAC,CAAC;MACzC,MAAMC,SAAS,GAAG,MAAMF,MAAM,CAACG,WAAW,CAACN,OAAO,CAAC;;MAEnD;MACA,MAAMO,aAAa,GAAG,MAAMzC,KAAK,CAACiC,IAAI,CAAC,iBAAiB,EAAE;QACxDnC,aAAa,EAAEoB,OAAO;QACtBqB,SAAS;QACTL;MACF,CAAC,CAAC;MAEF,MAAM;QAAE1C,KAAK,EAAEkD,QAAQ;QAAEpD,IAAI,EAAEqD;MAAS,CAAC,GAAGF,aAAa,CAAClC,IAAI;;MAE9D;MACAb,YAAY,CAACkD,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAAC;MAC1CjD,QAAQ,CAACiD,QAAQ,CAAC;MAClBnD,OAAO,CAACoD,QAAQ,CAAC;MACjB5C,gBAAgB,CAACmB,OAAO,CAAC;MAEzBvC,KAAK,CAACkE,OAAO,CAAC,yBAAyB,CAAC;MACxC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOrC,KAAK,EAAE;MAAA,IAAAsC,eAAA,EAAAC,oBAAA;MACdtC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC7B,KAAK,CAAC6B,KAAK,CAAC,EAAAsC,eAAA,GAAAtC,KAAK,CAACH,QAAQ,cAAAyC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBvC,IAAI,cAAAwC,oBAAA,uBAApBA,oBAAA,CAAsBvC,KAAK,KAAI,cAAc,CAAC;MAC1D,OAAO,KAAK;IACd,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,MAAM,GAAGA,CAAA,KAAM;IACnBhB,YAAY,CAACsD,UAAU,CAAC,UAAU,CAAC;IACnCvD,QAAQ,CAAC,IAAI,CAAC;IACdF,OAAO,CAAC,IAAI,CAAC;IACbQ,gBAAgB,CAAC,IAAI,CAAC;IACtB,OAAOC,KAAK,CAACC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDxB,KAAK,CAACkE,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAMI,UAAU,GAAIN,QAAQ,IAAK;IAC/BpD,OAAO,CAAC2D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGP;IAAS,CAAC,CAAC,CAAC;EAC7C,CAAC;EAED,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAM9C,QAAQ,GAAG,MAAML,KAAK,CAACM,GAAG,CAAC,oBAAoB,CAAC;MACtDf,OAAO,CAACc,QAAQ,CAACE,IAAI,CAACjB,IAAI,CAAC;MAC3B,OAAOe,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAM4C,KAAK,GAAG;IACZ9D,IAAI;IACJE,KAAK;IACLM,aAAa;IACbF,OAAO;IACPmC,KAAK;IACLrB,MAAM;IACNC,aAAa;IACbsC,UAAU;IACVE,eAAe;IACfE,eAAe,EAAE,CAAC,CAAC7D,KAAK,IAAI,CAAC,CAACF;EAChC,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACwE,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAhE,QAAA,EAChCA;EAAQ;IAAAmE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACrE,GAAA,CAtLWF,YAAY;AAAAwE,EAAA,GAAZxE,YAAY;AAAA,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}