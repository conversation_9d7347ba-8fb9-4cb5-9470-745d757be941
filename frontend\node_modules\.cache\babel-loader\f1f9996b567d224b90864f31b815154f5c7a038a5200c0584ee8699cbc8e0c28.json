{"ast": null, "code": "/**\n *  Generally the [[Wallet]] and [[JsonRpcSigner]] and their sub-classes\n *  are sufficent for most developers, but this is provided to\n *  fascilitate more complex Signers.\n *\n *  @_section: api/providers/abstract-signer: Subclassing Signer [abstract-signer]\n */\nimport { resolveAddress } from \"../address/index.js\";\nimport { Transaction } from \"../transaction/index.js\";\nimport { defineProperties, getBigInt, resolveProperties, assert, assertArgument } from \"../utils/index.js\";\nimport { copyRequest } from \"./provider.js\";\nfunction checkProvider(signer, operation) {\n  if (signer.provider) {\n    return signer.provider;\n  }\n  assert(false, \"missing provider\", \"UNSUPPORTED_OPERATION\", {\n    operation\n  });\n}\nasync function populate(signer, tx) {\n  let pop = copyRequest(tx);\n  if (pop.to != null) {\n    pop.to = resolveAddress(pop.to, signer);\n  }\n  if (pop.from != null) {\n    const from = pop.from;\n    pop.from = Promise.all([signer.getAddress(), resolveAddress(from, signer)]).then(([address, from]) => {\n      assertArgument(address.toLowerCase() === from.toLowerCase(), \"transaction from mismatch\", \"tx.from\", from);\n      return address;\n    });\n  } else {\n    pop.from = signer.getAddress();\n  }\n  return await resolveProperties(pop);\n}\n/**\n *  An **AbstractSigner** includes most of teh functionality required\n *  to get a [[Signer]] working as expected, but requires a few\n *  Signer-specific methods be overridden.\n *\n */\nexport class AbstractSigner {\n  /**\n   *  The provider this signer is connected to.\n   */\n  provider;\n  /**\n   *  Creates a new Signer connected to %%provider%%.\n   */\n  constructor(provider) {\n    defineProperties(this, {\n      provider: provider || null\n    });\n  }\n  async getNonce(blockTag) {\n    return checkProvider(this, \"getTransactionCount\").getTransactionCount(await this.getAddress(), blockTag);\n  }\n  async populateCall(tx) {\n    const pop = await populate(this, tx);\n    return pop;\n  }\n  async populateTransaction(tx) {\n    const provider = checkProvider(this, \"populateTransaction\");\n    const pop = await populate(this, tx);\n    if (pop.nonce == null) {\n      pop.nonce = await this.getNonce(\"pending\");\n    }\n    if (pop.gasLimit == null) {\n      pop.gasLimit = await this.estimateGas(pop);\n    }\n    // Populate the chain ID\n    const network = await this.provider.getNetwork();\n    if (pop.chainId != null) {\n      const chainId = getBigInt(pop.chainId);\n      assertArgument(chainId === network.chainId, \"transaction chainId mismatch\", \"tx.chainId\", tx.chainId);\n    } else {\n      pop.chainId = network.chainId;\n    }\n    // Do not allow mixing pre-eip-1559 and eip-1559 properties\n    const hasEip1559 = pop.maxFeePerGas != null || pop.maxPriorityFeePerGas != null;\n    if (pop.gasPrice != null && (pop.type === 2 || hasEip1559)) {\n      assertArgument(false, \"eip-1559 transaction do not support gasPrice\", \"tx\", tx);\n    } else if ((pop.type === 0 || pop.type === 1) && hasEip1559) {\n      assertArgument(false, \"pre-eip-1559 transaction do not support maxFeePerGas/maxPriorityFeePerGas\", \"tx\", tx);\n    }\n    if ((pop.type === 2 || pop.type == null) && pop.maxFeePerGas != null && pop.maxPriorityFeePerGas != null) {\n      // Fully-formed EIP-1559 transaction (skip getFeeData)\n      pop.type = 2;\n    } else if (pop.type === 0 || pop.type === 1) {\n      // Explicit Legacy or EIP-2930 transaction\n      // We need to get fee data to determine things\n      const feeData = await provider.getFeeData();\n      assert(feeData.gasPrice != null, \"network does not support gasPrice\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"getGasPrice\"\n      });\n      // Populate missing gasPrice\n      if (pop.gasPrice == null) {\n        pop.gasPrice = feeData.gasPrice;\n      }\n    } else {\n      // We need to get fee data to determine things\n      const feeData = await provider.getFeeData();\n      if (pop.type == null) {\n        // We need to auto-detect the intended type of this transaction...\n        if (feeData.maxFeePerGas != null && feeData.maxPriorityFeePerGas != null) {\n          // The network supports EIP-1559!\n          // Upgrade transaction from null to eip-1559\n          if (pop.authorizationList && pop.authorizationList.length) {\n            pop.type = 4;\n          } else {\n            pop.type = 2;\n          }\n          if (pop.gasPrice != null) {\n            // Using legacy gasPrice property on an eip-1559 network,\n            // so use gasPrice as both fee properties\n            const gasPrice = pop.gasPrice;\n            delete pop.gasPrice;\n            pop.maxFeePerGas = gasPrice;\n            pop.maxPriorityFeePerGas = gasPrice;\n          } else {\n            // Populate missing fee data\n            if (pop.maxFeePerGas == null) {\n              pop.maxFeePerGas = feeData.maxFeePerGas;\n            }\n            if (pop.maxPriorityFeePerGas == null) {\n              pop.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;\n            }\n          }\n        } else if (feeData.gasPrice != null) {\n          // Network doesn't support EIP-1559...\n          // ...but they are trying to use EIP-1559 properties\n          assert(!hasEip1559, \"network does not support EIP-1559\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"populateTransaction\"\n          });\n          // Populate missing fee data\n          if (pop.gasPrice == null) {\n            pop.gasPrice = feeData.gasPrice;\n          }\n          // Explicitly set untyped transaction to legacy\n          // @TODO: Maybe this shold allow type 1?\n          pop.type = 0;\n        } else {\n          // getFeeData has failed us.\n          assert(false, \"failed to get consistent fee data\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"signer.getFeeData\"\n          });\n        }\n      } else if (pop.type === 2 || pop.type === 3 || pop.type === 4) {\n        // Explicitly using EIP-1559 or EIP-4844\n        // Populate missing fee data\n        if (pop.maxFeePerGas == null) {\n          pop.maxFeePerGas = feeData.maxFeePerGas;\n        }\n        if (pop.maxPriorityFeePerGas == null) {\n          pop.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;\n        }\n      }\n    }\n    //@TOOD: Don't await all over the place; save them up for\n    // the end for better batching\n    return await resolveProperties(pop);\n  }\n  async populateAuthorization(_auth) {\n    const auth = Object.assign({}, _auth);\n    // Add a chain ID if not explicitly set to 0\n    if (auth.chainId == null) {\n      auth.chainId = (await checkProvider(this, \"getNetwork\").getNetwork()).chainId;\n    }\n    // @TODO: Take chain ID into account when populating noce?\n    if (auth.nonce == null) {\n      auth.nonce = await this.getNonce();\n    }\n    return auth;\n  }\n  async estimateGas(tx) {\n    return checkProvider(this, \"estimateGas\").estimateGas(await this.populateCall(tx));\n  }\n  async call(tx) {\n    return checkProvider(this, \"call\").call(await this.populateCall(tx));\n  }\n  async resolveName(name) {\n    const provider = checkProvider(this, \"resolveName\");\n    return await provider.resolveName(name);\n  }\n  async sendTransaction(tx) {\n    const provider = checkProvider(this, \"sendTransaction\");\n    const pop = await this.populateTransaction(tx);\n    delete pop.from;\n    const txObj = Transaction.from(pop);\n    return await provider.broadcastTransaction(await this.signTransaction(txObj));\n  }\n  // @TODO: in v7 move this to be abstract\n  authorize(authorization) {\n    assert(false, \"authorization not implemented for this signer\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"authorize\"\n    });\n  }\n}\n/**\n *  A **VoidSigner** is a class deisgned to allow an address to be used\n *  in any API which accepts a Signer, but for which there are no\n *  credentials available to perform any actual signing.\n *\n *  This for example allow impersonating an account for the purpose of\n *  static calls or estimating gas, but does not allow sending transactions.\n */\nexport class VoidSigner extends AbstractSigner {\n  /**\n   *  The signer address.\n   */\n  address;\n  /**\n   *  Creates a new **VoidSigner** with %%address%% attached to\n   *  %%provider%%.\n   */\n  constructor(address, provider) {\n    super(provider);\n    defineProperties(this, {\n      address\n    });\n  }\n  async getAddress() {\n    return this.address;\n  }\n  connect(provider) {\n    return new VoidSigner(this.address, provider);\n  }\n  #throwUnsupported(suffix, operation) {\n    assert(false, `VoidSigner cannot sign ${suffix}`, \"UNSUPPORTED_OPERATION\", {\n      operation\n    });\n  }\n  async signTransaction(tx) {\n    this.#throwUnsupported(\"transactions\", \"signTransaction\");\n  }\n  async signMessage(message) {\n    this.#throwUnsupported(\"messages\", \"signMessage\");\n  }\n  async signTypedData(domain, types, value) {\n    this.#throwUnsupported(\"typed-data\", \"signTypedData\");\n  }\n}", "map": {"version": 3, "names": ["resolve<PERSON>ddress", "Transaction", "defineProperties", "getBigInt", "resolveProperties", "assert", "assertArgument", "copyRequest", "checkProvider", "signer", "operation", "provider", "populate", "tx", "pop", "to", "from", "Promise", "all", "get<PERSON><PERSON><PERSON>", "then", "address", "toLowerCase", "Abstract<PERSON><PERSON><PERSON>", "constructor", "getNonce", "blockTag", "getTransactionCount", "populateCall", "populateTransaction", "nonce", "gasLimit", "estimateGas", "network", "getNetwork", "chainId", "hasEip1559", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "gasPrice", "type", "feeData", "getFeeData", "authorizationList", "length", "populateAuthorization", "_auth", "auth", "Object", "assign", "call", "resolveName", "name", "sendTransaction", "txObj", "broadcastTransaction", "signTransaction", "authorize", "authorization", "<PERSON>oid<PERSON><PERSON><PERSON>", "connect", "throwUnsupported", "#throwUnsupported", "suffix", "signMessage", "message", "signTypedData", "domain", "types", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\abstract-signer.ts"], "sourcesContent": ["/**\n *  Generally the [[Wallet]] and [[JsonRpcSigner]] and their sub-classes\n *  are sufficent for most developers, but this is provided to\n *  fascilitate more complex Signers.\n *\n *  @_section: api/providers/abstract-signer: Subclassing Signer [abstract-signer]\n */\nimport { resolveAddress } from \"../address/index.js\";\nimport { Transaction } from \"../transaction/index.js\";\nimport {\n    defineProperties, getBigInt, resolveProperties,\n    assert, assertArgument\n} from \"../utils/index.js\";\n\nimport { copyRequest } from \"./provider.js\";\n\nimport type {\n    AuthorizationRequest, TypedDataDomain, TypedDataField\n} from \"../hash/index.js\";\nimport type { Authorization, TransactionLike } from \"../transaction/index.js\";\n\nimport type {\n    BlockTag, Provider, TransactionRequest, TransactionResponse\n} from \"./provider.js\";\nimport type { Signer } from \"./signer.js\";\n\nfunction checkProvider(signer: AbstractSigner, operation: string): Provider {\n    if (signer.provider) { return signer.provider; }\n    assert(false, \"missing provider\", \"UNSUPPORTED_OPERATION\", { operation });\n}\n\nasync function populate(signer: AbstractSigner, tx: TransactionRequest): Promise<TransactionLike<string>> {\n    let pop: any = copyRequest(tx);\n\n    if (pop.to != null) { pop.to = resolveAddress(pop.to, signer); }\n\n    if (pop.from != null) {\n        const from = pop.from;\n        pop.from = Promise.all([\n            signer.getAddress(),\n            resolveAddress(from, signer)\n        ]).then(([ address, from ]) => {\n            assertArgument(address.toLowerCase() === from.toLowerCase(),\n                \"transaction from mismatch\", \"tx.from\", from);\n            return address;\n        });\n    } else {\n        pop.from = signer.getAddress();\n    }\n\n    return await resolveProperties(pop);\n}\n\n\n/**\n *  An **AbstractSigner** includes most of teh functionality required\n *  to get a [[Signer]] working as expected, but requires a few\n *  Signer-specific methods be overridden.\n *\n */\nexport abstract class AbstractSigner<P extends null | Provider = null | Provider> implements Signer {\n    /**\n     *  The provider this signer is connected to.\n     */\n    readonly provider!: P;\n\n    /**\n     *  Creates a new Signer connected to %%provider%%.\n     */\n    constructor(provider?: P) {\n        defineProperties<AbstractSigner>(this, { provider: (provider || null) });\n    }\n\n    /**\n     *  Resolves to the Signer address.\n     */\n    abstract getAddress(): Promise<string>;\n\n    /**\n     *  Returns the signer connected to %%provider%%.\n     *\n     *  This may throw, for example, a Signer connected over a Socket or\n     *  to a specific instance of a node may not be transferrable.\n     */\n    abstract connect(provider: null | Provider): Signer;\n\n    async getNonce(blockTag?: BlockTag): Promise<number> {\n        return checkProvider(this, \"getTransactionCount\").getTransactionCount(await this.getAddress(), blockTag);\n    }\n\n    async populateCall(tx: TransactionRequest): Promise<TransactionLike<string>> {\n        const pop = await populate(this, tx);\n        return pop;\n    }\n\n    async populateTransaction(tx: TransactionRequest): Promise<TransactionLike<string>> {\n        const provider = checkProvider(this, \"populateTransaction\");\n\n        const pop = await populate(this, tx);\n\n        if (pop.nonce == null) {\n            pop.nonce = await this.getNonce(\"pending\");\n        }\n\n        if (pop.gasLimit == null) {\n            pop.gasLimit = await this.estimateGas(pop);\n        }\n\n        // Populate the chain ID\n        const network = await (<Provider>(this.provider)).getNetwork();\n        if (pop.chainId != null) {\n            const chainId = getBigInt(pop.chainId);\n            assertArgument(chainId === network.chainId, \"transaction chainId mismatch\", \"tx.chainId\", tx.chainId);\n        } else {\n            pop.chainId = network.chainId;\n        }\n\n        // Do not allow mixing pre-eip-1559 and eip-1559 properties\n        const hasEip1559 = (pop.maxFeePerGas != null || pop.maxPriorityFeePerGas != null);\n        if (pop.gasPrice != null && (pop.type === 2 || hasEip1559)) {\n            assertArgument(false, \"eip-1559 transaction do not support gasPrice\", \"tx\", tx);\n        } else if ((pop.type === 0 || pop.type === 1) && hasEip1559) {\n            assertArgument(false, \"pre-eip-1559 transaction do not support maxFeePerGas/maxPriorityFeePerGas\", \"tx\", tx);\n        }\n\n        if ((pop.type === 2 || pop.type == null) && (pop.maxFeePerGas != null && pop.maxPriorityFeePerGas != null)) {\n            // Fully-formed EIP-1559 transaction (skip getFeeData)\n            pop.type = 2;\n\n        } else if (pop.type === 0 || pop.type === 1) {\n            // Explicit Legacy or EIP-2930 transaction\n\n            // We need to get fee data to determine things\n            const feeData = await provider.getFeeData();\n\n            assert(feeData.gasPrice != null, \"network does not support gasPrice\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"getGasPrice\" });\n\n            // Populate missing gasPrice\n            if (pop.gasPrice == null) { pop.gasPrice = feeData.gasPrice; }\n\n        } else {\n\n            // We need to get fee data to determine things\n            const feeData = await provider.getFeeData();\n\n            if (pop.type == null) {\n                // We need to auto-detect the intended type of this transaction...\n\n                if (feeData.maxFeePerGas != null && feeData.maxPriorityFeePerGas != null) {\n                    // The network supports EIP-1559!\n\n                    // Upgrade transaction from null to eip-1559\n                    if (pop.authorizationList && pop.authorizationList.length) {\n                        pop.type = 4;\n                    } else {\n                        pop.type = 2;\n                    }\n\n                    if (pop.gasPrice != null) {\n                        // Using legacy gasPrice property on an eip-1559 network,\n                        // so use gasPrice as both fee properties\n                        const gasPrice = pop.gasPrice;\n                        delete pop.gasPrice;\n                        pop.maxFeePerGas = gasPrice;\n                        pop.maxPriorityFeePerGas = gasPrice;\n\n                    } else {\n                        // Populate missing fee data\n\n                        if (pop.maxFeePerGas == null) {\n                            pop.maxFeePerGas = feeData.maxFeePerGas;\n                        }\n\n                        if (pop.maxPriorityFeePerGas == null) {\n                            pop.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;\n                        }\n                    }\n\n                } else if (feeData.gasPrice != null) {\n                    // Network doesn't support EIP-1559...\n\n                    // ...but they are trying to use EIP-1559 properties\n                    assert(!hasEip1559, \"network does not support EIP-1559\", \"UNSUPPORTED_OPERATION\", {\n                            operation: \"populateTransaction\" });\n\n                    // Populate missing fee data\n                    if (pop.gasPrice == null) {\n                        pop.gasPrice = feeData.gasPrice;\n                    }\n\n                    // Explicitly set untyped transaction to legacy\n                    // @TODO: Maybe this shold allow type 1?\n                    pop.type = 0;\n\n               } else {\n                    // getFeeData has failed us.\n                    assert(false, \"failed to get consistent fee data\", \"UNSUPPORTED_OPERATION\", {\n                        operation: \"signer.getFeeData\" });\n                }\n\n            } else if (pop.type === 2 || pop.type === 3 || pop.type === 4) {\n                // Explicitly using EIP-1559 or EIP-4844\n\n                // Populate missing fee data\n                if (pop.maxFeePerGas == null) {\n                    pop.maxFeePerGas = feeData.maxFeePerGas;\n                }\n\n                if (pop.maxPriorityFeePerGas == null) {\n                    pop.maxPriorityFeePerGas = feeData.maxPriorityFeePerGas;\n                }\n            }\n        }\n\n//@TOOD: Don't await all over the place; save them up for\n// the end for better batching\n        return await resolveProperties(pop);\n    }\n\n    async populateAuthorization(_auth: AuthorizationRequest): Promise<AuthorizationRequest> {\n        const auth = Object.assign({ }, _auth);\n\n        // Add a chain ID if not explicitly set to 0\n        if (auth.chainId == null) {\n            auth.chainId = (await checkProvider(this, \"getNetwork\").getNetwork()).chainId;\n        }\n\n        // @TODO: Take chain ID into account when populating noce?\n\n        if (auth.nonce == null) { auth.nonce = await this.getNonce(); }\n\n        return auth;\n    }\n\n    async estimateGas(tx: TransactionRequest): Promise<bigint> {\n        return checkProvider(this, \"estimateGas\").estimateGas(await this.populateCall(tx));\n    }\n\n    async call(tx: TransactionRequest): Promise<string> {\n        return checkProvider(this, \"call\").call(await this.populateCall(tx));\n    }\n\n    async resolveName(name: string): Promise<null | string> {\n        const provider = checkProvider(this, \"resolveName\");\n        return await provider.resolveName(name);\n    }\n\n    async sendTransaction(tx: TransactionRequest): Promise<TransactionResponse> {\n        const provider = checkProvider(this, \"sendTransaction\");\n\n        const pop = await this.populateTransaction(tx);\n        delete pop.from;\n        const txObj = Transaction.from(pop);\n        return await provider.broadcastTransaction(await this.signTransaction(txObj));\n    }\n\n    // @TODO: in v7 move this to be abstract\n    authorize(authorization: AuthorizationRequest): Promise<Authorization> {\n        assert(false, \"authorization not implemented for this signer\",\n          \"UNSUPPORTED_OPERATION\", { operation: \"authorize\" });\n    }\n\n    abstract signTransaction(tx: TransactionRequest): Promise<string>;\n    abstract signMessage(message: string | Uint8Array): Promise<string>;\n    abstract signTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): Promise<string>;\n}\n\n/**\n *  A **VoidSigner** is a class deisgned to allow an address to be used\n *  in any API which accepts a Signer, but for which there are no\n *  credentials available to perform any actual signing.\n *\n *  This for example allow impersonating an account for the purpose of\n *  static calls or estimating gas, but does not allow sending transactions.\n */\nexport class VoidSigner extends AbstractSigner {\n    /**\n     *  The signer address.\n     */\n    readonly address!: string;\n\n    /**\n     *  Creates a new **VoidSigner** with %%address%% attached to\n     *  %%provider%%.\n     */\n    constructor(address: string, provider?: null | Provider) {\n        super(provider);\n        defineProperties<VoidSigner>(this, { address });\n    }\n\n    async getAddress(): Promise<string> { return this.address; }\n\n    connect(provider: null | Provider): VoidSigner {\n        return new VoidSigner(this.address, provider);\n    }\n\n    #throwUnsupported(suffix: string, operation: string): never {\n        assert(false, `VoidSigner cannot sign ${ suffix }`, \"UNSUPPORTED_OPERATION\", { operation });\n    }\n\n    async signTransaction(tx: TransactionRequest): Promise<string> {\n        this.#throwUnsupported(\"transactions\", \"signTransaction\");\n    }\n\n    async signMessage(message: string | Uint8Array): Promise<string> {\n        this.#throwUnsupported(\"messages\", \"signMessage\");\n    }\n\n    async signTypedData(domain: TypedDataDomain, types: Record<string, Array<TypedDataField>>, value: Record<string, any>): Promise<string> {\n        this.#throwUnsupported(\"typed-data\", \"signTypedData\");\n    }\n}\n\n"], "mappings": "AAAA;;;;;;;AAOA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SACIC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAC9CC,MAAM,EAAEC,cAAc,QACnB,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,eAAe;AAY3C,SAASC,aAAaA,CAACC,MAAsB,EAAEC,SAAiB;EAC5D,IAAID,MAAM,CAACE,QAAQ,EAAE;IAAE,OAAOF,MAAM,CAACE,QAAQ;;EAC7CN,MAAM,CAAC,KAAK,EAAE,kBAAkB,EAAE,uBAAuB,EAAE;IAAEK;EAAS,CAAE,CAAC;AAC7E;AAEA,eAAeE,QAAQA,CAACH,MAAsB,EAAEI,EAAsB;EAClE,IAAIC,GAAG,GAAQP,WAAW,CAACM,EAAE,CAAC;EAE9B,IAAIC,GAAG,CAACC,EAAE,IAAI,IAAI,EAAE;IAAED,GAAG,CAACC,EAAE,GAAGf,cAAc,CAACc,GAAG,CAACC,EAAE,EAAEN,MAAM,CAAC;;EAE7D,IAAIK,GAAG,CAACE,IAAI,IAAI,IAAI,EAAE;IAClB,MAAMA,IAAI,GAAGF,GAAG,CAACE,IAAI;IACrBF,GAAG,CAACE,IAAI,GAAGC,OAAO,CAACC,GAAG,CAAC,CACnBT,MAAM,CAACU,UAAU,EAAE,EACnBnB,cAAc,CAACgB,IAAI,EAAEP,MAAM,CAAC,CAC/B,CAAC,CAACW,IAAI,CAAC,CAAC,CAAEC,OAAO,EAAEL,IAAI,CAAE,KAAI;MAC1BV,cAAc,CAACe,OAAO,CAACC,WAAW,EAAE,KAAKN,IAAI,CAACM,WAAW,EAAE,EACvD,2BAA2B,EAAE,SAAS,EAAEN,IAAI,CAAC;MACjD,OAAOK,OAAO;IAClB,CAAC,CAAC;GACL,MAAM;IACHP,GAAG,CAACE,IAAI,GAAGP,MAAM,CAACU,UAAU,EAAE;;EAGlC,OAAO,MAAMf,iBAAiB,CAACU,GAAG,CAAC;AACvC;AAGA;;;;;;AAMA,OAAM,MAAgBS,cAAc;EAChC;;;EAGSZ,QAAQ;EAEjB;;;EAGAa,YAAYb,QAAY;IACpBT,gBAAgB,CAAiB,IAAI,EAAE;MAAES,QAAQ,EAAGA,QAAQ,IAAI;IAAK,CAAE,CAAC;EAC5E;EAeA,MAAMc,QAAQA,CAACC,QAAmB;IAC9B,OAAOlB,aAAa,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAACmB,mBAAmB,CAAC,MAAM,IAAI,CAACR,UAAU,EAAE,EAAEO,QAAQ,CAAC;EAC5G;EAEA,MAAME,YAAYA,CAACf,EAAsB;IACrC,MAAMC,GAAG,GAAG,MAAMF,QAAQ,CAAC,IAAI,EAAEC,EAAE,CAAC;IACpC,OAAOC,GAAG;EACd;EAEA,MAAMe,mBAAmBA,CAAChB,EAAsB;IAC5C,MAAMF,QAAQ,GAAGH,aAAa,CAAC,IAAI,EAAE,qBAAqB,CAAC;IAE3D,MAAMM,GAAG,GAAG,MAAMF,QAAQ,CAAC,IAAI,EAAEC,EAAE,CAAC;IAEpC,IAAIC,GAAG,CAACgB,KAAK,IAAI,IAAI,EAAE;MACnBhB,GAAG,CAACgB,KAAK,GAAG,MAAM,IAAI,CAACL,QAAQ,CAAC,SAAS,CAAC;;IAG9C,IAAIX,GAAG,CAACiB,QAAQ,IAAI,IAAI,EAAE;MACtBjB,GAAG,CAACiB,QAAQ,GAAG,MAAM,IAAI,CAACC,WAAW,CAAClB,GAAG,CAAC;;IAG9C;IACA,MAAMmB,OAAO,GAAG,MAAkB,IAAI,CAACtB,QAAQ,CAAGuB,UAAU,EAAE;IAC9D,IAAIpB,GAAG,CAACqB,OAAO,IAAI,IAAI,EAAE;MACrB,MAAMA,OAAO,GAAGhC,SAAS,CAACW,GAAG,CAACqB,OAAO,CAAC;MACtC7B,cAAc,CAAC6B,OAAO,KAAKF,OAAO,CAACE,OAAO,EAAE,8BAA8B,EAAE,YAAY,EAAEtB,EAAE,CAACsB,OAAO,CAAC;KACxG,MAAM;MACHrB,GAAG,CAACqB,OAAO,GAAGF,OAAO,CAACE,OAAO;;IAGjC;IACA,MAAMC,UAAU,GAAItB,GAAG,CAACuB,YAAY,IAAI,IAAI,IAAIvB,GAAG,CAACwB,oBAAoB,IAAI,IAAK;IACjF,IAAIxB,GAAG,CAACyB,QAAQ,IAAI,IAAI,KAAKzB,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAIJ,UAAU,CAAC,EAAE;MACxD9B,cAAc,CAAC,KAAK,EAAE,8CAA8C,EAAE,IAAI,EAAEO,EAAE,CAAC;KAClF,MAAM,IAAI,CAACC,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAI1B,GAAG,CAAC0B,IAAI,KAAK,CAAC,KAAKJ,UAAU,EAAE;MACzD9B,cAAc,CAAC,KAAK,EAAE,2EAA2E,EAAE,IAAI,EAAEO,EAAE,CAAC;;IAGhH,IAAI,CAACC,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAI1B,GAAG,CAAC0B,IAAI,IAAI,IAAI,KAAM1B,GAAG,CAACuB,YAAY,IAAI,IAAI,IAAIvB,GAAG,CAACwB,oBAAoB,IAAI,IAAK,EAAE;MACxG;MACAxB,GAAG,CAAC0B,IAAI,GAAG,CAAC;KAEf,MAAM,IAAI1B,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAI1B,GAAG,CAAC0B,IAAI,KAAK,CAAC,EAAE;MACzC;MAEA;MACA,MAAMC,OAAO,GAAG,MAAM9B,QAAQ,CAAC+B,UAAU,EAAE;MAE3CrC,MAAM,CAACoC,OAAO,CAACF,QAAQ,IAAI,IAAI,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;QAC3F7B,SAAS,EAAE;OAAe,CAAC;MAE/B;MACA,IAAII,GAAG,CAACyB,QAAQ,IAAI,IAAI,EAAE;QAAEzB,GAAG,CAACyB,QAAQ,GAAGE,OAAO,CAACF,QAAQ;;KAE9D,MAAM;MAEH;MACA,MAAME,OAAO,GAAG,MAAM9B,QAAQ,CAAC+B,UAAU,EAAE;MAE3C,IAAI5B,GAAG,CAAC0B,IAAI,IAAI,IAAI,EAAE;QAClB;QAEA,IAAIC,OAAO,CAACJ,YAAY,IAAI,IAAI,IAAII,OAAO,CAACH,oBAAoB,IAAI,IAAI,EAAE;UACtE;UAEA;UACA,IAAIxB,GAAG,CAAC6B,iBAAiB,IAAI7B,GAAG,CAAC6B,iBAAiB,CAACC,MAAM,EAAE;YACvD9B,GAAG,CAAC0B,IAAI,GAAG,CAAC;WACf,MAAM;YACH1B,GAAG,CAAC0B,IAAI,GAAG,CAAC;;UAGhB,IAAI1B,GAAG,CAACyB,QAAQ,IAAI,IAAI,EAAE;YACtB;YACA;YACA,MAAMA,QAAQ,GAAGzB,GAAG,CAACyB,QAAQ;YAC7B,OAAOzB,GAAG,CAACyB,QAAQ;YACnBzB,GAAG,CAACuB,YAAY,GAAGE,QAAQ;YAC3BzB,GAAG,CAACwB,oBAAoB,GAAGC,QAAQ;WAEtC,MAAM;YACH;YAEA,IAAIzB,GAAG,CAACuB,YAAY,IAAI,IAAI,EAAE;cAC1BvB,GAAG,CAACuB,YAAY,GAAGI,OAAO,CAACJ,YAAY;;YAG3C,IAAIvB,GAAG,CAACwB,oBAAoB,IAAI,IAAI,EAAE;cAClCxB,GAAG,CAACwB,oBAAoB,GAAGG,OAAO,CAACH,oBAAoB;;;SAIlE,MAAM,IAAIG,OAAO,CAACF,QAAQ,IAAI,IAAI,EAAE;UACjC;UAEA;UACAlC,MAAM,CAAC,CAAC+B,UAAU,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;YAC1E1B,SAAS,EAAE;WAAuB,CAAC;UAE3C;UACA,IAAII,GAAG,CAACyB,QAAQ,IAAI,IAAI,EAAE;YACtBzB,GAAG,CAACyB,QAAQ,GAAGE,OAAO,CAACF,QAAQ;;UAGnC;UACA;UACAzB,GAAG,CAAC0B,IAAI,GAAG,CAAC;SAEhB,MAAM;UACF;UACAnC,MAAM,CAAC,KAAK,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;YACxEK,SAAS,EAAE;WAAqB,CAAC;;OAG5C,MAAM,IAAII,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAI1B,GAAG,CAAC0B,IAAI,KAAK,CAAC,IAAI1B,GAAG,CAAC0B,IAAI,KAAK,CAAC,EAAE;QAC3D;QAEA;QACA,IAAI1B,GAAG,CAACuB,YAAY,IAAI,IAAI,EAAE;UAC1BvB,GAAG,CAACuB,YAAY,GAAGI,OAAO,CAACJ,YAAY;;QAG3C,IAAIvB,GAAG,CAACwB,oBAAoB,IAAI,IAAI,EAAE;UAClCxB,GAAG,CAACwB,oBAAoB,GAAGG,OAAO,CAACH,oBAAoB;;;;IAK3E;IACA;IACQ,OAAO,MAAMlC,iBAAiB,CAACU,GAAG,CAAC;EACvC;EAEA,MAAM+B,qBAAqBA,CAACC,KAA2B;IACnD,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEH,KAAK,CAAC;IAEtC;IACA,IAAIC,IAAI,CAACZ,OAAO,IAAI,IAAI,EAAE;MACtBY,IAAI,CAACZ,OAAO,GAAG,CAAC,MAAM3B,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC0B,UAAU,EAAE,EAAEC,OAAO;;IAGjF;IAEA,IAAIY,IAAI,CAACjB,KAAK,IAAI,IAAI,EAAE;MAAEiB,IAAI,CAACjB,KAAK,GAAG,MAAM,IAAI,CAACL,QAAQ,EAAE;;IAE5D,OAAOsB,IAAI;EACf;EAEA,MAAMf,WAAWA,CAACnB,EAAsB;IACpC,OAAOL,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAACwB,WAAW,CAAC,MAAM,IAAI,CAACJ,YAAY,CAACf,EAAE,CAAC,CAAC;EACtF;EAEA,MAAMqC,IAAIA,CAACrC,EAAsB;IAC7B,OAAOL,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC0C,IAAI,CAAC,MAAM,IAAI,CAACtB,YAAY,CAACf,EAAE,CAAC,CAAC;EACxE;EAEA,MAAMsC,WAAWA,CAACC,IAAY;IAC1B,MAAMzC,QAAQ,GAAGH,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC;IACnD,OAAO,MAAMG,QAAQ,CAACwC,WAAW,CAACC,IAAI,CAAC;EAC3C;EAEA,MAAMC,eAAeA,CAACxC,EAAsB;IACxC,MAAMF,QAAQ,GAAGH,aAAa,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAEvD,MAAMM,GAAG,GAAG,MAAM,IAAI,CAACe,mBAAmB,CAAChB,EAAE,CAAC;IAC9C,OAAOC,GAAG,CAACE,IAAI;IACf,MAAMsC,KAAK,GAAGrD,WAAW,CAACe,IAAI,CAACF,GAAG,CAAC;IACnC,OAAO,MAAMH,QAAQ,CAAC4C,oBAAoB,CAAC,MAAM,IAAI,CAACC,eAAe,CAACF,KAAK,CAAC,CAAC;EACjF;EAEA;EACAG,SAASA,CAACC,aAAmC;IACzCrD,MAAM,CAAC,KAAK,EAAE,+CAA+C,EAC3D,uBAAuB,EAAE;MAAEK,SAAS,EAAE;IAAW,CAAE,CAAC;EAC1D;;AAOJ;;;;;;;;AAQA,OAAM,MAAOiD,UAAW,SAAQpC,cAAc;EAC1C;;;EAGSF,OAAO;EAEhB;;;;EAIAG,YAAYH,OAAe,EAAEV,QAA0B;IACnD,KAAK,CAACA,QAAQ,CAAC;IACfT,gBAAgB,CAAa,IAAI,EAAE;MAAEmB;IAAO,CAAE,CAAC;EACnD;EAEA,MAAMF,UAAUA,CAAA;IAAsB,OAAO,IAAI,CAACE,OAAO;EAAE;EAE3DuC,OAAOA,CAACjD,QAAyB;IAC7B,OAAO,IAAIgD,UAAU,CAAC,IAAI,CAACtC,OAAO,EAAEV,QAAQ,CAAC;EACjD;EAEA,CAAAkD,gBAAiBC,CAACC,MAAc,EAAErD,SAAiB;IAC/CL,MAAM,CAAC,KAAK,EAAE,0BAA2B0D,MAAO,EAAE,EAAE,uBAAuB,EAAE;MAAErD;IAAS,CAAE,CAAC;EAC/F;EAEA,MAAM8C,eAAeA,CAAC3C,EAAsB;IACxC,IAAI,CAAC,CAAAgD,gBAAiB,CAAC,cAAc,EAAE,iBAAiB,CAAC;EAC7D;EAEA,MAAMG,WAAWA,CAACC,OAA4B;IAC1C,IAAI,CAAC,CAAAJ,gBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC;EACrD;EAEA,MAAMK,aAAaA,CAACC,MAAuB,EAAEC,KAA4C,EAAEC,KAA0B;IACjH,IAAI,CAAC,CAAAR,gBAAiB,CAAC,YAAY,EAAE,eAAe,CAAC;EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}