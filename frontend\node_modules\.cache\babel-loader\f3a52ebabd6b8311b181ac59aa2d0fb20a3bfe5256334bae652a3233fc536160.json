{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport { mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nimport { bytesToNumberBE, concatBytes, ensureBytes, numberToBytesBE } from './abstract/utils.js';\nimport { createHasher, isogenyMap } from './abstract/hash-to-curve.js';\nimport { createCurve } from './_shortw_utils.js';\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a, b) => (a + b / _2n) / b;\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y) {\n  const P = secp256k1P;\n  // prettier-ignore\n  const _3n = BigInt(3),\n    _6n = BigInt(6),\n    _11n = BigInt(11),\n    _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23),\n    _44n = BigInt(44),\n    _88n = BigInt(88);\n  const b2 = y * y * y % P; // x^3, 11\n  const b3 = b2 * b2 * y % P; // x^7\n  const b6 = pow2(b3, _3n, P) * b3 % P;\n  const b9 = pow2(b6, _3n, P) * b3 % P;\n  const b11 = pow2(b9, _2n, P) * b2 % P;\n  const b22 = pow2(b11, _11n, P) * b11 % P;\n  const b44 = pow2(b22, _22n, P) * b22 % P;\n  const b88 = pow2(b44, _44n, P) * b44 % P;\n  const b176 = pow2(b88, _88n, P) * b88 % P;\n  const b220 = pow2(b176, _44n, P) * b44 % P;\n  const b223 = pow2(b220, _3n, P) * b3 % P;\n  const t1 = pow2(b223, _23n, P) * b22 % P;\n  const t2 = pow2(t1, _6n, P) * b2 % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fp.eql(Fp.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\nconst Fp = Field(secp256k1P, undefined, undefined, {\n  sqrt: sqrtMod\n});\nexport const secp256k1 = createCurve({\n  a: BigInt(0),\n  b: BigInt(7),\n  Fp,\n  n: secp256k1N,\n  // Base point (x, y) aka generator point\n  Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n  Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n  h: BigInt(1),\n  lowS: true,\n  /**\n   * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n   * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n   * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n   * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n   */\n  endo: {\n    beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n    splitScalar: k => {\n      const n = secp256k1N;\n      const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n      const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n      const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n      const b2 = a1;\n      const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n      const c1 = divNearest(b2 * k, n);\n      const c2 = divNearest(-b1 * k, n);\n      let k1 = mod(k - c1 * a1 - c2 * a2, n);\n      let k2 = mod(-c1 * b1 - c2 * b2, n);\n      const k1neg = k1 > POW_2_128;\n      const k2neg = k2 > POW_2_128;\n      if (k1neg) k1 = n - k1;\n      if (k2neg) k2 = n - k2;\n      if (k1 > POW_2_128 || k2 > POW_2_128) {\n        throw new Error('splitScalar: Endomorphism failed, k=' + k);\n      }\n      return {\n        k1neg,\n        k1,\n        k2neg,\n        k2\n      };\n    }\n  }\n}, sha256);\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = x => typeof x === 'bigint' && _0n < x && x < secp256k1P;\nconst ge = x => typeof x === 'bigint' && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES = {};\nfunction taggedHash(tag, ...messages) {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(Uint8Array.from(tag, c => c.charCodeAt(0)));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = point => point.toRawBytes(true).slice(1);\nconst numTo32b = n => numberToBytesBE(n, 32);\nconst modP = x => mod(x, secp256k1P);\nconst modN = x => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q, a, b) => Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv) {\n  let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n  let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = p.hasEvenY() ? d_ : modN(-d_);\n  return {\n    scalar: scalar,\n    bytes: pointToBytes(p)\n  };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x) {\n  if (!fe(x)) throw new Error('bad x: need 0 < x < p'); // Fail if x ≥ p.\n  const xx = modP(x * x);\n  const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n  if (y % _2n !== _0n) y = modP(-y); // Return the unique point P such that x(P) = x and\n  const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  p.assertValidity();\n  return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args) {\n  return modN(bytesToNumberBE(taggedHash('BIP0340/challenge', ...args)));\n}\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey) {\n  return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(message, privateKey, auxRand = randomBytes(32)) {\n  const m = ensureBytes('message', message);\n  const {\n    bytes: px,\n    scalar: d\n  } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = numTo32b(d ^ bytesToNumberBE(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  const k_ = modN(bytesToNumberBE(rand)); // Let k' = int(rand) mod n\n  if (k_ === _0n) throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n  const {\n    bytes: rx,\n    scalar: k\n  } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(numTo32b(modN(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature, message, publicKey) {\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(bytesToNumberBE(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = bytesToNumberBE(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!fe(r)) return false;\n    const s = bytesToNumberBE(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!ge(s)) return false;\n    const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n    const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n    if (!R || !R.hasEvenY() || R.toAffine().x !== r) return false; // -eP == (n-e)P\n    return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n  } catch (error) {\n    return false;\n  }\n}\nexport const schnorr = /* @__PURE__ */(() => ({\n  getPublicKey: schnorrGetPublicKey,\n  sign: schnorrSign,\n  verify: schnorrVerify,\n  utils: {\n    randomPrivateKey: secp256k1.utils.randomPrivateKey,\n    lift_x,\n    pointToBytes,\n    numberToBytesBE,\n    bytesToNumberBE,\n    taggedHash,\n    mod\n  }\n}))();\nconst isoMap = /* @__PURE__ */(() => isogenyMap(Fp, [\n// xNum\n['0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7', '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581', '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262', '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c'],\n// xDen\n['0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b', '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14', '0x0000000000000000000000000000000000000000000000000000000000000001' // LAST 1\n],\n// yNum\n['0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c', '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3', '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931', '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84'],\n// yDen\n['0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b', '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573', '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f', '0x0000000000000000000000000000000000000000000000000000000000000001' // LAST 1\n]].map(i => i.map(j => BigInt(j)))))();\nconst mapSWU = /* @__PURE__ */(() => mapToCurveSimpleSWU(Fp, {\n  A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n  B: BigInt('1771'),\n  Z: Fp.create(BigInt('-11'))\n}))();\nconst htf = /* @__PURE__ */(() => createHasher(secp256k1.ProjectivePoint, scalars => {\n  const {\n    x,\n    y\n  } = mapSWU(Fp.create(scalars[0]));\n  return isoMap(x, y);\n}, {\n  DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n  encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n  p: Fp.ORDER,\n  m: 1,\n  k: 128,\n  expand: 'xmd',\n  hash: sha256\n}))();\nexport const hashToCurve = /* @__PURE__ */(() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */(() => htf.encodeToCurve)();", "map": {"version": 3, "names": ["sha256", "randomBytes", "Field", "mod", "pow2", "mapToCurveSimpleSWU", "bytesToNumberBE", "concatBytes", "ensureBytes", "numberToBytesBE", "createHasher", "isogenyMap", "createCurve", "secp256k1P", "BigInt", "secp256k1N", "_1n", "_2n", "divNearest", "a", "b", "sqrtMod", "y", "P", "_3n", "_6n", "_11n", "_22n", "_23n", "_44n", "_88n", "b2", "b3", "b6", "b9", "b11", "b22", "b44", "b88", "b176", "b220", "b223", "t1", "t2", "root", "Fp", "eql", "sqr", "Error", "undefined", "sqrt", "secp256k1", "n", "Gx", "Gy", "h", "lowS", "endo", "beta", "splitScalar", "k", "a1", "b1", "a2", "POW_2_128", "c1", "c2", "k1", "k2", "k1neg", "k2neg", "_0n", "fe", "x", "ge", "TAGGED_HASH_PREFIXES", "taggedHash", "tag", "messages", "tagP", "tagH", "Uint8Array", "from", "c", "charCodeAt", "pointToBytes", "point", "toRawBytes", "slice", "numTo32b", "modP", "modN", "Point", "ProjectivePoint", "GmulAdd", "Q", "BASE", "multiplyAndAddUnsafe", "schnorrGetExtPubKey", "priv", "d_", "utils", "normPrivateKeyToScalar", "p", "fromPrivateKey", "scalar", "hasEvenY", "bytes", "lift_x", "xx", "assertValidity", "challenge", "args", "schnorrGetPublicKey", "privateKey", "schnorrSign", "message", "auxRand", "m", "px", "d", "t", "rand", "k_", "rx", "e", "sig", "set", "schnorrVerify", "signature", "public<PERSON>ey", "pub", "r", "subarray", "s", "R", "toAffine", "error", "schnorr", "getPublicKey", "sign", "verify", "randomPrivateKey", "isoMap", "map", "i", "j", "mapSWU", "A", "B", "Z", "create", "htf", "scalars", "DST", "encodeDST", "ORDER", "expand", "hash", "hashToCurve", "encodeToCurve"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\secp256k1.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\nimport { sha256 } from '@noble/hashes/sha256';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { Field, mod, pow2 } from './abstract/modular.js';\nimport { ProjPointType as PointType, mapToCurveSimpleSWU } from './abstract/weierstrass.js';\nimport type { Hex, PrivKey } from './abstract/utils.js';\nimport { bytesToNumberBE, concatBytes, ensureBytes, numberToBytesBE } from './abstract/utils.js';\nimport { createHasher, isogenyMap } from './abstract/hash-to-curve.js';\nimport { createCurve } from './_shortw_utils.js';\n\nconst secp256k1P = BigInt('0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f');\nconst secp256k1N = BigInt('0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141');\nconst _1n = BigInt(1);\nconst _2n = BigInt(2);\nconst divNearest = (a: bigint, b: bigint) => (a + b / _2n) / b;\n\n/**\n * √n = n^((p+1)/4) for fields p = 3 mod 4. We unwrap the loop and multiply bit-by-bit.\n * (P+1n/4n).toString(2) would produce bits [223x 1, 0, 22x 1, 4x 0, 11, 00]\n */\nfunction sqrtMod(y: bigint): bigint {\n  const P = secp256k1P;\n  // prettier-ignore\n  const _3n = BigInt(3), _6n = BigInt(6), _11n = BigInt(11), _22n = BigInt(22);\n  // prettier-ignore\n  const _23n = BigInt(23), _44n = BigInt(44), _88n = BigInt(88);\n  const b2 = (y * y * y) % P; // x^3, 11\n  const b3 = (b2 * b2 * y) % P; // x^7\n  const b6 = (pow2(b3, _3n, P) * b3) % P;\n  const b9 = (pow2(b6, _3n, P) * b3) % P;\n  const b11 = (pow2(b9, _2n, P) * b2) % P;\n  const b22 = (pow2(b11, _11n, P) * b11) % P;\n  const b44 = (pow2(b22, _22n, P) * b22) % P;\n  const b88 = (pow2(b44, _44n, P) * b44) % P;\n  const b176 = (pow2(b88, _88n, P) * b88) % P;\n  const b220 = (pow2(b176, _44n, P) * b44) % P;\n  const b223 = (pow2(b220, _3n, P) * b3) % P;\n  const t1 = (pow2(b223, _23n, P) * b22) % P;\n  const t2 = (pow2(t1, _6n, P) * b2) % P;\n  const root = pow2(t2, _2n, P);\n  if (!Fp.eql(Fp.sqr(root), y)) throw new Error('Cannot find square root');\n  return root;\n}\n\nconst Fp = Field(secp256k1P, undefined, undefined, { sqrt: sqrtMod });\n\nexport const secp256k1 = createCurve(\n  {\n    a: BigInt(0), // equation params: a, b\n    b: BigInt(7), // Seem to be rigid: bitcointalk.org/index.php?topic=289795.msg3183975#msg3183975\n    Fp, // Field's prime: 2n**256n - 2n**32n - 2n**9n - 2n**8n - 2n**7n - 2n**6n - 2n**4n - 1n\n    n: secp256k1N, // Curve order, total count of valid points in the field\n    // Base point (x, y) aka generator point\n    Gx: BigInt('55066263022277343669578718895168534326250603453777594175500187360389116729240'),\n    Gy: BigInt('32670510020758816978083085130507043184471273380659243275938904335757337482424'),\n    h: BigInt(1), // Cofactor\n    lowS: true, // Allow only low-S signatures by default in sign() and verify()\n    /**\n     * secp256k1 belongs to Koblitz curves: it has efficiently computable endomorphism.\n     * Endomorphism uses 2x less RAM, speeds up precomputation by 2x and ECDH / key recovery by 20%.\n     * For precomputed wNAF it trades off 1/2 init time & 1/3 ram for 20% perf hit.\n     * Explanation: https://gist.github.com/paulmillr/eb670806793e84df628a7c434a873066\n     */\n    endo: {\n      beta: BigInt('0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee'),\n      splitScalar: (k: bigint) => {\n        const n = secp256k1N;\n        const a1 = BigInt('0x3086d221a7d46bcde86c90e49284eb15');\n        const b1 = -_1n * BigInt('0xe4437ed6010e88286f547fa90abfe4c3');\n        const a2 = BigInt('0x114ca50f7a8e2f3f657c1108d9d44cfd8');\n        const b2 = a1;\n        const POW_2_128 = BigInt('0x100000000000000000000000000000000'); // (2n**128n).toString(16)\n\n        const c1 = divNearest(b2 * k, n);\n        const c2 = divNearest(-b1 * k, n);\n        let k1 = mod(k - c1 * a1 - c2 * a2, n);\n        let k2 = mod(-c1 * b1 - c2 * b2, n);\n        const k1neg = k1 > POW_2_128;\n        const k2neg = k2 > POW_2_128;\n        if (k1neg) k1 = n - k1;\n        if (k2neg) k2 = n - k2;\n        if (k1 > POW_2_128 || k2 > POW_2_128) {\n          throw new Error('splitScalar: Endomorphism failed, k=' + k);\n        }\n        return { k1neg, k1, k2neg, k2 };\n      },\n    },\n  },\n  sha256\n);\n\n// Schnorr signatures are superior to ECDSA from above. Below is Schnorr-specific BIP0340 code.\n// https://github.com/bitcoin/bips/blob/master/bip-0340.mediawiki\nconst _0n = BigInt(0);\nconst fe = (x: bigint) => typeof x === 'bigint' && _0n < x && x < secp256k1P;\nconst ge = (x: bigint) => typeof x === 'bigint' && _0n < x && x < secp256k1N;\n/** An object mapping tags to their tagged hash prefix of [SHA256(tag) | SHA256(tag)] */\nconst TAGGED_HASH_PREFIXES: { [tag: string]: Uint8Array } = {};\nfunction taggedHash(tag: string, ...messages: Uint8Array[]): Uint8Array {\n  let tagP = TAGGED_HASH_PREFIXES[tag];\n  if (tagP === undefined) {\n    const tagH = sha256(Uint8Array.from(tag, (c) => c.charCodeAt(0)));\n    tagP = concatBytes(tagH, tagH);\n    TAGGED_HASH_PREFIXES[tag] = tagP;\n  }\n  return sha256(concatBytes(tagP, ...messages));\n}\n\n// ECDSA compact points are 33-byte. Schnorr is 32: we strip first byte 0x02 or 0x03\nconst pointToBytes = (point: PointType<bigint>) => point.toRawBytes(true).slice(1);\nconst numTo32b = (n: bigint) => numberToBytesBE(n, 32);\nconst modP = (x: bigint) => mod(x, secp256k1P);\nconst modN = (x: bigint) => mod(x, secp256k1N);\nconst Point = secp256k1.ProjectivePoint;\nconst GmulAdd = (Q: PointType<bigint>, a: bigint, b: bigint) =>\n  Point.BASE.multiplyAndAddUnsafe(Q, a, b);\n\n// Calculate point, scalar and bytes\nfunction schnorrGetExtPubKey(priv: PrivKey) {\n  let d_ = secp256k1.utils.normPrivateKeyToScalar(priv); // same method executed in fromPrivateKey\n  let p = Point.fromPrivateKey(d_); // P = d'⋅G; 0 < d' < n check is done inside\n  const scalar = p.hasEvenY() ? d_ : modN(-d_);\n  return { scalar: scalar, bytes: pointToBytes(p) };\n}\n/**\n * lift_x from BIP340. Convert 32-byte x coordinate to elliptic curve point.\n * @returns valid point checked for being on-curve\n */\nfunction lift_x(x: bigint): PointType<bigint> {\n  if (!fe(x)) throw new Error('bad x: need 0 < x < p'); // Fail if x ≥ p.\n  const xx = modP(x * x);\n  const c = modP(xx * x + BigInt(7)); // Let c = x³ + 7 mod p.\n  let y = sqrtMod(c); // Let y = c^(p+1)/4 mod p.\n  if (y % _2n !== _0n) y = modP(-y); // Return the unique point P such that x(P) = x and\n  const p = new Point(x, y, _1n); // y(P) = y if y mod 2 = 0 or y(P) = p-y otherwise.\n  p.assertValidity();\n  return p;\n}\n/**\n * Create tagged hash, convert it to bigint, reduce modulo-n.\n */\nfunction challenge(...args: Uint8Array[]): bigint {\n  return modN(bytesToNumberBE(taggedHash('BIP0340/challenge', ...args)));\n}\n\n/**\n * Schnorr public key is just `x` coordinate of Point as per BIP340.\n */\nfunction schnorrGetPublicKey(privateKey: Hex): Uint8Array {\n  return schnorrGetExtPubKey(privateKey).bytes; // d'=int(sk). Fail if d'=0 or d'≥n. Ret bytes(d'⋅G)\n}\n\n/**\n * Creates Schnorr signature as per BIP340. Verifies itself before returning anything.\n * auxRand is optional and is not the sole source of k generation: bad CSPRNG won't be dangerous.\n */\nfunction schnorrSign(\n  message: Hex,\n  privateKey: PrivKey,\n  auxRand: Hex = randomBytes(32)\n): Uint8Array {\n  const m = ensureBytes('message', message);\n  const { bytes: px, scalar: d } = schnorrGetExtPubKey(privateKey); // checks for isWithinCurveOrder\n  const a = ensureBytes('auxRand', auxRand, 32); // Auxiliary random data a: a 32-byte array\n  const t = numTo32b(d ^ bytesToNumberBE(taggedHash('BIP0340/aux', a))); // Let t be the byte-wise xor of bytes(d) and hash/aux(a)\n  const rand = taggedHash('BIP0340/nonce', t, px, m); // Let rand = hash/nonce(t || bytes(P) || m)\n  const k_ = modN(bytesToNumberBE(rand)); // Let k' = int(rand) mod n\n  if (k_ === _0n) throw new Error('sign failed: k is zero'); // Fail if k' = 0.\n  const { bytes: rx, scalar: k } = schnorrGetExtPubKey(k_); // Let R = k'⋅G.\n  const e = challenge(rx, px, m); // Let e = int(hash/challenge(bytes(R) || bytes(P) || m)) mod n.\n  const sig = new Uint8Array(64); // Let sig = bytes(R) || bytes((k + ed) mod n).\n  sig.set(rx, 0);\n  sig.set(numTo32b(modN(k + e * d)), 32);\n  // If Verify(bytes(P), m, sig) (see below) returns failure, abort\n  if (!schnorrVerify(sig, m, px)) throw new Error('sign: Invalid signature produced');\n  return sig;\n}\n\n/**\n * Verifies Schnorr signature.\n * Will swallow errors & return false except for initial type validation of arguments.\n */\nfunction schnorrVerify(signature: Hex, message: Hex, publicKey: Hex): boolean {\n  const sig = ensureBytes('signature', signature, 64);\n  const m = ensureBytes('message', message);\n  const pub = ensureBytes('publicKey', publicKey, 32);\n  try {\n    const P = lift_x(bytesToNumberBE(pub)); // P = lift_x(int(pk)); fail if that fails\n    const r = bytesToNumberBE(sig.subarray(0, 32)); // Let r = int(sig[0:32]); fail if r ≥ p.\n    if (!fe(r)) return false;\n    const s = bytesToNumberBE(sig.subarray(32, 64)); // Let s = int(sig[32:64]); fail if s ≥ n.\n    if (!ge(s)) return false;\n    const e = challenge(numTo32b(r), pointToBytes(P), m); // int(challenge(bytes(r)||bytes(P)||m))%n\n    const R = GmulAdd(P, s, modN(-e)); // R = s⋅G - e⋅P\n    if (!R || !R.hasEvenY() || R.toAffine().x !== r) return false; // -eP == (n-e)P\n    return true; // Fail if is_infinite(R) / not has_even_y(R) / x(R) ≠ r.\n  } catch (error) {\n    return false;\n  }\n}\n\nexport const schnorr = /* @__PURE__ */ (() => ({\n  getPublicKey: schnorrGetPublicKey,\n  sign: schnorrSign,\n  verify: schnorrVerify,\n  utils: {\n    randomPrivateKey: secp256k1.utils.randomPrivateKey,\n    lift_x,\n    pointToBytes,\n    numberToBytesBE,\n    bytesToNumberBE,\n    taggedHash,\n    mod,\n  },\n}))();\n\nconst isoMap = /* @__PURE__ */ (() =>\n  isogenyMap(\n    Fp,\n    [\n      // xNum\n      [\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa8c7',\n        '0x7d3d4c80bc321d5b9f315cea7fd44c5d595d2fc0bf63b92dfff1044f17c6581',\n        '0x534c328d23f234e6e2a413deca25caece4506144037c40314ecbd0b53d9dd262',\n        '0x8e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38e38daaaaa88c',\n      ],\n      // xDen\n      [\n        '0xd35771193d94918a9ca34ccbb7b640dd86cd409542f8487d9fe6b745781eb49b',\n        '0xedadc6f64383dc1df7c4b2d51b54225406d36b641f5e41bbc52a56612a8c6d14',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n      // yNum\n      [\n        '0x4bda12f684bda12f684bda12f684bda12f684bda12f684bda12f684b8e38e23c',\n        '0xc75e0c32d5cb7c0fa9d0a54b12a0a6d5647ab046d686da6fdffc90fc201d71a3',\n        '0x29a6194691f91a73715209ef6512e576722830a201be2018a765e85a9ecee931',\n        '0x2f684bda12f684bda12f684bda12f684bda12f684bda12f684bda12f38e38d84',\n      ],\n      // yDen\n      [\n        '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffff93b',\n        '0x7a06534bb8bdb49fd5e9e6632722c2989467c1bfc8e8d978dfb425d2685c2573',\n        '0x6484aa716545ca2cf3a70c3fa8fe337e0a3d21162f0d6299a7bf8192bfd2a76f',\n        '0x0000000000000000000000000000000000000000000000000000000000000001', // LAST 1\n      ],\n    ].map((i) => i.map((j) => BigInt(j))) as [bigint[], bigint[], bigint[], bigint[]]\n  ))();\nconst mapSWU = /* @__PURE__ */ (() =>\n  mapToCurveSimpleSWU(Fp, {\n    A: BigInt('0x3f8731abdd661adca08a5558f0f5d272e953d363cb6f0e5d405447c01a444533'),\n    B: BigInt('1771'),\n    Z: Fp.create(BigInt('-11')),\n  }))();\nconst htf = /* @__PURE__ */ (() =>\n  createHasher(\n    secp256k1.ProjectivePoint,\n    (scalars: bigint[]) => {\n      const { x, y } = mapSWU(Fp.create(scalars[0]));\n      return isoMap(x, y);\n    },\n    {\n      DST: 'secp256k1_XMD:SHA-256_SSWU_RO_',\n      encodeDST: 'secp256k1_XMD:SHA-256_SSWU_NU_',\n      p: Fp.ORDER,\n      m: 1,\n      k: 128,\n      expand: 'xmd',\n      hash: sha256,\n    }\n  ))();\nexport const hashToCurve = /* @__PURE__ */ (() => htf.hashToCurve)();\nexport const encodeToCurve = /* @__PURE__ */ (() => htf.encodeToCurve)();\n"], "mappings": "AAAA;AACA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,KAAK,EAAEC,GAAG,EAAEC,IAAI,QAAQ,uBAAuB;AACxD,SAAqCC,mBAAmB,QAAQ,2BAA2B;AAE3F,SAASC,eAAe,EAAEC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,qBAAqB;AAChG,SAASC,YAAY,EAAEC,UAAU,QAAQ,6BAA6B;AACtE,SAASC,WAAW,QAAQ,oBAAoB;AAEhD,MAAMC,UAAU,GAAGC,MAAM,CAAC,oEAAoE,CAAC;AAC/F,MAAMC,UAAU,GAAGD,MAAM,CAAC,oEAAoE,CAAC;AAC/F,MAAME,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMI,UAAU,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAK,CAACD,CAAC,GAAGC,CAAC,GAAGH,GAAG,IAAIG,CAAC;AAE9D;;;;AAIA,SAASC,OAAOA,CAACC,CAAS;EACxB,MAAMC,CAAC,GAAGV,UAAU;EACpB;EACA,MAAMW,GAAG,GAAGV,MAAM,CAAC,CAAC,CAAC;IAAEW,GAAG,GAAGX,MAAM,CAAC,CAAC,CAAC;IAAEY,IAAI,GAAGZ,MAAM,CAAC,EAAE,CAAC;IAAEa,IAAI,GAAGb,MAAM,CAAC,EAAE,CAAC;EAC5E;EACA,MAAMc,IAAI,GAAGd,MAAM,CAAC,EAAE,CAAC;IAAEe,IAAI,GAAGf,MAAM,CAAC,EAAE,CAAC;IAAEgB,IAAI,GAAGhB,MAAM,CAAC,EAAE,CAAC;EAC7D,MAAMiB,EAAE,GAAIT,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAIC,CAAC,CAAC,CAAC;EAC5B,MAAMS,EAAE,GAAID,EAAE,GAAGA,EAAE,GAAGT,CAAC,GAAIC,CAAC,CAAC,CAAC;EAC9B,MAAMU,EAAE,GAAI7B,IAAI,CAAC4B,EAAE,EAAER,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EACtC,MAAMW,EAAE,GAAI9B,IAAI,CAAC6B,EAAE,EAAET,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EACtC,MAAMY,GAAG,GAAI/B,IAAI,CAAC8B,EAAE,EAAEjB,GAAG,EAAEM,CAAC,CAAC,GAAGQ,EAAE,GAAIR,CAAC;EACvC,MAAMa,GAAG,GAAIhC,IAAI,CAAC+B,GAAG,EAAET,IAAI,EAAEH,CAAC,CAAC,GAAGY,GAAG,GAAIZ,CAAC;EAC1C,MAAMc,GAAG,GAAIjC,IAAI,CAACgC,GAAG,EAAET,IAAI,EAAEJ,CAAC,CAAC,GAAGa,GAAG,GAAIb,CAAC;EAC1C,MAAMe,GAAG,GAAIlC,IAAI,CAACiC,GAAG,EAAER,IAAI,EAAEN,CAAC,CAAC,GAAGc,GAAG,GAAId,CAAC;EAC1C,MAAMgB,IAAI,GAAInC,IAAI,CAACkC,GAAG,EAAER,IAAI,EAAEP,CAAC,CAAC,GAAGe,GAAG,GAAIf,CAAC;EAC3C,MAAMiB,IAAI,GAAIpC,IAAI,CAACmC,IAAI,EAAEV,IAAI,EAAEN,CAAC,CAAC,GAAGc,GAAG,GAAId,CAAC;EAC5C,MAAMkB,IAAI,GAAIrC,IAAI,CAACoC,IAAI,EAAEhB,GAAG,EAAED,CAAC,CAAC,GAAGS,EAAE,GAAIT,CAAC;EAC1C,MAAMmB,EAAE,GAAItC,IAAI,CAACqC,IAAI,EAAEb,IAAI,EAAEL,CAAC,CAAC,GAAGa,GAAG,GAAIb,CAAC;EAC1C,MAAMoB,EAAE,GAAIvC,IAAI,CAACsC,EAAE,EAAEjB,GAAG,EAAEF,CAAC,CAAC,GAAGQ,EAAE,GAAIR,CAAC;EACtC,MAAMqB,IAAI,GAAGxC,IAAI,CAACuC,EAAE,EAAE1B,GAAG,EAAEM,CAAC,CAAC;EAC7B,IAAI,CAACsB,EAAE,CAACC,GAAG,CAACD,EAAE,CAACE,GAAG,CAACH,IAAI,CAAC,EAAEtB,CAAC,CAAC,EAAE,MAAM,IAAI0B,KAAK,CAAC,yBAAyB,CAAC;EACxE,OAAOJ,IAAI;AACb;AAEA,MAAMC,EAAE,GAAG3C,KAAK,CAACW,UAAU,EAAEoC,SAAS,EAAEA,SAAS,EAAE;EAAEC,IAAI,EAAE7B;AAAO,CAAE,CAAC;AAErE,OAAO,MAAM8B,SAAS,GAAGvC,WAAW,CAClC;EACEO,CAAC,EAAEL,MAAM,CAAC,CAAC,CAAC;EACZM,CAAC,EAAEN,MAAM,CAAC,CAAC,CAAC;EACZ+B,EAAE;EACFO,CAAC,EAAErC,UAAU;EACb;EACAsC,EAAE,EAAEvC,MAAM,CAAC,+EAA+E,CAAC;EAC3FwC,EAAE,EAAExC,MAAM,CAAC,+EAA+E,CAAC;EAC3FyC,CAAC,EAAEzC,MAAM,CAAC,CAAC,CAAC;EACZ0C,IAAI,EAAE,IAAI;EACV;;;;;;EAMAC,IAAI,EAAE;IACJC,IAAI,EAAE5C,MAAM,CAAC,oEAAoE,CAAC;IAClF6C,WAAW,EAAGC,CAAS,IAAI;MACzB,MAAMR,CAAC,GAAGrC,UAAU;MACpB,MAAM8C,EAAE,GAAG/C,MAAM,CAAC,oCAAoC,CAAC;MACvD,MAAMgD,EAAE,GAAG,CAAC9C,GAAG,GAAGF,MAAM,CAAC,oCAAoC,CAAC;MAC9D,MAAMiD,EAAE,GAAGjD,MAAM,CAAC,qCAAqC,CAAC;MACxD,MAAMiB,EAAE,GAAG8B,EAAE;MACb,MAAMG,SAAS,GAAGlD,MAAM,CAAC,qCAAqC,CAAC,CAAC,CAAC;MAEjE,MAAMmD,EAAE,GAAG/C,UAAU,CAACa,EAAE,GAAG6B,CAAC,EAAER,CAAC,CAAC;MAChC,MAAMc,EAAE,GAAGhD,UAAU,CAAC,CAAC4C,EAAE,GAAGF,CAAC,EAAER,CAAC,CAAC;MACjC,IAAIe,EAAE,GAAGhE,GAAG,CAACyD,CAAC,GAAGK,EAAE,GAAGJ,EAAE,GAAGK,EAAE,GAAGH,EAAE,EAAEX,CAAC,CAAC;MACtC,IAAIgB,EAAE,GAAGjE,GAAG,CAAC,CAAC8D,EAAE,GAAGH,EAAE,GAAGI,EAAE,GAAGnC,EAAE,EAAEqB,CAAC,CAAC;MACnC,MAAMiB,KAAK,GAAGF,EAAE,GAAGH,SAAS;MAC5B,MAAMM,KAAK,GAAGF,EAAE,GAAGJ,SAAS;MAC5B,IAAIK,KAAK,EAAEF,EAAE,GAAGf,CAAC,GAAGe,EAAE;MACtB,IAAIG,KAAK,EAAEF,EAAE,GAAGhB,CAAC,GAAGgB,EAAE;MACtB,IAAID,EAAE,GAAGH,SAAS,IAAII,EAAE,GAAGJ,SAAS,EAAE;QACpC,MAAM,IAAIhB,KAAK,CAAC,sCAAsC,GAAGY,CAAC,CAAC;;MAE7D,OAAO;QAAES,KAAK;QAAEF,EAAE;QAAEG,KAAK;QAAEF;MAAE,CAAE;IACjC;;CAEH,EACDpE,MAAM,CACP;AAED;AACA;AACA,MAAMuE,GAAG,GAAGzD,MAAM,CAAC,CAAC,CAAC;AACrB,MAAM0D,EAAE,GAAIC,CAAS,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIF,GAAG,GAAGE,CAAC,IAAIA,CAAC,GAAG5D,UAAU;AAC5E,MAAM6D,EAAE,GAAID,CAAS,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIF,GAAG,GAAGE,CAAC,IAAIA,CAAC,GAAG1D,UAAU;AAC5E;AACA,MAAM4D,oBAAoB,GAAkC,EAAE;AAC9D,SAASC,UAAUA,CAACC,GAAW,EAAE,GAAGC,QAAsB;EACxD,IAAIC,IAAI,GAAGJ,oBAAoB,CAACE,GAAG,CAAC;EACpC,IAAIE,IAAI,KAAK9B,SAAS,EAAE;IACtB,MAAM+B,IAAI,GAAGhF,MAAM,CAACiF,UAAU,CAACC,IAAI,CAACL,GAAG,EAAGM,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACjEL,IAAI,GAAGxE,WAAW,CAACyE,IAAI,EAAEA,IAAI,CAAC;IAC9BL,oBAAoB,CAACE,GAAG,CAAC,GAAGE,IAAI;;EAElC,OAAO/E,MAAM,CAACO,WAAW,CAACwE,IAAI,EAAE,GAAGD,QAAQ,CAAC,CAAC;AAC/C;AAEA;AACA,MAAMO,YAAY,GAAIC,KAAwB,IAAKA,KAAK,CAACC,UAAU,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;AAClF,MAAMC,QAAQ,GAAIrC,CAAS,IAAK3C,eAAe,CAAC2C,CAAC,EAAE,EAAE,CAAC;AACtD,MAAMsC,IAAI,GAAIjB,CAAS,IAAKtE,GAAG,CAACsE,CAAC,EAAE5D,UAAU,CAAC;AAC9C,MAAM8E,IAAI,GAAIlB,CAAS,IAAKtE,GAAG,CAACsE,CAAC,EAAE1D,UAAU,CAAC;AAC9C,MAAM6E,KAAK,GAAGzC,SAAS,CAAC0C,eAAe;AACvC,MAAMC,OAAO,GAAGA,CAACC,CAAoB,EAAE5E,CAAS,EAAEC,CAAS,KACzDwE,KAAK,CAACI,IAAI,CAACC,oBAAoB,CAACF,CAAC,EAAE5E,CAAC,EAAEC,CAAC,CAAC;AAE1C;AACA,SAAS8E,mBAAmBA,CAACC,IAAa;EACxC,IAAIC,EAAE,GAAGjD,SAAS,CAACkD,KAAK,CAACC,sBAAsB,CAACH,IAAI,CAAC,CAAC,CAAC;EACvD,IAAII,CAAC,GAAGX,KAAK,CAACY,cAAc,CAACJ,EAAE,CAAC,CAAC,CAAC;EAClC,MAAMK,MAAM,GAAGF,CAAC,CAACG,QAAQ,EAAE,GAAGN,EAAE,GAAGT,IAAI,CAAC,CAACS,EAAE,CAAC;EAC5C,OAAO;IAAEK,MAAM,EAAEA,MAAM;IAAEE,KAAK,EAAEtB,YAAY,CAACkB,CAAC;EAAC,CAAE;AACnD;AACA;;;;AAIA,SAASK,MAAMA,CAACnC,CAAS;EACvB,IAAI,CAACD,EAAE,CAACC,CAAC,CAAC,EAAE,MAAM,IAAIzB,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC;EACtD,MAAM6D,EAAE,GAAGnB,IAAI,CAACjB,CAAC,GAAGA,CAAC,CAAC;EACtB,MAAMU,CAAC,GAAGO,IAAI,CAACmB,EAAE,GAAGpC,CAAC,GAAG3D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC,IAAIQ,CAAC,GAAGD,OAAO,CAAC8D,CAAC,CAAC,CAAC,CAAC;EACpB,IAAI7D,CAAC,GAAGL,GAAG,KAAKsD,GAAG,EAAEjD,CAAC,GAAGoE,IAAI,CAAC,CAACpE,CAAC,CAAC,CAAC,CAAC;EACnC,MAAMiF,CAAC,GAAG,IAAIX,KAAK,CAACnB,CAAC,EAAEnD,CAAC,EAAEN,GAAG,CAAC,CAAC,CAAC;EAChCuF,CAAC,CAACO,cAAc,EAAE;EAClB,OAAOP,CAAC;AACV;AACA;;;AAGA,SAASQ,SAASA,CAAC,GAAGC,IAAkB;EACtC,OAAOrB,IAAI,CAACrF,eAAe,CAACsE,UAAU,CAAC,mBAAmB,EAAE,GAAGoC,IAAI,CAAC,CAAC,CAAC;AACxE;AAEA;;;AAGA,SAASC,mBAAmBA,CAACC,UAAe;EAC1C,OAAOhB,mBAAmB,CAACgB,UAAU,CAAC,CAACP,KAAK,CAAC,CAAC;AAChD;AAEA;;;;AAIA,SAASQ,WAAWA,CAClBC,OAAY,EACZF,UAAmB,EACnBG,OAAA,GAAepH,WAAW,CAAC,EAAE,CAAC;EAE9B,MAAMqH,CAAC,GAAG9G,WAAW,CAAC,SAAS,EAAE4G,OAAO,CAAC;EACzC,MAAM;IAAET,KAAK,EAAEY,EAAE;IAAEd,MAAM,EAAEe;EAAC,CAAE,GAAGtB,mBAAmB,CAACgB,UAAU,CAAC,CAAC,CAAC;EAClE,MAAM/F,CAAC,GAAGX,WAAW,CAAC,SAAS,EAAE6G,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;EAC/C,MAAMI,CAAC,GAAGhC,QAAQ,CAAC+B,CAAC,GAAGlH,eAAe,CAACsE,UAAU,CAAC,aAAa,EAAEzD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvE,MAAMuG,IAAI,GAAG9C,UAAU,CAAC,eAAe,EAAE6C,CAAC,EAAEF,EAAE,EAAED,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMK,EAAE,GAAGhC,IAAI,CAACrF,eAAe,CAACoH,IAAI,CAAC,CAAC,CAAC,CAAC;EACxC,IAAIC,EAAE,KAAKpD,GAAG,EAAE,MAAM,IAAIvB,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;EAC3D,MAAM;IAAE2D,KAAK,EAAEiB,EAAE;IAAEnB,MAAM,EAAE7C;EAAC,CAAE,GAAGsC,mBAAmB,CAACyB,EAAE,CAAC,CAAC,CAAC;EAC1D,MAAME,CAAC,GAAGd,SAAS,CAACa,EAAE,EAAEL,EAAE,EAAED,CAAC,CAAC,CAAC,CAAC;EAChC,MAAMQ,GAAG,GAAG,IAAI7C,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;EAChC6C,GAAG,CAACC,GAAG,CAACH,EAAE,EAAE,CAAC,CAAC;EACdE,GAAG,CAACC,GAAG,CAACtC,QAAQ,CAACE,IAAI,CAAC/B,CAAC,GAAGiE,CAAC,GAAGL,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtC;EACA,IAAI,CAACQ,aAAa,CAACF,GAAG,EAAER,CAAC,EAAEC,EAAE,CAAC,EAAE,MAAM,IAAIvE,KAAK,CAAC,kCAAkC,CAAC;EACnF,OAAO8E,GAAG;AACZ;AAEA;;;;AAIA,SAASE,aAAaA,CAACC,SAAc,EAAEb,OAAY,EAAEc,SAAc;EACjE,MAAMJ,GAAG,GAAGtH,WAAW,CAAC,WAAW,EAAEyH,SAAS,EAAE,EAAE,CAAC;EACnD,MAAMX,CAAC,GAAG9G,WAAW,CAAC,SAAS,EAAE4G,OAAO,CAAC;EACzC,MAAMe,GAAG,GAAG3H,WAAW,CAAC,WAAW,EAAE0H,SAAS,EAAE,EAAE,CAAC;EACnD,IAAI;IACF,MAAM3G,CAAC,GAAGqF,MAAM,CAACtG,eAAe,CAAC6H,GAAG,CAAC,CAAC,CAAC,CAAC;IACxC,MAAMC,CAAC,GAAG9H,eAAe,CAACwH,GAAG,CAACO,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAAC7D,EAAE,CAAC4D,CAAC,CAAC,EAAE,OAAO,KAAK;IACxB,MAAME,CAAC,GAAGhI,eAAe,CAACwH,GAAG,CAACO,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,CAAC3D,EAAE,CAAC4D,CAAC,CAAC,EAAE,OAAO,KAAK;IACxB,MAAMT,CAAC,GAAGd,SAAS,CAACtB,QAAQ,CAAC2C,CAAC,CAAC,EAAE/C,YAAY,CAAC9D,CAAC,CAAC,EAAE+F,CAAC,CAAC,CAAC,CAAC;IACtD,MAAMiB,CAAC,GAAGzC,OAAO,CAACvE,CAAC,EAAE+G,CAAC,EAAE3C,IAAI,CAAC,CAACkC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,IAAI,CAACU,CAAC,IAAI,CAACA,CAAC,CAAC7B,QAAQ,EAAE,IAAI6B,CAAC,CAACC,QAAQ,EAAE,CAAC/D,CAAC,KAAK2D,CAAC,EAAE,OAAO,KAAK,CAAC,CAAC;IAC/D,OAAO,IAAI,CAAC,CAAC;GACd,CAAC,OAAOK,KAAK,EAAE;IACd,OAAO,KAAK;;AAEhB;AAEA,OAAO,MAAMC,OAAO,GAAG,eAAgB,CAAC,OAAO;EAC7CC,YAAY,EAAE1B,mBAAmB;EACjC2B,IAAI,EAAEzB,WAAW;EACjB0B,MAAM,EAAEb,aAAa;EACrB3B,KAAK,EAAE;IACLyC,gBAAgB,EAAE3F,SAAS,CAACkD,KAAK,CAACyC,gBAAgB;IAClDlC,MAAM;IACNvB,YAAY;IACZ5E,eAAe;IACfH,eAAe;IACfsE,UAAU;IACVzE;;CAEH,CAAC,EAAC,CAAE;AAEL,MAAM4I,MAAM,GAAG,eAAgB,CAAC,MAC9BpI,UAAU,CACRkC,EAAE,EACF;AACE;AACA,CACE,oEAAoE,EACpE,mEAAmE,EACnE,oEAAoE,EACpE,oEAAoE,CACrE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CAAE;AAAA,CACvE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CACrE;AACD;AACA,CACE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,EACpE,oEAAoE,CAAE;AAAA,CACvE,CACF,CAACmG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACD,GAAG,CAAEE,CAAC,IAAKpI,MAAM,CAACoI,CAAC,CAAC,CAAC,CAA6C,CAClF,EAAC,CAAE;AACN,MAAMC,MAAM,GAAG,eAAgB,CAAC,MAC9B9I,mBAAmB,CAACwC,EAAE,EAAE;EACtBuG,CAAC,EAAEtI,MAAM,CAAC,oEAAoE,CAAC;EAC/EuI,CAAC,EAAEvI,MAAM,CAAC,MAAM,CAAC;EACjBwI,CAAC,EAAEzG,EAAE,CAAC0G,MAAM,CAACzI,MAAM,CAAC,KAAK,CAAC;CAC3B,CAAC,EAAC,CAAE;AACP,MAAM0I,GAAG,GAAG,eAAgB,CAAC,MAC3B9I,YAAY,CACVyC,SAAS,CAAC0C,eAAe,EACxB4D,OAAiB,IAAI;EACpB,MAAM;IAAEhF,CAAC;IAAEnD;EAAC,CAAE,GAAG6H,MAAM,CAACtG,EAAE,CAAC0G,MAAM,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,OAAOV,MAAM,CAACtE,CAAC,EAAEnD,CAAC,CAAC;AACrB,CAAC,EACD;EACEoI,GAAG,EAAE,gCAAgC;EACrCC,SAAS,EAAE,gCAAgC;EAC3CpD,CAAC,EAAE1D,EAAE,CAAC+G,KAAK;EACXtC,CAAC,EAAE,CAAC;EACJ1D,CAAC,EAAE,GAAG;EACNiG,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE9J;CACP,CACF,EAAC,CAAE;AACN,OAAO,MAAM+J,WAAW,GAAG,eAAgB,CAAC,MAAMP,GAAG,CAACO,WAAW,EAAC,CAAE;AACpE,OAAO,MAAMC,aAAa,GAAG,eAAgB,CAAC,MAAMR,GAAG,CAACQ,aAAa,EAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}