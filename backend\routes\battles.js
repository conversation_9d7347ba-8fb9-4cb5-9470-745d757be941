const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken } = require('../middleware/auth');
const blockchainService = require('../config/blockchain');
const { addUserXP } = require('./users');

const router = express.Router();

// Start PvE battle
router.post('/pve/start', verifyToken, [
  body('heroIds').isArray().isLength({ min: 1, max: 3 }),
  body('difficulty').isInt({ min: 1, max: 10 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { walletAddress } = req.user;
    const { heroIds, difficulty } = req.body;
    
    // Verify hero ownership
    const heroes = await executeQuery(
      'SELECT * FROM heroes WHERE id IN (?) AND owner_address = ? AND is_active = TRUE',
      [heroIds, walletAddress]
    );
    
    if (heroes.length !== heroIds.length) {
      return res.status(400).json({ error: 'Invalid heroes selected' });
    }
    
    // Generate enemy based on difficulty
    const enemy = generatePvEEnemy(difficulty);
    
    // Simulate battle
    const battleResult = simulateBattle(heroes, [enemy]);
    
    // Create battle record
    const battleRecord = await executeQuery(
      `INSERT INTO battles (battle_type, player1_address, player1_heroes, player2_heroes, winner_address, battle_log, duration_seconds)
       VALUES ('pve', ?, ?, ?, ?, ?, ?)`,
      [
        walletAddress,
        JSON.stringify(heroes.map(h => ({ id: h.id, name: h.name, type: h.hero_type }))),
        JSON.stringify([enemy]),
        battleResult.winner === 'player' ? walletAddress : null,
        JSON.stringify(battleResult.log),
        battleResult.duration
      ]
    );
    
    let reward = 0;
    if (battleResult.winner === 'player') {
      // Calculate reward based on difficulty
      reward = (parseInt(process.env.PVE_WIN_REWARD) || 10) * difficulty;
      
      try {
        // Distribute blockchain reward
        if (blockchainService.initialized) {
          await blockchainService.distributePvEReward(walletAddress);
        }
        
        // Update battle record with reward
        await executeQuery(
          'UPDATE battles SET reward_amount = ? WHERE id = ?',
          [reward, battleRecord.insertId]
        );
        
        // Record transaction
        await executeQuery(
          `INSERT INTO transactions (user_address, transaction_type, amount, description)
           VALUES (?, 'earn', ?, ?)`,
          [walletAddress, reward, `PvE victory (Difficulty ${difficulty})`]
        );
        
        // Add XP to user
        const xpGained = difficulty * 10;
        await addUserXP(walletAddress, xpGained);
        
        // Update user stats
        await executeQuery(
          'UPDATE users SET total_battles = total_battles + 1, wins = wins + 1 WHERE wallet_address = ?',
          [walletAddress]
        );
      } catch (error) {
        console.error('Reward distribution error:', error);
      }
    } else {
      // Update user stats for loss
      await executeQuery(
        'UPDATE users SET total_battles = total_battles + 1, losses = losses + 1 WHERE wallet_address = ?',
        [walletAddress]
      );
    }
    
    res.json({
      battleId: battleRecord.insertId,
      result: battleResult.winner,
      reward,
      battleLog: battleResult.log,
      duration: battleResult.duration
    });
  } catch (error) {
    console.error('PvE battle error:', error);
    res.status(500).json({ error: 'Failed to start PvE battle' });
  }
});

// Get battle history
router.get('/history', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    const { limit = 20, offset = 0 } = req.query;
    
    const battles = await executeQuery(
      `SELECT * FROM battles 
       WHERE player1_address = ? OR player2_address = ?
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [walletAddress, walletAddress, parseInt(limit), parseInt(offset)]
    );
    
    const formattedBattles = battles.map(battle => ({
      id: battle.id,
      type: battle.battle_type,
      opponent: battle.battle_type === 'pve' ? 'AI' : 
                (battle.player1_address === walletAddress ? battle.player2_address : battle.player1_address),
      result: battle.winner_address === walletAddress ? 'win' : 
              (battle.winner_address === null ? 'draw' : 'loss'),
      reward: battle.reward_amount,
      duration: battle.duration_seconds,
      createdAt: battle.created_at
    }));
    
    res.json({ battles: formattedBattles });
  } catch (error) {
    console.error('Get battle history error:', error);
    res.status(500).json({ error: 'Failed to get battle history' });
  }
});

// Generate PvE enemy based on difficulty
function generatePvEEnemy(difficulty) {
  const enemyTypes = ['Goblin', 'Orc', 'Skeleton', 'Dragon', 'Demon'];
  const baseStats = {
    hp: 80 + (difficulty * 15),
    atk: 15 + (difficulty * 3),
    def: 10 + (difficulty * 2),
    spd: 8 + difficulty,
    luk: 5 + difficulty
  };
  
  return {
    id: `enemy_${Date.now()}`,
    name: `${enemyTypes[Math.floor(Math.random() * enemyTypes.length)]} Lv.${difficulty}`,
    type: 'enemy',
    level: difficulty,
    stats: baseStats,
    skills: [
      { name: 'Attack', damage: baseStats.atk, cooldown: 1 },
      { name: 'Power Strike', damage: Math.floor(baseStats.atk * 1.5), cooldown: 3 }
    ]
  };
}

// Simple battle simulation
function simulateBattle(playerHeroes, enemyHeroes) {
  const log = [];
  let turn = 1;
  const maxTurns = 50;
  
  // Create battle participants with current HP
  const playerTeam = playerHeroes.map(hero => ({
    ...hero,
    currentHp: hero.hp,
    cooldowns: {}
  }));
  
  const enemyTeam = enemyHeroes.map(enemy => ({
    ...enemy,
    currentHp: enemy.stats.hp,
    cooldowns: {}
  }));
  
  const startTime = Date.now();
  
  while (turn <= maxTurns) {
    // Check win conditions
    const alivePlayerHeroes = playerTeam.filter(h => h.currentHp > 0);
    const aliveEnemyHeroes = enemyTeam.filter(h => h.currentHp > 0);
    
    if (alivePlayerHeroes.length === 0) {
      log.push({ turn, event: 'Player team defeated!' });
      break;
    }
    
    if (aliveEnemyHeroes.length === 0) {
      log.push({ turn, event: 'Enemy team defeated!' });
      break;
    }
    
    // Determine turn order by speed
    const allAlive = [...alivePlayerHeroes, ...aliveEnemyHeroes];
    allAlive.sort((a, b) => (b.spd || b.stats?.spd || 0) - (a.spd || a.stats?.spd || 0));
    
    for (const attacker of allAlive) {
      const isPlayer = playerTeam.includes(attacker);
      const targets = isPlayer ? aliveEnemyHeroes : alivePlayerHeroes;
      
      if (targets.length === 0 || attacker.currentHp <= 0) continue;
      
      // Simple AI: attack random target
      const target = targets[Math.floor(Math.random() * targets.length)];
      const damage = Math.max(1, (attacker.atk || attacker.stats?.atk || 0) - (target.def || target.stats?.def || 0));
      
      target.currentHp = Math.max(0, target.currentHp - damage);
      
      log.push({
        turn,
        attacker: attacker.name,
        target: target.name,
        damage,
        targetHp: target.currentHp
      });
      
      if (target.currentHp === 0) {
        log.push({ turn, event: `${target.name} is defeated!` });
      }
    }
    
    turn++;
  }
  
  const duration = Math.floor((Date.now() - startTime) / 1000);
  const alivePlayerHeroes = playerTeam.filter(h => h.currentHp > 0);
  const aliveEnemyHeroes = enemyTeam.filter(h => h.currentHp > 0);
  
  let winner = 'draw';
  if (alivePlayerHeroes.length > 0 && aliveEnemyHeroes.length === 0) {
    winner = 'player';
  } else if (aliveEnemyHeroes.length > 0 && alivePlayerHeroes.length === 0) {
    winner = 'enemy';
  }
  
  return {
    winner,
    log,
    duration,
    finalState: {
      playerTeam: playerTeam.map(h => ({ name: h.name, hp: h.currentHp })),
      enemyTeam: enemyTeam.map(h => ({ name: h.name, hp: h.currentHp }))
    }
  };
}

module.exports = router;
