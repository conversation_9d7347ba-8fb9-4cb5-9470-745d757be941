const jwt = require('jsonwebtoken');
const { ethers } = require('ethers');
const { executeQuery } = require('../config/database');

// Verify JWT token
const verifyToken = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'Access denied. No token provided.' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;
    
    // Verify user still exists in database
    const users = await executeQuery(
      'SELECT wallet_address, username, level, xp FROM users WHERE wallet_address = ?',
      [decoded.walletAddress]
    );
    
    if (users.length === 0) {
      return res.status(401).json({ error: 'Invalid token. User not found.' });
    }
    
    req.user.userData = users[0];
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token.' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired.' });
    }
    
    console.error('Auth middleware error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

// Verify wallet signature for authentication
const verifyWalletSignature = (message, signature, expectedAddress) => {
  try {
    const recoveredAddress = ethers.verifyMessage(message, signature);
    return recoveredAddress.toLowerCase() === expectedAddress.toLowerCase();
  } catch (error) {
    console.error('Signature verification error:', error);
    return false;
  }
};

// Generate authentication message
const generateAuthMessage = (walletAddress, nonce) => {
  return `Welcome to CryptoQuest!\n\nPlease sign this message to authenticate your wallet.\n\nWallet: ${walletAddress}\nNonce: ${nonce}\nTimestamp: ${Date.now()}`;
};

// Validate wallet address format
const isValidWalletAddress = (address) => {
  return ethers.isAddress(address);
};

// Generate JWT token
const generateToken = (walletAddress, expiresIn = '7d') => {
  return jwt.sign(
    { walletAddress },
    process.env.JWT_SECRET,
    { expiresIn }
  );
};

// Optional authentication (for public endpoints that benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
      
      // Get user data if token is valid
      const users = await executeQuery(
        'SELECT wallet_address, username, level, xp FROM users WHERE wallet_address = ?',
        [decoded.walletAddress]
      );
      
      if (users.length > 0) {
        req.user.userData = users[0];
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

// Admin authentication (for admin-only endpoints)
const verifyAdmin = async (req, res, next) => {
  try {
    await verifyToken(req, res, () => {});
    
    // Check if user is admin (you can implement your own admin logic)
    const adminAddresses = (process.env.ADMIN_ADDRESSES || '').split(',').map(addr => addr.toLowerCase());
    
    if (!adminAddresses.includes(req.user.walletAddress.toLowerCase())) {
      return res.status(403).json({ error: 'Access denied. Admin privileges required.' });
    }
    
    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  verifyToken,
  verifyWalletSignature,
  generateAuthMessage,
  isValidWalletAddress,
  generateToken,
  optionalAuth,
  verifyAdmin
};
