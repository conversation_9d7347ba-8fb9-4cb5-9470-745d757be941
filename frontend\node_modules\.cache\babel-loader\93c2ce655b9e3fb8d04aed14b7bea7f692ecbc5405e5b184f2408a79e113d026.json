{"ast": null, "code": "/**\n *  Some mathematic operations.\n *\n *  @_subsection: api/utils:Math Helpers  [about-maths]\n */\nimport { hexlify, isBytesLike } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\n//const BN_Max256 = (BN_1 << BigInt(256)) - BN_1;\n// IEEE 754 support 53-bits of mantissa\nconst maxValue = 0x1fffffffffffff;\n/**\n *  Convert %%value%% from a twos-compliment representation of %%width%%\n *  bits to its value.\n *\n *  If the highest bit is ``1``, the result will be negative.\n */\nexport function fromTwos(_value, _width) {\n  const value = getUint(_value, \"value\");\n  const width = BigInt(getNumber(_width, \"width\"));\n  assert(value >> width === BN_0, \"overflow\", \"NUMERIC_FAULT\", {\n    operation: \"fromTwos\",\n    fault: \"overflow\",\n    value: _value\n  });\n  // Top bit set; treat as a negative value\n  if (value >> width - BN_1) {\n    const mask = (BN_1 << width) - BN_1;\n    return -((~value & mask) + BN_1);\n  }\n  return value;\n}\n/**\n *  Convert %%value%% to a twos-compliment representation of\n *  %%width%% bits.\n *\n *  The result will always be positive.\n */\nexport function toTwos(_value, _width) {\n  let value = getBigInt(_value, \"value\");\n  const width = BigInt(getNumber(_width, \"width\"));\n  const limit = BN_1 << width - BN_1;\n  if (value < BN_0) {\n    value = -value;\n    assert(value <= limit, \"too low\", \"NUMERIC_FAULT\", {\n      operation: \"toTwos\",\n      fault: \"overflow\",\n      value: _value\n    });\n    const mask = (BN_1 << width) - BN_1;\n    return (~value & mask) + BN_1;\n  } else {\n    assert(value < limit, \"too high\", \"NUMERIC_FAULT\", {\n      operation: \"toTwos\",\n      fault: \"overflow\",\n      value: _value\n    });\n  }\n  return value;\n}\n/**\n *  Mask %%value%% with a bitmask of %%bits%% ones.\n */\nexport function mask(_value, _bits) {\n  const value = getUint(_value, \"value\");\n  const bits = BigInt(getNumber(_bits, \"bits\"));\n  return value & (BN_1 << bits) - BN_1;\n}\n/**\n *  Gets a BigInt from %%value%%. If it is an invalid value for\n *  a BigInt, then an ArgumentError will be thrown for %%name%%.\n */\nexport function getBigInt(value, name) {\n  switch (typeof value) {\n    case \"bigint\":\n      return value;\n    case \"number\":\n      assertArgument(Number.isInteger(value), \"underflow\", name || \"value\", value);\n      assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n      return BigInt(value);\n    case \"string\":\n      try {\n        if (value === \"\") {\n          throw new Error(\"empty string\");\n        }\n        if (value[0] === \"-\" && value[1] !== \"-\") {\n          return -BigInt(value.substring(1));\n        }\n        return BigInt(value);\n      } catch (e) {\n        assertArgument(false, `invalid BigNumberish string: ${e.message}`, name || \"value\", value);\n      }\n  }\n  assertArgument(false, \"invalid BigNumberish value\", name || \"value\", value);\n}\n/**\n *  Returns %%value%% as a bigint, validating it is valid as a bigint\n *  value and that it is positive.\n */\nexport function getUint(value, name) {\n  const result = getBigInt(value, name);\n  assert(result >= BN_0, \"unsigned value cannot be negative\", \"NUMERIC_FAULT\", {\n    fault: \"overflow\",\n    operation: \"getUint\",\n    value\n  });\n  return result;\n}\nconst Nibbles = \"0123456789abcdef\";\n/*\n * Converts %%value%% to a BigInt. If %%value%% is a Uint8Array, it\n * is treated as Big Endian data.\n */\nexport function toBigInt(value) {\n  if (value instanceof Uint8Array) {\n    let result = \"0x0\";\n    for (const v of value) {\n      result += Nibbles[v >> 4];\n      result += Nibbles[v & 0x0f];\n    }\n    return BigInt(result);\n  }\n  return getBigInt(value);\n}\n/**\n *  Gets a //number// from %%value%%. If it is an invalid value for\n *  a //number//, then an ArgumentError will be thrown for %%name%%.\n */\nexport function getNumber(value, name) {\n  switch (typeof value) {\n    case \"bigint\":\n      assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n      return Number(value);\n    case \"number\":\n      assertArgument(Number.isInteger(value), \"underflow\", name || \"value\", value);\n      assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n      return value;\n    case \"string\":\n      try {\n        if (value === \"\") {\n          throw new Error(\"empty string\");\n        }\n        return getNumber(BigInt(value), name);\n      } catch (e) {\n        assertArgument(false, `invalid numeric string: ${e.message}`, name || \"value\", value);\n      }\n  }\n  assertArgument(false, \"invalid numeric value\", name || \"value\", value);\n}\n/**\n *  Converts %%value%% to a number. If %%value%% is a Uint8Array, it\n *  is treated as Big Endian data. Throws if the value is not safe.\n */\nexport function toNumber(value) {\n  return getNumber(toBigInt(value));\n}\n/**\n *  Converts %%value%% to a Big Endian hexstring, optionally padded to\n *  %%width%% bytes.\n */\nexport function toBeHex(_value, _width) {\n  const value = getUint(_value, \"value\");\n  let result = value.toString(16);\n  if (_width == null) {\n    // Ensure the value is of even length\n    if (result.length % 2) {\n      result = \"0\" + result;\n    }\n  } else {\n    const width = getNumber(_width, \"width\");\n    assert(width * 2 >= result.length, `value exceeds width (${width} bytes)`, \"NUMERIC_FAULT\", {\n      operation: \"toBeHex\",\n      fault: \"overflow\",\n      value: _value\n    });\n    // Pad the value to the required width\n    while (result.length < width * 2) {\n      result = \"0\" + result;\n    }\n  }\n  return \"0x\" + result;\n}\n/**\n *  Converts %%value%% to a Big Endian Uint8Array.\n */\nexport function toBeArray(_value) {\n  const value = getUint(_value, \"value\");\n  if (value === BN_0) {\n    return new Uint8Array([]);\n  }\n  let hex = value.toString(16);\n  if (hex.length % 2) {\n    hex = \"0\" + hex;\n  }\n  const result = new Uint8Array(hex.length / 2);\n  for (let i = 0; i < result.length; i++) {\n    const offset = i * 2;\n    result[i] = parseInt(hex.substring(offset, offset + 2), 16);\n  }\n  return result;\n}\n/**\n *  Returns a [[HexString]] for %%value%% safe to use as a //Quantity//.\n *\n *  A //Quantity// does not have and leading 0 values unless the value is\n *  the literal value `0x0`. This is most commonly used for JSSON-RPC\n *  numeric values.\n */\nexport function toQuantity(value) {\n  let result = hexlify(isBytesLike(value) ? value : toBeArray(value)).substring(2);\n  while (result.startsWith(\"0\")) {\n    result = result.substring(1);\n  }\n  if (result === \"\") {\n    result = \"0\";\n  }\n  return \"0x\" + result;\n}", "map": {"version": 3, "names": ["hexlify", "isBytesLike", "assert", "assertArgument", "BN_0", "BigInt", "BN_1", "maxValue", "fromTwos", "_value", "_width", "value", "getUint", "width", "getNumber", "operation", "fault", "mask", "toTwos", "getBigInt", "limit", "_bits", "bits", "name", "Number", "isInteger", "Error", "substring", "e", "message", "result", "Nibbles", "toBigInt", "Uint8Array", "v", "toNumber", "toBeHex", "toString", "length", "toBeArray", "hex", "i", "offset", "parseInt", "toQuantity", "startsWith"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\maths.ts"], "sourcesContent": ["/**\n *  Some mathematic operations.\n *\n *  @_subsection: api/utils:Math Helpers  [about-maths]\n */\nimport { hexlify, isBytesLike } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\n\nimport type { BytesLike } from \"./data.js\";\n\n/**\n *  Any type that can be used where a numeric value is needed.\n */\nexport type Numeric = number | bigint;\n\n/**\n *  Any type that can be used where a big number is needed.\n */\nexport type BigNumberish = string | Numeric;\n\n\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\n\n//const BN_Max256 = (BN_1 << BigInt(256)) - BN_1;\n\n\n// IEEE 754 support 53-bits of mantissa\nconst maxValue = 0x1fffffffffffff;\n\n/**\n *  Convert %%value%% from a twos-compliment representation of %%width%%\n *  bits to its value.\n *\n *  If the highest bit is ``1``, the result will be negative.\n */\nexport function fromTwos(_value: BigNumberish, _width: Numeric): bigint {\n    const value = getUint(_value, \"value\");\n    const width = BigInt(getNumber(_width, \"width\"));\n\n    assert((value >> width) === BN_0, \"overflow\", \"NUMERIC_FAULT\", {\n        operation: \"fromTwos\", fault: \"overflow\", value: _value\n    });\n\n    // Top bit set; treat as a negative value\n    if (value >> (width - BN_1)) {\n        const mask = (BN_1 << width) - BN_1;\n        return -(((~value) & mask) + BN_1);\n    }\n\n    return value;\n}\n\n/**\n *  Convert %%value%% to a twos-compliment representation of\n *  %%width%% bits.\n *\n *  The result will always be positive.\n */\nexport function toTwos(_value: BigNumberish, _width: Numeric): bigint {\n    let value = getBigInt(_value, \"value\");\n    const width = BigInt(getNumber(_width, \"width\"));\n\n    const limit = (BN_1 << (width - BN_1));\n\n    if (value < BN_0) {\n        value = -value;\n        assert(value <= limit, \"too low\", \"NUMERIC_FAULT\", {\n            operation: \"toTwos\", fault: \"overflow\", value: _value\n        });\n        const mask = (BN_1 << width) - BN_1;\n        return ((~value) & mask) + BN_1;\n    } else {\n        assert(value < limit, \"too high\", \"NUMERIC_FAULT\", {\n            operation: \"toTwos\", fault: \"overflow\", value: _value\n        });\n    }\n\n    return value;\n}\n\n/**\n *  Mask %%value%% with a bitmask of %%bits%% ones.\n */\nexport function mask(_value: BigNumberish, _bits: Numeric): bigint {\n    const value = getUint(_value, \"value\");\n    const bits = BigInt(getNumber(_bits, \"bits\"));\n    return value & ((BN_1 << bits) - BN_1);\n}\n\n/**\n *  Gets a BigInt from %%value%%. If it is an invalid value for\n *  a BigInt, then an ArgumentError will be thrown for %%name%%.\n */\nexport function getBigInt(value: BigNumberish, name?: string): bigint {\n    switch (typeof(value)) {\n        case \"bigint\": return value;\n        case \"number\":\n            assertArgument(Number.isInteger(value), \"underflow\", name || \"value\", value);\n            assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n            return BigInt(value);\n        case \"string\":\n            try {\n                if (value === \"\") { throw new Error(\"empty string\"); }\n                if (value[0] === \"-\" && value[1] !== \"-\") {\n                    return -BigInt(value.substring(1));\n                }\n                return BigInt(value);\n            } catch(e: any) {\n                assertArgument(false, `invalid BigNumberish string: ${ e.message }`, name || \"value\", value);\n            }\n    }\n    assertArgument(false, \"invalid BigNumberish value\", name || \"value\", value);\n}\n\n/**\n *  Returns %%value%% as a bigint, validating it is valid as a bigint\n *  value and that it is positive.\n */\nexport function getUint(value: BigNumberish, name?: string): bigint {\n    const result = getBigInt(value, name);\n    assert(result >= BN_0, \"unsigned value cannot be negative\", \"NUMERIC_FAULT\", {\n        fault: \"overflow\", operation: \"getUint\", value\n    });\n    return result;\n}\n\nconst Nibbles = \"0123456789abcdef\";\n\n/*\n * Converts %%value%% to a BigInt. If %%value%% is a Uint8Array, it\n * is treated as Big Endian data.\n */\nexport function toBigInt(value: BigNumberish | Uint8Array): bigint {\n    if (value instanceof Uint8Array) {\n        let result = \"0x0\";\n        for (const v of value) {\n            result += Nibbles[v >> 4];\n            result += Nibbles[v & 0x0f];\n        }\n        return BigInt(result);\n    }\n\n    return getBigInt(value);\n}\n\n/**\n *  Gets a //number// from %%value%%. If it is an invalid value for\n *  a //number//, then an ArgumentError will be thrown for %%name%%.\n */\nexport function getNumber(value: BigNumberish, name?: string): number {\n    switch (typeof(value)) {\n        case \"bigint\":\n            assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n            return Number(value);\n        case \"number\":\n            assertArgument(Number.isInteger(value), \"underflow\", name || \"value\", value);\n            assertArgument(value >= -maxValue && value <= maxValue, \"overflow\", name || \"value\", value);\n            return value;\n        case \"string\":\n            try {\n                if (value === \"\") { throw new Error(\"empty string\"); }\n                return getNumber(BigInt(value), name);\n            } catch(e: any) {\n                assertArgument(false, `invalid numeric string: ${ e.message }`, name || \"value\", value);\n            }\n    }\n    assertArgument(false, \"invalid numeric value\", name || \"value\", value);\n}\n\n\n/**\n *  Converts %%value%% to a number. If %%value%% is a Uint8Array, it\n *  is treated as Big Endian data. Throws if the value is not safe.\n */\nexport function toNumber(value: BigNumberish | Uint8Array): number {\n    return getNumber(toBigInt(value));\n}\n\n/**\n *  Converts %%value%% to a Big Endian hexstring, optionally padded to\n *  %%width%% bytes.\n */\nexport function toBeHex(_value: BigNumberish, _width?: Numeric): string {\n    const value = getUint(_value, \"value\");\n\n    let result = value.toString(16);\n\n    if (_width == null) {\n        // Ensure the value is of even length\n        if (result.length % 2) { result = \"0\" + result; }\n    } else {\n        const width = getNumber(_width, \"width\");\n        assert(width * 2 >= result.length, `value exceeds width (${ width } bytes)`, \"NUMERIC_FAULT\", {\n            operation: \"toBeHex\",\n            fault: \"overflow\",\n            value: _value\n        });\n\n        // Pad the value to the required width\n        while (result.length < (width * 2)) { result = \"0\" + result; }\n\n    }\n\n    return \"0x\" + result;\n}\n\n/**\n *  Converts %%value%% to a Big Endian Uint8Array.\n */\nexport function toBeArray(_value: BigNumberish): Uint8Array {\n    const value = getUint(_value, \"value\");\n\n    if (value === BN_0) { return new Uint8Array([ ]); }\n\n    let hex = value.toString(16);\n    if (hex.length % 2) { hex = \"0\" + hex; }\n\n    const result = new Uint8Array(hex.length / 2);\n    for (let i = 0; i < result.length; i++) {\n        const offset = i * 2;\n        result[i] = parseInt(hex.substring(offset, offset + 2), 16);\n    }\n\n    return result;\n}\n\n/**\n *  Returns a [[HexString]] for %%value%% safe to use as a //Quantity//.\n *\n *  A //Quantity// does not have and leading 0 values unless the value is\n *  the literal value `0x0`. This is most commonly used for JSSON-RPC\n *  numeric values.\n */\nexport function toQuantity(value: BytesLike | BigNumberish): string {\n    let result = hexlify(isBytesLike(value) ? value: toBeArray(value)).substring(2);\n    while (result.startsWith(\"0\")) { result = result.substring(1); }\n    if (result === \"\") { result = \"0\"; }\n    return \"0x\" + result;\n}\n"], "mappings": "AAAA;;;;;AAKA,SAASA,OAAO,EAAEC,WAAW,QAAQ,WAAW;AAChD,SAASC,MAAM,EAAEC,cAAc,QAAQ,aAAa;AAepD,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AAEtB;AAGA;AACA,MAAME,QAAQ,GAAG,gBAAgB;AAEjC;;;;;;AAMA,OAAM,SAAUC,QAAQA,CAACC,MAAoB,EAAEC,MAAe;EAC1D,MAAMC,KAAK,GAAGC,OAAO,CAACH,MAAM,EAAE,OAAO,CAAC;EACtC,MAAMI,KAAK,GAAGR,MAAM,CAACS,SAAS,CAACJ,MAAM,EAAE,OAAO,CAAC,CAAC;EAEhDR,MAAM,CAAES,KAAK,IAAIE,KAAK,KAAMT,IAAI,EAAE,UAAU,EAAE,eAAe,EAAE;IAC3DW,SAAS,EAAE,UAAU;IAAEC,KAAK,EAAE,UAAU;IAAEL,KAAK,EAAEF;GACpD,CAAC;EAEF;EACA,IAAIE,KAAK,IAAKE,KAAK,GAAGP,IAAK,EAAE;IACzB,MAAMW,IAAI,GAAG,CAACX,IAAI,IAAIO,KAAK,IAAIP,IAAI;IACnC,OAAO,EAAE,CAAE,CAACK,KAAK,GAAIM,IAAI,IAAIX,IAAI,CAAC;;EAGtC,OAAOK,KAAK;AAChB;AAEA;;;;;;AAMA,OAAM,SAAUO,MAAMA,CAACT,MAAoB,EAAEC,MAAe;EACxD,IAAIC,KAAK,GAAGQ,SAAS,CAACV,MAAM,EAAE,OAAO,CAAC;EACtC,MAAMI,KAAK,GAAGR,MAAM,CAACS,SAAS,CAACJ,MAAM,EAAE,OAAO,CAAC,CAAC;EAEhD,MAAMU,KAAK,GAAId,IAAI,IAAKO,KAAK,GAAGP,IAAM;EAEtC,IAAIK,KAAK,GAAGP,IAAI,EAAE;IACdO,KAAK,GAAG,CAACA,KAAK;IACdT,MAAM,CAACS,KAAK,IAAIS,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE;MAC/CL,SAAS,EAAE,QAAQ;MAAEC,KAAK,EAAE,UAAU;MAAEL,KAAK,EAAEF;KAClD,CAAC;IACF,MAAMQ,IAAI,GAAG,CAACX,IAAI,IAAIO,KAAK,IAAIP,IAAI;IACnC,OAAO,CAAE,CAACK,KAAK,GAAIM,IAAI,IAAIX,IAAI;GAClC,MAAM;IACHJ,MAAM,CAACS,KAAK,GAAGS,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE;MAC/CL,SAAS,EAAE,QAAQ;MAAEC,KAAK,EAAE,UAAU;MAAEL,KAAK,EAAEF;KAClD,CAAC;;EAGN,OAAOE,KAAK;AAChB;AAEA;;;AAGA,OAAM,SAAUM,IAAIA,CAACR,MAAoB,EAAEY,KAAc;EACrD,MAAMV,KAAK,GAAGC,OAAO,CAACH,MAAM,EAAE,OAAO,CAAC;EACtC,MAAMa,IAAI,GAAGjB,MAAM,CAACS,SAAS,CAACO,KAAK,EAAE,MAAM,CAAC,CAAC;EAC7C,OAAOV,KAAK,GAAI,CAACL,IAAI,IAAIgB,IAAI,IAAIhB,IAAK;AAC1C;AAEA;;;;AAIA,OAAM,SAAUa,SAASA,CAACR,KAAmB,EAAEY,IAAa;EACxD,QAAQ,OAAOZ,KAAM;IACjB,KAAK,QAAQ;MAAE,OAAOA,KAAK;IAC3B,KAAK,QAAQ;MACTR,cAAc,CAACqB,MAAM,CAACC,SAAS,CAACd,KAAK,CAAC,EAAE,WAAW,EAAEY,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;MAC5ER,cAAc,CAACQ,KAAK,IAAI,CAACJ,QAAQ,IAAII,KAAK,IAAIJ,QAAQ,EAAE,UAAU,EAAEgB,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;MAC3F,OAAON,MAAM,CAACM,KAAK,CAAC;IACxB,KAAK,QAAQ;MACT,IAAI;QACA,IAAIA,KAAK,KAAK,EAAE,EAAE;UAAE,MAAM,IAAIe,KAAK,CAAC,cAAc,CAAC;;QACnD,IAAIf,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACtC,OAAO,CAACN,MAAM,CAACM,KAAK,CAACgB,SAAS,CAAC,CAAC,CAAC,CAAC;;QAEtC,OAAOtB,MAAM,CAACM,KAAK,CAAC;OACvB,CAAC,OAAMiB,CAAM,EAAE;QACZzB,cAAc,CAAC,KAAK,EAAE,gCAAiCyB,CAAC,CAACC,OAAQ,EAAE,EAAEN,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;;;EAGxGR,cAAc,CAAC,KAAK,EAAE,4BAA4B,EAAEoB,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;AAC/E;AAEA;;;;AAIA,OAAM,SAAUC,OAAOA,CAACD,KAAmB,EAAEY,IAAa;EACtD,MAAMO,MAAM,GAAGX,SAAS,CAACR,KAAK,EAAEY,IAAI,CAAC;EACrCrB,MAAM,CAAC4B,MAAM,IAAI1B,IAAI,EAAE,mCAAmC,EAAE,eAAe,EAAE;IACzEY,KAAK,EAAE,UAAU;IAAED,SAAS,EAAE,SAAS;IAAEJ;GAC5C,CAAC;EACF,OAAOmB,MAAM;AACjB;AAEA,MAAMC,OAAO,GAAG,kBAAkB;AAElC;;;;AAIA,OAAM,SAAUC,QAAQA,CAACrB,KAAgC;EACrD,IAAIA,KAAK,YAAYsB,UAAU,EAAE;IAC7B,IAAIH,MAAM,GAAG,KAAK;IAClB,KAAK,MAAMI,CAAC,IAAIvB,KAAK,EAAE;MACnBmB,MAAM,IAAIC,OAAO,CAACG,CAAC,IAAI,CAAC,CAAC;MACzBJ,MAAM,IAAIC,OAAO,CAACG,CAAC,GAAG,IAAI,CAAC;;IAE/B,OAAO7B,MAAM,CAACyB,MAAM,CAAC;;EAGzB,OAAOX,SAAS,CAACR,KAAK,CAAC;AAC3B;AAEA;;;;AAIA,OAAM,SAAUG,SAASA,CAACH,KAAmB,EAAEY,IAAa;EACxD,QAAQ,OAAOZ,KAAM;IACjB,KAAK,QAAQ;MACTR,cAAc,CAACQ,KAAK,IAAI,CAACJ,QAAQ,IAAII,KAAK,IAAIJ,QAAQ,EAAE,UAAU,EAAEgB,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;MAC3F,OAAOa,MAAM,CAACb,KAAK,CAAC;IACxB,KAAK,QAAQ;MACTR,cAAc,CAACqB,MAAM,CAACC,SAAS,CAACd,KAAK,CAAC,EAAE,WAAW,EAAEY,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;MAC5ER,cAAc,CAACQ,KAAK,IAAI,CAACJ,QAAQ,IAAII,KAAK,IAAIJ,QAAQ,EAAE,UAAU,EAAEgB,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;MAC3F,OAAOA,KAAK;IAChB,KAAK,QAAQ;MACT,IAAI;QACA,IAAIA,KAAK,KAAK,EAAE,EAAE;UAAE,MAAM,IAAIe,KAAK,CAAC,cAAc,CAAC;;QACnD,OAAOZ,SAAS,CAACT,MAAM,CAACM,KAAK,CAAC,EAAEY,IAAI,CAAC;OACxC,CAAC,OAAMK,CAAM,EAAE;QACZzB,cAAc,CAAC,KAAK,EAAE,2BAA4ByB,CAAC,CAACC,OAAQ,EAAE,EAAEN,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;;;EAGnGR,cAAc,CAAC,KAAK,EAAE,uBAAuB,EAAEoB,IAAI,IAAI,OAAO,EAAEZ,KAAK,CAAC;AAC1E;AAGA;;;;AAIA,OAAM,SAAUwB,QAAQA,CAACxB,KAAgC;EACrD,OAAOG,SAAS,CAACkB,QAAQ,CAACrB,KAAK,CAAC,CAAC;AACrC;AAEA;;;;AAIA,OAAM,SAAUyB,OAAOA,CAAC3B,MAAoB,EAAEC,MAAgB;EAC1D,MAAMC,KAAK,GAAGC,OAAO,CAACH,MAAM,EAAE,OAAO,CAAC;EAEtC,IAAIqB,MAAM,GAAGnB,KAAK,CAAC0B,QAAQ,CAAC,EAAE,CAAC;EAE/B,IAAI3B,MAAM,IAAI,IAAI,EAAE;IAChB;IACA,IAAIoB,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MAAER,MAAM,GAAG,GAAG,GAAGA,MAAM;;GACjD,MAAM;IACH,MAAMjB,KAAK,GAAGC,SAAS,CAACJ,MAAM,EAAE,OAAO,CAAC;IACxCR,MAAM,CAACW,KAAK,GAAG,CAAC,IAAIiB,MAAM,CAACQ,MAAM,EAAE,wBAAyBzB,KAAM,SAAS,EAAE,eAAe,EAAE;MAC1FE,SAAS,EAAE,SAAS;MACpBC,KAAK,EAAE,UAAU;MACjBL,KAAK,EAAEF;KACV,CAAC;IAEF;IACA,OAAOqB,MAAM,CAACQ,MAAM,GAAIzB,KAAK,GAAG,CAAE,EAAE;MAAEiB,MAAM,GAAG,GAAG,GAAGA,MAAM;;;EAI/D,OAAO,IAAI,GAAGA,MAAM;AACxB;AAEA;;;AAGA,OAAM,SAAUS,SAASA,CAAC9B,MAAoB;EAC1C,MAAME,KAAK,GAAGC,OAAO,CAACH,MAAM,EAAE,OAAO,CAAC;EAEtC,IAAIE,KAAK,KAAKP,IAAI,EAAE;IAAE,OAAO,IAAI6B,UAAU,CAAC,EAAG,CAAC;;EAEhD,IAAIO,GAAG,GAAG7B,KAAK,CAAC0B,QAAQ,CAAC,EAAE,CAAC;EAC5B,IAAIG,GAAG,CAACF,MAAM,GAAG,CAAC,EAAE;IAAEE,GAAG,GAAG,GAAG,GAAGA,GAAG;;EAErC,MAAMV,MAAM,GAAG,IAAIG,UAAU,CAACO,GAAG,CAACF,MAAM,GAAG,CAAC,CAAC;EAC7C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,CAACQ,MAAM,EAAEG,CAAC,EAAE,EAAE;IACpC,MAAMC,MAAM,GAAGD,CAAC,GAAG,CAAC;IACpBX,MAAM,CAACW,CAAC,CAAC,GAAGE,QAAQ,CAACH,GAAG,CAACb,SAAS,CAACe,MAAM,EAAEA,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;;EAG/D,OAAOZ,MAAM;AACjB;AAEA;;;;;;;AAOA,OAAM,SAAUc,UAAUA,CAACjC,KAA+B;EACtD,IAAImB,MAAM,GAAG9B,OAAO,CAACC,WAAW,CAACU,KAAK,CAAC,GAAGA,KAAK,GAAE4B,SAAS,CAAC5B,KAAK,CAAC,CAAC,CAACgB,SAAS,CAAC,CAAC,CAAC;EAC/E,OAAOG,MAAM,CAACe,UAAU,CAAC,GAAG,CAAC,EAAE;IAAEf,MAAM,GAAGA,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC;;EAC7D,IAAIG,MAAM,KAAK,EAAE,EAAE;IAAEA,MAAM,GAAG,GAAG;;EACjC,OAAO,IAAI,GAAGA,MAAM;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}