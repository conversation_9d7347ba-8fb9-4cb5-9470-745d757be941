{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Short <PERSON> curve. The formula is: y² = x³ + ax + b\nimport * as mod from './modular.js';\nimport * as ut from './utils.js';\nimport { ensureBytes } from './utils.js';\nimport { wNAF, validateBasic } from './curve.js';\nfunction validatePointOpts(curve) {\n  const opts = validateBasic(curve);\n  ut.validateObject(opts, {\n    a: 'field',\n    b: 'field'\n  }, {\n    allowedPrivateKeyLengths: 'array',\n    wrapPrivateKey: 'boolean',\n    isTorsionFree: 'function',\n    clearCofactor: 'function',\n    allowInfinityPoint: 'boolean',\n    fromBytes: 'function',\n    toBytes: 'function'\n  });\n  const {\n    endo,\n    Fp,\n    a\n  } = opts;\n  if (endo) {\n    if (!Fp.eql(a, Fp.ZERO)) {\n      throw new Error('Endomorphism can only be defined for Koblitz curves that have a=0');\n    }\n    if (typeof endo !== 'object' || typeof endo.beta !== 'bigint' || typeof endo.splitScalar !== 'function') {\n      throw new Error('Expected endomorphism with beta: bigint and splitScalar: function');\n    }\n  }\n  return Object.freeze({\n    ...opts\n  });\n}\n// ASN.1 DER encoding utilities\nconst {\n  bytesToNumberBE: b2n,\n  hexToBytes: h2b\n} = ut;\nexport const DER = {\n  // asn.1 DER encoding utils\n  Err: class DERErr extends Error {\n    constructor(m = '') {\n      super(m);\n    }\n  },\n  _parseInt(data) {\n    const {\n      Err: E\n    } = DER;\n    if (data.length < 2 || data[0] !== 0x02) throw new E('Invalid signature integer tag');\n    const len = data[1];\n    const res = data.subarray(2, len + 2);\n    if (!len || res.length !== len) throw new E('Invalid signature integer: wrong length');\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    if (res[0] & 0b10000000) throw new E('Invalid signature integer: negative');\n    if (res[0] === 0x00 && !(res[1] & 0b10000000)) throw new E('Invalid signature integer: unnecessary leading zero');\n    return {\n      d: b2n(res),\n      l: data.subarray(len + 2)\n    }; // d is data, l is left\n  },\n  toSig(hex) {\n    // parse DER signature\n    const {\n      Err: E\n    } = DER;\n    const data = typeof hex === 'string' ? h2b(hex) : hex;\n    if (!(data instanceof Uint8Array)) throw new Error('ui8a expected');\n    let l = data.length;\n    if (l < 2 || data[0] != 0x30) throw new E('Invalid signature tag');\n    if (data[1] !== l - 2) throw new E('Invalid signature: incorrect length');\n    const {\n      d: r,\n      l: sBytes\n    } = DER._parseInt(data.subarray(2));\n    const {\n      d: s,\n      l: rBytesLeft\n    } = DER._parseInt(sBytes);\n    if (rBytesLeft.length) throw new E('Invalid signature: left bytes after parsing');\n    return {\n      r,\n      s\n    };\n  },\n  hexFromSig(sig) {\n    // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n    const slice = s => Number.parseInt(s[0], 16) & 0b1000 ? '00' + s : s;\n    const h = num => {\n      const hex = num.toString(16);\n      return hex.length & 1 ? `0${hex}` : hex;\n    };\n    const s = slice(h(sig.s));\n    const r = slice(h(sig.r));\n    const shl = s.length / 2;\n    const rhl = r.length / 2;\n    const sl = h(shl);\n    const rl = h(rhl);\n    return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n  }\n};\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0),\n  _1n = BigInt(1),\n  _2n = BigInt(2),\n  _3n = BigInt(3),\n  _4n = BigInt(4);\nexport function weierstrassPoints(opts) {\n  const CURVE = validatePointOpts(opts);\n  const {\n    Fp\n  } = CURVE; // All curves has same field / group length as for now, but they can differ\n  const toBytes = CURVE.toBytes || ((_c, point, _isCompressed) => {\n    const a = point.toAffine();\n    return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n  });\n  const fromBytes = CURVE.fromBytes || (bytes => {\n    // const head = bytes[0];\n    const tail = bytes.subarray(1);\n    // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n    const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n    const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n    return {\n      x,\n      y\n    };\n  });\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula\n   * @returns y²\n   */\n  function weierstrassEquation(x) {\n    const {\n      a,\n      b\n    } = CURVE;\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x2 * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n  }\n  // Validate whether the passed curve params are valid.\n  // We check if curve equation works for generator point.\n  // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n  // ProjectivePoint class has not been initialized yet.\n  if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx))) throw new Error('bad generator point: equation left != right');\n  // Valid group elements reside in range 1..n-1\n  function isWithinCurveOrder(num) {\n    return typeof num === 'bigint' && _0n < num && num < CURVE.n;\n  }\n  function assertGE(num) {\n    if (!isWithinCurveOrder(num)) throw new Error('Expected valid bigint: 0 < bigint < curve.n');\n  }\n  // Validates if priv key is valid and converts it to bigint.\n  // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n  function normPrivateKeyToScalar(key) {\n    const {\n      allowedPrivateKeyLengths: lengths,\n      nByteLength,\n      wrapPrivateKey,\n      n\n    } = CURVE;\n    if (lengths && typeof key !== 'bigint') {\n      if (key instanceof Uint8Array) key = ut.bytesToHex(key);\n      // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n      if (typeof key !== 'string' || !lengths.includes(key.length)) throw new Error('Invalid key');\n      key = key.padStart(nByteLength * 2, '0');\n    }\n    let num;\n    try {\n      num = typeof key === 'bigint' ? key : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n    } catch (error) {\n      throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n    }\n    if (wrapPrivateKey) num = mod.mod(num, n); // disabled by default, enabled for BLS\n    assertGE(num); // num in range [1..N-1]\n    return num;\n  }\n  const pointPrecomputes = new Map();\n  function assertPrjPoint(other) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n   * Default Point works in 2d / affine coordinates: (x, y)\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point {\n    constructor(px, py, pz) {\n      this.px = px;\n      this.py = py;\n      this.pz = pz;\n      if (px == null || !Fp.isValid(px)) throw new Error('x required');\n      if (py == null || !Fp.isValid(py)) throw new Error('y required');\n      if (pz == null || !Fp.isValid(pz)) throw new Error('z required');\n    }\n    // Does not validate if the point is on-curve.\n    // Use fromHex instead, or call assertValidity() later.\n    static fromAffine(p) {\n      const {\n        x,\n        y\n      } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      const is0 = i => Fp.eql(i, Fp.ZERO);\n      // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n      if (is0(x) && is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n    get x() {\n      return this.toAffine().x;\n    }\n    get y() {\n      return this.toAffine().y;\n    }\n    /**\n     * Takes a bunch of Projective Points but executes only one\n     * inversion on all of them. Inversion is very slow operation,\n     * so this improves performance massively.\n     * Optimization: converts a list of projective points to a list of identical points with Z=1.\n     */\n    static normalizeZ(points) {\n      const toInv = Fp.invertBatch(points.map(p => p.pz));\n      return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n    }\n    /**\n     * Converts hash string or Uint8Array to Point.\n     * @param hex short/long ECDSA hex\n     */\n    static fromHex(hex) {\n      const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n      P.assertValidity();\n      return P;\n    }\n    // Multiplies generator point by privateKey.\n    static fromPrivateKey(privateKey) {\n      return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n    }\n    // \"Private method\", don't use it directly\n    _setWindowSize(windowSize) {\n      this._WINDOW_SIZE = windowSize;\n      pointPrecomputes.delete(this);\n    }\n    // A point on curve is valid if it conforms to equation.\n    assertValidity() {\n      if (this.is0()) {\n        // (0, 1, 0) aka ZERO is invalid in most contexts.\n        // In BLS, ZERO can be serialized, so we allow it.\n        // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n        if (CURVE.allowInfinityPoint && !Fp.is0(this.py)) return;\n        throw new Error('bad point: ZERO');\n      }\n      // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n      const {\n        x,\n        y\n      } = this.toAffine();\n      // Check if x, y are valid field elements\n      if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not FE');\n      const left = Fp.sqr(y); // y²\n      const right = weierstrassEquation(x); // x³ + ax + b\n      if (!Fp.eql(left, right)) throw new Error('bad point: equation left != right');\n      if (!this.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    }\n    hasEvenY() {\n      const {\n        y\n      } = this.toAffine();\n      if (Fp.isOdd) return !Fp.isOdd(y);\n      throw new Error(\"Field doesn't support isOdd\");\n    }\n    /**\n     * Compare one point to another.\n     */\n    equals(other) {\n      assertPrjPoint(other);\n      const {\n        px: X1,\n        py: Y1,\n        pz: Z1\n      } = this;\n      const {\n        px: X2,\n        py: Y2,\n        pz: Z2\n      } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n    /**\n     * Flips point to one corresponding to (x, -y) in Affine coordinates.\n     */\n    negate() {\n      return new Point(this.px, Fp.neg(this.py), this.pz);\n    }\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const {\n        a,\n        b\n      } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const {\n        px: X1,\n        py: Y1,\n        pz: Z1\n      } = this;\n      let X3 = Fp.ZERO,\n        Y3 = Fp.ZERO,\n        Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other) {\n      assertPrjPoint(other);\n      const {\n        px: X1,\n        py: Y1,\n        pz: Z1\n      } = this;\n      const {\n        px: X2,\n        py: Y2,\n        pz: Z2\n      } = other;\n      let X3 = Fp.ZERO,\n        Y3 = Fp.ZERO,\n        Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n    subtract(other) {\n      return this.add(other.negate());\n    }\n    is0() {\n      return this.equals(Point.ZERO);\n    }\n    wNAF(n) {\n      return wnaf.wNAFCached(this, pointPrecomputes, n, comp => {\n        const toInv = Fp.invertBatch(comp.map(p => p.pz));\n        return comp.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n      });\n    }\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed private key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(n) {\n      const I = Point.ZERO;\n      if (n === _0n) return I;\n      assertGE(n); // Will throw on 0\n      if (n === _1n) return this;\n      const {\n        endo\n      } = CURVE;\n      if (!endo) return wnaf.unsafeLadder(this, n);\n      // Apply endomorphism\n      let {\n        k1neg,\n        k1,\n        k2neg,\n        k2\n      } = endo.splitScalar(n);\n      let k1p = I;\n      let k2p = I;\n      let d = this;\n      while (k1 > _0n || k2 > _0n) {\n        if (k1 & _1n) k1p = k1p.add(d);\n        if (k2 & _1n) k2p = k2p.add(d);\n        d = d.double();\n        k1 >>= _1n;\n        k2 >>= _1n;\n      }\n      if (k1neg) k1p = k1p.negate();\n      if (k2neg) k2p = k2p.negate();\n      k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n      return k1p.add(k2p);\n    }\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar) {\n      assertGE(scalar);\n      let n = scalar;\n      let point, fake; // Fake point is used to const-time mult\n      const {\n        endo\n      } = CURVE;\n      if (endo) {\n        const {\n          k1neg,\n          k1,\n          k2neg,\n          k2\n        } = endo.splitScalar(n);\n        let {\n          p: k1p,\n          f: f1p\n        } = this.wNAF(k1);\n        let {\n          p: k2p,\n          f: f2p\n        } = this.wNAF(k2);\n        k1p = wnaf.constTimeNegate(k1neg, k1p);\n        k2p = wnaf.constTimeNegate(k2neg, k2p);\n        k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n        point = k1p.add(k2p);\n        fake = f1p.add(f2p);\n      } else {\n        const {\n          p,\n          f\n        } = this.wNAF(n);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return Point.normalizeZ([point, fake])[0];\n    }\n    /**\n     * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n     * Not using Strauss-Shamir trick: precomputation tables are faster.\n     * The trick could be useful if both P and Q are not G (not in our case).\n     * @returns non-zero affine point\n     */\n    multiplyAndAddUnsafe(Q, a, b) {\n      const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n      const mul = (P, a // Select faster multiply() method\n      ) => a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a);\n      const sum = mul(this, a).add(mul(Q, b));\n      return sum.is0() ? undefined : sum;\n    }\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    toAffine(iz) {\n      const {\n        px: x,\n        py: y,\n        pz: z\n      } = this;\n      const is0 = this.is0();\n      // If invZ was 0, we return zero point. However we still want to execute\n      // all operations, so we replace invZ with a random number, 1.\n      if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(z);\n      const ax = Fp.mul(x, iz);\n      const ay = Fp.mul(y, iz);\n      const zz = Fp.mul(z, iz);\n      if (is0) return {\n        x: Fp.ZERO,\n        y: Fp.ZERO\n      };\n      if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n      return {\n        x: ax,\n        y: ay\n      };\n    }\n    isTorsionFree() {\n      const {\n        h: cofactor,\n        isTorsionFree\n      } = CURVE;\n      if (cofactor === _1n) return true; // No subgroups, always torsion-free\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n    }\n    clearCofactor() {\n      const {\n        h: cofactor,\n        clearCofactor\n      } = CURVE;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this);\n      return this.multiplyUnsafe(CURVE.h);\n    }\n    toRawBytes(isCompressed = true) {\n      this.assertValidity();\n      return toBytes(Point, this, isCompressed);\n    }\n    toHex(isCompressed = true) {\n      return ut.bytesToHex(this.toRawBytes(isCompressed));\n    }\n  }\n  Point.BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n  Point.ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n  const _bits = CURVE.nBitLength;\n  const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n  // Validate if generator point is on curve\n  return {\n    CURVE,\n    ProjectivePoint: Point,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder\n  };\n}\nfunction validateOpts(curve) {\n  const opts = validateBasic(curve);\n  ut.validateObject(opts, {\n    hash: 'hash',\n    hmac: 'function',\n    randomBytes: 'function'\n  }, {\n    bits2int: 'function',\n    bits2int_modN: 'function',\n    lowS: 'boolean'\n  });\n  return Object.freeze({\n    lowS: true,\n    ...opts\n  });\n}\nexport function weierstrass(curveDef) {\n  const CURVE = validateOpts(curveDef);\n  const {\n    Fp,\n    n: CURVE_ORDER\n  } = CURVE;\n  const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n  const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n  function isValidFieldElement(num) {\n    return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n  }\n  function modN(a) {\n    return mod.mod(a, CURVE_ORDER);\n  }\n  function invN(a) {\n    return mod.invert(a, CURVE_ORDER);\n  }\n  const {\n    ProjectivePoint: Point,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder\n  } = weierstrassPoints({\n    ...CURVE,\n    toBytes(_c, point, isCompressed) {\n      const a = point.toAffine();\n      const x = Fp.toBytes(a.x);\n      const cat = ut.concatBytes;\n      if (isCompressed) {\n        return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n      } else {\n        return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n      }\n    },\n    fromBytes(bytes) {\n      const len = bytes.length;\n      const head = bytes[0];\n      const tail = bytes.subarray(1);\n      // this.assertValidity() is done inside of fromHex\n      if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n        const x = ut.bytesToNumberBE(tail);\n        if (!isValidFieldElement(x)) throw new Error('Point is not on curve');\n        const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n        let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n        const isYOdd = (y & _1n) === _1n;\n        // ECDSA\n        const isHeadOdd = (head & 1) === 1;\n        if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n        return {\n          x,\n          y\n        };\n      } else if (len === uncompressedLen && head === 0x04) {\n        const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n        const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n        return {\n          x,\n          y\n        };\n      } else {\n        throw new Error(`Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`);\n      }\n    }\n  });\n  const numToNByteStr = num => ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n  function isBiggerThanHalfOrder(number) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n  function normalizeS(s) {\n    return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n  }\n  // slice bytes num\n  const slcNum = (b, from, to) => ut.bytesToNumberBE(b.slice(from, to));\n  /**\n   * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n   */\n  class Signature {\n    constructor(r, s, recovery) {\n      this.r = r;\n      this.s = s;\n      this.recovery = recovery;\n      this.assertValidity();\n    }\n    // pair (bytes of r, bytes of s)\n    static fromCompact(hex) {\n      const l = CURVE.nByteLength;\n      hex = ensureBytes('compactSignature', hex, l * 2);\n      return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n    }\n    // DER encoded ECDSA signature\n    // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n    static fromDER(hex) {\n      const {\n        r,\n        s\n      } = DER.toSig(ensureBytes('DER', hex));\n      return new Signature(r, s);\n    }\n    assertValidity() {\n      // can use assertGE here\n      if (!isWithinCurveOrder(this.r)) throw new Error('r must be 0 < r < CURVE.n');\n      if (!isWithinCurveOrder(this.s)) throw new Error('s must be 0 < s < CURVE.n');\n    }\n    addRecoveryBit(recovery) {\n      return new Signature(this.r, this.s, recovery);\n    }\n    recoverPublicKey(msgHash) {\n      const {\n        r,\n        s,\n        recovery: rec\n      } = this;\n      const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n      const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n      if (radj >= Fp.ORDER) throw new Error('recovery id 2 or 3 invalid');\n      const prefix = (rec & 1) === 0 ? '02' : '03';\n      const R = Point.fromHex(prefix + numToNByteStr(radj));\n      const ir = invN(radj); // r^-1\n      const u1 = modN(-h * ir); // -hr^-1\n      const u2 = modN(s * ir); // sr^-1\n      const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n      if (!Q) throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n      Q.assertValidity();\n      return Q;\n    }\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS() {\n      return isBiggerThanHalfOrder(this.s);\n    }\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n    }\n    // DER-encoded\n    toDERRawBytes() {\n      return ut.hexToBytes(this.toDERHex());\n    }\n    toDERHex() {\n      return DER.hexFromSig({\n        r: this.r,\n        s: this.s\n      });\n    }\n    // padded bytes of r, then padded bytes of s\n    toCompactRawBytes() {\n      return ut.hexToBytes(this.toCompactHex());\n    }\n    toCompactHex() {\n      return numToNByteStr(this.r) + numToNByteStr(this.s);\n    }\n  }\n  const utils = {\n    isValidPrivateKey(privateKey) {\n      try {\n        normPrivateKeyToScalar(privateKey);\n        return true;\n      } catch (error) {\n        return false;\n      }\n    },\n    normPrivateKeyToScalar: normPrivateKeyToScalar,\n    /**\n     * Produces cryptographically secure private key from random of size\n     * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n     */\n    randomPrivateKey: () => {\n      const length = mod.getMinHashLength(CURVE.n);\n      return mod.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n    },\n    /**\n     * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n     * Allows to massively speed-up `point.multiply(scalar)`.\n     * @returns cached point\n     * @example\n     * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n     * fast.multiply(privKey); // much faster ECDH now\n     */\n    precompute(windowSize = 8, point = Point.BASE) {\n      point._setWindowSize(windowSize);\n      point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n      return point;\n    }\n  };\n  /**\n   * Computes public key for a private key. Checks for validity of the private key.\n   * @param privateKey private key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(privateKey, isCompressed = true) {\n    return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n  }\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item) {\n    const arr = item instanceof Uint8Array;\n    const str = typeof item === 'string';\n    const len = (arr || str) && item.length;\n    if (arr) return len === compressedLen || len === uncompressedLen;\n    if (str) return len === 2 * compressedLen || len === 2 * uncompressedLen;\n    if (item instanceof Point) return true;\n    return false;\n  }\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from private key and public key.\n   * Checks: 1) private key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param privateA private key\n   * @param publicB different public key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(privateA, publicB, isCompressed = true) {\n    if (isProbPub(privateA)) throw new Error('first arg must be private key');\n    if (!isProbPub(publicB)) throw new Error('second arg must be public key');\n    const b = Point.fromHex(publicB); // check for being on-curve\n    return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n  }\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int = CURVE.bits2int || function (bytes) {\n    // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n    // for some cases, since bytes.length * 8 is not actual bitLength.\n    const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n    const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n    return delta > 0 ? num >> BigInt(delta) : num;\n  };\n  const bits2int_modN = CURVE.bits2int_modN || function (bytes) {\n    return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n  };\n  // NOTE: pads output with zero as per spec\n  const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n  /**\n   * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n   */\n  function int2octets(num) {\n    if (typeof num !== 'bigint') throw new Error('bigint expected');\n    if (!(_0n <= num && num < ORDER_MASK)) throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n    // works with order, can have different size than numToField!\n    return ut.numberToBytesBE(num, CURVE.nByteLength);\n  }\n  // Steps A, D of RFC6979 3.2\n  // Creates RFC6979 seed; converts msg/privKey to numbers.\n  // Used only in sign, not in verify.\n  // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n  // Also it can be bigger for P224 + SHA256\n  function prepSig(msgHash, privateKey, opts = defaultSigOpts) {\n    if (['recovered', 'canonical'].some(k => k in opts)) throw new Error('sign() legacy options not supported');\n    const {\n      hash,\n      randomBytes\n    } = CURVE;\n    let {\n      lowS,\n      prehash,\n      extraEntropy: ent\n    } = opts; // generates low-s sigs by default\n    if (lowS == null) lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n    msgHash = ensureBytes('msgHash', msgHash);\n    if (prehash) msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(msgHash);\n    const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (ent != null) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    function k2sig(kBytes) {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n      if (!isWithinCurveOrder(k)) return; // Important: all mod() calls here must be done over N\n      const ik = invN(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n      const r = modN(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n      // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n      // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n      const s = modN(ik * modN(m + r * d)); // Not using blinding here\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = normalizeS(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery); // use normS, not s\n    }\n    return {\n      seed,\n      k2sig\n    };\n  }\n  const defaultSigOpts = {\n    lowS: CURVE.lowS,\n    prehash: false\n  };\n  const defaultVerOpts = {\n    lowS: CURVE.lowS,\n    prehash: false\n  };\n  /**\n   * Signs message hash with a private key.\n   * ```\n   * sign(m, d, k) where\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr)/k mod n\n   * ```\n   * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n   * @param privKey private key\n   * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n   * @returns signature with recovery param\n   */\n  function sign(msgHash, privKey, opts = defaultSigOpts) {\n    const {\n      seed,\n      k2sig\n    } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n    const C = CURVE;\n    const drbg = ut.createHmacDrbg(C.hash.outputLen, C.nByteLength, C.hmac);\n    return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n  }\n  // Enable precomputes. Slows down first publicKey computation by 20ms.\n  Point.BASE._setWindowSize(8);\n  // utils.precompute(8, ProjectivePoint.BASE)\n  /**\n   * Verifies a signature against message hash and public key.\n   * Rejects lowS signatures by default: to override,\n   * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   U1 = hs^-1 mod n\n   *   U2 = rs^-1 mod n\n   *   R = U1⋅G - U2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(signature, msgHash, publicKey, opts = defaultVerOpts) {\n    const sg = signature;\n    msgHash = ensureBytes('msgHash', msgHash);\n    publicKey = ensureBytes('publicKey', publicKey);\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n    const {\n      lowS,\n      prehash\n    } = opts;\n    let _sig = undefined;\n    let P;\n    try {\n      if (typeof sg === 'string' || sg instanceof Uint8Array) {\n        // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n        // Since DER can also be 2*nByteLength bytes, we check for it first.\n        try {\n          _sig = Signature.fromDER(sg);\n        } catch (derError) {\n          if (!(derError instanceof DER.Err)) throw derError;\n          _sig = Signature.fromCompact(sg);\n        }\n      } else if (typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint') {\n        const {\n          r,\n          s\n        } = sg;\n        _sig = new Signature(r, s);\n      } else {\n        throw new Error('PARSE');\n      }\n      P = Point.fromHex(publicKey);\n    } catch (error) {\n      if (error.message === 'PARSE') throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n      return false;\n    }\n    if (lowS && _sig.hasHighS()) return false;\n    if (prehash) msgHash = CURVE.hash(msgHash);\n    const {\n      r,\n      s\n    } = _sig;\n    const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n    const is = invN(s); // s^-1\n    const u1 = modN(h * is); // u1 = hs^-1 mod n\n    const u2 = modN(r * is); // u2 = rs^-1 mod n\n    const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n    if (!R) return false;\n    const v = modN(R.x);\n    return v === r;\n  }\n  return {\n    CURVE,\n    getPublicKey,\n    getSharedSecret,\n    sign,\n    verify,\n    ProjectivePoint: Point,\n    Signature,\n    utils\n  };\n}\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio(Fp, Z) {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << c1 - _1n - _1n;\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u, v) => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << tv5 - _1n; // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return {\n      isValid: isQR,\n      value: tv3\n    };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u, v) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return {\n        isValid: isQR,\n        value: y\n      }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU(Fp, opts) {\n  mod.validateField(Fp);\n  if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z)) throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n  if (!Fp.isOdd) throw new Error('Fp.isOdd is not implemented!');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return u => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const {\n      isValid,\n      value\n    } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd(u) === Fp.isOdd(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    x = Fp.div(x, tv4); // 25.   x = x / tv4\n    return {\n      x,\n      y\n    };\n  };\n}", "map": {"version": 3, "names": ["mod", "ut", "ensureBytes", "wNAF", "validateBasic", "validatePointOpts", "curve", "opts", "validateObject", "a", "b", "allowedPrivateKeyLengths", "wrapPrivateKey", "isTorsionFree", "clearCofactor", "allowInfinityPoint", "fromBytes", "toBytes", "endo", "Fp", "eql", "ZERO", "Error", "beta", "splitScalar", "Object", "freeze", "bytesToNumberBE", "b2n", "hexToBytes", "h2b", "DER", "Err", "DERErr", "constructor", "m", "_parseInt", "data", "E", "length", "len", "res", "subarray", "d", "l", "to<PERSON><PERSON>", "hex", "Uint8Array", "r", "sBytes", "s", "rBytesLeft", "hexFromSig", "sig", "slice", "Number", "parseInt", "h", "num", "toString", "shl", "rhl", "sl", "rl", "_0n", "BigInt", "_1n", "_2n", "_3n", "_4n", "weierstrassPoints", "CURVE", "_c", "point", "_isCompressed", "toAffine", "concatBytes", "from", "x", "y", "bytes", "tail", "BYTES", "weierstrassEquation", "x2", "sqr", "x3", "mul", "add", "Gy", "Gx", "isWithinCurveOrder", "n", "assertGE", "normPrivateKeyToScalar", "key", "lengths", "nByteLength", "bytesToHex", "includes", "padStart", "error", "pointPrecomputes", "Map", "assertPrjPoint", "other", "Point", "px", "py", "pz", "<PERSON><PERSON><PERSON><PERSON>", "fromAffine", "p", "is0", "i", "ONE", "normalizeZ", "points", "toInv", "invertBatch", "map", "fromHex", "P", "assertValidity", "fromPrivateKey", "privateKey", "BASE", "multiply", "_setWindowSize", "windowSize", "_WINDOW_SIZE", "delete", "left", "right", "hasEvenY", "isOdd", "equals", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "U1", "U2", "negate", "neg", "double", "b3", "X3", "Y3", "Z3", "t0", "t1", "t2", "t3", "sub", "t4", "t5", "subtract", "wnaf", "wNAFCached", "comp", "multiplyUnsafe", "I", "unsafeL<PERSON>der", "k1neg", "k1", "k2neg", "k2", "k1p", "k2p", "scalar", "fake", "f", "f1p", "f2p", "constTimeNegate", "multiplyAndAddUnsafe", "Q", "G", "sum", "undefined", "iz", "z", "inv", "ax", "ay", "zz", "cofactor", "toRawBytes", "isCompressed", "toHex", "_bits", "nBitLength", "Math", "ceil", "ProjectivePoint", "validateOpts", "hash", "hmac", "randomBytes", "bits2int", "bits2int_modN", "lowS", "<PERSON><PERSON><PERSON><PERSON>", "curveDef", "CURVE_ORDER", "compressedLen", "uncompressedLen", "isValidFieldElement", "ORDER", "modN", "invN", "invert", "cat", "head", "y2", "sqrt", "isYOdd", "isHeadOdd", "numToNByteStr", "numberToBytesBE", "isBiggerThanHalfOrder", "number", "HALF", "normalizeS", "slcNum", "to", "Signature", "recovery", "fromCompact", "fromDER", "addRecoveryBit", "recoverPublicKey", "msgHash", "rec", "radj", "prefix", "R", "ir", "u1", "u2", "hasHighS", "toDERRawBytes", "toDERHex", "toCompactRawBytes", "toCompactHex", "utils", "isValidPrivateKey", "randomPrivateKey", "get<PERSON>in<PERSON>ash<PERSON><PERSON><PERSON>", "mapHashToField", "precompute", "getPublicKey", "isProbPub", "item", "arr", "str", "getSharedSecret", "privateA", "publicB", "delta", "ORDER_MASK", "bitMask", "int2octets", "prepSig", "defaultSigOpts", "some", "k", "prehash", "extraEntropy", "ent", "h1int", "seedArgs", "e", "push", "seed", "k2sig", "kBytes", "ik", "q", "normS", "defaultVerOpts", "sign", "privKey", "C", "drbg", "createHmacDrbg", "outputLen", "verify", "signature", "public<PERSON>ey", "sg", "_sig", "<PERSON><PERSON><PERSON><PERSON>", "message", "is", "v", "SWUFpSqrtRatio", "Z", "o", "c1", "_2n_pow_c1_1", "_2n_pow_c1", "c2", "c3", "c4", "c5", "c6", "pow", "c7", "sqrtRatio", "u", "tv1", "tv2", "tv3", "tv5", "tv4", "isQR", "cmov", "tvv5", "e1", "value", "y1", "mapToCurveSimpleSWU", "validateField", "A", "B", "tv6", "div"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\abstract\\weierstrass.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Short <PERSON> curve. The formula is: y² = x³ + ax + b\nimport * as mod from './modular.js';\nimport * as ut from './utils.js';\nimport { <PERSON><PERSON>, Hex, Priv<PERSON><PERSON>, ensureBytes } from './utils.js';\nimport { Group, GroupConstructor, wNAF, BasicCurve, validateBasic, AffinePoint } from './curve.js';\n\nexport type { AffinePoint };\ntype HmacFnSync = (key: Uint8Array, ...messages: Uint8Array[]) => Uint8Array;\ntype EndomorphismOpts = {\n  beta: bigint;\n  splitScalar: (k: bigint) => { k1neg: boolean; k1: bigint; k2neg: boolean; k2: bigint };\n};\nexport type BasicWCurve<T> = BasicCurve<T> & {\n  // Params: a, b\n  a: T;\n  b: T;\n\n  // Optional params\n  allowedPrivateKeyLengths?: readonly number[]; // for P521\n  wrapPrivateKey?: boolean; // bls12-381 requires mod(n) instead of rejecting keys >= n\n  endo?: EndomorphismOpts; // Endomorphism options for Koblitz curves\n  // When a cofactor != 1, there can be an effective methods to:\n  // 1. Determine whether a point is torsion-free\n  isTorsionFree?: (c: ProjConstructor<T>, point: ProjPointType<T>) => boolean;\n  // 2. Clear torsion component\n  clearCofactor?: (c: ProjConstructor<T>, point: ProjPointType<T>) => ProjPointType<T>;\n};\n\ntype Entropy = Hex | true;\nexport type SignOpts = { lowS?: boolean; extraEntropy?: Entropy; prehash?: boolean };\nexport type VerOpts = { lowS?: boolean; prehash?: boolean };\n\n/**\n * ### Design rationale for types\n *\n * * Interaction between classes from different curves should fail:\n *   `k256.Point.BASE.add(p256.Point.BASE)`\n * * For this purpose we want to use `instanceof` operator, which is fast and works during runtime\n * * Different calls of `curve()` would return different classes -\n *   `curve(params) !== curve(params)`: if somebody decided to monkey-patch their curve,\n *   it won't affect others\n *\n * TypeScript can't infer types for classes created inside a function. Classes is one instance of nominative types in TypeScript and interfaces only check for shape, so it's hard to create unique type for every function call.\n *\n * We can use generic types via some param, like curve opts, but that would:\n *     1. Enable interaction between `curve(params)` and `curve(params)` (curves of same params)\n *     which is hard to debug.\n *     2. Params can be generic and we can't enforce them to be constant value:\n *     if somebody creates curve from non-constant params,\n *     it would be allowed to interact with other curves with non-constant params\n *\n * TODO: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-7.html#unique-symbol\n */\n\n// Instance for 3d XYZ points\nexport interface ProjPointType<T> extends Group<ProjPointType<T>> {\n  readonly px: T;\n  readonly py: T;\n  readonly pz: T;\n  get x(): T;\n  get y(): T;\n  multiply(scalar: bigint): ProjPointType<T>;\n  toAffine(iz?: T): AffinePoint<T>;\n  isTorsionFree(): boolean;\n  clearCofactor(): ProjPointType<T>;\n  assertValidity(): void;\n  hasEvenY(): boolean;\n  toRawBytes(isCompressed?: boolean): Uint8Array;\n  toHex(isCompressed?: boolean): string;\n\n  multiplyUnsafe(scalar: bigint): ProjPointType<T>;\n  multiplyAndAddUnsafe(Q: ProjPointType<T>, a: bigint, b: bigint): ProjPointType<T> | undefined;\n  _setWindowSize(windowSize: number): void;\n}\n// Static methods for 3d XYZ points\nexport interface ProjConstructor<T> extends GroupConstructor<ProjPointType<T>> {\n  new (x: T, y: T, z: T): ProjPointType<T>;\n  fromAffine(p: AffinePoint<T>): ProjPointType<T>;\n  fromHex(hex: Hex): ProjPointType<T>;\n  fromPrivateKey(privateKey: PrivKey): ProjPointType<T>;\n  normalizeZ(points: ProjPointType<T>[]): ProjPointType<T>[];\n}\n\nexport type CurvePointsType<T> = BasicWCurve<T> & {\n  // Bytes\n  fromBytes?: (bytes: Uint8Array) => AffinePoint<T>;\n  toBytes?: (c: ProjConstructor<T>, point: ProjPointType<T>, isCompressed: boolean) => Uint8Array;\n};\n\nfunction validatePointOpts<T>(curve: CurvePointsType<T>) {\n  const opts = validateBasic(curve);\n  ut.validateObject(\n    opts,\n    {\n      a: 'field',\n      b: 'field',\n    },\n    {\n      allowedPrivateKeyLengths: 'array',\n      wrapPrivateKey: 'boolean',\n      isTorsionFree: 'function',\n      clearCofactor: 'function',\n      allowInfinityPoint: 'boolean',\n      fromBytes: 'function',\n      toBytes: 'function',\n    }\n  );\n  const { endo, Fp, a } = opts;\n  if (endo) {\n    if (!Fp.eql(a, Fp.ZERO)) {\n      throw new Error('Endomorphism can only be defined for Koblitz curves that have a=0');\n    }\n    if (\n      typeof endo !== 'object' ||\n      typeof endo.beta !== 'bigint' ||\n      typeof endo.splitScalar !== 'function'\n    ) {\n      throw new Error('Expected endomorphism with beta: bigint and splitScalar: function');\n    }\n  }\n  return Object.freeze({ ...opts } as const);\n}\n\nexport type CurvePointsRes<T> = {\n  ProjectivePoint: ProjConstructor<T>;\n  normPrivateKeyToScalar: (key: PrivKey) => bigint;\n  weierstrassEquation: (x: T) => T;\n  isWithinCurveOrder: (num: bigint) => boolean;\n};\n\n// ASN.1 DER encoding utilities\nconst { bytesToNumberBE: b2n, hexToBytes: h2b } = ut;\nexport const DER = {\n  // asn.1 DER encoding utils\n  Err: class DERErr extends Error {\n    constructor(m = '') {\n      super(m);\n    }\n  },\n  _parseInt(data: Uint8Array): { d: bigint; l: Uint8Array } {\n    const { Err: E } = DER;\n    if (data.length < 2 || data[0] !== 0x02) throw new E('Invalid signature integer tag');\n    const len = data[1];\n    const res = data.subarray(2, len + 2);\n    if (!len || res.length !== len) throw new E('Invalid signature integer: wrong length');\n    // https://crypto.stackexchange.com/a/57734 Leftmost bit of first byte is 'negative' flag,\n    // since we always use positive integers here. It must always be empty:\n    // - add zero byte if exists\n    // - if next byte doesn't have a flag, leading zero is not allowed (minimal encoding)\n    if (res[0] & 0b10000000) throw new E('Invalid signature integer: negative');\n    if (res[0] === 0x00 && !(res[1] & 0b10000000))\n      throw new E('Invalid signature integer: unnecessary leading zero');\n    return { d: b2n(res), l: data.subarray(len + 2) }; // d is data, l is left\n  },\n  toSig(hex: string | Uint8Array): { r: bigint; s: bigint } {\n    // parse DER signature\n    const { Err: E } = DER;\n    const data = typeof hex === 'string' ? h2b(hex) : hex;\n    if (!(data instanceof Uint8Array)) throw new Error('ui8a expected');\n    let l = data.length;\n    if (l < 2 || data[0] != 0x30) throw new E('Invalid signature tag');\n    if (data[1] !== l - 2) throw new E('Invalid signature: incorrect length');\n    const { d: r, l: sBytes } = DER._parseInt(data.subarray(2));\n    const { d: s, l: rBytesLeft } = DER._parseInt(sBytes);\n    if (rBytesLeft.length) throw new E('Invalid signature: left bytes after parsing');\n    return { r, s };\n  },\n  hexFromSig(sig: { r: bigint; s: bigint }): string {\n    // Add leading zero if first byte has negative bit enabled. More details in '_parseInt'\n    const slice = (s: string): string => (Number.parseInt(s[0], 16) & 0b1000 ? '00' + s : s);\n    const h = (num: number | bigint) => {\n      const hex = num.toString(16);\n      return hex.length & 1 ? `0${hex}` : hex;\n    };\n    const s = slice(h(sig.s));\n    const r = slice(h(sig.r));\n    const shl = s.length / 2;\n    const rhl = r.length / 2;\n    const sl = h(shl);\n    const rl = h(rhl);\n    return `30${h(rhl + shl + 4)}02${rl}${r}02${sl}${s}`;\n  },\n};\n\n// Be friendly to bad ECMAScript parsers by not using bigint literals\n// prettier-ignore\nconst _0n = BigInt(0), _1n = BigInt(1), _2n = BigInt(2), _3n = BigInt(3), _4n = BigInt(4);\n\nexport function weierstrassPoints<T>(opts: CurvePointsType<T>) {\n  const CURVE = validatePointOpts(opts);\n  const { Fp } = CURVE; // All curves has same field / group length as for now, but they can differ\n\n  const toBytes =\n    CURVE.toBytes ||\n    ((_c: ProjConstructor<T>, point: ProjPointType<T>, _isCompressed: boolean) => {\n      const a = point.toAffine();\n      return ut.concatBytes(Uint8Array.from([0x04]), Fp.toBytes(a.x), Fp.toBytes(a.y));\n    });\n  const fromBytes =\n    CURVE.fromBytes ||\n    ((bytes: Uint8Array) => {\n      // const head = bytes[0];\n      const tail = bytes.subarray(1);\n      // if (head !== 0x04) throw new Error('Only non-compressed encoding is supported');\n      const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n      const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n      return { x, y };\n    });\n\n  /**\n   * y² = x³ + ax + b: Short weierstrass curve formula\n   * @returns y²\n   */\n  function weierstrassEquation(x: T): T {\n    const { a, b } = CURVE;\n    const x2 = Fp.sqr(x); // x * x\n    const x3 = Fp.mul(x2, x); // x2 * x\n    return Fp.add(Fp.add(x3, Fp.mul(x, a)), b); // x3 + a * x + b\n  }\n  // Validate whether the passed curve params are valid.\n  // We check if curve equation works for generator point.\n  // `assertValidity()` won't work: `isTorsionFree()` is not available at this point in bls12-381.\n  // ProjectivePoint class has not been initialized yet.\n  if (!Fp.eql(Fp.sqr(CURVE.Gy), weierstrassEquation(CURVE.Gx)))\n    throw new Error('bad generator point: equation left != right');\n\n  // Valid group elements reside in range 1..n-1\n  function isWithinCurveOrder(num: bigint): boolean {\n    return typeof num === 'bigint' && _0n < num && num < CURVE.n;\n  }\n  function assertGE(num: bigint) {\n    if (!isWithinCurveOrder(num)) throw new Error('Expected valid bigint: 0 < bigint < curve.n');\n  }\n  // Validates if priv key is valid and converts it to bigint.\n  // Supports options allowedPrivateKeyLengths and wrapPrivateKey.\n  function normPrivateKeyToScalar(key: PrivKey): bigint {\n    const { allowedPrivateKeyLengths: lengths, nByteLength, wrapPrivateKey, n } = CURVE;\n    if (lengths && typeof key !== 'bigint') {\n      if (key instanceof Uint8Array) key = ut.bytesToHex(key);\n      // Normalize to hex string, pad. E.g. P521 would norm 130-132 char hex to 132-char bytes\n      if (typeof key !== 'string' || !lengths.includes(key.length)) throw new Error('Invalid key');\n      key = key.padStart(nByteLength * 2, '0');\n    }\n    let num: bigint;\n    try {\n      num =\n        typeof key === 'bigint'\n          ? key\n          : ut.bytesToNumberBE(ensureBytes('private key', key, nByteLength));\n    } catch (error) {\n      throw new Error(`private key must be ${nByteLength} bytes, hex or bigint, not ${typeof key}`);\n    }\n    if (wrapPrivateKey) num = mod.mod(num, n); // disabled by default, enabled for BLS\n    assertGE(num); // num in range [1..N-1]\n    return num;\n  }\n\n  const pointPrecomputes = new Map<Point, Point[]>();\n  function assertPrjPoint(other: unknown) {\n    if (!(other instanceof Point)) throw new Error('ProjectivePoint expected');\n  }\n  /**\n   * Projective Point works in 3d / projective (homogeneous) coordinates: (x, y, z) ∋ (x=x/z, y=y/z)\n   * Default Point works in 2d / affine coordinates: (x, y)\n   * We're doing calculations in projective, because its operations don't require costly inversion.\n   */\n  class Point implements ProjPointType<T> {\n    static readonly BASE = new Point(CURVE.Gx, CURVE.Gy, Fp.ONE);\n    static readonly ZERO = new Point(Fp.ZERO, Fp.ONE, Fp.ZERO);\n\n    constructor(readonly px: T, readonly py: T, readonly pz: T) {\n      if (px == null || !Fp.isValid(px)) throw new Error('x required');\n      if (py == null || !Fp.isValid(py)) throw new Error('y required');\n      if (pz == null || !Fp.isValid(pz)) throw new Error('z required');\n    }\n\n    // Does not validate if the point is on-curve.\n    // Use fromHex instead, or call assertValidity() later.\n    static fromAffine(p: AffinePoint<T>): Point {\n      const { x, y } = p || {};\n      if (!p || !Fp.isValid(x) || !Fp.isValid(y)) throw new Error('invalid affine point');\n      if (p instanceof Point) throw new Error('projective point not allowed');\n      const is0 = (i: T) => Fp.eql(i, Fp.ZERO);\n      // fromAffine(x:0, y:0) would produce (x:0, y:0, z:1), but we need (x:0, y:1, z:0)\n      if (is0(x) && is0(y)) return Point.ZERO;\n      return new Point(x, y, Fp.ONE);\n    }\n\n    get x(): T {\n      return this.toAffine().x;\n    }\n    get y(): T {\n      return this.toAffine().y;\n    }\n\n    /**\n     * Takes a bunch of Projective Points but executes only one\n     * inversion on all of them. Inversion is very slow operation,\n     * so this improves performance massively.\n     * Optimization: converts a list of projective points to a list of identical points with Z=1.\n     */\n    static normalizeZ(points: Point[]): Point[] {\n      const toInv = Fp.invertBatch(points.map((p) => p.pz));\n      return points.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n    }\n\n    /**\n     * Converts hash string or Uint8Array to Point.\n     * @param hex short/long ECDSA hex\n     */\n    static fromHex(hex: Hex): Point {\n      const P = Point.fromAffine(fromBytes(ensureBytes('pointHex', hex)));\n      P.assertValidity();\n      return P;\n    }\n\n    // Multiplies generator point by privateKey.\n    static fromPrivateKey(privateKey: PrivKey) {\n      return Point.BASE.multiply(normPrivateKeyToScalar(privateKey));\n    }\n\n    // We calculate precomputes for elliptic curve point multiplication\n    // using windowed method. This specifies window size and\n    // stores precomputed values. Usually only base point would be precomputed.\n    _WINDOW_SIZE?: number;\n\n    // \"Private method\", don't use it directly\n    _setWindowSize(windowSize: number) {\n      this._WINDOW_SIZE = windowSize;\n      pointPrecomputes.delete(this);\n    }\n\n    // A point on curve is valid if it conforms to equation.\n    assertValidity(): void {\n      if (this.is0()) {\n        // (0, 1, 0) aka ZERO is invalid in most contexts.\n        // In BLS, ZERO can be serialized, so we allow it.\n        // (0, 0, 0) is wrong representation of ZERO and is always invalid.\n        if (CURVE.allowInfinityPoint && !Fp.is0(this.py)) return;\n        throw new Error('bad point: ZERO');\n      }\n      // Some 3rd-party test vectors require different wording between here & `fromCompressedHex`\n      const { x, y } = this.toAffine();\n      // Check if x, y are valid field elements\n      if (!Fp.isValid(x) || !Fp.isValid(y)) throw new Error('bad point: x or y not FE');\n      const left = Fp.sqr(y); // y²\n      const right = weierstrassEquation(x); // x³ + ax + b\n      if (!Fp.eql(left, right)) throw new Error('bad point: equation left != right');\n      if (!this.isTorsionFree()) throw new Error('bad point: not in prime-order subgroup');\n    }\n    hasEvenY(): boolean {\n      const { y } = this.toAffine();\n      if (Fp.isOdd) return !Fp.isOdd(y);\n      throw new Error(\"Field doesn't support isOdd\");\n    }\n\n    /**\n     * Compare one point to another.\n     */\n    equals(other: Point): boolean {\n      assertPrjPoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      const U1 = Fp.eql(Fp.mul(X1, Z2), Fp.mul(X2, Z1));\n      const U2 = Fp.eql(Fp.mul(Y1, Z2), Fp.mul(Y2, Z1));\n      return U1 && U2;\n    }\n\n    /**\n     * Flips point to one corresponding to (x, -y) in Affine coordinates.\n     */\n    negate(): Point {\n      return new Point(this.px, Fp.neg(this.py), this.pz);\n    }\n\n    // Renes-Costello-Batina exception-free doubling formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 3\n    // Cost: 8M + 3S + 3*a + 2*b3 + 15add.\n    double() {\n      const { a, b } = CURVE;\n      const b3 = Fp.mul(b, _3n);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      let t0 = Fp.mul(X1, X1); // step 1\n      let t1 = Fp.mul(Y1, Y1);\n      let t2 = Fp.mul(Z1, Z1);\n      let t3 = Fp.mul(X1, Y1);\n      t3 = Fp.add(t3, t3); // step 5\n      Z3 = Fp.mul(X1, Z1);\n      Z3 = Fp.add(Z3, Z3);\n      X3 = Fp.mul(a, Z3);\n      Y3 = Fp.mul(b3, t2);\n      Y3 = Fp.add(X3, Y3); // step 10\n      X3 = Fp.sub(t1, Y3);\n      Y3 = Fp.add(t1, Y3);\n      Y3 = Fp.mul(X3, Y3);\n      X3 = Fp.mul(t3, X3);\n      Z3 = Fp.mul(b3, Z3); // step 15\n      t2 = Fp.mul(a, t2);\n      t3 = Fp.sub(t0, t2);\n      t3 = Fp.mul(a, t3);\n      t3 = Fp.add(t3, Z3);\n      Z3 = Fp.add(t0, t0); // step 20\n      t0 = Fp.add(Z3, t0);\n      t0 = Fp.add(t0, t2);\n      t0 = Fp.mul(t0, t3);\n      Y3 = Fp.add(Y3, t0);\n      t2 = Fp.mul(Y1, Z1); // step 25\n      t2 = Fp.add(t2, t2);\n      t0 = Fp.mul(t2, t3);\n      X3 = Fp.sub(X3, t0);\n      Z3 = Fp.mul(t2, t1);\n      Z3 = Fp.add(Z3, Z3); // step 30\n      Z3 = Fp.add(Z3, Z3);\n      return new Point(X3, Y3, Z3);\n    }\n\n    // Renes-Costello-Batina exception-free addition formula.\n    // There is 30% faster Jacobian formula, but it is not complete.\n    // https://eprint.iacr.org/2015/1060, algorithm 1\n    // Cost: 12M + 0S + 3*a + 3*b3 + 23add.\n    add(other: Point): Point {\n      assertPrjPoint(other);\n      const { px: X1, py: Y1, pz: Z1 } = this;\n      const { px: X2, py: Y2, pz: Z2 } = other;\n      let X3 = Fp.ZERO, Y3 = Fp.ZERO, Z3 = Fp.ZERO; // prettier-ignore\n      const a = CURVE.a;\n      const b3 = Fp.mul(CURVE.b, _3n);\n      let t0 = Fp.mul(X1, X2); // step 1\n      let t1 = Fp.mul(Y1, Y2);\n      let t2 = Fp.mul(Z1, Z2);\n      let t3 = Fp.add(X1, Y1);\n      let t4 = Fp.add(X2, Y2); // step 5\n      t3 = Fp.mul(t3, t4);\n      t4 = Fp.add(t0, t1);\n      t3 = Fp.sub(t3, t4);\n      t4 = Fp.add(X1, Z1);\n      let t5 = Fp.add(X2, Z2); // step 10\n      t4 = Fp.mul(t4, t5);\n      t5 = Fp.add(t0, t2);\n      t4 = Fp.sub(t4, t5);\n      t5 = Fp.add(Y1, Z1);\n      X3 = Fp.add(Y2, Z2); // step 15\n      t5 = Fp.mul(t5, X3);\n      X3 = Fp.add(t1, t2);\n      t5 = Fp.sub(t5, X3);\n      Z3 = Fp.mul(a, t4);\n      X3 = Fp.mul(b3, t2); // step 20\n      Z3 = Fp.add(X3, Z3);\n      X3 = Fp.sub(t1, Z3);\n      Z3 = Fp.add(t1, Z3);\n      Y3 = Fp.mul(X3, Z3);\n      t1 = Fp.add(t0, t0); // step 25\n      t1 = Fp.add(t1, t0);\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.mul(b3, t4);\n      t1 = Fp.add(t1, t2);\n      t2 = Fp.sub(t0, t2); // step 30\n      t2 = Fp.mul(a, t2);\n      t4 = Fp.add(t4, t2);\n      t0 = Fp.mul(t1, t4);\n      Y3 = Fp.add(Y3, t0);\n      t0 = Fp.mul(t5, t4); // step 35\n      X3 = Fp.mul(t3, X3);\n      X3 = Fp.sub(X3, t0);\n      t0 = Fp.mul(t3, t1);\n      Z3 = Fp.mul(t5, Z3);\n      Z3 = Fp.add(Z3, t0); // step 40\n      return new Point(X3, Y3, Z3);\n    }\n\n    subtract(other: Point) {\n      return this.add(other.negate());\n    }\n\n    private is0() {\n      return this.equals(Point.ZERO);\n    }\n    private wNAF(n: bigint): { p: Point; f: Point } {\n      return wnaf.wNAFCached(this, pointPrecomputes, n, (comp: Point[]) => {\n        const toInv = Fp.invertBatch(comp.map((p) => p.pz));\n        return comp.map((p, i) => p.toAffine(toInv[i])).map(Point.fromAffine);\n      });\n    }\n\n    /**\n     * Non-constant-time multiplication. Uses double-and-add algorithm.\n     * It's faster, but should only be used when you don't care about\n     * an exposed private key e.g. sig verification, which works over *public* keys.\n     */\n    multiplyUnsafe(n: bigint): Point {\n      const I = Point.ZERO;\n      if (n === _0n) return I;\n      assertGE(n); // Will throw on 0\n      if (n === _1n) return this;\n      const { endo } = CURVE;\n      if (!endo) return wnaf.unsafeLadder(this, n);\n\n      // Apply endomorphism\n      let { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n      let k1p = I;\n      let k2p = I;\n      let d: Point = this;\n      while (k1 > _0n || k2 > _0n) {\n        if (k1 & _1n) k1p = k1p.add(d);\n        if (k2 & _1n) k2p = k2p.add(d);\n        d = d.double();\n        k1 >>= _1n;\n        k2 >>= _1n;\n      }\n      if (k1neg) k1p = k1p.negate();\n      if (k2neg) k2p = k2p.negate();\n      k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n      return k1p.add(k2p);\n    }\n\n    /**\n     * Constant time multiplication.\n     * Uses wNAF method. Windowed method may be 10% faster,\n     * but takes 2x longer to generate and consumes 2x memory.\n     * Uses precomputes when available.\n     * Uses endomorphism for Koblitz curves.\n     * @param scalar by which the point would be multiplied\n     * @returns New point\n     */\n    multiply(scalar: bigint): Point {\n      assertGE(scalar);\n      let n = scalar;\n      let point: Point, fake: Point; // Fake point is used to const-time mult\n      const { endo } = CURVE;\n      if (endo) {\n        const { k1neg, k1, k2neg, k2 } = endo.splitScalar(n);\n        let { p: k1p, f: f1p } = this.wNAF(k1);\n        let { p: k2p, f: f2p } = this.wNAF(k2);\n        k1p = wnaf.constTimeNegate(k1neg, k1p);\n        k2p = wnaf.constTimeNegate(k2neg, k2p);\n        k2p = new Point(Fp.mul(k2p.px, endo.beta), k2p.py, k2p.pz);\n        point = k1p.add(k2p);\n        fake = f1p.add(f2p);\n      } else {\n        const { p, f } = this.wNAF(n);\n        point = p;\n        fake = f;\n      }\n      // Normalize `z` for both points, but return only real one\n      return Point.normalizeZ([point, fake])[0];\n    }\n\n    /**\n     * Efficiently calculate `aP + bQ`. Unsafe, can expose private key, if used incorrectly.\n     * Not using Strauss-Shamir trick: precomputation tables are faster.\n     * The trick could be useful if both P and Q are not G (not in our case).\n     * @returns non-zero affine point\n     */\n    multiplyAndAddUnsafe(Q: Point, a: bigint, b: bigint): Point | undefined {\n      const G = Point.BASE; // No Strauss-Shamir trick: we have 10% faster G precomputes\n      const mul = (\n        P: Point,\n        a: bigint // Select faster multiply() method\n      ) => (a === _0n || a === _1n || !P.equals(G) ? P.multiplyUnsafe(a) : P.multiply(a));\n      const sum = mul(this, a).add(mul(Q, b));\n      return sum.is0() ? undefined : sum;\n    }\n\n    // Converts Projective point to affine (x, y) coordinates.\n    // Can accept precomputed Z^-1 - for example, from invertBatch.\n    // (x, y, z) ∋ (x=x/z, y=y/z)\n    toAffine(iz?: T): AffinePoint<T> {\n      const { px: x, py: y, pz: z } = this;\n      const is0 = this.is0();\n      // If invZ was 0, we return zero point. However we still want to execute\n      // all operations, so we replace invZ with a random number, 1.\n      if (iz == null) iz = is0 ? Fp.ONE : Fp.inv(z);\n      const ax = Fp.mul(x, iz);\n      const ay = Fp.mul(y, iz);\n      const zz = Fp.mul(z, iz);\n      if (is0) return { x: Fp.ZERO, y: Fp.ZERO };\n      if (!Fp.eql(zz, Fp.ONE)) throw new Error('invZ was invalid');\n      return { x: ax, y: ay };\n    }\n    isTorsionFree(): boolean {\n      const { h: cofactor, isTorsionFree } = CURVE;\n      if (cofactor === _1n) return true; // No subgroups, always torsion-free\n      if (isTorsionFree) return isTorsionFree(Point, this);\n      throw new Error('isTorsionFree() has not been declared for the elliptic curve');\n    }\n    clearCofactor(): Point {\n      const { h: cofactor, clearCofactor } = CURVE;\n      if (cofactor === _1n) return this; // Fast-path\n      if (clearCofactor) return clearCofactor(Point, this) as Point;\n      return this.multiplyUnsafe(CURVE.h);\n    }\n\n    toRawBytes(isCompressed = true): Uint8Array {\n      this.assertValidity();\n      return toBytes(Point, this, isCompressed);\n    }\n\n    toHex(isCompressed = true): string {\n      return ut.bytesToHex(this.toRawBytes(isCompressed));\n    }\n  }\n  const _bits = CURVE.nBitLength;\n  const wnaf = wNAF(Point, CURVE.endo ? Math.ceil(_bits / 2) : _bits);\n  // Validate if generator point is on curve\n  return {\n    CURVE,\n    ProjectivePoint: Point as ProjConstructor<T>,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder,\n  };\n}\n\n// Instance\nexport interface SignatureType {\n  readonly r: bigint;\n  readonly s: bigint;\n  readonly recovery?: number;\n  assertValidity(): void;\n  addRecoveryBit(recovery: number): RecoveredSignatureType;\n  hasHighS(): boolean;\n  normalizeS(): SignatureType;\n  recoverPublicKey(msgHash: Hex): ProjPointType<bigint>;\n  toCompactRawBytes(): Uint8Array;\n  toCompactHex(): string;\n  // DER-encoded\n  toDERRawBytes(isCompressed?: boolean): Uint8Array;\n  toDERHex(isCompressed?: boolean): string;\n}\nexport type RecoveredSignatureType = SignatureType & {\n  readonly recovery: number;\n};\n// Static methods\nexport type SignatureConstructor = {\n  new (r: bigint, s: bigint): SignatureType;\n  fromCompact(hex: Hex): SignatureType;\n  fromDER(hex: Hex): SignatureType;\n};\ntype SignatureLike = { r: bigint; s: bigint };\n\nexport type PubKey = Hex | ProjPointType<bigint>;\n\nexport type CurveType = BasicWCurve<bigint> & {\n  hash: CHash; // CHash not FHash because we need outputLen for DRBG\n  hmac: HmacFnSync;\n  randomBytes: (bytesLength?: number) => Uint8Array;\n  lowS?: boolean;\n  bits2int?: (bytes: Uint8Array) => bigint;\n  bits2int_modN?: (bytes: Uint8Array) => bigint;\n};\n\nfunction validateOpts(curve: CurveType) {\n  const opts = validateBasic(curve);\n  ut.validateObject(\n    opts,\n    {\n      hash: 'hash',\n      hmac: 'function',\n      randomBytes: 'function',\n    },\n    {\n      bits2int: 'function',\n      bits2int_modN: 'function',\n      lowS: 'boolean',\n    }\n  );\n  return Object.freeze({ lowS: true, ...opts } as const);\n}\n\nexport type CurveFn = {\n  CURVE: ReturnType<typeof validateOpts>;\n  getPublicKey: (privateKey: PrivKey, isCompressed?: boolean) => Uint8Array;\n  getSharedSecret: (privateA: PrivKey, publicB: Hex, isCompressed?: boolean) => Uint8Array;\n  sign: (msgHash: Hex, privKey: PrivKey, opts?: SignOpts) => RecoveredSignatureType;\n  verify: (signature: Hex | SignatureLike, msgHash: Hex, publicKey: Hex, opts?: VerOpts) => boolean;\n  ProjectivePoint: ProjConstructor<bigint>;\n  Signature: SignatureConstructor;\n  utils: {\n    normPrivateKeyToScalar: (key: PrivKey) => bigint;\n    isValidPrivateKey(privateKey: PrivKey): boolean;\n    randomPrivateKey: () => Uint8Array;\n    precompute: (windowSize?: number, point?: ProjPointType<bigint>) => ProjPointType<bigint>;\n  };\n};\n\nexport function weierstrass(curveDef: CurveType): CurveFn {\n  const CURVE = validateOpts(curveDef) as ReturnType<typeof validateOpts>;\n  const { Fp, n: CURVE_ORDER } = CURVE;\n  const compressedLen = Fp.BYTES + 1; // e.g. 33 for 32\n  const uncompressedLen = 2 * Fp.BYTES + 1; // e.g. 65 for 32\n\n  function isValidFieldElement(num: bigint): boolean {\n    return _0n < num && num < Fp.ORDER; // 0 is banned since it's not invertible FE\n  }\n  function modN(a: bigint) {\n    return mod.mod(a, CURVE_ORDER);\n  }\n  function invN(a: bigint) {\n    return mod.invert(a, CURVE_ORDER);\n  }\n\n  const {\n    ProjectivePoint: Point,\n    normPrivateKeyToScalar,\n    weierstrassEquation,\n    isWithinCurveOrder,\n  } = weierstrassPoints({\n    ...CURVE,\n    toBytes(_c, point, isCompressed: boolean): Uint8Array {\n      const a = point.toAffine();\n      const x = Fp.toBytes(a.x);\n      const cat = ut.concatBytes;\n      if (isCompressed) {\n        return cat(Uint8Array.from([point.hasEvenY() ? 0x02 : 0x03]), x);\n      } else {\n        return cat(Uint8Array.from([0x04]), x, Fp.toBytes(a.y));\n      }\n    },\n    fromBytes(bytes: Uint8Array) {\n      const len = bytes.length;\n      const head = bytes[0];\n      const tail = bytes.subarray(1);\n      // this.assertValidity() is done inside of fromHex\n      if (len === compressedLen && (head === 0x02 || head === 0x03)) {\n        const x = ut.bytesToNumberBE(tail);\n        if (!isValidFieldElement(x)) throw new Error('Point is not on curve');\n        const y2 = weierstrassEquation(x); // y² = x³ + ax + b\n        let y = Fp.sqrt(y2); // y = y² ^ (p+1)/4\n        const isYOdd = (y & _1n) === _1n;\n        // ECDSA\n        const isHeadOdd = (head & 1) === 1;\n        if (isHeadOdd !== isYOdd) y = Fp.neg(y);\n        return { x, y };\n      } else if (len === uncompressedLen && head === 0x04) {\n        const x = Fp.fromBytes(tail.subarray(0, Fp.BYTES));\n        const y = Fp.fromBytes(tail.subarray(Fp.BYTES, 2 * Fp.BYTES));\n        return { x, y };\n      } else {\n        throw new Error(\n          `Point of length ${len} was invalid. Expected ${compressedLen} compressed bytes or ${uncompressedLen} uncompressed bytes`\n        );\n      }\n    },\n  });\n  const numToNByteStr = (num: bigint): string =>\n    ut.bytesToHex(ut.numberToBytesBE(num, CURVE.nByteLength));\n\n  function isBiggerThanHalfOrder(number: bigint) {\n    const HALF = CURVE_ORDER >> _1n;\n    return number > HALF;\n  }\n\n  function normalizeS(s: bigint) {\n    return isBiggerThanHalfOrder(s) ? modN(-s) : s;\n  }\n  // slice bytes num\n  const slcNum = (b: Uint8Array, from: number, to: number) => ut.bytesToNumberBE(b.slice(from, to));\n\n  /**\n   * ECDSA signature with its (r, s) properties. Supports DER & compact representations.\n   */\n  class Signature implements SignatureType {\n    constructor(readonly r: bigint, readonly s: bigint, readonly recovery?: number) {\n      this.assertValidity();\n    }\n\n    // pair (bytes of r, bytes of s)\n    static fromCompact(hex: Hex) {\n      const l = CURVE.nByteLength;\n      hex = ensureBytes('compactSignature', hex, l * 2);\n      return new Signature(slcNum(hex, 0, l), slcNum(hex, l, 2 * l));\n    }\n\n    // DER encoded ECDSA signature\n    // https://bitcoin.stackexchange.com/questions/57644/what-are-the-parts-of-a-bitcoin-transaction-input-script\n    static fromDER(hex: Hex) {\n      const { r, s } = DER.toSig(ensureBytes('DER', hex));\n      return new Signature(r, s);\n    }\n\n    assertValidity(): void {\n      // can use assertGE here\n      if (!isWithinCurveOrder(this.r)) throw new Error('r must be 0 < r < CURVE.n');\n      if (!isWithinCurveOrder(this.s)) throw new Error('s must be 0 < s < CURVE.n');\n    }\n\n    addRecoveryBit(recovery: number): RecoveredSignature {\n      return new Signature(this.r, this.s, recovery) as RecoveredSignature;\n    }\n\n    recoverPublicKey(msgHash: Hex): typeof Point.BASE {\n      const { r, s, recovery: rec } = this;\n      const h = bits2int_modN(ensureBytes('msgHash', msgHash)); // Truncate hash\n      if (rec == null || ![0, 1, 2, 3].includes(rec)) throw new Error('recovery id invalid');\n      const radj = rec === 2 || rec === 3 ? r + CURVE.n : r;\n      if (radj >= Fp.ORDER) throw new Error('recovery id 2 or 3 invalid');\n      const prefix = (rec & 1) === 0 ? '02' : '03';\n      const R = Point.fromHex(prefix + numToNByteStr(radj));\n      const ir = invN(radj); // r^-1\n      const u1 = modN(-h * ir); // -hr^-1\n      const u2 = modN(s * ir); // sr^-1\n      const Q = Point.BASE.multiplyAndAddUnsafe(R, u1, u2); // (sr^-1)R-(hr^-1)G = -(hr^-1)G + (sr^-1)\n      if (!Q) throw new Error('point at infinify'); // unsafe is fine: no priv data leaked\n      Q.assertValidity();\n      return Q;\n    }\n\n    // Signatures should be low-s, to prevent malleability.\n    hasHighS(): boolean {\n      return isBiggerThanHalfOrder(this.s);\n    }\n\n    normalizeS() {\n      return this.hasHighS() ? new Signature(this.r, modN(-this.s), this.recovery) : this;\n    }\n\n    // DER-encoded\n    toDERRawBytes() {\n      return ut.hexToBytes(this.toDERHex());\n    }\n    toDERHex() {\n      return DER.hexFromSig({ r: this.r, s: this.s });\n    }\n\n    // padded bytes of r, then padded bytes of s\n    toCompactRawBytes() {\n      return ut.hexToBytes(this.toCompactHex());\n    }\n    toCompactHex() {\n      return numToNByteStr(this.r) + numToNByteStr(this.s);\n    }\n  }\n  type RecoveredSignature = Signature & { recovery: number };\n\n  const utils = {\n    isValidPrivateKey(privateKey: PrivKey) {\n      try {\n        normPrivateKeyToScalar(privateKey);\n        return true;\n      } catch (error) {\n        return false;\n      }\n    },\n    normPrivateKeyToScalar: normPrivateKeyToScalar,\n\n    /**\n     * Produces cryptographically secure private key from random of size\n     * (groupLen + ceil(groupLen / 2)) with modulo bias being negligible.\n     */\n    randomPrivateKey: (): Uint8Array => {\n      const length = mod.getMinHashLength(CURVE.n);\n      return mod.mapHashToField(CURVE.randomBytes(length), CURVE.n);\n    },\n\n    /**\n     * Creates precompute table for an arbitrary EC point. Makes point \"cached\".\n     * Allows to massively speed-up `point.multiply(scalar)`.\n     * @returns cached point\n     * @example\n     * const fast = utils.precompute(8, ProjectivePoint.fromHex(someonesPubKey));\n     * fast.multiply(privKey); // much faster ECDH now\n     */\n    precompute(windowSize = 8, point = Point.BASE): typeof Point.BASE {\n      point._setWindowSize(windowSize);\n      point.multiply(BigInt(3)); // 3 is arbitrary, just need any number here\n      return point;\n    },\n  };\n\n  /**\n   * Computes public key for a private key. Checks for validity of the private key.\n   * @param privateKey private key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns Public key, full when isCompressed=false; short when isCompressed=true\n   */\n  function getPublicKey(privateKey: PrivKey, isCompressed = true): Uint8Array {\n    return Point.fromPrivateKey(privateKey).toRawBytes(isCompressed);\n  }\n\n  /**\n   * Quick and dirty check for item being public key. Does not validate hex, or being on-curve.\n   */\n  function isProbPub(item: PrivKey | PubKey): boolean {\n    const arr = item instanceof Uint8Array;\n    const str = typeof item === 'string';\n    const len = (arr || str) && (item as Hex).length;\n    if (arr) return len === compressedLen || len === uncompressedLen;\n    if (str) return len === 2 * compressedLen || len === 2 * uncompressedLen;\n    if (item instanceof Point) return true;\n    return false;\n  }\n\n  /**\n   * ECDH (Elliptic Curve Diffie Hellman).\n   * Computes shared public key from private key and public key.\n   * Checks: 1) private key validity 2) shared key is on-curve.\n   * Does NOT hash the result.\n   * @param privateA private key\n   * @param publicB different public key\n   * @param isCompressed whether to return compact (default), or full key\n   * @returns shared public key\n   */\n  function getSharedSecret(privateA: PrivKey, publicB: Hex, isCompressed = true): Uint8Array {\n    if (isProbPub(privateA)) throw new Error('first arg must be private key');\n    if (!isProbPub(publicB)) throw new Error('second arg must be public key');\n    const b = Point.fromHex(publicB); // check for being on-curve\n    return b.multiply(normPrivateKeyToScalar(privateA)).toRawBytes(isCompressed);\n  }\n\n  // RFC6979: ensure ECDSA msg is X bytes and < N. RFC suggests optional truncating via bits2octets.\n  // FIPS 186-4 4.6 suggests the leftmost min(nBitLen, outLen) bits, which matches bits2int.\n  // bits2int can produce res>N, we can do mod(res, N) since the bitLen is the same.\n  // int2octets can't be used; pads small msgs with 0: unacceptatble for trunc as per RFC vectors\n  const bits2int =\n    CURVE.bits2int ||\n    function (bytes: Uint8Array): bigint {\n      // For curves with nBitLength % 8 !== 0: bits2octets(bits2octets(m)) !== bits2octets(m)\n      // for some cases, since bytes.length * 8 is not actual bitLength.\n      const num = ut.bytesToNumberBE(bytes); // check for == u8 done here\n      const delta = bytes.length * 8 - CURVE.nBitLength; // truncate to nBitLength leftmost bits\n      return delta > 0 ? num >> BigInt(delta) : num;\n    };\n  const bits2int_modN =\n    CURVE.bits2int_modN ||\n    function (bytes: Uint8Array): bigint {\n      return modN(bits2int(bytes)); // can't use bytesToNumberBE here\n    };\n  // NOTE: pads output with zero as per spec\n  const ORDER_MASK = ut.bitMask(CURVE.nBitLength);\n  /**\n   * Converts to bytes. Checks if num in `[0..ORDER_MASK-1]` e.g.: `[0..2^256-1]`.\n   */\n  function int2octets(num: bigint): Uint8Array {\n    if (typeof num !== 'bigint') throw new Error('bigint expected');\n    if (!(_0n <= num && num < ORDER_MASK))\n      throw new Error(`bigint expected < 2^${CURVE.nBitLength}`);\n    // works with order, can have different size than numToField!\n    return ut.numberToBytesBE(num, CURVE.nByteLength);\n  }\n\n  // Steps A, D of RFC6979 3.2\n  // Creates RFC6979 seed; converts msg/privKey to numbers.\n  // Used only in sign, not in verify.\n  // NOTE: we cannot assume here that msgHash has same amount of bytes as curve order, this will be wrong at least for P521.\n  // Also it can be bigger for P224 + SHA256\n  function prepSig(msgHash: Hex, privateKey: PrivKey, opts = defaultSigOpts) {\n    if (['recovered', 'canonical'].some((k) => k in opts))\n      throw new Error('sign() legacy options not supported');\n    const { hash, randomBytes } = CURVE;\n    let { lowS, prehash, extraEntropy: ent } = opts; // generates low-s sigs by default\n    if (lowS == null) lowS = true; // RFC6979 3.2: we skip step A, because we already provide hash\n    msgHash = ensureBytes('msgHash', msgHash);\n    if (prehash) msgHash = ensureBytes('prehashed msgHash', hash(msgHash));\n\n    // We can't later call bits2octets, since nested bits2int is broken for curves\n    // with nBitLength % 8 !== 0. Because of that, we unwrap it here as int2octets call.\n    // const bits2octets = (bits) => int2octets(bits2int_modN(bits))\n    const h1int = bits2int_modN(msgHash);\n    const d = normPrivateKeyToScalar(privateKey); // validate private key, convert to bigint\n    const seedArgs = [int2octets(d), int2octets(h1int)];\n    // extraEntropy. RFC6979 3.6: additional k' (optional).\n    if (ent != null) {\n      // K = HMAC_K(V || 0x00 || int2octets(x) || bits2octets(h1) || k')\n      const e = ent === true ? randomBytes(Fp.BYTES) : ent; // generate random bytes OR pass as-is\n      seedArgs.push(ensureBytes('extraEntropy', e)); // check for being bytes\n    }\n    const seed = ut.concatBytes(...seedArgs); // Step D of RFC6979 3.2\n    const m = h1int; // NOTE: no need to call bits2int second time here, it is inside truncateHash!\n    // Converts signature params into point w r/s, checks result for validity.\n    function k2sig(kBytes: Uint8Array): RecoveredSignature | undefined {\n      // RFC 6979 Section 3.2, step 3: k = bits2int(T)\n      const k = bits2int(kBytes); // Cannot use fields methods, since it is group element\n      if (!isWithinCurveOrder(k)) return; // Important: all mod() calls here must be done over N\n      const ik = invN(k); // k^-1 mod n\n      const q = Point.BASE.multiply(k).toAffine(); // q = Gk\n      const r = modN(q.x); // r = q.x mod n\n      if (r === _0n) return;\n      // Can use scalar blinding b^-1(bm + bdr) where b ∈ [1,q−1] according to\n      // https://tches.iacr.org/index.php/TCHES/article/view/7337/6509. We've decided against it:\n      // a) dependency on CSPRNG b) 15% slowdown c) doesn't really help since bigints are not CT\n      const s = modN(ik * modN(m + r * d)); // Not using blinding here\n      if (s === _0n) return;\n      let recovery = (q.x === r ? 0 : 2) | Number(q.y & _1n); // recovery bit (2 or 3, when q.x > n)\n      let normS = s;\n      if (lowS && isBiggerThanHalfOrder(s)) {\n        normS = normalizeS(s); // if lowS was passed, ensure s is always\n        recovery ^= 1; // // in the bottom half of N\n      }\n      return new Signature(r, normS, recovery) as RecoveredSignature; // use normS, not s\n    }\n    return { seed, k2sig };\n  }\n  const defaultSigOpts: SignOpts = { lowS: CURVE.lowS, prehash: false };\n  const defaultVerOpts: VerOpts = { lowS: CURVE.lowS, prehash: false };\n\n  /**\n   * Signs message hash with a private key.\n   * ```\n   * sign(m, d, k) where\n   *   (x, y) = G × k\n   *   r = x mod n\n   *   s = (m + dr)/k mod n\n   * ```\n   * @param msgHash NOT message. msg needs to be hashed to `msgHash`, or use `prehash`.\n   * @param privKey private key\n   * @param opts lowS for non-malleable sigs. extraEntropy for mixing randomness into k. prehash will hash first arg.\n   * @returns signature with recovery param\n   */\n  function sign(msgHash: Hex, privKey: PrivKey, opts = defaultSigOpts): RecoveredSignature {\n    const { seed, k2sig } = prepSig(msgHash, privKey, opts); // Steps A, D of RFC6979 3.2.\n    const C = CURVE;\n    const drbg = ut.createHmacDrbg<RecoveredSignature>(C.hash.outputLen, C.nByteLength, C.hmac);\n    return drbg(seed, k2sig); // Steps B, C, D, E, F, G\n  }\n\n  // Enable precomputes. Slows down first publicKey computation by 20ms.\n  Point.BASE._setWindowSize(8);\n  // utils.precompute(8, ProjectivePoint.BASE)\n\n  /**\n   * Verifies a signature against message hash and public key.\n   * Rejects lowS signatures by default: to override,\n   * specify option `{lowS: false}`. Implements section 4.1.4 from https://www.secg.org/sec1-v2.pdf:\n   *\n   * ```\n   * verify(r, s, h, P) where\n   *   U1 = hs^-1 mod n\n   *   U2 = rs^-1 mod n\n   *   R = U1⋅G - U2⋅P\n   *   mod(R.x, n) == r\n   * ```\n   */\n  function verify(\n    signature: Hex | SignatureLike,\n    msgHash: Hex,\n    publicKey: Hex,\n    opts = defaultVerOpts\n  ): boolean {\n    const sg = signature;\n    msgHash = ensureBytes('msgHash', msgHash);\n    publicKey = ensureBytes('publicKey', publicKey);\n    if ('strict' in opts) throw new Error('options.strict was renamed to lowS');\n    const { lowS, prehash } = opts;\n\n    let _sig: Signature | undefined = undefined;\n    let P: ProjPointType<bigint>;\n    try {\n      if (typeof sg === 'string' || sg instanceof Uint8Array) {\n        // Signature can be represented in 2 ways: compact (2*nByteLength) & DER (variable-length).\n        // Since DER can also be 2*nByteLength bytes, we check for it first.\n        try {\n          _sig = Signature.fromDER(sg);\n        } catch (derError) {\n          if (!(derError instanceof DER.Err)) throw derError;\n          _sig = Signature.fromCompact(sg);\n        }\n      } else if (typeof sg === 'object' && typeof sg.r === 'bigint' && typeof sg.s === 'bigint') {\n        const { r, s } = sg;\n        _sig = new Signature(r, s);\n      } else {\n        throw new Error('PARSE');\n      }\n      P = Point.fromHex(publicKey);\n    } catch (error) {\n      if ((error as Error).message === 'PARSE')\n        throw new Error(`signature must be Signature instance, Uint8Array or hex string`);\n      return false;\n    }\n    if (lowS && _sig.hasHighS()) return false;\n    if (prehash) msgHash = CURVE.hash(msgHash);\n    const { r, s } = _sig;\n    const h = bits2int_modN(msgHash); // Cannot use fields methods, since it is group element\n    const is = invN(s); // s^-1\n    const u1 = modN(h * is); // u1 = hs^-1 mod n\n    const u2 = modN(r * is); // u2 = rs^-1 mod n\n    const R = Point.BASE.multiplyAndAddUnsafe(P, u1, u2)?.toAffine(); // R = u1⋅G + u2⋅P\n    if (!R) return false;\n    const v = modN(R.x);\n    return v === r;\n  }\n  return {\n    CURVE,\n    getPublicKey,\n    getSharedSecret,\n    sign,\n    verify,\n    ProjectivePoint: Point,\n    Signature,\n    utils,\n  };\n}\n\n/**\n * Implementation of the Shallue and van de Woestijne method for any weierstrass curve.\n * TODO: check if there is a way to merge this with uvRatio in Edwards; move to modular.\n * b = True and y = sqrt(u / v) if (u / v) is square in F, and\n * b = False and y = sqrt(Z * (u / v)) otherwise.\n * @param Fp\n * @param Z\n * @returns\n */\nexport function SWUFpSqrtRatio<T>(Fp: mod.IField<T>, Z: T) {\n  // Generic implementation\n  const q = Fp.ORDER;\n  let l = _0n;\n  for (let o = q - _1n; o % _2n === _0n; o /= _2n) l += _1n;\n  const c1 = l; // 1. c1, the largest integer such that 2^c1 divides q - 1.\n  // We need 2n ** c1 and 2n ** (c1-1). We can't use **; but we can use <<.\n  // 2n ** c1 == 2n << (c1-1)\n  const _2n_pow_c1_1 = _2n << (c1 - _1n - _1n);\n  const _2n_pow_c1 = _2n_pow_c1_1 * _2n;\n  const c2 = (q - _1n) / _2n_pow_c1; // 2. c2 = (q - 1) / (2^c1)  # Integer arithmetic\n  const c3 = (c2 - _1n) / _2n; // 3. c3 = (c2 - 1) / 2            # Integer arithmetic\n  const c4 = _2n_pow_c1 - _1n; // 4. c4 = 2^c1 - 1                # Integer arithmetic\n  const c5 = _2n_pow_c1_1; // 5. c5 = 2^(c1 - 1)                  # Integer arithmetic\n  const c6 = Fp.pow(Z, c2); // 6. c6 = Z^c2\n  const c7 = Fp.pow(Z, (c2 + _1n) / _2n); // 7. c7 = Z^((c2 + 1) / 2)\n  let sqrtRatio = (u: T, v: T): { isValid: boolean; value: T } => {\n    let tv1 = c6; // 1. tv1 = c6\n    let tv2 = Fp.pow(v, c4); // 2. tv2 = v^c4\n    let tv3 = Fp.sqr(tv2); // 3. tv3 = tv2^2\n    tv3 = Fp.mul(tv3, v); // 4. tv3 = tv3 * v\n    let tv5 = Fp.mul(u, tv3); // 5. tv5 = u * tv3\n    tv5 = Fp.pow(tv5, c3); // 6. tv5 = tv5^c3\n    tv5 = Fp.mul(tv5, tv2); // 7. tv5 = tv5 * tv2\n    tv2 = Fp.mul(tv5, v); // 8. tv2 = tv5 * v\n    tv3 = Fp.mul(tv5, u); // 9. tv3 = tv5 * u\n    let tv4 = Fp.mul(tv3, tv2); // 10. tv4 = tv3 * tv2\n    tv5 = Fp.pow(tv4, c5); // 11. tv5 = tv4^c5\n    let isQR = Fp.eql(tv5, Fp.ONE); // 12. isQR = tv5 == 1\n    tv2 = Fp.mul(tv3, c7); // 13. tv2 = tv3 * c7\n    tv5 = Fp.mul(tv4, tv1); // 14. tv5 = tv4 * tv1\n    tv3 = Fp.cmov(tv2, tv3, isQR); // 15. tv3 = CMOV(tv2, tv3, isQR)\n    tv4 = Fp.cmov(tv5, tv4, isQR); // 16. tv4 = CMOV(tv5, tv4, isQR)\n    // 17. for i in (c1, c1 - 1, ..., 2):\n    for (let i = c1; i > _1n; i--) {\n      let tv5 = i - _2n; // 18.    tv5 = i - 2\n      tv5 = _2n << (tv5 - _1n); // 19.    tv5 = 2^tv5\n      let tvv5 = Fp.pow(tv4, tv5); // 20.    tv5 = tv4^tv5\n      const e1 = Fp.eql(tvv5, Fp.ONE); // 21.    e1 = tv5 == 1\n      tv2 = Fp.mul(tv3, tv1); // 22.    tv2 = tv3 * tv1\n      tv1 = Fp.mul(tv1, tv1); // 23.    tv1 = tv1 * tv1\n      tvv5 = Fp.mul(tv4, tv1); // 24.    tv5 = tv4 * tv1\n      tv3 = Fp.cmov(tv2, tv3, e1); // 25.    tv3 = CMOV(tv2, tv3, e1)\n      tv4 = Fp.cmov(tvv5, tv4, e1); // 26.    tv4 = CMOV(tv5, tv4, e1)\n    }\n    return { isValid: isQR, value: tv3 };\n  };\n  if (Fp.ORDER % _4n === _3n) {\n    // sqrt_ratio_3mod4(u, v)\n    const c1 = (Fp.ORDER - _3n) / _4n; // 1. c1 = (q - 3) / 4     # Integer arithmetic\n    const c2 = Fp.sqrt(Fp.neg(Z)); // 2. c2 = sqrt(-Z)\n    sqrtRatio = (u: T, v: T) => {\n      let tv1 = Fp.sqr(v); // 1. tv1 = v^2\n      const tv2 = Fp.mul(u, v); // 2. tv2 = u * v\n      tv1 = Fp.mul(tv1, tv2); // 3. tv1 = tv1 * tv2\n      let y1 = Fp.pow(tv1, c1); // 4. y1 = tv1^c1\n      y1 = Fp.mul(y1, tv2); // 5. y1 = y1 * tv2\n      const y2 = Fp.mul(y1, c2); // 6. y2 = y1 * c2\n      const tv3 = Fp.mul(Fp.sqr(y1), v); // 7. tv3 = y1^2; 8. tv3 = tv3 * v\n      const isQR = Fp.eql(tv3, u); // 9. isQR = tv3 == u\n      let y = Fp.cmov(y2, y1, isQR); // 10. y = CMOV(y2, y1, isQR)\n      return { isValid: isQR, value: y }; // 11. return (isQR, y) isQR ? y : y*c2\n    };\n  }\n  // No curves uses that\n  // if (Fp.ORDER % _8n === _5n) // sqrt_ratio_5mod8\n  return sqrtRatio;\n}\n/**\n * Simplified Shallue-van de Woestijne-Ulas Method\n * https://www.rfc-editor.org/rfc/rfc9380#section-6.6.2\n */\nexport function mapToCurveSimpleSWU<T>(\n  Fp: mod.IField<T>,\n  opts: {\n    A: T;\n    B: T;\n    Z: T;\n  }\n) {\n  mod.validateField(Fp);\n  if (!Fp.isValid(opts.A) || !Fp.isValid(opts.B) || !Fp.isValid(opts.Z))\n    throw new Error('mapToCurveSimpleSWU: invalid opts');\n  const sqrtRatio = SWUFpSqrtRatio(Fp, opts.Z);\n  if (!Fp.isOdd) throw new Error('Fp.isOdd is not implemented!');\n  // Input: u, an element of F.\n  // Output: (x, y), a point on E.\n  return (u: T): { x: T; y: T } => {\n    // prettier-ignore\n    let tv1, tv2, tv3, tv4, tv5, tv6, x, y;\n    tv1 = Fp.sqr(u); // 1.  tv1 = u^2\n    tv1 = Fp.mul(tv1, opts.Z); // 2.  tv1 = Z * tv1\n    tv2 = Fp.sqr(tv1); // 3.  tv2 = tv1^2\n    tv2 = Fp.add(tv2, tv1); // 4.  tv2 = tv2 + tv1\n    tv3 = Fp.add(tv2, Fp.ONE); // 5.  tv3 = tv2 + 1\n    tv3 = Fp.mul(tv3, opts.B); // 6.  tv3 = B * tv3\n    tv4 = Fp.cmov(opts.Z, Fp.neg(tv2), !Fp.eql(tv2, Fp.ZERO)); // 7.  tv4 = CMOV(Z, -tv2, tv2 != 0)\n    tv4 = Fp.mul(tv4, opts.A); // 8.  tv4 = A * tv4\n    tv2 = Fp.sqr(tv3); // 9.  tv2 = tv3^2\n    tv6 = Fp.sqr(tv4); // 10. tv6 = tv4^2\n    tv5 = Fp.mul(tv6, opts.A); // 11. tv5 = A * tv6\n    tv2 = Fp.add(tv2, tv5); // 12. tv2 = tv2 + tv5\n    tv2 = Fp.mul(tv2, tv3); // 13. tv2 = tv2 * tv3\n    tv6 = Fp.mul(tv6, tv4); // 14. tv6 = tv6 * tv4\n    tv5 = Fp.mul(tv6, opts.B); // 15. tv5 = B * tv6\n    tv2 = Fp.add(tv2, tv5); // 16. tv2 = tv2 + tv5\n    x = Fp.mul(tv1, tv3); // 17.   x = tv1 * tv3\n    const { isValid, value } = sqrtRatio(tv2, tv6); // 18. (is_gx1_square, y1) = sqrt_ratio(tv2, tv6)\n    y = Fp.mul(tv1, u); // 19.   y = tv1 * u  -> Z * u^3 * y1\n    y = Fp.mul(y, value); // 20.   y = y * y1\n    x = Fp.cmov(x, tv3, isValid); // 21.   x = CMOV(x, tv3, is_gx1_square)\n    y = Fp.cmov(y, value, isValid); // 22.   y = CMOV(y, y1, is_gx1_square)\n    const e1 = Fp.isOdd!(u) === Fp.isOdd!(y); // 23.  e1 = sgn0(u) == sgn0(y)\n    y = Fp.cmov(Fp.neg(y), y, e1); // 24.   y = CMOV(-y, y, e1)\n    x = Fp.div(x, tv4); // 25.   x = x / tv4\n    return { x, y };\n  };\n}\n"], "mappings": "AAAA;AACA;AACA,OAAO,KAAKA,GAAG,MAAM,cAAc;AACnC,OAAO,KAAKC,EAAE,MAAM,YAAY;AAChC,SAA8BC,WAAW,QAAQ,YAAY;AAC7D,SAAkCC,IAAI,EAAcC,aAAa,QAAqB,YAAY;AAqFlG,SAASC,iBAAiBA,CAAIC,KAAyB;EACrD,MAAMC,IAAI,GAAGH,aAAa,CAACE,KAAK,CAAC;EACjCL,EAAE,CAACO,cAAc,CACfD,IAAI,EACJ;IACEE,CAAC,EAAE,OAAO;IACVC,CAAC,EAAE;GACJ,EACD;IACEC,wBAAwB,EAAE,OAAO;IACjCC,cAAc,EAAE,SAAS;IACzBC,aAAa,EAAE,UAAU;IACzBC,aAAa,EAAE,UAAU;IACzBC,kBAAkB,EAAE,SAAS;IAC7BC,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE;GACV,CACF;EACD,MAAM;IAAEC,IAAI;IAAEC,EAAE;IAAEV;EAAC,CAAE,GAAGF,IAAI;EAC5B,IAAIW,IAAI,EAAE;IACR,IAAI,CAACC,EAAE,CAACC,GAAG,CAACX,CAAC,EAAEU,EAAE,CAACE,IAAI,CAAC,EAAE;MACvB,MAAM,IAAIC,KAAK,CAAC,mEAAmE,CAAC;;IAEtF,IACE,OAAOJ,IAAI,KAAK,QAAQ,IACxB,OAAOA,IAAI,CAACK,IAAI,KAAK,QAAQ,IAC7B,OAAOL,IAAI,CAACM,WAAW,KAAK,UAAU,EACtC;MACA,MAAM,IAAIF,KAAK,CAAC,mEAAmE,CAAC;;;EAGxF,OAAOG,MAAM,CAACC,MAAM,CAAC;IAAE,GAAGnB;EAAI,CAAW,CAAC;AAC5C;AASA;AACA,MAAM;EAAEoB,eAAe,EAAEC,GAAG;EAAEC,UAAU,EAAEC;AAAG,CAAE,GAAG7B,EAAE;AACpD,OAAO,MAAM8B,GAAG,GAAG;EACjB;EACAC,GAAG,EAAE,MAAMC,MAAO,SAAQX,KAAK;IAC7BY,YAAYC,CAAC,GAAG,EAAE;MAChB,KAAK,CAACA,CAAC,CAAC;IACV;GACD;EACDC,SAASA,CAACC,IAAgB;IACxB,MAAM;MAAEL,GAAG,EAAEM;IAAC,CAAE,GAAGP,GAAG;IACtB,IAAIM,IAAI,CAACE,MAAM,GAAG,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,MAAM,IAAIC,CAAC,CAAC,+BAA+B,CAAC;IACrF,MAAME,GAAG,GAAGH,IAAI,CAAC,CAAC,CAAC;IACnB,MAAMI,GAAG,GAAGJ,IAAI,CAACK,QAAQ,CAAC,CAAC,EAAEF,GAAG,GAAG,CAAC,CAAC;IACrC,IAAI,CAACA,GAAG,IAAIC,GAAG,CAACF,MAAM,KAAKC,GAAG,EAAE,MAAM,IAAIF,CAAC,CAAC,yCAAyC,CAAC;IACtF;IACA;IACA;IACA;IACA,IAAIG,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,EAAE,MAAM,IAAIH,CAAC,CAAC,qCAAqC,CAAC;IAC3E,IAAIG,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,EAAEA,GAAG,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAC3C,MAAM,IAAIH,CAAC,CAAC,qDAAqD,CAAC;IACpE,OAAO;MAAEK,CAAC,EAAEf,GAAG,CAACa,GAAG,CAAC;MAAEG,CAAC,EAAEP,IAAI,CAACK,QAAQ,CAACF,GAAG,GAAG,CAAC;IAAC,CAAE,CAAC,CAAC;EACrD,CAAC;EACDK,KAAKA,CAACC,GAAwB;IAC5B;IACA,MAAM;MAAEd,GAAG,EAAEM;IAAC,CAAE,GAAGP,GAAG;IACtB,MAAMM,IAAI,GAAG,OAAOS,GAAG,KAAK,QAAQ,GAAGhB,GAAG,CAACgB,GAAG,CAAC,GAAGA,GAAG;IACrD,IAAI,EAAET,IAAI,YAAYU,UAAU,CAAC,EAAE,MAAM,IAAIzB,KAAK,CAAC,eAAe,CAAC;IACnE,IAAIsB,CAAC,GAAGP,IAAI,CAACE,MAAM;IACnB,IAAIK,CAAC,GAAG,CAAC,IAAIP,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,MAAM,IAAIC,CAAC,CAAC,uBAAuB,CAAC;IAClE,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKO,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIN,CAAC,CAAC,qCAAqC,CAAC;IACzE,MAAM;MAAEK,CAAC,EAAEK,CAAC;MAAEJ,CAAC,EAAEK;IAAM,CAAE,GAAGlB,GAAG,CAACK,SAAS,CAACC,IAAI,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3D,MAAM;MAAEC,CAAC,EAAEO,CAAC;MAAEN,CAAC,EAAEO;IAAU,CAAE,GAAGpB,GAAG,CAACK,SAAS,CAACa,MAAM,CAAC;IACrD,IAAIE,UAAU,CAACZ,MAAM,EAAE,MAAM,IAAID,CAAC,CAAC,6CAA6C,CAAC;IACjF,OAAO;MAAEU,CAAC;MAAEE;IAAC,CAAE;EACjB,CAAC;EACDE,UAAUA,CAACC,GAA6B;IACtC;IACA,MAAMC,KAAK,GAAIJ,CAAS,IAAcK,MAAM,CAACC,QAAQ,CAACN,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,MAAM,GAAG,IAAI,GAAGA,CAAC,GAAGA,CAAE;IACxF,MAAMO,CAAC,GAAIC,GAAoB,IAAI;MACjC,MAAMZ,GAAG,GAAGY,GAAG,CAACC,QAAQ,CAAC,EAAE,CAAC;MAC5B,OAAOb,GAAG,CAACP,MAAM,GAAG,CAAC,GAAG,IAAIO,GAAG,EAAE,GAAGA,GAAG;IACzC,CAAC;IACD,MAAMI,CAAC,GAAGI,KAAK,CAACG,CAAC,CAACJ,GAAG,CAACH,CAAC,CAAC,CAAC;IACzB,MAAMF,CAAC,GAAGM,KAAK,CAACG,CAAC,CAACJ,GAAG,CAACL,CAAC,CAAC,CAAC;IACzB,MAAMY,GAAG,GAAGV,CAAC,CAACX,MAAM,GAAG,CAAC;IACxB,MAAMsB,GAAG,GAAGb,CAAC,CAACT,MAAM,GAAG,CAAC;IACxB,MAAMuB,EAAE,GAAGL,CAAC,CAACG,GAAG,CAAC;IACjB,MAAMG,EAAE,GAAGN,CAAC,CAACI,GAAG,CAAC;IACjB,OAAO,KAAKJ,CAAC,CAACI,GAAG,GAAGD,GAAG,GAAG,CAAC,CAAC,KAAKG,EAAE,GAAGf,CAAC,KAAKc,EAAE,GAAGZ,CAAC,EAAE;EACtD;CACD;AAED;AACA;AACA,MAAMc,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;EAAEC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;EAAEE,GAAG,GAAGF,MAAM,CAAC,CAAC,CAAC;EAAEG,GAAG,GAAGH,MAAM,CAAC,CAAC,CAAC;EAAEI,GAAG,GAAGJ,MAAM,CAAC,CAAC,CAAC;AAEzF,OAAM,SAAUK,iBAAiBA,CAAI/D,IAAwB;EAC3D,MAAMgE,KAAK,GAAGlE,iBAAiB,CAACE,IAAI,CAAC;EACrC,MAAM;IAAEY;EAAE,CAAE,GAAGoD,KAAK,CAAC,CAAC;EAEtB,MAAMtD,OAAO,GACXsD,KAAK,CAACtD,OAAO,KACZ,CAACuD,EAAsB,EAAEC,KAAuB,EAAEC,aAAsB,KAAI;IAC3E,MAAMjE,CAAC,GAAGgE,KAAK,CAACE,QAAQ,EAAE;IAC1B,OAAO1E,EAAE,CAAC2E,WAAW,CAAC7B,UAAU,CAAC8B,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE1D,EAAE,CAACF,OAAO,CAACR,CAAC,CAACqE,CAAC,CAAC,EAAE3D,EAAE,CAACF,OAAO,CAACR,CAAC,CAACsE,CAAC,CAAC,CAAC;EAClF,CAAC,CAAC;EACJ,MAAM/D,SAAS,GACbuD,KAAK,CAACvD,SAAS,KACbgE,KAAiB,IAAI;IACrB;IACA,MAAMC,IAAI,GAAGD,KAAK,CAACtC,QAAQ,CAAC,CAAC,CAAC;IAC9B;IACA,MAAMoC,CAAC,GAAG3D,EAAE,CAACH,SAAS,CAACiE,IAAI,CAACvC,QAAQ,CAAC,CAAC,EAAEvB,EAAE,CAAC+D,KAAK,CAAC,CAAC;IAClD,MAAMH,CAAC,GAAG5D,EAAE,CAACH,SAAS,CAACiE,IAAI,CAACvC,QAAQ,CAACvB,EAAE,CAAC+D,KAAK,EAAE,CAAC,GAAG/D,EAAE,CAAC+D,KAAK,CAAC,CAAC;IAC7D,OAAO;MAAEJ,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC,CAAC;EAEJ;;;;EAIA,SAASI,mBAAmBA,CAACL,CAAI;IAC/B,MAAM;MAAErE,CAAC;MAAEC;IAAC,CAAE,GAAG6D,KAAK;IACtB,MAAMa,EAAE,GAAGjE,EAAE,CAACkE,GAAG,CAACP,CAAC,CAAC,CAAC,CAAC;IACtB,MAAMQ,EAAE,GAAGnE,EAAE,CAACoE,GAAG,CAACH,EAAE,EAAEN,CAAC,CAAC,CAAC,CAAC;IAC1B,OAAO3D,EAAE,CAACqE,GAAG,CAACrE,EAAE,CAACqE,GAAG,CAACF,EAAE,EAAEnE,EAAE,CAACoE,GAAG,CAACT,CAAC,EAAErE,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;EAC9C;EACA;EACA;EACA;EACA;EACA,IAAI,CAACS,EAAE,CAACC,GAAG,CAACD,EAAE,CAACkE,GAAG,CAACd,KAAK,CAACkB,EAAE,CAAC,EAAEN,mBAAmB,CAACZ,KAAK,CAACmB,EAAE,CAAC,CAAC,EAC1D,MAAM,IAAIpE,KAAK,CAAC,6CAA6C,CAAC;EAEhE;EACA,SAASqE,kBAAkBA,CAACjC,GAAW;IACrC,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAIM,GAAG,GAAGN,GAAG,IAAIA,GAAG,GAAGa,KAAK,CAACqB,CAAC;EAC9D;EACA,SAASC,QAAQA,CAACnC,GAAW;IAC3B,IAAI,CAACiC,kBAAkB,CAACjC,GAAG,CAAC,EAAE,MAAM,IAAIpC,KAAK,CAAC,6CAA6C,CAAC;EAC9F;EACA;EACA;EACA,SAASwE,sBAAsBA,CAACC,GAAY;IAC1C,MAAM;MAAEpF,wBAAwB,EAAEqF,OAAO;MAAEC,WAAW;MAAErF,cAAc;MAAEgF;IAAC,CAAE,GAAGrB,KAAK;IACnF,IAAIyB,OAAO,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MACtC,IAAIA,GAAG,YAAYhD,UAAU,EAAEgD,GAAG,GAAG9F,EAAE,CAACiG,UAAU,CAACH,GAAG,CAAC;MACvD;MACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,CAACC,OAAO,CAACG,QAAQ,CAACJ,GAAG,CAACxD,MAAM,CAAC,EAAE,MAAM,IAAIjB,KAAK,CAAC,aAAa,CAAC;MAC5FyE,GAAG,GAAGA,GAAG,CAACK,QAAQ,CAACH,WAAW,GAAG,CAAC,EAAE,GAAG,CAAC;;IAE1C,IAAIvC,GAAW;IACf,IAAI;MACFA,GAAG,GACD,OAAOqC,GAAG,KAAK,QAAQ,GACnBA,GAAG,GACH9F,EAAE,CAAC0B,eAAe,CAACzB,WAAW,CAAC,aAAa,EAAE6F,GAAG,EAAEE,WAAW,CAAC,CAAC;KACvE,CAAC,OAAOI,KAAK,EAAE;MACd,MAAM,IAAI/E,KAAK,CAAC,uBAAuB2E,WAAW,8BAA8B,OAAOF,GAAG,EAAE,CAAC;;IAE/F,IAAInF,cAAc,EAAE8C,GAAG,GAAG1D,GAAG,CAACA,GAAG,CAAC0D,GAAG,EAAEkC,CAAC,CAAC,CAAC,CAAC;IAC3CC,QAAQ,CAACnC,GAAG,CAAC,CAAC,CAAC;IACf,OAAOA,GAAG;EACZ;EAEA,MAAM4C,gBAAgB,GAAG,IAAIC,GAAG,EAAkB;EAClD,SAASC,cAAcA,CAACC,KAAc;IACpC,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE,MAAM,IAAIpF,KAAK,CAAC,0BAA0B,CAAC;EAC5E;EACA;;;;;EAKA,MAAMoF,KAAK;IAITxE,YAAqByE,EAAK,EAAWC,EAAK,EAAWC,EAAK;MAArC,KAAAF,EAAE,GAAFA,EAAE;MAAc,KAAAC,EAAE,GAAFA,EAAE;MAAc,KAAAC,EAAE,GAAFA,EAAE;MACrD,IAAIF,EAAE,IAAI,IAAI,IAAI,CAACxF,EAAE,CAAC2F,OAAO,CAACH,EAAE,CAAC,EAAE,MAAM,IAAIrF,KAAK,CAAC,YAAY,CAAC;MAChE,IAAIsF,EAAE,IAAI,IAAI,IAAI,CAACzF,EAAE,CAAC2F,OAAO,CAACF,EAAE,CAAC,EAAE,MAAM,IAAItF,KAAK,CAAC,YAAY,CAAC;MAChE,IAAIuF,EAAE,IAAI,IAAI,IAAI,CAAC1F,EAAE,CAAC2F,OAAO,CAACD,EAAE,CAAC,EAAE,MAAM,IAAIvF,KAAK,CAAC,YAAY,CAAC;IAClE;IAEA;IACA;IACA,OAAOyF,UAAUA,CAACC,CAAiB;MACjC,MAAM;QAAElC,CAAC;QAAEC;MAAC,CAAE,GAAGiC,CAAC,IAAI,EAAE;MACxB,IAAI,CAACA,CAAC,IAAI,CAAC7F,EAAE,CAAC2F,OAAO,CAAChC,CAAC,CAAC,IAAI,CAAC3D,EAAE,CAAC2F,OAAO,CAAC/B,CAAC,CAAC,EAAE,MAAM,IAAIzD,KAAK,CAAC,sBAAsB,CAAC;MACnF,IAAI0F,CAAC,YAAYN,KAAK,EAAE,MAAM,IAAIpF,KAAK,CAAC,8BAA8B,CAAC;MACvE,MAAM2F,GAAG,GAAIC,CAAI,IAAK/F,EAAE,CAACC,GAAG,CAAC8F,CAAC,EAAE/F,EAAE,CAACE,IAAI,CAAC;MACxC;MACA,IAAI4F,GAAG,CAACnC,CAAC,CAAC,IAAImC,GAAG,CAAClC,CAAC,CAAC,EAAE,OAAO2B,KAAK,CAACrF,IAAI;MACvC,OAAO,IAAIqF,KAAK,CAAC5B,CAAC,EAAEC,CAAC,EAAE5D,EAAE,CAACgG,GAAG,CAAC;IAChC;IAEA,IAAIrC,CAACA,CAAA;MACH,OAAO,IAAI,CAACH,QAAQ,EAAE,CAACG,CAAC;IAC1B;IACA,IAAIC,CAACA,CAAA;MACH,OAAO,IAAI,CAACJ,QAAQ,EAAE,CAACI,CAAC;IAC1B;IAEA;;;;;;IAMA,OAAOqC,UAAUA,CAACC,MAAe;MAC/B,MAAMC,KAAK,GAAGnG,EAAE,CAACoG,WAAW,CAACF,MAAM,CAACG,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACH,EAAE,CAAC,CAAC;MACrD,OAAOQ,MAAM,CAACG,GAAG,CAAC,CAACR,CAAC,EAAEE,CAAC,KAAKF,CAAC,CAACrC,QAAQ,CAAC2C,KAAK,CAACJ,CAAC,CAAC,CAAC,CAAC,CAACM,GAAG,CAACd,KAAK,CAACK,UAAU,CAAC;IACzE;IAEA;;;;IAIA,OAAOU,OAAOA,CAAC3E,GAAQ;MACrB,MAAM4E,CAAC,GAAGhB,KAAK,CAACK,UAAU,CAAC/F,SAAS,CAACd,WAAW,CAAC,UAAU,EAAE4C,GAAG,CAAC,CAAC,CAAC;MACnE4E,CAAC,CAACC,cAAc,EAAE;MAClB,OAAOD,CAAC;IACV;IAEA;IACA,OAAOE,cAAcA,CAACC,UAAmB;MACvC,OAAOnB,KAAK,CAACoB,IAAI,CAACC,QAAQ,CAACjC,sBAAsB,CAAC+B,UAAU,CAAC,CAAC;IAChE;IAOA;IACAG,cAAcA,CAACC,UAAkB;MAC/B,IAAI,CAACC,YAAY,GAAGD,UAAU;MAC9B3B,gBAAgB,CAAC6B,MAAM,CAAC,IAAI,CAAC;IAC/B;IAEA;IACAR,cAAcA,CAAA;MACZ,IAAI,IAAI,CAACV,GAAG,EAAE,EAAE;QACd;QACA;QACA;QACA,IAAI1C,KAAK,CAACxD,kBAAkB,IAAI,CAACI,EAAE,CAAC8F,GAAG,CAAC,IAAI,CAACL,EAAE,CAAC,EAAE;QAClD,MAAM,IAAItF,KAAK,CAAC,iBAAiB,CAAC;;MAEpC;MACA,MAAM;QAAEwD,CAAC;QAAEC;MAAC,CAAE,GAAG,IAAI,CAACJ,QAAQ,EAAE;MAChC;MACA,IAAI,CAACxD,EAAE,CAAC2F,OAAO,CAAChC,CAAC,CAAC,IAAI,CAAC3D,EAAE,CAAC2F,OAAO,CAAC/B,CAAC,CAAC,EAAE,MAAM,IAAIzD,KAAK,CAAC,0BAA0B,CAAC;MACjF,MAAM8G,IAAI,GAAGjH,EAAE,CAACkE,GAAG,CAACN,CAAC,CAAC,CAAC,CAAC;MACxB,MAAMsD,KAAK,GAAGlD,mBAAmB,CAACL,CAAC,CAAC,CAAC,CAAC;MACtC,IAAI,CAAC3D,EAAE,CAACC,GAAG,CAACgH,IAAI,EAAEC,KAAK,CAAC,EAAE,MAAM,IAAI/G,KAAK,CAAC,mCAAmC,CAAC;MAC9E,IAAI,CAAC,IAAI,CAACT,aAAa,EAAE,EAAE,MAAM,IAAIS,KAAK,CAAC,wCAAwC,CAAC;IACtF;IACAgH,QAAQA,CAAA;MACN,MAAM;QAAEvD;MAAC,CAAE,GAAG,IAAI,CAACJ,QAAQ,EAAE;MAC7B,IAAIxD,EAAE,CAACoH,KAAK,EAAE,OAAO,CAACpH,EAAE,CAACoH,KAAK,CAACxD,CAAC,CAAC;MACjC,MAAM,IAAIzD,KAAK,CAAC,6BAA6B,CAAC;IAChD;IAEA;;;IAGAkH,MAAMA,CAAC/B,KAAY;MACjBD,cAAc,CAACC,KAAK,CAAC;MACrB,MAAM;QAAEE,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B;MAAE,CAAE,GAAG,IAAI;MACvC,MAAM;QAAEhC,EAAE,EAAEiC,EAAE;QAAEhC,EAAE,EAAEiC,EAAE;QAAEhC,EAAE,EAAEiC;MAAE,CAAE,GAAGrC,KAAK;MACxC,MAAMsC,EAAE,GAAG5H,EAAE,CAACC,GAAG,CAACD,EAAE,CAACoE,GAAG,CAACkD,EAAE,EAAEK,EAAE,CAAC,EAAE3H,EAAE,CAACoE,GAAG,CAACqD,EAAE,EAAED,EAAE,CAAC,CAAC;MACjD,MAAMK,EAAE,GAAG7H,EAAE,CAACC,GAAG,CAACD,EAAE,CAACoE,GAAG,CAACmD,EAAE,EAAEI,EAAE,CAAC,EAAE3H,EAAE,CAACoE,GAAG,CAACsD,EAAE,EAAEF,EAAE,CAAC,CAAC;MACjD,OAAOI,EAAE,IAAIC,EAAE;IACjB;IAEA;;;IAGAC,MAAMA,CAAA;MACJ,OAAO,IAAIvC,KAAK,CAAC,IAAI,CAACC,EAAE,EAAExF,EAAE,CAAC+H,GAAG,CAAC,IAAI,CAACtC,EAAE,CAAC,EAAE,IAAI,CAACC,EAAE,CAAC;IACrD;IAEA;IACA;IACA;IACA;IACAsC,MAAMA,CAAA;MACJ,MAAM;QAAE1I,CAAC;QAAEC;MAAC,CAAE,GAAG6D,KAAK;MACtB,MAAM6E,EAAE,GAAGjI,EAAE,CAACoE,GAAG,CAAC7E,CAAC,EAAE0D,GAAG,CAAC;MACzB,MAAM;QAAEuC,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B;MAAE,CAAE,GAAG,IAAI;MACvC,IAAIU,EAAE,GAAGlI,EAAE,CAACE,IAAI;QAAEiI,EAAE,GAAGnI,EAAE,CAACE,IAAI;QAAEkI,EAAE,GAAGpI,EAAE,CAACE,IAAI,CAAC,CAAC;MAC9C,IAAImI,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACkD,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIgB,EAAE,GAAGtI,EAAE,CAACoE,GAAG,CAACmD,EAAE,EAAEA,EAAE,CAAC;MACvB,IAAIgB,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAACoD,EAAE,EAAEA,EAAE,CAAC;MACvB,IAAIgB,EAAE,GAAGxI,EAAE,CAACoE,GAAG,CAACkD,EAAE,EAAEC,EAAE,CAAC;MACvBiB,EAAE,GAAGxI,EAAE,CAACqE,GAAG,CAACmE,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBJ,EAAE,GAAGpI,EAAE,CAACoE,GAAG,CAACkD,EAAE,EAAEE,EAAE,CAAC;MACnBY,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAAC+D,EAAE,EAAEA,EAAE,CAAC;MACnBF,EAAE,GAAGlI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAE8I,EAAE,CAAC;MAClBD,EAAE,GAAGnI,EAAE,CAACoE,GAAG,CAAC6D,EAAE,EAAEM,EAAE,CAAC;MACnBJ,EAAE,GAAGnI,EAAE,CAACqE,GAAG,CAAC6D,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBD,EAAE,GAAGlI,EAAE,CAACyI,GAAG,CAACH,EAAE,EAAEH,EAAE,CAAC;MACnBA,EAAE,GAAGnI,EAAE,CAACqE,GAAG,CAACiE,EAAE,EAAEH,EAAE,CAAC;MACnBA,EAAE,GAAGnI,EAAE,CAACoE,GAAG,CAAC8D,EAAE,EAAEC,EAAE,CAAC;MACnBD,EAAE,GAAGlI,EAAE,CAACoE,GAAG,CAACoE,EAAE,EAAEN,EAAE,CAAC;MACnBE,EAAE,GAAGpI,EAAE,CAACoE,GAAG,CAAC6D,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MACrBG,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAEiJ,EAAE,CAAC;MAClBC,EAAE,GAAGxI,EAAE,CAACyI,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC;MACnBC,EAAE,GAAGxI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAEkJ,EAAE,CAAC;MAClBA,EAAE,GAAGxI,EAAE,CAACqE,GAAG,CAACmE,EAAE,EAAEJ,EAAE,CAAC;MACnBA,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAACgE,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGrI,EAAE,CAACqE,GAAG,CAAC+D,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAGrI,EAAE,CAACqE,GAAG,CAACgE,EAAE,EAAEE,EAAE,CAAC;MACnBF,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACiE,EAAE,EAAEG,EAAE,CAAC;MACnBL,EAAE,GAAGnI,EAAE,CAACqE,GAAG,CAAC8D,EAAE,EAAEE,EAAE,CAAC;MACnBE,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAACmD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBe,EAAE,GAAGvI,EAAE,CAACqE,GAAG,CAACkE,EAAE,EAAEA,EAAE,CAAC;MACnBF,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACmE,EAAE,EAAEC,EAAE,CAAC;MACnBN,EAAE,GAAGlI,EAAE,CAACyI,GAAG,CAACP,EAAE,EAAEG,EAAE,CAAC;MACnBD,EAAE,GAAGpI,EAAE,CAACoE,GAAG,CAACmE,EAAE,EAAED,EAAE,CAAC;MACnBF,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAAC+D,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAAC+D,EAAE,EAAEA,EAAE,CAAC;MACnB,OAAO,IAAI7C,KAAK,CAAC2C,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9B;IAEA;IACA;IACA;IACA;IACA/D,GAAGA,CAACiB,KAAY;MACdD,cAAc,CAACC,KAAK,CAAC;MACrB,MAAM;QAAEE,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B,EAAE;QAAE7B,EAAE,EAAE8B;MAAE,CAAE,GAAG,IAAI;MACvC,MAAM;QAAEhC,EAAE,EAAEiC,EAAE;QAAEhC,EAAE,EAAEiC,EAAE;QAAEhC,EAAE,EAAEiC;MAAE,CAAE,GAAGrC,KAAK;MACxC,IAAI4C,EAAE,GAAGlI,EAAE,CAACE,IAAI;QAAEiI,EAAE,GAAGnI,EAAE,CAACE,IAAI;QAAEkI,EAAE,GAAGpI,EAAE,CAACE,IAAI,CAAC,CAAC;MAC9C,MAAMZ,CAAC,GAAG8D,KAAK,CAAC9D,CAAC;MACjB,MAAM2I,EAAE,GAAGjI,EAAE,CAACoE,GAAG,CAAChB,KAAK,CAAC7D,CAAC,EAAE0D,GAAG,CAAC;MAC/B,IAAIoF,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACkD,EAAE,EAAEG,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIa,EAAE,GAAGtI,EAAE,CAACoE,GAAG,CAACmD,EAAE,EAAEG,EAAE,CAAC;MACvB,IAAIa,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAACoD,EAAE,EAAEG,EAAE,CAAC;MACvB,IAAIa,EAAE,GAAGxI,EAAE,CAACqE,GAAG,CAACiD,EAAE,EAAEC,EAAE,CAAC;MACvB,IAAImB,EAAE,GAAG1I,EAAE,CAACqE,GAAG,CAACoD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACzBc,EAAE,GAAGxI,EAAE,CAACoE,GAAG,CAACoE,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAG1I,EAAE,CAACqE,GAAG,CAACgE,EAAE,EAAEC,EAAE,CAAC;MACnBE,EAAE,GAAGxI,EAAE,CAACyI,GAAG,CAACD,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAG1I,EAAE,CAACqE,GAAG,CAACiD,EAAE,EAAEE,EAAE,CAAC;MACnB,IAAImB,EAAE,GAAG3I,EAAE,CAACqE,GAAG,CAACoD,EAAE,EAAEE,EAAE,CAAC,CAAC,CAAC;MACzBe,EAAE,GAAG1I,EAAE,CAACoE,GAAG,CAACsE,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAG3I,EAAE,CAACqE,GAAG,CAACgE,EAAE,EAAEE,EAAE,CAAC;MACnBG,EAAE,GAAG1I,EAAE,CAACyI,GAAG,CAACC,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAG3I,EAAE,CAACqE,GAAG,CAACkD,EAAE,EAAEC,EAAE,CAAC;MACnBU,EAAE,GAAGlI,EAAE,CAACqE,GAAG,CAACqD,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrBgB,EAAE,GAAG3I,EAAE,CAACoE,GAAG,CAACuE,EAAE,EAAET,EAAE,CAAC;MACnBA,EAAE,GAAGlI,EAAE,CAACqE,GAAG,CAACiE,EAAE,EAAEC,EAAE,CAAC;MACnBI,EAAE,GAAG3I,EAAE,CAACyI,GAAG,CAACE,EAAE,EAAET,EAAE,CAAC;MACnBE,EAAE,GAAGpI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAEoJ,EAAE,CAAC;MAClBR,EAAE,GAAGlI,EAAE,CAACoE,GAAG,CAAC6D,EAAE,EAAEM,EAAE,CAAC,CAAC,CAAC;MACrBH,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAAC6D,EAAE,EAAEE,EAAE,CAAC;MACnBF,EAAE,GAAGlI,EAAE,CAACyI,GAAG,CAACH,EAAE,EAAEF,EAAE,CAAC;MACnBA,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAACiE,EAAE,EAAEF,EAAE,CAAC;MACnBD,EAAE,GAAGnI,EAAE,CAACoE,GAAG,CAAC8D,EAAE,EAAEE,EAAE,CAAC;MACnBE,EAAE,GAAGtI,EAAE,CAACqE,GAAG,CAACgE,EAAE,EAAEA,EAAE,CAAC,CAAC,CAAC;MACrBC,EAAE,GAAGtI,EAAE,CAACqE,GAAG,CAACiE,EAAE,EAAED,EAAE,CAAC;MACnBE,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAEiJ,EAAE,CAAC;MAClBG,EAAE,GAAG1I,EAAE,CAACoE,GAAG,CAAC6D,EAAE,EAAES,EAAE,CAAC;MACnBJ,EAAE,GAAGtI,EAAE,CAACqE,GAAG,CAACiE,EAAE,EAAEC,EAAE,CAAC;MACnBA,EAAE,GAAGvI,EAAE,CAACyI,GAAG,CAACJ,EAAE,EAAEE,EAAE,CAAC,CAAC,CAAC;MACrBA,EAAE,GAAGvI,EAAE,CAACoE,GAAG,CAAC9E,CAAC,EAAEiJ,EAAE,CAAC;MAClBG,EAAE,GAAG1I,EAAE,CAACqE,GAAG,CAACqE,EAAE,EAAEH,EAAE,CAAC;MACnBF,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACkE,EAAE,EAAEI,EAAE,CAAC;MACnBP,EAAE,GAAGnI,EAAE,CAACqE,GAAG,CAAC8D,EAAE,EAAEE,EAAE,CAAC;MACnBA,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACuE,EAAE,EAAED,EAAE,CAAC,CAAC,CAAC;MACrBR,EAAE,GAAGlI,EAAE,CAACoE,GAAG,CAACoE,EAAE,EAAEN,EAAE,CAAC;MACnBA,EAAE,GAAGlI,EAAE,CAACyI,GAAG,CAACP,EAAE,EAAEG,EAAE,CAAC;MACnBA,EAAE,GAAGrI,EAAE,CAACoE,GAAG,CAACoE,EAAE,EAAEF,EAAE,CAAC;MACnBF,EAAE,GAAGpI,EAAE,CAACoE,GAAG,CAACuE,EAAE,EAAEP,EAAE,CAAC;MACnBA,EAAE,GAAGpI,EAAE,CAACqE,GAAG,CAAC+D,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACrB,OAAO,IAAI9C,KAAK,CAAC2C,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC9B;IAEAQ,QAAQA,CAACtD,KAAY;MACnB,OAAO,IAAI,CAACjB,GAAG,CAACiB,KAAK,CAACwC,MAAM,EAAE,CAAC;IACjC;IAEQhC,GAAGA,CAAA;MACT,OAAO,IAAI,CAACuB,MAAM,CAAC9B,KAAK,CAACrF,IAAI,CAAC;IAChC;IACQlB,IAAIA,CAACyF,CAAS;MACpB,OAAOoE,IAAI,CAACC,UAAU,CAAC,IAAI,EAAE3D,gBAAgB,EAAEV,CAAC,EAAGsE,IAAa,IAAI;QAClE,MAAM5C,KAAK,GAAGnG,EAAE,CAACoG,WAAW,CAAC2C,IAAI,CAAC1C,GAAG,CAAER,CAAC,IAAKA,CAAC,CAACH,EAAE,CAAC,CAAC;QACnD,OAAOqD,IAAI,CAAC1C,GAAG,CAAC,CAACR,CAAC,EAAEE,CAAC,KAAKF,CAAC,CAACrC,QAAQ,CAAC2C,KAAK,CAACJ,CAAC,CAAC,CAAC,CAAC,CAACM,GAAG,CAACd,KAAK,CAACK,UAAU,CAAC;MACvE,CAAC,CAAC;IACJ;IAEA;;;;;IAKAoD,cAAcA,CAACvE,CAAS;MACtB,MAAMwE,CAAC,GAAG1D,KAAK,CAACrF,IAAI;MACpB,IAAIuE,CAAC,KAAK5B,GAAG,EAAE,OAAOoG,CAAC;MACvBvE,QAAQ,CAACD,CAAC,CAAC,CAAC,CAAC;MACb,IAAIA,CAAC,KAAK1B,GAAG,EAAE,OAAO,IAAI;MAC1B,MAAM;QAAEhD;MAAI,CAAE,GAAGqD,KAAK;MACtB,IAAI,CAACrD,IAAI,EAAE,OAAO8I,IAAI,CAACK,YAAY,CAAC,IAAI,EAAEzE,CAAC,CAAC;MAE5C;MACA,IAAI;QAAE0E,KAAK;QAAEC,EAAE;QAAEC,KAAK;QAAEC;MAAE,CAAE,GAAGvJ,IAAI,CAACM,WAAW,CAACoE,CAAC,CAAC;MAClD,IAAI8E,GAAG,GAAGN,CAAC;MACX,IAAIO,GAAG,GAAGP,CAAC;MACX,IAAIzH,CAAC,GAAU,IAAI;MACnB,OAAO4H,EAAE,GAAGvG,GAAG,IAAIyG,EAAE,GAAGzG,GAAG,EAAE;QAC3B,IAAIuG,EAAE,GAAGrG,GAAG,EAAEwG,GAAG,GAAGA,GAAG,CAAClF,GAAG,CAAC7C,CAAC,CAAC;QAC9B,IAAI8H,EAAE,GAAGvG,GAAG,EAAEyG,GAAG,GAAGA,GAAG,CAACnF,GAAG,CAAC7C,CAAC,CAAC;QAC9BA,CAAC,GAAGA,CAAC,CAACwG,MAAM,EAAE;QACdoB,EAAE,KAAKrG,GAAG;QACVuG,EAAE,KAAKvG,GAAG;;MAEZ,IAAIoG,KAAK,EAAEI,GAAG,GAAGA,GAAG,CAACzB,MAAM,EAAE;MAC7B,IAAIuB,KAAK,EAAEG,GAAG,GAAGA,GAAG,CAAC1B,MAAM,EAAE;MAC7B0B,GAAG,GAAG,IAAIjE,KAAK,CAACvF,EAAE,CAACoE,GAAG,CAACoF,GAAG,CAAChE,EAAE,EAAEzF,IAAI,CAACK,IAAI,CAAC,EAAEoJ,GAAG,CAAC/D,EAAE,EAAE+D,GAAG,CAAC9D,EAAE,CAAC;MAC1D,OAAO6D,GAAG,CAAClF,GAAG,CAACmF,GAAG,CAAC;IACrB;IAEA;;;;;;;;;IASA5C,QAAQA,CAAC6C,MAAc;MACrB/E,QAAQ,CAAC+E,MAAM,CAAC;MAChB,IAAIhF,CAAC,GAAGgF,MAAM;MACd,IAAInG,KAAY,EAAEoG,IAAW,CAAC,CAAC;MAC/B,MAAM;QAAE3J;MAAI,CAAE,GAAGqD,KAAK;MACtB,IAAIrD,IAAI,EAAE;QACR,MAAM;UAAEoJ,KAAK;UAAEC,EAAE;UAAEC,KAAK;UAAEC;QAAE,CAAE,GAAGvJ,IAAI,CAACM,WAAW,CAACoE,CAAC,CAAC;QACpD,IAAI;UAAEoB,CAAC,EAAE0D,GAAG;UAAEI,CAAC,EAAEC;QAAG,CAAE,GAAG,IAAI,CAAC5K,IAAI,CAACoK,EAAE,CAAC;QACtC,IAAI;UAAEvD,CAAC,EAAE2D,GAAG;UAAEG,CAAC,EAAEE;QAAG,CAAE,GAAG,IAAI,CAAC7K,IAAI,CAACsK,EAAE,CAAC;QACtCC,GAAG,GAAGV,IAAI,CAACiB,eAAe,CAACX,KAAK,EAAEI,GAAG,CAAC;QACtCC,GAAG,GAAGX,IAAI,CAACiB,eAAe,CAACT,KAAK,EAAEG,GAAG,CAAC;QACtCA,GAAG,GAAG,IAAIjE,KAAK,CAACvF,EAAE,CAACoE,GAAG,CAACoF,GAAG,CAAChE,EAAE,EAAEzF,IAAI,CAACK,IAAI,CAAC,EAAEoJ,GAAG,CAAC/D,EAAE,EAAE+D,GAAG,CAAC9D,EAAE,CAAC;QAC1DpC,KAAK,GAAGiG,GAAG,CAAClF,GAAG,CAACmF,GAAG,CAAC;QACpBE,IAAI,GAAGE,GAAG,CAACvF,GAAG,CAACwF,GAAG,CAAC;OACpB,MAAM;QACL,MAAM;UAAEhE,CAAC;UAAE8D;QAAC,CAAE,GAAG,IAAI,CAAC3K,IAAI,CAACyF,CAAC,CAAC;QAC7BnB,KAAK,GAAGuC,CAAC;QACT6D,IAAI,GAAGC,CAAC;;MAEV;MACA,OAAOpE,KAAK,CAACU,UAAU,CAAC,CAAC3C,KAAK,EAAEoG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3C;IAEA;;;;;;IAMAK,oBAAoBA,CAACC,CAAQ,EAAE1K,CAAS,EAAEC,CAAS;MACjD,MAAM0K,CAAC,GAAG1E,KAAK,CAACoB,IAAI,CAAC,CAAC;MACtB,MAAMvC,GAAG,GAAGA,CACVmC,CAAQ,EACRjH,CAAS,CAAC;MAAA,KACNA,CAAC,KAAKuD,GAAG,IAAIvD,CAAC,KAAKyD,GAAG,IAAI,CAACwD,CAAC,CAACc,MAAM,CAAC4C,CAAC,CAAC,GAAG1D,CAAC,CAACyC,cAAc,CAAC1J,CAAC,CAAC,GAAGiH,CAAC,CAACK,QAAQ,CAACtH,CAAC,CAAE;MACnF,MAAM4K,GAAG,GAAG9F,GAAG,CAAC,IAAI,EAAE9E,CAAC,CAAC,CAAC+E,GAAG,CAACD,GAAG,CAAC4F,CAAC,EAAEzK,CAAC,CAAC,CAAC;MACvC,OAAO2K,GAAG,CAACpE,GAAG,EAAE,GAAGqE,SAAS,GAAGD,GAAG;IACpC;IAEA;IACA;IACA;IACA1G,QAAQA,CAAC4G,EAAM;MACb,MAAM;QAAE5E,EAAE,EAAE7B,CAAC;QAAE8B,EAAE,EAAE7B,CAAC;QAAE8B,EAAE,EAAE2E;MAAC,CAAE,GAAG,IAAI;MACpC,MAAMvE,GAAG,GAAG,IAAI,CAACA,GAAG,EAAE;MACtB;MACA;MACA,IAAIsE,EAAE,IAAI,IAAI,EAAEA,EAAE,GAAGtE,GAAG,GAAG9F,EAAE,CAACgG,GAAG,GAAGhG,EAAE,CAACsK,GAAG,CAACD,CAAC,CAAC;MAC7C,MAAME,EAAE,GAAGvK,EAAE,CAACoE,GAAG,CAACT,CAAC,EAAEyG,EAAE,CAAC;MACxB,MAAMI,EAAE,GAAGxK,EAAE,CAACoE,GAAG,CAACR,CAAC,EAAEwG,EAAE,CAAC;MACxB,MAAMK,EAAE,GAAGzK,EAAE,CAACoE,GAAG,CAACiG,CAAC,EAAED,EAAE,CAAC;MACxB,IAAItE,GAAG,EAAE,OAAO;QAAEnC,CAAC,EAAE3D,EAAE,CAACE,IAAI;QAAE0D,CAAC,EAAE5D,EAAE,CAACE;MAAI,CAAE;MAC1C,IAAI,CAACF,EAAE,CAACC,GAAG,CAACwK,EAAE,EAAEzK,EAAE,CAACgG,GAAG,CAAC,EAAE,MAAM,IAAI7F,KAAK,CAAC,kBAAkB,CAAC;MAC5D,OAAO;QAAEwD,CAAC,EAAE4G,EAAE;QAAE3G,CAAC,EAAE4G;MAAE,CAAE;IACzB;IACA9K,aAAaA,CAAA;MACX,MAAM;QAAE4C,CAAC,EAAEoI,QAAQ;QAAEhL;MAAa,CAAE,GAAG0D,KAAK;MAC5C,IAAIsH,QAAQ,KAAK3H,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;MACnC,IAAIrD,aAAa,EAAE,OAAOA,aAAa,CAAC6F,KAAK,EAAE,IAAI,CAAC;MACpD,MAAM,IAAIpF,KAAK,CAAC,8DAA8D,CAAC;IACjF;IACAR,aAAaA,CAAA;MACX,MAAM;QAAE2C,CAAC,EAAEoI,QAAQ;QAAE/K;MAAa,CAAE,GAAGyD,KAAK;MAC5C,IAAIsH,QAAQ,KAAK3H,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;MACnC,IAAIpD,aAAa,EAAE,OAAOA,aAAa,CAAC4F,KAAK,EAAE,IAAI,CAAU;MAC7D,OAAO,IAAI,CAACyD,cAAc,CAAC5F,KAAK,CAACd,CAAC,CAAC;IACrC;IAEAqI,UAAUA,CAACC,YAAY,GAAG,IAAI;MAC5B,IAAI,CAACpE,cAAc,EAAE;MACrB,OAAO1G,OAAO,CAACyF,KAAK,EAAE,IAAI,EAAEqF,YAAY,CAAC;IAC3C;IAEAC,KAAKA,CAACD,YAAY,GAAG,IAAI;MACvB,OAAO9L,EAAE,CAACiG,UAAU,CAAC,IAAI,CAAC4F,UAAU,CAACC,YAAY,CAAC,CAAC;IACrD;;EA9UgBrF,KAAA,CAAAoB,IAAI,GAAG,IAAIpB,KAAK,CAACnC,KAAK,CAACmB,EAAE,EAAEnB,KAAK,CAACkB,EAAE,EAAEtE,EAAE,CAACgG,GAAG,CAAC;EAC5CT,KAAA,CAAArF,IAAI,GAAG,IAAIqF,KAAK,CAACvF,EAAE,CAACE,IAAI,EAAEF,EAAE,CAACgG,GAAG,EAAEhG,EAAE,CAACE,IAAI,CAAC;EA+U5D,MAAM4K,KAAK,GAAG1H,KAAK,CAAC2H,UAAU;EAC9B,MAAMlC,IAAI,GAAG7J,IAAI,CAACuG,KAAK,EAAEnC,KAAK,CAACrD,IAAI,GAAGiL,IAAI,CAACC,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC,GAAGA,KAAK,CAAC;EACnE;EACA,OAAO;IACL1H,KAAK;IACL8H,eAAe,EAAE3F,KAA2B;IAC5CZ,sBAAsB;IACtBX,mBAAmB;IACnBQ;GACD;AACH;AAwCA,SAAS2G,YAAYA,CAAChM,KAAgB;EACpC,MAAMC,IAAI,GAAGH,aAAa,CAACE,KAAK,CAAC;EACjCL,EAAE,CAACO,cAAc,CACfD,IAAI,EACJ;IACEgM,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE;GACd,EACD;IACEC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,UAAU;IACzBC,IAAI,EAAE;GACP,CACF;EACD,OAAOnL,MAAM,CAACC,MAAM,CAAC;IAAEkL,IAAI,EAAE,IAAI;IAAE,GAAGrM;EAAI,CAAW,CAAC;AACxD;AAkBA,OAAM,SAAUsM,WAAWA,CAACC,QAAmB;EAC7C,MAAMvI,KAAK,GAAG+H,YAAY,CAACQ,QAAQ,CAAoC;EACvE,MAAM;IAAE3L,EAAE;IAAEyE,CAAC,EAAEmH;EAAW,CAAE,GAAGxI,KAAK;EACpC,MAAMyI,aAAa,GAAG7L,EAAE,CAAC+D,KAAK,GAAG,CAAC,CAAC,CAAC;EACpC,MAAM+H,eAAe,GAAG,CAAC,GAAG9L,EAAE,CAAC+D,KAAK,GAAG,CAAC,CAAC,CAAC;EAE1C,SAASgI,mBAAmBA,CAACxJ,GAAW;IACtC,OAAOM,GAAG,GAAGN,GAAG,IAAIA,GAAG,GAAGvC,EAAE,CAACgM,KAAK,CAAC,CAAC;EACtC;EACA,SAASC,IAAIA,CAAC3M,CAAS;IACrB,OAAOT,GAAG,CAACA,GAAG,CAACS,CAAC,EAAEsM,WAAW,CAAC;EAChC;EACA,SAASM,IAAIA,CAAC5M,CAAS;IACrB,OAAOT,GAAG,CAACsN,MAAM,CAAC7M,CAAC,EAAEsM,WAAW,CAAC;EACnC;EAEA,MAAM;IACJV,eAAe,EAAE3F,KAAK;IACtBZ,sBAAsB;IACtBX,mBAAmB;IACnBQ;EAAkB,CACnB,GAAGrB,iBAAiB,CAAC;IACpB,GAAGC,KAAK;IACRtD,OAAOA,CAACuD,EAAE,EAAEC,KAAK,EAAEsH,YAAqB;MACtC,MAAMtL,CAAC,GAAGgE,KAAK,CAACE,QAAQ,EAAE;MAC1B,MAAMG,CAAC,GAAG3D,EAAE,CAACF,OAAO,CAACR,CAAC,CAACqE,CAAC,CAAC;MACzB,MAAMyI,GAAG,GAAGtN,EAAE,CAAC2E,WAAW;MAC1B,IAAImH,YAAY,EAAE;QAChB,OAAOwB,GAAG,CAACxK,UAAU,CAAC8B,IAAI,CAAC,CAACJ,KAAK,CAAC6D,QAAQ,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,EAAExD,CAAC,CAAC;OACjE,MAAM;QACL,OAAOyI,GAAG,CAACxK,UAAU,CAAC8B,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEC,CAAC,EAAE3D,EAAE,CAACF,OAAO,CAACR,CAAC,CAACsE,CAAC,CAAC,CAAC;;IAE3D,CAAC;IACD/D,SAASA,CAACgE,KAAiB;MACzB,MAAMxC,GAAG,GAAGwC,KAAK,CAACzC,MAAM;MACxB,MAAMiL,IAAI,GAAGxI,KAAK,CAAC,CAAC,CAAC;MACrB,MAAMC,IAAI,GAAGD,KAAK,CAACtC,QAAQ,CAAC,CAAC,CAAC;MAC9B;MACA,IAAIF,GAAG,KAAKwK,aAAa,KAAKQ,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;QAC7D,MAAM1I,CAAC,GAAG7E,EAAE,CAAC0B,eAAe,CAACsD,IAAI,CAAC;QAClC,IAAI,CAACiI,mBAAmB,CAACpI,CAAC,CAAC,EAAE,MAAM,IAAIxD,KAAK,CAAC,uBAAuB,CAAC;QACrE,MAAMmM,EAAE,GAAGtI,mBAAmB,CAACL,CAAC,CAAC,CAAC,CAAC;QACnC,IAAIC,CAAC,GAAG5D,EAAE,CAACuM,IAAI,CAACD,EAAE,CAAC,CAAC,CAAC;QACrB,MAAME,MAAM,GAAG,CAAC5I,CAAC,GAAGb,GAAG,MAAMA,GAAG;QAChC;QACA,MAAM0J,SAAS,GAAG,CAACJ,IAAI,GAAG,CAAC,MAAM,CAAC;QAClC,IAAII,SAAS,KAAKD,MAAM,EAAE5I,CAAC,GAAG5D,EAAE,CAAC+H,GAAG,CAACnE,CAAC,CAAC;QACvC,OAAO;UAAED,CAAC;UAAEC;QAAC,CAAE;OAChB,MAAM,IAAIvC,GAAG,KAAKyK,eAAe,IAAIO,IAAI,KAAK,IAAI,EAAE;QACnD,MAAM1I,CAAC,GAAG3D,EAAE,CAACH,SAAS,CAACiE,IAAI,CAACvC,QAAQ,CAAC,CAAC,EAAEvB,EAAE,CAAC+D,KAAK,CAAC,CAAC;QAClD,MAAMH,CAAC,GAAG5D,EAAE,CAACH,SAAS,CAACiE,IAAI,CAACvC,QAAQ,CAACvB,EAAE,CAAC+D,KAAK,EAAE,CAAC,GAAG/D,EAAE,CAAC+D,KAAK,CAAC,CAAC;QAC7D,OAAO;UAAEJ,CAAC;UAAEC;QAAC,CAAE;OAChB,MAAM;QACL,MAAM,IAAIzD,KAAK,CACb,mBAAmBkB,GAAG,0BAA0BwK,aAAa,wBAAwBC,eAAe,qBAAqB,CAC1H;;IAEL;GACD,CAAC;EACF,MAAMY,aAAa,GAAInK,GAAW,IAChCzD,EAAE,CAACiG,UAAU,CAACjG,EAAE,CAAC6N,eAAe,CAACpK,GAAG,EAAEa,KAAK,CAAC0B,WAAW,CAAC,CAAC;EAE3D,SAAS8H,qBAAqBA,CAACC,MAAc;IAC3C,MAAMC,IAAI,GAAGlB,WAAW,IAAI7I,GAAG;IAC/B,OAAO8J,MAAM,GAAGC,IAAI;EACtB;EAEA,SAASC,UAAUA,CAAChL,CAAS;IAC3B,OAAO6K,qBAAqB,CAAC7K,CAAC,CAAC,GAAGkK,IAAI,CAAC,CAAClK,CAAC,CAAC,GAAGA,CAAC;EAChD;EACA;EACA,MAAMiL,MAAM,GAAGA,CAACzN,CAAa,EAAEmE,IAAY,EAAEuJ,EAAU,KAAKnO,EAAE,CAAC0B,eAAe,CAACjB,CAAC,CAAC4C,KAAK,CAACuB,IAAI,EAAEuJ,EAAE,CAAC,CAAC;EAEjG;;;EAGA,MAAMC,SAAS;IACbnM,YAAqBc,CAAS,EAAWE,CAAS,EAAWoL,QAAiB;MAAzD,KAAAtL,CAAC,GAADA,CAAC;MAAmB,KAAAE,CAAC,GAADA,CAAC;MAAmB,KAAAoL,QAAQ,GAARA,QAAQ;MACnE,IAAI,CAAC3G,cAAc,EAAE;IACvB;IAEA;IACA,OAAO4G,WAAWA,CAACzL,GAAQ;MACzB,MAAMF,CAAC,GAAG2B,KAAK,CAAC0B,WAAW;MAC3BnD,GAAG,GAAG5C,WAAW,CAAC,kBAAkB,EAAE4C,GAAG,EAAEF,CAAC,GAAG,CAAC,CAAC;MACjD,OAAO,IAAIyL,SAAS,CAACF,MAAM,CAACrL,GAAG,EAAE,CAAC,EAAEF,CAAC,CAAC,EAAEuL,MAAM,CAACrL,GAAG,EAAEF,CAAC,EAAE,CAAC,GAAGA,CAAC,CAAC,CAAC;IAChE;IAEA;IACA;IACA,OAAO4L,OAAOA,CAAC1L,GAAQ;MACrB,MAAM;QAAEE,CAAC;QAAEE;MAAC,CAAE,GAAGnB,GAAG,CAACc,KAAK,CAAC3C,WAAW,CAAC,KAAK,EAAE4C,GAAG,CAAC,CAAC;MACnD,OAAO,IAAIuL,SAAS,CAACrL,CAAC,EAAEE,CAAC,CAAC;IAC5B;IAEAyE,cAAcA,CAAA;MACZ;MACA,IAAI,CAAChC,kBAAkB,CAAC,IAAI,CAAC3C,CAAC,CAAC,EAAE,MAAM,IAAI1B,KAAK,CAAC,2BAA2B,CAAC;MAC7E,IAAI,CAACqE,kBAAkB,CAAC,IAAI,CAACzC,CAAC,CAAC,EAAE,MAAM,IAAI5B,KAAK,CAAC,2BAA2B,CAAC;IAC/E;IAEAmN,cAAcA,CAACH,QAAgB;MAC7B,OAAO,IAAID,SAAS,CAAC,IAAI,CAACrL,CAAC,EAAE,IAAI,CAACE,CAAC,EAAEoL,QAAQ,CAAuB;IACtE;IAEAI,gBAAgBA,CAACC,OAAY;MAC3B,MAAM;QAAE3L,CAAC;QAAEE,CAAC;QAAEoL,QAAQ,EAAEM;MAAG,CAAE,GAAG,IAAI;MACpC,MAAMnL,CAAC,GAAGkJ,aAAa,CAACzM,WAAW,CAAC,SAAS,EAAEyO,OAAO,CAAC,CAAC,CAAC,CAAC;MAC1D,IAAIC,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACzI,QAAQ,CAACyI,GAAG,CAAC,EAAE,MAAM,IAAItN,KAAK,CAAC,qBAAqB,CAAC;MACtF,MAAMuN,IAAI,GAAGD,GAAG,KAAK,CAAC,IAAIA,GAAG,KAAK,CAAC,GAAG5L,CAAC,GAAGuB,KAAK,CAACqB,CAAC,GAAG5C,CAAC;MACrD,IAAI6L,IAAI,IAAI1N,EAAE,CAACgM,KAAK,EAAE,MAAM,IAAI7L,KAAK,CAAC,4BAA4B,CAAC;MACnE,MAAMwN,MAAM,GAAG,CAACF,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI;MAC5C,MAAMG,CAAC,GAAGrI,KAAK,CAACe,OAAO,CAACqH,MAAM,GAAGjB,aAAa,CAACgB,IAAI,CAAC,CAAC;MACrD,MAAMG,EAAE,GAAG3B,IAAI,CAACwB,IAAI,CAAC,CAAC,CAAC;MACvB,MAAMI,EAAE,GAAG7B,IAAI,CAAC,CAAC3J,CAAC,GAAGuL,EAAE,CAAC,CAAC,CAAC;MAC1B,MAAME,EAAE,GAAG9B,IAAI,CAAClK,CAAC,GAAG8L,EAAE,CAAC,CAAC,CAAC;MACzB,MAAM7D,CAAC,GAAGzE,KAAK,CAACoB,IAAI,CAACoD,oBAAoB,CAAC6D,CAAC,EAAEE,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;MACtD,IAAI,CAAC/D,CAAC,EAAE,MAAM,IAAI7J,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;MAC9C6J,CAAC,CAACxD,cAAc,EAAE;MAClB,OAAOwD,CAAC;IACV;IAEA;IACAgE,QAAQA,CAAA;MACN,OAAOpB,qBAAqB,CAAC,IAAI,CAAC7K,CAAC,CAAC;IACtC;IAEAgL,UAAUA,CAAA;MACR,OAAO,IAAI,CAACiB,QAAQ,EAAE,GAAG,IAAId,SAAS,CAAC,IAAI,CAACrL,CAAC,EAAEoK,IAAI,CAAC,CAAC,IAAI,CAAClK,CAAC,CAAC,EAAE,IAAI,CAACoL,QAAQ,CAAC,GAAG,IAAI;IACrF;IAEA;IACAc,aAAaA,CAAA;MACX,OAAOnP,EAAE,CAAC4B,UAAU,CAAC,IAAI,CAACwN,QAAQ,EAAE,CAAC;IACvC;IACAA,QAAQA,CAAA;MACN,OAAOtN,GAAG,CAACqB,UAAU,CAAC;QAAEJ,CAAC,EAAE,IAAI,CAACA,CAAC;QAAEE,CAAC,EAAE,IAAI,CAACA;MAAC,CAAE,CAAC;IACjD;IAEA;IACAoM,iBAAiBA,CAAA;MACf,OAAOrP,EAAE,CAAC4B,UAAU,CAAC,IAAI,CAAC0N,YAAY,EAAE,CAAC;IAC3C;IACAA,YAAYA,CAAA;MACV,OAAO1B,aAAa,CAAC,IAAI,CAAC7K,CAAC,CAAC,GAAG6K,aAAa,CAAC,IAAI,CAAC3K,CAAC,CAAC;IACtD;;EAIF,MAAMsM,KAAK,GAAG;IACZC,iBAAiBA,CAAC5H,UAAmB;MACnC,IAAI;QACF/B,sBAAsB,CAAC+B,UAAU,CAAC;QAClC,OAAO,IAAI;OACZ,CAAC,OAAOxB,KAAK,EAAE;QACd,OAAO,KAAK;;IAEhB,CAAC;IACDP,sBAAsB,EAAEA,sBAAsB;IAE9C;;;;IAIA4J,gBAAgB,EAAEA,CAAA,KAAiB;MACjC,MAAMnN,MAAM,GAAGvC,GAAG,CAAC2P,gBAAgB,CAACpL,KAAK,CAACqB,CAAC,CAAC;MAC5C,OAAO5F,GAAG,CAAC4P,cAAc,CAACrL,KAAK,CAACkI,WAAW,CAAClK,MAAM,CAAC,EAAEgC,KAAK,CAACqB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;;;;;IAQAiK,UAAUA,CAAC5H,UAAU,GAAG,CAAC,EAAExD,KAAK,GAAGiC,KAAK,CAACoB,IAAI;MAC3CrD,KAAK,CAACuD,cAAc,CAACC,UAAU,CAAC;MAChCxD,KAAK,CAACsD,QAAQ,CAAC9D,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,OAAOQ,KAAK;IACd;GACD;EAED;;;;;;EAMA,SAASqL,YAAYA,CAACjI,UAAmB,EAAEkE,YAAY,GAAG,IAAI;IAC5D,OAAOrF,KAAK,CAACkB,cAAc,CAACC,UAAU,CAAC,CAACiE,UAAU,CAACC,YAAY,CAAC;EAClE;EAEA;;;EAGA,SAASgE,SAASA,CAACC,IAAsB;IACvC,MAAMC,GAAG,GAAGD,IAAI,YAAYjN,UAAU;IACtC,MAAMmN,GAAG,GAAG,OAAOF,IAAI,KAAK,QAAQ;IACpC,MAAMxN,GAAG,GAAG,CAACyN,GAAG,IAAIC,GAAG,KAAMF,IAAY,CAACzN,MAAM;IAChD,IAAI0N,GAAG,EAAE,OAAOzN,GAAG,KAAKwK,aAAa,IAAIxK,GAAG,KAAKyK,eAAe;IAChE,IAAIiD,GAAG,EAAE,OAAO1N,GAAG,KAAK,CAAC,GAAGwK,aAAa,IAAIxK,GAAG,KAAK,CAAC,GAAGyK,eAAe;IACxE,IAAI+C,IAAI,YAAYtJ,KAAK,EAAE,OAAO,IAAI;IACtC,OAAO,KAAK;EACd;EAEA;;;;;;;;;;EAUA,SAASyJ,eAAeA,CAACC,QAAiB,EAAEC,OAAY,EAAEtE,YAAY,GAAG,IAAI;IAC3E,IAAIgE,SAAS,CAACK,QAAQ,CAAC,EAAE,MAAM,IAAI9O,KAAK,CAAC,+BAA+B,CAAC;IACzE,IAAI,CAACyO,SAAS,CAACM,OAAO,CAAC,EAAE,MAAM,IAAI/O,KAAK,CAAC,+BAA+B,CAAC;IACzE,MAAMZ,CAAC,GAAGgG,KAAK,CAACe,OAAO,CAAC4I,OAAO,CAAC,CAAC,CAAC;IAClC,OAAO3P,CAAC,CAACqH,QAAQ,CAACjC,sBAAsB,CAACsK,QAAQ,CAAC,CAAC,CAACtE,UAAU,CAACC,YAAY,CAAC;EAC9E;EAEA;EACA;EACA;EACA;EACA,MAAMW,QAAQ,GACZnI,KAAK,CAACmI,QAAQ,IACd,UAAU1H,KAAiB;IACzB;IACA;IACA,MAAMtB,GAAG,GAAGzD,EAAE,CAAC0B,eAAe,CAACqD,KAAK,CAAC,CAAC,CAAC;IACvC,MAAMsL,KAAK,GAAGtL,KAAK,CAACzC,MAAM,GAAG,CAAC,GAAGgC,KAAK,CAAC2H,UAAU,CAAC,CAAC;IACnD,OAAOoE,KAAK,GAAG,CAAC,GAAG5M,GAAG,IAAIO,MAAM,CAACqM,KAAK,CAAC,GAAG5M,GAAG;EAC/C,CAAC;EACH,MAAMiJ,aAAa,GACjBpI,KAAK,CAACoI,aAAa,IACnB,UAAU3H,KAAiB;IACzB,OAAOoI,IAAI,CAACV,QAAQ,CAAC1H,KAAK,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EACH;EACA,MAAMuL,UAAU,GAAGtQ,EAAE,CAACuQ,OAAO,CAACjM,KAAK,CAAC2H,UAAU,CAAC;EAC/C;;;EAGA,SAASuE,UAAUA,CAAC/M,GAAW;IAC7B,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,MAAM,IAAIpC,KAAK,CAAC,iBAAiB,CAAC;IAC/D,IAAI,EAAE0C,GAAG,IAAIN,GAAG,IAAIA,GAAG,GAAG6M,UAAU,CAAC,EACnC,MAAM,IAAIjP,KAAK,CAAC,uBAAuBiD,KAAK,CAAC2H,UAAU,EAAE,CAAC;IAC5D;IACA,OAAOjM,EAAE,CAAC6N,eAAe,CAACpK,GAAG,EAAEa,KAAK,CAAC0B,WAAW,CAAC;EACnD;EAEA;EACA;EACA;EACA;EACA;EACA,SAASyK,OAAOA,CAAC/B,OAAY,EAAE9G,UAAmB,EAAEtH,IAAI,GAAGoQ,cAAc;IACvE,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,IAAItQ,IAAI,CAAC,EACnD,MAAM,IAAIe,KAAK,CAAC,qCAAqC,CAAC;IACxD,MAAM;MAAEiL,IAAI;MAAEE;IAAW,CAAE,GAAGlI,KAAK;IACnC,IAAI;MAAEqI,IAAI;MAAEkE,OAAO;MAAEC,YAAY,EAAEC;IAAG,CAAE,GAAGzQ,IAAI,CAAC,CAAC;IACjD,IAAIqM,IAAI,IAAI,IAAI,EAAEA,IAAI,GAAG,IAAI,CAAC,CAAC;IAC/B+B,OAAO,GAAGzO,WAAW,CAAC,SAAS,EAAEyO,OAAO,CAAC;IACzC,IAAImC,OAAO,EAAEnC,OAAO,GAAGzO,WAAW,CAAC,mBAAmB,EAAEqM,IAAI,CAACoC,OAAO,CAAC,CAAC;IAEtE;IACA;IACA;IACA,MAAMsC,KAAK,GAAGtE,aAAa,CAACgC,OAAO,CAAC;IACpC,MAAMhM,CAAC,GAAGmD,sBAAsB,CAAC+B,UAAU,CAAC,CAAC,CAAC;IAC9C,MAAMqJ,QAAQ,GAAG,CAACT,UAAU,CAAC9N,CAAC,CAAC,EAAE8N,UAAU,CAACQ,KAAK,CAAC,CAAC;IACnD;IACA,IAAID,GAAG,IAAI,IAAI,EAAE;MACf;MACA,MAAMG,CAAC,GAAGH,GAAG,KAAK,IAAI,GAAGvE,WAAW,CAACtL,EAAE,CAAC+D,KAAK,CAAC,GAAG8L,GAAG,CAAC,CAAC;MACtDE,QAAQ,CAACE,IAAI,CAAClR,WAAW,CAAC,cAAc,EAAEiR,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjD,MAAME,IAAI,GAAGpR,EAAE,CAAC2E,WAAW,CAAC,GAAGsM,QAAQ,CAAC,CAAC,CAAC;IAC1C,MAAM/O,CAAC,GAAG8O,KAAK,CAAC,CAAC;IACjB;IACA,SAASK,KAAKA,CAACC,MAAkB;MAC/B;MACA,MAAMV,CAAC,GAAGnE,QAAQ,CAAC6E,MAAM,CAAC,CAAC,CAAC;MAC5B,IAAI,CAAC5L,kBAAkB,CAACkL,CAAC,CAAC,EAAE,OAAO,CAAC;MACpC,MAAMW,EAAE,GAAGnE,IAAI,CAACwD,CAAC,CAAC,CAAC,CAAC;MACpB,MAAMY,CAAC,GAAG/K,KAAK,CAACoB,IAAI,CAACC,QAAQ,CAAC8I,CAAC,CAAC,CAAClM,QAAQ,EAAE,CAAC,CAAC;MAC7C,MAAM3B,CAAC,GAAGoK,IAAI,CAACqE,CAAC,CAAC3M,CAAC,CAAC,CAAC,CAAC;MACrB,IAAI9B,CAAC,KAAKgB,GAAG,EAAE;MACf;MACA;MACA;MACA,MAAMd,CAAC,GAAGkK,IAAI,CAACoE,EAAE,GAAGpE,IAAI,CAACjL,CAAC,GAAGa,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,IAAIO,CAAC,KAAKc,GAAG,EAAE;MACf,IAAIsK,QAAQ,GAAG,CAACmD,CAAC,CAAC3M,CAAC,KAAK9B,CAAC,GAAG,CAAC,GAAG,CAAC,IAAIO,MAAM,CAACkO,CAAC,CAAC1M,CAAC,GAAGb,GAAG,CAAC,CAAC,CAAC;MACxD,IAAIwN,KAAK,GAAGxO,CAAC;MACb,IAAI0J,IAAI,IAAImB,qBAAqB,CAAC7K,CAAC,CAAC,EAAE;QACpCwO,KAAK,GAAGxD,UAAU,CAAChL,CAAC,CAAC,CAAC,CAAC;QACvBoL,QAAQ,IAAI,CAAC,CAAC,CAAC;;MAEjB,OAAO,IAAID,SAAS,CAACrL,CAAC,EAAE0O,KAAK,EAAEpD,QAAQ,CAAuB,CAAC,CAAC;IAClE;IACA,OAAO;MAAE+C,IAAI;MAAEC;IAAK,CAAE;EACxB;EACA,MAAMX,cAAc,GAAa;IAAE/D,IAAI,EAAErI,KAAK,CAACqI,IAAI;IAAEkE,OAAO,EAAE;EAAK,CAAE;EACrE,MAAMa,cAAc,GAAY;IAAE/E,IAAI,EAAErI,KAAK,CAACqI,IAAI;IAAEkE,OAAO,EAAE;EAAK,CAAE;EAEpE;;;;;;;;;;;;;EAaA,SAASc,IAAIA,CAACjD,OAAY,EAAEkD,OAAgB,EAAEtR,IAAI,GAAGoQ,cAAc;IACjE,MAAM;MAAEU,IAAI;MAAEC;IAAK,CAAE,GAAGZ,OAAO,CAAC/B,OAAO,EAAEkD,OAAO,EAAEtR,IAAI,CAAC,CAAC,CAAC;IACzD,MAAMuR,CAAC,GAAGvN,KAAK;IACf,MAAMwN,IAAI,GAAG9R,EAAE,CAAC+R,cAAc,CAAqBF,CAAC,CAACvF,IAAI,CAAC0F,SAAS,EAAEH,CAAC,CAAC7L,WAAW,EAAE6L,CAAC,CAACtF,IAAI,CAAC;IAC3F,OAAOuF,IAAI,CAACV,IAAI,EAAEC,KAAK,CAAC,CAAC,CAAC;EAC5B;EAEA;EACA5K,KAAK,CAACoB,IAAI,CAACE,cAAc,CAAC,CAAC,CAAC;EAC5B;EAEA;;;;;;;;;;;;;EAaA,SAASkK,MAAMA,CACbC,SAA8B,EAC9BxD,OAAY,EACZyD,SAAc,EACd7R,IAAI,GAAGoR,cAAc;IAErB,MAAMU,EAAE,GAAGF,SAAS;IACpBxD,OAAO,GAAGzO,WAAW,CAAC,SAAS,EAAEyO,OAAO,CAAC;IACzCyD,SAAS,GAAGlS,WAAW,CAAC,WAAW,EAAEkS,SAAS,CAAC;IAC/C,IAAI,QAAQ,IAAI7R,IAAI,EAAE,MAAM,IAAIe,KAAK,CAAC,oCAAoC,CAAC;IAC3E,MAAM;MAAEsL,IAAI;MAAEkE;IAAO,CAAE,GAAGvQ,IAAI;IAE9B,IAAI+R,IAAI,GAA0BhH,SAAS;IAC3C,IAAI5D,CAAwB;IAC5B,IAAI;MACF,IAAI,OAAO2K,EAAE,KAAK,QAAQ,IAAIA,EAAE,YAAYtP,UAAU,EAAE;QACtD;QACA;QACA,IAAI;UACFuP,IAAI,GAAGjE,SAAS,CAACG,OAAO,CAAC6D,EAAE,CAAC;SAC7B,CAAC,OAAOE,QAAQ,EAAE;UACjB,IAAI,EAAEA,QAAQ,YAAYxQ,GAAG,CAACC,GAAG,CAAC,EAAE,MAAMuQ,QAAQ;UAClDD,IAAI,GAAGjE,SAAS,CAACE,WAAW,CAAC8D,EAAE,CAAC;;OAEnC,MAAM,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAI,OAAOA,EAAE,CAACrP,CAAC,KAAK,QAAQ,IAAI,OAAOqP,EAAE,CAACnP,CAAC,KAAK,QAAQ,EAAE;QACzF,MAAM;UAAEF,CAAC;UAAEE;QAAC,CAAE,GAAGmP,EAAE;QACnBC,IAAI,GAAG,IAAIjE,SAAS,CAACrL,CAAC,EAAEE,CAAC,CAAC;OAC3B,MAAM;QACL,MAAM,IAAI5B,KAAK,CAAC,OAAO,CAAC;;MAE1BoG,CAAC,GAAGhB,KAAK,CAACe,OAAO,CAAC2K,SAAS,CAAC;KAC7B,CAAC,OAAO/L,KAAK,EAAE;MACd,IAAKA,KAAe,CAACmM,OAAO,KAAK,OAAO,EACtC,MAAM,IAAIlR,KAAK,CAAC,gEAAgE,CAAC;MACnF,OAAO,KAAK;;IAEd,IAAIsL,IAAI,IAAI0F,IAAI,CAACnD,QAAQ,EAAE,EAAE,OAAO,KAAK;IACzC,IAAI2B,OAAO,EAAEnC,OAAO,GAAGpK,KAAK,CAACgI,IAAI,CAACoC,OAAO,CAAC;IAC1C,MAAM;MAAE3L,CAAC;MAAEE;IAAC,CAAE,GAAGoP,IAAI;IACrB,MAAM7O,CAAC,GAAGkJ,aAAa,CAACgC,OAAO,CAAC,CAAC,CAAC;IAClC,MAAM8D,EAAE,GAAGpF,IAAI,CAACnK,CAAC,CAAC,CAAC,CAAC;IACpB,MAAM+L,EAAE,GAAG7B,IAAI,CAAC3J,CAAC,GAAGgP,EAAE,CAAC,CAAC,CAAC;IACzB,MAAMvD,EAAE,GAAG9B,IAAI,CAACpK,CAAC,GAAGyP,EAAE,CAAC,CAAC,CAAC;IACzB,MAAM1D,CAAC,GAAGrI,KAAK,CAACoB,IAAI,CAACoD,oBAAoB,CAACxD,CAAC,EAAEuH,EAAE,EAAEC,EAAE,CAAC,EAAEvK,QAAQ,EAAE,CAAC,CAAC;IAClE,IAAI,CAACoK,CAAC,EAAE,OAAO,KAAK;IACpB,MAAM2D,CAAC,GAAGtF,IAAI,CAAC2B,CAAC,CAACjK,CAAC,CAAC;IACnB,OAAO4N,CAAC,KAAK1P,CAAC;EAChB;EACA,OAAO;IACLuB,KAAK;IACLuL,YAAY;IACZK,eAAe;IACfyB,IAAI;IACJM,MAAM;IACN7F,eAAe,EAAE3F,KAAK;IACtB2H,SAAS;IACTmB;GACD;AACH;AAEA;;;;;;;;;AASA,OAAM,SAAUmD,cAAcA,CAAIxR,EAAiB,EAAEyR,CAAI;EACvD;EACA,MAAMnB,CAAC,GAAGtQ,EAAE,CAACgM,KAAK;EAClB,IAAIvK,CAAC,GAAGoB,GAAG;EACX,KAAK,IAAI6O,CAAC,GAAGpB,CAAC,GAAGvN,GAAG,EAAE2O,CAAC,GAAG1O,GAAG,KAAKH,GAAG,EAAE6O,CAAC,IAAI1O,GAAG,EAAEvB,CAAC,IAAIsB,GAAG;EACzD,MAAM4O,EAAE,GAAGlQ,CAAC,CAAC,CAAC;EACd;EACA;EACA,MAAMmQ,YAAY,GAAG5O,GAAG,IAAK2O,EAAE,GAAG5O,GAAG,GAAGA,GAAI;EAC5C,MAAM8O,UAAU,GAAGD,YAAY,GAAG5O,GAAG;EACrC,MAAM8O,EAAE,GAAG,CAACxB,CAAC,GAAGvN,GAAG,IAAI8O,UAAU,CAAC,CAAC;EACnC,MAAME,EAAE,GAAG,CAACD,EAAE,GAAG/O,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B,MAAMgP,EAAE,GAAGH,UAAU,GAAG9O,GAAG,CAAC,CAAC;EAC7B,MAAMkP,EAAE,GAAGL,YAAY,CAAC,CAAC;EACzB,MAAMM,EAAE,GAAGlS,EAAE,CAACmS,GAAG,CAACV,CAAC,EAAEK,EAAE,CAAC,CAAC,CAAC;EAC1B,MAAMM,EAAE,GAAGpS,EAAE,CAACmS,GAAG,CAACV,CAAC,EAAE,CAACK,EAAE,GAAG/O,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC;EACxC,IAAIqP,SAAS,GAAGA,CAACC,CAAI,EAAEf,CAAI,KAAoC;IAC7D,IAAIgB,GAAG,GAAGL,EAAE,CAAC,CAAC;IACd,IAAIM,GAAG,GAAGxS,EAAE,CAACmS,GAAG,CAACZ,CAAC,EAAES,EAAE,CAAC,CAAC,CAAC;IACzB,IAAIS,GAAG,GAAGzS,EAAE,CAACkE,GAAG,CAACsO,GAAG,CAAC,CAAC,CAAC;IACvBC,GAAG,GAAGzS,EAAE,CAACoE,GAAG,CAACqO,GAAG,EAAElB,CAAC,CAAC,CAAC,CAAC;IACtB,IAAImB,GAAG,GAAG1S,EAAE,CAACoE,GAAG,CAACkO,CAAC,EAAEG,GAAG,CAAC,CAAC,CAAC;IAC1BC,GAAG,GAAG1S,EAAE,CAACmS,GAAG,CAACO,GAAG,EAAEX,EAAE,CAAC,CAAC,CAAC;IACvBW,GAAG,GAAG1S,EAAE,CAACoE,GAAG,CAACsO,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;IACxBA,GAAG,GAAGxS,EAAE,CAACoE,GAAG,CAACsO,GAAG,EAAEnB,CAAC,CAAC,CAAC,CAAC;IACtBkB,GAAG,GAAGzS,EAAE,CAACoE,GAAG,CAACsO,GAAG,EAAEJ,CAAC,CAAC,CAAC,CAAC;IACtB,IAAIK,GAAG,GAAG3S,EAAE,CAACoE,GAAG,CAACqO,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;IAC5BE,GAAG,GAAG1S,EAAE,CAACmS,GAAG,CAACQ,GAAG,EAAEV,EAAE,CAAC,CAAC,CAAC;IACvB,IAAIW,IAAI,GAAG5S,EAAE,CAACC,GAAG,CAACyS,GAAG,EAAE1S,EAAE,CAACgG,GAAG,CAAC,CAAC,CAAC;IAChCwM,GAAG,GAAGxS,EAAE,CAACoE,GAAG,CAACqO,GAAG,EAAEL,EAAE,CAAC,CAAC,CAAC;IACvBM,GAAG,GAAG1S,EAAE,CAACoE,GAAG,CAACuO,GAAG,EAAEJ,GAAG,CAAC,CAAC,CAAC;IACxBE,GAAG,GAAGzS,EAAE,CAAC6S,IAAI,CAACL,GAAG,EAAEC,GAAG,EAAEG,IAAI,CAAC,CAAC,CAAC;IAC/BD,GAAG,GAAG3S,EAAE,CAAC6S,IAAI,CAACH,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC,CAAC,CAAC;IAC/B;IACA,KAAK,IAAI7M,CAAC,GAAG4L,EAAE,EAAE5L,CAAC,GAAGhD,GAAG,EAAEgD,CAAC,EAAE,EAAE;MAC7B,IAAI2M,GAAG,GAAG3M,CAAC,GAAG/C,GAAG,CAAC,CAAC;MACnB0P,GAAG,GAAG1P,GAAG,IAAK0P,GAAG,GAAG3P,GAAI,CAAC,CAAC;MAC1B,IAAI+P,IAAI,GAAG9S,EAAE,CAACmS,GAAG,CAACQ,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;MAC7B,MAAMK,EAAE,GAAG/S,EAAE,CAACC,GAAG,CAAC6S,IAAI,EAAE9S,EAAE,CAACgG,GAAG,CAAC,CAAC,CAAC;MACjCwM,GAAG,GAAGxS,EAAE,CAACoE,GAAG,CAACqO,GAAG,EAAEF,GAAG,CAAC,CAAC,CAAC;MACxBA,GAAG,GAAGvS,EAAE,CAACoE,GAAG,CAACmO,GAAG,EAAEA,GAAG,CAAC,CAAC,CAAC;MACxBO,IAAI,GAAG9S,EAAE,CAACoE,GAAG,CAACuO,GAAG,EAAEJ,GAAG,CAAC,CAAC,CAAC;MACzBE,GAAG,GAAGzS,EAAE,CAAC6S,IAAI,CAACL,GAAG,EAAEC,GAAG,EAAEM,EAAE,CAAC,CAAC,CAAC;MAC7BJ,GAAG,GAAG3S,EAAE,CAAC6S,IAAI,CAACC,IAAI,EAAEH,GAAG,EAAEI,EAAE,CAAC,CAAC,CAAC;;IAEhC,OAAO;MAAEpN,OAAO,EAAEiN,IAAI;MAAEI,KAAK,EAAEP;IAAG,CAAE;EACtC,CAAC;EACD,IAAIzS,EAAE,CAACgM,KAAK,GAAG9I,GAAG,KAAKD,GAAG,EAAE;IAC1B;IACA,MAAM0O,EAAE,GAAG,CAAC3R,EAAE,CAACgM,KAAK,GAAG/I,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,MAAM4O,EAAE,GAAG9R,EAAE,CAACuM,IAAI,CAACvM,EAAE,CAAC+H,GAAG,CAAC0J,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/BY,SAAS,GAAGA,CAACC,CAAI,EAAEf,CAAI,KAAI;MACzB,IAAIgB,GAAG,GAAGvS,EAAE,CAACkE,GAAG,CAACqN,CAAC,CAAC,CAAC,CAAC;MACrB,MAAMiB,GAAG,GAAGxS,EAAE,CAACoE,GAAG,CAACkO,CAAC,EAAEf,CAAC,CAAC,CAAC,CAAC;MAC1BgB,GAAG,GAAGvS,EAAE,CAACoE,GAAG,CAACmO,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;MACxB,IAAIS,EAAE,GAAGjT,EAAE,CAACmS,GAAG,CAACI,GAAG,EAAEZ,EAAE,CAAC,CAAC,CAAC;MAC1BsB,EAAE,GAAGjT,EAAE,CAACoE,GAAG,CAAC6O,EAAE,EAAET,GAAG,CAAC,CAAC,CAAC;MACtB,MAAMlG,EAAE,GAAGtM,EAAE,CAACoE,GAAG,CAAC6O,EAAE,EAAEnB,EAAE,CAAC,CAAC,CAAC;MAC3B,MAAMW,GAAG,GAAGzS,EAAE,CAACoE,GAAG,CAACpE,EAAE,CAACkE,GAAG,CAAC+O,EAAE,CAAC,EAAE1B,CAAC,CAAC,CAAC,CAAC;MACnC,MAAMqB,IAAI,GAAG5S,EAAE,CAACC,GAAG,CAACwS,GAAG,EAAEH,CAAC,CAAC,CAAC,CAAC;MAC7B,IAAI1O,CAAC,GAAG5D,EAAE,CAAC6S,IAAI,CAACvG,EAAE,EAAE2G,EAAE,EAAEL,IAAI,CAAC,CAAC,CAAC;MAC/B,OAAO;QAAEjN,OAAO,EAAEiN,IAAI;QAAEI,KAAK,EAAEpP;MAAC,CAAE,CAAC,CAAC;IACtC,CAAC;;EAEH;EACA;EACA,OAAOyO,SAAS;AAClB;AACA;;;;AAIA,OAAM,SAAUa,mBAAmBA,CACjClT,EAAiB,EACjBZ,IAIC;EAEDP,GAAG,CAACsU,aAAa,CAACnT,EAAE,CAAC;EACrB,IAAI,CAACA,EAAE,CAAC2F,OAAO,CAACvG,IAAI,CAACgU,CAAC,CAAC,IAAI,CAACpT,EAAE,CAAC2F,OAAO,CAACvG,IAAI,CAACiU,CAAC,CAAC,IAAI,CAACrT,EAAE,CAAC2F,OAAO,CAACvG,IAAI,CAACqS,CAAC,CAAC,EACnE,MAAM,IAAItR,KAAK,CAAC,mCAAmC,CAAC;EACtD,MAAMkS,SAAS,GAAGb,cAAc,CAACxR,EAAE,EAAEZ,IAAI,CAACqS,CAAC,CAAC;EAC5C,IAAI,CAACzR,EAAE,CAACoH,KAAK,EAAE,MAAM,IAAIjH,KAAK,CAAC,8BAA8B,CAAC;EAC9D;EACA;EACA,OAAQmS,CAAI,IAAoB;IAC9B;IACA,IAAIC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEE,GAAG,EAAED,GAAG,EAAEY,GAAG,EAAE3P,CAAC,EAAEC,CAAC;IACtC2O,GAAG,GAAGvS,EAAE,CAACkE,GAAG,CAACoO,CAAC,CAAC,CAAC,CAAC;IACjBC,GAAG,GAAGvS,EAAE,CAACoE,GAAG,CAACmO,GAAG,EAAEnT,IAAI,CAACqS,CAAC,CAAC,CAAC,CAAC;IAC3Be,GAAG,GAAGxS,EAAE,CAACkE,GAAG,CAACqO,GAAG,CAAC,CAAC,CAAC;IACnBC,GAAG,GAAGxS,EAAE,CAACqE,GAAG,CAACmO,GAAG,EAAED,GAAG,CAAC,CAAC,CAAC;IACxBE,GAAG,GAAGzS,EAAE,CAACqE,GAAG,CAACmO,GAAG,EAAExS,EAAE,CAACgG,GAAG,CAAC,CAAC,CAAC;IAC3ByM,GAAG,GAAGzS,EAAE,CAACoE,GAAG,CAACqO,GAAG,EAAErT,IAAI,CAACiU,CAAC,CAAC,CAAC,CAAC;IAC3BV,GAAG,GAAG3S,EAAE,CAAC6S,IAAI,CAACzT,IAAI,CAACqS,CAAC,EAAEzR,EAAE,CAAC+H,GAAG,CAACyK,GAAG,CAAC,EAAE,CAACxS,EAAE,CAACC,GAAG,CAACuS,GAAG,EAAExS,EAAE,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3DyS,GAAG,GAAG3S,EAAE,CAACoE,GAAG,CAACuO,GAAG,EAAEvT,IAAI,CAACgU,CAAC,CAAC,CAAC,CAAC;IAC3BZ,GAAG,GAAGxS,EAAE,CAACkE,GAAG,CAACuO,GAAG,CAAC,CAAC,CAAC;IACnBa,GAAG,GAAGtT,EAAE,CAACkE,GAAG,CAACyO,GAAG,CAAC,CAAC,CAAC;IACnBD,GAAG,GAAG1S,EAAE,CAACoE,GAAG,CAACkP,GAAG,EAAElU,IAAI,CAACgU,CAAC,CAAC,CAAC,CAAC;IAC3BZ,GAAG,GAAGxS,EAAE,CAACqE,GAAG,CAACmO,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACxBF,GAAG,GAAGxS,EAAE,CAACoE,GAAG,CAACoO,GAAG,EAAEC,GAAG,CAAC,CAAC,CAAC;IACxBa,GAAG,GAAGtT,EAAE,CAACoE,GAAG,CAACkP,GAAG,EAAEX,GAAG,CAAC,CAAC,CAAC;IACxBD,GAAG,GAAG1S,EAAE,CAACoE,GAAG,CAACkP,GAAG,EAAElU,IAAI,CAACiU,CAAC,CAAC,CAAC,CAAC;IAC3Bb,GAAG,GAAGxS,EAAE,CAACqE,GAAG,CAACmO,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACxB/O,CAAC,GAAG3D,EAAE,CAACoE,GAAG,CAACmO,GAAG,EAAEE,GAAG,CAAC,CAAC,CAAC;IACtB,MAAM;MAAE9M,OAAO;MAAEqN;IAAK,CAAE,GAAGX,SAAS,CAACG,GAAG,EAAEc,GAAG,CAAC,CAAC,CAAC;IAChD1P,CAAC,GAAG5D,EAAE,CAACoE,GAAG,CAACmO,GAAG,EAAED,CAAC,CAAC,CAAC,CAAC;IACpB1O,CAAC,GAAG5D,EAAE,CAACoE,GAAG,CAACR,CAAC,EAAEoP,KAAK,CAAC,CAAC,CAAC;IACtBrP,CAAC,GAAG3D,EAAE,CAAC6S,IAAI,CAAClP,CAAC,EAAE8O,GAAG,EAAE9M,OAAO,CAAC,CAAC,CAAC;IAC9B/B,CAAC,GAAG5D,EAAE,CAAC6S,IAAI,CAACjP,CAAC,EAAEoP,KAAK,EAAErN,OAAO,CAAC,CAAC,CAAC;IAChC,MAAMoN,EAAE,GAAG/S,EAAE,CAACoH,KAAM,CAACkL,CAAC,CAAC,KAAKtS,EAAE,CAACoH,KAAM,CAACxD,CAAC,CAAC,CAAC,CAAC;IAC1CA,CAAC,GAAG5D,EAAE,CAAC6S,IAAI,CAAC7S,EAAE,CAAC+H,GAAG,CAACnE,CAAC,CAAC,EAAEA,CAAC,EAAEmP,EAAE,CAAC,CAAC,CAAC;IAC/BpP,CAAC,GAAG3D,EAAE,CAACuT,GAAG,CAAC5P,CAAC,EAAEgP,GAAG,CAAC,CAAC,CAAC;IACpB,OAAO;MAAEhP,CAAC;MAAEC;IAAC,CAAE;EACjB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}