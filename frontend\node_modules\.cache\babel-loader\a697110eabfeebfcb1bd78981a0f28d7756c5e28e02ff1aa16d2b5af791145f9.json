{"ast": null, "code": "/**\n *  There are many awesome community services that provide Ethereum\n *  nodes both for developers just starting out and for large-scale\n *  communities.\n *\n *  @_section: api/providers/thirdparty: Community Providers  [thirdparty]\n */\n// Show the throttle message only once per service\nconst shown = new Set();\n/**\n *  Displays a warning in the console when the community resource is\n *  being used too heavily by the app, recommending the developer\n *  acquire their own credentials instead of using the community\n *  credentials.\n *\n *  The notification will only occur once per service.\n */\nexport function showThrottleMessage(service) {\n  if (shown.has(service)) {\n    return;\n  }\n  shown.add(service);\n  console.log(\"========= NOTICE =========\");\n  console.log(`Request-Rate Exceeded for ${service} (this message will not be repeated)`);\n  console.log(\"\");\n  console.log(\"The default API keys for each service are provided as a highly-throttled,\");\n  console.log(\"community resource for low-traffic projects and early prototyping.\");\n  console.log(\"\");\n  console.log(\"While your application will continue to function, we highly recommended\");\n  console.log(\"signing up for your own API keys to improve performance, increase your\");\n  console.log(\"request rate/limit and enable other perks, such as metrics and advanced APIs.\");\n  console.log(\"\");\n  console.log(\"For more details: https:/\\/docs.ethers.org/api-keys/\");\n  console.log(\"==========================\");\n}", "map": {"version": 3, "names": ["shown", "Set", "showThrottleMessage", "service", "has", "add", "console", "log"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\community.ts"], "sourcesContent": ["/**\n *  There are many awesome community services that provide Ethereum\n *  nodes both for developers just starting out and for large-scale\n *  communities.\n *\n *  @_section: api/providers/thirdparty: Community Providers  [thirdparty]\n */\n\n/**\n *  Providers which offer community credentials should extend this\n *  to notify any interested consumers whether community credentials\n *  are in-use.\n */\nexport interface CommunityResourcable {\n    /**\n     *  Returns true if the instance is connected using the community\n     *  credentials.\n     */\n    isCommunityResource(): boolean;\n}\n\n// Show the throttle message only once per service\nconst shown: Set<string> = new Set();\n\n/**\n *  Displays a warning in the console when the community resource is\n *  being used too heavily by the app, recommending the developer\n *  acquire their own credentials instead of using the community\n *  credentials.\n *\n *  The notification will only occur once per service.\n */\nexport function showThrottleMessage(service: string): void {\n    if (shown.has(service)) { return; }\n    shown.add(service);\n\n    console.log(\"========= NOTICE =========\")\n    console.log(`Request-Rate Exceeded for ${ service } (this message will not be repeated)`);\n    console.log(\"\");\n    console.log(\"The default API keys for each service are provided as a highly-throttled,\");\n    console.log(\"community resource for low-traffic projects and early prototyping.\");\n    console.log(\"\");\n    console.log(\"While your application will continue to function, we highly recommended\");\n    console.log(\"signing up for your own API keys to improve performance, increase your\");\n    console.log(\"request rate/limit and enable other perks, such as metrics and advanced APIs.\");\n    console.log(\"\");\n    console.log(\"For more details: https:/\\/docs.ethers.org/api-keys/\");\n    console.log(\"==========================\");\n}\n"], "mappings": "AAAA;;;;;;;AAqBA;AACA,MAAMA,KAAK,GAAgB,IAAIC,GAAG,EAAE;AAEpC;;;;;;;;AAQA,OAAM,SAAUC,mBAAmBA,CAACC,OAAe;EAC/C,IAAIH,KAAK,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE;IAAE;;EAC1BH,KAAK,CAACK,GAAG,CAACF,OAAO,CAAC;EAElBG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;EACzCD,OAAO,CAACC,GAAG,CAAC,6BAA8BJ,OAAQ,sCAAsC,CAAC;EACzFG,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACfD,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;EACxFD,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;EACjFD,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACfD,OAAO,CAACC,GAAG,CAAC,yEAAyE,CAAC;EACtFD,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;EACrFD,OAAO,CAACC,GAAG,CAAC,+EAA+E,CAAC;EAC5FD,OAAO,CAACC,GAAG,CAAC,EAAE,CAAC;EACfD,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;EACnED,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}