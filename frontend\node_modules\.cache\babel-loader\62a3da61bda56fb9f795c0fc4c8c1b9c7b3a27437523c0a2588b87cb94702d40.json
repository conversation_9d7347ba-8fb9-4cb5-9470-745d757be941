{"ast": null, "code": "import { exists, output } from './_assert.js';\nimport { Hash, createView, toBytes } from './utils.js';\n// Polyfill for Safari 14\nfunction setBigUint64(view, byteOffset, value, isLE) {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number(value >> _32n & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n// Base SHA2 class (RFC 6234)\nexport class SHA2 extends Hash {\n  constructor(blockLen, outputLen, padOffset, isLE) {\n    super();\n    this.blockLen = blockLen;\n    this.outputLen = outputLen;\n    this.padOffset = padOffset;\n    this.isLE = isLE;\n    this.finished = false;\n    this.length = 0;\n    this.pos = 0;\n    this.destroyed = false;\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data) {\n    exists(this);\n    const {\n      view,\n      buffer,\n      blockLen\n    } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len;) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out) {\n    exists(this);\n    output(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const {\n      buffer,\n      view,\n      blockLen,\n      isLE\n    } = this;\n    let {\n      pos\n    } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest() {\n    const {\n      buffer,\n      outputLen\n    } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to) {\n    to || (to = new this.constructor());\n    to.set(...this.get());\n    const {\n      blockLen,\n      buffer,\n      length,\n      finished,\n      destroyed,\n      pos\n    } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n}", "map": {"version": 3, "names": ["exists", "output", "Hash", "createView", "toBytes", "setBigUint64", "view", "byteOffset", "value", "isLE", "_32n", "BigInt", "_u32_max", "wh", "Number", "wl", "h", "l", "setUint32", "SHA2", "constructor", "blockLen", "outputLen", "padOffset", "finished", "length", "pos", "destroyed", "buffer", "Uint8Array", "update", "data", "len", "take", "Math", "min", "dataView", "process", "set", "subarray", "roundClean", "digestInto", "out", "fill", "i", "oview", "Error", "outLen", "state", "get", "digest", "res", "slice", "destroy", "_cloneInto", "to"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\_sha2.ts"], "sourcesContent": ["import { exists, output } from './_assert.js';\nimport { Hash, createView, Input, toBytes } from './utils.js';\n\n// Polyfill for Safari 14\nfunction setBigUint64(view: DataView, byteOffset: number, value: bigint, isLE: boolean): void {\n  if (typeof view.setBigUint64 === 'function') return view.setBigUint64(byteOffset, value, isLE);\n  const _32n = BigInt(32);\n  const _u32_max = BigInt(0xffffffff);\n  const wh = Number((value >> _32n) & _u32_max);\n  const wl = Number(value & _u32_max);\n  const h = isLE ? 4 : 0;\n  const l = isLE ? 0 : 4;\n  view.setUint32(byteOffset + h, wh, isLE);\n  view.setUint32(byteOffset + l, wl, isLE);\n}\n\n// Base SHA2 class (RFC 6234)\nexport abstract class SHA2<T extends SHA2<T>> extends Hash<T> {\n  protected abstract process(buf: DataView, offset: number): void;\n  protected abstract get(): number[];\n  protected abstract set(...args: number[]): void;\n  abstract destroy(): void;\n  protected abstract roundClean(): void;\n  // For partial updates less than block size\n  protected buffer: Uint8Array;\n  protected view: DataView;\n  protected finished = false;\n  protected length = 0;\n  protected pos = 0;\n  protected destroyed = false;\n\n  constructor(\n    readonly blockLen: number,\n    public outputLen: number,\n    readonly padOffset: number,\n    readonly isLE: boolean\n  ) {\n    super();\n    this.buffer = new Uint8Array(blockLen);\n    this.view = createView(this.buffer);\n  }\n  update(data: Input): this {\n    exists(this);\n    const { view, buffer, blockLen } = this;\n    data = toBytes(data);\n    const len = data.length;\n    for (let pos = 0; pos < len; ) {\n      const take = Math.min(blockLen - this.pos, len - pos);\n      // Fast path: we have at least one block in input, cast it to view and process\n      if (take === blockLen) {\n        const dataView = createView(data);\n        for (; blockLen <= len - pos; pos += blockLen) this.process(dataView, pos);\n        continue;\n      }\n      buffer.set(data.subarray(pos, pos + take), this.pos);\n      this.pos += take;\n      pos += take;\n      if (this.pos === blockLen) {\n        this.process(view, 0);\n        this.pos = 0;\n      }\n    }\n    this.length += data.length;\n    this.roundClean();\n    return this;\n  }\n  digestInto(out: Uint8Array) {\n    exists(this);\n    output(out, this);\n    this.finished = true;\n    // Padding\n    // We can avoid allocation of buffer for padding completely if it\n    // was previously not allocated here. But it won't change performance.\n    const { buffer, view, blockLen, isLE } = this;\n    let { pos } = this;\n    // append the bit '1' to the message\n    buffer[pos++] = 0b10000000;\n    this.buffer.subarray(pos).fill(0);\n    // we have less than padOffset left in buffer, so we cannot put length in current block, need process it and pad again\n    if (this.padOffset > blockLen - pos) {\n      this.process(view, 0);\n      pos = 0;\n    }\n    // Pad until full block byte with zeros\n    for (let i = pos; i < blockLen; i++) buffer[i] = 0;\n    // Note: sha512 requires length to be 128bit integer, but length in JS will overflow before that\n    // You need to write around 2 exabytes (u64_max / 8 / (1024**6)) for this to happen.\n    // So we just write lowest 64 bits of that value.\n    setBigUint64(view, blockLen - 8, BigInt(this.length * 8), isLE);\n    this.process(view, 0);\n    const oview = createView(out);\n    const len = this.outputLen;\n    // NOTE: we do division by 4 later, which should be fused in single op with modulo by JIT\n    if (len % 4) throw new Error('_sha2: outputLen should be aligned to 32bit');\n    const outLen = len / 4;\n    const state = this.get();\n    if (outLen > state.length) throw new Error('_sha2: outputLen bigger than state');\n    for (let i = 0; i < outLen; i++) oview.setUint32(4 * i, state[i], isLE);\n  }\n  digest() {\n    const { buffer, outputLen } = this;\n    this.digestInto(buffer);\n    const res = buffer.slice(0, outputLen);\n    this.destroy();\n    return res;\n  }\n  _cloneInto(to?: T): T {\n    to ||= new (this.constructor as any)() as T;\n    to.set(...this.get());\n    const { blockLen, buffer, length, finished, destroyed, pos } = this;\n    to.length = length;\n    to.pos = pos;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    if (length % blockLen) to.buffer.set(buffer);\n    return to;\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,MAAM,QAAQ,cAAc;AAC7C,SAASC,IAAI,EAAEC,UAAU,EAASC,OAAO,QAAQ,YAAY;AAE7D;AACA,SAASC,YAAYA,CAACC,IAAc,EAAEC,UAAkB,EAAEC,KAAa,EAAEC,IAAa;EACpF,IAAI,OAAOH,IAAI,CAACD,YAAY,KAAK,UAAU,EAAE,OAAOC,IAAI,CAACD,YAAY,CAACE,UAAU,EAAEC,KAAK,EAAEC,IAAI,CAAC;EAC9F,MAAMC,IAAI,GAAGC,MAAM,CAAC,EAAE,CAAC;EACvB,MAAMC,QAAQ,GAAGD,MAAM,CAAC,UAAU,CAAC;EACnC,MAAME,EAAE,GAAGC,MAAM,CAAEN,KAAK,IAAIE,IAAI,GAAIE,QAAQ,CAAC;EAC7C,MAAMG,EAAE,GAAGD,MAAM,CAACN,KAAK,GAAGI,QAAQ,CAAC;EACnC,MAAMI,CAAC,GAAGP,IAAI,GAAG,CAAC,GAAG,CAAC;EACtB,MAAMQ,CAAC,GAAGR,IAAI,GAAG,CAAC,GAAG,CAAC;EACtBH,IAAI,CAACY,SAAS,CAACX,UAAU,GAAGS,CAAC,EAAEH,EAAE,EAAEJ,IAAI,CAAC;EACxCH,IAAI,CAACY,SAAS,CAACX,UAAU,GAAGU,CAAC,EAAEF,EAAE,EAAEN,IAAI,CAAC;AAC1C;AAEA;AACA,OAAM,MAAgBU,IAAwB,SAAQjB,IAAO;EAc3DkB,YACWC,QAAgB,EAClBC,SAAiB,EACfC,SAAiB,EACjBd,IAAa;IAEtB,KAAK,EAAE;IALE,KAAAY,QAAQ,GAARA,QAAQ;IACV,KAAAC,SAAS,GAATA,SAAS;IACP,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAd,IAAI,GAAJA,IAAI;IATL,KAAAe,QAAQ,GAAG,KAAK;IAChB,KAAAC,MAAM,GAAG,CAAC;IACV,KAAAC,GAAG,GAAG,CAAC;IACP,KAAAC,SAAS,GAAG,KAAK;IASzB,IAAI,CAACC,MAAM,GAAG,IAAIC,UAAU,CAACR,QAAQ,CAAC;IACtC,IAAI,CAACf,IAAI,GAAGH,UAAU,CAAC,IAAI,CAACyB,MAAM,CAAC;EACrC;EACAE,MAAMA,CAACC,IAAW;IAChB/B,MAAM,CAAC,IAAI,CAAC;IACZ,MAAM;MAAEM,IAAI;MAAEsB,MAAM;MAAEP;IAAQ,CAAE,GAAG,IAAI;IACvCU,IAAI,GAAG3B,OAAO,CAAC2B,IAAI,CAAC;IACpB,MAAMC,GAAG,GAAGD,IAAI,CAACN,MAAM;IACvB,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGM,GAAG,GAAI;MAC7B,MAAMC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACd,QAAQ,GAAG,IAAI,CAACK,GAAG,EAAEM,GAAG,GAAGN,GAAG,CAAC;MACrD;MACA,IAAIO,IAAI,KAAKZ,QAAQ,EAAE;QACrB,MAAMe,QAAQ,GAAGjC,UAAU,CAAC4B,IAAI,CAAC;QACjC,OAAOV,QAAQ,IAAIW,GAAG,GAAGN,GAAG,EAAEA,GAAG,IAAIL,QAAQ,EAAE,IAAI,CAACgB,OAAO,CAACD,QAAQ,EAAEV,GAAG,CAAC;QAC1E;;MAEFE,MAAM,CAACU,GAAG,CAACP,IAAI,CAACQ,QAAQ,CAACb,GAAG,EAAEA,GAAG,GAAGO,IAAI,CAAC,EAAE,IAAI,CAACP,GAAG,CAAC;MACpD,IAAI,CAACA,GAAG,IAAIO,IAAI;MAChBP,GAAG,IAAIO,IAAI;MACX,IAAI,IAAI,CAACP,GAAG,KAAKL,QAAQ,EAAE;QACzB,IAAI,CAACgB,OAAO,CAAC/B,IAAI,EAAE,CAAC,CAAC;QACrB,IAAI,CAACoB,GAAG,GAAG,CAAC;;;IAGhB,IAAI,CAACD,MAAM,IAAIM,IAAI,CAACN,MAAM;IAC1B,IAAI,CAACe,UAAU,EAAE;IACjB,OAAO,IAAI;EACb;EACAC,UAAUA,CAACC,GAAe;IACxB1C,MAAM,CAAC,IAAI,CAAC;IACZC,MAAM,CAACyC,GAAG,EAAE,IAAI,CAAC;IACjB,IAAI,CAAClB,QAAQ,GAAG,IAAI;IACpB;IACA;IACA;IACA,MAAM;MAAEI,MAAM;MAAEtB,IAAI;MAAEe,QAAQ;MAAEZ;IAAI,CAAE,GAAG,IAAI;IAC7C,IAAI;MAAEiB;IAAG,CAAE,GAAG,IAAI;IAClB;IACAE,MAAM,CAACF,GAAG,EAAE,CAAC,GAAG,UAAU;IAC1B,IAAI,CAACE,MAAM,CAACW,QAAQ,CAACb,GAAG,CAAC,CAACiB,IAAI,CAAC,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACpB,SAAS,GAAGF,QAAQ,GAAGK,GAAG,EAAE;MACnC,IAAI,CAACW,OAAO,CAAC/B,IAAI,EAAE,CAAC,CAAC;MACrBoB,GAAG,GAAG,CAAC;;IAET;IACA,KAAK,IAAIkB,CAAC,GAAGlB,GAAG,EAAEkB,CAAC,GAAGvB,QAAQ,EAAEuB,CAAC,EAAE,EAAEhB,MAAM,CAACgB,CAAC,CAAC,GAAG,CAAC;IAClD;IACA;IACA;IACAvC,YAAY,CAACC,IAAI,EAAEe,QAAQ,GAAG,CAAC,EAAEV,MAAM,CAAC,IAAI,CAACc,MAAM,GAAG,CAAC,CAAC,EAAEhB,IAAI,CAAC;IAC/D,IAAI,CAAC4B,OAAO,CAAC/B,IAAI,EAAE,CAAC,CAAC;IACrB,MAAMuC,KAAK,GAAG1C,UAAU,CAACuC,GAAG,CAAC;IAC7B,MAAMV,GAAG,GAAG,IAAI,CAACV,SAAS;IAC1B;IACA,IAAIU,GAAG,GAAG,CAAC,EAAE,MAAM,IAAIc,KAAK,CAAC,6CAA6C,CAAC;IAC3E,MAAMC,MAAM,GAAGf,GAAG,GAAG,CAAC;IACtB,MAAMgB,KAAK,GAAG,IAAI,CAACC,GAAG,EAAE;IACxB,IAAIF,MAAM,GAAGC,KAAK,CAACvB,MAAM,EAAE,MAAM,IAAIqB,KAAK,CAAC,oCAAoC,CAAC;IAChF,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,EAAEH,CAAC,EAAE,EAAEC,KAAK,CAAC3B,SAAS,CAAC,CAAC,GAAG0B,CAAC,EAAEI,KAAK,CAACJ,CAAC,CAAC,EAAEnC,IAAI,CAAC;EACzE;EACAyC,MAAMA,CAAA;IACJ,MAAM;MAAEtB,MAAM;MAAEN;IAAS,CAAE,GAAG,IAAI;IAClC,IAAI,CAACmB,UAAU,CAACb,MAAM,CAAC;IACvB,MAAMuB,GAAG,GAAGvB,MAAM,CAACwB,KAAK,CAAC,CAAC,EAAE9B,SAAS,CAAC;IACtC,IAAI,CAAC+B,OAAO,EAAE;IACd,OAAOF,GAAG;EACZ;EACAG,UAAUA,CAACC,EAAM;IACfA,EAAE,KAAFA,EAAE,GAAK,IAAK,IAAI,CAACnC,WAAmB,EAAO;IAC3CmC,EAAE,CAACjB,GAAG,CAAC,GAAG,IAAI,CAACW,GAAG,EAAE,CAAC;IACrB,MAAM;MAAE5B,QAAQ;MAAEO,MAAM;MAAEH,MAAM;MAAED,QAAQ;MAAEG,SAAS;MAAED;IAAG,CAAE,GAAG,IAAI;IACnE6B,EAAE,CAAC9B,MAAM,GAAGA,MAAM;IAClB8B,EAAE,CAAC7B,GAAG,GAAGA,GAAG;IACZ6B,EAAE,CAAC/B,QAAQ,GAAGA,QAAQ;IACtB+B,EAAE,CAAC5B,SAAS,GAAGA,SAAS;IACxB,IAAIF,MAAM,GAAGJ,QAAQ,EAAEkC,EAAE,CAAC3B,MAAM,CAACU,GAAG,CAACV,MAAM,CAAC;IAC5C,OAAO2B,EAAE;EACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}