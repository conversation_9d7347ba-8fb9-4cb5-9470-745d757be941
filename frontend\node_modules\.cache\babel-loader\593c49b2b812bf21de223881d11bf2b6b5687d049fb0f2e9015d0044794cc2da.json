{"ast": null, "code": "/**\n *  ENS is a service which allows easy-to-remember names to map to\n *  network addresses.\n *\n *  @_section: api/providers/ens-resolver:ENS Resolver  [about-ens-rsolver]\n */\nimport { getAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { dnsEncode, namehash } from \"../hash/index.js\";\nimport { hexlify, isHexString, toBeHex, defineProperties, encodeBase58, assert, assertArgument, isError, FetchRequest } from \"../utils/index.js\";\n// @TODO: This should use the fetch-data:ipfs gateway\n// Trim off the ipfs:// prefix and return the default gateway URL\nfunction getIpfsLink(link) {\n  if (link.match(/^ipfs:\\/\\/ipfs\\//i)) {\n    link = link.substring(12);\n  } else if (link.match(/^ipfs:\\/\\//i)) {\n    link = link.substring(7);\n  } else {\n    assertArgument(false, \"unsupported IPFS format\", \"link\", link);\n  }\n  return `https:/\\/gateway.ipfs.io/ipfs/${link}`;\n}\n;\n;\n/**\n *  A provider plugin super-class for processing multicoin address types.\n */\nexport class MulticoinProviderPlugin {\n  /**\n   *  The name.\n   */\n  name;\n  /**\n   *  Creates a new **MulticoinProviderPluing** for %%name%%.\n   */\n  constructor(name) {\n    defineProperties(this, {\n      name\n    });\n  }\n  connect(proivder) {\n    return this;\n  }\n  /**\n   *  Returns ``true`` if %%coinType%% is supported by this plugin.\n   */\n  supportsCoinType(coinType) {\n    return false;\n  }\n  /**\n   *  Resolves to the encoded %%address%% for %%coinType%%.\n   */\n  async encodeAddress(coinType, address) {\n    throw new Error(\"unsupported coin\");\n  }\n  /**\n   *  Resolves to the decoded %%data%% for %%coinType%%.\n   */\n  async decodeAddress(coinType, data) {\n    throw new Error(\"unsupported coin\");\n  }\n}\nconst BasicMulticoinPluginId = \"org.ethers.plugins.provider.BasicMulticoin\";\n/**\n *  A **BasicMulticoinProviderPlugin** provides service for common\n *  coin types, which do not require additional libraries to encode or\n *  decode.\n */\nexport class BasicMulticoinProviderPlugin extends MulticoinProviderPlugin {\n  /**\n   *  Creates a new **BasicMulticoinProviderPlugin**.\n   */\n  constructor() {\n    super(BasicMulticoinPluginId);\n  }\n}\nconst matcherIpfs = new RegExp(\"^(ipfs):/\\/(.*)$\", \"i\");\nconst matchers = [new RegExp(\"^(https):/\\/(.*)$\", \"i\"), new RegExp(\"^(data):(.*)$\", \"i\"), matcherIpfs, new RegExp(\"^eip155:[0-9]+/(erc[0-9]+):(.*)$\", \"i\")];\n/**\n *  A connected object to a resolved ENS name resolver, which can be\n *  used to query additional details.\n */\nexport class EnsResolver {\n  /**\n   *  The connected provider.\n   */\n  provider;\n  /**\n   *  The address of the resolver.\n   */\n  address;\n  /**\n   *  The name this resolver was resolved against.\n   */\n  name;\n  // For EIP-2544 names, the ancestor that provided the resolver\n  #supports2544;\n  #resolver;\n  constructor(provider, address, name) {\n    defineProperties(this, {\n      provider,\n      address,\n      name\n    });\n    this.#supports2544 = null;\n    this.#resolver = new Contract(address, [\"function supportsInterface(bytes4) view returns (bool)\", \"function resolve(bytes, bytes) view returns (bytes)\", \"function addr(bytes32) view returns (address)\", \"function addr(bytes32, uint) view returns (bytes)\", \"function text(bytes32, string) view returns (string)\", \"function contenthash(bytes32) view returns (bytes)\"], provider);\n  }\n  /**\n   *  Resolves to true if the resolver supports wildcard resolution.\n   */\n  async supportsWildcard() {\n    if (this.#supports2544 == null) {\n      this.#supports2544 = (async () => {\n        try {\n          return await this.#resolver.supportsInterface(\"0x9061b923\");\n        } catch (error) {\n          // Wildcard resolvers must understand supportsInterface\n          // and return true.\n          if (isError(error, \"CALL_EXCEPTION\")) {\n            return false;\n          }\n          // Let future attempts try again...\n          this.#supports2544 = null;\n          throw error;\n        }\n      })();\n    }\n    return await this.#supports2544;\n  }\n  async #fetch(funcName, params) {\n    params = (params || []).slice();\n    const iface = this.#resolver.interface;\n    // The first parameters is always the nodehash\n    params.unshift(namehash(this.name));\n    let fragment = null;\n    if (await this.supportsWildcard()) {\n      fragment = iface.getFunction(funcName);\n      assert(fragment, \"missing fragment\", \"UNKNOWN_ERROR\", {\n        info: {\n          funcName\n        }\n      });\n      params = [dnsEncode(this.name, 255), iface.encodeFunctionData(fragment, params)];\n      funcName = \"resolve(bytes,bytes)\";\n    }\n    params.push({\n      enableCcipRead: true\n    });\n    try {\n      const result = await this.#resolver[funcName](...params);\n      if (fragment) {\n        return iface.decodeFunctionResult(fragment, result)[0];\n      }\n      return result;\n    } catch (error) {\n      if (!isError(error, \"CALL_EXCEPTION\")) {\n        throw error;\n      }\n    }\n    return null;\n  }\n  /**\n   *  Resolves to the address for %%coinType%% or null if the\n   *  provided %%coinType%% has not been configured.\n   */\n  async getAddress(coinType) {\n    if (coinType == null) {\n      coinType = 60;\n    }\n    if (coinType === 60) {\n      try {\n        const result = await this.#fetch(\"addr(bytes32)\");\n        // No address\n        if (result == null || result === ZeroAddress) {\n          return null;\n        }\n        return result;\n      } catch (error) {\n        if (isError(error, \"CALL_EXCEPTION\")) {\n          return null;\n        }\n        throw error;\n      }\n    }\n    // Try decoding its EVM canonical chain as an EVM chain address first\n    if (coinType >= 0 && coinType < 0x80000000) {\n      let ethCoinType = coinType + 0x80000000;\n      const data = await this.#fetch(\"addr(bytes32,uint)\", [ethCoinType]);\n      if (isHexString(data, 20)) {\n        return getAddress(data);\n      }\n    }\n    let coinPlugin = null;\n    for (const plugin of this.provider.plugins) {\n      if (!(plugin instanceof MulticoinProviderPlugin)) {\n        continue;\n      }\n      if (plugin.supportsCoinType(coinType)) {\n        coinPlugin = plugin;\n        break;\n      }\n    }\n    if (coinPlugin == null) {\n      return null;\n    }\n    // keccak256(\"addr(bytes32,uint256\")\n    const data = await this.#fetch(\"addr(bytes32,uint)\", [coinType]);\n    // No address\n    if (data == null || data === \"0x\") {\n      return null;\n    }\n    // Compute the address\n    const address = await coinPlugin.decodeAddress(coinType, data);\n    if (address != null) {\n      return address;\n    }\n    assert(false, `invalid coin data`, \"UNSUPPORTED_OPERATION\", {\n      operation: `getAddress(${coinType})`,\n      info: {\n        coinType,\n        data\n      }\n    });\n  }\n  /**\n   *  Resolves to the EIP-634 text record for %%key%%, or ``null``\n   *  if unconfigured.\n   */\n  async getText(key) {\n    const data = await this.#fetch(\"text(bytes32,string)\", [key]);\n    if (data == null || data === \"0x\") {\n      return null;\n    }\n    return data;\n  }\n  /**\n   *  Rsolves to the content-hash or ``null`` if unconfigured.\n   */\n  async getContentHash() {\n    // keccak256(\"contenthash()\")\n    const data = await this.#fetch(\"contenthash(bytes32)\");\n    // No contenthash\n    if (data == null || data === \"0x\") {\n      return null;\n    }\n    // IPFS (CID: 1, Type: 70=DAG-PB, 72=libp2p-key)\n    const ipfs = data.match(/^0x(e3010170|e5010172)(([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])([0-9a-f]*))$/);\n    if (ipfs) {\n      const scheme = ipfs[1] === \"e3010170\" ? \"ipfs\" : \"ipns\";\n      const length = parseInt(ipfs[4], 16);\n      if (ipfs[5].length === length * 2) {\n        return `${scheme}:/\\/${encodeBase58(\"0x\" + ipfs[2])}`;\n      }\n    }\n    // Swarm (CID: 1, Type: swarm-manifest; hash/length hard-coded to keccak256/32)\n    const swarm = data.match(/^0xe40101fa011b20([0-9a-f]*)$/);\n    if (swarm && swarm[1].length === 64) {\n      return `bzz:/\\/${swarm[1]}`;\n    }\n    assert(false, `invalid or unsupported content hash data`, \"UNSUPPORTED_OPERATION\", {\n      operation: \"getContentHash()\",\n      info: {\n        data\n      }\n    });\n  }\n  /**\n   *  Resolves to the avatar url or ``null`` if the avatar is either\n   *  unconfigured or incorrectly configured (e.g. references an NFT\n   *  not owned by the address).\n   *\n   *  If diagnosing issues with configurations, the [[_getAvatar]]\n   *  method may be useful.\n   */\n  async getAvatar() {\n    const avatar = await this._getAvatar();\n    return avatar.url;\n  }\n  /**\n   *  When resolving an avatar, there are many steps involved, such\n   *  fetching metadata and possibly validating ownership of an\n   *  NFT.\n   *\n   *  This method can be used to examine each step and the value it\n   *  was working from.\n   */\n  async _getAvatar() {\n    const linkage = [{\n      type: \"name\",\n      value: this.name\n    }];\n    try {\n      // test data for ricmoo.eth\n      //const avatar = \"eip155:1/erc721:******************************************/29233\";\n      const avatar = await this.getText(\"avatar\");\n      if (avatar == null) {\n        linkage.push({\n          type: \"!avatar\",\n          value: \"\"\n        });\n        return {\n          url: null,\n          linkage\n        };\n      }\n      linkage.push({\n        type: \"avatar\",\n        value: avatar\n      });\n      for (let i = 0; i < matchers.length; i++) {\n        const match = avatar.match(matchers[i]);\n        if (match == null) {\n          continue;\n        }\n        const scheme = match[1].toLowerCase();\n        switch (scheme) {\n          case \"https\":\n          case \"data\":\n            linkage.push({\n              type: \"url\",\n              value: avatar\n            });\n            return {\n              linkage,\n              url: avatar\n            };\n          case \"ipfs\":\n            {\n              const url = getIpfsLink(avatar);\n              linkage.push({\n                type: \"ipfs\",\n                value: avatar\n              });\n              linkage.push({\n                type: \"url\",\n                value: url\n              });\n              return {\n                linkage,\n                url\n              };\n            }\n          case \"erc721\":\n          case \"erc1155\":\n            {\n              // Depending on the ERC type, use tokenURI(uint256) or url(uint256)\n              const selector = scheme === \"erc721\" ? \"tokenURI(uint256)\" : \"uri(uint256)\";\n              linkage.push({\n                type: scheme,\n                value: avatar\n              });\n              // The owner of this name\n              const owner = await this.getAddress();\n              if (owner == null) {\n                linkage.push({\n                  type: \"!owner\",\n                  value: \"\"\n                });\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              const comps = (match[2] || \"\").split(\"/\");\n              if (comps.length !== 2) {\n                linkage.push({\n                  type: `!${scheme}caip`,\n                  value: match[2] || \"\"\n                });\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              const tokenId = comps[1];\n              const contract = new Contract(comps[0], [\n              // ERC-721\n              \"function tokenURI(uint) view returns (string)\", \"function ownerOf(uint) view returns (address)\",\n              // ERC-1155\n              \"function uri(uint) view returns (string)\", \"function balanceOf(address, uint256) view returns (uint)\"], this.provider);\n              // Check that this account owns the token\n              if (scheme === \"erc721\") {\n                const tokenOwner = await contract.ownerOf(tokenId);\n                if (owner !== tokenOwner) {\n                  linkage.push({\n                    type: \"!owner\",\n                    value: tokenOwner\n                  });\n                  return {\n                    url: null,\n                    linkage\n                  };\n                }\n                linkage.push({\n                  type: \"owner\",\n                  value: tokenOwner\n                });\n              } else if (scheme === \"erc1155\") {\n                const balance = await contract.balanceOf(owner, tokenId);\n                if (!balance) {\n                  linkage.push({\n                    type: \"!balance\",\n                    value: \"0\"\n                  });\n                  return {\n                    url: null,\n                    linkage\n                  };\n                }\n                linkage.push({\n                  type: \"balance\",\n                  value: balance.toString()\n                });\n              }\n              // Call the token contract for the metadata URL\n              let metadataUrl = await contract[selector](tokenId);\n              if (metadataUrl == null || metadataUrl === \"0x\") {\n                linkage.push({\n                  type: \"!metadata-url\",\n                  value: \"\"\n                });\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              linkage.push({\n                type: \"metadata-url-base\",\n                value: metadataUrl\n              });\n              // ERC-1155 allows a generic {id} in the URL\n              if (scheme === \"erc1155\") {\n                metadataUrl = metadataUrl.replace(\"{id}\", toBeHex(tokenId, 32).substring(2));\n                linkage.push({\n                  type: \"metadata-url-expanded\",\n                  value: metadataUrl\n                });\n              }\n              // Transform IPFS metadata links\n              if (metadataUrl.match(/^ipfs:/i)) {\n                metadataUrl = getIpfsLink(metadataUrl);\n              }\n              linkage.push({\n                type: \"metadata-url\",\n                value: metadataUrl\n              });\n              // Get the token metadata\n              let metadata = {};\n              const response = await new FetchRequest(metadataUrl).send();\n              response.assertOk();\n              try {\n                metadata = response.bodyJson;\n              } catch (error) {\n                try {\n                  linkage.push({\n                    type: \"!metadata\",\n                    value: response.bodyText\n                  });\n                } catch (error) {\n                  const bytes = response.body;\n                  if (bytes) {\n                    linkage.push({\n                      type: \"!metadata\",\n                      value: hexlify(bytes)\n                    });\n                  }\n                  return {\n                    url: null,\n                    linkage\n                  };\n                }\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              if (!metadata) {\n                linkage.push({\n                  type: \"!metadata\",\n                  value: \"\"\n                });\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              linkage.push({\n                type: \"metadata\",\n                value: JSON.stringify(metadata)\n              });\n              // Pull the image URL out\n              let imageUrl = metadata.image;\n              if (typeof imageUrl !== \"string\") {\n                linkage.push({\n                  type: \"!imageUrl\",\n                  value: \"\"\n                });\n                return {\n                  url: null,\n                  linkage\n                };\n              }\n              if (imageUrl.match(/^(https:\\/\\/|data:)/i)) {\n                // Allow\n              } else {\n                // Transform IPFS link to gateway\n                const ipfs = imageUrl.match(matcherIpfs);\n                if (ipfs == null) {\n                  linkage.push({\n                    type: \"!imageUrl-ipfs\",\n                    value: imageUrl\n                  });\n                  return {\n                    url: null,\n                    linkage\n                  };\n                }\n                linkage.push({\n                  type: \"imageUrl-ipfs\",\n                  value: imageUrl\n                });\n                imageUrl = getIpfsLink(imageUrl);\n              }\n              linkage.push({\n                type: \"url\",\n                value: imageUrl\n              });\n              return {\n                linkage,\n                url: imageUrl\n              };\n            }\n        }\n      }\n    } catch (error) {}\n    return {\n      linkage,\n      url: null\n    };\n  }\n  static async getEnsAddress(provider) {\n    const network = await provider.getNetwork();\n    const ensPlugin = network.getPlugin(\"org.ethers.plugins.network.Ens\");\n    // No ENS...\n    assert(ensPlugin, \"network does not support ENS\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"getEnsAddress\",\n      info: {\n        network\n      }\n    });\n    return ensPlugin.address;\n  }\n  static async #getResolver(provider, name) {\n    const ensAddr = await EnsResolver.getEnsAddress(provider);\n    try {\n      const contract = new Contract(ensAddr, [\"function resolver(bytes32) view returns (address)\"], provider);\n      const addr = await contract.resolver(namehash(name), {\n        enableCcipRead: true\n      });\n      if (addr === ZeroAddress) {\n        return null;\n      }\n      return addr;\n    } catch (error) {\n      // ENS registry cannot throw errors on resolver(bytes32),\n      // so probably a link error\n      throw error;\n    }\n    return null;\n  }\n  /**\n   *  Resolve to the ENS resolver for %%name%% using %%provider%% or\n   *  ``null`` if unconfigured.\n   */\n  static async fromName(provider, name) {\n    let currentName = name;\n    while (true) {\n      if (currentName === \"\" || currentName === \".\") {\n        return null;\n      }\n      // Optimization since the eth node cannot change and does\n      // not have a wildcard resolver\n      if (name !== \"eth\" && currentName === \"eth\") {\n        return null;\n      }\n      // Check the current node for a resolver\n      const addr = await EnsResolver.#getResolver(provider, currentName);\n      // Found a resolver!\n      if (addr != null) {\n        const resolver = new EnsResolver(provider, addr, name);\n        // Legacy resolver found, using EIP-2544 so it isn't safe to use\n        if (currentName !== name && !(await resolver.supportsWildcard())) {\n          return null;\n        }\n        return resolver;\n      }\n      // Get the parent node\n      currentName = currentName.split(\".\").slice(1).join(\".\");\n    }\n  }\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "ZeroAddress", "Contract", "dnsEncode", "<PERSON><PERSON><PERSON>", "hexlify", "isHexString", "toBeHex", "defineProperties", "encodeBase58", "assert", "assertArgument", "isError", "FetchRequest", "getIpfsLink", "link", "match", "substring", "MulticoinProviderPlugin", "name", "constructor", "connect", "proivder", "supportsCoinType", "coinType", "encodeAddress", "address", "Error", "decode<PERSON>ddress", "data", "BasicMulticoinPluginId", "BasicMulticoinProviderPlugin", "matcherIpfs", "RegExp", "matchers", "EnsResolver", "provider", "supports2544", "resolver", "supportsWildcard", "supportsInterface", "error", "fetch", "#fetch", "funcName", "params", "slice", "iface", "interface", "unshift", "fragment", "getFunction", "info", "encodeFunctionData", "push", "enableCcipRead", "result", "decodeFunctionResult", "ethCoinType", "coinPlugin", "plugin", "plugins", "operation", "getText", "key", "getContentHash", "ipfs", "scheme", "length", "parseInt", "swarm", "get<PERSON><PERSON><PERSON>", "avatar", "_get<PERSON>vatar", "url", "linkage", "type", "value", "i", "toLowerCase", "selector", "owner", "comps", "split", "tokenId", "contract", "tokenOwner", "ownerOf", "balance", "balanceOf", "toString", "metadataUrl", "replace", "metadata", "response", "send", "assertOk", "bodyJson", "bodyText", "bytes", "body", "JSON", "stringify", "imageUrl", "image", "getEnsAddress", "network", "getNetwork", "ensPlugin", "getPlugin", "getResolver", "#getResolver", "ensAddr", "addr", "fromName", "currentName", "join"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\ens-resolver.ts"], "sourcesContent": ["/**\n *  ENS is a service which allows easy-to-remember names to map to\n *  network addresses.\n *\n *  @_section: api/providers/ens-resolver:ENS Resolver  [about-ens-rsolver]\n */\n\nimport { getAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/index.js\";\nimport { Contract } from \"../contract/index.js\";\nimport { dnsEncode, namehash } from \"../hash/index.js\";\nimport {\n    hexlify, isHexString, toBeHex,\n    defineProperties, encodeBase58,\n    assert, assertArgument, isError,\n    FetchRequest\n} from \"../utils/index.js\";\n\nimport type { FunctionFragment } from \"../abi/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\nimport type { AbstractProvider, AbstractProviderPlugin } from \"./abstract-provider.js\";\nimport type { EnsPlugin } from \"./plugins-network.js\";\nimport type { Provider } from \"./provider.js\";\n\n// @TODO: This should use the fetch-data:ipfs gateway\n// Trim off the ipfs:// prefix and return the default gateway URL\nfunction getIpfsLink(link: string): string {\n    if (link.match(/^ipfs:\\/\\/ipfs\\//i)) {\n        link = link.substring(12);\n    } else if (link.match(/^ipfs:\\/\\//i)) {\n        link = link.substring(7);\n    } else {\n        assertArgument(false, \"unsupported IPFS format\", \"link\", link);\n    }\n\n    return `https:/\\/gateway.ipfs.io/ipfs/${ link }`;\n}\n\n/**\n *  The type of data found during a steip during avatar resolution.\n */\nexport type AvatarLinkageType = \"name\" | \"avatar\" | \"!avatar\" | \"url\" | \"data\" | \"ipfs\" |\n    \"erc721\" | \"erc1155\" | \"!erc721-caip\" | \"!erc1155-caip\" |\n    \"!owner\" | \"owner\" | \"!balance\" | \"balance\" |\n    \"metadata-url-base\" | \"metadata-url-expanded\" | \"metadata-url\" | \"!metadata-url\" |\n    \"!metadata\" | \"metadata\" |\n    \"!imageUrl\" | \"imageUrl-ipfs\" | \"imageUrl\" | \"!imageUrl-ipfs\";\n\n/**\n *  An individual record for each step during avatar resolution.\n */\nexport interface AvatarLinkage {\n    /**\n     *  The type of linkage.\n     */\n    type: AvatarLinkageType;\n\n    /**\n     *  The linkage value.\n     */\n    value: string;\n};\n\n/**\n *  When resolving an avatar for an ENS name, there are many\n *  steps involved, fetching metadata, validating results, et cetera.\n *\n *  Some applications may wish to analyse this data, or use this data\n *  to diagnose promblems, so an **AvatarResult** provides details of\n *  each completed step during avatar resolution.\n */\nexport interface AvatarResult {\n    /**\n     *  How the [[url]] was arrived at, resolving the many steps required\n     *  for an avatar URL.\n     */\n    linkage: Array<AvatarLinkage>;\n\n    /**\n     *  The avatar URL or null if the avatar was not set, or there was\n     *  an issue during validation (such as the address not owning the\n     *  avatar or a metadata error).\n     */\n    url: null | string;\n};\n\n/**\n *  A provider plugin super-class for processing multicoin address types.\n */\nexport abstract class MulticoinProviderPlugin implements AbstractProviderPlugin {\n    /**\n     *  The name.\n     */\n    readonly name!: string;\n\n    /**\n     *  Creates a new **MulticoinProviderPluing** for %%name%%.\n     */\n    constructor(name: string) {\n        defineProperties<MulticoinProviderPlugin>(this, { name });\n    }\n\n    connect(proivder: Provider): MulticoinProviderPlugin {\n        return this;\n    }\n\n    /**\n     *  Returns ``true`` if %%coinType%% is supported by this plugin.\n     */\n    supportsCoinType(coinType: number): boolean {\n        return false;\n    }\n\n    /**\n     *  Resolves to the encoded %%address%% for %%coinType%%.\n     */\n    async encodeAddress(coinType: number, address: string): Promise<string> {\n        throw new Error(\"unsupported coin\");\n    }\n\n    /**\n     *  Resolves to the decoded %%data%% for %%coinType%%.\n     */\n    async decodeAddress(coinType: number, data: BytesLike): Promise<string> {\n        throw new Error(\"unsupported coin\");\n    }\n}\n\nconst BasicMulticoinPluginId = \"org.ethers.plugins.provider.BasicMulticoin\";\n\n/**\n *  A **BasicMulticoinProviderPlugin** provides service for common\n *  coin types, which do not require additional libraries to encode or\n *  decode.\n */\nexport class BasicMulticoinProviderPlugin extends MulticoinProviderPlugin {\n    /**\n     *  Creates a new **BasicMulticoinProviderPlugin**.\n     */\n    constructor() {\n        super(BasicMulticoinPluginId);\n    }\n}\n\nconst matcherIpfs = new RegExp(\"^(ipfs):/\\/(.*)$\", \"i\");\nconst matchers = [\n    new RegExp(\"^(https):/\\/(.*)$\", \"i\"),\n    new RegExp(\"^(data):(.*)$\", \"i\"),\n    matcherIpfs,\n    new RegExp(\"^eip155:[0-9]+/(erc[0-9]+):(.*)$\", \"i\"),\n];\n\n/**\n *  A connected object to a resolved ENS name resolver, which can be\n *  used to query additional details.\n */\nexport class EnsResolver {\n    /**\n     *  The connected provider.\n     */\n    provider!: AbstractProvider;\n\n    /**\n     *  The address of the resolver.\n     */\n    address!: string;\n\n    /**\n     *  The name this resolver was resolved against.\n     */\n    name!: string;\n\n    // For EIP-2544 names, the ancestor that provided the resolver\n    #supports2544: null | Promise<boolean>;\n\n    #resolver: Contract;\n\n    constructor(provider: AbstractProvider, address: string, name: string) {\n        defineProperties<EnsResolver>(this, { provider, address, name });\n        this.#supports2544 = null;\n\n        this.#resolver = new Contract(address, [\n            \"function supportsInterface(bytes4) view returns (bool)\",\n            \"function resolve(bytes, bytes) view returns (bytes)\",\n            \"function addr(bytes32) view returns (address)\",\n            \"function addr(bytes32, uint) view returns (bytes)\",\n            \"function text(bytes32, string) view returns (string)\",\n            \"function contenthash(bytes32) view returns (bytes)\",\n        ], provider);\n\n    }\n\n    /**\n     *  Resolves to true if the resolver supports wildcard resolution.\n     */\n    async supportsWildcard(): Promise<boolean> {\n        if (this.#supports2544 == null) {\n            this.#supports2544 = (async () => {\n                try {\n                    return await this.#resolver.supportsInterface(\"0x9061b923\");\n                } catch (error) {\n                    // Wildcard resolvers must understand supportsInterface\n                    // and return true.\n                    if (isError(error, \"CALL_EXCEPTION\")) { return false; }\n\n                    // Let future attempts try again...\n                    this.#supports2544 = null;\n\n                    throw error;\n                }\n            })();\n        }\n\n        return await this.#supports2544;\n    }\n\n    async #fetch(funcName: string, params?: Array<any>): Promise<null | any> {\n        params = (params || []).slice();\n        const iface = this.#resolver.interface;\n\n        // The first parameters is always the nodehash\n        params.unshift(namehash(this.name))\n\n        let fragment: null | FunctionFragment = null;\n        if (await this.supportsWildcard()) {\n            fragment = iface.getFunction(funcName);\n            assert(fragment, \"missing fragment\", \"UNKNOWN_ERROR\", {\n                info: { funcName }\n            });\n\n            params = [\n                dnsEncode(this.name, 255),\n                iface.encodeFunctionData(fragment, params)\n            ];\n\n            funcName = \"resolve(bytes,bytes)\";\n        }\n\n        params.push({\n            enableCcipRead: true\n        });\n\n        try {\n            const result = await this.#resolver[funcName](...params);\n\n            if (fragment) {\n                return iface.decodeFunctionResult(fragment, result)[0];\n            }\n\n            return result;\n        } catch (error: any) {\n            if (!isError(error, \"CALL_EXCEPTION\")) { throw error; }\n        }\n\n        return null;\n    }\n\n    /**\n     *  Resolves to the address for %%coinType%% or null if the\n     *  provided %%coinType%% has not been configured.\n     */\n    async getAddress(coinType?: number): Promise<null | string> {\n        if (coinType == null) { coinType = 60; }\n        if (coinType === 60) {\n            try {\n                const result = await this.#fetch(\"addr(bytes32)\");\n\n                // No address\n                if (result == null || result === ZeroAddress) { return null; }\n\n                return result;\n            } catch (error: any) {\n                if (isError(error, \"CALL_EXCEPTION\")) { return null; }\n                throw error;\n            }\n        }\n\n        // Try decoding its EVM canonical chain as an EVM chain address first\n        if (coinType >= 0 && coinType < 0x80000000) {\n            let ethCoinType = coinType + 0x80000000;\n\n            const data = await this.#fetch(\"addr(bytes32,uint)\", [ ethCoinType ]);\n            if (isHexString(data, 20)) { return getAddress(data); }\n        }\n\n        let coinPlugin: null | MulticoinProviderPlugin = null;\n        for (const plugin of this.provider.plugins) {\n            if (!(plugin instanceof MulticoinProviderPlugin)) { continue; }\n            if (plugin.supportsCoinType(coinType)) {\n                coinPlugin = plugin;\n                break;\n            }\n        }\n\n        if (coinPlugin == null) { return null; }\n\n        // keccak256(\"addr(bytes32,uint256\")\n        const data = await this.#fetch(\"addr(bytes32,uint)\", [ coinType ]);\n\n        // No address\n        if (data == null || data === \"0x\") { return null; }\n\n        // Compute the address\n        const address = await coinPlugin.decodeAddress(coinType, data);\n\n        if (address != null) { return address; }\n\n        assert(false, `invalid coin data`, \"UNSUPPORTED_OPERATION\", {\n            operation: `getAddress(${ coinType })`,\n            info: { coinType, data }\n        });\n    }\n\n    /**\n     *  Resolves to the EIP-634 text record for %%key%%, or ``null``\n     *  if unconfigured.\n     */\n    async getText(key: string): Promise<null | string> {\n        const data = await this.#fetch(\"text(bytes32,string)\", [ key ]);\n        if (data == null || data === \"0x\") { return null; }\n        return data;\n    }\n\n    /**\n     *  Rsolves to the content-hash or ``null`` if unconfigured.\n     */\n    async getContentHash(): Promise<null | string> {\n        // keccak256(\"contenthash()\")\n        const data = await this.#fetch(\"contenthash(bytes32)\");\n\n        // No contenthash\n        if (data == null || data === \"0x\") { return null; }\n\n        // IPFS (CID: 1, Type: 70=DAG-PB, 72=libp2p-key)\n        const ipfs = data.match(/^0x(e3010170|e5010172)(([0-9a-f][0-9a-f])([0-9a-f][0-9a-f])([0-9a-f]*))$/);\n        if (ipfs) {\n            const scheme = (ipfs[1] === \"e3010170\") ? \"ipfs\": \"ipns\";\n            const length = parseInt(ipfs[4], 16);\n            if (ipfs[5].length === length * 2) {\n                return `${ scheme }:/\\/${ encodeBase58(\"0x\" + ipfs[2])}`;\n            }\n        }\n\n        // Swarm (CID: 1, Type: swarm-manifest; hash/length hard-coded to keccak256/32)\n        const swarm = data.match(/^0xe40101fa011b20([0-9a-f]*)$/)\n        if (swarm && swarm[1].length === 64) {\n            return `bzz:/\\/${ swarm[1] }`;\n        }\n\n        assert(false, `invalid or unsupported content hash data`, \"UNSUPPORTED_OPERATION\", {\n            operation: \"getContentHash()\",\n            info: { data }\n        });\n    }\n\n    /**\n     *  Resolves to the avatar url or ``null`` if the avatar is either\n     *  unconfigured or incorrectly configured (e.g. references an NFT\n     *  not owned by the address).\n     *\n     *  If diagnosing issues with configurations, the [[_getAvatar]]\n     *  method may be useful.\n     */\n    async getAvatar(): Promise<null | string> {\n        const avatar = await this._getAvatar();\n        return avatar.url;\n    }\n\n    /**\n     *  When resolving an avatar, there are many steps involved, such\n     *  fetching metadata and possibly validating ownership of an\n     *  NFT.\n     *\n     *  This method can be used to examine each step and the value it\n     *  was working from.\n     */\n    async _getAvatar(): Promise<AvatarResult> {\n        const linkage: Array<AvatarLinkage> = [ { type: \"name\", value: this.name } ];\n        try {\n            // test data for ricmoo.eth\n            //const avatar = \"eip155:1/erc721:******************************************/29233\";\n            const avatar = await this.getText(\"avatar\");\n            if (avatar == null) {\n                linkage.push({ type: \"!avatar\", value: \"\" });\n                return { url: null, linkage };\n            }\n            linkage.push({ type: \"avatar\", value: avatar });\n\n            for (let i = 0; i < matchers.length; i++) {\n                const match = avatar.match(matchers[i]);\n                if (match == null) { continue; }\n\n                const scheme = match[1].toLowerCase();\n\n                switch (scheme) {\n                    case \"https\":\n                    case \"data\":\n                        linkage.push({ type: \"url\", value: avatar });\n                        return { linkage, url: avatar };\n                    case \"ipfs\": {\n                        const url = getIpfsLink(avatar);\n                        linkage.push({ type: \"ipfs\", value: avatar });\n                        linkage.push({ type: \"url\", value: url });\n                        return { linkage, url };\n                    }\n\n                    case \"erc721\":\n                    case \"erc1155\": {\n                        // Depending on the ERC type, use tokenURI(uint256) or url(uint256)\n                        const selector = (scheme === \"erc721\") ? \"tokenURI(uint256)\": \"uri(uint256)\";\n                        linkage.push({ type: scheme, value: avatar });\n\n                        // The owner of this name\n                        const owner = await this.getAddress();\n                        if (owner == null) {\n                            linkage.push({ type: \"!owner\", value: \"\" });\n                            return { url: null, linkage };\n                        }\n\n                        const comps = (match[2] || \"\").split(\"/\");\n                        if (comps.length !== 2) {\n                            linkage.push({ type: <any>`!${ scheme }caip`, value: (match[2] || \"\") });\n                            return { url: null, linkage };\n                        }\n\n                        const tokenId = comps[1];\n\n                        const contract = new Contract(comps[0], [\n                            // ERC-721\n                            \"function tokenURI(uint) view returns (string)\",\n                            \"function ownerOf(uint) view returns (address)\",\n\n                            // ERC-1155\n                            \"function uri(uint) view returns (string)\",\n                            \"function balanceOf(address, uint256) view returns (uint)\"\n                        ], this.provider);\n\n                        // Check that this account owns the token\n                        if (scheme === \"erc721\") {\n                            const tokenOwner = await contract.ownerOf(tokenId);\n\n                            if (owner !== tokenOwner) {\n                                linkage.push({ type: \"!owner\", value: tokenOwner });\n                                return { url: null, linkage };\n                            }\n                            linkage.push({ type: \"owner\", value: tokenOwner });\n\n                        } else if (scheme === \"erc1155\") {\n                            const balance = await contract.balanceOf(owner, tokenId);\n                            if (!balance) {\n                                linkage.push({ type: \"!balance\", value: \"0\" });\n                                return { url: null, linkage };\n                            }\n                            linkage.push({ type: \"balance\", value: balance.toString() });\n                        }\n\n                        // Call the token contract for the metadata URL\n                        let metadataUrl = await contract[selector](tokenId);\n                        if (metadataUrl == null || metadataUrl === \"0x\") {\n                            linkage.push({ type: \"!metadata-url\", value: \"\" });\n                            return { url: null, linkage };\n                        }\n\n                        linkage.push({ type: \"metadata-url-base\", value: metadataUrl });\n\n                        // ERC-1155 allows a generic {id} in the URL\n                        if (scheme === \"erc1155\") {\n                            metadataUrl = metadataUrl.replace(\"{id}\", toBeHex(tokenId, 32).substring(2));\n                            linkage.push({ type: \"metadata-url-expanded\", value: metadataUrl });\n                        }\n\n                        // Transform IPFS metadata links\n                        if (metadataUrl.match(/^ipfs:/i)) {\n                            metadataUrl = getIpfsLink(metadataUrl);\n                        }\n                        linkage.push({ type: \"metadata-url\", value: metadataUrl });\n\n                        // Get the token metadata\n                        let metadata: any = { };\n                        const response = await (new FetchRequest(metadataUrl)).send();\n                        response.assertOk();\n\n                        try {\n                            metadata = response.bodyJson;\n                        } catch (error) {\n                            try {\n                                linkage.push({ type: \"!metadata\", value: response.bodyText });\n                            } catch (error) {\n                                const bytes = response.body;\n                                if (bytes) {\n                                    linkage.push({ type: \"!metadata\", value: hexlify(bytes) });\n                                }\n                                return { url: null, linkage };\n                            }\n                            return { url: null, linkage };\n                        }\n\n                        if (!metadata) {\n                            linkage.push({ type: \"!metadata\", value: \"\" });\n                            return { url: null, linkage };\n                        }\n\n                        linkage.push({ type: \"metadata\", value: JSON.stringify(metadata) });\n\n                        // Pull the image URL out\n                        let imageUrl = metadata.image;\n                        if (typeof(imageUrl) !== \"string\") {\n                            linkage.push({ type: \"!imageUrl\", value: \"\" });\n                            return { url: null, linkage };\n                        }\n\n                        if (imageUrl.match(/^(https:\\/\\/|data:)/i)) {\n                            // Allow\n                        } else {\n                            // Transform IPFS link to gateway\n                            const ipfs = imageUrl.match(matcherIpfs);\n                            if (ipfs == null) {\n                                linkage.push({ type: \"!imageUrl-ipfs\", value: imageUrl });\n                                return { url: null, linkage };\n                            }\n\n                            linkage.push({ type: \"imageUrl-ipfs\", value: imageUrl });\n                            imageUrl = getIpfsLink(imageUrl);\n                        }\n\n                        linkage.push({ type: \"url\", value: imageUrl });\n\n                        return { linkage, url: imageUrl };\n                    }\n                }\n            }\n        } catch (error) { }\n\n        return { linkage, url: null };\n    }\n\n    static async getEnsAddress(provider: Provider): Promise<string> {\n        const network = await provider.getNetwork();\n\n        const ensPlugin = network.getPlugin<EnsPlugin>(\"org.ethers.plugins.network.Ens\");\n\n        // No ENS...\n        assert(ensPlugin, \"network does not support ENS\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"getEnsAddress\", info: { network } });\n\n        return ensPlugin.address;\n    }\n\n    static async #getResolver(provider: Provider, name: string): Promise<null | string> {\n        const ensAddr = await EnsResolver.getEnsAddress(provider);\n\n        try {\n            const contract = new Contract(ensAddr, [\n                \"function resolver(bytes32) view returns (address)\"\n            ], provider);\n\n            const addr = await contract.resolver(namehash(name), {\n                enableCcipRead: true\n            });\n\n            if (addr === ZeroAddress) { return null; }\n            return addr;\n\n        } catch (error) {\n            // ENS registry cannot throw errors on resolver(bytes32),\n            // so probably a link error\n            throw error;\n        }\n\n        return null;\n    }\n\n    /**\n     *  Resolve to the ENS resolver for %%name%% using %%provider%% or\n     *  ``null`` if unconfigured.\n     */\n    static async fromName(provider: AbstractProvider, name: string): Promise<null | EnsResolver> {\n\n        let currentName = name;\n        while (true) {\n            if (currentName === \"\" || currentName === \".\") { return null; }\n\n            // Optimization since the eth node cannot change and does\n            // not have a wildcard resolver\n            if (name !== \"eth\" && currentName === \"eth\") { return null; }\n\n            // Check the current node for a resolver\n            const addr = await EnsResolver.#getResolver(provider, currentName);\n\n            // Found a resolver!\n            if (addr != null) {\n                const resolver = new EnsResolver(provider, addr, name);\n\n                // Legacy resolver found, using EIP-2544 so it isn't safe to use\n                if (currentName !== name && !(await resolver.supportsWildcard())) { return null; }\n\n                return resolver;\n            }\n\n            // Get the parent node\n            currentName = currentName.split(\".\").slice(1).join(\".\");\n        }\n    }\n}\n"], "mappings": "AAAA;;;;;;AAOA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,QAAQ,QAAQ,sBAAsB;AAC/C,SAASC,SAAS,EAAEC,QAAQ,QAAQ,kBAAkB;AACtD,SACIC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAC7BC,gBAAgB,EAAEC,YAAY,EAC9BC,MAAM,EAAEC,cAAc,EAAEC,OAAO,EAC/BC,YAAY,QACT,mBAAmB;AAU1B;AACA;AACA,SAASC,WAAWA,CAACC,IAAY;EAC7B,IAAIA,IAAI,CAACC,KAAK,CAAC,mBAAmB,CAAC,EAAE;IACjCD,IAAI,GAAGA,IAAI,CAACE,SAAS,CAAC,EAAE,CAAC;GAC5B,MAAM,IAAIF,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,EAAE;IAClCD,IAAI,GAAGA,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;GAC3B,MAAM;IACHN,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,MAAM,EAAEI,IAAI,CAAC;;EAGlE,OAAO,iCAAkCA,IAAK,EAAE;AACpD;AAyBC;AAuBA;AAED;;;AAGA,OAAM,MAAgBG,uBAAuB;EACzC;;;EAGSC,IAAI;EAEb;;;EAGAC,YAAYD,IAAY;IACpBX,gBAAgB,CAA0B,IAAI,EAAE;MAAEW;IAAI,CAAE,CAAC;EAC7D;EAEAE,OAAOA,CAACC,QAAkB;IACtB,OAAO,IAAI;EACf;EAEA;;;EAGAC,gBAAgBA,CAACC,QAAgB;IAC7B,OAAO,KAAK;EAChB;EAEA;;;EAGA,MAAMC,aAAaA,CAACD,QAAgB,EAAEE,OAAe;IACjD,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;EACvC;EAEA;;;EAGA,MAAMC,aAAaA,CAACJ,QAAgB,EAAEK,IAAe;IACjD,MAAM,IAAIF,KAAK,CAAC,kBAAkB,CAAC;EACvC;;AAGJ,MAAMG,sBAAsB,GAAG,4CAA4C;AAE3E;;;;;AAKA,OAAM,MAAOC,4BAA6B,SAAQb,uBAAuB;EACrE;;;EAGAE,YAAA;IACI,KAAK,CAACU,sBAAsB,CAAC;EACjC;;AAGJ,MAAME,WAAW,GAAG,IAAIC,MAAM,CAAC,kBAAkB,EAAE,GAAG,CAAC;AACvD,MAAMC,QAAQ,GAAG,CACb,IAAID,MAAM,CAAC,mBAAmB,EAAE,GAAG,CAAC,EACpC,IAAIA,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,EAChCD,WAAW,EACX,IAAIC,MAAM,CAAC,kCAAkC,EAAE,GAAG,CAAC,CACtD;AAED;;;;AAIA,OAAM,MAAOE,WAAW;EACpB;;;EAGAC,QAAQ;EAER;;;EAGAV,OAAO;EAEP;;;EAGAP,IAAI;EAEJ;EACA,CAAAkB,YAAa;EAEb,CAAAC,QAAS;EAETlB,YAAYgB,QAA0B,EAAEV,OAAe,EAAEP,IAAY;IACjEX,gBAAgB,CAAc,IAAI,EAAE;MAAE4B,QAAQ;MAAEV,OAAO;MAAEP;IAAI,CAAE,CAAC;IAChE,IAAI,CAAC,CAAAkB,YAAa,GAAG,IAAI;IAEzB,IAAI,CAAC,CAAAC,QAAS,GAAG,IAAIpC,QAAQ,CAACwB,OAAO,EAAE,CACnC,wDAAwD,EACxD,qDAAqD,EACrD,+CAA+C,EAC/C,mDAAmD,EACnD,sDAAsD,EACtD,oDAAoD,CACvD,EAAEU,QAAQ,CAAC;EAEhB;EAEA;;;EAGA,MAAMG,gBAAgBA,CAAA;IAClB,IAAI,IAAI,CAAC,CAAAF,YAAa,IAAI,IAAI,EAAE;MAC5B,IAAI,CAAC,CAAAA,YAAa,GAAG,CAAC,YAAW;QAC7B,IAAI;UACA,OAAO,MAAM,IAAI,CAAC,CAAAC,QAAS,CAACE,iBAAiB,CAAC,YAAY,CAAC;SAC9D,CAAC,OAAOC,KAAK,EAAE;UACZ;UACA;UACA,IAAI7B,OAAO,CAAC6B,KAAK,EAAE,gBAAgB,CAAC,EAAE;YAAE,OAAO,KAAK;;UAEpD;UACA,IAAI,CAAC,CAAAJ,YAAa,GAAG,IAAI;UAEzB,MAAMI,KAAK;;MAEnB,CAAC,EAAC,CAAE;;IAGR,OAAO,MAAM,IAAI,CAAC,CAAAJ,YAAa;EACnC;EAEA,MAAM,CAAAK,KAAMC,CAACC,QAAgB,EAAEC,MAAmB;IAC9CA,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAEC,KAAK,EAAE;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAAT,QAAS,CAACU,SAAS;IAEtC;IACAH,MAAM,CAACI,OAAO,CAAC7C,QAAQ,CAAC,IAAI,CAACe,IAAI,CAAC,CAAC;IAEnC,IAAI+B,QAAQ,GAA4B,IAAI;IAC5C,IAAI,MAAM,IAAI,CAACX,gBAAgB,EAAE,EAAE;MAC/BW,QAAQ,GAAGH,KAAK,CAACI,WAAW,CAACP,QAAQ,CAAC;MACtClC,MAAM,CAACwC,QAAQ,EAAE,kBAAkB,EAAE,eAAe,EAAE;QAClDE,IAAI,EAAE;UAAER;QAAQ;OACnB,CAAC;MAEFC,MAAM,GAAG,CACL1C,SAAS,CAAC,IAAI,CAACgB,IAAI,EAAE,GAAG,CAAC,EACzB4B,KAAK,CAACM,kBAAkB,CAACH,QAAQ,EAAEL,MAAM,CAAC,CAC7C;MAEDD,QAAQ,GAAG,sBAAsB;;IAGrCC,MAAM,CAACS,IAAI,CAAC;MACRC,cAAc,EAAE;KACnB,CAAC;IAEF,IAAI;MACA,MAAMC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAlB,QAAS,CAACM,QAAQ,CAAC,CAAC,GAAGC,MAAM,CAAC;MAExD,IAAIK,QAAQ,EAAE;QACV,OAAOH,KAAK,CAACU,oBAAoB,CAACP,QAAQ,EAAEM,MAAM,CAAC,CAAC,CAAC,CAAC;;MAG1D,OAAOA,MAAM;KAChB,CAAC,OAAOf,KAAU,EAAE;MACjB,IAAI,CAAC7B,OAAO,CAAC6B,KAAK,EAAE,gBAAgB,CAAC,EAAE;QAAE,MAAMA,KAAK;;;IAGxD,OAAO,IAAI;EACf;EAEA;;;;EAIA,MAAMzC,UAAUA,CAACwB,QAAiB;IAC9B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,EAAE;;IACrC,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACjB,IAAI;QACA,MAAMgC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAAd,KAAM,CAAC,eAAe,CAAC;QAEjD;QACA,IAAIc,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAKvD,WAAW,EAAE;UAAE,OAAO,IAAI;;QAE3D,OAAOuD,MAAM;OAChB,CAAC,OAAOf,KAAU,EAAE;QACjB,IAAI7B,OAAO,CAAC6B,KAAK,EAAE,gBAAgB,CAAC,EAAE;UAAE,OAAO,IAAI;;QACnD,MAAMA,KAAK;;;IAInB;IACA,IAAIjB,QAAQ,IAAI,CAAC,IAAIA,QAAQ,GAAG,UAAU,EAAE;MACxC,IAAIkC,WAAW,GAAGlC,QAAQ,GAAG,UAAU;MAEvC,MAAMK,IAAI,GAAG,MAAM,IAAI,CAAC,CAAAa,KAAM,CAAC,oBAAoB,EAAE,CAAEgB,WAAW,CAAE,CAAC;MACrE,IAAIpD,WAAW,CAACuB,IAAI,EAAE,EAAE,CAAC,EAAE;QAAE,OAAO7B,UAAU,CAAC6B,IAAI,CAAC;;;IAGxD,IAAI8B,UAAU,GAAmC,IAAI;IACrD,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACxB,QAAQ,CAACyB,OAAO,EAAE;MACxC,IAAI,EAAED,MAAM,YAAY1C,uBAAuB,CAAC,EAAE;QAAE;;MACpD,IAAI0C,MAAM,CAACrC,gBAAgB,CAACC,QAAQ,CAAC,EAAE;QACnCmC,UAAU,GAAGC,MAAM;QACnB;;;IAIR,IAAID,UAAU,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAErC;IACA,MAAM9B,IAAI,GAAG,MAAM,IAAI,CAAC,CAAAa,KAAM,CAAC,oBAAoB,EAAE,CAAElB,QAAQ,CAAE,CAAC;IAElE;IACA,IAAIK,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEhD;IACA,MAAMH,OAAO,GAAG,MAAMiC,UAAU,CAAC/B,aAAa,CAACJ,QAAQ,EAAEK,IAAI,CAAC;IAE9D,IAAIH,OAAO,IAAI,IAAI,EAAE;MAAE,OAAOA,OAAO;;IAErChB,MAAM,CAAC,KAAK,EAAE,mBAAmB,EAAE,uBAAuB,EAAE;MACxDoD,SAAS,EAAE,cAAetC,QAAS,GAAG;MACtC4B,IAAI,EAAE;QAAE5B,QAAQ;QAAEK;MAAI;KACzB,CAAC;EACN;EAEA;;;;EAIA,MAAMkC,OAAOA,CAACC,GAAW;IACrB,MAAMnC,IAAI,GAAG,MAAM,IAAI,CAAC,CAAAa,KAAM,CAAC,sBAAsB,EAAE,CAAEsB,GAAG,CAAE,CAAC;IAC/D,IAAInC,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;;IAChD,OAAOA,IAAI;EACf;EAEA;;;EAGA,MAAMoC,cAAcA,CAAA;IAChB;IACA,MAAMpC,IAAI,GAAG,MAAM,IAAI,CAAC,CAAAa,KAAM,CAAC,sBAAsB,CAAC;IAEtD;IACA,IAAIb,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;MAAE,OAAO,IAAI;;IAEhD;IACA,MAAMqC,IAAI,GAAGrC,IAAI,CAACb,KAAK,CAAC,0EAA0E,CAAC;IACnG,IAAIkD,IAAI,EAAE;MACN,MAAMC,MAAM,GAAID,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,GAAI,MAAM,GAAE,MAAM;MACxD,MAAME,MAAM,GAAGC,QAAQ,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACpC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACE,MAAM,KAAKA,MAAM,GAAG,CAAC,EAAE;QAC/B,OAAO,GAAID,MAAO,OAAQ1D,YAAY,CAAC,IAAI,GAAGyD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;;;IAIhE;IACA,MAAMI,KAAK,GAAGzC,IAAI,CAACb,KAAK,CAAC,+BAA+B,CAAC;IACzD,IAAIsD,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACF,MAAM,KAAK,EAAE,EAAE;MACjC,OAAO,UAAWE,KAAK,CAAC,CAAC,CAAE,EAAE;;IAGjC5D,MAAM,CAAC,KAAK,EAAE,0CAA0C,EAAE,uBAAuB,EAAE;MAC/EoD,SAAS,EAAE,kBAAkB;MAC7BV,IAAI,EAAE;QAAEvB;MAAI;KACf,CAAC;EACN;EAEA;;;;;;;;EAQA,MAAM0C,SAASA,CAAA;IACX,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACC,UAAU,EAAE;IACtC,OAAOD,MAAM,CAACE,GAAG;EACrB;EAEA;;;;;;;;EAQA,MAAMD,UAAUA,CAAA;IACZ,MAAME,OAAO,GAAyB,CAAE;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,IAAI,CAAC1D;IAAI,CAAE,CAAE;IAC5E,IAAI;MACA;MACA;MACA,MAAMqD,MAAM,GAAG,MAAM,IAAI,CAACT,OAAO,CAAC,QAAQ,CAAC;MAC3C,IAAIS,MAAM,IAAI,IAAI,EAAE;QAChBG,OAAO,CAACrB,IAAI,CAAC;UAAEsB,IAAI,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAE,CAAE,CAAC;QAC5C,OAAO;UAAEH,GAAG,EAAE,IAAI;UAAEC;QAAO,CAAE;;MAEjCA,OAAO,CAACrB,IAAI,CAAC;QAAEsB,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAEL;MAAM,CAAE,CAAC;MAE/C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,QAAQ,CAACkC,MAAM,EAAEU,CAAC,EAAE,EAAE;QACtC,MAAM9D,KAAK,GAAGwD,MAAM,CAACxD,KAAK,CAACkB,QAAQ,CAAC4C,CAAC,CAAC,CAAC;QACvC,IAAI9D,KAAK,IAAI,IAAI,EAAE;UAAE;;QAErB,MAAMmD,MAAM,GAAGnD,KAAK,CAAC,CAAC,CAAC,CAAC+D,WAAW,EAAE;QAErC,QAAQZ,MAAM;UACV,KAAK,OAAO;UACZ,KAAK,MAAM;YACPQ,OAAO,CAACrB,IAAI,CAAC;cAAEsB,IAAI,EAAE,KAAK;cAAEC,KAAK,EAAEL;YAAM,CAAE,CAAC;YAC5C,OAAO;cAAEG,OAAO;cAAED,GAAG,EAAEF;YAAM,CAAE;UACnC,KAAK,MAAM;YAAE;cACT,MAAME,GAAG,GAAG5D,WAAW,CAAC0D,MAAM,CAAC;cAC/BG,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,MAAM;gBAAEC,KAAK,EAAEL;cAAM,CAAE,CAAC;cAC7CG,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,KAAK;gBAAEC,KAAK,EAAEH;cAAG,CAAE,CAAC;cACzC,OAAO;gBAAEC,OAAO;gBAAED;cAAG,CAAE;;UAG3B,KAAK,QAAQ;UACb,KAAK,SAAS;YAAE;cACZ;cACA,MAAMM,QAAQ,GAAIb,MAAM,KAAK,QAAQ,GAAI,mBAAmB,GAAE,cAAc;cAC5EQ,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAET,MAAM;gBAAEU,KAAK,EAAEL;cAAM,CAAE,CAAC;cAE7C;cACA,MAAMS,KAAK,GAAG,MAAM,IAAI,CAACjF,UAAU,EAAE;cACrC,IAAIiF,KAAK,IAAI,IAAI,EAAE;gBACfN,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,QAAQ;kBAAEC,KAAK,EAAE;gBAAE,CAAE,CAAC;gBAC3C,OAAO;kBAAEH,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjC,MAAMO,KAAK,GAAG,CAAClE,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEmE,KAAK,CAAC,GAAG,CAAC;cACzC,IAAID,KAAK,CAACd,MAAM,KAAK,CAAC,EAAE;gBACpBO,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAO,IAAKT,MAAO,MAAM;kBAAEU,KAAK,EAAG7D,KAAK,CAAC,CAAC,CAAC,IAAI;gBAAG,CAAE,CAAC;gBACxE,OAAO;kBAAE0D,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjC,MAAMS,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC;cAExB,MAAMG,QAAQ,GAAG,IAAInF,QAAQ,CAACgF,KAAK,CAAC,CAAC,CAAC,EAAE;cACpC;cACA,+CAA+C,EAC/C,+CAA+C;cAE/C;cACA,0CAA0C,EAC1C,0DAA0D,CAC7D,EAAE,IAAI,CAAC9C,QAAQ,CAAC;cAEjB;cACA,IAAI+B,MAAM,KAAK,QAAQ,EAAE;gBACrB,MAAMmB,UAAU,GAAG,MAAMD,QAAQ,CAACE,OAAO,CAACH,OAAO,CAAC;gBAElD,IAAIH,KAAK,KAAKK,UAAU,EAAE;kBACtBX,OAAO,CAACrB,IAAI,CAAC;oBAAEsB,IAAI,EAAE,QAAQ;oBAAEC,KAAK,EAAES;kBAAU,CAAE,CAAC;kBACnD,OAAO;oBAAEZ,GAAG,EAAE,IAAI;oBAAEC;kBAAO,CAAE;;gBAEjCA,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,OAAO;kBAAEC,KAAK,EAAES;gBAAU,CAAE,CAAC;eAErD,MAAM,IAAInB,MAAM,KAAK,SAAS,EAAE;gBAC7B,MAAMqB,OAAO,GAAG,MAAMH,QAAQ,CAACI,SAAS,CAACR,KAAK,EAAEG,OAAO,CAAC;gBACxD,IAAI,CAACI,OAAO,EAAE;kBACVb,OAAO,CAACrB,IAAI,CAAC;oBAAEsB,IAAI,EAAE,UAAU;oBAAEC,KAAK,EAAE;kBAAG,CAAE,CAAC;kBAC9C,OAAO;oBAAEH,GAAG,EAAE,IAAI;oBAAEC;kBAAO,CAAE;;gBAEjCA,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,SAAS;kBAAEC,KAAK,EAAEW,OAAO,CAACE,QAAQ;gBAAE,CAAE,CAAC;;cAGhE;cACA,IAAIC,WAAW,GAAG,MAAMN,QAAQ,CAACL,QAAQ,CAAC,CAACI,OAAO,CAAC;cACnD,IAAIO,WAAW,IAAI,IAAI,IAAIA,WAAW,KAAK,IAAI,EAAE;gBAC7ChB,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,eAAe;kBAAEC,KAAK,EAAE;gBAAE,CAAE,CAAC;gBAClD,OAAO;kBAAEH,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjCA,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,mBAAmB;gBAAEC,KAAK,EAAEc;cAAW,CAAE,CAAC;cAE/D;cACA,IAAIxB,MAAM,KAAK,SAAS,EAAE;gBACtBwB,WAAW,GAAGA,WAAW,CAACC,OAAO,CAAC,MAAM,EAAErF,OAAO,CAAC6E,OAAO,EAAE,EAAE,CAAC,CAACnE,SAAS,CAAC,CAAC,CAAC,CAAC;gBAC5E0D,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,uBAAuB;kBAAEC,KAAK,EAAEc;gBAAW,CAAE,CAAC;;cAGvE;cACA,IAAIA,WAAW,CAAC3E,KAAK,CAAC,SAAS,CAAC,EAAE;gBAC9B2E,WAAW,GAAG7E,WAAW,CAAC6E,WAAW,CAAC;;cAE1ChB,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,cAAc;gBAAEC,KAAK,EAAEc;cAAW,CAAE,CAAC;cAE1D;cACA,IAAIE,QAAQ,GAAQ,EAAG;cACvB,MAAMC,QAAQ,GAAG,MAAO,IAAIjF,YAAY,CAAC8E,WAAW,CAAC,CAAEI,IAAI,EAAE;cAC7DD,QAAQ,CAACE,QAAQ,EAAE;cAEnB,IAAI;gBACAH,QAAQ,GAAGC,QAAQ,CAACG,QAAQ;eAC/B,CAAC,OAAOxD,KAAK,EAAE;gBACZ,IAAI;kBACAkC,OAAO,CAACrB,IAAI,CAAC;oBAAEsB,IAAI,EAAE,WAAW;oBAAEC,KAAK,EAAEiB,QAAQ,CAACI;kBAAQ,CAAE,CAAC;iBAChE,CAAC,OAAOzD,KAAK,EAAE;kBACZ,MAAM0D,KAAK,GAAGL,QAAQ,CAACM,IAAI;kBAC3B,IAAID,KAAK,EAAE;oBACPxB,OAAO,CAACrB,IAAI,CAAC;sBAAEsB,IAAI,EAAE,WAAW;sBAAEC,KAAK,EAAExE,OAAO,CAAC8F,KAAK;oBAAC,CAAE,CAAC;;kBAE9D,OAAO;oBAAEzB,GAAG,EAAE,IAAI;oBAAEC;kBAAO,CAAE;;gBAEjC,OAAO;kBAAED,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjC,IAAI,CAACkB,QAAQ,EAAE;gBACXlB,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,WAAW;kBAAEC,KAAK,EAAE;gBAAE,CAAE,CAAC;gBAC9C,OAAO;kBAAEH,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjCA,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,UAAU;gBAAEC,KAAK,EAAEwB,IAAI,CAACC,SAAS,CAACT,QAAQ;cAAC,CAAE,CAAC;cAEnE;cACA,IAAIU,QAAQ,GAAGV,QAAQ,CAACW,KAAK;cAC7B,IAAI,OAAOD,QAAS,KAAK,QAAQ,EAAE;gBAC/B5B,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,WAAW;kBAAEC,KAAK,EAAE;gBAAE,CAAE,CAAC;gBAC9C,OAAO;kBAAEH,GAAG,EAAE,IAAI;kBAAEC;gBAAO,CAAE;;cAGjC,IAAI4B,QAAQ,CAACvF,KAAK,CAAC,sBAAsB,CAAC,EAAE;gBACxC;cAAA,CACH,MAAM;gBACH;gBACA,MAAMkD,IAAI,GAAGqC,QAAQ,CAACvF,KAAK,CAACgB,WAAW,CAAC;gBACxC,IAAIkC,IAAI,IAAI,IAAI,EAAE;kBACdS,OAAO,CAACrB,IAAI,CAAC;oBAAEsB,IAAI,EAAE,gBAAgB;oBAAEC,KAAK,EAAE0B;kBAAQ,CAAE,CAAC;kBACzD,OAAO;oBAAE7B,GAAG,EAAE,IAAI;oBAAEC;kBAAO,CAAE;;gBAGjCA,OAAO,CAACrB,IAAI,CAAC;kBAAEsB,IAAI,EAAE,eAAe;kBAAEC,KAAK,EAAE0B;gBAAQ,CAAE,CAAC;gBACxDA,QAAQ,GAAGzF,WAAW,CAACyF,QAAQ,CAAC;;cAGpC5B,OAAO,CAACrB,IAAI,CAAC;gBAAEsB,IAAI,EAAE,KAAK;gBAAEC,KAAK,EAAE0B;cAAQ,CAAE,CAAC;cAE9C,OAAO;gBAAE5B,OAAO;gBAAED,GAAG,EAAE6B;cAAQ,CAAE;;;;KAIhD,CAAC,OAAO9D,KAAK,EAAE;IAEhB,OAAO;MAAEkC,OAAO;MAAED,GAAG,EAAE;IAAI,CAAE;EACjC;EAEA,aAAa+B,aAAaA,CAACrE,QAAkB;IACzC,MAAMsE,OAAO,GAAG,MAAMtE,QAAQ,CAACuE,UAAU,EAAE;IAE3C,MAAMC,SAAS,GAAGF,OAAO,CAACG,SAAS,CAAY,gCAAgC,CAAC;IAEhF;IACAnG,MAAM,CAACkG,SAAS,EAAE,8BAA8B,EAAE,uBAAuB,EAAE;MACvE9C,SAAS,EAAE,eAAe;MAAEV,IAAI,EAAE;QAAEsD;MAAO;KAAI,CAAC;IAEpD,OAAOE,SAAS,CAAClF,OAAO;EAC5B;EAEA,aAAa,CAAAoF,WAAYC,CAAC3E,QAAkB,EAAEjB,IAAY;IACtD,MAAM6F,OAAO,GAAG,MAAM7E,WAAW,CAACsE,aAAa,CAACrE,QAAQ,CAAC;IAEzD,IAAI;MACA,MAAMiD,QAAQ,GAAG,IAAInF,QAAQ,CAAC8G,OAAO,EAAE,CACnC,mDAAmD,CACtD,EAAE5E,QAAQ,CAAC;MAEZ,MAAM6E,IAAI,GAAG,MAAM5B,QAAQ,CAAC/C,QAAQ,CAAClC,QAAQ,CAACe,IAAI,CAAC,EAAE;QACjDoC,cAAc,EAAE;OACnB,CAAC;MAEF,IAAI0D,IAAI,KAAKhH,WAAW,EAAE;QAAE,OAAO,IAAI;;MACvC,OAAOgH,IAAI;KAEd,CAAC,OAAOxE,KAAK,EAAE;MACZ;MACA;MACA,MAAMA,KAAK;;IAGf,OAAO,IAAI;EACf;EAEA;;;;EAIA,aAAayE,QAAQA,CAAC9E,QAA0B,EAAEjB,IAAY;IAE1D,IAAIgG,WAAW,GAAGhG,IAAI;IACtB,OAAO,IAAI,EAAE;MACT,IAAIgG,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAK,GAAG,EAAE;QAAE,OAAO,IAAI;;MAE5D;MACA;MACA,IAAIhG,IAAI,KAAK,KAAK,IAAIgG,WAAW,KAAK,KAAK,EAAE;QAAE,OAAO,IAAI;;MAE1D;MACA,MAAMF,IAAI,GAAG,MAAM9E,WAAW,CAAC,CAAA2E,WAAY,CAAC1E,QAAQ,EAAE+E,WAAW,CAAC;MAElE;MACA,IAAIF,IAAI,IAAI,IAAI,EAAE;QACd,MAAM3E,QAAQ,GAAG,IAAIH,WAAW,CAACC,QAAQ,EAAE6E,IAAI,EAAE9F,IAAI,CAAC;QAEtD;QACA,IAAIgG,WAAW,KAAKhG,IAAI,IAAI,EAAE,MAAMmB,QAAQ,CAACC,gBAAgB,EAAE,CAAC,EAAE;UAAE,OAAO,IAAI;;QAE/E,OAAOD,QAAQ;;MAGnB;MACA6E,WAAW,GAAGA,WAAW,CAAChC,KAAK,CAAC,GAAG,CAAC,CAACrC,KAAK,CAAC,CAAC,CAAC,CAACsE,IAAI,CAAC,GAAG,CAAC;;EAE/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}