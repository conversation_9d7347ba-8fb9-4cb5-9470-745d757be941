{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { keccak256 as _keccak256, sha256 as _sha256 } from \"../crypto/index.js\";\nimport { concat, dataLength, getBytes, hexlify, toBeArray, toTwos, toUtf8Bytes, zeroPadBytes, zeroPadValue, assertArgument } from \"../utils/index.js\";\nconst regexBytes = new RegExp(\"^bytes([0-9]+)$\");\nconst regexNumber = new RegExp(\"^(u?int)([0-9]*)$\");\nconst regexArray = new RegExp(\"^(.*)\\\\[([0-9]*)\\\\]$\");\nfunction _pack(type, value, isArray) {\n  switch (type) {\n    case \"address\":\n      if (isArray) {\n        return getBytes(zeroPadValue(value, 32));\n      }\n      return getBytes(getAddress(value));\n    case \"string\":\n      return toUtf8Bytes(value);\n    case \"bytes\":\n      return getBytes(value);\n    case \"bool\":\n      value = !!value ? \"0x01\" : \"0x00\";\n      if (isArray) {\n        return getBytes(zeroPadValue(value, 32));\n      }\n      return getBytes(value);\n  }\n  let match = type.match(regexNumber);\n  if (match) {\n    let signed = match[1] === \"int\";\n    let size = parseInt(match[2] || \"256\");\n    assertArgument((!match[2] || match[2] === String(size)) && size % 8 === 0 && size !== 0 && size <= 256, \"invalid number type\", \"type\", type);\n    if (isArray) {\n      size = 256;\n    }\n    if (signed) {\n      value = toTwos(value, size);\n    }\n    return getBytes(zeroPadValue(toBeArray(value), size / 8));\n  }\n  match = type.match(regexBytes);\n  if (match) {\n    const size = parseInt(match[1]);\n    assertArgument(String(size) === match[1] && size !== 0 && size <= 32, \"invalid bytes type\", \"type\", type);\n    assertArgument(dataLength(value) === size, `invalid value for ${type}`, \"value\", value);\n    if (isArray) {\n      return getBytes(zeroPadBytes(value, 32));\n    }\n    return value;\n  }\n  match = type.match(regexArray);\n  if (match && Array.isArray(value)) {\n    const baseType = match[1];\n    const count = parseInt(match[2] || String(value.length));\n    assertArgument(count === value.length, `invalid array length for ${type}`, \"value\", value);\n    const result = [];\n    value.forEach(function (value) {\n      result.push(_pack(baseType, value, true));\n    });\n    return getBytes(concat(result));\n  }\n  assertArgument(false, \"invalid type\", \"type\", type);\n}\n// @TODO: Array Enum\n/**\n *   Computes the [[link-solc-packed]] representation of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPacked([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPacked(types, values) {\n  assertArgument(types.length === values.length, \"wrong number of values; expected ${ types.length }\", \"values\", values);\n  const tight = [];\n  types.forEach(function (type, index) {\n    tight.push(_pack(type, values[index]));\n  });\n  return hexlify(concat(tight));\n}\n/**\n *   Computes the [[link-solc-packed]] [[keccak256]] hash of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPackedKeccak256([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPackedKeccak256(types, values) {\n  return _keccak256(solidityPacked(types, values));\n}\n/**\n *   Computes the [[link-solc-packed]] [[sha256]] hash of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPackedSha256([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPackedSha256(types, values) {\n  return _sha256(solidityPacked(types, values));\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "keccak256", "_keccak256", "sha256", "_sha256", "concat", "dataLength", "getBytes", "hexlify", "toBeArray", "toTwos", "toUtf8Bytes", "zeroPadBytes", "zeroPadValue", "assertArgument", "regexBytes", "RegExp", "regexNumber", "regexArray", "_pack", "type", "value", "isArray", "match", "signed", "size", "parseInt", "String", "Array", "baseType", "count", "length", "result", "for<PERSON>ach", "push", "solidityPacked", "types", "values", "tight", "index", "solidityPackedKeccak256", "solidityPackedSha256"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\hash\\solidity.ts"], "sourcesContent": ["import { getAddress } from \"../address/index.js\";\nimport {\n    keccak256 as _keccak256, sha256 as _sha256\n} from \"../crypto/index.js\";\nimport {\n    concat, dataLength, getBytes, hexlify, toBeArray, toTwos, toUtf8Bytes, zeroPadBytes, zeroPadValue,\n    assertArgument\n} from \"../utils/index.js\";\n\n\nconst regexBytes = new RegExp(\"^bytes([0-9]+)$\");\nconst regexNumber = new RegExp(\"^(u?int)([0-9]*)$\");\nconst regexArray = new RegExp(\"^(.*)\\\\[([0-9]*)\\\\]$\");\n\n\nfunction _pack(type: string, value: any, isArray?: boolean): Uint8Array {\n    switch(type) {\n        case \"address\":\n            if (isArray) { return getBytes(zeroPadValue(value, 32)); }\n            return getBytes(getAddress(value));\n        case \"string\":\n            return toUtf8Bytes(value);\n        case \"bytes\":\n            return getBytes(value);\n        case \"bool\":\n            value = (!!value ? \"0x01\": \"0x00\");\n            if (isArray) { return getBytes(zeroPadValue(value, 32)); }\n            return getBytes(value);\n    }\n\n    let match =  type.match(regexNumber);\n    if (match) {\n        let signed = (match[1] === \"int\");\n        let size = parseInt(match[2] || \"256\")\n\n        assertArgument((!match[2] || match[2] === String(size)) && (size % 8 === 0) && size !== 0 && size <= 256, \"invalid number type\", \"type\", type);\n\n        if (isArray) { size = 256; }\n\n        if (signed) { value = toTwos(value, size); }\n\n        return getBytes(zeroPadValue(toBeArray(value), size / 8));\n    }\n\n    match = type.match(regexBytes);\n    if (match) {\n        const size = parseInt(match[1]);\n\n        assertArgument(String(size) === match[1] && size !== 0 && size <= 32, \"invalid bytes type\", \"type\", type);\n        assertArgument(dataLength(value) === size, `invalid value for ${ type }`, \"value\", value);\n\n        if (isArray) { return getBytes(zeroPadBytes(value, 32)); }\n        return value;\n    }\n\n    match = type.match(regexArray);\n    if (match && Array.isArray(value)) {\n        const baseType = match[1];\n        const count = parseInt(match[2] || String(value.length));\n        assertArgument(count === value.length, `invalid array length for ${ type }`, \"value\", value);\n\n        const result: Array<Uint8Array> = [];\n        value.forEach(function(value) {\n            result.push(_pack(baseType, value, true));\n        });\n        return getBytes(concat(result));\n    }\n\n    assertArgument(false, \"invalid type\", \"type\", type)\n}\n\n// @TODO: Array Enum\n\n/**\n *   Computes the [[link-solc-packed]] representation of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPacked([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPacked(types: ReadonlyArray<string>, values: ReadonlyArray<any>): string {\n    assertArgument(types.length === values.length, \"wrong number of values; expected ${ types.length }\", \"values\", values);\n\n    const tight: Array<Uint8Array> = [];\n    types.forEach(function(type, index) {\n        tight.push(_pack(type, values[index]));\n    });\n    return hexlify(concat(tight));\n}\n\n/**\n *   Computes the [[link-solc-packed]] [[keccak256]] hash of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPackedKeccak256([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPackedKeccak256(types: ReadonlyArray<string>, values: ReadonlyArray<any>): string {\n    return _keccak256(solidityPacked(types, values));\n}\n\n/**\n *   Computes the [[link-solc-packed]] [[sha256]] hash of %%values%%\n *   respectively to their %%types%%.\n *\n *   @example:\n *       addr = \"0x8ba1f109551bd432803012645ac136ddd64dba72\"\n *       solidityPackedSha256([ \"address\", \"uint\" ], [ addr, 45 ]);\n *       //_result:\n */\nexport function solidityPackedSha256(types: ReadonlyArray<string>, values: ReadonlyArray<any>): string {\n    return _sha256(solidityPacked(types, values));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SACIC,SAAS,IAAIC,UAAU,EAAEC,MAAM,IAAIC,OAAO,QACvC,oBAAoB;AAC3B,SACIC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,YAAY,EACjGC,cAAc,QACX,mBAAmB;AAG1B,MAAMC,UAAU,GAAG,IAAIC,MAAM,CAAC,iBAAiB,CAAC;AAChD,MAAMC,WAAW,GAAG,IAAID,MAAM,CAAC,mBAAmB,CAAC;AACnD,MAAME,UAAU,GAAG,IAAIF,MAAM,CAAC,sBAAsB,CAAC;AAGrD,SAASG,KAAKA,CAACC,IAAY,EAAEC,KAAU,EAAEC,OAAiB;EACtD,QAAOF,IAAI;IACP,KAAK,SAAS;MACV,IAAIE,OAAO,EAAE;QAAE,OAAOf,QAAQ,CAACM,YAAY,CAACQ,KAAK,EAAE,EAAE,CAAC,CAAC;;MACvD,OAAOd,QAAQ,CAACP,UAAU,CAACqB,KAAK,CAAC,CAAC;IACtC,KAAK,QAAQ;MACT,OAAOV,WAAW,CAACU,KAAK,CAAC;IAC7B,KAAK,OAAO;MACR,OAAOd,QAAQ,CAACc,KAAK,CAAC;IAC1B,KAAK,MAAM;MACPA,KAAK,GAAI,CAAC,CAACA,KAAK,GAAG,MAAM,GAAE,MAAO;MAClC,IAAIC,OAAO,EAAE;QAAE,OAAOf,QAAQ,CAACM,YAAY,CAACQ,KAAK,EAAE,EAAE,CAAC,CAAC;;MACvD,OAAOd,QAAQ,CAACc,KAAK,CAAC;;EAG9B,IAAIE,KAAK,GAAIH,IAAI,CAACG,KAAK,CAACN,WAAW,CAAC;EACpC,IAAIM,KAAK,EAAE;IACP,IAAIC,MAAM,GAAID,KAAK,CAAC,CAAC,CAAC,KAAK,KAAM;IACjC,IAAIE,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;IAEtCT,cAAc,CAAC,CAAC,CAACS,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAKI,MAAM,CAACF,IAAI,CAAC,KAAMA,IAAI,GAAG,CAAC,KAAK,CAAE,IAAIA,IAAI,KAAK,CAAC,IAAIA,IAAI,IAAI,GAAG,EAAE,qBAAqB,EAAE,MAAM,EAAEL,IAAI,CAAC;IAE9I,IAAIE,OAAO,EAAE;MAAEG,IAAI,GAAG,GAAG;;IAEzB,IAAID,MAAM,EAAE;MAAEH,KAAK,GAAGX,MAAM,CAACW,KAAK,EAAEI,IAAI,CAAC;;IAEzC,OAAOlB,QAAQ,CAACM,YAAY,CAACJ,SAAS,CAACY,KAAK,CAAC,EAAEI,IAAI,GAAG,CAAC,CAAC,CAAC;;EAG7DF,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACR,UAAU,CAAC;EAC9B,IAAIQ,KAAK,EAAE;IACP,MAAME,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IAE/BT,cAAc,CAACa,MAAM,CAACF,IAAI,CAAC,KAAKF,KAAK,CAAC,CAAC,CAAC,IAAIE,IAAI,KAAK,CAAC,IAAIA,IAAI,IAAI,EAAE,EAAE,oBAAoB,EAAE,MAAM,EAAEL,IAAI,CAAC;IACzGN,cAAc,CAACR,UAAU,CAACe,KAAK,CAAC,KAAKI,IAAI,EAAE,qBAAsBL,IAAK,EAAE,EAAE,OAAO,EAAEC,KAAK,CAAC;IAEzF,IAAIC,OAAO,EAAE;MAAE,OAAOf,QAAQ,CAACK,YAAY,CAACS,KAAK,EAAE,EAAE,CAAC,CAAC;;IACvD,OAAOA,KAAK;;EAGhBE,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACL,UAAU,CAAC;EAC9B,IAAIK,KAAK,IAAIK,KAAK,CAACN,OAAO,CAACD,KAAK,CAAC,EAAE;IAC/B,MAAMQ,QAAQ,GAAGN,KAAK,CAAC,CAAC,CAAC;IACzB,MAAMO,KAAK,GAAGJ,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,IAAII,MAAM,CAACN,KAAK,CAACU,MAAM,CAAC,CAAC;IACxDjB,cAAc,CAACgB,KAAK,KAAKT,KAAK,CAACU,MAAM,EAAE,4BAA6BX,IAAK,EAAE,EAAE,OAAO,EAAEC,KAAK,CAAC;IAE5F,MAAMW,MAAM,GAAsB,EAAE;IACpCX,KAAK,CAACY,OAAO,CAAC,UAASZ,KAAK;MACxBW,MAAM,CAACE,IAAI,CAACf,KAAK,CAACU,QAAQ,EAAER,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACF,MAAM,CAAC2B,MAAM,CAAC,CAAC;;EAGnClB,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,MAAM,EAAEM,IAAI,CAAC;AACvD;AAEA;AAEA;;;;;;;;;AASA,OAAM,SAAUe,cAAcA,CAACC,KAA4B,EAAEC,MAA0B;EACnFvB,cAAc,CAACsB,KAAK,CAACL,MAAM,KAAKM,MAAM,CAACN,MAAM,EAAE,oDAAoD,EAAE,QAAQ,EAAEM,MAAM,CAAC;EAEtH,MAAMC,KAAK,GAAsB,EAAE;EACnCF,KAAK,CAACH,OAAO,CAAC,UAASb,IAAI,EAAEmB,KAAK;IAC9BD,KAAK,CAACJ,IAAI,CAACf,KAAK,CAACC,IAAI,EAAEiB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC;EAC1C,CAAC,CAAC;EACF,OAAO/B,OAAO,CAACH,MAAM,CAACiC,KAAK,CAAC,CAAC;AACjC;AAEA;;;;;;;;;AASA,OAAM,SAAUE,uBAAuBA,CAACJ,KAA4B,EAAEC,MAA0B;EAC5F,OAAOnC,UAAU,CAACiC,cAAc,CAACC,KAAK,EAAEC,MAAM,CAAC,CAAC;AACpD;AAEA;;;;;;;;;AASA,OAAM,SAAUI,oBAAoBA,CAACL,KAA4B,EAAEC,MAA0B;EACzF,OAAOjC,OAAO,CAAC+B,cAAc,CAACC,KAAK,EAAEC,MAAM,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}