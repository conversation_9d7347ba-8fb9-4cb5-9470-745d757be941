{"ast": null, "code": "import { getAddress } from \"../../address/index.js\";\nimport { toBeHex } from \"../../utils/maths.js\";\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n/**\n *  @_ignore\n */\nexport class AddressCoder extends Coder {\n  constructor(localName) {\n    super(\"address\", \"address\", localName, false);\n  }\n  defaultValue() {\n    return \"******************************************\";\n  }\n  encode(writer, _value) {\n    let value = Typed.dereference(_value, \"string\");\n    try {\n      value = getAddress(value);\n    } catch (error) {\n      return this._throwError(error.message, _value);\n    }\n    return writer.writeValue(value);\n  }\n  decode(reader) {\n    return getAddress(toBeHex(reader.readValue(), 20));\n  }\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "toBeHex", "Typed", "Coder", "AddressCoder", "constructor", "localName", "defaultValue", "encode", "writer", "_value", "value", "dereference", "error", "_throwError", "message", "writeValue", "decode", "reader", "readValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\address.ts"], "sourcesContent": ["import { getAddress } from \"../../address/index.js\";\nimport { toBeHex } from \"../../utils/maths.js\";\n\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n\n/**\n *  @_ignore\n */\nexport class AddressCoder extends Coder {\n\n    constructor(localName: string) {\n        super(\"address\", \"address\", localName, false);\n    }\n\n    defaultValue(): string {\n        return \"******************************************\";\n    }\n\n    encode(writer: Writer, _value: string | Typed): number {\n        let value = Typed.dereference(_value, \"string\");\n        try {\n            value = getAddress(value);\n        } catch (error: any) {\n            return this._throwError(error.message, _value);\n        }\n        return writer.writeValue(value);\n    }\n\n    decode(reader: Reader): any {\n        return getAddress(toBeHex(reader.readValue(), 20));\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,wBAAwB;AACnD,SAASC,OAAO,QAAQ,sBAAsB;AAE9C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,qBAAqB;AAK3C;;;AAGA,OAAM,MAAOC,YAAa,SAAQD,KAAK;EAEnCE,YAAYC,SAAiB;IACzB,KAAK,CAAC,SAAS,EAAE,SAAS,EAAEA,SAAS,EAAE,KAAK,CAAC;EACjD;EAEAC,YAAYA,CAAA;IACR,OAAO,4CAA4C;EACvD;EAEAC,MAAMA,CAACC,MAAc,EAAEC,MAAsB;IACzC,IAAIC,KAAK,GAAGT,KAAK,CAACU,WAAW,CAACF,MAAM,EAAE,QAAQ,CAAC;IAC/C,IAAI;MACAC,KAAK,GAAGX,UAAU,CAACW,KAAK,CAAC;KAC5B,CAAC,OAAOE,KAAU,EAAE;MACjB,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAACE,OAAO,EAAEL,MAAM,CAAC;;IAElD,OAAOD,MAAM,CAACO,UAAU,CAACL,KAAK,CAAC;EACnC;EAEAM,MAAMA,CAACC,MAAc;IACjB,OAAOlB,UAAU,CAACC,OAAO,CAACiB,MAAM,CAACC,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;EACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}