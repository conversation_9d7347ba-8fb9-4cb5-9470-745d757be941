{"_format": "hh-sol-cache-2", "files": {"C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\contracts\\CQTToken.sol": {"lastModificationDate": 1748529773279, "contentHash": "1f175b9919c41239c1859f3b3c600633", "sourceName": "contracts/CQTToken.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/token/ERC20/ERC20.sol", "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/Pausable.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["CQTToken"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\utils\\Pausable.sol": {"lastModificationDate": 1748533822043, "contentHash": "0d47b53e10b1985efbb396f937626279", "sourceName": "@openzeppelin/contracts/utils/Pausable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Pausable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\access\\Ownable.sol": {"lastModificationDate": 1748533822022, "contentHash": "d3c790edc9ccf808a17c5a6cd13614fd", "sourceName": "@openzeppelin/contracts/access/Ownable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["Ownable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\ERC20.sol": {"lastModificationDate": 1748533821522, "contentHash": "57d79df281f57bbb1b09214c7914f877", "sourceName": "@openzeppelin/contracts/token/ERC20/ERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["./IERC20.sol", "./extensions/IERC20Metadata.sol", "../../utils/Context.sol", "../../interfaces/draft-IERC6093.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\ERC20Burnable.sol": {"lastModificationDate": 1748533821527, "contentHash": "273d8d24b06f67207dd5f35c3a0c1086", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../ERC20.sol", "../../../utils/Context.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["ERC20Burnable"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\utils\\Context.sol": {"lastModificationDate": 1748533821324, "contentHash": "67bfbc07588eb8683b3fd8f6f909563e", "sourceName": "@openzeppelin/contracts/utils/Context.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Context"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\interfaces\\draft-IERC6093.sol": {"lastModificationDate": 1748533821403, "contentHash": "267d92fe4de67b1bdb3302c08f387dbf", "sourceName": "@openzeppelin/contracts/interfaces/draft-IERC6093.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC1155Errors", "IERC20Errors", "IERC721Errors"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\IERC20.sol": {"lastModificationDate": 1748533821828, "contentHash": "8f19f64d2adadf448840908bbaf431c8", "sourceName": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\token\\ERC20\\extensions\\IERC20Metadata.sol": {"lastModificationDate": 1748533821839, "contentHash": "794db3115001aa372c79326fcfd44b1f", "sourceName": "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["../IERC20.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["IERC20Metadata"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\contracts\\StakingPool.sol": {"lastModificationDate": 1748529841448, "contentHash": "2aa23a623bc42dacf4b15b76f05b5cb2", "sourceName": "contracts/StakingPool.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "./CQTToken.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["StakingPool"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\node_modules\\@openzeppelin\\contracts\\utils\\ReentrancyGuard.sol": {"lastModificationDate": 1748533822064, "contentHash": "190613e556d509d9e9a0ea43dc5d891d", "sourceName": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": [], "versionPragmas": ["^0.8.20"], "artifacts": ["Reentrancy<PERSON><PERSON>"]}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\contracts\\contracts\\GameTreasury.sol": {"lastModificationDate": 1748529808073, "contentHash": "e3c1855d9a51ee53ebb9e1ddd8496417", "sourceName": "contracts/GameTreasury.sol", "solcConfig": {"version": "0.8.20", "settings": {"optimizer": {"enabled": true, "runs": 200}, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "imports": ["@openzeppelin/contracts/access/Ownable.sol", "@openzeppelin/contracts/utils/Pausable.sol", "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "./CQTToken.sol"], "versionPragmas": ["^0.8.20"], "artifacts": ["GameTreasury"]}}}