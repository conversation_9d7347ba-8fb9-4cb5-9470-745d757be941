/**
 * @typedef {object} Phaser.Types.Renderer.WebGL.WebGLPipelineBatchEntry
 * @since 3.60.0
 *
 * @property {number} start - The vertext count this batch entry starts from.
 * @property {number} count - The total number of vertices in this batch entry.
 * @property {number} unit - The current texture unit of the batch entry.
 * @property {number} maxUnit - The maximum number of texture units in this batch entry.
 * @property {Phaser.Renderer.WebGL.Wrappers.WebGLTextureWrapper[]} texture - An array of WebGLTextureWrapper references used in this batch entry.
 */
