{"ast": null, "code": "import { hash as assertHash, bytes as assertBytes, exists as assertExists } from './_assert.js';\nimport { Hash, toBytes } from './utils.js';\n// HMAC (RFC 2104)\nexport class HMAC extends Hash {\n  constructor(hash, _key) {\n    super();\n    this.finished = false;\n    this.destroyed = false;\n    assertHash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create();\n    if (typeof this.iHash.update !== 'function') throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create();\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    pad.fill(0);\n  }\n  update(buf) {\n    assertExists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out) {\n    assertExists(this);\n    assertBytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest() {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to) {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to || (to = Object.create(Object.getPrototypeOf(this), {}));\n    const {\n      oHash,\n      iHash,\n      finished,\n      destroyed,\n      blockLen,\n      outputLen\n    } = this;\n    to = to;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */\nexport const hmac = (hash, key, message) => new HMAC(hash, key).update(message).digest();\nhmac.create = (hash, key) => new HMAC(hash, key);", "map": {"version": 3, "names": ["hash", "assertHash", "bytes", "assertBytes", "exists", "assertExists", "Hash", "toBytes", "HMAC", "constructor", "_key", "finished", "destroyed", "key", "iHash", "create", "update", "Error", "blockLen", "outputLen", "pad", "Uint8Array", "set", "length", "digest", "i", "oHash", "fill", "buf", "digestInto", "out", "destroy", "_cloneInto", "to", "Object", "getPrototypeOf", "hmac", "message"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\hashes\\src\\hmac.ts"], "sourcesContent": ["import { hash as assertHash, bytes as assertBytes, exists as assertExists } from './_assert.js';\nimport { Hash, CHash, Input, toBytes } from './utils.js';\n// HMAC (RFC 2104)\nexport class HMAC<T extends Hash<T>> extends Hash<HMAC<T>> {\n  oHash: T;\n  iHash: T;\n  blockLen: number;\n  outputLen: number;\n  private finished = false;\n  private destroyed = false;\n\n  constructor(hash: CHash, _key: Input) {\n    super();\n    assertHash(hash);\n    const key = toBytes(_key);\n    this.iHash = hash.create() as T;\n    if (typeof this.iHash.update !== 'function')\n      throw new Error('Expected instance of class which extends utils.Hash');\n    this.blockLen = this.iHash.blockLen;\n    this.outputLen = this.iHash.outputLen;\n    const blockLen = this.blockLen;\n    const pad = new Uint8Array(blockLen);\n    // blockLen can be bigger than outputLen\n    pad.set(key.length > blockLen ? hash.create().update(key).digest() : key);\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36;\n    this.iHash.update(pad);\n    // By doing update (processing of first block) of outer hash here we can re-use it between multiple calls via clone\n    this.oHash = hash.create() as T;\n    // Undo internal XOR && apply outer XOR\n    for (let i = 0; i < pad.length; i++) pad[i] ^= 0x36 ^ 0x5c;\n    this.oHash.update(pad);\n    pad.fill(0);\n  }\n  update(buf: Input) {\n    assertExists(this);\n    this.iHash.update(buf);\n    return this;\n  }\n  digestInto(out: Uint8Array) {\n    assertExists(this);\n    assertBytes(out, this.outputLen);\n    this.finished = true;\n    this.iHash.digestInto(out);\n    this.oHash.update(out);\n    this.oHash.digestInto(out);\n    this.destroy();\n  }\n  digest() {\n    const out = new Uint8Array(this.oHash.outputLen);\n    this.digestInto(out);\n    return out;\n  }\n  _cloneInto(to?: HMAC<T>): HMAC<T> {\n    // Create new instance without calling constructor since key already in state and we don't know it.\n    to ||= Object.create(Object.getPrototypeOf(this), {});\n    const { oHash, iHash, finished, destroyed, blockLen, outputLen } = this;\n    to = to as this;\n    to.finished = finished;\n    to.destroyed = destroyed;\n    to.blockLen = blockLen;\n    to.outputLen = outputLen;\n    to.oHash = oHash._cloneInto(to.oHash);\n    to.iHash = iHash._cloneInto(to.iHash);\n    return to;\n  }\n  destroy() {\n    this.destroyed = true;\n    this.oHash.destroy();\n    this.iHash.destroy();\n  }\n}\n\n/**\n * HMAC: RFC2104 message authentication code.\n * @param hash - function that would be used e.g. sha256\n * @param key - message key\n * @param message - message data\n */\nexport const hmac = (hash: CHash, key: Input, message: Input): Uint8Array =>\n  new HMAC<any>(hash, key).update(message).digest();\nhmac.create = (hash: CHash, key: Input) => new HMAC<any>(hash, key);\n"], "mappings": "AAAA,SAASA,IAAI,IAAIC,UAAU,EAAEC,KAAK,IAAIC,WAAW,EAAEC,MAAM,IAAIC,YAAY,QAAQ,cAAc;AAC/F,SAASC,IAAI,EAAgBC,OAAO,QAAQ,YAAY;AACxD;AACA,OAAM,MAAOC,IAAwB,SAAQF,IAAa;EAQxDG,YAAYT,IAAW,EAAEU,IAAW;IAClC,KAAK,EAAE;IAJD,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAC,SAAS,GAAG,KAAK;IAIvBX,UAAU,CAACD,IAAI,CAAC;IAChB,MAAMa,GAAG,GAAGN,OAAO,CAACG,IAAI,CAAC;IACzB,IAAI,CAACI,KAAK,GAAGd,IAAI,CAACe,MAAM,EAAO;IAC/B,IAAI,OAAO,IAAI,CAACD,KAAK,CAACE,MAAM,KAAK,UAAU,EACzC,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;IACxE,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACJ,KAAK,CAACI,QAAQ;IACnC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACL,KAAK,CAACK,SAAS;IACrC,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAME,GAAG,GAAG,IAAIC,UAAU,CAACH,QAAQ,CAAC;IACpC;IACAE,GAAG,CAACE,GAAG,CAACT,GAAG,CAACU,MAAM,GAAGL,QAAQ,GAAGlB,IAAI,CAACe,MAAM,EAAE,CAACC,MAAM,CAACH,GAAG,CAAC,CAACW,MAAM,EAAE,GAAGX,GAAG,CAAC;IACzE,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEL,GAAG,CAACK,CAAC,CAAC,IAAI,IAAI;IACnD,IAAI,CAACX,KAAK,CAACE,MAAM,CAACI,GAAG,CAAC;IACtB;IACA,IAAI,CAACM,KAAK,GAAG1B,IAAI,CAACe,MAAM,EAAO;IAC/B;IACA,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,GAAG,CAACG,MAAM,EAAEE,CAAC,EAAE,EAAEL,GAAG,CAACK,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI;IAC1D,IAAI,CAACC,KAAK,CAACV,MAAM,CAACI,GAAG,CAAC;IACtBA,GAAG,CAACO,IAAI,CAAC,CAAC,CAAC;EACb;EACAX,MAAMA,CAACY,GAAU;IACfvB,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI,CAACS,KAAK,CAACE,MAAM,CAACY,GAAG,CAAC;IACtB,OAAO,IAAI;EACb;EACAC,UAAUA,CAACC,GAAe;IACxBzB,YAAY,CAAC,IAAI,CAAC;IAClBF,WAAW,CAAC2B,GAAG,EAAE,IAAI,CAACX,SAAS,CAAC;IAChC,IAAI,CAACR,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,KAAK,CAACe,UAAU,CAACC,GAAG,CAAC;IAC1B,IAAI,CAACJ,KAAK,CAACV,MAAM,CAACc,GAAG,CAAC;IACtB,IAAI,CAACJ,KAAK,CAACG,UAAU,CAACC,GAAG,CAAC;IAC1B,IAAI,CAACC,OAAO,EAAE;EAChB;EACAP,MAAMA,CAAA;IACJ,MAAMM,GAAG,GAAG,IAAIT,UAAU,CAAC,IAAI,CAACK,KAAK,CAACP,SAAS,CAAC;IAChD,IAAI,CAACU,UAAU,CAACC,GAAG,CAAC;IACpB,OAAOA,GAAG;EACZ;EACAE,UAAUA,CAACC,EAAY;IACrB;IACAA,EAAE,KAAFA,EAAE,GAAKC,MAAM,CAACnB,MAAM,CAACmB,MAAM,CAACC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACrD,MAAM;MAAET,KAAK;MAAEZ,KAAK;MAAEH,QAAQ;MAAEC,SAAS;MAAEM,QAAQ;MAAEC;IAAS,CAAE,GAAG,IAAI;IACvEc,EAAE,GAAGA,EAAU;IACfA,EAAE,CAACtB,QAAQ,GAAGA,QAAQ;IACtBsB,EAAE,CAACrB,SAAS,GAAGA,SAAS;IACxBqB,EAAE,CAACf,QAAQ,GAAGA,QAAQ;IACtBe,EAAE,CAACd,SAAS,GAAGA,SAAS;IACxBc,EAAE,CAACP,KAAK,GAAGA,KAAK,CAACM,UAAU,CAACC,EAAE,CAACP,KAAK,CAAC;IACrCO,EAAE,CAACnB,KAAK,GAAGA,KAAK,CAACkB,UAAU,CAACC,EAAE,CAACnB,KAAK,CAAC;IACrC,OAAOmB,EAAE;EACX;EACAF,OAAOA,CAAA;IACL,IAAI,CAACnB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACc,KAAK,CAACK,OAAO,EAAE;IACpB,IAAI,CAACjB,KAAK,CAACiB,OAAO,EAAE;EACtB;;AAGF;;;;;;AAMA,OAAO,MAAMK,IAAI,GAAGA,CAACpC,IAAW,EAAEa,GAAU,EAAEwB,OAAc,KAC1D,IAAI7B,IAAI,CAAMR,IAAI,EAAEa,GAAG,CAAC,CAACG,MAAM,CAACqB,OAAO,CAAC,CAACb,MAAM,EAAE;AACnDY,IAAI,CAACrB,MAAM,GAAG,CAACf,IAAW,EAAEa,GAAU,KAAK,IAAIL,IAAI,CAAMR,IAAI,EAAEa,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}