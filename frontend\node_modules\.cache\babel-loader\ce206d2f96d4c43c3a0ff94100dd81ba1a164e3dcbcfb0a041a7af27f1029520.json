{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/addresses.js\";\nimport { keccak256, sha256, Signature, SigningKey } from \"../crypto/index.js\";\nimport { concat, decodeRlp, encodeRlp, getBytes, getBigInt, getNumber, hexlify, assert, assertArgument, isBytesLike, isHexString, toBeArray, zeroPadValue } from \"../utils/index.js\";\nimport { accessListify } from \"./accesslist.js\";\nimport { authorizationify } from \"./authorization.js\";\nimport { recoverAddress } from \"./address.js\";\nconst BN_0 = BigInt(0);\nconst BN_2 = BigInt(2);\nconst BN_27 = BigInt(27);\nconst BN_28 = BigInt(28);\nconst BN_35 = BigInt(35);\nconst BN_MAX_UINT = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\nconst BLOB_SIZE = 4096 * 32;\nfunction getKzgLibrary(kzg) {\n  const blobToKzgCommitment = blob => {\n    if (\"computeBlobProof\" in kzg) {\n      // micro-ecc-signer; check for computeBlobProof since this API\n      // expects a string while the kzg-wasm below expects a Unit8Array\n      if (\"blobToKzgCommitment\" in kzg && typeof kzg.blobToKzgCommitment === \"function\") {\n        return getBytes(kzg.blobToKzgCommitment(hexlify(blob)));\n      }\n    } else if (\"blobToKzgCommitment\" in kzg && typeof kzg.blobToKzgCommitment === \"function\") {\n      // kzg-wasm <0.5.0; blobToKzgCommitment(Uint8Array) => Uint8Array\n      return getBytes(kzg.blobToKzgCommitment(blob));\n    }\n    // kzg-wasm >= 0.5.0; blobToKZGCommitment(string) => string\n    if (\"blobToKZGCommitment\" in kzg && typeof kzg.blobToKZGCommitment === \"function\") {\n      return getBytes(kzg.blobToKZGCommitment(hexlify(blob)));\n    }\n    assertArgument(false, \"unsupported KZG library\", \"kzg\", kzg);\n  };\n  const computeBlobKzgProof = (blob, commitment) => {\n    // micro-ecc-signer\n    if (\"computeBlobProof\" in kzg && typeof kzg.computeBlobProof === \"function\") {\n      return getBytes(kzg.computeBlobProof(hexlify(blob), hexlify(commitment)));\n    }\n    // kzg-wasm <0.5.0; computeBlobKzgProof(Uint8Array, Uint8Array) => Uint8Array\n    if (\"computeBlobKzgProof\" in kzg && typeof kzg.computeBlobKzgProof === \"function\") {\n      return kzg.computeBlobKzgProof(blob, commitment);\n    }\n    // kzg-wasm >= 0.5.0; computeBlobKZGProof(string, string) => string\n    if (\"computeBlobKZGProof\" in kzg && typeof kzg.computeBlobKZGProof === \"function\") {\n      return getBytes(kzg.computeBlobKZGProof(hexlify(blob), hexlify(commitment)));\n    }\n    assertArgument(false, \"unsupported KZG library\", \"kzg\", kzg);\n  };\n  return {\n    blobToKzgCommitment,\n    computeBlobKzgProof\n  };\n}\nfunction getVersionedHash(version, hash) {\n  let versioned = version.toString(16);\n  while (versioned.length < 2) {\n    versioned = \"0\" + versioned;\n  }\n  versioned += sha256(hash).substring(4);\n  return \"0x\" + versioned;\n}\nfunction handleAddress(value) {\n  if (value === \"0x\") {\n    return null;\n  }\n  return getAddress(value);\n}\nfunction handleAccessList(value, param) {\n  try {\n    return accessListify(value);\n  } catch (error) {\n    assertArgument(false, error.message, param, value);\n  }\n}\nfunction handleAuthorizationList(value, param) {\n  try {\n    if (!Array.isArray(value)) {\n      throw new Error(\"authorizationList: invalid array\");\n    }\n    const result = [];\n    for (let i = 0; i < value.length; i++) {\n      const auth = value[i];\n      if (!Array.isArray(auth)) {\n        throw new Error(`authorization[${i}]: invalid array`);\n      }\n      if (auth.length !== 6) {\n        throw new Error(`authorization[${i}]: wrong length`);\n      }\n      if (!auth[1]) {\n        throw new Error(`authorization[${i}]: null address`);\n      }\n      result.push({\n        address: handleAddress(auth[1]),\n        nonce: handleUint(auth[2], \"nonce\"),\n        chainId: handleUint(auth[0], \"chainId\"),\n        signature: Signature.from({\n          yParity: handleNumber(auth[3], \"yParity\"),\n          r: zeroPadValue(auth[4], 32),\n          s: zeroPadValue(auth[5], 32)\n        })\n      });\n    }\n    return result;\n  } catch (error) {\n    assertArgument(false, error.message, param, value);\n  }\n}\nfunction handleNumber(_value, param) {\n  if (_value === \"0x\") {\n    return 0;\n  }\n  return getNumber(_value, param);\n}\nfunction handleUint(_value, param) {\n  if (_value === \"0x\") {\n    return BN_0;\n  }\n  const value = getBigInt(_value, param);\n  assertArgument(value <= BN_MAX_UINT, \"value exceeds uint size\", param, value);\n  return value;\n}\nfunction formatNumber(_value, name) {\n  const value = getBigInt(_value, \"value\");\n  const result = toBeArray(value);\n  assertArgument(result.length <= 32, `value too large`, `tx.${name}`, value);\n  return result;\n}\nfunction formatAccessList(value) {\n  return accessListify(value).map(set => [set.address, set.storageKeys]);\n}\nfunction formatAuthorizationList(value) {\n  return value.map(a => {\n    return [formatNumber(a.chainId, \"chainId\"), a.address, formatNumber(a.nonce, \"nonce\"), formatNumber(a.signature.yParity, \"yParity\"), a.signature.r, a.signature.s];\n  });\n}\nfunction formatHashes(value, param) {\n  assertArgument(Array.isArray(value), `invalid ${param}`, \"value\", value);\n  for (let i = 0; i < value.length; i++) {\n    assertArgument(isHexString(value[i], 32), \"invalid ${ param } hash\", `value[${i}]`, value[i]);\n  }\n  return value;\n}\nfunction _parseLegacy(data) {\n  const fields = decodeRlp(data);\n  assertArgument(Array.isArray(fields) && (fields.length === 9 || fields.length === 6), \"invalid field count for legacy transaction\", \"data\", data);\n  const tx = {\n    type: 0,\n    nonce: handleNumber(fields[0], \"nonce\"),\n    gasPrice: handleUint(fields[1], \"gasPrice\"),\n    gasLimit: handleUint(fields[2], \"gasLimit\"),\n    to: handleAddress(fields[3]),\n    value: handleUint(fields[4], \"value\"),\n    data: hexlify(fields[5]),\n    chainId: BN_0\n  };\n  // Legacy unsigned transaction\n  if (fields.length === 6) {\n    return tx;\n  }\n  const v = handleUint(fields[6], \"v\");\n  const r = handleUint(fields[7], \"r\");\n  const s = handleUint(fields[8], \"s\");\n  if (r === BN_0 && s === BN_0) {\n    // EIP-155 unsigned transaction\n    tx.chainId = v;\n  } else {\n    // Compute the EIP-155 chain ID (or 0 for legacy)\n    let chainId = (v - BN_35) / BN_2;\n    if (chainId < BN_0) {\n      chainId = BN_0;\n    }\n    tx.chainId = chainId;\n    // Signed Legacy Transaction\n    assertArgument(chainId !== BN_0 || v === BN_27 || v === BN_28, \"non-canonical legacy v\", \"v\", fields[6]);\n    tx.signature = Signature.from({\n      r: zeroPadValue(fields[7], 32),\n      s: zeroPadValue(fields[8], 32),\n      v\n    });\n    //tx.hash = keccak256(data);\n  }\n  return tx;\n}\nfunction _serializeLegacy(tx, sig) {\n  const fields = [formatNumber(tx.nonce, \"nonce\"), formatNumber(tx.gasPrice || 0, \"gasPrice\"), formatNumber(tx.gasLimit, \"gasLimit\"), tx.to || \"0x\", formatNumber(tx.value, \"value\"), tx.data];\n  let chainId = BN_0;\n  if (tx.chainId != BN_0) {\n    // A chainId was provided; if non-zero we'll use EIP-155\n    chainId = getBigInt(tx.chainId, \"tx.chainId\");\n    // We have a chainId in the tx and an EIP-155 v in the signature,\n    // make sure they agree with each other\n    assertArgument(!sig || sig.networkV == null || sig.legacyChainId === chainId, \"tx.chainId/sig.v mismatch\", \"sig\", sig);\n  } else if (tx.signature) {\n    // No explicit chainId, but EIP-155 have a derived implicit chainId\n    const legacy = tx.signature.legacyChainId;\n    if (legacy != null) {\n      chainId = legacy;\n    }\n  }\n  // Requesting an unsigned transaction\n  if (!sig) {\n    // We have an EIP-155 transaction (chainId was specified and non-zero)\n    if (chainId !== BN_0) {\n      fields.push(toBeArray(chainId));\n      fields.push(\"0x\");\n      fields.push(\"0x\");\n    }\n    return encodeRlp(fields);\n  }\n  // @TODO: We should probably check that tx.signature, chainId, and sig\n  //        match but that logic could break existing code, so schedule\n  //        this for the next major bump.\n  // Compute the EIP-155 v\n  let v = BigInt(27 + sig.yParity);\n  if (chainId !== BN_0) {\n    v = Signature.getChainIdV(chainId, sig.v);\n  } else if (BigInt(sig.v) !== v) {\n    assertArgument(false, \"tx.chainId/sig.v mismatch\", \"sig\", sig);\n  }\n  // Add the signature\n  fields.push(toBeArray(v));\n  fields.push(toBeArray(sig.r));\n  fields.push(toBeArray(sig.s));\n  return encodeRlp(fields);\n}\nfunction _parseEipSignature(tx, fields) {\n  let yParity;\n  try {\n    yParity = handleNumber(fields[0], \"yParity\");\n    if (yParity !== 0 && yParity !== 1) {\n      throw new Error(\"bad yParity\");\n    }\n  } catch (error) {\n    assertArgument(false, \"invalid yParity\", \"yParity\", fields[0]);\n  }\n  const r = zeroPadValue(fields[1], 32);\n  const s = zeroPadValue(fields[2], 32);\n  const signature = Signature.from({\n    r,\n    s,\n    yParity\n  });\n  tx.signature = signature;\n}\nfunction _parseEip1559(data) {\n  const fields = decodeRlp(getBytes(data).slice(1));\n  assertArgument(Array.isArray(fields) && (fields.length === 9 || fields.length === 12), \"invalid field count for transaction type: 2\", \"data\", hexlify(data));\n  const tx = {\n    type: 2,\n    chainId: handleUint(fields[0], \"chainId\"),\n    nonce: handleNumber(fields[1], \"nonce\"),\n    maxPriorityFeePerGas: handleUint(fields[2], \"maxPriorityFeePerGas\"),\n    maxFeePerGas: handleUint(fields[3], \"maxFeePerGas\"),\n    gasPrice: null,\n    gasLimit: handleUint(fields[4], \"gasLimit\"),\n    to: handleAddress(fields[5]),\n    value: handleUint(fields[6], \"value\"),\n    data: hexlify(fields[7]),\n    accessList: handleAccessList(fields[8], \"accessList\")\n  };\n  // Unsigned EIP-1559 Transaction\n  if (fields.length === 9) {\n    return tx;\n  }\n  //tx.hash = keccak256(data);\n  _parseEipSignature(tx, fields.slice(9));\n  return tx;\n}\nfunction _serializeEip1559(tx, sig) {\n  const fields = [formatNumber(tx.chainId, \"chainId\"), formatNumber(tx.nonce, \"nonce\"), formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"), formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"), formatNumber(tx.gasLimit, \"gasLimit\"), tx.to || \"0x\", formatNumber(tx.value, \"value\"), tx.data, formatAccessList(tx.accessList || [])];\n  if (sig) {\n    fields.push(formatNumber(sig.yParity, \"yParity\"));\n    fields.push(toBeArray(sig.r));\n    fields.push(toBeArray(sig.s));\n  }\n  return concat([\"0x02\", encodeRlp(fields)]);\n}\nfunction _parseEip2930(data) {\n  const fields = decodeRlp(getBytes(data).slice(1));\n  assertArgument(Array.isArray(fields) && (fields.length === 8 || fields.length === 11), \"invalid field count for transaction type: 1\", \"data\", hexlify(data));\n  const tx = {\n    type: 1,\n    chainId: handleUint(fields[0], \"chainId\"),\n    nonce: handleNumber(fields[1], \"nonce\"),\n    gasPrice: handleUint(fields[2], \"gasPrice\"),\n    gasLimit: handleUint(fields[3], \"gasLimit\"),\n    to: handleAddress(fields[4]),\n    value: handleUint(fields[5], \"value\"),\n    data: hexlify(fields[6]),\n    accessList: handleAccessList(fields[7], \"accessList\")\n  };\n  // Unsigned EIP-2930 Transaction\n  if (fields.length === 8) {\n    return tx;\n  }\n  //tx.hash = keccak256(data);\n  _parseEipSignature(tx, fields.slice(8));\n  return tx;\n}\nfunction _serializeEip2930(tx, sig) {\n  const fields = [formatNumber(tx.chainId, \"chainId\"), formatNumber(tx.nonce, \"nonce\"), formatNumber(tx.gasPrice || 0, \"gasPrice\"), formatNumber(tx.gasLimit, \"gasLimit\"), tx.to || \"0x\", formatNumber(tx.value, \"value\"), tx.data, formatAccessList(tx.accessList || [])];\n  if (sig) {\n    fields.push(formatNumber(sig.yParity, \"recoveryParam\"));\n    fields.push(toBeArray(sig.r));\n    fields.push(toBeArray(sig.s));\n  }\n  return concat([\"0x01\", encodeRlp(fields)]);\n}\nfunction _parseEip4844(data) {\n  let fields = decodeRlp(getBytes(data).slice(1));\n  let typeName = \"3\";\n  let blobs = null;\n  // Parse the network format\n  if (fields.length === 4 && Array.isArray(fields[0])) {\n    typeName = \"3 (network format)\";\n    const fBlobs = fields[1],\n      fCommits = fields[2],\n      fProofs = fields[3];\n    assertArgument(Array.isArray(fBlobs), \"invalid network format: blobs not an array\", \"fields[1]\", fBlobs);\n    assertArgument(Array.isArray(fCommits), \"invalid network format: commitments not an array\", \"fields[2]\", fCommits);\n    assertArgument(Array.isArray(fProofs), \"invalid network format: proofs not an array\", \"fields[3]\", fProofs);\n    assertArgument(fBlobs.length === fCommits.length, \"invalid network format: blobs/commitments length mismatch\", \"fields\", fields);\n    assertArgument(fBlobs.length === fProofs.length, \"invalid network format: blobs/proofs length mismatch\", \"fields\", fields);\n    blobs = [];\n    for (let i = 0; i < fields[1].length; i++) {\n      blobs.push({\n        data: fBlobs[i],\n        commitment: fCommits[i],\n        proof: fProofs[i]\n      });\n    }\n    fields = fields[0];\n  }\n  assertArgument(Array.isArray(fields) && (fields.length === 11 || fields.length === 14), `invalid field count for transaction type: ${typeName}`, \"data\", hexlify(data));\n  const tx = {\n    type: 3,\n    chainId: handleUint(fields[0], \"chainId\"),\n    nonce: handleNumber(fields[1], \"nonce\"),\n    maxPriorityFeePerGas: handleUint(fields[2], \"maxPriorityFeePerGas\"),\n    maxFeePerGas: handleUint(fields[3], \"maxFeePerGas\"),\n    gasPrice: null,\n    gasLimit: handleUint(fields[4], \"gasLimit\"),\n    to: handleAddress(fields[5]),\n    value: handleUint(fields[6], \"value\"),\n    data: hexlify(fields[7]),\n    accessList: handleAccessList(fields[8], \"accessList\"),\n    maxFeePerBlobGas: handleUint(fields[9], \"maxFeePerBlobGas\"),\n    blobVersionedHashes: fields[10]\n  };\n  if (blobs) {\n    tx.blobs = blobs;\n  }\n  assertArgument(tx.to != null, `invalid address for transaction type: ${typeName}`, \"data\", data);\n  assertArgument(Array.isArray(tx.blobVersionedHashes), \"invalid blobVersionedHashes: must be an array\", \"data\", data);\n  for (let i = 0; i < tx.blobVersionedHashes.length; i++) {\n    assertArgument(isHexString(tx.blobVersionedHashes[i], 32), `invalid blobVersionedHash at index ${i}: must be length 32`, \"data\", data);\n  }\n  // Unsigned EIP-4844 Transaction\n  if (fields.length === 11) {\n    return tx;\n  }\n  // @TODO: Do we need to do this? This is only called internally\n  // and used to verify hashes; it might save time to not do this\n  //tx.hash = keccak256(concat([ \"0x03\", encodeRlp(fields) ]));\n  _parseEipSignature(tx, fields.slice(11));\n  return tx;\n}\nfunction _serializeEip4844(tx, sig, blobs) {\n  const fields = [formatNumber(tx.chainId, \"chainId\"), formatNumber(tx.nonce, \"nonce\"), formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"), formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"), formatNumber(tx.gasLimit, \"gasLimit\"), tx.to || ZeroAddress, formatNumber(tx.value, \"value\"), tx.data, formatAccessList(tx.accessList || []), formatNumber(tx.maxFeePerBlobGas || 0, \"maxFeePerBlobGas\"), formatHashes(tx.blobVersionedHashes || [], \"blobVersionedHashes\")];\n  if (sig) {\n    fields.push(formatNumber(sig.yParity, \"yParity\"));\n    fields.push(toBeArray(sig.r));\n    fields.push(toBeArray(sig.s));\n    // We have blobs; return the network wrapped format\n    if (blobs) {\n      return concat([\"0x03\", encodeRlp([fields, blobs.map(b => b.data), blobs.map(b => b.commitment), blobs.map(b => b.proof)])]);\n    }\n  }\n  return concat([\"0x03\", encodeRlp(fields)]);\n}\nfunction _parseEip7702(data) {\n  const fields = decodeRlp(getBytes(data).slice(1));\n  assertArgument(Array.isArray(fields) && (fields.length === 10 || fields.length === 13), \"invalid field count for transaction type: 4\", \"data\", hexlify(data));\n  const tx = {\n    type: 4,\n    chainId: handleUint(fields[0], \"chainId\"),\n    nonce: handleNumber(fields[1], \"nonce\"),\n    maxPriorityFeePerGas: handleUint(fields[2], \"maxPriorityFeePerGas\"),\n    maxFeePerGas: handleUint(fields[3], \"maxFeePerGas\"),\n    gasPrice: null,\n    gasLimit: handleUint(fields[4], \"gasLimit\"),\n    to: handleAddress(fields[5]),\n    value: handleUint(fields[6], \"value\"),\n    data: hexlify(fields[7]),\n    accessList: handleAccessList(fields[8], \"accessList\"),\n    authorizationList: handleAuthorizationList(fields[9], \"authorizationList\")\n  };\n  // Unsigned EIP-7702 Transaction\n  if (fields.length === 10) {\n    return tx;\n  }\n  _parseEipSignature(tx, fields.slice(10));\n  return tx;\n}\nfunction _serializeEip7702(tx, sig) {\n  const fields = [formatNumber(tx.chainId, \"chainId\"), formatNumber(tx.nonce, \"nonce\"), formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"), formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"), formatNumber(tx.gasLimit, \"gasLimit\"), tx.to || \"0x\", formatNumber(tx.value, \"value\"), tx.data, formatAccessList(tx.accessList || []), formatAuthorizationList(tx.authorizationList || [])];\n  if (sig) {\n    fields.push(formatNumber(sig.yParity, \"yParity\"));\n    fields.push(toBeArray(sig.r));\n    fields.push(toBeArray(sig.s));\n  }\n  return concat([\"0x04\", encodeRlp(fields)]);\n}\n/**\n *  A **Transaction** describes an operation to be executed on\n *  Ethereum by an Externally Owned Account (EOA). It includes\n *  who (the [[to]] address), what (the [[data]]) and how much (the\n *  [[value]] in ether) the operation should entail.\n *\n *  @example:\n *    tx = new Transaction()\n *    //_result:\n *\n *    tx.data = \"0x1234\";\n *    //_result:\n */\nexport class Transaction {\n  #type;\n  #to;\n  #data;\n  #nonce;\n  #gasLimit;\n  #gasPrice;\n  #maxPriorityFeePerGas;\n  #maxFeePerGas;\n  #value;\n  #chainId;\n  #sig;\n  #accessList;\n  #maxFeePerBlobGas;\n  #blobVersionedHashes;\n  #kzg;\n  #blobs;\n  #auths;\n  /**\n   *  The transaction type.\n   *\n   *  If null, the type will be automatically inferred based on\n   *  explicit properties.\n   */\n  get type() {\n    return this.#type;\n  }\n  set type(value) {\n    switch (value) {\n      case null:\n        this.#type = null;\n        break;\n      case 0:\n      case \"legacy\":\n        this.#type = 0;\n        break;\n      case 1:\n      case \"berlin\":\n      case \"eip-2930\":\n        this.#type = 1;\n        break;\n      case 2:\n      case \"london\":\n      case \"eip-1559\":\n        this.#type = 2;\n        break;\n      case 3:\n      case \"cancun\":\n      case \"eip-4844\":\n        this.#type = 3;\n        break;\n      case 4:\n      case \"pectra\":\n      case \"eip-7702\":\n        this.#type = 4;\n        break;\n      default:\n        assertArgument(false, \"unsupported transaction type\", \"type\", value);\n    }\n  }\n  /**\n   *  The name of the transaction type.\n   */\n  get typeName() {\n    switch (this.type) {\n      case 0:\n        return \"legacy\";\n      case 1:\n        return \"eip-2930\";\n      case 2:\n        return \"eip-1559\";\n      case 3:\n        return \"eip-4844\";\n      case 4:\n        return \"eip-7702\";\n    }\n    return null;\n  }\n  /**\n   *  The ``to`` address for the transaction or ``null`` if the\n   *  transaction is an ``init`` transaction.\n   */\n  get to() {\n    const value = this.#to;\n    if (value == null && this.type === 3) {\n      return ZeroAddress;\n    }\n    return value;\n  }\n  set to(value) {\n    this.#to = value == null ? null : getAddress(value);\n  }\n  /**\n   *  The transaction nonce.\n   */\n  get nonce() {\n    return this.#nonce;\n  }\n  set nonce(value) {\n    this.#nonce = getNumber(value, \"value\");\n  }\n  /**\n   *  The gas limit.\n   */\n  get gasLimit() {\n    return this.#gasLimit;\n  }\n  set gasLimit(value) {\n    this.#gasLimit = getBigInt(value);\n  }\n  /**\n   *  The gas price.\n   *\n   *  On legacy networks this defines the fee that will be paid. On\n   *  EIP-1559 networks, this should be ``null``.\n   */\n  get gasPrice() {\n    const value = this.#gasPrice;\n    if (value == null && (this.type === 0 || this.type === 1)) {\n      return BN_0;\n    }\n    return value;\n  }\n  set gasPrice(value) {\n    this.#gasPrice = value == null ? null : getBigInt(value, \"gasPrice\");\n  }\n  /**\n   *  The maximum priority fee per unit of gas to pay. On legacy\n   *  networks this should be ``null``.\n   */\n  get maxPriorityFeePerGas() {\n    const value = this.#maxPriorityFeePerGas;\n    if (value == null) {\n      if (this.type === 2 || this.type === 3) {\n        return BN_0;\n      }\n      return null;\n    }\n    return value;\n  }\n  set maxPriorityFeePerGas(value) {\n    this.#maxPriorityFeePerGas = value == null ? null : getBigInt(value, \"maxPriorityFeePerGas\");\n  }\n  /**\n   *  The maximum total fee per unit of gas to pay. On legacy\n   *  networks this should be ``null``.\n   */\n  get maxFeePerGas() {\n    const value = this.#maxFeePerGas;\n    if (value == null) {\n      if (this.type === 2 || this.type === 3) {\n        return BN_0;\n      }\n      return null;\n    }\n    return value;\n  }\n  set maxFeePerGas(value) {\n    this.#maxFeePerGas = value == null ? null : getBigInt(value, \"maxFeePerGas\");\n  }\n  /**\n   *  The transaction data. For ``init`` transactions this is the\n   *  deployment code.\n   */\n  get data() {\n    return this.#data;\n  }\n  set data(value) {\n    this.#data = hexlify(value);\n  }\n  /**\n   *  The amount of ether (in wei) to send in this transactions.\n   */\n  get value() {\n    return this.#value;\n  }\n  set value(value) {\n    this.#value = getBigInt(value, \"value\");\n  }\n  /**\n   *  The chain ID this transaction is valid on.\n   */\n  get chainId() {\n    return this.#chainId;\n  }\n  set chainId(value) {\n    this.#chainId = getBigInt(value);\n  }\n  /**\n   *  If signed, the signature for this transaction.\n   */\n  get signature() {\n    return this.#sig || null;\n  }\n  set signature(value) {\n    this.#sig = value == null ? null : Signature.from(value);\n  }\n  /**\n   *  The access list.\n   *\n   *  An access list permits discounted (but pre-paid) access to\n   *  bytecode and state variable access within contract execution.\n   */\n  get accessList() {\n    const value = this.#accessList || null;\n    if (value == null) {\n      if (this.type === 1 || this.type === 2 || this.type === 3) {\n        // @TODO: in v7, this should assign the value or become\n        // a live object itself, otherwise mutation is inconsistent\n        return [];\n      }\n      return null;\n    }\n    return value;\n  }\n  set accessList(value) {\n    this.#accessList = value == null ? null : accessListify(value);\n  }\n  get authorizationList() {\n    const value = this.#auths || null;\n    if (value == null) {\n      if (this.type === 4) {\n        // @TODO: in v7, this should become a live object itself,\n        // otherwise mutation is inconsistent\n        return [];\n      }\n    }\n    return value;\n  }\n  set authorizationList(auths) {\n    this.#auths = auths == null ? null : auths.map(a => authorizationify(a));\n  }\n  /**\n   *  The max fee per blob gas for Cancun transactions.\n   */\n  get maxFeePerBlobGas() {\n    const value = this.#maxFeePerBlobGas;\n    if (value == null && this.type === 3) {\n      return BN_0;\n    }\n    return value;\n  }\n  set maxFeePerBlobGas(value) {\n    this.#maxFeePerBlobGas = value == null ? null : getBigInt(value, \"maxFeePerBlobGas\");\n  }\n  /**\n   *  The BLOb versioned hashes for Cancun transactions.\n   */\n  get blobVersionedHashes() {\n    // @TODO: Mutation is inconsistent; if unset, the returned value\n    // cannot mutate the object, if set it can\n    let value = this.#blobVersionedHashes;\n    if (value == null && this.type === 3) {\n      return [];\n    }\n    return value;\n  }\n  set blobVersionedHashes(value) {\n    if (value != null) {\n      assertArgument(Array.isArray(value), \"blobVersionedHashes must be an Array\", \"value\", value);\n      value = value.slice();\n      for (let i = 0; i < value.length; i++) {\n        assertArgument(isHexString(value[i], 32), \"invalid blobVersionedHash\", `value[${i}]`, value[i]);\n      }\n    }\n    this.#blobVersionedHashes = value;\n  }\n  /**\n   *  The BLObs for the Transaction, if any.\n   *\n   *  If ``blobs`` is non-``null``, then the [[seriailized]]\n   *  will return the network formatted sidecar, otherwise it\n   *  will return the standard [[link-eip-2718]] payload. The\n   *  [[unsignedSerialized]] is unaffected regardless.\n   *\n   *  When setting ``blobs``, either fully valid [[Blob]] objects\n   *  may be specified (i.e. correctly padded, with correct\n   *  committments and proofs) or a raw [[BytesLike]] may\n   *  be provided.\n   *\n   *  If raw [[BytesLike]] are provided, the [[kzg]] property **must**\n   *  be already set. The blob will be correctly padded and the\n   *  [[KzgLibrary]] will be used to compute the committment and\n   *  proof for the blob.\n   *\n   *  A BLOb is a sequence of field elements, each of which must\n   *  be within the BLS field modulo, so some additional processing\n   *  may be required to encode arbitrary data to ensure each 32 byte\n   *  field is within the valid range.\n   *\n   *  Setting this automatically populates [[blobVersionedHashes]],\n   *  overwriting any existing values. Setting this to ``null``\n   *  does **not** remove the [[blobVersionedHashes]], leaving them\n   *  present.\n   */\n  get blobs() {\n    if (this.#blobs == null) {\n      return null;\n    }\n    return this.#blobs.map(b => Object.assign({}, b));\n  }\n  set blobs(_blobs) {\n    if (_blobs == null) {\n      this.#blobs = null;\n      return;\n    }\n    const blobs = [];\n    const versionedHashes = [];\n    for (let i = 0; i < _blobs.length; i++) {\n      const blob = _blobs[i];\n      if (isBytesLike(blob)) {\n        assert(this.#kzg, \"adding a raw blob requires a KZG library\", \"UNSUPPORTED_OPERATION\", {\n          operation: \"set blobs()\"\n        });\n        let data = getBytes(blob);\n        assertArgument(data.length <= BLOB_SIZE, \"blob is too large\", `blobs[${i}]`, blob);\n        // Pad blob if necessary\n        if (data.length !== BLOB_SIZE) {\n          const padded = new Uint8Array(BLOB_SIZE);\n          padded.set(data);\n          data = padded;\n        }\n        const commit = this.#kzg.blobToKzgCommitment(data);\n        const proof = hexlify(this.#kzg.computeBlobKzgProof(data, commit));\n        blobs.push({\n          data: hexlify(data),\n          commitment: hexlify(commit),\n          proof\n        });\n        versionedHashes.push(getVersionedHash(1, commit));\n      } else {\n        const commit = hexlify(blob.commitment);\n        blobs.push({\n          data: hexlify(blob.data),\n          commitment: commit,\n          proof: hexlify(blob.proof)\n        });\n        versionedHashes.push(getVersionedHash(1, commit));\n      }\n    }\n    this.#blobs = blobs;\n    this.#blobVersionedHashes = versionedHashes;\n  }\n  get kzg() {\n    return this.#kzg;\n  }\n  set kzg(kzg) {\n    if (kzg == null) {\n      this.#kzg = null;\n    } else {\n      this.#kzg = getKzgLibrary(kzg);\n    }\n  }\n  /**\n   *  Creates a new Transaction with default values.\n   */\n  constructor() {\n    this.#type = null;\n    this.#to = null;\n    this.#nonce = 0;\n    this.#gasLimit = BN_0;\n    this.#gasPrice = null;\n    this.#maxPriorityFeePerGas = null;\n    this.#maxFeePerGas = null;\n    this.#data = \"0x\";\n    this.#value = BN_0;\n    this.#chainId = BN_0;\n    this.#sig = null;\n    this.#accessList = null;\n    this.#maxFeePerBlobGas = null;\n    this.#blobVersionedHashes = null;\n    this.#kzg = null;\n    this.#blobs = null;\n    this.#auths = null;\n  }\n  /**\n   *  The transaction hash, if signed. Otherwise, ``null``.\n   */\n  get hash() {\n    if (this.signature == null) {\n      return null;\n    }\n    return keccak256(this.#getSerialized(true, false));\n  }\n  /**\n   *  The pre-image hash of this transaction.\n   *\n   *  This is the digest that a [[Signer]] must sign to authorize\n   *  this transaction.\n   */\n  get unsignedHash() {\n    return keccak256(this.unsignedSerialized);\n  }\n  /**\n   *  The sending address, if signed. Otherwise, ``null``.\n   */\n  get from() {\n    if (this.signature == null) {\n      return null;\n    }\n    return recoverAddress(this.unsignedHash, this.signature);\n  }\n  /**\n   *  The public key of the sender, if signed. Otherwise, ``null``.\n   */\n  get fromPublicKey() {\n    if (this.signature == null) {\n      return null;\n    }\n    return SigningKey.recoverPublicKey(this.unsignedHash, this.signature);\n  }\n  /**\n   *  Returns true if signed.\n   *\n   *  This provides a Type Guard that properties requiring a signed\n   *  transaction are non-null.\n   */\n  isSigned() {\n    return this.signature != null;\n  }\n  #getSerialized(signed, sidecar) {\n    assert(!signed || this.signature != null, \"cannot serialize unsigned transaction; maybe you meant .unsignedSerialized\", \"UNSUPPORTED_OPERATION\", {\n      operation: \".serialized\"\n    });\n    const sig = signed ? this.signature : null;\n    switch (this.inferType()) {\n      case 0:\n        return _serializeLegacy(this, sig);\n      case 1:\n        return _serializeEip2930(this, sig);\n      case 2:\n        return _serializeEip1559(this, sig);\n      case 3:\n        return _serializeEip4844(this, sig, sidecar ? this.blobs : null);\n      case 4:\n        return _serializeEip7702(this, sig);\n    }\n    assert(false, \"unsupported transaction type\", \"UNSUPPORTED_OPERATION\", {\n      operation: \".serialized\"\n    });\n  }\n  /**\n   *  The serialized transaction.\n   *\n   *  This throws if the transaction is unsigned. For the pre-image,\n   *  use [[unsignedSerialized]].\n   */\n  get serialized() {\n    return this.#getSerialized(true, true);\n  }\n  /**\n   *  The transaction pre-image.\n   *\n   *  The hash of this is the digest which needs to be signed to\n   *  authorize this transaction.\n   */\n  get unsignedSerialized() {\n    return this.#getSerialized(false, false);\n  }\n  /**\n   *  Return the most \"likely\" type; currently the highest\n   *  supported transaction type.\n   */\n  inferType() {\n    const types = this.inferTypes();\n    // Prefer London (EIP-1559) over Cancun (BLOb)\n    if (types.indexOf(2) >= 0) {\n      return 2;\n    }\n    // Return the highest inferred type\n    return types.pop();\n  }\n  /**\n   *  Validates the explicit properties and returns a list of compatible\n   *  transaction types.\n   */\n  inferTypes() {\n    // Checks that there are no conflicting properties set\n    const hasGasPrice = this.gasPrice != null;\n    const hasFee = this.maxFeePerGas != null || this.maxPriorityFeePerGas != null;\n    const hasAccessList = this.accessList != null;\n    const hasBlob = this.#maxFeePerBlobGas != null || this.#blobVersionedHashes;\n    //if (hasGasPrice && hasFee) {\n    //    throw new Error(\"transaction cannot have gasPrice and maxFeePerGas\");\n    //}\n    if (this.maxFeePerGas != null && this.maxPriorityFeePerGas != null) {\n      assert(this.maxFeePerGas >= this.maxPriorityFeePerGas, \"priorityFee cannot be more than maxFee\", \"BAD_DATA\", {\n        value: this\n      });\n    }\n    //if (this.type === 2 && hasGasPrice) {\n    //    throw new Error(\"eip-1559 transaction cannot have gasPrice\");\n    //}\n    assert(!hasFee || this.type !== 0 && this.type !== 1, \"transaction type cannot have maxFeePerGas or maxPriorityFeePerGas\", \"BAD_DATA\", {\n      value: this\n    });\n    assert(this.type !== 0 || !hasAccessList, \"legacy transaction cannot have accessList\", \"BAD_DATA\", {\n      value: this\n    });\n    const types = [];\n    // Explicit type\n    if (this.type != null) {\n      types.push(this.type);\n    } else {\n      if (this.authorizationList && this.authorizationList.length) {\n        types.push(4);\n      } else if (hasFee) {\n        types.push(2);\n      } else if (hasGasPrice) {\n        types.push(1);\n        if (!hasAccessList) {\n          types.push(0);\n        }\n      } else if (hasAccessList) {\n        types.push(1);\n        types.push(2);\n      } else if (hasBlob && this.to) {\n        types.push(3);\n      } else {\n        types.push(0);\n        types.push(1);\n        types.push(2);\n        types.push(3);\n      }\n    }\n    types.sort();\n    return types;\n  }\n  /**\n   *  Returns true if this transaction is a legacy transaction (i.e.\n   *  ``type === 0``).\n   *\n   *  This provides a Type Guard that the related properties are\n   *  non-null.\n   */\n  isLegacy() {\n    return this.type === 0;\n  }\n  /**\n   *  Returns true if this transaction is berlin hardform transaction (i.e.\n   *  ``type === 1``).\n   *\n   *  This provides a Type Guard that the related properties are\n   *  non-null.\n   */\n  isBerlin() {\n    return this.type === 1;\n  }\n  /**\n   *  Returns true if this transaction is london hardform transaction (i.e.\n   *  ``type === 2``).\n   *\n   *  This provides a Type Guard that the related properties are\n   *  non-null.\n   */\n  isLondon() {\n    return this.type === 2;\n  }\n  /**\n   *  Returns true if this transaction is an [[link-eip-4844]] BLOB\n   *  transaction.\n   *\n   *  This provides a Type Guard that the related properties are\n   *  non-null.\n   */\n  isCancun() {\n    return this.type === 3;\n  }\n  /**\n   *  Create a copy of this transaciton.\n   */\n  clone() {\n    return Transaction.from(this);\n  }\n  /**\n   *  Return a JSON-friendly object.\n   */\n  toJSON() {\n    const s = v => {\n      if (v == null) {\n        return null;\n      }\n      return v.toString();\n    };\n    return {\n      type: this.type,\n      to: this.to,\n      //            from: this.from,\n      data: this.data,\n      nonce: this.nonce,\n      gasLimit: s(this.gasLimit),\n      gasPrice: s(this.gasPrice),\n      maxPriorityFeePerGas: s(this.maxPriorityFeePerGas),\n      maxFeePerGas: s(this.maxFeePerGas),\n      value: s(this.value),\n      chainId: s(this.chainId),\n      sig: this.signature ? this.signature.toJSON() : null,\n      accessList: this.accessList\n    };\n  }\n  /**\n   *  Create a **Transaction** from a serialized transaction or a\n   *  Transaction-like object.\n   */\n  static from(tx) {\n    if (tx == null) {\n      return new Transaction();\n    }\n    if (typeof tx === \"string\") {\n      const payload = getBytes(tx);\n      if (payload[0] >= 0x7f) {\n        // @TODO: > vs >= ??\n        return Transaction.from(_parseLegacy(payload));\n      }\n      switch (payload[0]) {\n        case 1:\n          return Transaction.from(_parseEip2930(payload));\n        case 2:\n          return Transaction.from(_parseEip1559(payload));\n        case 3:\n          return Transaction.from(_parseEip4844(payload));\n        case 4:\n          return Transaction.from(_parseEip7702(payload));\n      }\n      assert(false, \"unsupported transaction type\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"from\"\n      });\n    }\n    const result = new Transaction();\n    if (tx.type != null) {\n      result.type = tx.type;\n    }\n    if (tx.to != null) {\n      result.to = tx.to;\n    }\n    if (tx.nonce != null) {\n      result.nonce = tx.nonce;\n    }\n    if (tx.gasLimit != null) {\n      result.gasLimit = tx.gasLimit;\n    }\n    if (tx.gasPrice != null) {\n      result.gasPrice = tx.gasPrice;\n    }\n    if (tx.maxPriorityFeePerGas != null) {\n      result.maxPriorityFeePerGas = tx.maxPriorityFeePerGas;\n    }\n    if (tx.maxFeePerGas != null) {\n      result.maxFeePerGas = tx.maxFeePerGas;\n    }\n    if (tx.maxFeePerBlobGas != null) {\n      result.maxFeePerBlobGas = tx.maxFeePerBlobGas;\n    }\n    if (tx.data != null) {\n      result.data = tx.data;\n    }\n    if (tx.value != null) {\n      result.value = tx.value;\n    }\n    if (tx.chainId != null) {\n      result.chainId = tx.chainId;\n    }\n    if (tx.signature != null) {\n      result.signature = Signature.from(tx.signature);\n    }\n    if (tx.accessList != null) {\n      result.accessList = tx.accessList;\n    }\n    if (tx.authorizationList != null) {\n      result.authorizationList = tx.authorizationList;\n    }\n    // This will get overwritten by blobs, if present\n    if (tx.blobVersionedHashes != null) {\n      result.blobVersionedHashes = tx.blobVersionedHashes;\n    }\n    // Make sure we assign the kzg before assigning blobs, which\n    // require the library in the event raw blob data is provided.\n    if (tx.kzg != null) {\n      result.kzg = tx.kzg;\n    }\n    if (tx.blobs != null) {\n      result.blobs = tx.blobs;\n    }\n    if (tx.hash != null) {\n      assertArgument(result.isSigned(), \"unsigned transaction cannot define '.hash'\", \"tx\", tx);\n      assertArgument(result.hash === tx.hash, \"hash mismatch\", \"tx\", tx);\n    }\n    if (tx.from != null) {\n      assertArgument(result.isSigned(), \"unsigned transaction cannot define '.from'\", \"tx\", tx);\n      assertArgument(result.from.toLowerCase() === (tx.from || \"\").toLowerCase(), \"from mismatch\", \"tx\", tx);\n    }\n    return result;\n  }\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "ZeroAddress", "keccak256", "sha256", "Signature", "SigningKey", "concat", "decodeRlp", "encodeRlp", "getBytes", "getBigInt", "getNumber", "hexlify", "assert", "assertArgument", "isBytesLike", "isHexString", "toBeArray", "zeroPadValue", "accessListify", "authorizationify", "recoverAddress", "BN_0", "BigInt", "BN_2", "BN_27", "BN_28", "BN_35", "BN_MAX_UINT", "BLOB_SIZE", "getKzgLibrary", "kzg", "blobToKzgCommitment", "blob", "blobToKZGCommitment", "computeBlobKzgProof", "commitment", "computeBlobProof", "computeBlobKZGProof", "getVersionedHash", "version", "hash", "versioned", "toString", "length", "substring", "handleAddress", "value", "handleAccessList", "param", "error", "message", "handleAuthorizationList", "Array", "isArray", "Error", "result", "i", "auth", "push", "address", "nonce", "handleUint", "chainId", "signature", "from", "yParity", "handleNumber", "r", "s", "_value", "formatNumber", "name", "formatAccessList", "map", "set", "storageKeys", "formatAuthorizationList", "a", "formatHashes", "_parseLegacy", "data", "fields", "tx", "type", "gasPrice", "gasLimit", "to", "v", "_serializeLegacy", "sig", "networkV", "legacyChainId", "legacy", "getChainIdV", "_parseEipSignature", "_parseEip1559", "slice", "maxPriorityFeePerGas", "maxFeePer<PERSON>as", "accessList", "_serializeEip1559", "_parseEip2930", "_serializeEip2930", "_parseEip4844", "typeName", "blobs", "fBlobs", "fCommits", "fProofs", "proof", "maxFeePerBlobGas", "blobVersionedHashes", "_serializeEip4844", "b", "_parseEip7702", "authorizationList", "_serializeEip7702", "Transaction", "auths", "Object", "assign", "_blobs", "versionedHashes", "operation", "padded", "Uint8Array", "commit", "constructor", "getSerialized", "unsignedHash", "unsignedSerialized", "fromPublicKey", "recoverPublicKey", "isSigned", "#getSerialized", "signed", "sidecar", "inferType", "serialized", "types", "inferTypes", "indexOf", "pop", "hasGasPrice", "<PERSON><PERSON>ee", "hasAccessList", "hasBlob", "sort", "isLegacy", "isBerlin", "isLondon", "isCancun", "clone", "toJSON", "payload", "toLowerCase"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\transaction\\transaction.ts"], "sourcesContent": ["\nimport { getAddress } from \"../address/index.js\";\nimport { ZeroAddress } from \"../constants/addresses.js\";\nimport {\n    keccak256, sha256, Signature, SigningKey\n} from \"../crypto/index.js\";\nimport {\n    concat, decodeRlp, encodeRlp, getBytes, getBigInt, getNumber, hexlify,\n    assert, assertArgument, isBytesLike, isHexString, toBeArray, zeroPadValue\n} from \"../utils/index.js\";\n\nimport { accessListify } from \"./accesslist.js\";\nimport { authorizationify } from \"./authorization.js\";\nimport { recoverAddress } from \"./address.js\";\n\nimport type { BigNumberish, BytesLike } from \"../utils/index.js\";\nimport type { SignatureLike } from \"../crypto/index.js\";\n\nimport type {\n    AccessList, AccessListish, Authorization, AuthorizationLike\n} from \"./index.js\";\n\n\nconst BN_0 = BigInt(0);\nconst BN_2 = BigInt(2);\nconst BN_27 = BigInt(27)\nconst BN_28 = BigInt(28)\nconst BN_35 = BigInt(35);\nconst BN_MAX_UINT = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n\nconst BLOB_SIZE = 4096 * 32;\n\n// The BLS Modulo; each field within a BLOb must be less than this\n//const BLOB_BLS_MODULO = BigInt(\"0x73eda753299d7d483339d80809a1d80553bda402fffe5bfeffffffff00000001\");\n\n/**\n *  A **TransactionLike** is an object which is appropriate as a loose\n *  input for many operations which will populate missing properties of\n *  a transaction.\n */\nexport interface TransactionLike<A = string> {\n    /**\n     *  The type.\n     */\n    type?: null | number;\n\n    /**\n     *  The recipient address or ``null`` for an ``init`` transaction.\n     */\n    to?: null | A;\n\n    /**\n     *  The sender.\n     */\n    from?: null | A;\n\n    /**\n     *  The nonce.\n     */\n    nonce?: null | number;\n\n    /**\n     *  The maximum amount of gas that can be used.\n     */\n    gasLimit?: null | BigNumberish;\n\n    /**\n     *  The gas price for legacy and berlin transactions.\n     */\n    gasPrice?: null | BigNumberish;\n\n    /**\n     *  The maximum priority fee per gas for london transactions.\n     */\n    maxPriorityFeePerGas?: null | BigNumberish;\n\n    /**\n     *  The maximum total fee per gas for london transactions.\n     */\n    maxFeePerGas?: null | BigNumberish;\n\n    /**\n     *  The data.\n     */\n    data?: null | string;\n\n    /**\n     *  The value (in wei) to send.\n     */\n    value?: null | BigNumberish;\n\n    /**\n     *  The chain ID the transaction is valid on.\n     */\n    chainId?: null | BigNumberish;\n\n    /**\n     *  The transaction hash.\n     */\n    hash?: null | string;\n\n    /**\n     *  The signature provided by the sender.\n     */\n    signature?: null | SignatureLike;\n\n    /**\n     *  The access list for berlin and london transactions.\n     */\n    accessList?: null | AccessListish;\n\n    /**\n     *  The maximum fee per blob gas (see [[link-eip-4844]]).\n     */\n    maxFeePerBlobGas?: null | BigNumberish;\n\n    /**\n     *  The versioned hashes (see [[link-eip-4844]]).\n     */\n    blobVersionedHashes?: null | Array<string>;\n\n    /**\n     *  The blobs (if any) attached to this transaction (see [[link-eip-4844]]).\n     */\n    blobs?: null | Array<BlobLike>\n\n    /**\n     *  An external library for computing the KZG commitments and\n     *  proofs necessary for EIP-4844 transactions (see [[link-eip-4844]]).\n     *\n     *  This is generally ``null``, unless you are creating BLOb\n     *  transactions.\n     */\n    kzg?: null | KzgLibraryLike;\n\n    /**\n     *  The [[link-eip-7702]] authorizations (if any).\n     */\n    authorizationList?: null | Array<Authorization>;\n}\n\n/**\n *  A full-valid BLOb object for [[link-eip-4844]] transactions.\n *\n *  The commitment and proof should have been computed using a\n *  KZG library.\n */\nexport interface Blob {\n    data: string;\n    proof: string;\n    commitment: string;\n}\n\n/**\n *  A BLOb object that can be passed for [[link-eip-4844]]\n *  transactions.\n *\n *  It may have had its commitment and proof already provided\n *  or rely on an attached [[KzgLibrary]] to compute them.\n */\nexport type BlobLike = BytesLike | {\n    data: BytesLike;\n    proof: BytesLike;\n    commitment: BytesLike;\n};\n\n/**\n *  A KZG Library with the necessary functions to compute\n *  BLOb commitments and proofs.\n */\nexport interface KzgLibrary {\n    blobToKzgCommitment: (blob: Uint8Array) => Uint8Array;\n    computeBlobKzgProof: (blob: Uint8Array, commitment: Uint8Array) => Uint8Array;\n}\n\n/**\n *  A KZG Library with any of the various API configurations.\n *  As the library is still experimental and the API is not\n *  stable, depending on the version used the method names and\n *  signatures are still in flux.\n *\n *  This allows any of the versions to be passed into Transaction\n *  while providing a stable external API.\n */\nexport type KzgLibraryLike  = KzgLibrary | {\n    // kzg-wasm >= 0.5.0\n    blobToKZGCommitment: (blob: string) => string;\n    computeBlobKZGProof: (blob: string, commitment: string) => string;\n} | {\n    // micro-ecc-signer\n    blobToKzgCommitment: (blob: string) => string | Uint8Array;\n    computeBlobProof: (blob: string, commitment: string) => string | Uint8Array;\n};\n\nfunction getKzgLibrary(kzg: KzgLibraryLike): KzgLibrary {\n\n    const blobToKzgCommitment = (blob: Uint8Array) => {\n\n        if (\"computeBlobProof\" in kzg) {\n            // micro-ecc-signer; check for computeBlobProof since this API\n            // expects a string while the kzg-wasm below expects a Unit8Array\n\n            if (\"blobToKzgCommitment\" in kzg && typeof(kzg.blobToKzgCommitment) === \"function\") {\n                return getBytes(kzg.blobToKzgCommitment(hexlify(blob)))\n            }\n\n        } else if (\"blobToKzgCommitment\" in kzg && typeof(kzg.blobToKzgCommitment) === \"function\") {\n            // kzg-wasm <0.5.0; blobToKzgCommitment(Uint8Array) => Uint8Array\n\n            return getBytes(kzg.blobToKzgCommitment(blob));\n        }\n\n        // kzg-wasm >= 0.5.0; blobToKZGCommitment(string) => string\n        if (\"blobToKZGCommitment\" in kzg && typeof(kzg.blobToKZGCommitment) === \"function\") {\n            return getBytes(kzg.blobToKZGCommitment(hexlify(blob)));\n        }\n\n        assertArgument(false, \"unsupported KZG library\", \"kzg\", kzg);\n    };\n\n    const computeBlobKzgProof = (blob: Uint8Array, commitment: Uint8Array) => {\n\n        // micro-ecc-signer\n        if (\"computeBlobProof\" in kzg && typeof(kzg.computeBlobProof) === \"function\") {\n            return getBytes(kzg.computeBlobProof(hexlify(blob), hexlify(commitment)))\n        }\n\n        // kzg-wasm <0.5.0; computeBlobKzgProof(Uint8Array, Uint8Array) => Uint8Array\n        if (\"computeBlobKzgProof\" in kzg && typeof(kzg.computeBlobKzgProof) === \"function\") {\n            return kzg.computeBlobKzgProof(blob, commitment);\n        }\n\n        // kzg-wasm >= 0.5.0; computeBlobKZGProof(string, string) => string\n        if (\"computeBlobKZGProof\" in kzg && typeof(kzg.computeBlobKZGProof) === \"function\") {\n            return getBytes(kzg.computeBlobKZGProof(hexlify(blob), hexlify(commitment)));\n        }\n\n        assertArgument(false, \"unsupported KZG library\", \"kzg\", kzg);\n    };\n\n    return { blobToKzgCommitment, computeBlobKzgProof };\n}\n\nfunction getVersionedHash(version: number, hash: BytesLike): string {\n    let versioned = version.toString(16);\n    while (versioned.length < 2) { versioned = \"0\" + versioned; }\n    versioned += sha256(hash).substring(4);\n    return \"0x\" + versioned;\n}\n\nfunction handleAddress(value: string): null | string {\n    if (value === \"0x\") { return null; }\n    return getAddress(value);\n}\n\nfunction handleAccessList(value: any, param: string): AccessList {\n    try {\n        return accessListify(value);\n    } catch (error: any) {\n        assertArgument(false, error.message, param, value);\n    }\n}\n\nfunction handleAuthorizationList(value: any, param: string): Array<Authorization> {\n    try {\n        if (!Array.isArray(value)) { throw new Error(\"authorizationList: invalid array\"); }\n        const result: Array<Authorization> = [ ];\n        for (let i = 0; i < value.length; i++) {\n            const auth: Array<string> = value[i];\n            if (!Array.isArray(auth)) { throw new Error(`authorization[${ i }]: invalid array`); }\n            if (auth.length !== 6) { throw new Error(`authorization[${ i }]: wrong length`); }\n            if (!auth[1]) { throw new Error(`authorization[${ i }]: null address`); }\n            result.push({\n                address: <string>handleAddress(auth[1]),\n                nonce: handleUint(auth[2], \"nonce\"),\n                chainId: handleUint(auth[0], \"chainId\"),\n                signature: Signature.from({\n                    yParity: <0 | 1>handleNumber(auth[3], \"yParity\"),\n                    r: zeroPadValue(auth[4], 32),\n                    s: zeroPadValue(auth[5], 32)\n                })\n            });\n        }\n        return result;\n    } catch (error: any) {\n        assertArgument(false, error.message, param, value);\n    }\n}\n\nfunction handleNumber(_value: string, param: string): number {\n    if (_value === \"0x\") { return 0; }\n    return getNumber(_value, param);\n}\n\nfunction handleUint(_value: string, param: string): bigint {\n    if (_value === \"0x\") { return BN_0; }\n    const value = getBigInt(_value, param);\n    assertArgument(value <= BN_MAX_UINT, \"value exceeds uint size\", param, value);\n    return value;\n}\n\nfunction formatNumber(_value: BigNumberish, name: string): Uint8Array {\n    const value = getBigInt(_value, \"value\");\n    const result = toBeArray(value);\n    assertArgument(result.length <= 32, `value too large`, `tx.${ name }`, value);\n    return result;\n}\n\nfunction formatAccessList(value: AccessListish): Array<[ string, Array<string> ]> {\n    return accessListify(value).map((set) => [ set.address, set.storageKeys ]);\n}\n\nfunction formatAuthorizationList(value: Array<Authorization>): Array<Array<string | Uint8Array>> {\n    return value.map((a) => {\n        return [\n            formatNumber(a.chainId, \"chainId\"),\n            a.address,\n            formatNumber(a.nonce, \"nonce\"),\n            formatNumber(a.signature.yParity, \"yParity\"),\n            a.signature.r,\n            a.signature.s\n        ];\n    });\n}\n\nfunction formatHashes(value: Array<string>, param: string): Array<string> {\n    assertArgument(Array.isArray(value), `invalid ${ param }`, \"value\", value);\n    for (let i = 0; i < value.length; i++) {\n        assertArgument(isHexString(value[i], 32), \"invalid ${ param } hash\", `value[${ i }]`, value[i]);\n    }\n    return value;\n}\n\nfunction _parseLegacy(data: Uint8Array): TransactionLike {\n    const fields: any = decodeRlp(data);\n\n    assertArgument(Array.isArray(fields) && (fields.length === 9 || fields.length === 6),\n        \"invalid field count for legacy transaction\", \"data\", data);\n\n    const tx: TransactionLike = {\n        type:     0,\n        nonce:    handleNumber(fields[0], \"nonce\"),\n        gasPrice: handleUint(fields[1], \"gasPrice\"),\n        gasLimit: handleUint(fields[2], \"gasLimit\"),\n        to:       handleAddress(fields[3]),\n        value:    handleUint(fields[4], \"value\"),\n        data:     hexlify(fields[5]),\n        chainId:  BN_0\n    };\n\n    // Legacy unsigned transaction\n    if (fields.length === 6) { return tx; }\n\n    const v = handleUint(fields[6], \"v\");\n    const r = handleUint(fields[7], \"r\");\n    const s = handleUint(fields[8], \"s\");\n\n    if (r === BN_0 && s === BN_0) {\n        // EIP-155 unsigned transaction\n        tx.chainId = v;\n\n    } else {\n\n        // Compute the EIP-155 chain ID (or 0 for legacy)\n        let chainId = (v - BN_35) / BN_2;\n        if (chainId < BN_0) { chainId = BN_0; }\n        tx.chainId = chainId\n\n        // Signed Legacy Transaction\n        assertArgument(chainId !== BN_0 || (v === BN_27 || v === BN_28), \"non-canonical legacy v\", \"v\", fields[6]);\n\n        tx.signature = Signature.from({\n            r: zeroPadValue(fields[7], 32),\n            s: zeroPadValue(fields[8], 32),\n            v\n        });\n\n        //tx.hash = keccak256(data);\n    }\n\n    return tx;\n}\n\nfunction _serializeLegacy(tx: Transaction, sig: null | Signature): string {\n    const fields: Array<any> = [\n        formatNumber(tx.nonce, \"nonce\"),\n        formatNumber(tx.gasPrice || 0, \"gasPrice\"),\n        formatNumber(tx.gasLimit, \"gasLimit\"),\n        (tx.to || \"0x\"),\n        formatNumber(tx.value, \"value\"),\n        tx.data,\n    ];\n\n    let chainId = BN_0;\n    if (tx.chainId != BN_0) {\n        // A chainId was provided; if non-zero we'll use EIP-155\n        chainId = getBigInt(tx.chainId, \"tx.chainId\");\n\n        // We have a chainId in the tx and an EIP-155 v in the signature,\n        // make sure they agree with each other\n        assertArgument(!sig || sig.networkV == null || sig.legacyChainId === chainId,\n             \"tx.chainId/sig.v mismatch\", \"sig\", sig);\n\n    } else if (tx.signature) {\n        // No explicit chainId, but EIP-155 have a derived implicit chainId\n        const legacy = tx.signature.legacyChainId;\n        if (legacy != null) { chainId = legacy; }\n    }\n\n    // Requesting an unsigned transaction\n    if (!sig) {\n        // We have an EIP-155 transaction (chainId was specified and non-zero)\n        if (chainId !== BN_0) {\n            fields.push(toBeArray(chainId));\n            fields.push(\"0x\");\n            fields.push(\"0x\");\n        }\n\n        return encodeRlp(fields);\n    }\n\n    // @TODO: We should probably check that tx.signature, chainId, and sig\n    //        match but that logic could break existing code, so schedule\n    //        this for the next major bump.\n\n    // Compute the EIP-155 v\n    let v = BigInt(27 + sig.yParity);\n    if (chainId !== BN_0) {\n        v = Signature.getChainIdV(chainId, sig.v);\n    } else if (BigInt(sig.v) !== v) {\n        assertArgument(false, \"tx.chainId/sig.v mismatch\", \"sig\", sig);\n    }\n\n    // Add the signature\n    fields.push(toBeArray(v));\n    fields.push(toBeArray(sig.r));\n    fields.push(toBeArray(sig.s));\n\n    return encodeRlp(fields);\n}\n\nfunction _parseEipSignature(tx: TransactionLike, fields: Array<string>): void {\n    let yParity: number;\n    try {\n        yParity = handleNumber(fields[0], \"yParity\");\n        if (yParity !== 0 && yParity !== 1) { throw new Error(\"bad yParity\"); }\n    } catch (error) {\n        assertArgument(false, \"invalid yParity\", \"yParity\", fields[0]);\n    }\n\n    const r = zeroPadValue(fields[1], 32);\n    const s = zeroPadValue(fields[2], 32);\n\n    const signature = Signature.from({ r, s, yParity });\n    tx.signature = signature;\n}\n\nfunction _parseEip1559(data: Uint8Array): TransactionLike {\n    const fields: any = decodeRlp(getBytes(data).slice(1));\n\n    assertArgument(Array.isArray(fields) && (fields.length === 9 || fields.length === 12),\n        \"invalid field count for transaction type: 2\", \"data\", hexlify(data));\n\n    const tx: TransactionLike = {\n        type:                  2,\n        chainId:               handleUint(fields[0], \"chainId\"),\n        nonce:                 handleNumber(fields[1], \"nonce\"),\n        maxPriorityFeePerGas:  handleUint(fields[2], \"maxPriorityFeePerGas\"),\n        maxFeePerGas:          handleUint(fields[3], \"maxFeePerGas\"),\n        gasPrice:              null,\n        gasLimit:              handleUint(fields[4], \"gasLimit\"),\n        to:                    handleAddress(fields[5]),\n        value:                 handleUint(fields[6], \"value\"),\n        data:                  hexlify(fields[7]),\n        accessList:            handleAccessList(fields[8], \"accessList\"),\n    };\n\n    // Unsigned EIP-1559 Transaction\n    if (fields.length === 9) { return tx; }\n\n    //tx.hash = keccak256(data);\n\n    _parseEipSignature(tx, fields.slice(9));\n\n    return tx;\n}\n\nfunction _serializeEip1559(tx: Transaction, sig: null | Signature): string {\n    const fields: Array<any> = [\n        formatNumber(tx.chainId, \"chainId\"),\n        formatNumber(tx.nonce, \"nonce\"),\n        formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"),\n        formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"),\n        formatNumber(tx.gasLimit, \"gasLimit\"),\n        (tx.to || \"0x\"),\n        formatNumber(tx.value, \"value\"),\n        tx.data,\n        formatAccessList(tx.accessList || [ ])\n    ];\n\n    if (sig) {\n        fields.push(formatNumber(sig.yParity, \"yParity\"));\n        fields.push(toBeArray(sig.r));\n        fields.push(toBeArray(sig.s));\n    }\n\n    return concat([ \"0x02\", encodeRlp(fields)]);\n}\n\nfunction _parseEip2930(data: Uint8Array): TransactionLike {\n    const fields: any = decodeRlp(getBytes(data).slice(1));\n\n    assertArgument(Array.isArray(fields) && (fields.length === 8 || fields.length === 11),\n        \"invalid field count for transaction type: 1\", \"data\", hexlify(data));\n\n    const tx: TransactionLike = {\n        type:       1,\n        chainId:    handleUint(fields[0], \"chainId\"),\n        nonce:      handleNumber(fields[1], \"nonce\"),\n        gasPrice:   handleUint(fields[2], \"gasPrice\"),\n        gasLimit:   handleUint(fields[3], \"gasLimit\"),\n        to:         handleAddress(fields[4]),\n        value:      handleUint(fields[5], \"value\"),\n        data:       hexlify(fields[6]),\n        accessList: handleAccessList(fields[7], \"accessList\")\n    };\n\n    // Unsigned EIP-2930 Transaction\n    if (fields.length === 8) { return tx; }\n\n    //tx.hash = keccak256(data);\n\n    _parseEipSignature(tx, fields.slice(8));\n\n    return tx;\n}\n\nfunction _serializeEip2930(tx: Transaction, sig: null | Signature): string {\n    const fields: any = [\n        formatNumber(tx.chainId, \"chainId\"),\n        formatNumber(tx.nonce, \"nonce\"),\n        formatNumber(tx.gasPrice || 0, \"gasPrice\"),\n        formatNumber(tx.gasLimit, \"gasLimit\"),\n        (tx.to || \"0x\"),\n        formatNumber(tx.value, \"value\"),\n        tx.data,\n        formatAccessList(tx.accessList || [ ])\n    ];\n\n    if (sig) {\n        fields.push(formatNumber(sig.yParity, \"recoveryParam\"));\n        fields.push(toBeArray(sig.r));\n        fields.push(toBeArray(sig.s));\n    }\n\n    return concat([ \"0x01\", encodeRlp(fields)]);\n}\n\nfunction _parseEip4844(data: Uint8Array): TransactionLike {\n    let fields: any = decodeRlp(getBytes(data).slice(1));\n\n    let typeName = \"3\";\n\n    let blobs: null | Array<Blob> = null;\n\n    // Parse the network format\n    if (fields.length === 4 && Array.isArray(fields[0])) {\n        typeName = \"3 (network format)\";\n        const fBlobs = fields[1], fCommits = fields[2], fProofs = fields[3];\n        assertArgument(Array.isArray(fBlobs), \"invalid network format: blobs not an array\", \"fields[1]\", fBlobs);\n        assertArgument(Array.isArray(fCommits), \"invalid network format: commitments not an array\", \"fields[2]\", fCommits);\n        assertArgument(Array.isArray(fProofs), \"invalid network format: proofs not an array\", \"fields[3]\", fProofs);\n        assertArgument(fBlobs.length === fCommits.length, \"invalid network format: blobs/commitments length mismatch\", \"fields\", fields);\n        assertArgument(fBlobs.length === fProofs.length, \"invalid network format: blobs/proofs length mismatch\", \"fields\", fields);\n\n        blobs = [ ];\n        for (let i = 0; i < fields[1].length; i++) {\n            blobs.push({\n                data: fBlobs[i],\n                commitment: fCommits[i],\n                proof: fProofs[i],\n            });\n        }\n\n        fields = fields[0];\n    }\n\n    assertArgument(Array.isArray(fields) && (fields.length === 11 || fields.length === 14),\n        `invalid field count for transaction type: ${ typeName }`, \"data\", hexlify(data));\n\n    const tx: TransactionLike = {\n        type:                  3,\n        chainId:               handleUint(fields[0], \"chainId\"),\n        nonce:                 handleNumber(fields[1], \"nonce\"),\n        maxPriorityFeePerGas:  handleUint(fields[2], \"maxPriorityFeePerGas\"),\n        maxFeePerGas:          handleUint(fields[3], \"maxFeePerGas\"),\n        gasPrice:              null,\n        gasLimit:              handleUint(fields[4], \"gasLimit\"),\n        to:                    handleAddress(fields[5]),\n        value:                 handleUint(fields[6], \"value\"),\n        data:                  hexlify(fields[7]),\n        accessList:            handleAccessList(fields[8], \"accessList\"),\n        maxFeePerBlobGas:      handleUint(fields[9], \"maxFeePerBlobGas\"),\n        blobVersionedHashes:   fields[10]\n    };\n\n    if (blobs) { tx.blobs = blobs; }\n\n    assertArgument(tx.to != null, `invalid address for transaction type: ${ typeName }`, \"data\", data);\n\n    assertArgument(Array.isArray(tx.blobVersionedHashes), \"invalid blobVersionedHashes: must be an array\", \"data\", data);\n    for (let i = 0; i < tx.blobVersionedHashes.length; i++) {\n        assertArgument(isHexString(tx.blobVersionedHashes[i], 32), `invalid blobVersionedHash at index ${ i }: must be length 32`, \"data\", data);\n    }\n\n    // Unsigned EIP-4844 Transaction\n    if (fields.length === 11) { return tx; }\n\n    // @TODO: Do we need to do this? This is only called internally\n    // and used to verify hashes; it might save time to not do this\n    //tx.hash = keccak256(concat([ \"0x03\", encodeRlp(fields) ]));\n\n    _parseEipSignature(tx, fields.slice(11));\n\n    return tx;\n}\n\nfunction _serializeEip4844(tx: Transaction, sig: null | Signature, blobs: null | Array<Blob>): string {\n    const fields: Array<any> = [\n        formatNumber(tx.chainId, \"chainId\"),\n        formatNumber(tx.nonce, \"nonce\"),\n        formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"),\n        formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"),\n        formatNumber(tx.gasLimit, \"gasLimit\"),\n        (tx.to || ZeroAddress),\n        formatNumber(tx.value, \"value\"),\n        tx.data,\n        formatAccessList(tx.accessList || [ ]),\n        formatNumber(tx.maxFeePerBlobGas || 0, \"maxFeePerBlobGas\"),\n        formatHashes(tx.blobVersionedHashes || [ ], \"blobVersionedHashes\")\n    ];\n\n    if (sig) {\n        fields.push(formatNumber(sig.yParity, \"yParity\"));\n        fields.push(toBeArray(sig.r));\n        fields.push(toBeArray(sig.s));\n\n        // We have blobs; return the network wrapped format\n        if (blobs) {\n            return concat([\n                \"0x03\",\n                encodeRlp([\n                    fields,\n                    blobs.map((b) => b.data),\n                    blobs.map((b) => b.commitment),\n                    blobs.map((b) => b.proof),\n                ])\n            ]);\n        }\n\n    }\n\n    return concat([ \"0x03\", encodeRlp(fields)]);\n}\n\nfunction _parseEip7702(data: Uint8Array): TransactionLike {\n    const fields: any = decodeRlp(getBytes(data).slice(1));\n\n    assertArgument(Array.isArray(fields) && (fields.length === 10 || fields.length === 13),\n        \"invalid field count for transaction type: 4\", \"data\", hexlify(data));\n\n    const tx: TransactionLike = {\n        type:                  4,\n        chainId:               handleUint(fields[0], \"chainId\"),\n        nonce:                 handleNumber(fields[1], \"nonce\"),\n        maxPriorityFeePerGas:  handleUint(fields[2], \"maxPriorityFeePerGas\"),\n        maxFeePerGas:          handleUint(fields[3], \"maxFeePerGas\"),\n        gasPrice:              null,\n        gasLimit:              handleUint(fields[4], \"gasLimit\"),\n        to:                    handleAddress(fields[5]),\n        value:                 handleUint(fields[6], \"value\"),\n        data:                  hexlify(fields[7]),\n        accessList:            handleAccessList(fields[8], \"accessList\"),\n        authorizationList:     handleAuthorizationList(fields[9], \"authorizationList\"),\n    };\n\n    // Unsigned EIP-7702 Transaction\n    if (fields.length === 10) { return tx; }\n\n    _parseEipSignature(tx, fields.slice(10));\n\n    return tx;\n}\n\nfunction _serializeEip7702(tx: Transaction, sig: null | Signature): string {\n    const fields: Array<any> = [\n        formatNumber(tx.chainId, \"chainId\"),\n        formatNumber(tx.nonce, \"nonce\"),\n        formatNumber(tx.maxPriorityFeePerGas || 0, \"maxPriorityFeePerGas\"),\n        formatNumber(tx.maxFeePerGas || 0, \"maxFeePerGas\"),\n        formatNumber(tx.gasLimit, \"gasLimit\"),\n        (tx.to || \"0x\"),\n        formatNumber(tx.value, \"value\"),\n        tx.data,\n        formatAccessList(tx.accessList || [ ]),\n        formatAuthorizationList(tx.authorizationList || [ ])\n    ];\n\n    if (sig) {\n        fields.push(formatNumber(sig.yParity, \"yParity\"));\n        fields.push(toBeArray(sig.r));\n        fields.push(toBeArray(sig.s));\n    }\n\n    return concat([ \"0x04\", encodeRlp(fields)]);\n}\n\n/**\n *  A **Transaction** describes an operation to be executed on\n *  Ethereum by an Externally Owned Account (EOA). It includes\n *  who (the [[to]] address), what (the [[data]]) and how much (the\n *  [[value]] in ether) the operation should entail.\n *\n *  @example:\n *    tx = new Transaction()\n *    //_result:\n *\n *    tx.data = \"0x1234\";\n *    //_result:\n */\nexport class Transaction implements TransactionLike<string> {\n    #type: null | number;\n    #to: null | string;\n    #data: string;\n    #nonce: number;\n    #gasLimit: bigint;\n    #gasPrice: null | bigint;\n    #maxPriorityFeePerGas: null | bigint;\n    #maxFeePerGas: null | bigint;\n    #value: bigint;\n    #chainId: bigint;\n    #sig: null | Signature;\n    #accessList: null | AccessList;\n    #maxFeePerBlobGas: null | bigint;\n    #blobVersionedHashes: null | Array<string>;\n    #kzg: null | KzgLibrary;\n    #blobs: null | Array<Blob>;\n    #auths: null | Array<Authorization>;\n\n    /**\n     *  The transaction type.\n     *\n     *  If null, the type will be automatically inferred based on\n     *  explicit properties.\n     */\n    get type(): null | number { return this.#type; }\n    set type(value: null | number | string) {\n        switch (value) {\n            case null:\n                this.#type = null;\n                break;\n            case 0: case \"legacy\":\n                this.#type = 0;\n                break;\n            case 1: case \"berlin\": case \"eip-2930\":\n                this.#type = 1;\n                break;\n            case 2: case \"london\": case \"eip-1559\":\n                this.#type = 2;\n                break;\n            case 3: case \"cancun\": case \"eip-4844\":\n                this.#type = 3;\n                break;\n            case 4: case \"pectra\": case \"eip-7702\":\n                this.#type = 4;\n                break;\n            default:\n                assertArgument(false, \"unsupported transaction type\", \"type\", value);\n        }\n    }\n\n    /**\n     *  The name of the transaction type.\n     */\n    get typeName(): null | string {\n        switch (this.type) {\n            case 0: return \"legacy\";\n            case 1: return \"eip-2930\";\n            case 2: return \"eip-1559\";\n            case 3: return \"eip-4844\";\n            case 4: return \"eip-7702\";\n        }\n\n        return null;\n    }\n\n    /**\n     *  The ``to`` address for the transaction or ``null`` if the\n     *  transaction is an ``init`` transaction.\n     */\n    get to(): null | string {\n        const value = this.#to;\n        if (value == null && this.type === 3) { return ZeroAddress; }\n        return value;\n    }\n    set to(value: null | string) {\n        this.#to = (value == null) ? null: getAddress(value);\n    }\n\n    /**\n     *  The transaction nonce.\n     */\n    get nonce(): number { return this.#nonce; }\n    set nonce(value: BigNumberish) { this.#nonce = getNumber(value, \"value\"); }\n\n    /**\n     *  The gas limit.\n     */\n    get gasLimit(): bigint { return this.#gasLimit; }\n    set gasLimit(value: BigNumberish) { this.#gasLimit = getBigInt(value); }\n\n    /**\n     *  The gas price.\n     *\n     *  On legacy networks this defines the fee that will be paid. On\n     *  EIP-1559 networks, this should be ``null``.\n     */\n    get gasPrice(): null | bigint {\n        const value = this.#gasPrice;\n        if (value == null && (this.type === 0 || this.type === 1)) { return BN_0; }\n        return value;\n    }\n    set gasPrice(value: null | BigNumberish) {\n        this.#gasPrice = (value == null) ? null: getBigInt(value, \"gasPrice\");\n    }\n\n    /**\n     *  The maximum priority fee per unit of gas to pay. On legacy\n     *  networks this should be ``null``.\n     */\n    get maxPriorityFeePerGas(): null | bigint {\n        const value = this.#maxPriorityFeePerGas;\n        if (value == null) {\n            if (this.type === 2 || this.type === 3) { return BN_0; }\n            return null;\n        }\n        return value;\n    }\n    set maxPriorityFeePerGas(value: null | BigNumberish) {\n        this.#maxPriorityFeePerGas = (value == null) ? null: getBigInt(value, \"maxPriorityFeePerGas\");\n    }\n\n    /**\n     *  The maximum total fee per unit of gas to pay. On legacy\n     *  networks this should be ``null``.\n     */\n    get maxFeePerGas(): null | bigint {\n        const value = this.#maxFeePerGas;\n        if (value == null) {\n            if (this.type === 2 || this.type === 3) { return BN_0; }\n            return null;\n        }\n        return value;\n    }\n    set maxFeePerGas(value: null | BigNumberish) {\n        this.#maxFeePerGas = (value == null) ? null: getBigInt(value, \"maxFeePerGas\");\n    }\n\n    /**\n     *  The transaction data. For ``init`` transactions this is the\n     *  deployment code.\n     */\n    get data(): string { return this.#data; }\n    set data(value: BytesLike) { this.#data = hexlify(value); }\n\n    /**\n     *  The amount of ether (in wei) to send in this transactions.\n     */\n    get value(): bigint { return this.#value; }\n    set value(value: BigNumberish) {\n        this.#value = getBigInt(value, \"value\");\n    }\n\n    /**\n     *  The chain ID this transaction is valid on.\n     */\n    get chainId(): bigint { return this.#chainId; }\n    set chainId(value: BigNumberish) { this.#chainId = getBigInt(value); }\n\n    /**\n     *  If signed, the signature for this transaction.\n     */\n    get signature(): null | Signature { return this.#sig || null; }\n    set signature(value: null | SignatureLike) {\n        this.#sig = (value == null) ? null: Signature.from(value);\n    }\n\n    /**\n     *  The access list.\n     *\n     *  An access list permits discounted (but pre-paid) access to\n     *  bytecode and state variable access within contract execution.\n     */\n    get accessList(): null | AccessList {\n        const value = this.#accessList || null;\n        if (value == null) {\n            if (this.type === 1 || this.type === 2 || this.type === 3) {\n                // @TODO: in v7, this should assign the value or become\n                // a live object itself, otherwise mutation is inconsistent\n                return [ ];\n            }\n            return null;\n        }\n        return value;\n    }\n    set accessList(value: null | AccessListish) {\n        this.#accessList = (value == null) ? null: accessListify(value);\n    }\n\n    get authorizationList(): null | Array<Authorization> {\n        const value = this.#auths || null;\n        if (value == null) {\n            if (this.type === 4) {\n                // @TODO: in v7, this should become a live object itself,\n                // otherwise mutation is inconsistent\n                return [ ];\n            }\n        }\n        return value;\n    }\n    set authorizationList(auths: null | Array<AuthorizationLike>) {\n        this.#auths = (auths == null) ? null: auths.map((a) =>\n          authorizationify(a));\n    }\n\n    /**\n     *  The max fee per blob gas for Cancun transactions.\n     */\n    get maxFeePerBlobGas(): null | bigint {\n        const value = this.#maxFeePerBlobGas;\n        if (value == null && this.type === 3) { return BN_0; }\n        return value;\n    }\n    set maxFeePerBlobGas(value: null | BigNumberish) {\n        this.#maxFeePerBlobGas = (value == null) ? null: getBigInt(value, \"maxFeePerBlobGas\");\n    }\n\n    /**\n     *  The BLOb versioned hashes for Cancun transactions.\n     */\n    get blobVersionedHashes(): null | Array<string> {\n        // @TODO: Mutation is inconsistent; if unset, the returned value\n        // cannot mutate the object, if set it can\n        let value = this.#blobVersionedHashes;\n        if (value == null && this.type === 3) { return [ ]; }\n        return value;\n    }\n    set blobVersionedHashes(value: null | Array<string>) {\n        if (value != null) {\n            assertArgument(Array.isArray(value), \"blobVersionedHashes must be an Array\", \"value\", value);\n            value = value.slice();\n            for (let i = 0; i < value.length; i++) {\n                assertArgument(isHexString(value[i], 32), \"invalid blobVersionedHash\", `value[${ i }]`, value[i]);\n            }\n        }\n        this.#blobVersionedHashes = value;\n    }\n\n    /**\n     *  The BLObs for the Transaction, if any.\n     *\n     *  If ``blobs`` is non-``null``, then the [[seriailized]]\n     *  will return the network formatted sidecar, otherwise it\n     *  will return the standard [[link-eip-2718]] payload. The\n     *  [[unsignedSerialized]] is unaffected regardless.\n     *\n     *  When setting ``blobs``, either fully valid [[Blob]] objects\n     *  may be specified (i.e. correctly padded, with correct\n     *  committments and proofs) or a raw [[BytesLike]] may\n     *  be provided.\n     *\n     *  If raw [[BytesLike]] are provided, the [[kzg]] property **must**\n     *  be already set. The blob will be correctly padded and the\n     *  [[KzgLibrary]] will be used to compute the committment and\n     *  proof for the blob.\n     *\n     *  A BLOb is a sequence of field elements, each of which must\n     *  be within the BLS field modulo, so some additional processing\n     *  may be required to encode arbitrary data to ensure each 32 byte\n     *  field is within the valid range.\n     *\n     *  Setting this automatically populates [[blobVersionedHashes]],\n     *  overwriting any existing values. Setting this to ``null``\n     *  does **not** remove the [[blobVersionedHashes]], leaving them\n     *  present.\n     */\n    get blobs(): null | Array<Blob> {\n        if (this.#blobs == null) { return null; }\n        return this.#blobs.map((b) => Object.assign({ }, b));\n    }\n    set blobs(_blobs: null | Array<BlobLike>) {\n        if (_blobs == null) {\n            this.#blobs = null;\n            return;\n        }\n\n        const blobs: Array<Blob> = [ ];\n        const versionedHashes: Array<string> = [ ];\n        for (let i = 0; i < _blobs.length; i++) {\n            const blob = _blobs[i];\n\n            if (isBytesLike(blob)) {\n                assert(this.#kzg, \"adding a raw blob requires a KZG library\", \"UNSUPPORTED_OPERATION\", {\n                    operation: \"set blobs()\"\n                });\n\n                let data = getBytes(blob);\n                assertArgument(data.length <= BLOB_SIZE, \"blob is too large\", `blobs[${ i }]`, blob);\n\n                // Pad blob if necessary\n                if (data.length !== BLOB_SIZE) {\n                    const padded = new Uint8Array(BLOB_SIZE);\n                    padded.set(data);\n                    data = padded;\n                }\n\n                const commit = this.#kzg.blobToKzgCommitment(data);\n                const proof = hexlify(this.#kzg.computeBlobKzgProof(data, commit));\n\n                blobs.push({\n                    data: hexlify(data),\n                    commitment: hexlify(commit),\n                    proof\n                });\n                versionedHashes.push(getVersionedHash(1, commit));\n\n            } else {\n                const commit = hexlify(blob.commitment);\n                blobs.push({\n                    data: hexlify(blob.data),\n                    commitment: commit,\n                    proof: hexlify(blob.proof)\n                });\n                versionedHashes.push(getVersionedHash(1, commit));\n            }\n        }\n\n        this.#blobs = blobs;\n        this.#blobVersionedHashes = versionedHashes;\n    }\n\n    get kzg(): null | KzgLibrary { return this.#kzg; }\n    set kzg(kzg: null | KzgLibraryLike) {\n        if (kzg == null) {\n            this.#kzg = null;\n        } else {\n            this.#kzg = getKzgLibrary(kzg);\n        }\n    }\n\n    /**\n     *  Creates a new Transaction with default values.\n     */\n    constructor() {\n        this.#type = null;\n        this.#to = null;\n        this.#nonce = 0;\n        this.#gasLimit = BN_0;\n        this.#gasPrice = null;\n        this.#maxPriorityFeePerGas = null;\n        this.#maxFeePerGas = null;\n        this.#data = \"0x\";\n        this.#value = BN_0;\n        this.#chainId = BN_0;\n        this.#sig = null;\n        this.#accessList = null;\n        this.#maxFeePerBlobGas = null;\n        this.#blobVersionedHashes = null;\n        this.#kzg = null;\n        this.#blobs = null;\n        this.#auths = null;\n    }\n\n    /**\n     *  The transaction hash, if signed. Otherwise, ``null``.\n     */\n    get hash(): null | string {\n        if (this.signature == null) { return null; }\n        return keccak256(this.#getSerialized(true, false));\n    }\n\n    /**\n     *  The pre-image hash of this transaction.\n     *\n     *  This is the digest that a [[Signer]] must sign to authorize\n     *  this transaction.\n     */\n    get unsignedHash(): string {\n        return keccak256(this.unsignedSerialized);\n    }\n\n    /**\n     *  The sending address, if signed. Otherwise, ``null``.\n     */\n    get from(): null | string {\n        if (this.signature == null) { return null; }\n        return recoverAddress(this.unsignedHash, this.signature);\n    }\n\n    /**\n     *  The public key of the sender, if signed. Otherwise, ``null``.\n     */\n    get fromPublicKey(): null | string {\n        if (this.signature == null) { return null; }\n        return SigningKey.recoverPublicKey(this.unsignedHash, this.signature);\n    }\n\n    /**\n     *  Returns true if signed.\n     *\n     *  This provides a Type Guard that properties requiring a signed\n     *  transaction are non-null.\n     */\n    isSigned(): this is (Transaction & { type: number, typeName: string, from: string, signature: Signature }) {\n        return this.signature != null;\n    }\n\n    #getSerialized(signed: boolean, sidecar: boolean): string {\n        assert(!signed || this.signature != null, \"cannot serialize unsigned transaction; maybe you meant .unsignedSerialized\", \"UNSUPPORTED_OPERATION\", { operation: \".serialized\"});\n\n        const sig = signed ? this.signature: null;\n        switch (this.inferType()) {\n            case 0:\n                return _serializeLegacy(this, sig);\n            case 1:\n                return _serializeEip2930(this, sig);\n            case 2:\n                return _serializeEip1559(this, sig);\n            case 3:\n                return _serializeEip4844(this, sig, sidecar ? this.blobs: null);\n            case 4:\n                return _serializeEip7702(this, sig);\n        }\n\n        assert(false, \"unsupported transaction type\", \"UNSUPPORTED_OPERATION\", { operation: \".serialized\" });\n    }\n\n    /**\n     *  The serialized transaction.\n     *\n     *  This throws if the transaction is unsigned. For the pre-image,\n     *  use [[unsignedSerialized]].\n     */\n    get serialized(): string {\n        return this.#getSerialized(true, true);\n    }\n\n    /**\n     *  The transaction pre-image.\n     *\n     *  The hash of this is the digest which needs to be signed to\n     *  authorize this transaction.\n     */\n    get unsignedSerialized(): string {\n        return this.#getSerialized(false, false);\n    }\n\n    /**\n     *  Return the most \"likely\" type; currently the highest\n     *  supported transaction type.\n     */\n    inferType(): number {\n        const types = this.inferTypes();\n\n        // Prefer London (EIP-1559) over Cancun (BLOb)\n        if (types.indexOf(2) >= 0) { return 2; }\n\n        // Return the highest inferred type\n        return <number>(types.pop());\n    }\n\n    /**\n     *  Validates the explicit properties and returns a list of compatible\n     *  transaction types.\n     */\n    inferTypes(): Array<number> {\n\n        // Checks that there are no conflicting properties set\n        const hasGasPrice = this.gasPrice != null;\n        const hasFee = (this.maxFeePerGas != null || this.maxPriorityFeePerGas != null);\n        const hasAccessList = (this.accessList != null);\n        const hasBlob = (this.#maxFeePerBlobGas != null || this.#blobVersionedHashes);\n\n        //if (hasGasPrice && hasFee) {\n        //    throw new Error(\"transaction cannot have gasPrice and maxFeePerGas\");\n        //}\n\n        if (this.maxFeePerGas != null && this.maxPriorityFeePerGas != null) {\n            assert(this.maxFeePerGas >= this.maxPriorityFeePerGas, \"priorityFee cannot be more than maxFee\", \"BAD_DATA\", { value: this });\n        }\n\n        //if (this.type === 2 && hasGasPrice) {\n        //    throw new Error(\"eip-1559 transaction cannot have gasPrice\");\n        //}\n\n        assert(!hasFee || (this.type !== 0 && this.type !== 1), \"transaction type cannot have maxFeePerGas or maxPriorityFeePerGas\", \"BAD_DATA\", { value: this });\n        assert(this.type !== 0 || !hasAccessList, \"legacy transaction cannot have accessList\", \"BAD_DATA\", { value: this })\n\n        const types: Array<number> = [ ];\n\n        // Explicit type\n        if (this.type != null) {\n            types.push(this.type);\n\n        } else {\n            if (this.authorizationList && this.authorizationList.length) {\n                types.push(4);\n            } else if (hasFee) {\n                types.push(2);\n            } else if (hasGasPrice) {\n                types.push(1);\n                if (!hasAccessList) { types.push(0); }\n            } else if (hasAccessList) {\n                types.push(1);\n                types.push(2);\n            } else if (hasBlob && this.to) {\n                types.push(3);\n            } else {\n                types.push(0);\n                types.push(1);\n                types.push(2);\n                types.push(3);\n            }\n        }\n\n        types.sort();\n\n        return types;\n    }\n\n    /**\n     *  Returns true if this transaction is a legacy transaction (i.e.\n     *  ``type === 0``).\n     *\n     *  This provides a Type Guard that the related properties are\n     *  non-null.\n     */\n    isLegacy(): this is (Transaction & { type: 0, gasPrice: bigint }) {\n        return (this.type === 0);\n    }\n\n    /**\n     *  Returns true if this transaction is berlin hardform transaction (i.e.\n     *  ``type === 1``).\n     *\n     *  This provides a Type Guard that the related properties are\n     *  non-null.\n     */\n    isBerlin(): this is (Transaction & { type: 1, gasPrice: bigint, accessList: AccessList }) {\n        return (this.type === 1);\n    }\n\n    /**\n     *  Returns true if this transaction is london hardform transaction (i.e.\n     *  ``type === 2``).\n     *\n     *  This provides a Type Guard that the related properties are\n     *  non-null.\n     */\n    isLondon(): this is (Transaction & { type: 2, accessList: AccessList, maxFeePerGas: bigint, maxPriorityFeePerGas: bigint }) {\n        return (this.type === 2);\n    }\n\n    /**\n     *  Returns true if this transaction is an [[link-eip-4844]] BLOB\n     *  transaction.\n     *\n     *  This provides a Type Guard that the related properties are\n     *  non-null.\n     */\n    isCancun(): this is (Transaction & { type: 3, to: string, accessList: AccessList, maxFeePerGas: bigint, maxPriorityFeePerGas: bigint, maxFeePerBlobGas: bigint, blobVersionedHashes: Array<string> }) {\n        return (this.type === 3);\n    }\n\n    /**\n     *  Create a copy of this transaciton.\n     */\n    clone(): Transaction {\n        return Transaction.from(this);\n    }\n\n    /**\n     *  Return a JSON-friendly object.\n     */\n    toJSON(): any {\n        const s = (v: null | bigint) => {\n            if (v == null) { return null; }\n            return v.toString();\n        };\n\n        return {\n            type: this.type,\n            to: this.to,\n//            from: this.from,\n            data: this.data,\n            nonce: this.nonce,\n            gasLimit: s(this.gasLimit),\n            gasPrice: s(this.gasPrice),\n            maxPriorityFeePerGas: s(this.maxPriorityFeePerGas),\n            maxFeePerGas: s(this.maxFeePerGas),\n            value: s(this.value),\n            chainId: s(this.chainId),\n            sig: this.signature ? this.signature.toJSON(): null,\n            accessList: this.accessList\n        };\n    }\n\n    /**\n     *  Create a **Transaction** from a serialized transaction or a\n     *  Transaction-like object.\n     */\n    static from(tx?: string | TransactionLike<string>): Transaction {\n        if (tx == null) { return new Transaction(); }\n\n        if (typeof(tx) === \"string\") {\n            const payload = getBytes(tx);\n\n            if (payload[0] >= 0x7f) { // @TODO: > vs >= ??\n                return Transaction.from(_parseLegacy(payload));\n            }\n\n            switch(payload[0]) {\n                case 1: return Transaction.from(_parseEip2930(payload));\n                case 2: return Transaction.from(_parseEip1559(payload));\n                case 3: return Transaction.from(_parseEip4844(payload));\n                case 4: return Transaction.from(_parseEip7702(payload));\n            }\n            assert(false, \"unsupported transaction type\", \"UNSUPPORTED_OPERATION\", { operation: \"from\" });\n        }\n\n        const result = new Transaction();\n        if (tx.type != null) { result.type = tx.type; }\n        if (tx.to != null) { result.to = tx.to; }\n        if (tx.nonce != null) { result.nonce = tx.nonce; }\n        if (tx.gasLimit != null) { result.gasLimit = tx.gasLimit; }\n        if (tx.gasPrice != null) { result.gasPrice = tx.gasPrice; }\n        if (tx.maxPriorityFeePerGas != null) { result.maxPriorityFeePerGas = tx.maxPriorityFeePerGas; }\n        if (tx.maxFeePerGas != null) { result.maxFeePerGas = tx.maxFeePerGas; }\n        if (tx.maxFeePerBlobGas != null) { result.maxFeePerBlobGas = tx.maxFeePerBlobGas; }\n        if (tx.data != null) { result.data = tx.data; }\n        if (tx.value != null) { result.value = tx.value; }\n        if (tx.chainId != null) { result.chainId = tx.chainId; }\n        if (tx.signature != null) { result.signature = Signature.from(tx.signature); }\n        if (tx.accessList != null) { result.accessList = tx.accessList; }\n        if (tx.authorizationList != null) {\n            result.authorizationList = tx.authorizationList;\n        }\n\n        // This will get overwritten by blobs, if present\n        if (tx.blobVersionedHashes != null) { result.blobVersionedHashes = tx.blobVersionedHashes; }\n\n        // Make sure we assign the kzg before assigning blobs, which\n        // require the library in the event raw blob data is provided.\n        if (tx.kzg != null) { result.kzg = tx.kzg; }\n        if (tx.blobs != null) { result.blobs = tx.blobs; }\n\n        if (tx.hash != null) {\n            assertArgument(result.isSigned(), \"unsigned transaction cannot define '.hash'\", \"tx\", tx);\n            assertArgument(result.hash === tx.hash, \"hash mismatch\", \"tx\", tx);\n        }\n\n        if (tx.from != null) {\n            assertArgument(result.isSigned(), \"unsigned transaction cannot define '.from'\", \"tx\", tx);\n            assertArgument(result.from.toLowerCase() === (tx.from || \"\").toLowerCase(), \"from mismatch\", \"tx\", tx);\n        }\n\n        return result;\n    }\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SACIC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,UAAU,QACrC,oBAAoB;AAC3B,SACIC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EACrEC,MAAM,EAAEC,cAAc,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,YAAY,QACtE,mBAAmB;AAE1B,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,cAAc,QAAQ,cAAc;AAU7C,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AACtB,MAAME,KAAK,GAAGF,MAAM,CAAC,EAAE,CAAC;AACxB,MAAMG,KAAK,GAAGH,MAAM,CAAC,EAAE,CAAC;AACxB,MAAMI,KAAK,GAAGJ,MAAM,CAAC,EAAE,CAAC;AACxB,MAAMK,WAAW,GAAGL,MAAM,CAAC,oEAAoE,CAAC;AAEhG,MAAMM,SAAS,GAAG,IAAI,GAAG,EAAE;AAoK3B,SAASC,aAAaA,CAACC,GAAmB;EAEtC,MAAMC,mBAAmB,GAAIC,IAAgB,IAAI;IAE7C,IAAI,kBAAkB,IAAIF,GAAG,EAAE;MAC3B;MACA;MAEA,IAAI,qBAAqB,IAAIA,GAAG,IAAI,OAAOA,GAAG,CAACC,mBAAoB,KAAK,UAAU,EAAE;QAChF,OAAOvB,QAAQ,CAACsB,GAAG,CAACC,mBAAmB,CAACpB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC;;KAG9D,MAAM,IAAI,qBAAqB,IAAIF,GAAG,IAAI,OAAOA,GAAG,CAACC,mBAAoB,KAAK,UAAU,EAAE;MACvF;MAEA,OAAOvB,QAAQ,CAACsB,GAAG,CAACC,mBAAmB,CAACC,IAAI,CAAC,CAAC;;IAGlD;IACA,IAAI,qBAAqB,IAAIF,GAAG,IAAI,OAAOA,GAAG,CAACG,mBAAoB,KAAK,UAAU,EAAE;MAChF,OAAOzB,QAAQ,CAACsB,GAAG,CAACG,mBAAmB,CAACtB,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC;;IAG3DnB,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,KAAK,EAAEiB,GAAG,CAAC;EAChE,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAACF,IAAgB,EAAEG,UAAsB,KAAI;IAErE;IACA,IAAI,kBAAkB,IAAIL,GAAG,IAAI,OAAOA,GAAG,CAACM,gBAAiB,KAAK,UAAU,EAAE;MAC1E,OAAO5B,QAAQ,CAACsB,GAAG,CAACM,gBAAgB,CAACzB,OAAO,CAACqB,IAAI,CAAC,EAAErB,OAAO,CAACwB,UAAU,CAAC,CAAC,CAAC;;IAG7E;IACA,IAAI,qBAAqB,IAAIL,GAAG,IAAI,OAAOA,GAAG,CAACI,mBAAoB,KAAK,UAAU,EAAE;MAChF,OAAOJ,GAAG,CAACI,mBAAmB,CAACF,IAAI,EAAEG,UAAU,CAAC;;IAGpD;IACA,IAAI,qBAAqB,IAAIL,GAAG,IAAI,OAAOA,GAAG,CAACO,mBAAoB,KAAK,UAAU,EAAE;MAChF,OAAO7B,QAAQ,CAACsB,GAAG,CAACO,mBAAmB,CAAC1B,OAAO,CAACqB,IAAI,CAAC,EAAErB,OAAO,CAACwB,UAAU,CAAC,CAAC,CAAC;;IAGhFtB,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,KAAK,EAAEiB,GAAG,CAAC;EAChE,CAAC;EAED,OAAO;IAAEC,mBAAmB;IAAEG;EAAmB,CAAE;AACvD;AAEA,SAASI,gBAAgBA,CAACC,OAAe,EAAEC,IAAe;EACtD,IAAIC,SAAS,GAAGF,OAAO,CAACG,QAAQ,CAAC,EAAE,CAAC;EACpC,OAAOD,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;IAAEF,SAAS,GAAG,GAAG,GAAGA,SAAS;;EAC1DA,SAAS,IAAIvC,MAAM,CAACsC,IAAI,CAAC,CAACI,SAAS,CAAC,CAAC,CAAC;EACtC,OAAO,IAAI,GAAGH,SAAS;AAC3B;AAEA,SAASI,aAAaA,CAACC,KAAa;EAChC,IAAIA,KAAK,KAAK,IAAI,EAAE;IAAE,OAAO,IAAI;;EACjC,OAAO/C,UAAU,CAAC+C,KAAK,CAAC;AAC5B;AAEA,SAASC,gBAAgBA,CAACD,KAAU,EAAEE,KAAa;EAC/C,IAAI;IACA,OAAO9B,aAAa,CAAC4B,KAAK,CAAC;GAC9B,CAAC,OAAOG,KAAU,EAAE;IACjBpC,cAAc,CAAC,KAAK,EAAEoC,KAAK,CAACC,OAAO,EAAEF,KAAK,EAAEF,KAAK,CAAC;;AAE1D;AAEA,SAASK,uBAAuBA,CAACL,KAAU,EAAEE,KAAa;EACtD,IAAI;IACA,IAAI,CAACI,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE;MAAE,MAAM,IAAIQ,KAAK,CAAC,kCAAkC,CAAC;;IAChF,MAAMC,MAAM,GAAyB,EAAG;IACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACH,MAAM,EAAEa,CAAC,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAkBX,KAAK,CAACU,CAAC,CAAC;MACpC,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC,EAAE;QAAE,MAAM,IAAIH,KAAK,CAAC,iBAAkBE,CAAE,kBAAkB,CAAC;;MACnF,IAAIC,IAAI,CAACd,MAAM,KAAK,CAAC,EAAE;QAAE,MAAM,IAAIW,KAAK,CAAC,iBAAkBE,CAAE,iBAAiB,CAAC;;MAC/E,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE;QAAE,MAAM,IAAIH,KAAK,CAAC,iBAAkBE,CAAE,iBAAiB,CAAC;;MACtED,MAAM,CAACG,IAAI,CAAC;QACRC,OAAO,EAAUd,aAAa,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC;QACvCG,KAAK,EAAEC,UAAU,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;QACnCK,OAAO,EAAED,UAAU,CAACJ,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;QACvCM,SAAS,EAAE5D,SAAS,CAAC6D,IAAI,CAAC;UACtBC,OAAO,EAASC,YAAY,CAACT,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;UAChDU,CAAC,EAAElD,YAAY,CAACwC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UAC5BW,CAAC,EAAEnD,YAAY,CAACwC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;SAC9B;OACJ,CAAC;;IAEN,OAAOF,MAAM;GAChB,CAAC,OAAON,KAAU,EAAE;IACjBpC,cAAc,CAAC,KAAK,EAAEoC,KAAK,CAACC,OAAO,EAAEF,KAAK,EAAEF,KAAK,CAAC;;AAE1D;AAEA,SAASoB,YAAYA,CAACG,MAAc,EAAErB,KAAa;EAC/C,IAAIqB,MAAM,KAAK,IAAI,EAAE;IAAE,OAAO,CAAC;;EAC/B,OAAO3D,SAAS,CAAC2D,MAAM,EAAErB,KAAK,CAAC;AACnC;AAEA,SAASa,UAAUA,CAACQ,MAAc,EAAErB,KAAa;EAC7C,IAAIqB,MAAM,KAAK,IAAI,EAAE;IAAE,OAAOhD,IAAI;;EAClC,MAAMyB,KAAK,GAAGrC,SAAS,CAAC4D,MAAM,EAAErB,KAAK,CAAC;EACtCnC,cAAc,CAACiC,KAAK,IAAInB,WAAW,EAAE,yBAAyB,EAAEqB,KAAK,EAAEF,KAAK,CAAC;EAC7E,OAAOA,KAAK;AAChB;AAEA,SAASwB,YAAYA,CAACD,MAAoB,EAAEE,IAAY;EACpD,MAAMzB,KAAK,GAAGrC,SAAS,CAAC4D,MAAM,EAAE,OAAO,CAAC;EACxC,MAAMd,MAAM,GAAGvC,SAAS,CAAC8B,KAAK,CAAC;EAC/BjC,cAAc,CAAC0C,MAAM,CAACZ,MAAM,IAAI,EAAE,EAAE,iBAAiB,EAAE,MAAO4B,IAAK,EAAE,EAAEzB,KAAK,CAAC;EAC7E,OAAOS,MAAM;AACjB;AAEA,SAASiB,gBAAgBA,CAAC1B,KAAoB;EAC1C,OAAO5B,aAAa,CAAC4B,KAAK,CAAC,CAAC2B,GAAG,CAAEC,GAAG,IAAK,CAAEA,GAAG,CAACf,OAAO,EAAEe,GAAG,CAACC,WAAW,CAAE,CAAC;AAC9E;AAEA,SAASC,uBAAuBA,CAAC9B,KAA2B;EACxD,OAAOA,KAAK,CAAC2B,GAAG,CAAEI,CAAC,IAAI;IACnB,OAAO,CACHP,YAAY,CAACO,CAAC,CAACf,OAAO,EAAE,SAAS,CAAC,EAClCe,CAAC,CAAClB,OAAO,EACTW,YAAY,CAACO,CAAC,CAACjB,KAAK,EAAE,OAAO,CAAC,EAC9BU,YAAY,CAACO,CAAC,CAACd,SAAS,CAACE,OAAO,EAAE,SAAS,CAAC,EAC5CY,CAAC,CAACd,SAAS,CAACI,CAAC,EACbU,CAAC,CAACd,SAAS,CAACK,CAAC,CAChB;EACL,CAAC,CAAC;AACN;AAEA,SAASU,YAAYA,CAAChC,KAAoB,EAAEE,KAAa;EACrDnC,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE,WAAYE,KAAM,EAAE,EAAE,OAAO,EAAEF,KAAK,CAAC;EAC1E,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACH,MAAM,EAAEa,CAAC,EAAE,EAAE;IACnC3C,cAAc,CAACE,WAAW,CAAC+B,KAAK,CAACU,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,yBAAyB,EAAE,SAAUA,CAAE,GAAG,EAAEV,KAAK,CAACU,CAAC,CAAC,CAAC;;EAEnG,OAAOV,KAAK;AAChB;AAEA,SAASiC,YAAYA,CAACC,IAAgB;EAClC,MAAMC,MAAM,GAAQ3E,SAAS,CAAC0E,IAAI,CAAC;EAEnCnE,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,KAAKA,MAAM,CAACtC,MAAM,KAAK,CAAC,IAAIsC,MAAM,CAACtC,MAAM,KAAK,CAAC,CAAC,EAChF,4CAA4C,EAAE,MAAM,EAAEqC,IAAI,CAAC;EAE/D,MAAME,EAAE,GAAoB;IACxBC,IAAI,EAAM,CAAC;IACXvB,KAAK,EAAKM,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IAC1CG,QAAQ,EAAEvB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IAC3CI,QAAQ,EAAExB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IAC3CK,EAAE,EAAQzC,aAAa,CAACoC,MAAM,CAAC,CAAC,CAAC,CAAC;IAClCnC,KAAK,EAAKe,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACxCD,IAAI,EAAMrE,OAAO,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC5BnB,OAAO,EAAGzC;GACb;EAED;EACA,IAAI4D,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOuC,EAAE;;EAEpC,MAAMK,CAAC,GAAG1B,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;EACpC,MAAMd,CAAC,GAAGN,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;EACpC,MAAMb,CAAC,GAAGP,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;EAEpC,IAAId,CAAC,KAAK9C,IAAI,IAAI+C,CAAC,KAAK/C,IAAI,EAAE;IAC1B;IACA6D,EAAE,CAACpB,OAAO,GAAGyB,CAAC;GAEjB,MAAM;IAEH;IACA,IAAIzB,OAAO,GAAG,CAACyB,CAAC,GAAG7D,KAAK,IAAIH,IAAI;IAChC,IAAIuC,OAAO,GAAGzC,IAAI,EAAE;MAAEyC,OAAO,GAAGzC,IAAI;;IACpC6D,EAAE,CAACpB,OAAO,GAAGA,OAAO;IAEpB;IACAjD,cAAc,CAACiD,OAAO,KAAKzC,IAAI,IAAKkE,CAAC,KAAK/D,KAAK,IAAI+D,CAAC,KAAK9D,KAAM,EAAE,wBAAwB,EAAE,GAAG,EAAEwD,MAAM,CAAC,CAAC,CAAC,CAAC;IAE1GC,EAAE,CAACnB,SAAS,GAAG5D,SAAS,CAAC6D,IAAI,CAAC;MAC1BG,CAAC,EAAElD,YAAY,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9Bb,CAAC,EAAEnD,YAAY,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9BM;KACH,CAAC;IAEF;;EAGJ,OAAOL,EAAE;AACb;AAEA,SAASM,gBAAgBA,CAACN,EAAe,EAAEO,GAAqB;EAC5D,MAAMR,MAAM,GAAe,CACvBX,YAAY,CAACY,EAAE,CAACtB,KAAK,EAAE,OAAO,CAAC,EAC/BU,YAAY,CAACY,EAAE,CAACE,QAAQ,IAAI,CAAC,EAAE,UAAU,CAAC,EAC1Cd,YAAY,CAACY,EAAE,CAACG,QAAQ,EAAE,UAAU,CAAC,EACpCH,EAAE,CAACI,EAAE,IAAI,IAAI,EACdhB,YAAY,CAACY,EAAE,CAACpC,KAAK,EAAE,OAAO,CAAC,EAC/BoC,EAAE,CAACF,IAAI,CACV;EAED,IAAIlB,OAAO,GAAGzC,IAAI;EAClB,IAAI6D,EAAE,CAACpB,OAAO,IAAIzC,IAAI,EAAE;IACpB;IACAyC,OAAO,GAAGrD,SAAS,CAACyE,EAAE,CAACpB,OAAO,EAAE,YAAY,CAAC;IAE7C;IACA;IACAjD,cAAc,CAAC,CAAC4E,GAAG,IAAIA,GAAG,CAACC,QAAQ,IAAI,IAAI,IAAID,GAAG,CAACE,aAAa,KAAK7B,OAAO,EACvE,2BAA2B,EAAE,KAAK,EAAE2B,GAAG,CAAC;GAEhD,MAAM,IAAIP,EAAE,CAACnB,SAAS,EAAE;IACrB;IACA,MAAM6B,MAAM,GAAGV,EAAE,CAACnB,SAAS,CAAC4B,aAAa;IACzC,IAAIC,MAAM,IAAI,IAAI,EAAE;MAAE9B,OAAO,GAAG8B,MAAM;;;EAG1C;EACA,IAAI,CAACH,GAAG,EAAE;IACN;IACA,IAAI3B,OAAO,KAAKzC,IAAI,EAAE;MAClB4D,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAAC8C,OAAO,CAAC,CAAC;MAC/BmB,MAAM,CAACvB,IAAI,CAAC,IAAI,CAAC;MACjBuB,MAAM,CAACvB,IAAI,CAAC,IAAI,CAAC;;IAGrB,OAAOnD,SAAS,CAAC0E,MAAM,CAAC;;EAG5B;EACA;EACA;EAEA;EACA,IAAIM,CAAC,GAAGjE,MAAM,CAAC,EAAE,GAAGmE,GAAG,CAACxB,OAAO,CAAC;EAChC,IAAIH,OAAO,KAAKzC,IAAI,EAAE;IAClBkE,CAAC,GAAGpF,SAAS,CAAC0F,WAAW,CAAC/B,OAAO,EAAE2B,GAAG,CAACF,CAAC,CAAC;GAC5C,MAAM,IAAIjE,MAAM,CAACmE,GAAG,CAACF,CAAC,CAAC,KAAKA,CAAC,EAAE;IAC5B1E,cAAc,CAAC,KAAK,EAAE,2BAA2B,EAAE,KAAK,EAAE4E,GAAG,CAAC;;EAGlE;EACAR,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACuE,CAAC,CAAC,CAAC;EACzBN,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACtB,CAAC,CAAC,CAAC;EAC7Bc,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACrB,CAAC,CAAC,CAAC;EAE7B,OAAO7D,SAAS,CAAC0E,MAAM,CAAC;AAC5B;AAEA,SAASa,kBAAkBA,CAACZ,EAAmB,EAAED,MAAqB;EAClE,IAAIhB,OAAe;EACnB,IAAI;IACAA,OAAO,GAAGC,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;IAC5C,IAAIhB,OAAO,KAAK,CAAC,IAAIA,OAAO,KAAK,CAAC,EAAE;MAAE,MAAM,IAAIX,KAAK,CAAC,aAAa,CAAC;;GACvE,CAAC,OAAOL,KAAK,EAAE;IACZpC,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAEoE,MAAM,CAAC,CAAC,CAAC,CAAC;;EAGlE,MAAMd,CAAC,GAAGlD,YAAY,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACrC,MAAMb,CAAC,GAAGnD,YAAY,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAErC,MAAMlB,SAAS,GAAG5D,SAAS,CAAC6D,IAAI,CAAC;IAAEG,CAAC;IAAEC,CAAC;IAAEH;EAAO,CAAE,CAAC;EACnDiB,EAAE,CAACnB,SAAS,GAAGA,SAAS;AAC5B;AAEA,SAASgC,aAAaA,CAACf,IAAgB;EACnC,MAAMC,MAAM,GAAQ3E,SAAS,CAACE,QAAQ,CAACwE,IAAI,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EAEtDnF,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,KAAKA,MAAM,CAACtC,MAAM,KAAK,CAAC,IAAIsC,MAAM,CAACtC,MAAM,KAAK,EAAE,CAAC,EACjF,6CAA6C,EAAE,MAAM,EAAEhC,OAAO,CAACqE,IAAI,CAAC,CAAC;EAEzE,MAAME,EAAE,GAAoB;IACxBC,IAAI,EAAmB,CAAC;IACxBrB,OAAO,EAAgBD,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;IACvDrB,KAAK,EAAkBM,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACvDgB,oBAAoB,EAAGpC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACpEiB,YAAY,EAAWrC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC;IAC5DG,QAAQ,EAAe,IAAI;IAC3BC,QAAQ,EAAexB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IACxDK,EAAE,EAAqBzC,aAAa,CAACoC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/CnC,KAAK,EAAkBe,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACrDD,IAAI,EAAmBrE,OAAO,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC;IACzCkB,UAAU,EAAapD,gBAAgB,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY;GAClE;EAED;EACA,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOuC,EAAE;;EAEpC;EAEAY,kBAAkB,CAACZ,EAAE,EAAED,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;EAEvC,OAAOd,EAAE;AACb;AAEA,SAASkB,iBAAiBA,CAAClB,EAAe,EAAEO,GAAqB;EAC7D,MAAMR,MAAM,GAAe,CACvBX,YAAY,CAACY,EAAE,CAACpB,OAAO,EAAE,SAAS,CAAC,EACnCQ,YAAY,CAACY,EAAE,CAACtB,KAAK,EAAE,OAAO,CAAC,EAC/BU,YAAY,CAACY,EAAE,CAACe,oBAAoB,IAAI,CAAC,EAAE,sBAAsB,CAAC,EAClE3B,YAAY,CAACY,EAAE,CAACgB,YAAY,IAAI,CAAC,EAAE,cAAc,CAAC,EAClD5B,YAAY,CAACY,EAAE,CAACG,QAAQ,EAAE,UAAU,CAAC,EACpCH,EAAE,CAACI,EAAE,IAAI,IAAI,EACdhB,YAAY,CAACY,EAAE,CAACpC,KAAK,EAAE,OAAO,CAAC,EAC/BoC,EAAE,CAACF,IAAI,EACPR,gBAAgB,CAACU,EAAE,CAACiB,UAAU,IAAI,EAAG,CAAC,CACzC;EAED,IAAIV,GAAG,EAAE;IACLR,MAAM,CAACvB,IAAI,CAACY,YAAY,CAACmB,GAAG,CAACxB,OAAO,EAAE,SAAS,CAAC,CAAC;IACjDgB,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACtB,CAAC,CAAC,CAAC;IAC7Bc,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACrB,CAAC,CAAC,CAAC;;EAGjC,OAAO/D,MAAM,CAAC,CAAE,MAAM,EAAEE,SAAS,CAAC0E,MAAM,CAAC,CAAC,CAAC;AAC/C;AAEA,SAASoB,aAAaA,CAACrB,IAAgB;EACnC,MAAMC,MAAM,GAAQ3E,SAAS,CAACE,QAAQ,CAACwE,IAAI,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EAEtDnF,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,KAAKA,MAAM,CAACtC,MAAM,KAAK,CAAC,IAAIsC,MAAM,CAACtC,MAAM,KAAK,EAAE,CAAC,EACjF,6CAA6C,EAAE,MAAM,EAAEhC,OAAO,CAACqE,IAAI,CAAC,CAAC;EAEzE,MAAME,EAAE,GAAoB;IACxBC,IAAI,EAAQ,CAAC;IACbrB,OAAO,EAAKD,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;IAC5CrB,KAAK,EAAOM,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IAC5CG,QAAQ,EAAIvB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IAC7CI,QAAQ,EAAIxB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IAC7CK,EAAE,EAAUzC,aAAa,CAACoC,MAAM,CAAC,CAAC,CAAC,CAAC;IACpCnC,KAAK,EAAOe,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IAC1CD,IAAI,EAAQrE,OAAO,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9BkB,UAAU,EAAEpD,gBAAgB,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY;GACvD;EAED;EACA,IAAIA,MAAM,CAACtC,MAAM,KAAK,CAAC,EAAE;IAAE,OAAOuC,EAAE;;EAEpC;EAEAY,kBAAkB,CAACZ,EAAE,EAAED,MAAM,CAACe,KAAK,CAAC,CAAC,CAAC,CAAC;EAEvC,OAAOd,EAAE;AACb;AAEA,SAASoB,iBAAiBA,CAACpB,EAAe,EAAEO,GAAqB;EAC7D,MAAMR,MAAM,GAAQ,CAChBX,YAAY,CAACY,EAAE,CAACpB,OAAO,EAAE,SAAS,CAAC,EACnCQ,YAAY,CAACY,EAAE,CAACtB,KAAK,EAAE,OAAO,CAAC,EAC/BU,YAAY,CAACY,EAAE,CAACE,QAAQ,IAAI,CAAC,EAAE,UAAU,CAAC,EAC1Cd,YAAY,CAACY,EAAE,CAACG,QAAQ,EAAE,UAAU,CAAC,EACpCH,EAAE,CAACI,EAAE,IAAI,IAAI,EACdhB,YAAY,CAACY,EAAE,CAACpC,KAAK,EAAE,OAAO,CAAC,EAC/BoC,EAAE,CAACF,IAAI,EACPR,gBAAgB,CAACU,EAAE,CAACiB,UAAU,IAAI,EAAG,CAAC,CACzC;EAED,IAAIV,GAAG,EAAE;IACLR,MAAM,CAACvB,IAAI,CAACY,YAAY,CAACmB,GAAG,CAACxB,OAAO,EAAE,eAAe,CAAC,CAAC;IACvDgB,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACtB,CAAC,CAAC,CAAC;IAC7Bc,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACrB,CAAC,CAAC,CAAC;;EAGjC,OAAO/D,MAAM,CAAC,CAAE,MAAM,EAAEE,SAAS,CAAC0E,MAAM,CAAC,CAAC,CAAC;AAC/C;AAEA,SAASsB,aAAaA,CAACvB,IAAgB;EACnC,IAAIC,MAAM,GAAQ3E,SAAS,CAACE,QAAQ,CAACwE,IAAI,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EAEpD,IAAIQ,QAAQ,GAAG,GAAG;EAElB,IAAIC,KAAK,GAAuB,IAAI;EAEpC;EACA,IAAIxB,MAAM,CAACtC,MAAM,KAAK,CAAC,IAAIS,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IACjDuB,QAAQ,GAAG,oBAAoB;IAC/B,MAAME,MAAM,GAAGzB,MAAM,CAAC,CAAC,CAAC;MAAE0B,QAAQ,GAAG1B,MAAM,CAAC,CAAC,CAAC;MAAE2B,OAAO,GAAG3B,MAAM,CAAC,CAAC,CAAC;IACnEpE,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACqD,MAAM,CAAC,EAAE,4CAA4C,EAAE,WAAW,EAAEA,MAAM,CAAC;IACxG7F,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACsD,QAAQ,CAAC,EAAE,kDAAkD,EAAE,WAAW,EAAEA,QAAQ,CAAC;IAClH9F,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACuD,OAAO,CAAC,EAAE,6CAA6C,EAAE,WAAW,EAAEA,OAAO,CAAC;IAC3G/F,cAAc,CAAC6F,MAAM,CAAC/D,MAAM,KAAKgE,QAAQ,CAAChE,MAAM,EAAE,2DAA2D,EAAE,QAAQ,EAAEsC,MAAM,CAAC;IAChIpE,cAAc,CAAC6F,MAAM,CAAC/D,MAAM,KAAKiE,OAAO,CAACjE,MAAM,EAAE,sDAAsD,EAAE,QAAQ,EAAEsC,MAAM,CAAC;IAE1HwB,KAAK,GAAG,EAAG;IACX,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,MAAM,CAAC,CAAC,CAAC,CAACtC,MAAM,EAAEa,CAAC,EAAE,EAAE;MACvCiD,KAAK,CAAC/C,IAAI,CAAC;QACPsB,IAAI,EAAE0B,MAAM,CAAClD,CAAC,CAAC;QACfrB,UAAU,EAAEwE,QAAQ,CAACnD,CAAC,CAAC;QACvBqD,KAAK,EAAED,OAAO,CAACpD,CAAC;OACnB,CAAC;;IAGNyB,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;;EAGtBpE,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,KAAKA,MAAM,CAACtC,MAAM,KAAK,EAAE,IAAIsC,MAAM,CAACtC,MAAM,KAAK,EAAE,CAAC,EAClF,6CAA8C6D,QAAS,EAAE,EAAE,MAAM,EAAE7F,OAAO,CAACqE,IAAI,CAAC,CAAC;EAErF,MAAME,EAAE,GAAoB;IACxBC,IAAI,EAAmB,CAAC;IACxBrB,OAAO,EAAgBD,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;IACvDrB,KAAK,EAAkBM,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACvDgB,oBAAoB,EAAGpC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACpEiB,YAAY,EAAWrC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC;IAC5DG,QAAQ,EAAe,IAAI;IAC3BC,QAAQ,EAAexB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IACxDK,EAAE,EAAqBzC,aAAa,CAACoC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/CnC,KAAK,EAAkBe,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACrDD,IAAI,EAAmBrE,OAAO,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC;IACzCkB,UAAU,EAAapD,gBAAgB,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;IAChE6B,gBAAgB,EAAOjD,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC;IAChE8B,mBAAmB,EAAI9B,MAAM,CAAC,EAAE;GACnC;EAED,IAAIwB,KAAK,EAAE;IAAEvB,EAAE,CAACuB,KAAK,GAAGA,KAAK;;EAE7B5F,cAAc,CAACqE,EAAE,CAACI,EAAE,IAAI,IAAI,EAAE,yCAA0CkB,QAAS,EAAE,EAAE,MAAM,EAAExB,IAAI,CAAC;EAElGnE,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC6B,EAAE,CAAC6B,mBAAmB,CAAC,EAAE,+CAA+C,EAAE,MAAM,EAAE/B,IAAI,CAAC;EACpH,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,EAAE,CAAC6B,mBAAmB,CAACpE,MAAM,EAAEa,CAAC,EAAE,EAAE;IACpD3C,cAAc,CAACE,WAAW,CAACmE,EAAE,CAAC6B,mBAAmB,CAACvD,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,sCAAuCA,CAAE,qBAAqB,EAAE,MAAM,EAAEwB,IAAI,CAAC;;EAG5I;EACA,IAAIC,MAAM,CAACtC,MAAM,KAAK,EAAE,EAAE;IAAE,OAAOuC,EAAE;;EAErC;EACA;EACA;EAEAY,kBAAkB,CAACZ,EAAE,EAAED,MAAM,CAACe,KAAK,CAAC,EAAE,CAAC,CAAC;EAExC,OAAOd,EAAE;AACb;AAEA,SAAS8B,iBAAiBA,CAAC9B,EAAe,EAAEO,GAAqB,EAAEgB,KAAyB;EACxF,MAAMxB,MAAM,GAAe,CACvBX,YAAY,CAACY,EAAE,CAACpB,OAAO,EAAE,SAAS,CAAC,EACnCQ,YAAY,CAACY,EAAE,CAACtB,KAAK,EAAE,OAAO,CAAC,EAC/BU,YAAY,CAACY,EAAE,CAACe,oBAAoB,IAAI,CAAC,EAAE,sBAAsB,CAAC,EAClE3B,YAAY,CAACY,EAAE,CAACgB,YAAY,IAAI,CAAC,EAAE,cAAc,CAAC,EAClD5B,YAAY,CAACY,EAAE,CAACG,QAAQ,EAAE,UAAU,CAAC,EACpCH,EAAE,CAACI,EAAE,IAAItF,WAAW,EACrBsE,YAAY,CAACY,EAAE,CAACpC,KAAK,EAAE,OAAO,CAAC,EAC/BoC,EAAE,CAACF,IAAI,EACPR,gBAAgB,CAACU,EAAE,CAACiB,UAAU,IAAI,EAAG,CAAC,EACtC7B,YAAY,CAACY,EAAE,CAAC4B,gBAAgB,IAAI,CAAC,EAAE,kBAAkB,CAAC,EAC1DhC,YAAY,CAACI,EAAE,CAAC6B,mBAAmB,IAAI,EAAG,EAAE,qBAAqB,CAAC,CACrE;EAED,IAAItB,GAAG,EAAE;IACLR,MAAM,CAACvB,IAAI,CAACY,YAAY,CAACmB,GAAG,CAACxB,OAAO,EAAE,SAAS,CAAC,CAAC;IACjDgB,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACtB,CAAC,CAAC,CAAC;IAC7Bc,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACrB,CAAC,CAAC,CAAC;IAE7B;IACA,IAAIqC,KAAK,EAAE;MACP,OAAOpG,MAAM,CAAC,CACV,MAAM,EACNE,SAAS,CAAC,CACN0E,MAAM,EACNwB,KAAK,CAAChC,GAAG,CAAEwC,CAAC,IAAKA,CAAC,CAACjC,IAAI,CAAC,EACxByB,KAAK,CAAChC,GAAG,CAAEwC,CAAC,IAAKA,CAAC,CAAC9E,UAAU,CAAC,EAC9BsE,KAAK,CAAChC,GAAG,CAAEwC,CAAC,IAAKA,CAAC,CAACJ,KAAK,CAAC,CAC5B,CAAC,CACL,CAAC;;;EAKV,OAAOxG,MAAM,CAAC,CAAE,MAAM,EAAEE,SAAS,CAAC0E,MAAM,CAAC,CAAC,CAAC;AAC/C;AAEA,SAASiC,aAAaA,CAAClC,IAAgB;EACnC,MAAMC,MAAM,GAAQ3E,SAAS,CAACE,QAAQ,CAACwE,IAAI,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EAEtDnF,cAAc,CAACuC,KAAK,CAACC,OAAO,CAAC4B,MAAM,CAAC,KAAKA,MAAM,CAACtC,MAAM,KAAK,EAAE,IAAIsC,MAAM,CAACtC,MAAM,KAAK,EAAE,CAAC,EAClF,6CAA6C,EAAE,MAAM,EAAEhC,OAAO,CAACqE,IAAI,CAAC,CAAC;EAEzE,MAAME,EAAE,GAAoB;IACxBC,IAAI,EAAmB,CAAC;IACxBrB,OAAO,EAAgBD,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC;IACvDrB,KAAK,EAAkBM,YAAY,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACvDgB,oBAAoB,EAAGpC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,sBAAsB,CAAC;IACpEiB,YAAY,EAAWrC,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,cAAc,CAAC;IAC5DG,QAAQ,EAAe,IAAI;IAC3BC,QAAQ,EAAexB,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC;IACxDK,EAAE,EAAqBzC,aAAa,CAACoC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC/CnC,KAAK,EAAkBe,UAAU,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;IACrDD,IAAI,EAAmBrE,OAAO,CAACsE,MAAM,CAAC,CAAC,CAAC,CAAC;IACzCkB,UAAU,EAAapD,gBAAgB,CAACkC,MAAM,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;IAChEkC,iBAAiB,EAAMhE,uBAAuB,CAAC8B,MAAM,CAAC,CAAC,CAAC,EAAE,mBAAmB;GAChF;EAED;EACA,IAAIA,MAAM,CAACtC,MAAM,KAAK,EAAE,EAAE;IAAE,OAAOuC,EAAE;;EAErCY,kBAAkB,CAACZ,EAAE,EAAED,MAAM,CAACe,KAAK,CAAC,EAAE,CAAC,CAAC;EAExC,OAAOd,EAAE;AACb;AAEA,SAASkC,iBAAiBA,CAAClC,EAAe,EAAEO,GAAqB;EAC7D,MAAMR,MAAM,GAAe,CACvBX,YAAY,CAACY,EAAE,CAACpB,OAAO,EAAE,SAAS,CAAC,EACnCQ,YAAY,CAACY,EAAE,CAACtB,KAAK,EAAE,OAAO,CAAC,EAC/BU,YAAY,CAACY,EAAE,CAACe,oBAAoB,IAAI,CAAC,EAAE,sBAAsB,CAAC,EAClE3B,YAAY,CAACY,EAAE,CAACgB,YAAY,IAAI,CAAC,EAAE,cAAc,CAAC,EAClD5B,YAAY,CAACY,EAAE,CAACG,QAAQ,EAAE,UAAU,CAAC,EACpCH,EAAE,CAACI,EAAE,IAAI,IAAI,EACdhB,YAAY,CAACY,EAAE,CAACpC,KAAK,EAAE,OAAO,CAAC,EAC/BoC,EAAE,CAACF,IAAI,EACPR,gBAAgB,CAACU,EAAE,CAACiB,UAAU,IAAI,EAAG,CAAC,EACtCvB,uBAAuB,CAACM,EAAE,CAACiC,iBAAiB,IAAI,EAAG,CAAC,CACvD;EAED,IAAI1B,GAAG,EAAE;IACLR,MAAM,CAACvB,IAAI,CAACY,YAAY,CAACmB,GAAG,CAACxB,OAAO,EAAE,SAAS,CAAC,CAAC;IACjDgB,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACtB,CAAC,CAAC,CAAC;IAC7Bc,MAAM,CAACvB,IAAI,CAAC1C,SAAS,CAACyE,GAAG,CAACrB,CAAC,CAAC,CAAC;;EAGjC,OAAO/D,MAAM,CAAC,CAAE,MAAM,EAAEE,SAAS,CAAC0E,MAAM,CAAC,CAAC,CAAC;AAC/C;AAEA;;;;;;;;;;;;;AAaA,OAAM,MAAOoC,WAAW;EACpB,CAAAlC,IAAK;EACL,CAAAG,EAAG;EACH,CAAAN,IAAK;EACL,CAAApB,KAAM;EACN,CAAAyB,QAAS;EACT,CAAAD,QAAS;EACT,CAAAa,oBAAqB;EACrB,CAAAC,YAAa;EACb,CAAApD,KAAM;EACN,CAAAgB,OAAQ;EACR,CAAA2B,GAAI;EACJ,CAAAU,UAAW;EACX,CAAAW,gBAAiB;EACjB,CAAAC,mBAAoB;EACpB,CAAAjF,GAAI;EACJ,CAAA2E,KAAM;EACN,CAAAa,KAAM;EAEN;;;;;;EAMA,IAAInC,IAAIA,CAAA;IAAoB,OAAO,IAAI,CAAC,CAAAA,IAAK;EAAE;EAC/C,IAAIA,IAAIA,CAACrC,KAA6B;IAClC,QAAQA,KAAK;MACT,KAAK,IAAI;QACL,IAAI,CAAC,CAAAqC,IAAK,GAAG,IAAI;QACjB;MACJ,KAAK,CAAC;MAAE,KAAK,QAAQ;QACjB,IAAI,CAAC,CAAAA,IAAK,GAAG,CAAC;QACd;MACJ,KAAK,CAAC;MAAE,KAAK,QAAQ;MAAE,KAAK,UAAU;QAClC,IAAI,CAAC,CAAAA,IAAK,GAAG,CAAC;QACd;MACJ,KAAK,CAAC;MAAE,KAAK,QAAQ;MAAE,KAAK,UAAU;QAClC,IAAI,CAAC,CAAAA,IAAK,GAAG,CAAC;QACd;MACJ,KAAK,CAAC;MAAE,KAAK,QAAQ;MAAE,KAAK,UAAU;QAClC,IAAI,CAAC,CAAAA,IAAK,GAAG,CAAC;QACd;MACJ,KAAK,CAAC;MAAE,KAAK,QAAQ;MAAE,KAAK,UAAU;QAClC,IAAI,CAAC,CAAAA,IAAK,GAAG,CAAC;QACd;MACJ;QACItE,cAAc,CAAC,KAAK,EAAE,8BAA8B,EAAE,MAAM,EAAEiC,KAAK,CAAC;;EAEhF;EAEA;;;EAGA,IAAI0D,QAAQA,CAAA;IACR,QAAQ,IAAI,CAACrB,IAAI;MACb,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB,KAAK,CAAC;QAAE,OAAO,UAAU;MACzB,KAAK,CAAC;QAAE,OAAO,UAAU;MACzB,KAAK,CAAC;QAAE,OAAO,UAAU;MACzB,KAAK,CAAC;QAAE,OAAO,UAAU;;IAG7B,OAAO,IAAI;EACf;EAEA;;;;EAIA,IAAIG,EAAEA,CAAA;IACF,MAAMxC,KAAK,GAAG,IAAI,CAAC,CAAAwC,EAAG;IACtB,IAAIxC,KAAK,IAAI,IAAI,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,EAAE;MAAE,OAAOnF,WAAW;;IAC1D,OAAO8C,KAAK;EAChB;EACA,IAAIwC,EAAEA,CAACxC,KAAoB;IACvB,IAAI,CAAC,CAAAwC,EAAG,GAAIxC,KAAK,IAAI,IAAI,GAAI,IAAI,GAAE/C,UAAU,CAAC+C,KAAK,CAAC;EACxD;EAEA;;;EAGA,IAAIc,KAAKA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,KAAM;EAAE;EAC1C,IAAIA,KAAKA,CAACd,KAAmB;IAAI,IAAI,CAAC,CAAAc,KAAM,GAAGlD,SAAS,CAACoC,KAAK,EAAE,OAAO,CAAC;EAAE;EAE1E;;;EAGA,IAAIuC,QAAQA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,QAAS;EAAE;EAChD,IAAIA,QAAQA,CAACvC,KAAmB;IAAI,IAAI,CAAC,CAAAuC,QAAS,GAAG5E,SAAS,CAACqC,KAAK,CAAC;EAAE;EAEvE;;;;;;EAMA,IAAIsC,QAAQA,CAAA;IACR,MAAMtC,KAAK,GAAG,IAAI,CAAC,CAAAsC,QAAS;IAC5B,IAAItC,KAAK,IAAI,IAAI,KAAK,IAAI,CAACqC,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,CAAC,EAAE;MAAE,OAAO9D,IAAI;;IACxE,OAAOyB,KAAK;EAChB;EACA,IAAIsC,QAAQA,CAACtC,KAA0B;IACnC,IAAI,CAAC,CAAAsC,QAAS,GAAItC,KAAK,IAAI,IAAI,GAAI,IAAI,GAAErC,SAAS,CAACqC,KAAK,EAAE,UAAU,CAAC;EACzE;EAEA;;;;EAIA,IAAImD,oBAAoBA,CAAA;IACpB,MAAMnD,KAAK,GAAG,IAAI,CAAC,CAAAmD,oBAAqB;IACxC,IAAInD,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;QAAE,OAAO9D,IAAI;;MACrD,OAAO,IAAI;;IAEf,OAAOyB,KAAK;EAChB;EACA,IAAImD,oBAAoBA,CAACnD,KAA0B;IAC/C,IAAI,CAAC,CAAAmD,oBAAqB,GAAInD,KAAK,IAAI,IAAI,GAAI,IAAI,GAAErC,SAAS,CAACqC,KAAK,EAAE,sBAAsB,CAAC;EACjG;EAEA;;;;EAIA,IAAIoD,YAAYA,CAAA;IACZ,MAAMpD,KAAK,GAAG,IAAI,CAAC,CAAAoD,YAAa;IAChC,IAAIpD,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;QAAE,OAAO9D,IAAI;;MACrD,OAAO,IAAI;;IAEf,OAAOyB,KAAK;EAChB;EACA,IAAIoD,YAAYA,CAACpD,KAA0B;IACvC,IAAI,CAAC,CAAAoD,YAAa,GAAIpD,KAAK,IAAI,IAAI,GAAI,IAAI,GAAErC,SAAS,CAACqC,KAAK,EAAE,cAAc,CAAC;EACjF;EAEA;;;;EAIA,IAAIkC,IAAIA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,IAAK;EAAE;EACxC,IAAIA,IAAIA,CAAClC,KAAgB;IAAI,IAAI,CAAC,CAAAkC,IAAK,GAAGrE,OAAO,CAACmC,KAAK,CAAC;EAAE;EAE1D;;;EAGA,IAAIA,KAAKA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,KAAM;EAAE;EAC1C,IAAIA,KAAKA,CAACA,KAAmB;IACzB,IAAI,CAAC,CAAAA,KAAM,GAAGrC,SAAS,CAACqC,KAAK,EAAE,OAAO,CAAC;EAC3C;EAEA;;;EAGA,IAAIgB,OAAOA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,OAAQ;EAAE;EAC9C,IAAIA,OAAOA,CAAChB,KAAmB;IAAI,IAAI,CAAC,CAAAgB,OAAQ,GAAGrD,SAAS,CAACqC,KAAK,CAAC;EAAE;EAErE;;;EAGA,IAAIiB,SAASA,CAAA;IAAuB,OAAO,IAAI,CAAC,CAAA0B,GAAI,IAAI,IAAI;EAAE;EAC9D,IAAI1B,SAASA,CAACjB,KAA2B;IACrC,IAAI,CAAC,CAAA2C,GAAI,GAAI3C,KAAK,IAAI,IAAI,GAAI,IAAI,GAAE3C,SAAS,CAAC6D,IAAI,CAAClB,KAAK,CAAC;EAC7D;EAEA;;;;;;EAMA,IAAIqD,UAAUA,CAAA;IACV,MAAMrD,KAAK,GAAG,IAAI,CAAC,CAAAqD,UAAW,IAAI,IAAI;IACtC,IAAIrD,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;QACvD;QACA;QACA,OAAO,EAAG;;MAEd,OAAO,IAAI;;IAEf,OAAOrC,KAAK;EAChB;EACA,IAAIqD,UAAUA,CAACrD,KAA2B;IACtC,IAAI,CAAC,CAAAqD,UAAW,GAAIrD,KAAK,IAAI,IAAI,GAAI,IAAI,GAAE5B,aAAa,CAAC4B,KAAK,CAAC;EACnE;EAEA,IAAIqE,iBAAiBA,CAAA;IACjB,MAAMrE,KAAK,GAAG,IAAI,CAAC,CAAAwE,KAAM,IAAI,IAAI;IACjC,IAAIxE,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,EAAE;QACjB;QACA;QACA,OAAO,EAAG;;;IAGlB,OAAOrC,KAAK;EAChB;EACA,IAAIqE,iBAAiBA,CAACG,KAAsC;IACxD,IAAI,CAAC,CAAAA,KAAM,GAAIA,KAAK,IAAI,IAAI,GAAI,IAAI,GAAEA,KAAK,CAAC7C,GAAG,CAAEI,CAAC,IAChD1D,gBAAgB,CAAC0D,CAAC,CAAC,CAAC;EAC1B;EAEA;;;EAGA,IAAIiC,gBAAgBA,CAAA;IAChB,MAAMhE,KAAK,GAAG,IAAI,CAAC,CAAAgE,gBAAiB;IACpC,IAAIhE,KAAK,IAAI,IAAI,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,EAAE;MAAE,OAAO9D,IAAI;;IACnD,OAAOyB,KAAK;EAChB;EACA,IAAIgE,gBAAgBA,CAAChE,KAA0B;IAC3C,IAAI,CAAC,CAAAgE,gBAAiB,GAAIhE,KAAK,IAAI,IAAI,GAAI,IAAI,GAAErC,SAAS,CAACqC,KAAK,EAAE,kBAAkB,CAAC;EACzF;EAEA;;;EAGA,IAAIiE,mBAAmBA,CAAA;IACnB;IACA;IACA,IAAIjE,KAAK,GAAG,IAAI,CAAC,CAAAiE,mBAAoB;IACrC,IAAIjE,KAAK,IAAI,IAAI,IAAI,IAAI,CAACqC,IAAI,KAAK,CAAC,EAAE;MAAE,OAAO,EAAG;;IAClD,OAAOrC,KAAK;EAChB;EACA,IAAIiE,mBAAmBA,CAACjE,KAA2B;IAC/C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACfjC,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,EAAE,sCAAsC,EAAE,OAAO,EAAEA,KAAK,CAAC;MAC5FA,KAAK,GAAGA,KAAK,CAACkD,KAAK,EAAE;MACrB,KAAK,IAAIxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACH,MAAM,EAAEa,CAAC,EAAE,EAAE;QACnC3C,cAAc,CAACE,WAAW,CAAC+B,KAAK,CAACU,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,2BAA2B,EAAE,SAAUA,CAAE,GAAG,EAAEV,KAAK,CAACU,CAAC,CAAC,CAAC;;;IAGzG,IAAI,CAAC,CAAAuD,mBAAoB,GAAGjE,KAAK;EACrC;EAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA,IAAI2D,KAAKA,CAAA;IACL,IAAI,IAAI,CAAC,CAAAA,KAAM,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACtC,OAAO,IAAI,CAAC,CAAAA,KAAM,CAAChC,GAAG,CAAEwC,CAAC,IAAKM,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEP,CAAC,CAAC,CAAC;EACxD;EACA,IAAIR,KAAKA,CAACgB,MAA8B;IACpC,IAAIA,MAAM,IAAI,IAAI,EAAE;MAChB,IAAI,CAAC,CAAAhB,KAAM,GAAG,IAAI;MAClB;;IAGJ,MAAMA,KAAK,GAAgB,EAAG;IAC9B,MAAMiB,eAAe,GAAkB,EAAG;IAC1C,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiE,MAAM,CAAC9E,MAAM,EAAEa,CAAC,EAAE,EAAE;MACpC,MAAMxB,IAAI,GAAGyF,MAAM,CAACjE,CAAC,CAAC;MAEtB,IAAI1C,WAAW,CAACkB,IAAI,CAAC,EAAE;QACnBpB,MAAM,CAAC,IAAI,CAAC,CAAAkB,GAAI,EAAE,0CAA0C,EAAE,uBAAuB,EAAE;UACnF6F,SAAS,EAAE;SACd,CAAC;QAEF,IAAI3C,IAAI,GAAGxE,QAAQ,CAACwB,IAAI,CAAC;QACzBnB,cAAc,CAACmE,IAAI,CAACrC,MAAM,IAAIf,SAAS,EAAE,mBAAmB,EAAE,SAAU4B,CAAE,GAAG,EAAExB,IAAI,CAAC;QAEpF;QACA,IAAIgD,IAAI,CAACrC,MAAM,KAAKf,SAAS,EAAE;UAC3B,MAAMgG,MAAM,GAAG,IAAIC,UAAU,CAACjG,SAAS,CAAC;UACxCgG,MAAM,CAAClD,GAAG,CAACM,IAAI,CAAC;UAChBA,IAAI,GAAG4C,MAAM;;QAGjB,MAAME,MAAM,GAAG,IAAI,CAAC,CAAAhG,GAAI,CAACC,mBAAmB,CAACiD,IAAI,CAAC;QAClD,MAAM6B,KAAK,GAAGlG,OAAO,CAAC,IAAI,CAAC,CAAAmB,GAAI,CAACI,mBAAmB,CAAC8C,IAAI,EAAE8C,MAAM,CAAC,CAAC;QAElErB,KAAK,CAAC/C,IAAI,CAAC;UACPsB,IAAI,EAAErE,OAAO,CAACqE,IAAI,CAAC;UACnB7C,UAAU,EAAExB,OAAO,CAACmH,MAAM,CAAC;UAC3BjB;SACH,CAAC;QACFa,eAAe,CAAChE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,EAAEwF,MAAM,CAAC,CAAC;OAEpD,MAAM;QACH,MAAMA,MAAM,GAAGnH,OAAO,CAACqB,IAAI,CAACG,UAAU,CAAC;QACvCsE,KAAK,CAAC/C,IAAI,CAAC;UACPsB,IAAI,EAAErE,OAAO,CAACqB,IAAI,CAACgD,IAAI,CAAC;UACxB7C,UAAU,EAAE2F,MAAM;UAClBjB,KAAK,EAAElG,OAAO,CAACqB,IAAI,CAAC6E,KAAK;SAC5B,CAAC;QACFa,eAAe,CAAChE,IAAI,CAACpB,gBAAgB,CAAC,CAAC,EAAEwF,MAAM,CAAC,CAAC;;;IAIzD,IAAI,CAAC,CAAArB,KAAM,GAAGA,KAAK;IACnB,IAAI,CAAC,CAAAM,mBAAoB,GAAGW,eAAe;EAC/C;EAEA,IAAI5F,GAAGA,CAAA;IAAwB,OAAO,IAAI,CAAC,CAAAA,GAAI;EAAE;EACjD,IAAIA,GAAGA,CAACA,GAA0B;IAC9B,IAAIA,GAAG,IAAI,IAAI,EAAE;MACb,IAAI,CAAC,CAAAA,GAAI,GAAG,IAAI;KACnB,MAAM;MACH,IAAI,CAAC,CAAAA,GAAI,GAAGD,aAAa,CAACC,GAAG,CAAC;;EAEtC;EAEA;;;EAGAiG,YAAA;IACI,IAAI,CAAC,CAAA5C,IAAK,GAAG,IAAI;IACjB,IAAI,CAAC,CAAAG,EAAG,GAAG,IAAI;IACf,IAAI,CAAC,CAAA1B,KAAM,GAAG,CAAC;IACf,IAAI,CAAC,CAAAyB,QAAS,GAAGhE,IAAI;IACrB,IAAI,CAAC,CAAA+D,QAAS,GAAG,IAAI;IACrB,IAAI,CAAC,CAAAa,oBAAqB,GAAG,IAAI;IACjC,IAAI,CAAC,CAAAC,YAAa,GAAG,IAAI;IACzB,IAAI,CAAC,CAAAlB,IAAK,GAAG,IAAI;IACjB,IAAI,CAAC,CAAAlC,KAAM,GAAGzB,IAAI;IAClB,IAAI,CAAC,CAAAyC,OAAQ,GAAGzC,IAAI;IACpB,IAAI,CAAC,CAAAoE,GAAI,GAAG,IAAI;IAChB,IAAI,CAAC,CAAAU,UAAW,GAAG,IAAI;IACvB,IAAI,CAAC,CAAAW,gBAAiB,GAAG,IAAI;IAC7B,IAAI,CAAC,CAAAC,mBAAoB,GAAG,IAAI;IAChC,IAAI,CAAC,CAAAjF,GAAI,GAAG,IAAI;IAChB,IAAI,CAAC,CAAA2E,KAAM,GAAG,IAAI;IAClB,IAAI,CAAC,CAAAa,KAAM,GAAG,IAAI;EACtB;EAEA;;;EAGA,IAAI9E,IAAIA,CAAA;IACJ,IAAI,IAAI,CAACuB,SAAS,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACzC,OAAO9D,SAAS,CAAC,IAAI,CAAC,CAAA+H,aAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;EACtD;EAEA;;;;;;EAMA,IAAIC,YAAYA,CAAA;IACZ,OAAOhI,SAAS,CAAC,IAAI,CAACiI,kBAAkB,CAAC;EAC7C;EAEA;;;EAGA,IAAIlE,IAAIA,CAAA;IACJ,IAAI,IAAI,CAACD,SAAS,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACzC,OAAO3C,cAAc,CAAC,IAAI,CAAC6G,YAAY,EAAE,IAAI,CAAClE,SAAS,CAAC;EAC5D;EAEA;;;EAGA,IAAIoE,aAAaA,CAAA;IACb,IAAI,IAAI,CAACpE,SAAS,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACzC,OAAO3D,UAAU,CAACgI,gBAAgB,CAAC,IAAI,CAACH,YAAY,EAAE,IAAI,CAAClE,SAAS,CAAC;EACzE;EAEA;;;;;;EAMAsE,QAAQA,CAAA;IACJ,OAAO,IAAI,CAACtE,SAAS,IAAI,IAAI;EACjC;EAEA,CAAAiE,aAAcM,CAACC,MAAe,EAAEC,OAAgB;IAC5C5H,MAAM,CAAC,CAAC2H,MAAM,IAAI,IAAI,CAACxE,SAAS,IAAI,IAAI,EAAE,4EAA4E,EAAE,uBAAuB,EAAE;MAAE4D,SAAS,EAAE;IAAa,CAAC,CAAC;IAE7K,MAAMlC,GAAG,GAAG8C,MAAM,GAAG,IAAI,CAACxE,SAAS,GAAE,IAAI;IACzC,QAAQ,IAAI,CAAC0E,SAAS,EAAE;MACpB,KAAK,CAAC;QACF,OAAOjD,gBAAgB,CAAC,IAAI,EAAEC,GAAG,CAAC;MACtC,KAAK,CAAC;QACF,OAAOa,iBAAiB,CAAC,IAAI,EAAEb,GAAG,CAAC;MACvC,KAAK,CAAC;QACF,OAAOW,iBAAiB,CAAC,IAAI,EAAEX,GAAG,CAAC;MACvC,KAAK,CAAC;QACF,OAAOuB,iBAAiB,CAAC,IAAI,EAAEvB,GAAG,EAAE+C,OAAO,GAAG,IAAI,CAAC/B,KAAK,GAAE,IAAI,CAAC;MACnE,KAAK,CAAC;QACF,OAAOW,iBAAiB,CAAC,IAAI,EAAE3B,GAAG,CAAC;;IAG3C7E,MAAM,CAAC,KAAK,EAAE,8BAA8B,EAAE,uBAAuB,EAAE;MAAE+G,SAAS,EAAE;IAAa,CAAE,CAAC;EACxG;EAEA;;;;;;EAMA,IAAIe,UAAUA,CAAA;IACV,OAAO,IAAI,CAAC,CAAAV,aAAc,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1C;EAEA;;;;;;EAMA,IAAIE,kBAAkBA,CAAA;IAClB,OAAO,IAAI,CAAC,CAAAF,aAAc,CAAC,KAAK,EAAE,KAAK,CAAC;EAC5C;EAEA;;;;EAIAS,SAASA,CAAA;IACL,MAAME,KAAK,GAAG,IAAI,CAACC,UAAU,EAAE;IAE/B;IACA,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;MAAE,OAAO,CAAC;;IAErC;IACA,OAAgBF,KAAK,CAACG,GAAG,EAAE;EAC/B;EAEA;;;;EAIAF,UAAUA,CAAA;IAEN;IACA,MAAMG,WAAW,GAAG,IAAI,CAAC3D,QAAQ,IAAI,IAAI;IACzC,MAAM4D,MAAM,GAAI,IAAI,CAAC9C,YAAY,IAAI,IAAI,IAAI,IAAI,CAACD,oBAAoB,IAAI,IAAK;IAC/E,MAAMgD,aAAa,GAAI,IAAI,CAAC9C,UAAU,IAAI,IAAK;IAC/C,MAAM+C,OAAO,GAAI,IAAI,CAAC,CAAApC,gBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,CAAAC,mBAAqB;IAE7E;IACA;IACA;IAEA,IAAI,IAAI,CAACb,YAAY,IAAI,IAAI,IAAI,IAAI,CAACD,oBAAoB,IAAI,IAAI,EAAE;MAChErF,MAAM,CAAC,IAAI,CAACsF,YAAY,IAAI,IAAI,CAACD,oBAAoB,EAAE,wCAAwC,EAAE,UAAU,EAAE;QAAEnD,KAAK,EAAE;MAAI,CAAE,CAAC;;IAGjI;IACA;IACA;IAEAlC,MAAM,CAAC,CAACoI,MAAM,IAAK,IAAI,CAAC7D,IAAI,KAAK,CAAC,IAAI,IAAI,CAACA,IAAI,KAAK,CAAE,EAAE,mEAAmE,EAAE,UAAU,EAAE;MAAErC,KAAK,EAAE;IAAI,CAAE,CAAC;IACzJlC,MAAM,CAAC,IAAI,CAACuE,IAAI,KAAK,CAAC,IAAI,CAAC8D,aAAa,EAAE,2CAA2C,EAAE,UAAU,EAAE;MAAEnG,KAAK,EAAE;IAAI,CAAE,CAAC;IAEnH,MAAM6F,KAAK,GAAkB,EAAG;IAEhC;IACA,IAAI,IAAI,CAACxD,IAAI,IAAI,IAAI,EAAE;MACnBwD,KAAK,CAACjF,IAAI,CAAC,IAAI,CAACyB,IAAI,CAAC;KAExB,MAAM;MACH,IAAI,IAAI,CAACgC,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACxE,MAAM,EAAE;QACzDgG,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;OAChB,MAAM,IAAIsF,MAAM,EAAE;QACfL,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;OAChB,MAAM,IAAIqF,WAAW,EAAE;QACpBJ,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;QACb,IAAI,CAACuF,aAAa,EAAE;UAAEN,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;;OACtC,MAAM,IAAIuF,aAAa,EAAE;QACtBN,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;QACbiF,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;OAChB,MAAM,IAAIwF,OAAO,IAAI,IAAI,CAAC5D,EAAE,EAAE;QAC3BqD,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;OAChB,MAAM;QACHiF,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;QACbiF,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;QACbiF,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;QACbiF,KAAK,CAACjF,IAAI,CAAC,CAAC,CAAC;;;IAIrBiF,KAAK,CAACQ,IAAI,EAAE;IAEZ,OAAOR,KAAK;EAChB;EAEA;;;;;;;EAOAS,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACjE,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;;;;EAOAkE,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAAClE,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;;;;EAOAmE,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACnE,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;;;;EAOAoE,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACpE,IAAI,KAAK,CAAC;EAC3B;EAEA;;;EAGAqE,KAAKA,CAAA;IACD,OAAOnC,WAAW,CAACrD,IAAI,CAAC,IAAI,CAAC;EACjC;EAEA;;;EAGAyF,MAAMA,CAAA;IACF,MAAMrF,CAAC,GAAImB,CAAgB,IAAI;MAC3B,IAAIA,CAAC,IAAI,IAAI,EAAE;QAAE,OAAO,IAAI;;MAC5B,OAAOA,CAAC,CAAC7C,QAAQ,EAAE;IACvB,CAAC;IAED,OAAO;MACHyC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfG,EAAE,EAAE,IAAI,CAACA,EAAE;MACvB;MACYN,IAAI,EAAE,IAAI,CAACA,IAAI;MACfpB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjByB,QAAQ,EAAEjB,CAAC,CAAC,IAAI,CAACiB,QAAQ,CAAC;MAC1BD,QAAQ,EAAEhB,CAAC,CAAC,IAAI,CAACgB,QAAQ,CAAC;MAC1Ba,oBAAoB,EAAE7B,CAAC,CAAC,IAAI,CAAC6B,oBAAoB,CAAC;MAClDC,YAAY,EAAE9B,CAAC,CAAC,IAAI,CAAC8B,YAAY,CAAC;MAClCpD,KAAK,EAAEsB,CAAC,CAAC,IAAI,CAACtB,KAAK,CAAC;MACpBgB,OAAO,EAAEM,CAAC,CAAC,IAAI,CAACN,OAAO,CAAC;MACxB2B,GAAG,EAAE,IAAI,CAAC1B,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC0F,MAAM,EAAE,GAAE,IAAI;MACnDtD,UAAU,EAAE,IAAI,CAACA;KACpB;EACL;EAEA;;;;EAIA,OAAOnC,IAAIA,CAACkB,EAAqC;IAC7C,IAAIA,EAAE,IAAI,IAAI,EAAE;MAAE,OAAO,IAAImC,WAAW,EAAE;;IAE1C,IAAI,OAAOnC,EAAG,KAAK,QAAQ,EAAE;MACzB,MAAMwE,OAAO,GAAGlJ,QAAQ,CAAC0E,EAAE,CAAC;MAE5B,IAAIwE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QAAE;QACtB,OAAOrC,WAAW,CAACrD,IAAI,CAACe,YAAY,CAAC2E,OAAO,CAAC,CAAC;;MAGlD,QAAOA,OAAO,CAAC,CAAC,CAAC;QACb,KAAK,CAAC;UAAE,OAAOrC,WAAW,CAACrD,IAAI,CAACqC,aAAa,CAACqD,OAAO,CAAC,CAAC;QACvD,KAAK,CAAC;UAAE,OAAOrC,WAAW,CAACrD,IAAI,CAAC+B,aAAa,CAAC2D,OAAO,CAAC,CAAC;QACvD,KAAK,CAAC;UAAE,OAAOrC,WAAW,CAACrD,IAAI,CAACuC,aAAa,CAACmD,OAAO,CAAC,CAAC;QACvD,KAAK,CAAC;UAAE,OAAOrC,WAAW,CAACrD,IAAI,CAACkD,aAAa,CAACwC,OAAO,CAAC,CAAC;;MAE3D9I,MAAM,CAAC,KAAK,EAAE,8BAA8B,EAAE,uBAAuB,EAAE;QAAE+G,SAAS,EAAE;MAAM,CAAE,CAAC;;IAGjG,MAAMpE,MAAM,GAAG,IAAI8D,WAAW,EAAE;IAChC,IAAInC,EAAE,CAACC,IAAI,IAAI,IAAI,EAAE;MAAE5B,MAAM,CAAC4B,IAAI,GAAGD,EAAE,CAACC,IAAI;;IAC5C,IAAID,EAAE,CAACI,EAAE,IAAI,IAAI,EAAE;MAAE/B,MAAM,CAAC+B,EAAE,GAAGJ,EAAE,CAACI,EAAE;;IACtC,IAAIJ,EAAE,CAACtB,KAAK,IAAI,IAAI,EAAE;MAAEL,MAAM,CAACK,KAAK,GAAGsB,EAAE,CAACtB,KAAK;;IAC/C,IAAIsB,EAAE,CAACG,QAAQ,IAAI,IAAI,EAAE;MAAE9B,MAAM,CAAC8B,QAAQ,GAAGH,EAAE,CAACG,QAAQ;;IACxD,IAAIH,EAAE,CAACE,QAAQ,IAAI,IAAI,EAAE;MAAE7B,MAAM,CAAC6B,QAAQ,GAAGF,EAAE,CAACE,QAAQ;;IACxD,IAAIF,EAAE,CAACe,oBAAoB,IAAI,IAAI,EAAE;MAAE1C,MAAM,CAAC0C,oBAAoB,GAAGf,EAAE,CAACe,oBAAoB;;IAC5F,IAAIf,EAAE,CAACgB,YAAY,IAAI,IAAI,EAAE;MAAE3C,MAAM,CAAC2C,YAAY,GAAGhB,EAAE,CAACgB,YAAY;;IACpE,IAAIhB,EAAE,CAAC4B,gBAAgB,IAAI,IAAI,EAAE;MAAEvD,MAAM,CAACuD,gBAAgB,GAAG5B,EAAE,CAAC4B,gBAAgB;;IAChF,IAAI5B,EAAE,CAACF,IAAI,IAAI,IAAI,EAAE;MAAEzB,MAAM,CAACyB,IAAI,GAAGE,EAAE,CAACF,IAAI;;IAC5C,IAAIE,EAAE,CAACpC,KAAK,IAAI,IAAI,EAAE;MAAES,MAAM,CAACT,KAAK,GAAGoC,EAAE,CAACpC,KAAK;;IAC/C,IAAIoC,EAAE,CAACpB,OAAO,IAAI,IAAI,EAAE;MAAEP,MAAM,CAACO,OAAO,GAAGoB,EAAE,CAACpB,OAAO;;IACrD,IAAIoB,EAAE,CAACnB,SAAS,IAAI,IAAI,EAAE;MAAER,MAAM,CAACQ,SAAS,GAAG5D,SAAS,CAAC6D,IAAI,CAACkB,EAAE,CAACnB,SAAS,CAAC;;IAC3E,IAAImB,EAAE,CAACiB,UAAU,IAAI,IAAI,EAAE;MAAE5C,MAAM,CAAC4C,UAAU,GAAGjB,EAAE,CAACiB,UAAU;;IAC9D,IAAIjB,EAAE,CAACiC,iBAAiB,IAAI,IAAI,EAAE;MAC9B5D,MAAM,CAAC4D,iBAAiB,GAAGjC,EAAE,CAACiC,iBAAiB;;IAGnD;IACA,IAAIjC,EAAE,CAAC6B,mBAAmB,IAAI,IAAI,EAAE;MAAExD,MAAM,CAACwD,mBAAmB,GAAG7B,EAAE,CAAC6B,mBAAmB;;IAEzF;IACA;IACA,IAAI7B,EAAE,CAACpD,GAAG,IAAI,IAAI,EAAE;MAAEyB,MAAM,CAACzB,GAAG,GAAGoD,EAAE,CAACpD,GAAG;;IACzC,IAAIoD,EAAE,CAACuB,KAAK,IAAI,IAAI,EAAE;MAAElD,MAAM,CAACkD,KAAK,GAAGvB,EAAE,CAACuB,KAAK;;IAE/C,IAAIvB,EAAE,CAAC1C,IAAI,IAAI,IAAI,EAAE;MACjB3B,cAAc,CAAC0C,MAAM,CAAC8E,QAAQ,EAAE,EAAE,4CAA4C,EAAE,IAAI,EAAEnD,EAAE,CAAC;MACzFrE,cAAc,CAAC0C,MAAM,CAACf,IAAI,KAAK0C,EAAE,CAAC1C,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE0C,EAAE,CAAC;;IAGtE,IAAIA,EAAE,CAAClB,IAAI,IAAI,IAAI,EAAE;MACjBnD,cAAc,CAAC0C,MAAM,CAAC8E,QAAQ,EAAE,EAAE,4CAA4C,EAAE,IAAI,EAAEnD,EAAE,CAAC;MACzFrE,cAAc,CAAC0C,MAAM,CAACS,IAAI,CAAC2F,WAAW,EAAE,KAAK,CAACzE,EAAE,CAAClB,IAAI,IAAI,EAAE,EAAE2F,WAAW,EAAE,EAAE,eAAe,EAAE,IAAI,EAAEzE,EAAE,CAAC;;IAG1G,OAAO3B,MAAM;EACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}