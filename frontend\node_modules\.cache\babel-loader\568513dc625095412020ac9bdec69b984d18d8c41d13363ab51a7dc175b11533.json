{"ast": null, "code": "import { scrypt as _nobleSync, scryptAsync as _nobleAsync } from \"@noble/hashes/scrypt\";\nimport { getBytes, hexlify as H } from \"../utils/index.js\";\nlet lockedSync = false,\n  lockedAsync = false;\nconst _scryptAsync = async function (passwd, salt, N, r, p, dk<PERSON>en, onProgress) {\n  return await _nobleAsync(passwd, salt, {\n    N,\n    r,\n    p,\n    dkLen,\n    onProgress\n  });\n};\nconst _scryptSync = function (passwd, salt, N, r, p, dkLen) {\n  return _nobleSync(passwd, salt, {\n    N,\n    r,\n    p,\n    dkLen\n  });\n};\nlet __scryptAsync = _scryptAsync;\nlet __scryptSync = _scryptSync;\n/**\n *  The [[link-wiki-scrypt]] uses a memory and cpu hard method of\n *  derivation to increase the resource cost to brute-force a password\n *  for a given key.\n *\n *  This means this algorithm is intentionally slow, and can be tuned to\n *  become slower. As computation and memory speed improve over time,\n *  increasing the difficulty maintains the cost of an attacker.\n *\n *  For example, if a target time of 5 seconds is used, a legitimate user\n *  which knows their password requires only 5 seconds to unlock their\n *  account. A 6 character password has 68 billion possibilities, which\n *  would require an attacker to invest over 10,000 years of CPU time. This\n *  is of course a crude example (as password generally aren't random),\n *  but demonstrates to value of imposing large costs to decryption.\n *\n *  For this reason, if building a UI which involved decrypting or\n *  encrypting datsa using scrypt, it is recommended to use a\n *  [[ProgressCallback]] (as event short periods can seem lik an eternity\n *  if the UI freezes). Including the phrase //\"decrypting\"// in the UI\n *  can also help, assuring the user their waiting is for a good reason.\n *\n *  @_docloc: api/crypto:Passwords\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the scrypt\n *    scrypt(passwordBytes, salt, 1024, 8, 1, 16)\n *    //_result:\n */\nexport async function scrypt(_passwd, _salt, N, r, p, dkLen, progress) {\n  const passwd = getBytes(_passwd, \"passwd\");\n  const salt = getBytes(_salt, \"salt\");\n  return H(await __scryptAsync(passwd, salt, N, r, p, dkLen, progress));\n}\nscrypt._ = _scryptAsync;\nscrypt.lock = function () {\n  lockedAsync = true;\n};\nscrypt.register = function (func) {\n  if (lockedAsync) {\n    throw new Error(\"scrypt is locked\");\n  }\n  __scryptAsync = func;\n};\nObject.freeze(scrypt);\n/**\n *  Provides a synchronous variant of [[scrypt]].\n *\n *  This will completely lock up and freeze the UI in a browser and will\n *  prevent any event loop from progressing. For this reason, it is\n *  preferred to use the [async variant](scrypt).\n *\n *  @_docloc: api/crypto:Passwords\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the scrypt\n *    scryptSync(passwordBytes, salt, 1024, 8, 1, 16)\n *    //_result:\n */\nexport function scryptSync(_passwd, _salt, N, r, p, dkLen) {\n  const passwd = getBytes(_passwd, \"passwd\");\n  const salt = getBytes(_salt, \"salt\");\n  return H(__scryptSync(passwd, salt, N, r, p, dkLen));\n}\nscryptSync._ = _scryptSync;\nscryptSync.lock = function () {\n  lockedSync = true;\n};\nscryptSync.register = function (func) {\n  if (lockedSync) {\n    throw new Error(\"scryptSync is locked\");\n  }\n  __scryptSync = func;\n};\nObject.freeze(scryptSync);", "map": {"version": 3, "names": ["scrypt", "_nobleSync", "scryptAsync", "_noble<PERSON><PERSON>", "getBytes", "hexlify", "H", "lockedSync", "lockedAsync", "_scryptAsync", "passwd", "salt", "N", "r", "p", "dkLen", "onProgress", "_scryptSync", "__scryptAsync", "__scryptSync", "_passwd", "_salt", "progress", "_", "lock", "register", "func", "Error", "Object", "freeze", "scryptSync"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\scrypt.ts"], "sourcesContent": ["import { scrypt as _nobleSync, scryptAsync as _nobleAsync } from \"@noble/hashes/scrypt\";\n\nimport { getBytes, hexlify as H } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n/**\n *  A callback during long-running operations to update any\n *  UI or provide programatic access to the progress.\n *\n *  The %%percent%% is a value between ``0`` and ``1``.\n *\n *  @_docloc: api/crypto:Passwords\n */\nexport type ProgressCallback = (percent: number) => void;\n\n\nlet lockedSync = false, lockedAsync = false;\n\nconst _scryptAsync = async function(passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number, onProgress?: ProgressCallback) {\n    return await _nobleAsync(passwd, salt, { N, r, p, dkLen, onProgress });\n}\nconst _scryptSync = function(passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number) {\n    return _nobleSync(passwd, salt, { N, r, p, dkLen });\n}\n\nlet __scryptAsync: (passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number, onProgress?: ProgressCallback) => Promise<BytesLike> = _scryptAsync;\nlet __scryptSync: (passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number) => BytesLike = _scryptSync\n\n\n/**\n *  The [[link-wiki-scrypt]] uses a memory and cpu hard method of\n *  derivation to increase the resource cost to brute-force a password\n *  for a given key.\n *\n *  This means this algorithm is intentionally slow, and can be tuned to\n *  become slower. As computation and memory speed improve over time,\n *  increasing the difficulty maintains the cost of an attacker.\n *\n *  For example, if a target time of 5 seconds is used, a legitimate user\n *  which knows their password requires only 5 seconds to unlock their\n *  account. A 6 character password has 68 billion possibilities, which\n *  would require an attacker to invest over 10,000 years of CPU time. This\n *  is of course a crude example (as password generally aren't random),\n *  but demonstrates to value of imposing large costs to decryption.\n *\n *  For this reason, if building a UI which involved decrypting or\n *  encrypting datsa using scrypt, it is recommended to use a\n *  [[ProgressCallback]] (as event short periods can seem lik an eternity\n *  if the UI freezes). Including the phrase //\"decrypting\"// in the UI\n *  can also help, assuring the user their waiting is for a good reason.\n *\n *  @_docloc: api/crypto:Passwords\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the scrypt\n *    scrypt(passwordBytes, salt, 1024, 8, 1, 16)\n *    //_result:\n */\nexport async function scrypt(_passwd: BytesLike, _salt: BytesLike, N: number, r: number, p: number, dkLen: number, progress?: ProgressCallback): Promise<string> {\n    const passwd = getBytes(_passwd, \"passwd\");\n    const salt = getBytes(_salt, \"salt\");\n    return H(await __scryptAsync(passwd, salt, N, r, p, dkLen, progress));\n}\nscrypt._ = _scryptAsync;\nscrypt.lock = function(): void { lockedAsync = true; }\nscrypt.register = function(func: (passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number, progress?: ProgressCallback) => Promise<BytesLike>) {\n    if (lockedAsync) { throw new Error(\"scrypt is locked\"); }\n    __scryptAsync = func;\n}\nObject.freeze(scrypt);\n\n/**\n *  Provides a synchronous variant of [[scrypt]].\n *\n *  This will completely lock up and freeze the UI in a browser and will\n *  prevent any event loop from progressing. For this reason, it is\n *  preferred to use the [async variant](scrypt).\n *\n *  @_docloc: api/crypto:Passwords\n *\n *  @example:\n *    // The password must be converted to bytes, and it is generally\n *    // best practices to ensure the string has been normalized. Many\n *    // formats explicitly indicate the normalization form to use.\n *    password = \"hello\"\n *    passwordBytes = toUtf8Bytes(password, \"NFKC\")\n *\n *    salt = id(\"some-salt\")\n *\n *    // Compute the scrypt\n *    scryptSync(passwordBytes, salt, 1024, 8, 1, 16)\n *    //_result:\n */\nexport function scryptSync(_passwd: BytesLike, _salt: BytesLike, N: number, r: number, p: number, dkLen: number): string {\n    const passwd = getBytes(_passwd, \"passwd\");\n    const salt = getBytes(_salt, \"salt\");\n    return H(__scryptSync(passwd, salt, N, r, p, dkLen));\n}\nscryptSync._ = _scryptSync;\nscryptSync.lock = function(): void { lockedSync = true; }\nscryptSync.register = function(func: (passwd: Uint8Array, salt: Uint8Array, N: number, r: number, p: number, dkLen: number) => BytesLike) {\n    if (lockedSync) { throw new Error(\"scryptSync is locked\"); }\n    __scryptSync = func;\n}\nObject.freeze(scryptSync);\n"], "mappings": "AAAA,SAASA,MAAM,IAAIC,UAAU,EAAEC,WAAW,IAAIC,WAAW,QAAQ,sBAAsB;AAEvF,SAASC,QAAQ,EAAEC,OAAO,IAAIC,CAAC,QAAQ,mBAAmB;AAe1D,IAAIC,UAAU,GAAG,KAAK;EAAEC,WAAW,GAAG,KAAK;AAE3C,MAAMC,YAAY,GAAG,eAAAA,CAAeC,MAAkB,EAAEC,IAAgB,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,KAAa,EAAEC,UAA6B;EACnJ,OAAO,MAAMb,WAAW,CAACO,MAAM,EAAEC,IAAI,EAAE;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,KAAK;IAAEC;EAAU,CAAE,CAAC;AAC1E,CAAC;AACD,MAAMC,WAAW,GAAG,SAAAA,CAASP,MAAkB,EAAEC,IAAgB,EAAEC,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,KAAa;EAC7G,OAAOd,UAAU,CAACS,MAAM,EAAEC,IAAI,EAAE;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC;EAAK,CAAE,CAAC;AACvD,CAAC;AAED,IAAIG,aAAa,GAAgJT,YAAY;AAC7K,IAAIU,YAAY,GAAwGF,WAAW;AAGnI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,OAAO,eAAejB,MAAMA,CAACoB,OAAkB,EAAEC,KAAgB,EAAET,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,KAAa,EAAEO,QAA2B;EAC1I,MAAMZ,MAAM,GAAGN,QAAQ,CAACgB,OAAO,EAAE,QAAQ,CAAC;EAC1C,MAAMT,IAAI,GAAGP,QAAQ,CAACiB,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOf,CAAC,CAAC,MAAMY,aAAa,CAACR,MAAM,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEO,QAAQ,CAAC,CAAC;AACzE;AACAtB,MAAM,CAACuB,CAAC,GAAGd,YAAY;AACvBT,MAAM,CAACwB,IAAI,GAAG;EAAmBhB,WAAW,GAAG,IAAI;AAAE,CAAC;AACtDR,MAAM,CAACyB,QAAQ,GAAG,UAASC,IAA+I;EACtK,IAAIlB,WAAW,EAAE;IAAE,MAAM,IAAImB,KAAK,CAAC,kBAAkB,CAAC;;EACtDT,aAAa,GAAGQ,IAAI;AACxB,CAAC;AACDE,MAAM,CAACC,MAAM,CAAC7B,MAAM,CAAC;AAErB;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAM,SAAU8B,UAAUA,CAACV,OAAkB,EAAEC,KAAgB,EAAET,CAAS,EAAEC,CAAS,EAAEC,CAAS,EAAEC,KAAa;EAC3G,MAAML,MAAM,GAAGN,QAAQ,CAACgB,OAAO,EAAE,QAAQ,CAAC;EAC1C,MAAMT,IAAI,GAAGP,QAAQ,CAACiB,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOf,CAAC,CAACa,YAAY,CAACT,MAAM,EAAEC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,CAAC;AACxD;AACAe,UAAU,CAACP,CAAC,GAAGN,WAAW;AAC1Ba,UAAU,CAACN,IAAI,GAAG;EAAmBjB,UAAU,GAAG,IAAI;AAAE,CAAC;AACzDuB,UAAU,CAACL,QAAQ,GAAG,UAASC,IAAyG;EACpI,IAAInB,UAAU,EAAE;IAAE,MAAM,IAAIoB,KAAK,CAAC,sBAAsB,CAAC;;EACzDR,YAAY,GAAGO,IAAI;AACvB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACC,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}