const { ethers } = require('ethers');
require('dotenv').config();

// Contract ABIs (simplified for key functions)
const CQT_TOKEN_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function distributeReward(address player, uint256 amount, string reason)",
  "function burnFrom(address from, uint256 amount)",
  "function totalSupply() view returns (uint256)",
  "function decimals() view returns (uint8)"
];

const GAME_TREASURY_ABI = [
  "function distributePvEReward(address player)",
  "function distributePvPReward(address player)",
  "function distributeDailyQuestReward(address player)",
  "function upgradeHero(address player)",
  "function unlockSkill(address player)",
  "function enterTournament(address player)",
  "function distributeTournamentPrize(address winner, uint256 prizeAmount)",
  "function getPlayerStats(address player) view returns (uint256, uint256, uint256, uint256, uint256)"
];

const STAKING_POOL_ABI = [
  "function stake(uint256 amount)",
  "function unstake()",
  "function claimRewards()",
  "function getStakeInfo(address user) view returns (uint256, uint256, uint256, uint256, bool, bool, uint256)",
  "function hasStakingBoost(address user) view returns (bool)",
  "function calculatePendingRewards(address user) view returns (uint256)"
];

class BlockchainService {
  constructor() {
    this.provider = null;
    this.wallet = null;
    this.cqtToken = null;
    this.gameTreasury = null;
    this.stakingPool = null;
    this.initialized = false;
  }

  async initialize() {
    try {
      // Initialize provider
      this.provider = new ethers.JsonRpcProvider(process.env.RPC_URL || 'http://127.0.0.1:8545');
      
      // Initialize wallet
      if (!process.env.PRIVATE_KEY) {
        throw new Error('PRIVATE_KEY not set in environment variables');
      }
      this.wallet = new ethers.Wallet(process.env.PRIVATE_KEY, this.provider);
      
      // Initialize contracts
      if (process.env.CQT_TOKEN_ADDRESS) {
        this.cqtToken = new ethers.Contract(
          process.env.CQT_TOKEN_ADDRESS,
          CQT_TOKEN_ABI,
          this.wallet
        );
      }
      
      if (process.env.GAME_TREASURY_ADDRESS) {
        this.gameTreasury = new ethers.Contract(
          process.env.GAME_TREASURY_ADDRESS,
          GAME_TREASURY_ABI,
          this.wallet
        );
      }
      
      if (process.env.STAKING_POOL_ADDRESS) {
        this.stakingPool = new ethers.Contract(
          process.env.STAKING_POOL_ADDRESS,
          STAKING_POOL_ABI,
          this.wallet
        );
      }

      // Test connection
      const network = await this.provider.getNetwork();
      console.log('✅ Blockchain connected to network:', network.name);
      
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('❌ Blockchain initialization failed:', error.message);
      return false;
    }
  }

  // Token operations
  async getTokenBalance(address) {
    if (!this.cqtToken) throw new Error('CQT Token contract not initialized');
    return await this.cqtToken.balanceOf(address);
  }

  async getTokenBalanceFormatted(address) {
    const balance = await this.getTokenBalance(address);
    return ethers.formatEther(balance);
  }

  // Game Treasury operations
  async distributePvEReward(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const tx = await this.gameTreasury.distributePvEReward(playerAddress);
    return await tx.wait();
  }

  async distributePvPReward(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const tx = await this.gameTreasury.distributePvPReward(playerAddress);
    return await tx.wait();
  }

  async distributeDailyQuestReward(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const tx = await this.gameTreasury.distributeDailyQuestReward(playerAddress);
    return await tx.wait();
  }

  async upgradeHero(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const tx = await this.gameTreasury.upgradeHero(playerAddress);
    return await tx.wait();
  }

  async unlockSkill(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const tx = await this.gameTreasury.unlockSkill(playerAddress);
    return await tx.wait();
  }

  async getPlayerStats(playerAddress) {
    if (!this.gameTreasury) throw new Error('Game Treasury contract not initialized');
    const stats = await this.gameTreasury.getPlayerStats(playerAddress);
    return {
      totalEarned: ethers.formatEther(stats[0]),
      totalSpent: ethers.formatEther(stats[1]),
      pveWins: stats[2].toString(),
      pvpWins: stats[3].toString(),
      balance: ethers.formatEther(stats[4])
    };
  }

  // Staking operations
  async getStakeInfo(userAddress) {
    if (!this.stakingPool) throw new Error('Staking Pool contract not initialized');
    const info = await this.stakingPool.getStakeInfo(userAddress);
    return {
      amount: ethers.formatEther(info[0]),
      stakeTime: info[1].toString(),
      pendingRewards: ethers.formatEther(info[2]),
      accumulatedRewards: ethers.formatEther(info[3]),
      isActive: info[4],
      xpBoost: info[5],
      unlockTime: info[6].toString()
    };
  }

  async hasStakingBoost(userAddress) {
    if (!this.stakingPool) throw new Error('Staking Pool contract not initialized');
    return await this.stakingPool.hasStakingBoost(userAddress);
  }

  // Utility functions
  isValidAddress(address) {
    return ethers.isAddress(address);
  }

  formatEther(value) {
    return ethers.formatEther(value);
  }

  parseEther(value) {
    return ethers.parseEther(value.toString());
  }
}

// Create singleton instance
const blockchainService = new BlockchainService();

module.exports = blockchainService;
