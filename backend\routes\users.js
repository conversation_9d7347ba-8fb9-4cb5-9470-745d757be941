const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken } = require('../middleware/auth');
const blockchainService = require('../config/blockchain');

const router = express.Router();

// Get user profile
router.get('/profile', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    
    // Get user data
    const users = await executeQuery(
      'SELECT * FROM users WHERE wallet_address = ?',
      [walletAddress]
    );
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const user = users[0];
    
    // Get blockchain data
    let tokenBalance = '0';
    let stakingInfo = null;
    
    try {
      if (blockchainService.initialized) {
        tokenBalance = await blockchainService.getTokenBalanceFormatted(walletAddress);
        stakingInfo = await blockchainService.getStakeInfo(walletAddress);
      }
    } catch (error) {
      console.warn('Failed to fetch blockchain data:', error.message);
    }
    
    // Get user statistics
    const stats = await getUserStats(walletAddress);
    
    res.json({
      user: {
        walletAddress: user.wallet_address,
        username: user.username,
        email: user.email,
        level: user.level,
        xp: user.xp,
        rankPoints: user.rank_points,
        totalBattles: user.total_battles,
        wins: user.wins,
        losses: user.losses,
        lastDailyQuest: user.last_daily_quest,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      },
      blockchain: {
        tokenBalance,
        stakingInfo
      },
      stats
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get user profile' });
  }
});

// Update user profile
router.put('/profile', verifyToken, [
  body('username').optional().isString().isLength({ min: 3, max: 50 }),
  body('email').optional().isEmail()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { walletAddress } = req.user;
    const { username, email } = req.body;
    
    const updateFields = [];
    const updateValues = [];
    
    if (username !== undefined) {
      // Check if username is already taken
      const existingUsers = await executeQuery(
        'SELECT wallet_address FROM users WHERE username = ? AND wallet_address != ?',
        [username, walletAddress]
      );
      
      if (existingUsers.length > 0) {
        return res.status(400).json({ error: 'Username already taken' });
      }
      
      updateFields.push('username = ?');
      updateValues.push(username);
    }
    
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    
    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'No fields to update' });
    }
    
    updateValues.push(walletAddress);
    
    await executeQuery(
      `UPDATE users SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE wallet_address = ?`,
      updateValues
    );
    
    res.json({ message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Get user statistics
async function getUserStats(walletAddress) {
  try {
    // Get hero count
    const heroCount = await executeQuery(
      'SELECT COUNT(*) as count FROM heroes WHERE owner_address = ? AND is_active = TRUE',
      [walletAddress]
    );
    
    // Get recent battles
    const recentBattles = await executeQuery(
      `SELECT battle_type, winner_address, reward_amount, created_at 
       FROM battles 
       WHERE player1_address = ? OR player2_address = ? 
       ORDER BY created_at DESC 
       LIMIT 10`,
      [walletAddress, walletAddress]
    );
    
    // Get total earnings
    const earnings = await executeQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE user_address = ? AND transaction_type = "earn"',
      [walletAddress]
    );
    
    // Get total spending
    const spending = await executeQuery(
      'SELECT SUM(amount) as total FROM transactions WHERE user_address = ? AND transaction_type = "spend"',
      [walletAddress]
    );
    
    // Get win rate
    const winRate = await executeQuery(
      `SELECT 
         COUNT(*) as total_battles,
         SUM(CASE WHEN winner_address = ? THEN 1 ELSE 0 END) as wins
       FROM battles 
       WHERE player1_address = ? OR player2_address = ?`,
      [walletAddress, walletAddress, walletAddress]
    );
    
    return {
      heroCount: heroCount[0].count,
      recentBattles: recentBattles.map(battle => ({
        type: battle.battle_type,
        won: battle.winner_address === walletAddress,
        reward: battle.reward_amount,
        date: battle.created_at
      })),
      totalEarnings: earnings[0].total || 0,
      totalSpending: spending[0].total || 0,
      totalBattles: winRate[0].total_battles,
      wins: winRate[0].wins,
      winRate: winRate[0].total_battles > 0 ? (winRate[0].wins / winRate[0].total_battles * 100).toFixed(2) : 0
    };
  } catch (error) {
    console.error('Get user stats error:', error);
    return {
      heroCount: 0,
      recentBattles: [],
      totalEarnings: 0,
      totalSpending: 0,
      totalBattles: 0,
      wins: 0,
      winRate: 0
    };
  }
}

// Get leaderboard
router.get('/leaderboard', async (req, res) => {
  try {
    const { type = 'rank', limit = 50 } = req.query;
    
    let orderBy;
    switch (type) {
      case 'level':
        orderBy = 'level DESC, xp DESC';
        break;
      case 'wins':
        orderBy = 'wins DESC, total_battles ASC';
        break;
      case 'rank':
      default:
        orderBy = 'rank_points DESC';
        break;
    }
    
    const leaderboard = await executeQuery(
      `SELECT 
         wallet_address,
         username,
         level,
         xp,
         rank_points,
         wins,
         total_battles,
         CASE 
           WHEN total_battles > 0 THEN ROUND((wins / total_battles) * 100, 2)
           ELSE 0 
         END as win_rate
       FROM users 
       ORDER BY ${orderBy}
       LIMIT ?`,
      [parseInt(limit)]
    );
    
    res.json({
      leaderboard: leaderboard.map((user, index) => ({
        rank: index + 1,
        walletAddress: user.wallet_address,
        username: user.username || `Player ${user.wallet_address.slice(0, 6)}...`,
        level: user.level,
        xp: user.xp,
        rankPoints: user.rank_points,
        wins: user.wins,
        totalBattles: user.total_battles,
        winRate: user.win_rate
      })),
      type
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({ error: 'Failed to get leaderboard' });
  }
});

// Get user rank
router.get('/rank', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    
    const rankResult = await executeQuery(
      `SELECT 
         COUNT(*) + 1 as rank
       FROM users 
       WHERE rank_points > (
         SELECT rank_points FROM users WHERE wallet_address = ?
       )`,
      [walletAddress]
    );
    
    const userRank = rankResult[0].rank;
    
    res.json({ rank: userRank });
  } catch (error) {
    console.error('Get user rank error:', error);
    res.status(500).json({ error: 'Failed to get user rank' });
  }
});

// Add XP to user (internal function, called by game logic)
async function addUserXP(walletAddress, xpAmount) {
  try {
    const user = await executeQuery(
      'SELECT level, xp FROM users WHERE wallet_address = ?',
      [walletAddress]
    );
    
    if (user.length === 0) return false;
    
    const currentLevel = user[0].level;
    const currentXP = user[0].xp;
    const newXP = currentXP + xpAmount;
    
    // Calculate new level (simple formula: level = floor(sqrt(xp/100)))
    const newLevel = Math.floor(Math.sqrt(newXP / 100)) + 1;
    
    await executeQuery(
      'UPDATE users SET xp = ?, level = ? WHERE wallet_address = ?',
      [newXP, newLevel, walletAddress]
    );
    
    return {
      levelUp: newLevel > currentLevel,
      oldLevel: currentLevel,
      newLevel,
      oldXP: currentXP,
      newXP
    };
  } catch (error) {
    console.error('Add XP error:', error);
    return false;
  }
}

module.exports = router;
module.exports.addUserXP = addUserXP;
