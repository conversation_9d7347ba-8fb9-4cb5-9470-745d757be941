{"ast": null, "code": "/**\n *  A Wordlist is a set of 2048 words used to encode private keys\n *  (or other binary data) that is easier for humans to write down,\n *  transcribe and dictate.\n *\n *  The [[link-bip-39]] standard includes several checksum bits,\n *  depending on the size of the mnemonic phrase.\n *\n *  A mnemonic phrase may be 12, 15, 18, 21 or 24 words long. For\n *  most purposes 12 word mnemonics should be used, as including\n *  additional words increases the difficulty and potential for\n *  mistakes and does not offer any effective improvement on security.\n *\n *  There are a variety of [[link-bip39-wordlists]] for different\n *  languages, but for maximal compatibility, the\n *  [English Wordlist](LangEn) is recommended.\n *\n *  @_section: api/wordlists:Wordlists [about-wordlists]\n */\nexport { Wordlist } from \"./wordlist.js\";\nexport { LangEn } from \"./lang-en.js\";\nexport { WordlistOwl } from \"./wordlist-owl.js\";\nexport { WordlistOwlA } from \"./wordlist-owla.js\";\nexport { wordlists } from \"./wordlists.js\";", "map": {"version": 3, "names": ["Wordlist", "LangEn", "WordlistOwl", "WordlistOwlA", "wordlists"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\index.ts"], "sourcesContent": ["/**\n *  A Wordlist is a set of 2048 words used to encode private keys\n *  (or other binary data) that is easier for humans to write down,\n *  transcribe and dictate.\n *\n *  The [[link-bip-39]] standard includes several checksum bits,\n *  depending on the size of the mnemonic phrase.\n *\n *  A mnemonic phrase may be 12, 15, 18, 21 or 24 words long. For\n *  most purposes 12 word mnemonics should be used, as including\n *  additional words increases the difficulty and potential for\n *  mistakes and does not offer any effective improvement on security.\n *\n *  There are a variety of [[link-bip39-wordlists]] for different\n *  languages, but for maximal compatibility, the\n *  [English Wordlist](LangEn) is recommended.\n *\n *  @_section: api/wordlists:Wordlists [about-wordlists]\n */\nexport { Wordlist } from \"./wordlist.js\";\nexport { LangEn } from \"./lang-en.js\";\n\nexport { WordlistOwl } from \"./wordlist-owl.js\";\nexport { WordlistOwlA } from \"./wordlist-owla.js\";\n\nexport { wordlists } from \"./wordlists.js\";\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,YAAY,QAAQ,oBAAoB;AAEjD,SAASC,SAAS,QAAQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}