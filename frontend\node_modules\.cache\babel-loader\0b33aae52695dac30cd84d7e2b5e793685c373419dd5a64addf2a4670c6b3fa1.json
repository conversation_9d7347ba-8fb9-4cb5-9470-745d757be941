{"ast": null, "code": "export function pkcs7Pad(data) {\n  const padder = 16 - data.length % 16;\n  const result = new Uint8Array(data.length + padder);\n  result.set(data);\n  for (let i = data.length; i < result.length; i++) {\n    result[i] = padder;\n  }\n  return result;\n}\nexport function pkcs7Strip(data) {\n  if (data.length < 16) {\n    throw new TypeError('PKCS#7 invalid length');\n  }\n  const padder = data[data.length - 1];\n  if (padder > 16) {\n    throw new TypeError('PKCS#7 padding byte out of range');\n  }\n  const length = data.length - padder;\n  for (let i = 0; i < padder; i++) {\n    if (data[length + i] !== padder) {\n      throw new TypeError('PKCS#7 invalid padding byte');\n    }\n  }\n  return new Uint8Array(data.subarray(0, length));\n}", "map": {"version": 3, "names": ["pkcs7Pad", "data", "padder", "length", "result", "Uint8Array", "set", "i", "pkcs7Strip", "TypeError", "subarray"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\aes-js\\src.ts\\padding.ts"], "sourcesContent": ["\nexport function pkcs7Pad(data: Uint8Array): Uint8Array {\n    const padder = 16 - (data.length % 16);\n\n    const result = new Uint8Array(data.length + padder);\n    result.set(data);\n\n    for (let i = data.length; i < result.length; i++) {\n        result[i] = padder;\n    }\n\n    return result;\n}\n\nexport function pkcs7Strip(data: Uint8Array): Uint8Array {\n    if (data.length < 16) { throw new TypeError('PKCS#7 invalid length'); }\n\n    const padder = data[data.length - 1];\n    if (padder > 16) { throw new TypeError('PKCS#7 padding byte out of range'); }\n\n    const length = data.length - padder;\n    for (let i = 0; i < padder; i++) {\n        if (data[length + i] !== padder) {\n            throw new TypeError('PKCS#7 invalid padding byte');\n        }\n    }\n\n    return new Uint8Array(data.subarray(0, length));\n}\n"], "mappings": "AACA,OAAM,SAAUA,QAAQA,CAACC,IAAgB;EACrC,MAAMC,MAAM,GAAG,EAAE,GAAID,IAAI,CAACE,MAAM,GAAG,EAAG;EAEtC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAACJ,IAAI,CAACE,MAAM,GAAGD,MAAM,CAAC;EACnDE,MAAM,CAACE,GAAG,CAACL,IAAI,CAAC;EAEhB,KAAK,IAAIM,CAAC,GAAGN,IAAI,CAACE,MAAM,EAAEI,CAAC,GAAGH,MAAM,CAACD,MAAM,EAAEI,CAAC,EAAE,EAAE;IAC9CH,MAAM,CAACG,CAAC,CAAC,GAAGL,MAAM;;EAGtB,OAAOE,MAAM;AACjB;AAEA,OAAM,SAAUI,UAAUA,CAACP,IAAgB;EACvC,IAAIA,IAAI,CAACE,MAAM,GAAG,EAAE,EAAE;IAAE,MAAM,IAAIM,SAAS,CAAC,uBAAuB,CAAC;;EAEpE,MAAMP,MAAM,GAAGD,IAAI,CAACA,IAAI,CAACE,MAAM,GAAG,CAAC,CAAC;EACpC,IAAID,MAAM,GAAG,EAAE,EAAE;IAAE,MAAM,IAAIO,SAAS,CAAC,kCAAkC,CAAC;;EAE1E,MAAMN,MAAM,GAAGF,IAAI,CAACE,MAAM,GAAGD,MAAM;EACnC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;IAC7B,IAAIN,IAAI,CAACE,MAAM,GAAGI,CAAC,CAAC,KAAKL,MAAM,EAAE;MAC7B,MAAM,IAAIO,SAAS,CAAC,6BAA6B,CAAC;;;EAI1D,OAAO,IAAIJ,UAAU,CAACJ,IAAI,CAACS,QAAQ,CAAC,CAAC,EAAEP,MAAM,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}