import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  ShieldCheckIcon, 
  TrophyIcon, 
  CurrencyDollarIcon,
  SparklesIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

const Home = () => {
  const { isAuthenticated } = useAuth();

  const features = [
    {
      icon: ShieldCheckIcon,
      title: 'Collect Heroes',
      description: 'Build your team with unique heroes, each with special abilities and stats.',
    },
    {
      icon: TrophyIcon,
      title: 'Battle & Earn',
      description: 'Fight in PvE adventures and PvP arenas to earn CQT tokens.',
    },
    {
      icon: CurrencyDollarIcon,
      title: 'Stake & Grow',
      description: 'Stake your CQT tokens to earn passive rewards and unlock bonuses.',
    },
    {
      icon: SparklesIcon,
      title: 'Upgrade & Evolve',
      description: 'Use earned tokens to upgrade heroes and unlock powerful skills.',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-game-pattern opacity-10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-game font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-400 via-secondary-400 to-accent-400 bg-clip-text text-transparent">
                CryptoQuest
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-dark-300 mb-8 max-w-3xl mx-auto">
              The ultimate Web3 Play-to-Earn battle game. Collect heroes, battle monsters, 
              and earn real cryptocurrency rewards on the blockchain.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              {isAuthenticated ? (
                <Link to="/dashboard" className="game-button text-lg px-8 py-3 flex items-center space-x-2">
                  <PlayIcon className="w-5 h-5" />
                  <span>Enter Game</span>
                </Link>
              ) : (
                <Link to="/login" className="game-button text-lg px-8 py-3 flex items-center space-x-2">
                  <PlayIcon className="w-5 h-5" />
                  <span>Start Playing</span>
                </Link>
              )}
              <Link to="/leaderboard" className="game-button-secondary text-lg px-8 py-3">
                View Leaderboard
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-dark-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              How to Play
            </h2>
            <p className="text-xl text-dark-300 max-w-2xl mx-auto">
              Master the art of battle and earn real cryptocurrency rewards
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="game-card p-6 text-center hover:glow-primary transition-all duration-300">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-dark-300">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Game Statistics
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="game-card p-8 text-center">
              <div className="text-4xl font-bold text-primary-400 mb-2">1B</div>
              <div className="text-dark-300">Total CQT Supply</div>
            </div>
            <div className="game-card p-8 text-center">
              <div className="text-4xl font-bold text-secondary-400 mb-2">12%</div>
              <div className="text-dark-300">Staking APY</div>
            </div>
            <div className="game-card p-8 text-center">
              <div className="text-4xl font-bold text-accent-400 mb-2">5</div>
              <div className="text-dark-300">Hero Types</div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-primary-900/50 to-secondary-900/50">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Start Your Quest?
          </h2>
          <p className="text-xl text-dark-300 mb-8">
            Join thousands of players earning real cryptocurrency through strategic battles
          </p>
          {!isAuthenticated && (
            <Link to="/login" className="game-button text-lg px-8 py-3 inline-flex items-center space-x-2">
              <span>Connect Wallet & Play</span>
              <PlayIcon className="w-5 h-5" />
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;
