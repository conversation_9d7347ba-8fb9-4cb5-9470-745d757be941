{"ast": null, "code": "import { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n/**\n *  @_ignore\n */\nexport class BooleanCoder extends Coder {\n  constructor(localName) {\n    super(\"bool\", \"bool\", localName, false);\n  }\n  defaultValue() {\n    return false;\n  }\n  encode(writer, _value) {\n    const value = Typed.dereference(_value, \"bool\");\n    return writer.writeValue(value ? 1 : 0);\n  }\n  decode(reader) {\n    return !!reader.readValue();\n  }\n}", "map": {"version": 3, "names": ["Typed", "Coder", "BooleanCoder", "constructor", "localName", "defaultValue", "encode", "writer", "_value", "value", "dereference", "writeValue", "decode", "reader", "readValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\boolean.ts"], "sourcesContent": ["import { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n/**\n *  @_ignore\n */\nexport class BooleanCoder extends Coder {\n\n    constructor(localName: string) {\n        super(\"bool\", \"bool\", localName, false);\n    }\n\n    defaultValue(): boolean {\n        return false;\n    }\n\n    encode(writer: Writer, _value: boolean | Typed): number {\n        const value = Typed.dereference(_value, \"bool\");\n        return writer.writeValue(value ? 1: 0);\n    }\n\n    decode(reader: Reader): any {\n        return !!reader.readValue();\n    }\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,qBAAqB;AAI3C;;;AAGA,OAAM,MAAOC,YAAa,SAAQD,KAAK;EAEnCE,YAAYC,SAAiB;IACzB,KAAK,CAAC,MAAM,EAAE,MAAM,EAAEA,SAAS,EAAE,KAAK,CAAC;EAC3C;EAEAC,YAAYA,CAAA;IACR,OAAO,KAAK;EAChB;EAEAC,MAAMA,CAACC,MAAc,EAAEC,MAAuB;IAC1C,MAAMC,KAAK,GAAGT,KAAK,CAACU,WAAW,CAACF,MAAM,EAAE,MAAM,CAAC;IAC/C,OAAOD,MAAM,CAACI,UAAU,CAACF,KAAK,GAAG,CAAC,GAAE,CAAC,CAAC;EAC1C;EAEAG,MAAMA,CAACC,MAAc;IACjB,OAAO,CAAC,CAACA,MAAM,CAACC,SAAS,EAAE;EAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}