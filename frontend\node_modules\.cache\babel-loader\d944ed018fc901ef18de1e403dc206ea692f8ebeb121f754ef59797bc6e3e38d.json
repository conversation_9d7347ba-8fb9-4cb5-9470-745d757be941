{"ast": null, "code": "import { useState } from 'react';\nimport { initPrefersReducedMotion } from './index.mjs';\nimport { warnOnce } from '../warn-once.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\n/**\n * A hook that returns `true` if we should be using reduced motion based on the current device's Reduced Motion setting.\n *\n * This can be used to implement changes to your UI based on Reduced Motion. For instance, replacing motion-sickness inducing\n * `x`/`y` animations with `opacity`, disabling the autoplay of background videos, or turning off parallax motion.\n *\n * It will actively respond to changes and re-render your components with the latest setting.\n *\n * ```jsx\n * export function Sidebar({ isOpen }) {\n *   const shouldReduceMotion = useReducedMotion()\n *   const closedX = shouldReduceMotion ? 0 : \"-100%\"\n *\n *   return (\n *     <motion.div animate={{\n *       opacity: isOpen ? 1 : 0,\n *       x: isOpen ? 0 : closedX\n *     }} />\n *   )\n * }\n * ```\n *\n * @return boolean\n *\n * @public\n */\nfunction useReducedMotion() {\n  /**\n   * Lazy initialisation of prefersReducedMotion\n   */\n  !hasReducedMotionListener.current && initPrefersReducedMotion();\n  const [shouldReduceMotion] = useState(prefersReducedMotion.current);\n  if (process.env.NODE_ENV !== \"production\") {\n    warnOnce(shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n  }\n  /**\n   * TODO See if people miss automatically updating shouldReduceMotion setting\n   */\n  return shouldReduceMotion;\n}\nexport { useReducedMotion };", "map": {"version": 3, "names": ["useState", "initPrefersReducedMotion", "warnOnce", "hasReducedMotionListener", "prefersReducedMotion", "useReducedMotion", "current", "shouldReduceMotion", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/utils/reduced-motion/use-reduced-motion.mjs"], "sourcesContent": ["import { useState } from 'react';\nimport { initPrefersReducedMotion } from './index.mjs';\nimport { warnOnce } from '../warn-once.mjs';\nimport { hasReducedMotionListener, prefersReducedMotion } from './state.mjs';\n\n/**\n * A hook that returns `true` if we should be using reduced motion based on the current device's Reduced Motion setting.\n *\n * This can be used to implement changes to your UI based on Reduced Motion. For instance, replacing motion-sickness inducing\n * `x`/`y` animations with `opacity`, disabling the autoplay of background videos, or turning off parallax motion.\n *\n * It will actively respond to changes and re-render your components with the latest setting.\n *\n * ```jsx\n * export function Sidebar({ isOpen }) {\n *   const shouldReduceMotion = useReducedMotion()\n *   const closedX = shouldReduceMotion ? 0 : \"-100%\"\n *\n *   return (\n *     <motion.div animate={{\n *       opacity: isOpen ? 1 : 0,\n *       x: isOpen ? 0 : closedX\n *     }} />\n *   )\n * }\n * ```\n *\n * @return boolean\n *\n * @public\n */\nfunction useReducedMotion() {\n    /**\n     * Lazy initialisation of prefersReducedMotion\n     */\n    !hasReducedMotionListener.current && initPrefersReducedMotion();\n    const [shouldReduceMotion] = useState(prefersReducedMotion.current);\n    if (process.env.NODE_ENV !== \"production\") {\n        warnOnce(shouldReduceMotion !== true, \"You have Reduced Motion enabled on your device. Animations may not appear as expected.\");\n    }\n    /**\n     * TODO See if people miss automatically updating shouldReduceMotion setting\n     */\n    return shouldReduceMotion;\n}\n\nexport { useReducedMotion };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,wBAAwB,QAAQ,aAAa;AACtD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,aAAa;;AAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxB;AACJ;AACA;EACI,CAACF,wBAAwB,CAACG,OAAO,IAAIL,wBAAwB,CAAC,CAAC;EAC/D,MAAM,CAACM,kBAAkB,CAAC,GAAGP,QAAQ,CAACI,oBAAoB,CAACE,OAAO,CAAC;EACnE,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACvCR,QAAQ,CAACK,kBAAkB,KAAK,IAAI,EAAE,wFAAwF,CAAC;EACnI;EACA;AACJ;AACA;EACI,OAAOA,kBAAkB;AAC7B;AAEA,SAASF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}