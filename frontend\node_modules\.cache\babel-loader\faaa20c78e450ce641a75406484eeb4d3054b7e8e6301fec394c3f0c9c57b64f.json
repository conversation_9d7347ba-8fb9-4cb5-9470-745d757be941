{"ast": null, "code": "/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Abelian group utilities\nimport { validateField, nLength } from './modular.js';\nimport { validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nexport function wNAF(c, bits) {\n  const constTimeNegate = (condition, item) => {\n    const neg = item.negate();\n    return condition ? neg : item;\n  };\n  const opts = W => {\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return {\n      windows,\n      windowSize\n    };\n  };\n  return {\n    constTimeNegate,\n    // non-const time multiplication ladder\n    unsafeLadder(elm, n) {\n      let p = c.ZERO;\n      let d = elm;\n      while (n > _0n) {\n        if (n & _1n) p = p.add(d);\n        d = d.double();\n        n >>= _1n;\n      }\n      return p;\n    },\n    /**\n     * Creates a wNAF precomputation window. Used for caching.\n     * Default window size is set by `utils.precompute()` and is equal to 8.\n     * Number of precomputed points depends on the curve size:\n     * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n     * - 𝑊 is the window size\n     * - 𝑛 is the bitlength of the curve order.\n     * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n     * @returns precomputed point tables flattened to a single array\n     */\n    precomputeWindow(elm, W) {\n      const {\n        windows,\n        windowSize\n      } = opts(W);\n      const points = [];\n      let p = elm;\n      let base = p;\n      for (let window = 0; window < windows; window++) {\n        base = p;\n        points.push(base);\n        // =1, because we skip zero\n        for (let i = 1; i < windowSize; i++) {\n          base = base.add(p);\n          points.push(base);\n        }\n        p = base.double();\n      }\n      return points;\n    },\n    /**\n     * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @returns real and fake (for const-time) points\n     */\n    wNAF(W, precomputes, n) {\n      // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n      // But need to carefully remove other checks before wNAF. ORDER == bits here\n      const {\n        windows,\n        windowSize\n      } = opts(W);\n      let p = c.ZERO;\n      let f = c.BASE;\n      const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n      const maxNumber = 2 ** W;\n      const shiftBy = BigInt(W);\n      for (let window = 0; window < windows; window++) {\n        const offset = window * windowSize;\n        // Extract W bits.\n        let wbits = Number(n & mask);\n        // Shift number by W bits.\n        n >>= shiftBy;\n        // If the bits are bigger than max size, we'll split those.\n        // +224 => 256 - 32\n        if (wbits > windowSize) {\n          wbits -= maxNumber;\n          n += _1n;\n        }\n        // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n        // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n        // there is negate now: it is possible that negated element from low value\n        // would be the same as high element, which will create carry into next window.\n        // It's not obvious how this can fail, but still worth investigating later.\n        // Check if we're onto Zero point.\n        // Add random point inside current window to f.\n        const offset1 = offset;\n        const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n        const cond1 = window % 2 !== 0;\n        const cond2 = wbits < 0;\n        if (wbits === 0) {\n          // The most important part for const-time getPublicKey\n          f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n        } else {\n          p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n        }\n      }\n      // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n      // Even if the variable is still unused, there are some checks which will\n      // throw an exception, so compiler needs to prove they won't happen, which is hard.\n      // At this point there is a way to F be infinity-point even if p is not,\n      // which makes it less const-time: around 1 bigint multiply.\n      return {\n        p,\n        f\n      };\n    },\n    wNAFCached(P, precomputesMap, n, transform) {\n      // @ts-ignore\n      const W = P._WINDOW_SIZE || 1;\n      // Calculate precomputes on a first run, reuse them after\n      let comp = precomputesMap.get(P);\n      if (!comp) {\n        comp = this.precomputeWindow(P, W);\n        if (W !== 1) {\n          precomputesMap.set(P, transform(comp));\n        }\n      }\n      return this.wNAF(W, comp, n);\n    }\n  };\n}\nexport function validateBasic(curve) {\n  validateField(curve.Fp);\n  validateObject(curve, {\n    n: 'bigint',\n    h: 'bigint',\n    Gx: 'field',\n    Gy: 'field'\n  }, {\n    nBitLength: 'isSafeInteger',\n    nByteLength: 'isSafeInteger'\n  });\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{\n      p: curve.Fp.ORDER\n    }\n  });\n}", "map": {"version": 3, "names": ["validateField", "nLength", "validateObject", "_0n", "BigInt", "_1n", "wNAF", "c", "bits", "constTimeNegate", "condition", "item", "neg", "negate", "opts", "W", "windows", "Math", "ceil", "windowSize", "unsafeL<PERSON>der", "elm", "n", "p", "ZERO", "d", "add", "double", "precomputeWindow", "points", "base", "window", "push", "i", "precomputes", "f", "BASE", "mask", "maxNumber", "shiftBy", "offset", "wbits", "Number", "offset1", "offset2", "abs", "cond1", "cond2", "wNAFCached", "P", "precomputesMap", "transform", "_WINDOW_SIZE", "comp", "get", "set", "validateBasic", "curve", "Fp", "h", "Gx", "Gy", "nBitLength", "nByteLength", "Object", "freeze", "ORDER"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\@noble\\curves\\src\\abstract\\curve.ts"], "sourcesContent": ["/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */\n// Abelian group utilities\nimport { IField, validateField, nLength } from './modular.js';\nimport { validateObject } from './utils.js';\nconst _0n = BigInt(0);\nconst _1n = BigInt(1);\n\nexport type AffinePoint<T> = {\n  x: T;\n  y: T;\n} & { z?: never; t?: never };\n\nexport interface Group<T extends Group<T>> {\n  double(): T;\n  negate(): T;\n  add(other: T): T;\n  subtract(other: T): T;\n  equals(other: T): boolean;\n  multiply(scalar: bigint): T;\n}\n\nexport type GroupConstructor<T> = {\n  BASE: T;\n  ZERO: T;\n};\nexport type Mapper<T> = (i: T[]) => T[];\n\n// Elliptic curve multiplication of Point by scalar. Fragile.\n// Scalars should always be less than curve order: this should be checked inside of a curve itself.\n// Creates precomputation tables for fast multiplication:\n// - private scalar is split by fixed size windows of W bits\n// - every window point is collected from window's table & added to accumulator\n// - since windows are different, same point inside tables won't be accessed more than once per calc\n// - each multiplication is 'Math.ceil(CURVE_ORDER / 𝑊) + 1' point additions (fixed for any scalar)\n// - +1 window is neccessary for wNAF\n// - wNAF reduces table size: 2x less memory + 2x faster generation, but 10% slower multiplication\n// TODO: Research returning 2d JS array of windows, instead of a single window. This would allow\n// windows to be in different memory locations\nexport function wNAF<T extends Group<T>>(c: GroupConstructor<T>, bits: number) {\n  const constTimeNegate = (condition: boolean, item: T): T => {\n    const neg = item.negate();\n    return condition ? neg : item;\n  };\n  const opts = (W: number) => {\n    const windows = Math.ceil(bits / W) + 1; // +1, because\n    const windowSize = 2 ** (W - 1); // -1 because we skip zero\n    return { windows, windowSize };\n  };\n  return {\n    constTimeNegate,\n    // non-const time multiplication ladder\n    unsafeLadder(elm: T, n: bigint) {\n      let p = c.ZERO;\n      let d: T = elm;\n      while (n > _0n) {\n        if (n & _1n) p = p.add(d);\n        d = d.double();\n        n >>= _1n;\n      }\n      return p;\n    },\n\n    /**\n     * Creates a wNAF precomputation window. Used for caching.\n     * Default window size is set by `utils.precompute()` and is equal to 8.\n     * Number of precomputed points depends on the curve size:\n     * 2^(𝑊−1) * (Math.ceil(𝑛 / 𝑊) + 1), where:\n     * - 𝑊 is the window size\n     * - 𝑛 is the bitlength of the curve order.\n     * For a 256-bit curve and window size 8, the number of precomputed points is 128 * 33 = 4224.\n     * @returns precomputed point tables flattened to a single array\n     */\n    precomputeWindow(elm: T, W: number): Group<T>[] {\n      const { windows, windowSize } = opts(W);\n      const points: T[] = [];\n      let p: T = elm;\n      let base = p;\n      for (let window = 0; window < windows; window++) {\n        base = p;\n        points.push(base);\n        // =1, because we skip zero\n        for (let i = 1; i < windowSize; i++) {\n          base = base.add(p);\n          points.push(base);\n        }\n        p = base.double();\n      }\n      return points;\n    },\n\n    /**\n     * Implements ec multiplication using precomputed tables and w-ary non-adjacent form.\n     * @param W window size\n     * @param precomputes precomputed tables\n     * @param n scalar (we don't check here, but should be less than curve order)\n     * @returns real and fake (for const-time) points\n     */\n    wNAF(W: number, precomputes: T[], n: bigint): { p: T; f: T } {\n      // TODO: maybe check that scalar is less than group order? wNAF behavious is undefined otherwise\n      // But need to carefully remove other checks before wNAF. ORDER == bits here\n      const { windows, windowSize } = opts(W);\n\n      let p = c.ZERO;\n      let f = c.BASE;\n\n      const mask = BigInt(2 ** W - 1); // Create mask with W ones: 0b1111 for W=4 etc.\n      const maxNumber = 2 ** W;\n      const shiftBy = BigInt(W);\n\n      for (let window = 0; window < windows; window++) {\n        const offset = window * windowSize;\n        // Extract W bits.\n        let wbits = Number(n & mask);\n\n        // Shift number by W bits.\n        n >>= shiftBy;\n\n        // If the bits are bigger than max size, we'll split those.\n        // +224 => 256 - 32\n        if (wbits > windowSize) {\n          wbits -= maxNumber;\n          n += _1n;\n        }\n\n        // This code was first written with assumption that 'f' and 'p' will never be infinity point:\n        // since each addition is multiplied by 2 ** W, it cannot cancel each other. However,\n        // there is negate now: it is possible that negated element from low value\n        // would be the same as high element, which will create carry into next window.\n        // It's not obvious how this can fail, but still worth investigating later.\n\n        // Check if we're onto Zero point.\n        // Add random point inside current window to f.\n        const offset1 = offset;\n        const offset2 = offset + Math.abs(wbits) - 1; // -1 because we skip zero\n        const cond1 = window % 2 !== 0;\n        const cond2 = wbits < 0;\n        if (wbits === 0) {\n          // The most important part for const-time getPublicKey\n          f = f.add(constTimeNegate(cond1, precomputes[offset1]));\n        } else {\n          p = p.add(constTimeNegate(cond2, precomputes[offset2]));\n        }\n      }\n      // JIT-compiler should not eliminate f here, since it will later be used in normalizeZ()\n      // Even if the variable is still unused, there are some checks which will\n      // throw an exception, so compiler needs to prove they won't happen, which is hard.\n      // At this point there is a way to F be infinity-point even if p is not,\n      // which makes it less const-time: around 1 bigint multiply.\n      return { p, f };\n    },\n\n    wNAFCached(P: T, precomputesMap: Map<T, T[]>, n: bigint, transform: Mapper<T>): { p: T; f: T } {\n      // @ts-ignore\n      const W: number = P._WINDOW_SIZE || 1;\n      // Calculate precomputes on a first run, reuse them after\n      let comp = precomputesMap.get(P);\n      if (!comp) {\n        comp = this.precomputeWindow(P, W) as T[];\n        if (W !== 1) {\n          precomputesMap.set(P, transform(comp));\n        }\n      }\n      return this.wNAF(W, comp, n);\n    },\n  };\n}\n\n// Generic BasicCurve interface: works even for polynomial fields (BLS): P, n, h would be ok.\n// Though generator can be different (Fp2 / Fp6 for BLS).\nexport type BasicCurve<T> = {\n  Fp: IField<T>; // Field over which we'll do calculations (Fp)\n  n: bigint; // Curve order, total count of valid points in the field\n  nBitLength?: number; // bit length of curve order\n  nByteLength?: number; // byte length of curve order\n  h: bigint; // cofactor. we can assign default=1, but users will just ignore it w/o validation\n  hEff?: bigint; // Number to multiply to clear cofactor\n  Gx: T; // base point X coordinate\n  Gy: T; // base point Y coordinate\n  allowInfinityPoint?: boolean; // bls12-381 requires it. ZERO point is valid, but invalid pubkey\n};\n\nexport function validateBasic<FP, T>(curve: BasicCurve<FP> & T) {\n  validateField(curve.Fp);\n  validateObject(\n    curve,\n    {\n      n: 'bigint',\n      h: 'bigint',\n      Gx: 'field',\n      Gy: 'field',\n    },\n    {\n      nBitLength: 'isSafeInteger',\n      nByteLength: 'isSafeInteger',\n    }\n  );\n  // Set defaults\n  return Object.freeze({\n    ...nLength(curve.n, curve.nBitLength),\n    ...curve,\n    ...{ p: curve.Fp.ORDER },\n  } as const);\n}\n"], "mappings": "AAAA;AACA;AACA,SAAiBA,aAAa,EAAEC,OAAO,QAAQ,cAAc;AAC7D,SAASC,cAAc,QAAQ,YAAY;AAC3C,MAAMC,GAAG,GAAGC,MAAM,CAAC,CAAC,CAAC;AACrB,MAAMC,GAAG,GAAGD,MAAM,CAAC,CAAC,CAAC;AAsBrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAM,SAAUE,IAAIA,CAAqBC,CAAsB,EAAEC,IAAY;EAC3E,MAAMC,eAAe,GAAGA,CAACC,SAAkB,EAAEC,IAAO,KAAO;IACzD,MAAMC,GAAG,GAAGD,IAAI,CAACE,MAAM,EAAE;IACzB,OAAOH,SAAS,GAAGE,GAAG,GAAGD,IAAI;EAC/B,CAAC;EACD,MAAMG,IAAI,GAAIC,CAAS,IAAI;IACzB,MAAMC,OAAO,GAAGC,IAAI,CAACC,IAAI,CAACV,IAAI,GAAGO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACzC,MAAMI,UAAU,GAAG,CAAC,KAAKJ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,OAAO;MAAEC,OAAO;MAAEG;IAAU,CAAE;EAChC,CAAC;EACD,OAAO;IACLV,eAAe;IACf;IACAW,YAAYA,CAACC,GAAM,EAAEC,CAAS;MAC5B,IAAIC,CAAC,GAAGhB,CAAC,CAACiB,IAAI;MACd,IAAIC,CAAC,GAAMJ,GAAG;MACd,OAAOC,CAAC,GAAGnB,GAAG,EAAE;QACd,IAAImB,CAAC,GAAGjB,GAAG,EAAEkB,CAAC,GAAGA,CAAC,CAACG,GAAG,CAACD,CAAC,CAAC;QACzBA,CAAC,GAAGA,CAAC,CAACE,MAAM,EAAE;QACdL,CAAC,KAAKjB,GAAG;;MAEX,OAAOkB,CAAC;IACV,CAAC;IAED;;;;;;;;;;IAUAK,gBAAgBA,CAACP,GAAM,EAAEN,CAAS;MAChC,MAAM;QAAEC,OAAO;QAAEG;MAAU,CAAE,GAAGL,IAAI,CAACC,CAAC,CAAC;MACvC,MAAMc,MAAM,GAAQ,EAAE;MACtB,IAAIN,CAAC,GAAMF,GAAG;MACd,IAAIS,IAAI,GAAGP,CAAC;MACZ,KAAK,IAAIQ,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGf,OAAO,EAAEe,MAAM,EAAE,EAAE;QAC/CD,IAAI,GAAGP,CAAC;QACRM,MAAM,CAACG,IAAI,CAACF,IAAI,CAAC;QACjB;QACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,EAAEc,CAAC,EAAE,EAAE;UACnCH,IAAI,GAAGA,IAAI,CAACJ,GAAG,CAACH,CAAC,CAAC;UAClBM,MAAM,CAACG,IAAI,CAACF,IAAI,CAAC;;QAEnBP,CAAC,GAAGO,IAAI,CAACH,MAAM,EAAE;;MAEnB,OAAOE,MAAM;IACf,CAAC;IAED;;;;;;;IAOAvB,IAAIA,CAACS,CAAS,EAAEmB,WAAgB,EAAEZ,CAAS;MACzC;MACA;MACA,MAAM;QAAEN,OAAO;QAAEG;MAAU,CAAE,GAAGL,IAAI,CAACC,CAAC,CAAC;MAEvC,IAAIQ,CAAC,GAAGhB,CAAC,CAACiB,IAAI;MACd,IAAIW,CAAC,GAAG5B,CAAC,CAAC6B,IAAI;MAEd,MAAMC,IAAI,GAAGjC,MAAM,CAAC,CAAC,IAAIW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACjC,MAAMuB,SAAS,GAAG,CAAC,IAAIvB,CAAC;MACxB,MAAMwB,OAAO,GAAGnC,MAAM,CAACW,CAAC,CAAC;MAEzB,KAAK,IAAIgB,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGf,OAAO,EAAEe,MAAM,EAAE,EAAE;QAC/C,MAAMS,MAAM,GAAGT,MAAM,GAAGZ,UAAU;QAClC;QACA,IAAIsB,KAAK,GAAGC,MAAM,CAACpB,CAAC,GAAGe,IAAI,CAAC;QAE5B;QACAf,CAAC,KAAKiB,OAAO;QAEb;QACA;QACA,IAAIE,KAAK,GAAGtB,UAAU,EAAE;UACtBsB,KAAK,IAAIH,SAAS;UAClBhB,CAAC,IAAIjB,GAAG;;QAGV;QACA;QACA;QACA;QACA;QAEA;QACA;QACA,MAAMsC,OAAO,GAAGH,MAAM;QACtB,MAAMI,OAAO,GAAGJ,MAAM,GAAGvB,IAAI,CAAC4B,GAAG,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9C,MAAMK,KAAK,GAAGf,MAAM,GAAG,CAAC,KAAK,CAAC;QAC9B,MAAMgB,KAAK,GAAGN,KAAK,GAAG,CAAC;QACvB,IAAIA,KAAK,KAAK,CAAC,EAAE;UACf;UACAN,CAAC,GAAGA,CAAC,CAACT,GAAG,CAACjB,eAAe,CAACqC,KAAK,EAAEZ,WAAW,CAACS,OAAO,CAAC,CAAC,CAAC;SACxD,MAAM;UACLpB,CAAC,GAAGA,CAAC,CAACG,GAAG,CAACjB,eAAe,CAACsC,KAAK,EAAEb,WAAW,CAACU,OAAO,CAAC,CAAC,CAAC;;;MAG3D;MACA;MACA;MACA;MACA;MACA,OAAO;QAAErB,CAAC;QAAEY;MAAC,CAAE;IACjB,CAAC;IAEDa,UAAUA,CAACC,CAAI,EAAEC,cAA2B,EAAE5B,CAAS,EAAE6B,SAAoB;MAC3E;MACA,MAAMpC,CAAC,GAAWkC,CAAC,CAACG,YAAY,IAAI,CAAC;MACrC;MACA,IAAIC,IAAI,GAAGH,cAAc,CAACI,GAAG,CAACL,CAAC,CAAC;MAChC,IAAI,CAACI,IAAI,EAAE;QACTA,IAAI,GAAG,IAAI,CAACzB,gBAAgB,CAACqB,CAAC,EAAElC,CAAC,CAAQ;QACzC,IAAIA,CAAC,KAAK,CAAC,EAAE;UACXmC,cAAc,CAACK,GAAG,CAACN,CAAC,EAAEE,SAAS,CAACE,IAAI,CAAC,CAAC;;;MAG1C,OAAO,IAAI,CAAC/C,IAAI,CAACS,CAAC,EAAEsC,IAAI,EAAE/B,CAAC,CAAC;IAC9B;GACD;AACH;AAgBA,OAAM,SAAUkC,aAAaA,CAAQC,KAAyB;EAC5DzD,aAAa,CAACyD,KAAK,CAACC,EAAE,CAAC;EACvBxD,cAAc,CACZuD,KAAK,EACL;IACEnC,CAAC,EAAE,QAAQ;IACXqC,CAAC,EAAE,QAAQ;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE;GACL,EACD;IACEC,UAAU,EAAE,eAAe;IAC3BC,WAAW,EAAE;GACd,CACF;EACD;EACA,OAAOC,MAAM,CAACC,MAAM,CAAC;IACnB,GAAGhE,OAAO,CAACwD,KAAK,CAACnC,CAAC,EAAEmC,KAAK,CAACK,UAAU,CAAC;IACrC,GAAGL,KAAK;IACR,GAAG;MAAElC,CAAC,EAAEkC,KAAK,CAACC,EAAE,CAACQ;IAAK;GACd,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}