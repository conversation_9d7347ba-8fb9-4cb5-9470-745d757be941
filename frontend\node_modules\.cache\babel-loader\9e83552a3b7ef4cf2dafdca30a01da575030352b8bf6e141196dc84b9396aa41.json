{"ast": null, "code": "import { Signing<PERSON><PERSON> } from \"../crypto/index.js\";\nimport { assertArgument } from \"../utils/index.js\";\nimport { BaseWallet } from \"./base-wallet.js\";\nimport { HDNodeWallet } from \"./hdwallet.js\";\nimport { decrypt<PERSON><PERSON><PERSON>ale<PERSON>son, is<PERSON><PERSON>ds<PERSON><PERSON><PERSON> } from \"./json-crowdsale.js\";\nimport { decryptKeystoreJson, decryptKeystoreJsonSync, encryptKeystoreJson, encryptKeystoreJsonSync, isKeystoreJson } from \"./json-keystore.js\";\nimport { Mnemonic } from \"./mnemonic.js\";\nfunction stall(duration) {\n  return new Promise(resolve => {\n    setTimeout(() => {\n      resolve();\n    }, duration);\n  });\n}\n/**\n *  A **Wallet** manages a single private key which is used to sign\n *  transactions, messages and other common payloads.\n *\n *  This class is generally the main entry point for developers\n *  that wish to use a private key directly, as it can create\n *  instances from a large variety of common sources, including\n *  raw private key, [[link-bip-39]] mnemonics and encrypte JSON\n *  wallets.\n */\nexport class Wallet extends BaseWallet {\n  /**\n   *  Create a new wallet for the private %%key%%, optionally connected\n   *  to %%provider%%.\n   */\n  constructor(key, provider) {\n    if (typeof key === \"string\" && !key.startsWith(\"0x\")) {\n      key = \"0x\" + key;\n    }\n    let signingKey = typeof key === \"string\" ? new SigningKey(key) : key;\n    super(signingKey, provider);\n  }\n  connect(provider) {\n    return new Wallet(this.signingKey, provider);\n  }\n  /**\n   *  Resolves to a [JSON Keystore Wallet](json-wallets) encrypted with\n   *  %%password%%.\n   *\n   *  If %%progressCallback%% is specified, it will receive periodic\n   *  updates as the encryption process progreses.\n   */\n  async encrypt(password, progressCallback) {\n    const account = {\n      address: this.address,\n      privateKey: this.privateKey\n    };\n    return await encryptKeystoreJson(account, password, {\n      progressCallback\n    });\n  }\n  /**\n   *  Returns a [JSON Keystore Wallet](json-wallets) encryped with\n   *  %%password%%.\n   *\n   *  It is preferred to use the [async version](encrypt) instead,\n   *  which allows a [[ProgressCallback]] to keep the user informed.\n   *\n   *  This method will block the event loop (freezing all UI) until\n   *  it is complete, which may be a non-trivial duration.\n   */\n  encryptSync(password) {\n    const account = {\n      address: this.address,\n      privateKey: this.privateKey\n    };\n    return encryptKeystoreJsonSync(account, password);\n  }\n  static #fromAccount(account) {\n    assertArgument(account, \"invalid JSON wallet\", \"json\", \"[ REDACTED ]\");\n    if (\"mnemonic\" in account && account.mnemonic && account.mnemonic.locale === \"en\") {\n      const mnemonic = Mnemonic.fromEntropy(account.mnemonic.entropy);\n      const wallet = HDNodeWallet.fromMnemonic(mnemonic, account.mnemonic.path);\n      if (wallet.address === account.address && wallet.privateKey === account.privateKey) {\n        return wallet;\n      }\n      console.log(\"WARNING: JSON mismatch address/privateKey != mnemonic; fallback onto private key\");\n    }\n    const wallet = new Wallet(account.privateKey);\n    assertArgument(wallet.address === account.address, \"address/privateKey mismatch\", \"json\", \"[ REDACTED ]\");\n    return wallet;\n  }\n  /**\n   *  Creates (asynchronously) a **Wallet** by decrypting the %%json%%\n   *  with %%password%%.\n   *\n   *  If %%progress%% is provided, it is called periodically during\n   *  decryption so that any UI can be updated.\n   */\n  static async fromEncryptedJson(json, password, progress) {\n    let account = null;\n    if (isKeystoreJson(json)) {\n      account = await decryptKeystoreJson(json, password, progress);\n    } else if (isCrowdsaleJson(json)) {\n      if (progress) {\n        progress(0);\n        await stall(0);\n      }\n      account = decryptCrowdsaleJson(json, password);\n      if (progress) {\n        progress(1);\n        await stall(0);\n      }\n    }\n    return Wallet.#fromAccount(account);\n  }\n  /**\n   *  Creates a **Wallet** by decrypting the %%json%% with %%password%%.\n   *\n   *  The [[fromEncryptedJson]] method is preferred, as this method\n   *  will lock up and freeze the UI during decryption, which may take\n   *  some time.\n   */\n  static fromEncryptedJsonSync(json, password) {\n    let account = null;\n    if (isKeystoreJson(json)) {\n      account = decryptKeystoreJsonSync(json, password);\n    } else if (isCrowdsaleJson(json)) {\n      account = decryptCrowdsaleJson(json, password);\n    } else {\n      assertArgument(false, \"invalid JSON wallet\", \"json\", \"[ REDACTED ]\");\n    }\n    return Wallet.#fromAccount(account);\n  }\n  /**\n   *  Creates a new random [[HDNodeWallet]] using the available\n   *  [cryptographic random source](randomBytes).\n   *\n   *  If there is no crytographic random source, this will throw.\n   */\n  static createRandom(provider) {\n    const wallet = HDNodeWallet.createRandom();\n    if (provider) {\n      return wallet.connect(provider);\n    }\n    return wallet;\n  }\n  /**\n   *  Creates a [[HDNodeWallet]] for %%phrase%%.\n   */\n  static fromPhrase(phrase, provider) {\n    const wallet = HDNodeWallet.fromPhrase(phrase);\n    if (provider) {\n      return wallet.connect(provider);\n    }\n    return wallet;\n  }\n}", "map": {"version": 3, "names": ["SigningKey", "assertArgument", "BaseWallet", "HDNodeWallet", "decryptCrowdsale<PERSON>son", "isCrowdsaleJson", "decryptKeystoreJson", "decryptKeystoreJsonSync", "encryptKeystoreJson", "encryptKeystoreJsonSync", "isKeystoreJson", "Mnemonic", "stall", "duration", "Promise", "resolve", "setTimeout", "Wallet", "constructor", "key", "provider", "startsWith", "<PERSON><PERSON><PERSON>", "connect", "encrypt", "password", "progressCallback", "account", "address", "privateKey", "encryptSync", "fromA<PERSON>unt", "#fromAccount", "mnemonic", "locale", "fromEntropy", "entropy", "wallet", "fromMnemonic", "path", "console", "log", "fromEncryptedJson", "json", "progress", "fromEncryptedJsonSync", "createRandom", "fromPhrase", "phrase"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\wallet.ts"], "sourcesContent": ["import { Signing<PERSON><PERSON> } from \"../crypto/index.js\";\nimport { assertArgument } from \"../utils/index.js\";\n\nimport { BaseWallet } from \"./base-wallet.js\";\nimport { HDNodeWallet } from \"./hdwallet.js\";\nimport { decrypt<PERSON>rowdsale<PERSON>son, isCrowdsale<PERSON>son  } from \"./json-crowdsale.js\";\nimport {\n    decryptKeystoreJson, decryptKeystoreJsonSync,\n    encryptKeystoreJson, encryptKeystoreJsonSync,\n    isKeystoreJson\n} from \"./json-keystore.js\";\nimport { Mnemonic } from \"./mnemonic.js\";\n\nimport type { ProgressCallback } from \"../crypto/index.js\";\nimport type { Provider } from \"../providers/index.js\";\n\nimport type { CrowdsaleAccount } from \"./json-crowdsale.js\";\nimport type { KeystoreAccount } from \"./json-keystore.js\";\n\n\nfunction stall(duration: number): Promise<void> {\n    return new Promise((resolve) => { setTimeout(() => { resolve(); }, duration); });\n}\n\n/**\n *  A **Wallet** manages a single private key which is used to sign\n *  transactions, messages and other common payloads.\n *\n *  This class is generally the main entry point for developers\n *  that wish to use a private key directly, as it can create\n *  instances from a large variety of common sources, including\n *  raw private key, [[link-bip-39]] mnemonics and encrypte JSON\n *  wallets.\n */\nexport class Wallet extends BaseWallet {\n\n    /**\n     *  Create a new wallet for the private %%key%%, optionally connected\n     *  to %%provider%%.\n     */\n    constructor(key: string | SigningKey, provider?: null | Provider) {\n        if (typeof(key) === \"string\" && !key.startsWith(\"0x\")) {\n            key = \"0x\" + key;\n        }\n\n        let signingKey = (typeof(key) === \"string\") ? new SigningKey(key): key;\n        super(signingKey, provider);\n    }\n\n    connect(provider: null | Provider): Wallet {\n        return new Wallet(this.signingKey, provider);\n    }\n\n    /**\n     *  Resolves to a [JSON Keystore Wallet](json-wallets) encrypted with\n     *  %%password%%.\n     *\n     *  If %%progressCallback%% is specified, it will receive periodic\n     *  updates as the encryption process progreses.\n     */\n    async encrypt(password: Uint8Array | string, progressCallback?: ProgressCallback): Promise<string> {\n        const account = { address: this.address, privateKey: this.privateKey };\n        return await encryptKeystoreJson(account, password, { progressCallback });\n    }\n\n    /**\n     *  Returns a [JSON Keystore Wallet](json-wallets) encryped with\n     *  %%password%%.\n     *\n     *  It is preferred to use the [async version](encrypt) instead,\n     *  which allows a [[ProgressCallback]] to keep the user informed.\n     *\n     *  This method will block the event loop (freezing all UI) until\n     *  it is complete, which may be a non-trivial duration.\n     */\n    encryptSync(password: Uint8Array | string): string {\n        const account = { address: this.address, privateKey: this.privateKey };\n        return encryptKeystoreJsonSync(account, password);\n    }\n\n    static #fromAccount(account: null | CrowdsaleAccount | KeystoreAccount): HDNodeWallet | Wallet {\n        assertArgument(account, \"invalid JSON wallet\", \"json\", \"[ REDACTED ]\");\n\n        if (\"mnemonic\" in account && account.mnemonic && account.mnemonic.locale === \"en\") {\n            const mnemonic = Mnemonic.fromEntropy(account.mnemonic.entropy);\n            const wallet = HDNodeWallet.fromMnemonic(mnemonic, account.mnemonic.path);\n            if (wallet.address === account.address && wallet.privateKey === account.privateKey) {\n                return wallet;\n            }\n            console.log(\"WARNING: JSON mismatch address/privateKey != mnemonic; fallback onto private key\");\n        }\n\n        const wallet = new Wallet(account.privateKey);\n\n        assertArgument(wallet.address === account.address,\n            \"address/privateKey mismatch\", \"json\", \"[ REDACTED ]\");\n\n        return wallet;\n    }\n\n    /**\n     *  Creates (asynchronously) a **Wallet** by decrypting the %%json%%\n     *  with %%password%%.\n     *\n     *  If %%progress%% is provided, it is called periodically during\n     *  decryption so that any UI can be updated.\n     */\n    static async fromEncryptedJson(json: string, password: Uint8Array | string, progress?: ProgressCallback): Promise<HDNodeWallet | Wallet> {\n        let account: null | CrowdsaleAccount | KeystoreAccount = null;\n        if (isKeystoreJson(json)) {\n            account = await decryptKeystoreJson(json, password, progress);\n\n        } else if (isCrowdsaleJson(json)) {\n            if (progress) { progress(0); await stall(0); }\n            account = decryptCrowdsaleJson(json, password);\n            if (progress) { progress(1); await stall(0); }\n\n        }\n\n        return Wallet.#fromAccount(account);\n    }\n\n    /**\n     *  Creates a **Wallet** by decrypting the %%json%% with %%password%%.\n     *\n     *  The [[fromEncryptedJson]] method is preferred, as this method\n     *  will lock up and freeze the UI during decryption, which may take\n     *  some time.\n     */\n    static fromEncryptedJsonSync(json: string, password: Uint8Array | string): HDNodeWallet | Wallet {\n        let account: null | CrowdsaleAccount | KeystoreAccount = null;\n        if (isKeystoreJson(json)) {\n            account = decryptKeystoreJsonSync(json, password);\n        } else if (isCrowdsaleJson(json)) {\n            account = decryptCrowdsaleJson(json, password);\n        } else {\n            assertArgument(false, \"invalid JSON wallet\", \"json\", \"[ REDACTED ]\");\n        }\n\n        return Wallet.#fromAccount(account);\n    }\n\n    /**\n     *  Creates a new random [[HDNodeWallet]] using the available\n     *  [cryptographic random source](randomBytes).\n     *\n     *  If there is no crytographic random source, this will throw.\n     */\n    static createRandom(provider?: null | Provider): HDNodeWallet {\n        const wallet = HDNodeWallet.createRandom();\n        if (provider) { return wallet.connect(provider); }\n        return wallet;\n    }\n\n    /**\n     *  Creates a [[HDNodeWallet]] for %%phrase%%.\n     */\n    static fromPhrase(phrase: string, provider?: Provider): HDNodeWallet {\n        const wallet = HDNodeWallet.fromPhrase(phrase);\n        if (provider) { return wallet.connect(provider); }\n        return wallet;\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,cAAc,QAAQ,mBAAmB;AAElD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,oBAAoB,EAAEC,eAAe,QAAS,qBAAqB;AAC5E,SACIC,mBAAmB,EAAEC,uBAAuB,EAC5CC,mBAAmB,EAAEC,uBAAuB,EAC5CC,cAAc,QACX,oBAAoB;AAC3B,SAASC,QAAQ,QAAQ,eAAe;AASxC,SAASC,KAAKA,CAACC,QAAgB;EAC3B,OAAO,IAAIC,OAAO,CAAEC,OAAO,IAAI;IAAGC,UAAU,CAAC,MAAK;MAAGD,OAAO,EAAE;IAAE,CAAC,EAAEF,QAAQ,CAAC;EAAE,CAAC,CAAC;AACpF;AAEA;;;;;;;;;;AAUA,OAAM,MAAOI,MAAO,SAAQf,UAAU;EAElC;;;;EAIAgB,YAAYC,GAAwB,EAAEC,QAA0B;IAC5D,IAAI,OAAOD,GAAI,KAAK,QAAQ,IAAI,CAACA,GAAG,CAACE,UAAU,CAAC,IAAI,CAAC,EAAE;MACnDF,GAAG,GAAG,IAAI,GAAGA,GAAG;;IAGpB,IAAIG,UAAU,GAAI,OAAOH,GAAI,KAAK,QAAQ,GAAI,IAAInB,UAAU,CAACmB,GAAG,CAAC,GAAEA,GAAG;IACtE,KAAK,CAACG,UAAU,EAAEF,QAAQ,CAAC;EAC/B;EAEAG,OAAOA,CAACH,QAAyB;IAC7B,OAAO,IAAIH,MAAM,CAAC,IAAI,CAACK,UAAU,EAAEF,QAAQ,CAAC;EAChD;EAEA;;;;;;;EAOA,MAAMI,OAAOA,CAACC,QAA6B,EAAEC,gBAAmC;IAC5E,MAAMC,OAAO,GAAG;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE;IACtE,OAAO,MAAMrB,mBAAmB,CAACmB,OAAO,EAAEF,QAAQ,EAAE;MAAEC;IAAgB,CAAE,CAAC;EAC7E;EAEA;;;;;;;;;;EAUAI,WAAWA,CAACL,QAA6B;IACrC,MAAME,OAAO,GAAG;MAAEC,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEC,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE;IACtE,OAAOpB,uBAAuB,CAACkB,OAAO,EAAEF,QAAQ,CAAC;EACrD;EAEA,OAAO,CAAAM,WAAYC,CAACL,OAAkD;IAClE1B,cAAc,CAAC0B,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAE,cAAc,CAAC;IAEtE,IAAI,UAAU,IAAIA,OAAO,IAAIA,OAAO,CAACM,QAAQ,IAAIN,OAAO,CAACM,QAAQ,CAACC,MAAM,KAAK,IAAI,EAAE;MAC/E,MAAMD,QAAQ,GAAGtB,QAAQ,CAACwB,WAAW,CAACR,OAAO,CAACM,QAAQ,CAACG,OAAO,CAAC;MAC/D,MAAMC,MAAM,GAAGlC,YAAY,CAACmC,YAAY,CAACL,QAAQ,EAAEN,OAAO,CAACM,QAAQ,CAACM,IAAI,CAAC;MACzE,IAAIF,MAAM,CAACT,OAAO,KAAKD,OAAO,CAACC,OAAO,IAAIS,MAAM,CAACR,UAAU,KAAKF,OAAO,CAACE,UAAU,EAAE;QAChF,OAAOQ,MAAM;;MAEjBG,OAAO,CAACC,GAAG,CAAC,kFAAkF,CAAC;;IAGnG,MAAMJ,MAAM,GAAG,IAAIpB,MAAM,CAACU,OAAO,CAACE,UAAU,CAAC;IAE7C5B,cAAc,CAACoC,MAAM,CAACT,OAAO,KAAKD,OAAO,CAACC,OAAO,EAC7C,6BAA6B,EAAE,MAAM,EAAE,cAAc,CAAC;IAE1D,OAAOS,MAAM;EACjB;EAEA;;;;;;;EAOA,aAAaK,iBAAiBA,CAACC,IAAY,EAAElB,QAA6B,EAAEmB,QAA2B;IACnG,IAAIjB,OAAO,GAA8C,IAAI;IAC7D,IAAIjB,cAAc,CAACiC,IAAI,CAAC,EAAE;MACtBhB,OAAO,GAAG,MAAMrB,mBAAmB,CAACqC,IAAI,EAAElB,QAAQ,EAAEmB,QAAQ,CAAC;KAEhE,MAAM,IAAIvC,eAAe,CAACsC,IAAI,CAAC,EAAE;MAC9B,IAAIC,QAAQ,EAAE;QAAEA,QAAQ,CAAC,CAAC,CAAC;QAAE,MAAMhC,KAAK,CAAC,CAAC,CAAC;;MAC3Ce,OAAO,GAAGvB,oBAAoB,CAACuC,IAAI,EAAElB,QAAQ,CAAC;MAC9C,IAAImB,QAAQ,EAAE;QAAEA,QAAQ,CAAC,CAAC,CAAC;QAAE,MAAMhC,KAAK,CAAC,CAAC,CAAC;;;IAI/C,OAAOK,MAAM,CAAC,CAAAc,WAAY,CAACJ,OAAO,CAAC;EACvC;EAEA;;;;;;;EAOA,OAAOkB,qBAAqBA,CAACF,IAAY,EAAElB,QAA6B;IACpE,IAAIE,OAAO,GAA8C,IAAI;IAC7D,IAAIjB,cAAc,CAACiC,IAAI,CAAC,EAAE;MACtBhB,OAAO,GAAGpB,uBAAuB,CAACoC,IAAI,EAAElB,QAAQ,CAAC;KACpD,MAAM,IAAIpB,eAAe,CAACsC,IAAI,CAAC,EAAE;MAC9BhB,OAAO,GAAGvB,oBAAoB,CAACuC,IAAI,EAAElB,QAAQ,CAAC;KACjD,MAAM;MACHxB,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,MAAM,EAAE,cAAc,CAAC;;IAGxE,OAAOgB,MAAM,CAAC,CAAAc,WAAY,CAACJ,OAAO,CAAC;EACvC;EAEA;;;;;;EAMA,OAAOmB,YAAYA,CAAC1B,QAA0B;IAC1C,MAAMiB,MAAM,GAAGlC,YAAY,CAAC2C,YAAY,EAAE;IAC1C,IAAI1B,QAAQ,EAAE;MAAE,OAAOiB,MAAM,CAACd,OAAO,CAACH,QAAQ,CAAC;;IAC/C,OAAOiB,MAAM;EACjB;EAEA;;;EAGA,OAAOU,UAAUA,CAACC,MAAc,EAAE5B,QAAmB;IACjD,MAAMiB,MAAM,GAAGlC,YAAY,CAAC4C,UAAU,CAACC,MAAM,CAAC;IAC9C,IAAI5B,QAAQ,EAAE;MAAE,OAAOiB,MAAM,CAACd,OAAO,CAACH,QAAQ,CAAC;;IAC/C,OAAOiB,MAAM;EACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}