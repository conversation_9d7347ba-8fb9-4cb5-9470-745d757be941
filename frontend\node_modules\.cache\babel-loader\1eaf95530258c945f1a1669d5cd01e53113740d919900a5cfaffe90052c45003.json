{"ast": null, "code": "import { LangEn } from \"./lang-en.js\";\nexport const wordlists = {\n  en: LangEn.wordlist()\n};", "map": {"version": 3, "names": ["LangEn", "wordlists", "en", "wordlist"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\wordlists-browser.ts"], "sourcesContent": ["\nimport { LangEn } from \"./lang-en.js\";\n\nimport type { Wordlist } from \"./wordlist.js\";\n\nexport const wordlists: Record<string, Wordlist> = {\n  en: LangEn.wordlist(),\n};\n"], "mappings": "AACA,SAASA,MAAM,QAAQ,cAAc;AAIrC,OAAO,MAAMC,SAAS,GAA6B;EACjDC,EAAE,EAAEF,MAAM,CAACG,QAAQ;CACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}