{"ast": null, "code": "import { getAddress } from \"../address/index.js\";\nimport { assertArgument, isHexString } from \"../utils/index.js\";\nfunction accessSetify(addr, storageKeys) {\n  return {\n    address: getAddress(addr),\n    storageKeys: storageKeys.map((storageKey, index) => {\n      assertArgument(isHexString(storageKey, 32), \"invalid slot\", `storageKeys[${index}]`, storageKey);\n      return storageKey.toLowerCase();\n    })\n  };\n}\n/**\n *  Returns a [[AccessList]] from any ethers-supported access-list structure.\n */\nexport function accessListify(value) {\n  if (Array.isArray(value)) {\n    return value.map((set, index) => {\n      if (Array.isArray(set)) {\n        assertArgument(set.length === 2, \"invalid slot set\", `value[${index}]`, set);\n        return accessSetify(set[0], set[1]);\n      }\n      assertArgument(set != null && typeof set === \"object\", \"invalid address-slot set\", \"value\", value);\n      return accessSetify(set.address, set.storageKeys);\n    });\n  }\n  assertArgument(value != null && typeof value === \"object\", \"invalid access list\", \"value\", value);\n  const result = Object.keys(value).map(addr => {\n    const storageKeys = value[addr].reduce((accum, storageKey) => {\n      accum[storageKey] = true;\n      return accum;\n    }, {});\n    return accessSetify(addr, Object.keys(storageKeys).sort());\n  });\n  result.sort((a, b) => a.address.localeCompare(b.address));\n  return result;\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "assertArgument", "isHexString", "accessSetify", "addr", "storageKeys", "address", "map", "storageKey", "index", "toLowerCase", "accessListify", "value", "Array", "isArray", "set", "length", "result", "Object", "keys", "reduce", "accum", "sort", "a", "b", "localeCompare"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\transaction\\accesslist.ts"], "sourcesContent": ["import { getAddress } from \"../address/index.js\";\nimport { assertArgument, isHexString } from \"../utils/index.js\";\n\nimport type { AccessList, AccessListish } from \"./index.js\";\n\n\nfunction accessSetify(addr: string, storageKeys: Array<string>): { address: string,storageKeys: Array<string> } {\n    return {\n        address: getAddress(addr),\n        storageKeys: storageKeys.map((storageKey, index) => {\n            assertArgument(isHexString(storageKey, 32), \"invalid slot\", `storageKeys[${ index }]`, storageKey);\n            return storageKey.toLowerCase();\n        })\n    };\n}\n\n/**\n *  Returns a [[AccessList]] from any ethers-supported access-list structure.\n */\nexport function accessListify(value: AccessListish): AccessList {\n    if (Array.isArray(value)) {\n        return (<Array<[ string, Array<string>] | { address: string, storageKeys: Array<string>}>>value).map((set, index) => {\n            if (Array.isArray(set)) {\n                assertArgument(set.length === 2, \"invalid slot set\", `value[${ index }]`, set);\n                return accessSetify(set[0], set[1])\n            }\n            assertArgument(set != null && typeof(set) === \"object\", \"invalid address-slot set\", \"value\", value);\n            return accessSetify(set.address, set.storageKeys);\n        });\n    }\n\n    assertArgument(value != null && typeof(value) === \"object\", \"invalid access list\", \"value\", value);\n\n    const result: Array<{ address: string, storageKeys: Array<string> }> = Object.keys(value).map((addr) => {\n        const storageKeys: Record<string, true> = value[addr].reduce((accum, storageKey) => {\n            accum[storageKey] = true;\n            return accum;\n        }, <Record<string, true>>{ });\n        return accessSetify(addr, Object.keys(storageKeys).sort())\n    });\n    result.sort((a, b) => (a.address.localeCompare(b.address)));\n    return result;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,qBAAqB;AAChD,SAASC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAK/D,SAASC,YAAYA,CAACC,IAAY,EAAEC,WAA0B;EAC1D,OAAO;IACHC,OAAO,EAAEN,UAAU,CAACI,IAAI,CAAC;IACzBC,WAAW,EAAEA,WAAW,CAACE,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAI;MAC/CR,cAAc,CAACC,WAAW,CAACM,UAAU,EAAE,EAAE,CAAC,EAAE,cAAc,EAAE,eAAgBC,KAAM,GAAG,EAAED,UAAU,CAAC;MAClG,OAAOA,UAAU,CAACE,WAAW,EAAE;IACnC,CAAC;GACJ;AACL;AAEA;;;AAGA,OAAM,SAAUC,aAAaA,CAACC,KAAoB;EAC9C,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACtB,OAA0FA,KAAM,CAACL,GAAG,CAAC,CAACQ,GAAG,EAAEN,KAAK,KAAI;MAChH,IAAII,KAAK,CAACC,OAAO,CAACC,GAAG,CAAC,EAAE;QACpBd,cAAc,CAACc,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE,kBAAkB,EAAE,SAAUP,KAAM,GAAG,EAAEM,GAAG,CAAC;QAC9E,OAAOZ,YAAY,CAACY,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEvCd,cAAc,CAACc,GAAG,IAAI,IAAI,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE,0BAA0B,EAAE,OAAO,EAAEH,KAAK,CAAC;MACnG,OAAOT,YAAY,CAACY,GAAG,CAACT,OAAO,EAAES,GAAG,CAACV,WAAW,CAAC;IACrD,CAAC,CAAC;;EAGNJ,cAAc,CAACW,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE,qBAAqB,EAAE,OAAO,EAAEA,KAAK,CAAC;EAElG,MAAMK,MAAM,GAA2DC,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC,CAACL,GAAG,CAAEH,IAAI,IAAI;IACnG,MAAMC,WAAW,GAAyBO,KAAK,CAACR,IAAI,CAAC,CAACgB,MAAM,CAAC,CAACC,KAAK,EAAEb,UAAU,KAAI;MAC/Ea,KAAK,CAACb,UAAU,CAAC,GAAG,IAAI;MACxB,OAAOa,KAAK;IAChB,CAAC,EAAwB,EAAG,CAAC;IAC7B,OAAOlB,YAAY,CAACC,IAAI,EAAEc,MAAM,CAACC,IAAI,CAACd,WAAW,CAAC,CAACiB,IAAI,EAAE,CAAC;EAC9D,CAAC,CAAC;EACFL,MAAM,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACjB,OAAO,CAACmB,aAAa,CAACD,CAAC,CAAClB,OAAO,CAAE,CAAC;EAC3D,OAAOW,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}