{"ast": null, "code": "/**\n *  [[link-ankr]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB (``bnb``)\n *  - BNB Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:Ankr  [providers-ankr]\n */\nimport { defineProperties, FetchRequest, assertArgument } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nconst defaultApiKey = \"9f7d929b018cdffb338517efa06f58359e86ff1ffd350bc889738523659e7972\";\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"rpc.ankr.com/eth\";\n    case \"goerli\":\n      return \"rpc.ankr.com/eth_goerli\";\n    case \"sepolia\":\n      return \"rpc.ankr.com/eth_sepolia\";\n    case \"arbitrum\":\n      return \"rpc.ankr.com/arbitrum\";\n    case \"base\":\n      return \"rpc.ankr.com/base\";\n    case \"base-goerli\":\n      return \"rpc.ankr.com/base_goerli\";\n    case \"base-sepolia\":\n      return \"rpc.ankr.com/base_sepolia\";\n    case \"bnb\":\n      return \"rpc.ankr.com/bsc\";\n    case \"bnbt\":\n      return \"rpc.ankr.com/bsc_testnet_chapel\";\n    case \"matic\":\n      return \"rpc.ankr.com/polygon\";\n    case \"matic-mumbai\":\n      return \"rpc.ankr.com/polygon_mumbai\";\n    case \"optimism\":\n      return \"rpc.ankr.com/optimism\";\n    case \"optimism-goerli\":\n      return \"rpc.ankr.com/optimism_testnet\";\n    case \"optimism-sepolia\":\n      return \"rpc.ankr.com/optimism_sepolia\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **AnkrProvider** connects to the [[link-ankr]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-ankr-signup).\n */\nexport class AnkrProvider extends JsonRpcProvider {\n  /**\n   *  The API key for the Ankr connection.\n   */\n  apiKey;\n  /**\n   *  Create a new **AnkrProvider**.\n   *\n   *  By default connecting to ``mainnet`` with a highly throttled\n   *  API key.\n   */\n  constructor(_network, apiKey) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (apiKey == null) {\n      apiKey = defaultApiKey;\n    }\n    // Ankr does not support filterId, so we force polling\n    const options = {\n      polling: true,\n      staticNetwork: network\n    };\n    const request = AnkrProvider.getRequest(network, apiKey);\n    super(request, network, options);\n    defineProperties(this, {\n      apiKey\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new AnkrProvider(chainId, this.apiKey);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  /**\n   *  Returns a prepared request for connecting to %%network%% with\n   *  %%apiKey%%.\n   */\n  static getRequest(network, apiKey) {\n    if (apiKey == null) {\n      apiKey = defaultApiKey;\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/${apiKey}`);\n    request.allowGzip = true;\n    if (apiKey === defaultApiKey) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"AnkrProvider\");\n        return true;\n      };\n    }\n    return request;\n  }\n  getRpcError(payload, error) {\n    if (payload.method === \"eth_sendRawTransaction\") {\n      if (error && error.error && error.error.message === \"INTERNAL_ERROR: could not replace existing tx\") {\n        error.error.message = \"replacement transaction underpriced\";\n      }\n    }\n    return super.getRpcError(payload, error);\n  }\n  isCommunityResource() {\n    return this.apiKey === defaultApiKey;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "FetchRequest", "assertArgument", "showThrottleMessage", "Network", "JsonRpcProvider", "defaultApiKey", "getHost", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_network", "network", "from", "options", "polling", "staticNetwork", "request", "getRequest", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "allowGzip", "retryFunc", "response", "attempt", "getRpcError", "payload", "method", "message", "isCommunityResource"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-ankr.ts"], "sourcesContent": ["/**\n *  [[link-ankr]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB (``bnb``)\n *  - BNB Testnet (``bnbt``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:Ankr  [providers-ankr]\n */\nimport {\n    defineProperties, FetchRequest, assertArgument\n} from \"../utils/index.js\";\n\nimport { AbstractProvider } from \"./abstract-provider.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\nimport type { JsonRpcError, JsonRpcPayload } from \"./provider-jsonrpc.js\";\n\n\nconst defaultApiKey = \"9f7d929b018cdffb338517efa06f58359e86ff1ffd350bc889738523659e7972\";\n\nfunction getHost(name: string): string {\n    switch (name) {\n        case \"mainnet\":\n            return \"rpc.ankr.com/eth\";\n        case \"goerli\":\n            return \"rpc.ankr.com/eth_goerli\";\n        case \"sepolia\":\n            return \"rpc.ankr.com/eth_sepolia\";\n\n        case \"arbitrum\":\n            return \"rpc.ankr.com/arbitrum\";\n        case \"base\":\n            return \"rpc.ankr.com/base\";\n        case \"base-goerli\":\n            return \"rpc.ankr.com/base_goerli\";\n        case \"base-sepolia\":\n            return \"rpc.ankr.com/base_sepolia\";\n        case \"bnb\":\n            return \"rpc.ankr.com/bsc\";\n        case \"bnbt\":\n            return \"rpc.ankr.com/bsc_testnet_chapel\";\n        case \"matic\":\n            return \"rpc.ankr.com/polygon\";\n        case \"matic-mumbai\":\n            return \"rpc.ankr.com/polygon_mumbai\";\n        case \"optimism\":\n            return \"rpc.ankr.com/optimism\";\n        case \"optimism-goerli\":\n            return \"rpc.ankr.com/optimism_testnet\";\n        case \"optimism-sepolia\":\n            return \"rpc.ankr.com/optimism_sepolia\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n\n/**\n *  The **AnkrProvider** connects to the [[link-ankr]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-ankr-signup).\n */\nexport class AnkrProvider extends JsonRpcProvider implements CommunityResourcable {\n\n    /**\n     *  The API key for the Ankr connection.\n     */\n    readonly apiKey!: string;\n\n    /**\n     *  Create a new **AnkrProvider**.\n     *\n     *  By default connecting to ``mainnet`` with a highly throttled\n     *  API key.\n     */\n    constructor(_network?: Networkish, apiKey?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        if (apiKey == null) { apiKey = defaultApiKey; }\n\n        // Ankr does not support filterId, so we force polling\n        const options = { polling: true, staticNetwork: network };\n\n        const request = AnkrProvider.getRequest(network, apiKey);\n        super(request, network, options);\n\n        defineProperties<AnkrProvider>(this, { apiKey });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new AnkrProvider(chainId, this.apiKey);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    /**\n     *  Returns a prepared request for connecting to %%network%% with\n     *  %%apiKey%%.\n     */\n    static getRequest(network: Network, apiKey?: null | string): FetchRequest {\n        if (apiKey == null) { apiKey = defaultApiKey; }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/${ apiKey }`);\n        request.allowGzip = true;\n\n        if (apiKey === defaultApiKey) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"AnkrProvider\");\n                return true;\n            };\n        }\n\n        return request;\n    }\n\n    getRpcError(payload: JsonRpcPayload, error: JsonRpcError): Error {\n        if (payload.method === \"eth_sendRawTransaction\") {\n            if (error && error.error && error.error.message === \"INTERNAL_ERROR: could not replace existing tx\") {\n                error.error.message = \"replacement transaction underpriced\";\n            }\n        }\n\n        return super.getRpcError(payload, error);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.apiKey === defaultApiKey);\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;AAuBA,SACIA,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,QAC3C,mBAAmB;AAG1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAOvD,MAAMC,aAAa,GAAG,kEAAkE;AAExF,SAASC,OAAOA,CAACC,IAAY;EACzB,QAAQA,IAAI;IACR,KAAK,SAAS;MACV,OAAO,kBAAkB;IAC7B,KAAK,QAAQ;MACT,OAAO,yBAAyB;IACpC,KAAK,SAAS;MACV,OAAO,0BAA0B;IAErC,KAAK,UAAU;MACX,OAAO,uBAAuB;IAClC,KAAK,MAAM;MACP,OAAO,mBAAmB;IAC9B,KAAK,aAAa;MACd,OAAO,0BAA0B;IACrC,KAAK,cAAc;MACf,OAAO,2BAA2B;IACtC,KAAK,KAAK;MACN,OAAO,kBAAkB;IAC7B,KAAK,MAAM;MACP,OAAO,iCAAiC;IAC5C,KAAK,OAAO;MACR,OAAO,sBAAsB;IACjC,KAAK,cAAc;MACf,OAAO,6BAA6B;IACxC,KAAK,UAAU;MACX,OAAO,uBAAuB;IAClC,KAAK,iBAAiB;MAClB,OAAO,+BAA+B;IAC1C,KAAK,kBAAkB;MACnB,OAAO,+BAA+B;;EAG9CN,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEM,IAAI,CAAC;AACjE;AAGA;;;;;;;;;AASA,OAAM,MAAOC,YAAa,SAAQJ,eAAe;EAE7C;;;EAGSK,MAAM;EAEf;;;;;;EAMAC,YAAYC,QAAqB,EAAEF,MAAsB;IACrD,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGT,OAAO,CAACU,IAAI,CAACF,QAAQ,CAAC;IACtC,IAAIF,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,aAAa;;IAE5C;IACA,MAAMS,OAAO,GAAG;MAAEC,OAAO,EAAE,IAAI;MAAEC,aAAa,EAAEJ;IAAO,CAAE;IAEzD,MAAMK,OAAO,GAAGT,YAAY,CAACU,UAAU,CAACN,OAAO,EAAEH,MAAM,CAAC;IACxD,KAAK,CAACQ,OAAO,EAAEL,OAAO,EAAEE,OAAO,CAAC;IAEhCf,gBAAgB,CAAe,IAAI,EAAE;MAAEU;IAAM,CAAE,CAAC;EACpD;EAEAU,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIZ,YAAY,CAACY,OAAO,EAAE,IAAI,CAACX,MAAM,CAAC;KAChD,CAAC,OAAOY,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEA;;;;EAIA,OAAOF,UAAUA,CAACN,OAAgB,EAAEH,MAAsB;IACtD,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAGJ,aAAa;;IAE5C,MAAMY,OAAO,GAAG,IAAIjB,YAAY,CAAC,YAAaM,OAAO,CAACM,OAAO,CAACL,IAAI,CAAE,IAAKE,MAAO,EAAE,CAAC;IACnFQ,OAAO,CAACK,SAAS,GAAG,IAAI;IAExB,IAAIb,MAAM,KAAKJ,aAAa,EAAE;MAC1BY,OAAO,CAACM,SAAS,GAAG,OAAON,OAAO,EAAEO,QAAQ,EAAEC,OAAO,KAAI;QACrDvB,mBAAmB,CAAC,cAAc,CAAC;QACnC,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOe,OAAO;EAClB;EAEAS,WAAWA,CAACC,OAAuB,EAAEN,KAAmB;IACpD,IAAIM,OAAO,CAACC,MAAM,KAAK,wBAAwB,EAAE;MAC7C,IAAIP,KAAK,IAAIA,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAACQ,OAAO,KAAK,+CAA+C,EAAE;QACjGR,KAAK,CAACA,KAAK,CAACQ,OAAO,GAAG,qCAAqC;;;IAInE,OAAO,KAAK,CAACH,WAAW,CAACC,OAAO,EAAEN,KAAK,CAAC;EAC5C;EAEAS,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACrB,MAAM,KAAKJ,aAAa;EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}