{"ast": null, "code": "import { isString } from '../utils.mjs';\nimport { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\nconst color = {\n  test: v => rgba.test(v) || hex.test(v) || hsla.test(v),\n  parse: v => {\n    if (rgba.test(v)) {\n      return rgba.parse(v);\n    } else if (hsla.test(v)) {\n      return hsla.parse(v);\n    } else {\n      return hex.parse(v);\n    }\n  },\n  transform: v => {\n    return isString(v) ? v : v.hasOwnProperty(\"red\") ? rgba.transform(v) : hsla.transform(v);\n  }\n};\nexport { color };", "map": {"version": 3, "names": ["isString", "hex", "hsla", "rgba", "color", "test", "v", "parse", "transform", "hasOwnProperty"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/value/types/color/index.mjs"], "sourcesContent": ["import { isString } from '../utils.mjs';\nimport { hex } from './hex.mjs';\nimport { hsla } from './hsla.mjs';\nimport { rgba } from './rgba.mjs';\n\nconst color = {\n    test: (v) => rgba.test(v) || hex.test(v) || hsla.test(v),\n    parse: (v) => {\n        if (rgba.test(v)) {\n            return rgba.parse(v);\n        }\n        else if (hsla.test(v)) {\n            return hsla.parse(v);\n        }\n        else {\n            return hex.parse(v);\n        }\n    },\n    transform: (v) => {\n        return isString(v)\n            ? v\n            : v.hasOwnProperty(\"red\")\n                ? rgba.transform(v)\n                : hsla.transform(v);\n    },\n};\n\nexport { color };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,GAAG,QAAQ,WAAW;AAC/B,SAASC,IAAI,QAAQ,YAAY;AACjC,SAASC,IAAI,QAAQ,YAAY;AAEjC,MAAMC,KAAK,GAAG;EACVC,IAAI,EAAGC,CAAC,IAAKH,IAAI,CAACE,IAAI,CAACC,CAAC,CAAC,IAAIL,GAAG,CAACI,IAAI,CAACC,CAAC,CAAC,IAAIJ,IAAI,CAACG,IAAI,CAACC,CAAC,CAAC;EACxDC,KAAK,EAAGD,CAAC,IAAK;IACV,IAAIH,IAAI,CAACE,IAAI,CAACC,CAAC,CAAC,EAAE;MACd,OAAOH,IAAI,CAACI,KAAK,CAACD,CAAC,CAAC;IACxB,CAAC,MACI,IAAIJ,IAAI,CAACG,IAAI,CAACC,CAAC,CAAC,EAAE;MACnB,OAAOJ,IAAI,CAACK,KAAK,CAACD,CAAC,CAAC;IACxB,CAAC,MACI;MACD,OAAOL,GAAG,CAACM,KAAK,CAACD,CAAC,CAAC;IACvB;EACJ,CAAC;EACDE,SAAS,EAAGF,CAAC,IAAK;IACd,OAAON,QAAQ,CAACM,CAAC,CAAC,GACZA,CAAC,GACDA,CAAC,CAACG,cAAc,CAAC,KAAK,CAAC,GACnBN,IAAI,CAACK,SAAS,CAACF,CAAC,CAAC,GACjBJ,IAAI,CAACM,SAAS,CAACF,CAAC,CAAC;EAC/B;AACJ,CAAC;AAED,SAASF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}