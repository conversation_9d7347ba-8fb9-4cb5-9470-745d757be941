{"ast": null, "code": "//import { resolveAddress } from \"@ethersproject/address\";\nimport { defineProperties, getBigInt, getNumber, hexlify, isBytesLike, resolveProperties, assert, assertArgument, isError, makeError } from \"../utils/index.js\";\nimport { accessListify } from \"../transaction/index.js\";\nconst BN_0 = BigInt(0);\n// -----------------------\nfunction getValue(value) {\n  if (value == null) {\n    return null;\n  }\n  return value;\n}\nfunction toJson(value) {\n  if (value == null) {\n    return null;\n  }\n  return value.toString();\n}\n// @TODO? <T extends FeeData = { }> implements Required<T>\n/**\n *  A **FeeData** wraps all the fee-related values associated with\n *  the network.\n */\nexport class FeeData {\n  /**\n   *  The gas price for legacy networks.\n   */\n  gasPrice;\n  /**\n   *  The maximum fee to pay per gas.\n   *\n   *  The base fee per gas is defined by the network and based on\n   *  congestion, increasing the cost during times of heavy load\n   *  and lowering when less busy.\n   *\n   *  The actual fee per gas will be the base fee for the block\n   *  and the priority fee, up to the max fee per gas.\n   *\n   *  This will be ``null`` on legacy networks (i.e. [pre-EIP-1559](link-eip-1559))\n   */\n  maxFeePerGas;\n  /**\n   *  The additional amout to pay per gas to encourage a validator\n   *  to include the transaction.\n   *\n   *  The purpose of this is to compensate the validator for the\n   *  adjusted risk for including a given transaction.\n   *\n   *  This will be ``null`` on legacy networks (i.e. [pre-EIP-1559](link-eip-1559))\n   */\n  maxPriorityFeePerGas;\n  /**\n   *  Creates a new FeeData for %%gasPrice%%, %%maxFeePerGas%% and\n   *  %%maxPriorityFeePerGas%%.\n   */\n  constructor(gasPrice, maxFeePerGas, maxPriorityFeePerGas) {\n    defineProperties(this, {\n      gasPrice: getValue(gasPrice),\n      maxFeePerGas: getValue(maxFeePerGas),\n      maxPriorityFeePerGas: getValue(maxPriorityFeePerGas)\n    });\n  }\n  /**\n   *  Returns a JSON-friendly value.\n   */\n  toJSON() {\n    const {\n      gasPrice,\n      maxFeePerGas,\n      maxPriorityFeePerGas\n    } = this;\n    return {\n      _type: \"FeeData\",\n      gasPrice: toJson(gasPrice),\n      maxFeePerGas: toJson(maxFeePerGas),\n      maxPriorityFeePerGas: toJson(maxPriorityFeePerGas)\n    };\n  }\n}\n;\n/**\n *  Returns a copy of %%req%% with all properties coerced to their strict\n *  types.\n */\nexport function copyRequest(req) {\n  const result = {};\n  // These could be addresses, ENS names or Addressables\n  if (req.to) {\n    result.to = req.to;\n  }\n  if (req.from) {\n    result.from = req.from;\n  }\n  if (req.data) {\n    result.data = hexlify(req.data);\n  }\n  const bigIntKeys = \"chainId,gasLimit,gasPrice,maxFeePerBlobGas,maxFeePerGas,maxPriorityFeePerGas,value\".split(/,/);\n  for (const key of bigIntKeys) {\n    if (!(key in req) || req[key] == null) {\n      continue;\n    }\n    result[key] = getBigInt(req[key], `request.${key}`);\n  }\n  const numberKeys = \"type,nonce\".split(/,/);\n  for (const key of numberKeys) {\n    if (!(key in req) || req[key] == null) {\n      continue;\n    }\n    result[key] = getNumber(req[key], `request.${key}`);\n  }\n  if (req.accessList) {\n    result.accessList = accessListify(req.accessList);\n  }\n  if (req.authorizationList) {\n    result.authorizationList = req.authorizationList.slice();\n  }\n  if (\"blockTag\" in req) {\n    result.blockTag = req.blockTag;\n  }\n  if (\"enableCcipRead\" in req) {\n    result.enableCcipRead = !!req.enableCcipRead;\n  }\n  if (\"customData\" in req) {\n    result.customData = req.customData;\n  }\n  if (\"blobVersionedHashes\" in req && req.blobVersionedHashes) {\n    result.blobVersionedHashes = req.blobVersionedHashes.slice();\n  }\n  if (\"kzg\" in req) {\n    result.kzg = req.kzg;\n  }\n  if (\"blobs\" in req && req.blobs) {\n    result.blobs = req.blobs.map(b => {\n      if (isBytesLike(b)) {\n        return hexlify(b);\n      }\n      return Object.assign({}, b);\n    });\n  }\n  return result;\n}\n/**\n *  A **Block** represents the data associated with a full block on\n *  Ethereum.\n */\nexport class Block {\n  /**\n   *  The provider connected to the block used to fetch additional details\n   *  if necessary.\n   */\n  provider;\n  /**\n   *  The block number, sometimes called the block height. This is a\n   *  sequential number that is one higher than the parent block.\n   */\n  number;\n  /**\n   *  The block hash.\n   *\n   *  This hash includes all properties, so can be safely used to identify\n   *  an exact set of block properties.\n   */\n  hash;\n  /**\n   *  The timestamp for this block, which is the number of seconds since\n   *  epoch that this block was included.\n   */\n  timestamp;\n  /**\n   *  The block hash of the parent block.\n   */\n  parentHash;\n  /**\n   *  The hash tree root of the parent beacon block for the given\n   *  execution block. See [[link-eip-4788]].\n   */\n  parentBeaconBlockRoot;\n  /**\n   *  The nonce.\n   *\n   *  On legacy networks, this is the random number inserted which\n   *  permitted the difficulty target to be reached.\n   */\n  nonce;\n  /**\n   *  The difficulty target.\n   *\n   *  On legacy networks, this is the proof-of-work target required\n   *  for a block to meet the protocol rules to be included.\n   *\n   *  On modern networks, this is a random number arrived at using\n   *  randao.  @TODO: Find links?\n   */\n  difficulty;\n  /**\n   *  The total gas limit for this block.\n   */\n  gasLimit;\n  /**\n   *  The total gas used in this block.\n   */\n  gasUsed;\n  /**\n   *  The root hash for the global state after applying changes\n   *  in this block.\n   */\n  stateRoot;\n  /**\n   *  The hash of the transaction receipts trie.\n   */\n  receiptsRoot;\n  /**\n   *  The total amount of blob gas consumed by the transactions\n   *  within the block. See [[link-eip-4844]].\n   */\n  blobGasUsed;\n  /**\n   *  The running total of blob gas consumed in excess of the\n   *  target, prior to the block. See [[link-eip-4844]].\n   */\n  excessBlobGas;\n  /**\n   *  The miner coinbase address, wihch receives any subsidies for\n   *  including this block.\n   */\n  miner;\n  /**\n   *  The latest RANDAO mix of the post beacon state of\n   *  the previous block.\n   */\n  prevRandao;\n  /**\n   *  Any extra data the validator wished to include.\n   */\n  extraData;\n  /**\n   *  The base fee per gas that all transactions in this block were\n   *  charged.\n   *\n   *  This adjusts after each block, depending on how congested the network\n   *  is.\n   */\n  baseFeePerGas;\n  #transactions;\n  /**\n   *  Create a new **Block** object.\n   *\n   *  This should generally not be necessary as the unless implementing a\n   *  low-level library.\n   */\n  constructor(block, provider) {\n    this.#transactions = block.transactions.map(tx => {\n      if (typeof tx !== \"string\") {\n        return new TransactionResponse(tx, provider);\n      }\n      return tx;\n    });\n    defineProperties(this, {\n      provider,\n      hash: getValue(block.hash),\n      number: block.number,\n      timestamp: block.timestamp,\n      parentHash: block.parentHash,\n      parentBeaconBlockRoot: block.parentBeaconBlockRoot,\n      nonce: block.nonce,\n      difficulty: block.difficulty,\n      gasLimit: block.gasLimit,\n      gasUsed: block.gasUsed,\n      blobGasUsed: block.blobGasUsed,\n      excessBlobGas: block.excessBlobGas,\n      miner: block.miner,\n      prevRandao: getValue(block.prevRandao),\n      extraData: block.extraData,\n      baseFeePerGas: getValue(block.baseFeePerGas),\n      stateRoot: block.stateRoot,\n      receiptsRoot: block.receiptsRoot\n    });\n  }\n  /**\n   *  Returns the list of transaction hashes, in the order\n   *  they were executed within the block.\n   */\n  get transactions() {\n    return this.#transactions.map(tx => {\n      if (typeof tx === \"string\") {\n        return tx;\n      }\n      return tx.hash;\n    });\n  }\n  /**\n   *  Returns the complete transactions, in the order they\n   *  were executed within the block.\n   *\n   *  This is only available for blocks which prefetched\n   *  transactions, by passing ``true`` to %%prefetchTxs%%\n   *  into [[Provider-getBlock]].\n   */\n  get prefetchedTransactions() {\n    const txs = this.#transactions.slice();\n    // Doesn't matter...\n    if (txs.length === 0) {\n      return [];\n    }\n    // Make sure we prefetched the transactions\n    assert(typeof txs[0] === \"object\", \"transactions were not prefetched with block request\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"transactionResponses()\"\n    });\n    return txs;\n  }\n  /**\n   *  Returns a JSON-friendly value.\n   */\n  toJSON() {\n    const {\n      baseFeePerGas,\n      difficulty,\n      extraData,\n      gasLimit,\n      gasUsed,\n      hash,\n      miner,\n      prevRandao,\n      nonce,\n      number,\n      parentHash,\n      parentBeaconBlockRoot,\n      stateRoot,\n      receiptsRoot,\n      timestamp,\n      transactions\n    } = this;\n    return {\n      _type: \"Block\",\n      baseFeePerGas: toJson(baseFeePerGas),\n      difficulty: toJson(difficulty),\n      extraData,\n      gasLimit: toJson(gasLimit),\n      gasUsed: toJson(gasUsed),\n      blobGasUsed: toJson(this.blobGasUsed),\n      excessBlobGas: toJson(this.excessBlobGas),\n      hash,\n      miner,\n      prevRandao,\n      nonce,\n      number,\n      parentHash,\n      timestamp,\n      parentBeaconBlockRoot,\n      stateRoot,\n      receiptsRoot,\n      transactions\n    };\n  }\n  [Symbol.iterator]() {\n    let index = 0;\n    const txs = this.transactions;\n    return {\n      next: () => {\n        if (index < this.length) {\n          return {\n            value: txs[index++],\n            done: false\n          };\n        }\n        return {\n          value: undefined,\n          done: true\n        };\n      }\n    };\n  }\n  /**\n   *  The number of transactions in this block.\n   */\n  get length() {\n    return this.#transactions.length;\n  }\n  /**\n   *  The [[link-js-date]] this block was included at.\n   */\n  get date() {\n    if (this.timestamp == null) {\n      return null;\n    }\n    return new Date(this.timestamp * 1000);\n  }\n  /**\n   *  Get the transaction at %%indexe%% within this block.\n   */\n  async getTransaction(indexOrHash) {\n    // Find the internal value by its index or hash\n    let tx = undefined;\n    if (typeof indexOrHash === \"number\") {\n      tx = this.#transactions[indexOrHash];\n    } else {\n      const hash = indexOrHash.toLowerCase();\n      for (const v of this.#transactions) {\n        if (typeof v === \"string\") {\n          if (v !== hash) {\n            continue;\n          }\n          tx = v;\n          break;\n        } else {\n          if (v.hash !== hash) {\n            continue;\n          }\n          tx = v;\n          break;\n        }\n      }\n    }\n    if (tx == null) {\n      throw new Error(\"no such tx\");\n    }\n    if (typeof tx === \"string\") {\n      return await this.provider.getTransaction(tx);\n    } else {\n      return tx;\n    }\n  }\n  /**\n   *  If a **Block** was fetched with a request to include the transactions\n   *  this will allow synchronous access to those transactions.\n   *\n   *  If the transactions were not prefetched, this will throw.\n   */\n  getPrefetchedTransaction(indexOrHash) {\n    const txs = this.prefetchedTransactions;\n    if (typeof indexOrHash === \"number\") {\n      return txs[indexOrHash];\n    }\n    indexOrHash = indexOrHash.toLowerCase();\n    for (const tx of txs) {\n      if (tx.hash === indexOrHash) {\n        return tx;\n      }\n    }\n    assertArgument(false, \"no matching transaction\", \"indexOrHash\", indexOrHash);\n  }\n  /**\n   *  Returns true if this block been mined. This provides a type guard\n   *  for all properties on a [[MinedBlock]].\n   */\n  isMined() {\n    return !!this.hash;\n  }\n  /**\n   *  Returns true if this block is an [[link-eip-2930]] block.\n   */\n  isLondon() {\n    return !!this.baseFeePerGas;\n  }\n  /**\n   *  @_ignore:\n   */\n  orphanedEvent() {\n    if (!this.isMined()) {\n      throw new Error(\"\");\n    }\n    return createOrphanedBlockFilter(this);\n  }\n}\n//////////////////////\n// Log\n/**\n *  A **Log** in Ethereum represents an event that has been included in a\n *  transaction using the ``LOG*`` opcodes, which are most commonly used by\n *  Solidity's emit for announcing events.\n */\nexport class Log {\n  /**\n   *  The provider connected to the log used to fetch additional details\n   *  if necessary.\n   */\n  provider;\n  /**\n   *  The transaction hash of the transaction this log occurred in. Use the\n   *  [[Log-getTransaction]] to get the [[TransactionResponse]].\n   */\n  transactionHash;\n  /**\n   *  The block hash of the block this log occurred in. Use the\n   *  [[Log-getBlock]] to get the [[Block]].\n   */\n  blockHash;\n  /**\n   *  The block number of the block this log occurred in. It is preferred\n   *  to use the [[Block-hash]] when fetching the related [[Block]],\n   *  since in the case of an orphaned block, the block at that height may\n   *  have changed.\n   */\n  blockNumber;\n  /**\n   *  If the **Log** represents a block that was removed due to an orphaned\n   *  block, this will be true.\n   *\n   *  This can only happen within an orphan event listener.\n   */\n  removed;\n  /**\n   *  The address of the contract that emitted this log.\n   */\n  address;\n  /**\n   *  The data included in this log when it was emitted.\n   */\n  data;\n  /**\n   *  The indexed topics included in this log when it was emitted.\n   *\n   *  All topics are included in the bloom filters, so they can be\n   *  efficiently filtered using the [[Provider-getLogs]] method.\n   */\n  topics;\n  /**\n   *  The index within the block this log occurred at. This is generally\n   *  not useful to developers, but can be used with the various roots\n   *  to proof inclusion within a block.\n   */\n  index;\n  /**\n   *  The index within the transaction of this log.\n   */\n  transactionIndex;\n  /**\n   *  @_ignore:\n   */\n  constructor(log, provider) {\n    this.provider = provider;\n    const topics = Object.freeze(log.topics.slice());\n    defineProperties(this, {\n      transactionHash: log.transactionHash,\n      blockHash: log.blockHash,\n      blockNumber: log.blockNumber,\n      removed: log.removed,\n      address: log.address,\n      data: log.data,\n      topics,\n      index: log.index,\n      transactionIndex: log.transactionIndex\n    });\n  }\n  /**\n   *  Returns a JSON-compatible object.\n   */\n  toJSON() {\n    const {\n      address,\n      blockHash,\n      blockNumber,\n      data,\n      index,\n      removed,\n      topics,\n      transactionHash,\n      transactionIndex\n    } = this;\n    return {\n      _type: \"log\",\n      address,\n      blockHash,\n      blockNumber,\n      data,\n      index,\n      removed,\n      topics,\n      transactionHash,\n      transactionIndex\n    };\n  }\n  /**\n   *  Returns the block that this log occurred in.\n   */\n  async getBlock() {\n    const block = await this.provider.getBlock(this.blockHash);\n    assert(!!block, \"failed to find transaction\", \"UNKNOWN_ERROR\", {});\n    return block;\n  }\n  /**\n   *  Returns the transaction that this log occurred in.\n   */\n  async getTransaction() {\n    const tx = await this.provider.getTransaction(this.transactionHash);\n    assert(!!tx, \"failed to find transaction\", \"UNKNOWN_ERROR\", {});\n    return tx;\n  }\n  /**\n   *  Returns the transaction receipt fot the transaction that this\n   *  log occurred in.\n   */\n  async getTransactionReceipt() {\n    const receipt = await this.provider.getTransactionReceipt(this.transactionHash);\n    assert(!!receipt, \"failed to find transaction receipt\", \"UNKNOWN_ERROR\", {});\n    return receipt;\n  }\n  /**\n   *  @_ignore:\n   */\n  removedEvent() {\n    return createRemovedLogFilter(this);\n  }\n}\n//////////////////////\n// Transaction Receipt\n/*\nexport interface LegacyTransactionReceipt {\n    byzantium: false;\n    status: null;\n    root: string;\n}\n\nexport interface ByzantiumTransactionReceipt {\n    byzantium: true;\n    status: number;\n    root: null;\n}\n*/\n/**\n *  A **TransactionReceipt** includes additional information about a\n *  transaction that is only available after it has been mined.\n */\nexport class TransactionReceipt {\n  /**\n   *  The provider connected to the log used to fetch additional details\n   *  if necessary.\n   */\n  provider;\n  /**\n   *  The address the transaction was sent to.\n   */\n  to;\n  /**\n   *  The sender of the transaction.\n   */\n  from;\n  /**\n   *  The address of the contract if the transaction was directly\n   *  responsible for deploying one.\n   *\n   *  This is non-null **only** if the ``to`` is empty and the ``data``\n   *  was successfully executed as initcode.\n   */\n  contractAddress;\n  /**\n   *  The transaction hash.\n   */\n  hash;\n  /**\n   *  The index of this transaction within the block transactions.\n   */\n  index;\n  /**\n   *  The block hash of the [[Block]] this transaction was included in.\n   */\n  blockHash;\n  /**\n   *  The block number of the [[Block]] this transaction was included in.\n   */\n  blockNumber;\n  /**\n   *  The bloom filter bytes that represent all logs that occurred within\n   *  this transaction. This is generally not useful for most developers,\n   *  but can be used to validate the included logs.\n   */\n  logsBloom;\n  /**\n   *  The actual amount of gas used by this transaction.\n   *\n   *  When creating a transaction, the amount of gas that will be used can\n   *  only be approximated, but the sender must pay the gas fee for the\n   *  entire gas limit. After the transaction, the difference is refunded.\n   */\n  gasUsed;\n  /**\n   *  The gas used for BLObs. See [[link-eip-4844]].\n   */\n  blobGasUsed;\n  /**\n   *  The amount of gas used by all transactions within the block for this\n   *  and all transactions with a lower ``index``.\n   *\n   *  This is generally not useful for developers but can be used to\n   *  validate certain aspects of execution.\n   */\n  cumulativeGasUsed;\n  /**\n   *  The actual gas price used during execution.\n   *\n   *  Due to the complexity of [[link-eip-1559]] this value can only\n   *  be caluclated after the transaction has been mined, snce the base\n   *  fee is protocol-enforced.\n   */\n  gasPrice;\n  /**\n   *  The price paid per BLOB in gas. See [[link-eip-4844]].\n   */\n  blobGasPrice;\n  /**\n   *  The [[link-eip-2718]] transaction type.\n   */\n  type;\n  //readonly byzantium!: boolean;\n  /**\n   *  The status of this transaction, indicating success (i.e. ``1``) or\n   *  a revert (i.e. ``0``).\n   *\n   *  This is available in post-byzantium blocks, but some backends may\n   *  backfill this value.\n   */\n  status;\n  /**\n   *  The root hash of this transaction.\n   *\n   *  This is no present and was only included in pre-byzantium blocks, but\n   *  could be used to validate certain parts of the receipt.\n   */\n  root;\n  #logs;\n  /**\n   *  @_ignore:\n   */\n  constructor(tx, provider) {\n    this.#logs = Object.freeze(tx.logs.map(log => {\n      return new Log(log, provider);\n    }));\n    let gasPrice = BN_0;\n    if (tx.effectiveGasPrice != null) {\n      gasPrice = tx.effectiveGasPrice;\n    } else if (tx.gasPrice != null) {\n      gasPrice = tx.gasPrice;\n    }\n    defineProperties(this, {\n      provider,\n      to: tx.to,\n      from: tx.from,\n      contractAddress: tx.contractAddress,\n      hash: tx.hash,\n      index: tx.index,\n      blockHash: tx.blockHash,\n      blockNumber: tx.blockNumber,\n      logsBloom: tx.logsBloom,\n      gasUsed: tx.gasUsed,\n      cumulativeGasUsed: tx.cumulativeGasUsed,\n      blobGasUsed: tx.blobGasUsed,\n      gasPrice,\n      blobGasPrice: tx.blobGasPrice,\n      type: tx.type,\n      //byzantium: tx.byzantium,\n      status: tx.status,\n      root: tx.root\n    });\n  }\n  /**\n   *  The logs for this transaction.\n   */\n  get logs() {\n    return this.#logs;\n  }\n  /**\n   *  Returns a JSON-compatible representation.\n   */\n  toJSON() {\n    const {\n      to,\n      from,\n      contractAddress,\n      hash,\n      index,\n      blockHash,\n      blockNumber,\n      logsBloom,\n      logs,\n      //byzantium, \n      status,\n      root\n    } = this;\n    return {\n      _type: \"TransactionReceipt\",\n      blockHash,\n      blockNumber,\n      //byzantium, \n      contractAddress,\n      cumulativeGasUsed: toJson(this.cumulativeGasUsed),\n      from,\n      gasPrice: toJson(this.gasPrice),\n      blobGasUsed: toJson(this.blobGasUsed),\n      blobGasPrice: toJson(this.blobGasPrice),\n      gasUsed: toJson(this.gasUsed),\n      hash,\n      index,\n      logs,\n      logsBloom,\n      root,\n      status,\n      to\n    };\n  }\n  /**\n   *  @_ignore:\n   */\n  get length() {\n    return this.logs.length;\n  }\n  [Symbol.iterator]() {\n    let index = 0;\n    return {\n      next: () => {\n        if (index < this.length) {\n          return {\n            value: this.logs[index++],\n            done: false\n          };\n        }\n        return {\n          value: undefined,\n          done: true\n        };\n      }\n    };\n  }\n  /**\n   *  The total fee for this transaction, in wei.\n   */\n  get fee() {\n    return this.gasUsed * this.gasPrice;\n  }\n  /**\n   *  Resolves to the block this transaction occurred in.\n   */\n  async getBlock() {\n    const block = await this.provider.getBlock(this.blockHash);\n    if (block == null) {\n      throw new Error(\"TODO\");\n    }\n    return block;\n  }\n  /**\n   *  Resolves to the transaction this transaction occurred in.\n   */\n  async getTransaction() {\n    const tx = await this.provider.getTransaction(this.hash);\n    if (tx == null) {\n      throw new Error(\"TODO\");\n    }\n    return tx;\n  }\n  /**\n   *  Resolves to the return value of the execution of this transaction.\n   *\n   *  Support for this feature is limited, as it requires an archive node\n   *  with the ``debug_`` or ``trace_`` API enabled.\n   */\n  async getResult() {\n    return await this.provider.getTransactionResult(this.hash);\n  }\n  /**\n   *  Resolves to the number of confirmations this transaction has.\n   */\n  async confirmations() {\n    return (await this.provider.getBlockNumber()) - this.blockNumber + 1;\n  }\n  /**\n   *  @_ignore:\n   */\n  removedEvent() {\n    return createRemovedTransactionFilter(this);\n  }\n  /**\n   *  @_ignore:\n   */\n  reorderedEvent(other) {\n    assert(!other || other.isMined(), \"unmined 'other' transction cannot be orphaned\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"reorderedEvent(other)\"\n    });\n    return createReorderedTransactionFilter(this, other);\n  }\n}\n/**\n *  A **TransactionResponse** includes all properties about a transaction\n *  that was sent to the network, which may or may not be included in a\n *  block.\n *\n *  The [[TransactionResponse-isMined]] can be used to check if the\n *  transaction has been mined as well as type guard that the otherwise\n *  possibly ``null`` properties are defined.\n */\nexport class TransactionResponse {\n  /**\n   *  The provider this is connected to, which will influence how its\n   *  methods will resolve its async inspection methods.\n   */\n  provider;\n  /**\n   *  The block number of the block that this transaction was included in.\n   *\n   *  This is ``null`` for pending transactions.\n   */\n  blockNumber;\n  /**\n   *  The blockHash of the block that this transaction was included in.\n   *\n   *  This is ``null`` for pending transactions.\n   */\n  blockHash;\n  /**\n   *  The index within the block that this transaction resides at.\n   */\n  index;\n  /**\n   *  The transaction hash.\n   */\n  hash;\n  /**\n   *  The [[link-eip-2718]] transaction envelope type. This is\n   *  ``0`` for legacy transactions types.\n   */\n  type;\n  /**\n   *  The receiver of this transaction.\n   *\n   *  If ``null``, then the transaction is an initcode transaction.\n   *  This means the result of executing the [[data]] will be deployed\n   *  as a new contract on chain (assuming it does not revert) and the\n   *  address may be computed using [[getCreateAddress]].\n   */\n  to;\n  /**\n   *  The sender of this transaction. It is implicitly computed\n   *  from the transaction pre-image hash (as the digest) and the\n   *  [[signature]] using ecrecover.\n   */\n  from;\n  /**\n   *  The nonce, which is used to prevent replay attacks and offer\n   *  a method to ensure transactions from a given sender are explicitly\n   *  ordered.\n   *\n   *  When sending a transaction, this must be equal to the number of\n   *  transactions ever sent by [[from]].\n   */\n  nonce;\n  /**\n   *  The maximum units of gas this transaction can consume. If execution\n   *  exceeds this, the entries transaction is reverted and the sender\n   *  is charged for the full amount, despite not state changes being made.\n   */\n  gasLimit;\n  /**\n   *  The gas price can have various values, depending on the network.\n   *\n   *  In modern networks, for transactions that are included this is\n   *  the //effective gas price// (the fee per gas that was actually\n   *  charged), while for transactions that have not been included yet\n   *  is the [[maxFeePerGas]].\n   *\n   *  For legacy transactions, or transactions on legacy networks, this\n   *  is the fee that will be charged per unit of gas the transaction\n   *  consumes.\n   */\n  gasPrice;\n  /**\n   *  The maximum priority fee (per unit of gas) to allow a\n   *  validator to charge the sender. This is inclusive of the\n   *  [[maxFeeFeePerGas]].\n   */\n  maxPriorityFeePerGas;\n  /**\n   *  The maximum fee (per unit of gas) to allow this transaction\n   *  to charge the sender.\n   */\n  maxFeePerGas;\n  /**\n   *  The [[link-eip-4844]] max fee per BLOb gas.\n   */\n  maxFeePerBlobGas;\n  /**\n   *  The data.\n   */\n  data;\n  /**\n   *  The value, in wei. Use [[formatEther]] to format this value\n   *  as ether.\n   */\n  value;\n  /**\n   *  The chain ID.\n   */\n  chainId;\n  /**\n   *  The signature.\n   */\n  signature;\n  /**\n   *  The [[link-eip-2930]] access list for transaction types that\n   *  support it, otherwise ``null``.\n   */\n  accessList;\n  /**\n   *  The [[link-eip-4844]] BLOb versioned hashes.\n   */\n  blobVersionedHashes;\n  /**\n   *  The [[link-eip-7702]] authorizations (if any).\n   */\n  authorizationList;\n  #startBlock;\n  /**\n   *  @_ignore:\n   */\n  constructor(tx, provider) {\n    this.provider = provider;\n    this.blockNumber = tx.blockNumber != null ? tx.blockNumber : null;\n    this.blockHash = tx.blockHash != null ? tx.blockHash : null;\n    this.hash = tx.hash;\n    this.index = tx.index;\n    this.type = tx.type;\n    this.from = tx.from;\n    this.to = tx.to || null;\n    this.gasLimit = tx.gasLimit;\n    this.nonce = tx.nonce;\n    this.data = tx.data;\n    this.value = tx.value;\n    this.gasPrice = tx.gasPrice;\n    this.maxPriorityFeePerGas = tx.maxPriorityFeePerGas != null ? tx.maxPriorityFeePerGas : null;\n    this.maxFeePerGas = tx.maxFeePerGas != null ? tx.maxFeePerGas : null;\n    this.maxFeePerBlobGas = tx.maxFeePerBlobGas != null ? tx.maxFeePerBlobGas : null;\n    this.chainId = tx.chainId;\n    this.signature = tx.signature;\n    this.accessList = tx.accessList != null ? tx.accessList : null;\n    this.blobVersionedHashes = tx.blobVersionedHashes != null ? tx.blobVersionedHashes : null;\n    this.authorizationList = tx.authorizationList != null ? tx.authorizationList : null;\n    this.#startBlock = -1;\n  }\n  /**\n   *  Returns a JSON-compatible representation of this transaction.\n   */\n  toJSON() {\n    const {\n      blockNumber,\n      blockHash,\n      index,\n      hash,\n      type,\n      to,\n      from,\n      nonce,\n      data,\n      signature,\n      accessList,\n      blobVersionedHashes\n    } = this;\n    return {\n      _type: \"TransactionResponse\",\n      accessList,\n      blockNumber,\n      blockHash,\n      blobVersionedHashes,\n      chainId: toJson(this.chainId),\n      data,\n      from,\n      gasLimit: toJson(this.gasLimit),\n      gasPrice: toJson(this.gasPrice),\n      hash,\n      maxFeePerGas: toJson(this.maxFeePerGas),\n      maxPriorityFeePerGas: toJson(this.maxPriorityFeePerGas),\n      maxFeePerBlobGas: toJson(this.maxFeePerBlobGas),\n      nonce,\n      signature,\n      to,\n      index,\n      type,\n      value: toJson(this.value)\n    };\n  }\n  /**\n   *  Resolves to the Block that this transaction was included in.\n   *\n   *  This will return null if the transaction has not been included yet.\n   */\n  async getBlock() {\n    let blockNumber = this.blockNumber;\n    if (blockNumber == null) {\n      const tx = await this.getTransaction();\n      if (tx) {\n        blockNumber = tx.blockNumber;\n      }\n    }\n    if (blockNumber == null) {\n      return null;\n    }\n    const block = this.provider.getBlock(blockNumber);\n    if (block == null) {\n      throw new Error(\"TODO\");\n    }\n    return block;\n  }\n  /**\n   *  Resolves to this transaction being re-requested from the\n   *  provider. This can be used if you have an unmined transaction\n   *  and wish to get an up-to-date populated instance.\n   */\n  async getTransaction() {\n    return this.provider.getTransaction(this.hash);\n  }\n  /**\n   *  Resolve to the number of confirmations this transaction has.\n   */\n  async confirmations() {\n    if (this.blockNumber == null) {\n      const {\n        tx,\n        blockNumber\n      } = await resolveProperties({\n        tx: this.getTransaction(),\n        blockNumber: this.provider.getBlockNumber()\n      });\n      // Not mined yet...\n      if (tx == null || tx.blockNumber == null) {\n        return 0;\n      }\n      return blockNumber - tx.blockNumber + 1;\n    }\n    const blockNumber = await this.provider.getBlockNumber();\n    return blockNumber - this.blockNumber + 1;\n  }\n  /**\n   *  Resolves once this transaction has been mined and has\n   *  %%confirms%% blocks including it (default: ``1``) with an\n   *  optional %%timeout%%.\n   *\n   *  This can resolve to ``null`` only if %%confirms%% is ``0``\n   *  and the transaction has not been mined, otherwise this will\n   *  wait until enough confirmations have completed.\n   */\n  async wait(_confirms, _timeout) {\n    const confirms = _confirms == null ? 1 : _confirms;\n    const timeout = _timeout == null ? 0 : _timeout;\n    let startBlock = this.#startBlock;\n    let nextScan = -1;\n    let stopScanning = startBlock === -1 ? true : false;\n    const checkReplacement = async () => {\n      // Get the current transaction count for this sender\n      if (stopScanning) {\n        return null;\n      }\n      const {\n        blockNumber,\n        nonce\n      } = await resolveProperties({\n        blockNumber: this.provider.getBlockNumber(),\n        nonce: this.provider.getTransactionCount(this.from)\n      });\n      // No transaction or our nonce has not been mined yet; but we\n      // can start scanning later when we do start\n      if (nonce < this.nonce) {\n        startBlock = blockNumber;\n        return;\n      }\n      // We were mined; no replacement\n      if (stopScanning) {\n        return null;\n      }\n      const mined = await this.getTransaction();\n      if (mined && mined.blockNumber != null) {\n        return;\n      }\n      // We were replaced; start scanning for that transaction\n      // Starting to scan; look back a few extra blocks for safety\n      if (nextScan === -1) {\n        nextScan = startBlock - 3;\n        if (nextScan < this.#startBlock) {\n          nextScan = this.#startBlock;\n        }\n      }\n      while (nextScan <= blockNumber) {\n        // Get the next block to scan\n        if (stopScanning) {\n          return null;\n        }\n        const block = await this.provider.getBlock(nextScan, true);\n        // This should not happen; but we'll try again shortly\n        if (block == null) {\n          return;\n        }\n        // We were mined; no replacement\n        for (const hash of block) {\n          if (hash === this.hash) {\n            return;\n          }\n        }\n        // Search for the transaction that replaced us\n        for (let i = 0; i < block.length; i++) {\n          const tx = await block.getTransaction(i);\n          if (tx.from === this.from && tx.nonce === this.nonce) {\n            // Get the receipt\n            if (stopScanning) {\n              return null;\n            }\n            const receipt = await this.provider.getTransactionReceipt(tx.hash);\n            // This should not happen; but we'll try again shortly\n            if (receipt == null) {\n              return;\n            }\n            // We will retry this on the next block (this case could be optimized)\n            if (blockNumber - receipt.blockNumber + 1 < confirms) {\n              return;\n            }\n            // The reason we were replaced\n            let reason = \"replaced\";\n            if (tx.data === this.data && tx.to === this.to && tx.value === this.value) {\n              reason = \"repriced\";\n            } else if (tx.data === \"0x\" && tx.from === tx.to && tx.value === BN_0) {\n              reason = \"cancelled\";\n            }\n            assert(false, \"transaction was replaced\", \"TRANSACTION_REPLACED\", {\n              cancelled: reason === \"replaced\" || reason === \"cancelled\",\n              reason,\n              replacement: tx.replaceableTransaction(startBlock),\n              hash: tx.hash,\n              receipt\n            });\n          }\n        }\n        nextScan++;\n      }\n      return;\n    };\n    const checkReceipt = receipt => {\n      if (receipt == null || receipt.status !== 0) {\n        return receipt;\n      }\n      assert(false, \"transaction execution reverted\", \"CALL_EXCEPTION\", {\n        action: \"sendTransaction\",\n        data: null,\n        reason: null,\n        invocation: null,\n        revert: null,\n        transaction: {\n          to: receipt.to,\n          from: receipt.from,\n          data: \"\" // @TODO: in v7, split out sendTransaction properties\n        },\n        receipt\n      });\n    };\n    const receipt = await this.provider.getTransactionReceipt(this.hash);\n    if (confirms === 0) {\n      return checkReceipt(receipt);\n    }\n    if (receipt) {\n      if (confirms === 1 || (await receipt.confirmations()) >= confirms) {\n        return checkReceipt(receipt);\n      }\n    } else {\n      // Check for a replacement; throws if a replacement was found\n      await checkReplacement();\n      // Allow null only when the confirms is 0\n      if (confirms === 0) {\n        return null;\n      }\n    }\n    const waiter = new Promise((resolve, reject) => {\n      // List of things to cancel when we have a result (one way or the other)\n      const cancellers = [];\n      const cancel = () => {\n        cancellers.forEach(c => c());\n      };\n      // On cancel, stop scanning for replacements\n      cancellers.push(() => {\n        stopScanning = true;\n      });\n      // Set up any timeout requested\n      if (timeout > 0) {\n        const timer = setTimeout(() => {\n          cancel();\n          reject(makeError(\"wait for transaction timeout\", \"TIMEOUT\"));\n        }, timeout);\n        cancellers.push(() => {\n          clearTimeout(timer);\n        });\n      }\n      const txListener = async receipt => {\n        // Done; return it!\n        if ((await receipt.confirmations()) >= confirms) {\n          cancel();\n          try {\n            resolve(checkReceipt(receipt));\n          } catch (error) {\n            reject(error);\n          }\n        }\n      };\n      cancellers.push(() => {\n        this.provider.off(this.hash, txListener);\n      });\n      this.provider.on(this.hash, txListener);\n      // We support replacement detection; start checking\n      if (startBlock >= 0) {\n        const replaceListener = async () => {\n          try {\n            // Check for a replacement; this throws only if one is found\n            await checkReplacement();\n          } catch (error) {\n            // We were replaced (with enough confirms); re-throw the error\n            if (isError(error, \"TRANSACTION_REPLACED\")) {\n              cancel();\n              reject(error);\n              return;\n            }\n          }\n          // Rescheudle a check on the next block\n          if (!stopScanning) {\n            this.provider.once(\"block\", replaceListener);\n          }\n        };\n        cancellers.push(() => {\n          this.provider.off(\"block\", replaceListener);\n        });\n        this.provider.once(\"block\", replaceListener);\n      }\n    });\n    return await waiter;\n  }\n  /**\n   *  Returns ``true`` if this transaction has been included.\n   *\n   *  This is effective only as of the time the TransactionResponse\n   *  was instantiated. To get up-to-date information, use\n   *  [[getTransaction]].\n   *\n   *  This provides a Type Guard that this transaction will have\n   *  non-null property values for properties that are null for\n   *  unmined transactions.\n   */\n  isMined() {\n    return this.blockHash != null;\n  }\n  /**\n   *  Returns true if the transaction is a legacy (i.e. ``type == 0``)\n   *  transaction.\n   *\n   *  This provides a Type Guard that this transaction will have\n   *  the ``null``-ness for hardfork-specific properties set correctly.\n   */\n  isLegacy() {\n    return this.type === 0;\n  }\n  /**\n   *  Returns true if the transaction is a Berlin (i.e. ``type == 1``)\n   *  transaction. See [[link-eip-2070]].\n   *\n   *  This provides a Type Guard that this transaction will have\n   *  the ``null``-ness for hardfork-specific properties set correctly.\n   */\n  isBerlin() {\n    return this.type === 1;\n  }\n  /**\n   *  Returns true if the transaction is a London (i.e. ``type == 2``)\n   *  transaction. See [[link-eip-1559]].\n   *\n   *  This provides a Type Guard that this transaction will have\n   *  the ``null``-ness for hardfork-specific properties set correctly.\n   */\n  isLondon() {\n    return this.type === 2;\n  }\n  /**\n   *  Returns true if hte transaction is a Cancun (i.e. ``type == 3``)\n   *  transaction. See [[link-eip-4844]].\n   */\n  isCancun() {\n    return this.type === 3;\n  }\n  /**\n   *  Returns a filter which can be used to listen for orphan events\n   *  that evict this transaction.\n   */\n  removedEvent() {\n    assert(this.isMined(), \"unmined transaction canot be orphaned\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"removeEvent()\"\n    });\n    return createRemovedTransactionFilter(this);\n  }\n  /**\n   *  Returns a filter which can be used to listen for orphan events\n   *  that re-order this event against %%other%%.\n   */\n  reorderedEvent(other) {\n    assert(this.isMined(), \"unmined transaction canot be orphaned\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"removeEvent()\"\n    });\n    assert(!other || other.isMined(), \"unmined 'other' transaction canot be orphaned\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"removeEvent()\"\n    });\n    return createReorderedTransactionFilter(this, other);\n  }\n  /**\n   *  Returns a new TransactionResponse instance which has the ability to\n   *  detect (and throw an error) if the transaction is replaced, which\n   *  will begin scanning at %%startBlock%%.\n   *\n   *  This should generally not be used by developers and is intended\n   *  primarily for internal use. Setting an incorrect %%startBlock%% can\n   *  have devastating performance consequences if used incorrectly.\n   */\n  replaceableTransaction(startBlock) {\n    assertArgument(Number.isInteger(startBlock) && startBlock >= 0, \"invalid startBlock\", \"startBlock\", startBlock);\n    const tx = new TransactionResponse(this, this.provider);\n    tx.#startBlock = startBlock;\n    return tx;\n  }\n}\nfunction createOrphanedBlockFilter(block) {\n  return {\n    orphan: \"drop-block\",\n    hash: block.hash,\n    number: block.number\n  };\n}\nfunction createReorderedTransactionFilter(tx, other) {\n  return {\n    orphan: \"reorder-transaction\",\n    tx,\n    other\n  };\n}\nfunction createRemovedTransactionFilter(tx) {\n  return {\n    orphan: \"drop-transaction\",\n    tx\n  };\n}\nfunction createRemovedLogFilter(log) {\n  return {\n    orphan: \"drop-log\",\n    log: {\n      transactionHash: log.transactionHash,\n      blockHash: log.blockHash,\n      blockNumber: log.blockNumber,\n      address: log.address,\n      data: log.data,\n      topics: Object.freeze(log.topics.slice()),\n      index: log.index\n    }\n  };\n}", "map": {"version": 3, "names": ["defineProperties", "getBigInt", "getNumber", "hexlify", "isBytesLike", "resolveProperties", "assert", "assertArgument", "isError", "makeError", "accessListify", "BN_0", "BigInt", "getValue", "value", "to<PERSON><PERSON>", "toString", "FeeData", "gasPrice", "maxFeePer<PERSON>as", "maxPriorityFeePerGas", "constructor", "toJSON", "_type", "copyRequest", "req", "result", "to", "from", "data", "bigInt<PERSON>eys", "split", "key", "numberKeys", "accessList", "authorizationList", "slice", "blockTag", "enableCcipRead", "customData", "blobVersionedHashes", "kzg", "blobs", "map", "b", "Object", "assign", "Block", "provider", "number", "hash", "timestamp", "parentHash", "parentBeaconBlockRoot", "nonce", "difficulty", "gasLimit", "gasUsed", "stateRoot", "receiptsRoot", "blobGasUsed", "excessBlobGas", "miner", "prevRandao", "extraData", "baseFeePerGas", "transactions", "block", "tx", "TransactionResponse", "prefetchedTransactions", "txs", "length", "operation", "Symbol", "iterator", "index", "next", "done", "undefined", "date", "Date", "getTransaction", "indexOrHash", "toLowerCase", "v", "Error", "getPrefetchedTransaction", "isMined", "isLondon", "orphanedEvent", "createOrphanedBlockFilter", "Log", "transactionHash", "blockHash", "blockNumber", "removed", "address", "topics", "transactionIndex", "log", "freeze", "getBlock", "getTransactionReceipt", "receipt", "removedEvent", "createRemovedLogFilter", "TransactionReceipt", "contractAddress", "logsBloom", "cumulativeGasUsed", "blobGasPrice", "type", "status", "root", "logs", "effectiveGasPrice", "fee", "getResult", "getTransactionResult", "confirmations", "getBlockNumber", "createRemovedTransactionFilter", "reorderedEvent", "other", "createReorderedTransactionFilter", "maxFeePerBlobGas", "chainId", "signature", "startBlock", "wait", "_confirms", "_timeout", "confirms", "timeout", "nextScan", "stopScanning", "checkReplacement", "getTransactionCount", "mined", "i", "reason", "cancelled", "replacement", "replaceableTransaction", "checkReceipt", "action", "invocation", "revert", "transaction", "waiter", "Promise", "resolve", "reject", "cancellers", "cancel", "for<PERSON>ach", "c", "push", "timer", "setTimeout", "clearTimeout", "txListener", "error", "off", "on", "replaceListener", "once", "isLegacy", "isBerlin", "isCancun", "Number", "isInteger", "orphan"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider.ts"], "sourcesContent": ["//import { resolveAddress } from \"@ethersproject/address\";\nimport {\n    defineProperties, getBigInt, getNumber, hexlify, isBytesLike,\n    resolveProperties,\n    assert, assertArgument, isError, makeError\n} from \"../utils/index.js\";\nimport { accessListify } from \"../transaction/index.js\";\n\nimport type { AddressLike, NameResolver } from \"../address/index.js\";\nimport type { BigNumberish, EventEmitterable } from \"../utils/index.js\";\nimport type { Signature } from \"../crypto/index.js\";\nimport type {\n    AccessList, AccessListish, Authorization, AuthorizationLike, BlobLike,\n    KzgLibraryLike, TransactionLike\n} from \"../transaction/index.js\";\n\nimport type { ContractRunner } from \"./contracts.js\";\nimport type { Network } from \"./network.js\";\n\n\nconst BN_0 = BigInt(0);\n\n/**\n *  A **BlockTag** specifies a specific block.\n *\n *  **numeric value** - specifies the block height, where\n *  the genesis block is block 0; many operations accept a negative\n *  value which indicates the block number should be deducted from\n *  the most recent block. A numeric value may be a ``number``, ``bigint``,\n *  or a decimal of hex string.\n *\n *  **blockhash** - specifies a specific block by its blockhash; this allows\n *  potentially orphaned blocks to be specifed, without ambiguity, but many\n *  backends do not support this for some operations.\n */\nexport type BlockTag = BigNumberish | string;\n\nimport {\n    BlockParams, LogParams, TransactionReceiptParams,\n    TransactionResponseParams\n} from \"./formatting.js\";\n\n// -----------------------\n\nfunction getValue<T>(value: undefined | null | T): null | T {\n    if (value == null) { return null; }\n    return value;\n}\n\nfunction toJson(value: null | bigint): null | string {\n    if (value == null) { return null; }\n    return value.toString();\n}\n\n// @TODO? <T extends FeeData = { }> implements Required<T>\n\n/**\n *  A **FeeData** wraps all the fee-related values associated with\n *  the network.\n */\nexport class FeeData {\n    /**\n     *  The gas price for legacy networks.\n     */\n    readonly gasPrice!: null | bigint;\n\n    /**\n     *  The maximum fee to pay per gas.\n     *\n     *  The base fee per gas is defined by the network and based on\n     *  congestion, increasing the cost during times of heavy load\n     *  and lowering when less busy.\n     *\n     *  The actual fee per gas will be the base fee for the block\n     *  and the priority fee, up to the max fee per gas.\n     *\n     *  This will be ``null`` on legacy networks (i.e. [pre-EIP-1559](link-eip-1559))\n     */\n    readonly maxFeePerGas!: null | bigint;\n\n    /**\n     *  The additional amout to pay per gas to encourage a validator\n     *  to include the transaction.\n     *\n     *  The purpose of this is to compensate the validator for the\n     *  adjusted risk for including a given transaction.\n     *\n     *  This will be ``null`` on legacy networks (i.e. [pre-EIP-1559](link-eip-1559))\n     */\n    readonly maxPriorityFeePerGas!: null | bigint;\n\n    /**\n     *  Creates a new FeeData for %%gasPrice%%, %%maxFeePerGas%% and\n     *  %%maxPriorityFeePerGas%%.\n     */\n    constructor(gasPrice?: null | bigint, maxFeePerGas?: null | bigint, maxPriorityFeePerGas?: null | bigint) {\n        defineProperties<FeeData>(this, {\n            gasPrice: getValue(gasPrice),\n            maxFeePerGas: getValue(maxFeePerGas),\n            maxPriorityFeePerGas: getValue(maxPriorityFeePerGas)\n        });\n    }\n\n    /**\n     *  Returns a JSON-friendly value.\n     */\n    toJSON(): any {\n        const {\n            gasPrice, maxFeePerGas, maxPriorityFeePerGas\n        } = this;\n        return {\n            _type: \"FeeData\",\n            gasPrice: toJson(gasPrice),\n            maxFeePerGas: toJson(maxFeePerGas),\n            maxPriorityFeePerGas: toJson(maxPriorityFeePerGas),\n        };\n    }\n}\n\n\n/**\n *  A **TransactionRequest** is a transactions with potentially various\n *  properties not defined, or with less strict types for its values.\n *\n *  This is used to pass to various operations, which will internally\n *  coerce any types and populate any necessary values.\n */\nexport interface TransactionRequest {\n    /**\n     *  The transaction type.\n     */\n    type?: null | number;\n\n    /**\n     *  The target of the transaction.\n     */\n    to?: null | AddressLike;\n\n    /**\n     *  The sender of the transaction.\n     */\n    from?: null | AddressLike;\n\n    /**\n     *  The nonce of the transaction, used to prevent replay attacks.\n     */\n    nonce?: null | number;\n\n    /**\n     *  The maximum amount of gas to allow this transaction to consume.\n     */\n    gasLimit?: null | BigNumberish;\n\n    /**\n     *  The gas price to use for legacy transactions or transactions on\n     *  legacy networks.\n     *\n     *  Most of the time the ``max*FeePerGas`` is preferred.\n     */\n    gasPrice?: null | BigNumberish;\n\n    /**\n     *  The [[link-eip-1559]] maximum priority fee to pay per gas.\n     */\n    maxPriorityFeePerGas?: null | BigNumberish;\n\n    /**\n     *  The [[link-eip-1559]] maximum total fee to pay per gas. The actual\n     *  value used is protocol enforced to be the block's base fee.\n     */\n    maxFeePerGas?: null | BigNumberish;\n\n    /**\n     *  The transaction data.\n     */\n    data?: null | string;\n\n    /**\n     *  The transaction value (in wei).\n     */\n    value?: null | BigNumberish;\n\n    /**\n     *  The chain ID for the network this transaction is valid on.\n     */\n    chainId?: null | BigNumberish;\n\n    /**\n     *  The [[link-eip-2930]] access list. Storage slots included in the access\n     *  list are //warmed// by pre-loading them, so their initial cost to\n     *  fetch is guaranteed, but then each additional access is cheaper.\n     */\n    accessList?: null | AccessListish;\n\n    /**\n     *  A custom object, which can be passed along for network-specific\n     *  values.\n     */\n    customData?: any;\n\n    // Only meaningful when used for call\n\n    /**\n     *  When using ``call`` or ``estimateGas``, this allows a specific\n     *  block to be queried. Many backends do not support this and when\n     *  unsupported errors are silently squelched and ``\"latest\"`` is used. \n     */\n    blockTag?: BlockTag;\n\n    /**\n     *  When using ``call``, this enables CCIP-read, which permits the\n     *  provider to be redirected to web-based content during execution,\n     *  which is then further validated by the contract.\n     *\n     *  There are potential security implications allowing CCIP-read, as\n     *  it could be used to expose the IP address or user activity during\n     *  the fetch to unexpected parties.\n     */\n    enableCcipRead?: boolean;\n\n    /**\n     *  The blob versioned hashes (see [[link-eip-4844]]).\n     */\n    blobVersionedHashes?: null | Array<string>\n\n    /**\n     *  The maximum fee per blob gas (see [[link-eip-4844]]).\n     */\n    maxFeePerBlobGas?: null | BigNumberish;\n\n    /**\n     *  Any blobs to include in the transaction (see [[link-eip-4844]]).\n     */\n    blobs?: null | Array<BlobLike>;\n\n    /**\n     *  An external library for computing the KZG commitments and\n     *  proofs necessary for EIP-4844 transactions (see [[link-eip-4844]]).\n     *\n     *  This is generally ``null``, unless you are creating BLOb\n     *  transactions.\n     */\n    kzg?: null | KzgLibraryLike;\n\n    /**\n     *  The [[link-eip-7702]] authorizations (if any).\n     */\n    authorizationList?: null | Array<AuthorizationLike>;\n\n    // Todo?\n    //gasMultiplier?: number;\n};\n\n/**\n *  A **PreparedTransactionRequest** is identical to a [[TransactionRequest]]\n *  except all the property types are strictly enforced.\n */\nexport interface PreparedTransactionRequest {\n    /**\n     *  The transaction type.\n     */\n    type?: number;\n\n\n    /**\n     *  The target of the transaction.\n     */\n    to?: AddressLike;\n\n    /**\n     *  The sender of the transaction.\n     */\n    from?: AddressLike;\n\n    /**\n     *  The nonce of the transaction, used to prevent replay attacks.\n     */\n\n    nonce?: number;\n\n    /**\n     *  The maximum amount of gas to allow this transaction to consume.\n     */\n    gasLimit?: bigint;\n\n    /**\n     *  The gas price to use for legacy transactions or transactions on\n     *  legacy networks.\n     *\n     *  Most of the time the ``max*FeePerGas`` is preferred.\n     */\n    gasPrice?: bigint;\n\n    /**\n     *  The [[link-eip-1559]] maximum priority fee to pay per gas.\n     */\n    maxPriorityFeePerGas?: bigint;\n\n    /**\n     *  The [[link-eip-1559]] maximum total fee to pay per gas. The actual\n     *  value used is protocol enforced to be the block's base fee.\n     */\n    maxFeePerGas?: bigint;\n\n    /**\n     *  The transaction data.\n     */\n    data?: string;\n\n\n    /**\n     *  The transaction value (in wei).\n     */\n    value?: bigint;\n\n    /**\n     *  The chain ID for the network this transaction is valid on.\n     */\n    chainId?: bigint;\n\n    /**\n     *  The [[link-eip-2930]] access list. Storage slots included in the access\n     *  list are //warmed// by pre-loading them, so their initial cost to\n     *  fetch is guaranteed, but then each additional access is cheaper.\n     */\n    accessList?: AccessList;\n\n    /**\n     *  The [[link-eip-7702]] authorizations (if any).\n     */\n    authorizationList?: Array<Authorization>;\n\n    /**\n     *  A custom object, which can be passed along for network-specific\n     *  values.\n     */\n    customData?: any;\n\n\n\n    /**\n     *  When using ``call`` or ``estimateGas``, this allows a specific\n     *  block to be queried. Many backends do not support this and when\n     *  unsupported errors are silently squelched and ``\"latest\"`` is used. \n     */\n    blockTag?: BlockTag;\n\n    /**\n     *  When using ``call``, this enables CCIP-read, which permits the\n     *  provider to be redirected to web-based content during execution,\n     *  which is then further validated by the contract.\n     *\n     *  There are potential security implications allowing CCIP-read, as\n     *  it could be used to expose the IP address or user activity during\n     *  the fetch to unexpected parties.\n     */\n    enableCcipRead?: boolean;\n}\n\n/**\n *  Returns a copy of %%req%% with all properties coerced to their strict\n *  types.\n */\nexport function copyRequest(req: TransactionRequest): PreparedTransactionRequest {\n    const result: any = { };\n\n    // These could be addresses, ENS names or Addressables\n    if (req.to) { result.to = req.to; }\n    if (req.from) { result.from = req.from; }\n\n    if (req.data) { result.data = hexlify(req.data); }\n\n    const bigIntKeys = \"chainId,gasLimit,gasPrice,maxFeePerBlobGas,maxFeePerGas,maxPriorityFeePerGas,value\".split(/,/);\n    for (const key of bigIntKeys) {\n        if (!(key in req) || (<any>req)[key] == null) { continue; }\n        result[key] = getBigInt((<any>req)[key], `request.${ key }`);\n    }\n\n    const numberKeys = \"type,nonce\".split(/,/);\n    for (const key of numberKeys) {\n        if (!(key in req) || (<any>req)[key] == null) { continue; }\n        result[key] = getNumber((<any>req)[key], `request.${ key }`);\n    }\n\n    if (req.accessList) {\n        result.accessList = accessListify(req.accessList);\n    }\n\n    if (req.authorizationList) {\n        result.authorizationList = req.authorizationList.slice();\n    }\n\n    if (\"blockTag\" in req) { result.blockTag = req.blockTag; }\n\n    if (\"enableCcipRead\" in req) {\n        result.enableCcipRead = !!req.enableCcipRead\n    }\n\n    if (\"customData\" in req) {\n        result.customData = req.customData;\n    }\n\n    if (\"blobVersionedHashes\" in req && req.blobVersionedHashes) {\n        result.blobVersionedHashes = req.blobVersionedHashes.slice();\n    }\n\n    if (\"kzg\" in req) { result.kzg = req.kzg; }\n\n    if (\"blobs\" in req && req.blobs) {\n        result.blobs = req.blobs.map((b) => {\n            if (isBytesLike(b)) { return hexlify(b); }\n            return Object.assign({ }, b);\n        });\n    }\n\n    return result;\n}\n\n//////////////////////\n// Block\n\n/**\n *  An Interface to indicate a [[Block]] has been included in the\n *  blockchain. This asserts a Type Guard that necessary properties\n *  are non-null.\n *\n *  Before a block is included, it is a //pending// block.\n */\nexport interface MinedBlock extends Block {\n    /**\n     *  The block number also known as the block height.\n     */\n    readonly number: number;\n\n    /**\n     *  The block hash.\n     */\n    readonly hash: string;\n\n    /**\n     *  The block timestamp, in seconds from epoch.\n     */\n    readonly timestamp: number;\n\n    /**\n     *  The block date, created from the [[timestamp]].\n     */\n    readonly date: Date;\n\n    /**\n     *  The miner of the block, also known as the ``author`` or\n     *  block ``producer``.\n     */\n    readonly miner: string;\n}\n\n/**\n *  A **Block** represents the data associated with a full block on\n *  Ethereum.\n */\nexport class Block implements BlockParams, Iterable<string> {\n\n    /**\n     *  The provider connected to the block used to fetch additional details\n     *  if necessary.\n     */\n    readonly provider!: Provider;\n\n    /**\n     *  The block number, sometimes called the block height. This is a\n     *  sequential number that is one higher than the parent block.\n     */\n    readonly number!: number;\n\n    /**\n     *  The block hash.\n     *\n     *  This hash includes all properties, so can be safely used to identify\n     *  an exact set of block properties.\n     */\n    readonly hash!: null | string;\n\n    /**\n     *  The timestamp for this block, which is the number of seconds since\n     *  epoch that this block was included.\n     */\n    readonly timestamp!: number;\n\n    /**\n     *  The block hash of the parent block.\n     */\n    readonly parentHash!: string;\n\n    /**\n     *  The hash tree root of the parent beacon block for the given\n     *  execution block. See [[link-eip-4788]].\n     */\n    parentBeaconBlockRoot!: null | string;\n\n    /**\n     *  The nonce.\n     *\n     *  On legacy networks, this is the random number inserted which\n     *  permitted the difficulty target to be reached.\n     */\n    readonly nonce!: string;\n\n    /**\n     *  The difficulty target.\n     *\n     *  On legacy networks, this is the proof-of-work target required\n     *  for a block to meet the protocol rules to be included.\n     *\n     *  On modern networks, this is a random number arrived at using\n     *  randao.  @TODO: Find links?\n     */\n    readonly difficulty!: bigint;\n\n\n    /**\n     *  The total gas limit for this block.\n     */\n    readonly gasLimit!: bigint;\n\n    /**\n     *  The total gas used in this block.\n     */\n    readonly gasUsed!: bigint;\n\n\n    /**\n     *  The root hash for the global state after applying changes\n     *  in this block.\n     */\n    readonly stateRoot!: null | string;\n\n    /**\n     *  The hash of the transaction receipts trie.\n     */\n    readonly receiptsRoot!: null | string;\n\n    /**\n     *  The total amount of blob gas consumed by the transactions\n     *  within the block. See [[link-eip-4844]].\n     */\n    readonly blobGasUsed!: null | bigint;\n\n    /**\n     *  The running total of blob gas consumed in excess of the\n     *  target, prior to the block. See [[link-eip-4844]].\n     */\n    readonly excessBlobGas!: null | bigint;\n\n    /**\n     *  The miner coinbase address, wihch receives any subsidies for\n     *  including this block.\n     */\n    readonly miner!: string;\n\n    /**\n     *  The latest RANDAO mix of the post beacon state of\n     *  the previous block.\n     */\n    readonly prevRandao!: null | string;\n\n    /**\n     *  Any extra data the validator wished to include.\n     */\n    readonly extraData!: string;\n\n    /**\n     *  The base fee per gas that all transactions in this block were\n     *  charged.\n     *\n     *  This adjusts after each block, depending on how congested the network\n     *  is.\n     */\n    readonly baseFeePerGas!: null | bigint;\n\n    readonly #transactions: Array<string | TransactionResponse>;\n\n    /**\n     *  Create a new **Block** object.\n     *\n     *  This should generally not be necessary as the unless implementing a\n     *  low-level library.\n     */\n    constructor(block: BlockParams, provider: Provider) {\n\n        this.#transactions = block.transactions.map((tx) => {\n            if (typeof(tx) !== \"string\") {\n                return new TransactionResponse(tx, provider);\n            }\n            return tx;\n        });\n\n        defineProperties<Block>(this, {\n            provider,\n\n            hash: getValue(block.hash),\n\n            number: block.number,\n            timestamp: block.timestamp,\n\n            parentHash: block.parentHash,\n            parentBeaconBlockRoot: block.parentBeaconBlockRoot,\n\n            nonce: block.nonce,\n            difficulty: block.difficulty,\n\n            gasLimit: block.gasLimit,\n            gasUsed: block.gasUsed,\n            blobGasUsed: block.blobGasUsed,\n            excessBlobGas: block.excessBlobGas,\n            miner: block.miner,\n            prevRandao: getValue(block.prevRandao),\n            extraData: block.extraData,\n\n            baseFeePerGas: getValue(block.baseFeePerGas),\n\n            stateRoot: block.stateRoot,\n            receiptsRoot: block.receiptsRoot,\n        });\n    }\n\n    /**\n     *  Returns the list of transaction hashes, in the order\n     *  they were executed within the block.\n     */\n    get transactions(): ReadonlyArray<string> {\n        return this.#transactions.map((tx) => {\n            if (typeof(tx) === \"string\") { return tx; }\n            return tx.hash;\n        });\n    }\n\n    /**\n     *  Returns the complete transactions, in the order they\n     *  were executed within the block.\n     *\n     *  This is only available for blocks which prefetched\n     *  transactions, by passing ``true`` to %%prefetchTxs%%\n     *  into [[Provider-getBlock]].\n     */\n    get prefetchedTransactions(): Array<TransactionResponse> {\n        const txs = this.#transactions.slice();\n\n        // Doesn't matter...\n        if (txs.length === 0) { return [ ]; }\n\n        // Make sure we prefetched the transactions\n        assert(typeof(txs[0]) === \"object\", \"transactions were not prefetched with block request\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"transactionResponses()\"\n        });\n\n        return <Array<TransactionResponse>>txs;\n    }\n\n    /**\n     *  Returns a JSON-friendly value.\n     */\n    toJSON(): any {\n        const {\n            baseFeePerGas, difficulty, extraData, gasLimit, gasUsed, hash,\n            miner, prevRandao, nonce, number, parentHash, parentBeaconBlockRoot,\n            stateRoot, receiptsRoot, timestamp, transactions\n        } = this;\n\n        return {\n            _type: \"Block\",\n            baseFeePerGas: toJson(baseFeePerGas),\n            difficulty: toJson(difficulty),\n            extraData,\n            gasLimit: toJson(gasLimit),\n            gasUsed: toJson(gasUsed),\n            blobGasUsed: toJson(this.blobGasUsed),\n            excessBlobGas: toJson(this.excessBlobGas),\n            hash, miner, prevRandao, nonce, number, parentHash, timestamp,\n            parentBeaconBlockRoot, stateRoot, receiptsRoot,\n            transactions,\n        };\n    }\n\n    [Symbol.iterator](): Iterator<string> {\n        let index = 0;\n        const txs = this.transactions;\n        return {\n            next: () => {\n                if (index < this.length) {\n                    return {\n                        value: txs[index++], done: false\n                    }\n                }\n                return { value: undefined, done: true };\n            }\n        };\n    }\n\n    /**\n     *  The number of transactions in this block.\n     */\n    get length(): number { return this.#transactions.length; }\n\n    /**\n     *  The [[link-js-date]] this block was included at.\n     */\n    get date(): null | Date {\n        if (this.timestamp == null) { return null; }\n        return new Date(this.timestamp * 1000);\n    }\n\n    /**\n     *  Get the transaction at %%indexe%% within this block.\n     */\n    async getTransaction(indexOrHash: number | string): Promise<TransactionResponse> {\n        // Find the internal value by its index or hash\n        let tx: string | TransactionResponse | undefined = undefined;\n        if (typeof(indexOrHash) === \"number\") {\n            tx = this.#transactions[indexOrHash];\n\n        } else {\n            const hash = indexOrHash.toLowerCase();\n            for (const v of this.#transactions) {\n                if (typeof(v) === \"string\") {\n                    if (v !== hash) { continue; }\n                    tx = v;\n                    break;\n                } else {\n                    if (v.hash !== hash) { continue; }\n                    tx = v;\n                    break;\n                }\n            }\n        }\n        if (tx == null) { throw new Error(\"no such tx\"); }\n\n        if (typeof(tx) === \"string\") {\n            return <TransactionResponse>(await this.provider.getTransaction(tx));\n        } else {\n            return tx;\n        }\n    }\n\n    /**\n     *  If a **Block** was fetched with a request to include the transactions\n     *  this will allow synchronous access to those transactions.\n     *\n     *  If the transactions were not prefetched, this will throw.\n     */\n    getPrefetchedTransaction(indexOrHash: number | string): TransactionResponse {\n        const txs = this.prefetchedTransactions;\n        if (typeof(indexOrHash) === \"number\") {\n            return txs[indexOrHash];\n        }\n\n        indexOrHash = indexOrHash.toLowerCase();\n        for (const tx of txs) {\n            if (tx.hash === indexOrHash) { return tx; }\n        }\n\n        assertArgument(false, \"no matching transaction\", \"indexOrHash\", indexOrHash);\n    }\n\n    /**\n     *  Returns true if this block been mined. This provides a type guard\n     *  for all properties on a [[MinedBlock]].\n     */\n    isMined(): this is MinedBlock { return !!this.hash; }\n\n    /**\n     *  Returns true if this block is an [[link-eip-2930]] block.\n     */\n    isLondon(): this is (Block & { baseFeePerGas: bigint }) {\n        return !!this.baseFeePerGas;\n    }\n\n    /**\n     *  @_ignore:\n     */\n    orphanedEvent(): OrphanFilter {\n        if (!this.isMined()) { throw new Error(\"\"); }\n        return createOrphanedBlockFilter(this);\n    }\n}\n\n//////////////////////\n// Log\n\n/**\n *  A **Log** in Ethereum represents an event that has been included in a\n *  transaction using the ``LOG*`` opcodes, which are most commonly used by\n *  Solidity's emit for announcing events.\n */\nexport class Log implements LogParams {\n\n    /**\n     *  The provider connected to the log used to fetch additional details\n     *  if necessary.\n     */\n    readonly provider: Provider;\n\n    /**\n     *  The transaction hash of the transaction this log occurred in. Use the\n     *  [[Log-getTransaction]] to get the [[TransactionResponse]].\n     */\n    readonly transactionHash!: string;\n\n    /**\n     *  The block hash of the block this log occurred in. Use the\n     *  [[Log-getBlock]] to get the [[Block]].\n     */\n    readonly blockHash!: string;\n\n    /**\n     *  The block number of the block this log occurred in. It is preferred\n     *  to use the [[Block-hash]] when fetching the related [[Block]],\n     *  since in the case of an orphaned block, the block at that height may\n     *  have changed.\n     */\n    readonly blockNumber!: number;\n\n    /**\n     *  If the **Log** represents a block that was removed due to an orphaned\n     *  block, this will be true.\n     *\n     *  This can only happen within an orphan event listener.\n     */\n    readonly removed!: boolean;\n\n    /**\n     *  The address of the contract that emitted this log.\n     */\n    readonly address!: string;\n\n    /**\n     *  The data included in this log when it was emitted.\n     */\n    readonly data!: string;\n\n    /**\n     *  The indexed topics included in this log when it was emitted.\n     *\n     *  All topics are included in the bloom filters, so they can be\n     *  efficiently filtered using the [[Provider-getLogs]] method.\n     */\n    readonly topics!: ReadonlyArray<string>;\n\n    /**\n     *  The index within the block this log occurred at. This is generally\n     *  not useful to developers, but can be used with the various roots\n     *  to proof inclusion within a block.\n     */\n    readonly index!: number;\n\n    /**\n     *  The index within the transaction of this log.\n     */\n    readonly transactionIndex!: number;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(log: LogParams, provider: Provider) {\n        this.provider = provider;\n\n        const topics = Object.freeze(log.topics.slice());\n        defineProperties<Log>(this, {\n            transactionHash: log.transactionHash,\n            blockHash: log.blockHash,\n            blockNumber: log.blockNumber,\n\n            removed: log.removed,\n\n            address: log.address,\n            data: log.data,\n\n            topics,\n\n            index: log.index,\n            transactionIndex: log.transactionIndex,\n        });\n    }\n\n    /**\n     *  Returns a JSON-compatible object.\n     */\n    toJSON(): any {\n        const {\n            address, blockHash, blockNumber, data, index,\n            removed, topics, transactionHash, transactionIndex\n        } = this;\n\n        return {\n            _type: \"log\",\n            address, blockHash, blockNumber, data, index,\n            removed, topics, transactionHash, transactionIndex\n        };\n    }\n\n    /**\n     *  Returns the block that this log occurred in.\n     */\n    async getBlock(): Promise<Block> {\n        const block = await this.provider.getBlock(this.blockHash);\n        assert(!!block, \"failed to find transaction\", \"UNKNOWN_ERROR\", { });\n        return block;\n    }\n\n    /**\n     *  Returns the transaction that this log occurred in.\n     */\n    async getTransaction(): Promise<TransactionResponse> {\n        const tx = await this.provider.getTransaction(this.transactionHash);\n        assert(!!tx, \"failed to find transaction\", \"UNKNOWN_ERROR\", { });\n        return tx;\n    }\n\n    /**\n     *  Returns the transaction receipt fot the transaction that this\n     *  log occurred in.\n     */\n    async getTransactionReceipt(): Promise<TransactionReceipt> {\n        const receipt = await this.provider.getTransactionReceipt(this.transactionHash);\n        assert(!!receipt, \"failed to find transaction receipt\", \"UNKNOWN_ERROR\", { });\n        return receipt;\n    }\n\n    /**\n     *  @_ignore:\n     */\n    removedEvent(): OrphanFilter {\n        return createRemovedLogFilter(this);\n    }\n}\n\n//////////////////////\n// Transaction Receipt\n\n/*\nexport interface LegacyTransactionReceipt {\n    byzantium: false;\n    status: null;\n    root: string;\n}\n\nexport interface ByzantiumTransactionReceipt {\n    byzantium: true;\n    status: number;\n    root: null;\n}\n*/\n\n/**\n *  A **TransactionReceipt** includes additional information about a\n *  transaction that is only available after it has been mined.\n */\nexport class TransactionReceipt implements TransactionReceiptParams, Iterable<Log> {\n    /**\n     *  The provider connected to the log used to fetch additional details\n     *  if necessary.\n     */\n    readonly provider!: Provider;\n\n    /**\n     *  The address the transaction was sent to.\n     */\n    readonly to!: null | string;\n\n    /**\n     *  The sender of the transaction.\n     */\n    readonly from!: string;\n\n    /**\n     *  The address of the contract if the transaction was directly\n     *  responsible for deploying one.\n     *\n     *  This is non-null **only** if the ``to`` is empty and the ``data``\n     *  was successfully executed as initcode.\n     */\n    readonly contractAddress!: null | string;\n\n    /**\n     *  The transaction hash.\n     */\n    readonly hash!: string;\n\n    /**\n     *  The index of this transaction within the block transactions.\n     */\n    readonly index!: number;\n\n    /**\n     *  The block hash of the [[Block]] this transaction was included in.\n     */\n    readonly blockHash!: string;\n\n    /**\n     *  The block number of the [[Block]] this transaction was included in.\n     */\n    readonly blockNumber!: number;\n\n    /**\n     *  The bloom filter bytes that represent all logs that occurred within\n     *  this transaction. This is generally not useful for most developers,\n     *  but can be used to validate the included logs.\n     */\n    readonly logsBloom!: string;\n\n    /**\n     *  The actual amount of gas used by this transaction.\n     *\n     *  When creating a transaction, the amount of gas that will be used can\n     *  only be approximated, but the sender must pay the gas fee for the\n     *  entire gas limit. After the transaction, the difference is refunded.\n     */\n    readonly gasUsed!: bigint;\n\n    /**\n     *  The gas used for BLObs. See [[link-eip-4844]].\n     */\n    readonly blobGasUsed!: null | bigint;\n\n    /**\n     *  The amount of gas used by all transactions within the block for this\n     *  and all transactions with a lower ``index``.\n     *\n     *  This is generally not useful for developers but can be used to\n     *  validate certain aspects of execution.\n     */\n    readonly cumulativeGasUsed!: bigint;\n\n    /**\n     *  The actual gas price used during execution.\n     *\n     *  Due to the complexity of [[link-eip-1559]] this value can only\n     *  be caluclated after the transaction has been mined, snce the base\n     *  fee is protocol-enforced.\n     */\n    readonly gasPrice!: bigint;\n\n    /**\n     *  The price paid per BLOB in gas. See [[link-eip-4844]].\n     */\n    readonly blobGasPrice!: null | bigint;\n\n    /**\n     *  The [[link-eip-2718]] transaction type.\n     */\n    readonly type!: number;\n    //readonly byzantium!: boolean;\n\n    /**\n     *  The status of this transaction, indicating success (i.e. ``1``) or\n     *  a revert (i.e. ``0``).\n     *\n     *  This is available in post-byzantium blocks, but some backends may\n     *  backfill this value.\n     */\n    readonly status!: null | number;\n\n    /**\n     *  The root hash of this transaction.\n     *\n     *  This is no present and was only included in pre-byzantium blocks, but\n     *  could be used to validate certain parts of the receipt.\n     */\n    readonly root!: null | string;\n\n    readonly #logs: ReadonlyArray<Log>;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(tx: TransactionReceiptParams, provider: Provider) {\n        this.#logs = Object.freeze(tx.logs.map((log) => {\n            return new Log(log, provider);\n        }));\n\n        let gasPrice = BN_0;\n        if (tx.effectiveGasPrice != null) {\n            gasPrice = tx.effectiveGasPrice;\n        } else if (tx.gasPrice != null) {\n            gasPrice = tx.gasPrice;\n        }\n\n        defineProperties<TransactionReceipt>(this, {\n            provider,\n\n            to: tx.to,\n            from: tx.from,\n            contractAddress: tx.contractAddress,\n\n            hash: tx.hash,\n            index: tx.index,\n\n            blockHash: tx.blockHash,\n            blockNumber: tx.blockNumber,\n\n            logsBloom: tx.logsBloom,\n\n            gasUsed: tx.gasUsed,\n            cumulativeGasUsed: tx.cumulativeGasUsed,\n            blobGasUsed: tx.blobGasUsed,\n            gasPrice,\n            blobGasPrice: tx.blobGasPrice,\n\n            type: tx.type,\n            //byzantium: tx.byzantium,\n            status: tx.status,\n            root: tx.root\n        });\n    }\n\n    /**\n     *  The logs for this transaction.\n     */\n    get logs(): ReadonlyArray<Log> { return this.#logs; }\n\n    /**\n     *  Returns a JSON-compatible representation.\n     */\n    toJSON(): any {\n        const {\n            to, from, contractAddress, hash, index,\n            blockHash, blockNumber, logsBloom,\n            logs, //byzantium, \n            status, root\n        } = this;\n\n        return {\n            _type: \"TransactionReceipt\",\n            blockHash, blockNumber,\n            //byzantium, \n            contractAddress,\n            cumulativeGasUsed: toJson(this.cumulativeGasUsed),\n            from,\n            gasPrice: toJson(this.gasPrice),\n            blobGasUsed: toJson(this.blobGasUsed),\n            blobGasPrice: toJson(this.blobGasPrice),\n            gasUsed: toJson(this.gasUsed),\n            hash, index, logs, logsBloom, root, status, to\n        };\n    }\n\n    /**\n     *  @_ignore:\n     */\n    get length(): number { return this.logs.length; }\n\n    [Symbol.iterator](): Iterator<Log> {\n        let index = 0;\n        return {\n            next: () => {\n                if (index < this.length) {\n                    return { value: this.logs[index++], done: false }\n                }\n                return { value: undefined, done: true };\n            }\n        };\n    }\n\n    /**\n     *  The total fee for this transaction, in wei.\n     */\n    get fee(): bigint {\n        return this.gasUsed * this.gasPrice;\n    }\n\n    /**\n     *  Resolves to the block this transaction occurred in.\n     */\n    async getBlock(): Promise<Block> {\n        const block = await this.provider.getBlock(this.blockHash);\n        if (block == null) { throw new Error(\"TODO\"); }\n        return block;\n    }\n\n    /**\n     *  Resolves to the transaction this transaction occurred in.\n     */\n    async getTransaction(): Promise<TransactionResponse> {\n        const tx = await this.provider.getTransaction(this.hash);\n        if (tx == null) { throw new Error(\"TODO\"); }\n        return tx;\n    }\n\n    /**\n     *  Resolves to the return value of the execution of this transaction.\n     *\n     *  Support for this feature is limited, as it requires an archive node\n     *  with the ``debug_`` or ``trace_`` API enabled.\n     */\n    async getResult(): Promise<string> {\n        return <string>(await this.provider.getTransactionResult(this.hash));\n    }\n\n    /**\n     *  Resolves to the number of confirmations this transaction has.\n     */\n    async confirmations(): Promise<number> {\n        return (await this.provider.getBlockNumber()) - this.blockNumber + 1;\n    }\n\n    /**\n     *  @_ignore:\n     */\n    removedEvent(): OrphanFilter {\n        return createRemovedTransactionFilter(this);\n    }\n\n    /**\n     *  @_ignore:\n     */\n    reorderedEvent(other?: TransactionResponse): OrphanFilter {\n        assert(!other || other.isMined(), \"unmined 'other' transction cannot be orphaned\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"reorderedEvent(other)\" });\n        return createReorderedTransactionFilter(this, other);\n    }\n}\n\n\n//////////////////////\n// Transaction Response\n\n/**\n *  A **MinedTransactionResponse** is an interface representing a\n *  transaction which has been mined and allows for a type guard for its\n *  property values being defined.\n */\nexport interface MinedTransactionResponse extends TransactionResponse {\n    /**\n     *  The block number this transaction occurred in.\n     */\n    blockNumber: number;\n\n    /**\n     *  The block hash this transaction occurred in.\n     */\n    blockHash: string;\n\n    /**\n     *  The date this transaction occurred on.\n     */\n    date: Date;\n}\n\n\n/**\n *  A **TransactionResponse** includes all properties about a transaction\n *  that was sent to the network, which may or may not be included in a\n *  block.\n *\n *  The [[TransactionResponse-isMined]] can be used to check if the\n *  transaction has been mined as well as type guard that the otherwise\n *  possibly ``null`` properties are defined.\n */\nexport class TransactionResponse implements TransactionLike<string>, TransactionResponseParams {\n    /**\n     *  The provider this is connected to, which will influence how its\n     *  methods will resolve its async inspection methods.\n     */\n    readonly provider: Provider;\n\n    /**\n     *  The block number of the block that this transaction was included in.\n     *\n     *  This is ``null`` for pending transactions.\n     */\n    readonly blockNumber: null | number;\n\n    /**\n     *  The blockHash of the block that this transaction was included in.\n     *\n     *  This is ``null`` for pending transactions.\n     */\n    readonly blockHash: null | string;\n\n    /**\n     *  The index within the block that this transaction resides at.\n     */\n    readonly index!: number;\n\n    /**\n     *  The transaction hash.\n     */\n    readonly hash!: string;\n\n    /**\n     *  The [[link-eip-2718]] transaction envelope type. This is\n     *  ``0`` for legacy transactions types.\n     */\n    readonly type!: number;\n\n    /**\n     *  The receiver of this transaction.\n     *\n     *  If ``null``, then the transaction is an initcode transaction.\n     *  This means the result of executing the [[data]] will be deployed\n     *  as a new contract on chain (assuming it does not revert) and the\n     *  address may be computed using [[getCreateAddress]].\n     */\n    readonly to!: null | string;\n\n    /**\n     *  The sender of this transaction. It is implicitly computed\n     *  from the transaction pre-image hash (as the digest) and the\n     *  [[signature]] using ecrecover.\n     */\n    readonly from!: string;\n\n    /**\n     *  The nonce, which is used to prevent replay attacks and offer\n     *  a method to ensure transactions from a given sender are explicitly\n     *  ordered.\n     *\n     *  When sending a transaction, this must be equal to the number of\n     *  transactions ever sent by [[from]].\n     */\n    readonly nonce!: number;\n\n    /**\n     *  The maximum units of gas this transaction can consume. If execution\n     *  exceeds this, the entries transaction is reverted and the sender\n     *  is charged for the full amount, despite not state changes being made.\n     */\n    readonly gasLimit!: bigint;\n\n    /**\n     *  The gas price can have various values, depending on the network.\n     *\n     *  In modern networks, for transactions that are included this is\n     *  the //effective gas price// (the fee per gas that was actually\n     *  charged), while for transactions that have not been included yet\n     *  is the [[maxFeePerGas]].\n     *\n     *  For legacy transactions, or transactions on legacy networks, this\n     *  is the fee that will be charged per unit of gas the transaction\n     *  consumes.\n     */\n    readonly gasPrice!: bigint;\n\n    /**\n     *  The maximum priority fee (per unit of gas) to allow a\n     *  validator to charge the sender. This is inclusive of the\n     *  [[maxFeeFeePerGas]].\n     */\n    readonly maxPriorityFeePerGas!: null | bigint;\n\n    /**\n     *  The maximum fee (per unit of gas) to allow this transaction\n     *  to charge the sender.\n     */\n    readonly maxFeePerGas!: null | bigint;\n\n    /**\n     *  The [[link-eip-4844]] max fee per BLOb gas.\n     */\n    readonly maxFeePerBlobGas!: null | bigint;\n\n    /**\n     *  The data.\n     */\n    readonly data!: string;\n\n    /**\n     *  The value, in wei. Use [[formatEther]] to format this value\n     *  as ether.\n     */\n    readonly value!: bigint;\n\n    /**\n     *  The chain ID.\n     */\n    readonly chainId!: bigint;\n\n    /**\n     *  The signature.\n     */\n    readonly signature!: Signature;\n\n    /**\n     *  The [[link-eip-2930]] access list for transaction types that\n     *  support it, otherwise ``null``.\n     */\n    readonly accessList!: null | AccessList;\n\n    /**\n     *  The [[link-eip-4844]] BLOb versioned hashes.\n     */\n    readonly blobVersionedHashes!: null | Array<string>;\n\n    /**\n     *  The [[link-eip-7702]] authorizations (if any).\n     */\n    readonly authorizationList!: null | Array<Authorization>;\n\n    #startBlock: number;\n\n    /**\n     *  @_ignore:\n     */\n    constructor(tx: TransactionResponseParams, provider: Provider) {\n        this.provider = provider;\n\n        this.blockNumber = (tx.blockNumber != null) ? tx.blockNumber: null;\n        this.blockHash = (tx.blockHash != null) ? tx.blockHash: null;\n\n        this.hash = tx.hash;\n        this.index = tx.index;\n\n        this.type = tx.type;\n\n        this.from = tx.from;\n        this.to = tx.to || null;\n\n        this.gasLimit = tx.gasLimit;\n        this.nonce = tx.nonce;\n        this.data = tx.data;\n        this.value = tx.value;\n\n        this.gasPrice = tx.gasPrice;\n        this.maxPriorityFeePerGas = (tx.maxPriorityFeePerGas != null) ? tx.maxPriorityFeePerGas: null;\n        this.maxFeePerGas = (tx.maxFeePerGas != null) ? tx.maxFeePerGas: null;\n        this.maxFeePerBlobGas = (tx.maxFeePerBlobGas != null) ? tx.maxFeePerBlobGas: null;\n\n        this.chainId = tx.chainId;\n        this.signature = tx.signature;\n\n        this.accessList = (tx.accessList != null) ? tx.accessList: null;\n        this.blobVersionedHashes = (tx.blobVersionedHashes != null) ? tx.blobVersionedHashes: null;\n\n        this.authorizationList = (tx.authorizationList != null) ? tx.authorizationList: null;\n\n        this.#startBlock = -1;\n    }\n\n    /**\n     *  Returns a JSON-compatible representation of this transaction.\n     */\n    toJSON(): any {\n        const {\n            blockNumber, blockHash, index, hash, type, to, from, nonce,\n            data, signature, accessList, blobVersionedHashes\n        } = this;\n\n        return {\n            _type: \"TransactionResponse\",\n            accessList, blockNumber, blockHash,\n            blobVersionedHashes,\n            chainId: toJson(this.chainId),\n            data, from,\n            gasLimit: toJson(this.gasLimit),\n            gasPrice: toJson(this.gasPrice),\n            hash,\n            maxFeePerGas: toJson(this.maxFeePerGas),\n            maxPriorityFeePerGas: toJson(this.maxPriorityFeePerGas),\n            maxFeePerBlobGas: toJson(this.maxFeePerBlobGas),\n            nonce, signature, to, index, type,\n            value: toJson(this.value),\n        };\n    }\n\n    /**\n     *  Resolves to the Block that this transaction was included in.\n     *\n     *  This will return null if the transaction has not been included yet.\n     */\n    async getBlock(): Promise<null | Block> {\n        let blockNumber = this.blockNumber;\n        if (blockNumber == null) {\n            const tx = await this.getTransaction();\n            if (tx) { blockNumber = tx.blockNumber; }\n        }\n        if (blockNumber == null) { return null; }\n        const block = this.provider.getBlock(blockNumber);\n        if (block == null) { throw new Error(\"TODO\"); }\n        return block;\n    }\n\n    /**\n     *  Resolves to this transaction being re-requested from the\n     *  provider. This can be used if you have an unmined transaction\n     *  and wish to get an up-to-date populated instance.\n     */\n    async getTransaction(): Promise<null | TransactionResponse> {\n        return this.provider.getTransaction(this.hash);\n    }\n\n    /**\n     *  Resolve to the number of confirmations this transaction has.\n     */\n    async confirmations(): Promise<number> {\n        if (this.blockNumber == null) {\n            const { tx, blockNumber } = await resolveProperties({\n                tx: this.getTransaction(),\n                blockNumber: this.provider.getBlockNumber()\n            });\n\n            // Not mined yet...\n            if (tx == null || tx.blockNumber == null) { return 0; }\n\n            return blockNumber - tx.blockNumber + 1;\n        }\n\n        const blockNumber = await this.provider.getBlockNumber();\n        return blockNumber - this.blockNumber + 1;\n    }\n\n    /**\n     *  Resolves once this transaction has been mined and has\n     *  %%confirms%% blocks including it (default: ``1``) with an\n     *  optional %%timeout%%.\n     *\n     *  This can resolve to ``null`` only if %%confirms%% is ``0``\n     *  and the transaction has not been mined, otherwise this will\n     *  wait until enough confirmations have completed.\n     */\n    async wait(_confirms?: number, _timeout?: number): Promise<null | TransactionReceipt> {\n        const confirms = (_confirms == null) ? 1: _confirms;\n        const timeout = (_timeout == null) ? 0: _timeout;\n\n        let startBlock = this.#startBlock\n        let nextScan = -1;\n        let stopScanning = (startBlock === -1) ? true: false;\n        const checkReplacement = async () => {\n            // Get the current transaction count for this sender\n            if (stopScanning) { return null; }\n            const { blockNumber, nonce } = await resolveProperties({\n                blockNumber: this.provider.getBlockNumber(),\n                nonce: this.provider.getTransactionCount(this.from)\n            });\n\n            // No transaction or our nonce has not been mined yet; but we\n            // can start scanning later when we do start\n            if (nonce < this.nonce) {\n                startBlock = blockNumber;\n                return;\n            }\n\n            // We were mined; no replacement\n            if (stopScanning) { return null; }\n            const mined = await this.getTransaction();\n            if (mined && mined.blockNumber != null) { return; }\n\n            // We were replaced; start scanning for that transaction\n\n            // Starting to scan; look back a few extra blocks for safety\n            if (nextScan === -1) {\n                nextScan = startBlock - 3;\n                if (nextScan < this.#startBlock) { nextScan = this.#startBlock; }\n            }\n\n            while (nextScan <= blockNumber) {\n                // Get the next block to scan\n                if (stopScanning) { return null; }\n                const block = await this.provider.getBlock(nextScan, true);\n\n                // This should not happen; but we'll try again shortly\n                if (block == null) { return; }\n\n                // We were mined; no replacement\n                for (const hash of block) {\n                    if (hash === this.hash) { return; }\n                }\n\n                // Search for the transaction that replaced us\n                for (let i = 0; i < block.length; i++) {\n                    const tx: TransactionResponse = await block.getTransaction(i);\n\n                    if (tx.from === this.from && tx.nonce === this.nonce) {\n                        // Get the receipt\n                        if (stopScanning) { return null; }\n                        const receipt = await this.provider.getTransactionReceipt(tx.hash);\n\n                        // This should not happen; but we'll try again shortly\n                        if (receipt == null) { return; }\n\n                        // We will retry this on the next block (this case could be optimized)\n                        if ((blockNumber - receipt.blockNumber + 1) < confirms) { return; }\n\n                        // The reason we were replaced\n                        let reason: \"replaced\" | \"repriced\" | \"cancelled\" = \"replaced\";\n                        if (tx.data === this.data && tx.to === this.to && tx.value === this.value) {\n                            reason = \"repriced\";\n                        } else  if (tx.data === \"0x\" && tx.from === tx.to && tx.value === BN_0) {\n                            reason = \"cancelled\"\n                        }\n\n                        assert(false, \"transaction was replaced\", \"TRANSACTION_REPLACED\", {\n                            cancelled: (reason === \"replaced\" || reason === \"cancelled\"),\n                            reason,\n                            replacement: tx.replaceableTransaction(startBlock),\n                            hash: tx.hash,\n                            receipt\n                        });\n                    }\n                }\n\n                nextScan++;\n            }\n            return;\n        };\n\n        const checkReceipt = (receipt: null | TransactionReceipt) => {\n            if (receipt == null || receipt.status !== 0) { return receipt; }\n            assert(false, \"transaction execution reverted\", \"CALL_EXCEPTION\", {\n                action: \"sendTransaction\",\n                data: null, reason: null, invocation: null, revert: null,\n                transaction: {\n                    to: receipt.to,\n                    from: receipt.from,\n                    data: \"\" // @TODO: in v7, split out sendTransaction properties\n                }, receipt\n            });\n        };\n\n        const receipt = await this.provider.getTransactionReceipt(this.hash);\n\n        if (confirms === 0) { return checkReceipt(receipt); }\n\n        if (receipt) {\n            if (confirms === 1 || (await receipt.confirmations()) >= confirms) {\n                return checkReceipt(receipt);\n            }\n\n        } else {\n            // Check for a replacement; throws if a replacement was found\n            await checkReplacement();\n\n            // Allow null only when the confirms is 0\n            if (confirms === 0) { return null; }\n        }\n\n        const waiter = new Promise((resolve, reject) => {\n            // List of things to cancel when we have a result (one way or the other)\n            const cancellers: Array<() => void> = [ ];\n            const cancel = () => { cancellers.forEach((c) => c()); };\n\n            // On cancel, stop scanning for replacements\n            cancellers.push(() => { stopScanning = true; });\n\n            // Set up any timeout requested\n            if (timeout > 0) {\n                const timer = setTimeout(() => {\n                    cancel();\n                    reject(makeError(\"wait for transaction timeout\", \"TIMEOUT\"));\n                }, timeout);\n                cancellers.push(() => { clearTimeout(timer); });\n            }\n\n            const txListener = async (receipt: TransactionReceipt) => {\n                // Done; return it!\n                if ((await receipt.confirmations()) >= confirms) {\n                    cancel();\n                    try {\n                        resolve(checkReceipt(receipt));\n                    } catch (error) { reject(error); }\n                }\n            };\n            cancellers.push(() => { this.provider.off(this.hash, txListener); });\n            this.provider.on(this.hash, txListener);\n            // We support replacement detection; start checking\n            if (startBlock >= 0) {\n                const replaceListener = async () => {\n                    try {\n                        // Check for a replacement; this throws only if one is found\n                        await checkReplacement();\n\n                    } catch (error) {\n                        // We were replaced (with enough confirms); re-throw the error\n                        if (isError(error, \"TRANSACTION_REPLACED\")) {\n                            cancel();\n                            reject(error);\n                            return;\n                        }\n                    }\n\n                    // Rescheudle a check on the next block\n                    if (!stopScanning) {\n                        this.provider.once(\"block\", replaceListener);\n                    }\n                };\n                cancellers.push(() => { this.provider.off(\"block\", replaceListener); });\n                this.provider.once(\"block\", replaceListener);\n            }\n        });\n\n        return await <Promise<TransactionReceipt>>waiter;\n    }\n\n    /**\n     *  Returns ``true`` if this transaction has been included.\n     *\n     *  This is effective only as of the time the TransactionResponse\n     *  was instantiated. To get up-to-date information, use\n     *  [[getTransaction]].\n     *\n     *  This provides a Type Guard that this transaction will have\n     *  non-null property values for properties that are null for\n     *  unmined transactions.\n     */\n    isMined(): this is MinedTransactionResponse {\n        return (this.blockHash != null);\n    }\n\n    /**\n     *  Returns true if the transaction is a legacy (i.e. ``type == 0``)\n     *  transaction.\n     *\n     *  This provides a Type Guard that this transaction will have\n     *  the ``null``-ness for hardfork-specific properties set correctly.\n     */\n    isLegacy(): this is (TransactionResponse & { accessList: null, maxFeePerGas: null, maxPriorityFeePerGas: null }) {\n        return (this.type === 0)\n    }\n\n    /**\n     *  Returns true if the transaction is a Berlin (i.e. ``type == 1``)\n     *  transaction. See [[link-eip-2070]].\n     *\n     *  This provides a Type Guard that this transaction will have\n     *  the ``null``-ness for hardfork-specific properties set correctly.\n     */\n    isBerlin(): this is (TransactionResponse & { accessList: AccessList, maxFeePerGas: null, maxPriorityFeePerGas: null }) {\n        return (this.type === 1);\n    }\n\n    /**\n     *  Returns true if the transaction is a London (i.e. ``type == 2``)\n     *  transaction. See [[link-eip-1559]].\n     *\n     *  This provides a Type Guard that this transaction will have\n     *  the ``null``-ness for hardfork-specific properties set correctly.\n     */\n    isLondon(): this is (TransactionResponse & { accessList: AccessList, maxFeePerGas: bigint, maxPriorityFeePerGas: bigint }){\n        return (this.type === 2);\n    }\n\n    /**\n     *  Returns true if hte transaction is a Cancun (i.e. ``type == 3``)\n     *  transaction. See [[link-eip-4844]].\n     */\n    isCancun(): this is (TransactionResponse & { accessList: AccessList, maxFeePerGas: bigint, maxPriorityFeePerGas: bigint, maxFeePerBlobGas: bigint, blobVersionedHashes: Array<string> }){\n        return (this.type === 3);\n    }\n\n    /**\n     *  Returns a filter which can be used to listen for orphan events\n     *  that evict this transaction.\n     */\n    removedEvent(): OrphanFilter {\n        assert(this.isMined(), \"unmined transaction canot be orphaned\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"removeEvent()\" });\n        return createRemovedTransactionFilter(this);\n    }\n\n    /**\n     *  Returns a filter which can be used to listen for orphan events\n     *  that re-order this event against %%other%%.\n     */\n    reorderedEvent(other?: TransactionResponse): OrphanFilter {\n        assert(this.isMined(), \"unmined transaction canot be orphaned\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"removeEvent()\" });\n\n        assert(!other || other.isMined(), \"unmined 'other' transaction canot be orphaned\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"removeEvent()\" });\n\n        return createReorderedTransactionFilter(this, other);\n    }\n\n    /**\n     *  Returns a new TransactionResponse instance which has the ability to\n     *  detect (and throw an error) if the transaction is replaced, which\n     *  will begin scanning at %%startBlock%%.\n     *\n     *  This should generally not be used by developers and is intended\n     *  primarily for internal use. Setting an incorrect %%startBlock%% can\n     *  have devastating performance consequences if used incorrectly.\n     */\n    replaceableTransaction(startBlock: number): TransactionResponse {\n        assertArgument(Number.isInteger(startBlock) && startBlock >= 0, \"invalid startBlock\", \"startBlock\", startBlock);\n        const tx = new TransactionResponse(this, this.provider);\n        tx.#startBlock = startBlock;\n        return tx;\n    }\n}\n\n\n//////////////////////\n// OrphanFilter\n\n/**\n *  An Orphan Filter allows detecting when an orphan block has\n *  resulted in dropping a block or transaction or has resulted\n *  in transactions changing order.\n *\n *  Not currently fully supported.\n */\nexport type OrphanFilter = {\n    orphan: \"drop-block\",\n    hash: string,\n    number: number\n} | {\n    orphan: \"drop-transaction\",\n    tx: { hash: string, blockHash: string, blockNumber: number },\n    other?: { hash: string, blockHash: string, blockNumber: number }\n} | {\n    orphan: \"reorder-transaction\",\n    tx: { hash: string, blockHash: string, blockNumber: number },\n    other?: { hash: string, blockHash: string, blockNumber: number }\n} | {\n    orphan: \"drop-log\",\n    log: {\n        transactionHash: string,\n        blockHash: string,\n        blockNumber: number,\n        address: string,\n        data: string,\n        topics: ReadonlyArray<string>,\n        index: number\n    }\n};\n\nfunction createOrphanedBlockFilter(block: { hash: string, number: number }): OrphanFilter {\n    return { orphan: \"drop-block\", hash: block.hash, number: block.number };\n}\n\nfunction createReorderedTransactionFilter(tx: { hash: string, blockHash: string, blockNumber: number }, other?: { hash: string, blockHash: string, blockNumber: number }): OrphanFilter {\n    return { orphan: \"reorder-transaction\", tx, other };\n}\n\nfunction createRemovedTransactionFilter(tx: { hash: string, blockHash: string, blockNumber: number }): OrphanFilter {\n    return { orphan: \"drop-transaction\", tx };\n}\n\nfunction createRemovedLogFilter(log: { blockHash: string, transactionHash: string, blockNumber: number, address: string, data: string, topics: ReadonlyArray<string>, index: number }): OrphanFilter {\n    return { orphan: \"drop-log\", log: {\n        transactionHash: log.transactionHash,\n        blockHash: log.blockHash,\n        blockNumber: log.blockNumber,\n        address: log.address,\n        data: log.data,\n        topics: Object.freeze(log.topics.slice()),\n        index: log.index\n    } };\n}\n\n//////////////////////\n// EventFilter\n\n/**\n *  A **TopicFilter** provides a struture to define bloom-filter\n *  queries.\n *\n *  Each field that is ``null`` matches **any** value, a field that is\n *  a ``string`` must match exactly that value and ``array`` is\n *  effectively an ``OR``-ed set, where any one of those values must\n *  match.\n */\nexport type TopicFilter = Array<null | string | Array<string>>;\n\n// @TODO:\n//export type DeferableTopicFilter = Array<null | string | Promise<string> | Array<string | Promise<string>>>;\n\n/**\n *  An **EventFilter** allows efficiently filtering logs (also known as\n *  events) using bloom filters included within blocks.\n */\nexport interface EventFilter {\n    address?: AddressLike | Array<AddressLike>;\n    topics?: TopicFilter;\n}\n\n/**\n *  A **Filter** allows searching a specific range of blocks for mathcing\n *  logs.\n */\nexport interface Filter extends EventFilter {\n\n    /**\n     *  The start block for the filter (inclusive).\n     */\n    fromBlock?: BlockTag;\n\n    /**\n     *  The end block for the filter (inclusive).\n     */\n    toBlock?: BlockTag;\n}\n\n/**\n *  A **FilterByBlockHash** allows searching a specific block for mathcing\n *  logs.\n */\nexport interface FilterByBlockHash extends EventFilter {\n    /**\n     *  The blockhash of the specific block for the filter.\n     */\n    blockHash?: string;\n}\n\n\n//////////////////////\n// ProviderEvent\n\n/**\n *  A **ProviderEvent** provides the types of events that can be subscribed\n *  to on a [[Provider]].\n *\n *  Each provider may include additional possible events it supports, but\n *  the most commonly supported are:\n *\n *  **``\"block\"``** - calls the listener with the current block number on each\n *  new block.\n *\n *  **``\"error\"``** - calls the listener on each async error that occurs during\n *  the event loop, with the error.\n *\n *  **``\"debug\"``** - calls the listener on debug events, which can be used to\n *  troubleshoot network errors, provider problems, etc.\n *\n *  **``transaction hash``** - calls the listener on each block after the\n *  transaction has been mined; generally ``.once`` is more appropriate for\n *  this event.\n *\n *  **``Array``** - calls the listener on each log that matches the filter.\n *\n *  [[EventFilter]] - calls the listener with each matching log\n */\nexport type ProviderEvent = string | Array<string | Array<string>> | EventFilter | OrphanFilter;\n\n\n//////////////////////\n// Provider\n\n/**\n *  A **Provider** is the primary method to interact with the read-only\n *  content on Ethereum.\n *\n *  It allows access to details about accounts, blocks and transactions\n *  and the ability to query event logs and simulate contract execution.\n *\n *  Account data includes the [balance](getBalance),\n *  [transaction count](getTransactionCount), [code](getCode) and\n *  [state trie storage](getStorage).\n *\n *  Simulating execution can be used to [call](call),\n *  [estimate gas](estimateGas) and\n *  [get transaction results](getTransactionResult).\n *\n *  The [[broadcastTransaction]] is the only method which allows updating\n *  the blockchain, but it is usually accessed by a [[Signer]], since a\n *  private key must be used to sign the transaction before it can be\n *  broadcast.\n */\nexport interface Provider extends ContractRunner, EventEmitterable<ProviderEvent>, NameResolver {\n\n    /**\n     *  The provider iteself.\n     *\n     *  This is part of the necessary API for executing a contract, as\n     *  it provides a common property on any [[ContractRunner]] that\n     *  can be used to access the read-only portion of the runner.\n     */\n    provider: this;\n\n    /**\n     *  Shutdown any resources this provider is using. No additional\n     *  calls should be made to this provider after calling this.\n     */\n    destroy(): void;\n\n    ////////////////////\n    // State\n\n    /**\n     *  Get the current block number.\n     */\n    getBlockNumber(): Promise<number>;\n\n    /**\n     *  Get the connected [[Network]].\n     */\n    getNetwork(): Promise<Network>;\n\n    /**\n     *  Get the best guess at the recommended [[FeeData]].\n     */\n    getFeeData(): Promise<FeeData>;\n\n\n    ////////////////////\n    // Account\n\n    /**\n     *  Get the account balance (in wei) of %%address%%. If %%blockTag%%\n     *  is specified and the node supports archive access for that\n     *  %%blockTag%%, the balance is as of that [[BlockTag]].\n     *\n     *  @note On nodes without archive access enabled, the %%blockTag%% may be\n     *        **silently ignored** by the node, which may cause issues if relied on.\n     */\n    getBalance(address: AddressLike, blockTag?: BlockTag): Promise<bigint>;\n\n    /**\n     *  Get the number of transactions ever sent for %%address%%, which\n     *  is used as the ``nonce`` when sending a transaction. If\n     *  %%blockTag%% is specified and the node supports archive access\n     *  for that %%blockTag%%, the transaction count is as of that\n     *  [[BlockTag]].\n     *\n     *  @note On nodes without archive access enabled, the %%blockTag%% may be\n     *        **silently ignored** by the node, which may cause issues if relied on.\n     */\n    getTransactionCount(address: AddressLike, blockTag?: BlockTag): Promise<number>;\n\n    /**\n     *  Get the bytecode for %%address%%.\n     *\n     *  @note On nodes without archive access enabled, the %%blockTag%% may be\n     *        **silently ignored** by the node, which may cause issues if relied on.\n     */\n    getCode(address: AddressLike, blockTag?: BlockTag): Promise<string>\n\n    /**\n     *  Get the storage slot value for %%address%% at slot %%position%%.\n     *\n     *  @note On nodes without archive access enabled, the %%blockTag%% may be\n     *        **silently ignored** by the node, which may cause issues if relied on.\n     */\n    getStorage(address: AddressLike, position: BigNumberish, blockTag?: BlockTag): Promise<string>\n\n\n    ////////////////////\n    // Execution\n\n    /**\n     *  Estimates the amount of gas required to execute %%tx%%.\n     */\n    estimateGas(tx: TransactionRequest): Promise<bigint>;\n\n    /**\n     *  Simulate the execution of %%tx%%. If the call reverts, it will\n     *  throw a [[CallExceptionError]] which includes the revert data.\n     */\n    call(tx: TransactionRequest): Promise<string>\n\n    /**\n     *  Broadcasts the %%signedTx%% to the network, adding it to the\n     *  memory pool of any node for which the transaction meets the\n     *  rebroadcast requirements.\n     */\n    broadcastTransaction(signedTx: string): Promise<TransactionResponse>;\n\n\n    ////////////////////\n    // Queries\n\n    /**\n     *  Resolves to the block for %%blockHashOrBlockTag%%.\n     *\n     *  If %%prefetchTxs%%, and the backend supports including transactions\n     *  with block requests, all transactions will be included and the\n     *  [[Block]] object will not need to make remote calls for getting\n     *  transactions.\n     */\n    getBlock(blockHashOrBlockTag: BlockTag | string, prefetchTxs?: boolean): Promise<null | Block>;\n\n    /**\n     *  Resolves to the transaction for %%hash%%.\n     *\n     *  If the transaction is unknown or on pruning nodes which\n     *  discard old transactions this resolves to ``null``.\n     */\n    getTransaction(hash: string): Promise<null | TransactionResponse>;\n\n    /**\n     *  Resolves to the transaction receipt for %%hash%%, if mined.\n     *\n     *  If the transaction has not been mined, is unknown or on\n     *  pruning nodes which discard old transactions this resolves to\n     *  ``null``.\n     */\n    getTransactionReceipt(hash: string): Promise<null | TransactionReceipt>;\n\n    /**\n     *  Resolves to the result returned by the executions of %%hash%%.\n     *\n     *  This is only supported on nodes with archive access and with\n     *  the necessary debug APIs enabled.\n     */\n    getTransactionResult(hash: string): Promise<null | string>;\n\n\n    ////////////////////\n    // Bloom-filter Queries\n\n    /**\n     *  Resolves to the list of Logs that match %%filter%%\n     */\n    getLogs(filter: Filter | FilterByBlockHash): Promise<Array<Log>>;\n\n\n    ////////////////////\n    // ENS\n\n    /**\n     *  Resolves to the address configured for the %%ensName%% or\n     *  ``null`` if unconfigured.\n     */\n    resolveName(ensName: string): Promise<null | string>;\n\n    /**\n     *  Resolves to the ENS name associated for the %%address%% or\n     *  ``null`` if the //primary name// is not configured.\n     *\n     *  Users must perform additional steps to configure a //primary name//,\n     *  which is not currently common.\n     */\n    lookupAddress(address: string): Promise<null | string>;\n\n    /**\n     *  Waits until the transaction %%hash%% is mined and has %%confirms%%\n     *  confirmations.\n     */\n    waitForTransaction(hash: string, confirms?: number, timeout?: number): Promise<null | TransactionReceipt>;\n\n    /**\n     *  Resolves to the block at %%blockTag%% once it has been mined.\n     *\n     *  This can be useful for waiting some number of blocks by using\n     *  the ``currentBlockNumber + N``.\n     */\n    waitForBlock(blockTag?: BlockTag): Promise<Block>;\n}\n"], "mappings": "AAAA;AACA,SACIA,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAC5DC,iBAAiB,EACjBC,MAAM,EAAEC,cAAc,EAAEC,OAAO,EAAEC,SAAS,QACvC,mBAAmB;AAC1B,SAASC,aAAa,QAAQ,yBAAyB;AAcvD,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AAsBtB;AAEA,SAASC,QAAQA,CAAIC,KAA2B;EAC5C,IAAIA,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,IAAI;;EAChC,OAAOA,KAAK;AAChB;AAEA,SAASC,MAAMA,CAACD,KAAoB;EAChC,IAAIA,KAAK,IAAI,IAAI,EAAE;IAAE,OAAO,IAAI;;EAChC,OAAOA,KAAK,CAACE,QAAQ,EAAE;AAC3B;AAEA;AAEA;;;;AAIA,OAAM,MAAOC,OAAO;EAChB;;;EAGSC,QAAQ;EAEjB;;;;;;;;;;;;EAYSC,YAAY;EAErB;;;;;;;;;EASSC,oBAAoB;EAE7B;;;;EAIAC,YAAYH,QAAwB,EAAEC,YAA4B,EAAEC,oBAAoC;IACpGpB,gBAAgB,CAAU,IAAI,EAAE;MAC5BkB,QAAQ,EAAEL,QAAQ,CAACK,QAAQ,CAAC;MAC5BC,YAAY,EAAEN,QAAQ,CAACM,YAAY,CAAC;MACpCC,oBAAoB,EAAEP,QAAQ,CAACO,oBAAoB;KACtD,CAAC;EACN;EAEA;;;EAGAE,MAAMA,CAAA;IACF,MAAM;MACFJ,QAAQ;MAAEC,YAAY;MAAEC;IAAoB,CAC/C,GAAG,IAAI;IACR,OAAO;MACHG,KAAK,EAAE,SAAS;MAChBL,QAAQ,EAAEH,MAAM,CAACG,QAAQ,CAAC;MAC1BC,YAAY,EAAEJ,MAAM,CAACI,YAAY,CAAC;MAClCC,oBAAoB,EAAEL,MAAM,CAACK,oBAAoB;KACpD;EACL;;AAuIH;AA4GD;;;;AAIA,OAAM,SAAUI,WAAWA,CAACC,GAAuB;EAC/C,MAAMC,MAAM,GAAQ,EAAG;EAEvB;EACA,IAAID,GAAG,CAACE,EAAE,EAAE;IAAED,MAAM,CAACC,EAAE,GAAGF,GAAG,CAACE,EAAE;;EAChC,IAAIF,GAAG,CAACG,IAAI,EAAE;IAAEF,MAAM,CAACE,IAAI,GAAGH,GAAG,CAACG,IAAI;;EAEtC,IAAIH,GAAG,CAACI,IAAI,EAAE;IAAEH,MAAM,CAACG,IAAI,GAAG1B,OAAO,CAACsB,GAAG,CAACI,IAAI,CAAC;;EAE/C,MAAMC,UAAU,GAAG,oFAAoF,CAACC,KAAK,CAAC,GAAG,CAAC;EAClH,KAAK,MAAMC,GAAG,IAAIF,UAAU,EAAE;IAC1B,IAAI,EAAEE,GAAG,IAAIP,GAAG,CAAC,IAAUA,GAAI,CAACO,GAAG,CAAC,IAAI,IAAI,EAAE;MAAE;;IAChDN,MAAM,CAACM,GAAG,CAAC,GAAG/B,SAAS,CAAOwB,GAAI,CAACO,GAAG,CAAC,EAAE,WAAYA,GAAI,EAAE,CAAC;;EAGhE,MAAMC,UAAU,GAAG,YAAY,CAACF,KAAK,CAAC,GAAG,CAAC;EAC1C,KAAK,MAAMC,GAAG,IAAIC,UAAU,EAAE;IAC1B,IAAI,EAAED,GAAG,IAAIP,GAAG,CAAC,IAAUA,GAAI,CAACO,GAAG,CAAC,IAAI,IAAI,EAAE;MAAE;;IAChDN,MAAM,CAACM,GAAG,CAAC,GAAG9B,SAAS,CAAOuB,GAAI,CAACO,GAAG,CAAC,EAAE,WAAYA,GAAI,EAAE,CAAC;;EAGhE,IAAIP,GAAG,CAACS,UAAU,EAAE;IAChBR,MAAM,CAACQ,UAAU,GAAGxB,aAAa,CAACe,GAAG,CAACS,UAAU,CAAC;;EAGrD,IAAIT,GAAG,CAACU,iBAAiB,EAAE;IACvBT,MAAM,CAACS,iBAAiB,GAAGV,GAAG,CAACU,iBAAiB,CAACC,KAAK,EAAE;;EAG5D,IAAI,UAAU,IAAIX,GAAG,EAAE;IAAEC,MAAM,CAACW,QAAQ,GAAGZ,GAAG,CAACY,QAAQ;;EAEvD,IAAI,gBAAgB,IAAIZ,GAAG,EAAE;IACzBC,MAAM,CAACY,cAAc,GAAG,CAAC,CAACb,GAAG,CAACa,cAAc;;EAGhD,IAAI,YAAY,IAAIb,GAAG,EAAE;IACrBC,MAAM,CAACa,UAAU,GAAGd,GAAG,CAACc,UAAU;;EAGtC,IAAI,qBAAqB,IAAId,GAAG,IAAIA,GAAG,CAACe,mBAAmB,EAAE;IACzDd,MAAM,CAACc,mBAAmB,GAAGf,GAAG,CAACe,mBAAmB,CAACJ,KAAK,EAAE;;EAGhE,IAAI,KAAK,IAAIX,GAAG,EAAE;IAAEC,MAAM,CAACe,GAAG,GAAGhB,GAAG,CAACgB,GAAG;;EAExC,IAAI,OAAO,IAAIhB,GAAG,IAAIA,GAAG,CAACiB,KAAK,EAAE;IAC7BhB,MAAM,CAACgB,KAAK,GAAGjB,GAAG,CAACiB,KAAK,CAACC,GAAG,CAAEC,CAAC,IAAI;MAC/B,IAAIxC,WAAW,CAACwC,CAAC,CAAC,EAAE;QAAE,OAAOzC,OAAO,CAACyC,CAAC,CAAC;;MACvC,OAAOC,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEF,CAAC,CAAC;IAChC,CAAC,CAAC;;EAGN,OAAOlB,MAAM;AACjB;AAwCA;;;;AAIA,OAAM,MAAOqB,KAAK;EAEd;;;;EAISC,QAAQ;EAEjB;;;;EAISC,MAAM;EAEf;;;;;;EAMSC,IAAI;EAEb;;;;EAISC,SAAS;EAElB;;;EAGSC,UAAU;EAEnB;;;;EAIAC,qBAAqB;EAErB;;;;;;EAMSC,KAAK;EAEd;;;;;;;;;EASSC,UAAU;EAGnB;;;EAGSC,QAAQ;EAEjB;;;EAGSC,OAAO;EAGhB;;;;EAISC,SAAS;EAElB;;;EAGSC,YAAY;EAErB;;;;EAISC,WAAW;EAEpB;;;;EAISC,aAAa;EAEtB;;;;EAISC,KAAK;EAEd;;;;EAISC,UAAU;EAEnB;;;EAGSC,SAAS;EAElB;;;;;;;EAOSC,aAAa;EAEb,CAAAC,YAAa;EAEtB;;;;;;EAMA7C,YAAY8C,KAAkB,EAAEnB,QAAkB;IAE9C,IAAI,CAAC,CAAAkB,YAAa,GAAGC,KAAK,CAACD,YAAY,CAACvB,GAAG,CAAEyB,EAAE,IAAI;MAC/C,IAAI,OAAOA,EAAG,KAAK,QAAQ,EAAE;QACzB,OAAO,IAAIC,mBAAmB,CAACD,EAAE,EAAEpB,QAAQ,CAAC;;MAEhD,OAAOoB,EAAE;IACb,CAAC,CAAC;IAEFpE,gBAAgB,CAAQ,IAAI,EAAE;MAC1BgD,QAAQ;MAERE,IAAI,EAAErC,QAAQ,CAACsD,KAAK,CAACjB,IAAI,CAAC;MAE1BD,MAAM,EAAEkB,KAAK,CAAClB,MAAM;MACpBE,SAAS,EAAEgB,KAAK,CAAChB,SAAS;MAE1BC,UAAU,EAAEe,KAAK,CAACf,UAAU;MAC5BC,qBAAqB,EAAEc,KAAK,CAACd,qBAAqB;MAElDC,KAAK,EAAEa,KAAK,CAACb,KAAK;MAClBC,UAAU,EAAEY,KAAK,CAACZ,UAAU;MAE5BC,QAAQ,EAAEW,KAAK,CAACX,QAAQ;MACxBC,OAAO,EAAEU,KAAK,CAACV,OAAO;MACtBG,WAAW,EAAEO,KAAK,CAACP,WAAW;MAC9BC,aAAa,EAAEM,KAAK,CAACN,aAAa;MAClCC,KAAK,EAAEK,KAAK,CAACL,KAAK;MAClBC,UAAU,EAAElD,QAAQ,CAACsD,KAAK,CAACJ,UAAU,CAAC;MACtCC,SAAS,EAAEG,KAAK,CAACH,SAAS;MAE1BC,aAAa,EAAEpD,QAAQ,CAACsD,KAAK,CAACF,aAAa,CAAC;MAE5CP,SAAS,EAAES,KAAK,CAACT,SAAS;MAC1BC,YAAY,EAAEQ,KAAK,CAACR;KACvB,CAAC;EACN;EAEA;;;;EAIA,IAAIO,YAAYA,CAAA;IACZ,OAAO,IAAI,CAAC,CAAAA,YAAa,CAACvB,GAAG,CAAEyB,EAAE,IAAI;MACjC,IAAI,OAAOA,EAAG,KAAK,QAAQ,EAAE;QAAE,OAAOA,EAAE;;MACxC,OAAOA,EAAE,CAAClB,IAAI;IAClB,CAAC,CAAC;EACN;EAEA;;;;;;;;EAQA,IAAIoB,sBAAsBA,CAAA;IACtB,MAAMC,GAAG,GAAG,IAAI,CAAC,CAAAL,YAAa,CAAC9B,KAAK,EAAE;IAEtC;IACA,IAAImC,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO,EAAG;;IAElC;IACAlE,MAAM,CAAC,OAAOiE,GAAG,CAAC,CAAC,CAAE,KAAK,QAAQ,EAAE,qDAAqD,EAAE,uBAAuB,EAAE;MAChHE,SAAS,EAAE;KACd,CAAC;IAEF,OAAmCF,GAAG;EAC1C;EAEA;;;EAGAjD,MAAMA,CAAA;IACF,MAAM;MACF2C,aAAa;MAAEV,UAAU;MAAES,SAAS;MAAER,QAAQ;MAAEC,OAAO;MAAEP,IAAI;MAC7DY,KAAK;MAAEC,UAAU;MAAET,KAAK;MAAEL,MAAM;MAAEG,UAAU;MAAEC,qBAAqB;MACnEK,SAAS;MAAEC,YAAY;MAAER,SAAS;MAAEe;IAAY,CACnD,GAAG,IAAI;IAER,OAAO;MACH3C,KAAK,EAAE,OAAO;MACd0C,aAAa,EAAElD,MAAM,CAACkD,aAAa,CAAC;MACpCV,UAAU,EAAExC,MAAM,CAACwC,UAAU,CAAC;MAC9BS,SAAS;MACTR,QAAQ,EAAEzC,MAAM,CAACyC,QAAQ,CAAC;MAC1BC,OAAO,EAAE1C,MAAM,CAAC0C,OAAO,CAAC;MACxBG,WAAW,EAAE7C,MAAM,CAAC,IAAI,CAAC6C,WAAW,CAAC;MACrCC,aAAa,EAAE9C,MAAM,CAAC,IAAI,CAAC8C,aAAa,CAAC;MACzCX,IAAI;MAAEY,KAAK;MAAEC,UAAU;MAAET,KAAK;MAAEL,MAAM;MAAEG,UAAU;MAAED,SAAS;MAC7DE,qBAAqB;MAAEK,SAAS;MAAEC,YAAY;MAC9CO;KACH;EACL;EAEA,CAACQ,MAAM,CAACC,QAAQ,IAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,MAAML,GAAG,GAAG,IAAI,CAACL,YAAY;IAC7B,OAAO;MACHW,IAAI,EAAEA,CAAA,KAAK;QACP,IAAID,KAAK,GAAG,IAAI,CAACJ,MAAM,EAAE;UACrB,OAAO;YACH1D,KAAK,EAAEyD,GAAG,CAACK,KAAK,EAAE,CAAC;YAAEE,IAAI,EAAE;WAC9B;;QAEL,OAAO;UAAEhE,KAAK,EAAEiE,SAAS;UAAED,IAAI,EAAE;QAAI,CAAE;MAC3C;KACH;EACL;EAEA;;;EAGA,IAAIN,MAAMA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAN,YAAa,CAACM,MAAM;EAAE;EAEzD;;;EAGA,IAAIQ,IAAIA,CAAA;IACJ,IAAI,IAAI,CAAC7B,SAAS,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACzC,OAAO,IAAI8B,IAAI,CAAC,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAAC;EAC1C;EAEA;;;EAGA,MAAM+B,cAAcA,CAACC,WAA4B;IAC7C;IACA,IAAIf,EAAE,GAA6CW,SAAS;IAC5D,IAAI,OAAOI,WAAY,KAAK,QAAQ,EAAE;MAClCf,EAAE,GAAG,IAAI,CAAC,CAAAF,YAAa,CAACiB,WAAW,CAAC;KAEvC,MAAM;MACH,MAAMjC,IAAI,GAAGiC,WAAW,CAACC,WAAW,EAAE;MACtC,KAAK,MAAMC,CAAC,IAAI,IAAI,CAAC,CAAAnB,YAAa,EAAE;QAChC,IAAI,OAAOmB,CAAE,KAAK,QAAQ,EAAE;UACxB,IAAIA,CAAC,KAAKnC,IAAI,EAAE;YAAE;;UAClBkB,EAAE,GAAGiB,CAAC;UACN;SACH,MAAM;UACH,IAAIA,CAAC,CAACnC,IAAI,KAAKA,IAAI,EAAE;YAAE;;UACvBkB,EAAE,GAAGiB,CAAC;UACN;;;;IAIZ,IAAIjB,EAAE,IAAI,IAAI,EAAE;MAAE,MAAM,IAAIkB,KAAK,CAAC,YAAY,CAAC;;IAE/C,IAAI,OAAOlB,EAAG,KAAK,QAAQ,EAAE;MACzB,OAA6B,MAAM,IAAI,CAACpB,QAAQ,CAACkC,cAAc,CAACd,EAAE,CAAC;KACtE,MAAM;MACH,OAAOA,EAAE;;EAEjB;EAEA;;;;;;EAMAmB,wBAAwBA,CAACJ,WAA4B;IACjD,MAAMZ,GAAG,GAAG,IAAI,CAACD,sBAAsB;IACvC,IAAI,OAAOa,WAAY,KAAK,QAAQ,EAAE;MAClC,OAAOZ,GAAG,CAACY,WAAW,CAAC;;IAG3BA,WAAW,GAAGA,WAAW,CAACC,WAAW,EAAE;IACvC,KAAK,MAAMhB,EAAE,IAAIG,GAAG,EAAE;MAClB,IAAIH,EAAE,CAAClB,IAAI,KAAKiC,WAAW,EAAE;QAAE,OAAOf,EAAE;;;IAG5C7D,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,aAAa,EAAE4E,WAAW,CAAC;EAChF;EAEA;;;;EAIAK,OAAOA,CAAA;IAAyB,OAAO,CAAC,CAAC,IAAI,CAACtC,IAAI;EAAE;EAEpD;;;EAGAuC,QAAQA,CAAA;IACJ,OAAO,CAAC,CAAC,IAAI,CAACxB,aAAa;EAC/B;EAEA;;;EAGAyB,aAAaA,CAAA;IACT,IAAI,CAAC,IAAI,CAACF,OAAO,EAAE,EAAE;MAAE,MAAM,IAAIF,KAAK,CAAC,EAAE,CAAC;;IAC1C,OAAOK,yBAAyB,CAAC,IAAI,CAAC;EAC1C;;AAGJ;AACA;AAEA;;;;;AAKA,OAAM,MAAOC,GAAG;EAEZ;;;;EAIS5C,QAAQ;EAEjB;;;;EAIS6C,eAAe;EAExB;;;;EAISC,SAAS;EAElB;;;;;;EAMSC,WAAW;EAEpB;;;;;;EAMSC,OAAO;EAEhB;;;EAGSC,OAAO;EAEhB;;;EAGSpE,IAAI;EAEb;;;;;;EAMSqE,MAAM;EAEf;;;;;EAKStB,KAAK;EAEd;;;EAGSuB,gBAAgB;EAEzB;;;EAGA9E,YAAY+E,GAAc,EAAEpD,QAAkB;IAC1C,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAExB,MAAMkD,MAAM,GAAGrD,MAAM,CAACwD,MAAM,CAACD,GAAG,CAACF,MAAM,CAAC9D,KAAK,EAAE,CAAC;IAChDpC,gBAAgB,CAAM,IAAI,EAAE;MACxB6F,eAAe,EAAEO,GAAG,CAACP,eAAe;MACpCC,SAAS,EAAEM,GAAG,CAACN,SAAS;MACxBC,WAAW,EAAEK,GAAG,CAACL,WAAW;MAE5BC,OAAO,EAAEI,GAAG,CAACJ,OAAO;MAEpBC,OAAO,EAAEG,GAAG,CAACH,OAAO;MACpBpE,IAAI,EAAEuE,GAAG,CAACvE,IAAI;MAEdqE,MAAM;MAENtB,KAAK,EAAEwB,GAAG,CAACxB,KAAK;MAChBuB,gBAAgB,EAAEC,GAAG,CAACD;KACzB,CAAC;EACN;EAEA;;;EAGA7E,MAAMA,CAAA;IACF,MAAM;MACF2E,OAAO;MAAEH,SAAS;MAAEC,WAAW;MAAElE,IAAI;MAAE+C,KAAK;MAC5CoB,OAAO;MAAEE,MAAM;MAAEL,eAAe;MAAEM;IAAgB,CACrD,GAAG,IAAI;IAER,OAAO;MACH5E,KAAK,EAAE,KAAK;MACZ0E,OAAO;MAAEH,SAAS;MAAEC,WAAW;MAAElE,IAAI;MAAE+C,KAAK;MAC5CoB,OAAO;MAAEE,MAAM;MAAEL,eAAe;MAAEM;KACrC;EACL;EAEA;;;EAGA,MAAMG,QAAQA,CAAA;IACV,MAAMnC,KAAK,GAAG,MAAM,IAAI,CAACnB,QAAQ,CAACsD,QAAQ,CAAC,IAAI,CAACR,SAAS,CAAC;IAC1DxF,MAAM,CAAC,CAAC,CAAC6D,KAAK,EAAE,4BAA4B,EAAE,eAAe,EAAE,EAAG,CAAC;IACnE,OAAOA,KAAK;EAChB;EAEA;;;EAGA,MAAMe,cAAcA,CAAA;IAChB,MAAMd,EAAE,GAAG,MAAM,IAAI,CAACpB,QAAQ,CAACkC,cAAc,CAAC,IAAI,CAACW,eAAe,CAAC;IACnEvF,MAAM,CAAC,CAAC,CAAC8D,EAAE,EAAE,4BAA4B,EAAE,eAAe,EAAE,EAAG,CAAC;IAChE,OAAOA,EAAE;EACb;EAEA;;;;EAIA,MAAMmC,qBAAqBA,CAAA;IACvB,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACxD,QAAQ,CAACuD,qBAAqB,CAAC,IAAI,CAACV,eAAe,CAAC;IAC/EvF,MAAM,CAAC,CAAC,CAACkG,OAAO,EAAE,oCAAoC,EAAE,eAAe,EAAE,EAAG,CAAC;IAC7E,OAAOA,OAAO;EAClB;EAEA;;;EAGAC,YAAYA,CAAA;IACR,OAAOC,sBAAsB,CAAC,IAAI,CAAC;EACvC;;AAGJ;AACA;AAEA;;;;;;;;;;;;;AAcA;;;;AAIA,OAAM,MAAOC,kBAAkB;EAC3B;;;;EAIS3D,QAAQ;EAEjB;;;EAGSrB,EAAE;EAEX;;;EAGSC,IAAI;EAEb;;;;;;;EAOSgF,eAAe;EAExB;;;EAGS1D,IAAI;EAEb;;;EAGS0B,KAAK;EAEd;;;EAGSkB,SAAS;EAElB;;;EAGSC,WAAW;EAEpB;;;;;EAKSc,SAAS;EAElB;;;;;;;EAOSpD,OAAO;EAEhB;;;EAGSG,WAAW;EAEpB;;;;;;;EAOSkD,iBAAiB;EAE1B;;;;;;;EAOS5F,QAAQ;EAEjB;;;EAGS6F,YAAY;EAErB;;;EAGSC,IAAI;EACb;EAEA;;;;;;;EAOSC,MAAM;EAEf;;;;;;EAMSC,IAAI;EAEJ,CAAAC,IAAK;EAEd;;;EAGA9F,YAAY+C,EAA4B,EAAEpB,QAAkB;IACxD,IAAI,CAAC,CAAAmE,IAAK,GAAGtE,MAAM,CAACwD,MAAM,CAACjC,EAAE,CAAC+C,IAAI,CAACxE,GAAG,CAAEyD,GAAG,IAAI;MAC3C,OAAO,IAAIR,GAAG,CAACQ,GAAG,EAAEpD,QAAQ,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,IAAI9B,QAAQ,GAAGP,IAAI;IACnB,IAAIyD,EAAE,CAACgD,iBAAiB,IAAI,IAAI,EAAE;MAC9BlG,QAAQ,GAAGkD,EAAE,CAACgD,iBAAiB;KAClC,MAAM,IAAIhD,EAAE,CAAClD,QAAQ,IAAI,IAAI,EAAE;MAC5BA,QAAQ,GAAGkD,EAAE,CAAClD,QAAQ;;IAG1BlB,gBAAgB,CAAqB,IAAI,EAAE;MACvCgD,QAAQ;MAERrB,EAAE,EAAEyC,EAAE,CAACzC,EAAE;MACTC,IAAI,EAAEwC,EAAE,CAACxC,IAAI;MACbgF,eAAe,EAAExC,EAAE,CAACwC,eAAe;MAEnC1D,IAAI,EAAEkB,EAAE,CAAClB,IAAI;MACb0B,KAAK,EAAER,EAAE,CAACQ,KAAK;MAEfkB,SAAS,EAAE1B,EAAE,CAAC0B,SAAS;MACvBC,WAAW,EAAE3B,EAAE,CAAC2B,WAAW;MAE3Bc,SAAS,EAAEzC,EAAE,CAACyC,SAAS;MAEvBpD,OAAO,EAAEW,EAAE,CAACX,OAAO;MACnBqD,iBAAiB,EAAE1C,EAAE,CAAC0C,iBAAiB;MACvClD,WAAW,EAAEQ,EAAE,CAACR,WAAW;MAC3B1C,QAAQ;MACR6F,YAAY,EAAE3C,EAAE,CAAC2C,YAAY;MAE7BC,IAAI,EAAE5C,EAAE,CAAC4C,IAAI;MACb;MACAC,MAAM,EAAE7C,EAAE,CAAC6C,MAAM;MACjBC,IAAI,EAAE9C,EAAE,CAAC8C;KACZ,CAAC;EACN;EAEA;;;EAGA,IAAIC,IAAIA,CAAA;IAAyB,OAAO,IAAI,CAAC,CAAAA,IAAK;EAAE;EAEpD;;;EAGA7F,MAAMA,CAAA;IACF,MAAM;MACFK,EAAE;MAAEC,IAAI;MAAEgF,eAAe;MAAE1D,IAAI;MAAE0B,KAAK;MACtCkB,SAAS;MAAEC,WAAW;MAAEc,SAAS;MACjCM,IAAI;MAAE;MACNF,MAAM;MAAEC;IAAI,CACf,GAAG,IAAI;IAER,OAAO;MACH3F,KAAK,EAAE,oBAAoB;MAC3BuE,SAAS;MAAEC,WAAW;MACtB;MACAa,eAAe;MACfE,iBAAiB,EAAE/F,MAAM,CAAC,IAAI,CAAC+F,iBAAiB,CAAC;MACjDlF,IAAI;MACJV,QAAQ,EAAEH,MAAM,CAAC,IAAI,CAACG,QAAQ,CAAC;MAC/B0C,WAAW,EAAE7C,MAAM,CAAC,IAAI,CAAC6C,WAAW,CAAC;MACrCmD,YAAY,EAAEhG,MAAM,CAAC,IAAI,CAACgG,YAAY,CAAC;MACvCtD,OAAO,EAAE1C,MAAM,CAAC,IAAI,CAAC0C,OAAO,CAAC;MAC7BP,IAAI;MAAE0B,KAAK;MAAEuC,IAAI;MAAEN,SAAS;MAAEK,IAAI;MAAED,MAAM;MAAEtF;KAC/C;EACL;EAEA;;;EAGA,IAAI6C,MAAMA,CAAA;IAAa,OAAO,IAAI,CAAC2C,IAAI,CAAC3C,MAAM;EAAE;EAEhD,CAACE,MAAM,CAACC,QAAQ,IAAC;IACb,IAAIC,KAAK,GAAG,CAAC;IACb,OAAO;MACHC,IAAI,EAAEA,CAAA,KAAK;QACP,IAAID,KAAK,GAAG,IAAI,CAACJ,MAAM,EAAE;UACrB,OAAO;YAAE1D,KAAK,EAAE,IAAI,CAACqG,IAAI,CAACvC,KAAK,EAAE,CAAC;YAAEE,IAAI,EAAE;UAAK,CAAE;;QAErD,OAAO;UAAEhE,KAAK,EAAEiE,SAAS;UAAED,IAAI,EAAE;QAAI,CAAE;MAC3C;KACH;EACL;EAEA;;;EAGA,IAAIuC,GAAGA,CAAA;IACH,OAAO,IAAI,CAAC5D,OAAO,GAAG,IAAI,CAACvC,QAAQ;EACvC;EAEA;;;EAGA,MAAMoF,QAAQA,CAAA;IACV,MAAMnC,KAAK,GAAG,MAAM,IAAI,CAACnB,QAAQ,CAACsD,QAAQ,CAAC,IAAI,CAACR,SAAS,CAAC;IAC1D,IAAI3B,KAAK,IAAI,IAAI,EAAE;MAAE,MAAM,IAAImB,KAAK,CAAC,MAAM,CAAC;;IAC5C,OAAOnB,KAAK;EAChB;EAEA;;;EAGA,MAAMe,cAAcA,CAAA;IAChB,MAAMd,EAAE,GAAG,MAAM,IAAI,CAACpB,QAAQ,CAACkC,cAAc,CAAC,IAAI,CAAChC,IAAI,CAAC;IACxD,IAAIkB,EAAE,IAAI,IAAI,EAAE;MAAE,MAAM,IAAIkB,KAAK,CAAC,MAAM,CAAC;;IACzC,OAAOlB,EAAE;EACb;EAEA;;;;;;EAMA,MAAMkD,SAASA,CAAA;IACX,OAAgB,MAAM,IAAI,CAACtE,QAAQ,CAACuE,oBAAoB,CAAC,IAAI,CAACrE,IAAI,CAAC;EACvE;EAEA;;;EAGA,MAAMsE,aAAaA,CAAA;IACf,OAAO,CAAC,MAAM,IAAI,CAACxE,QAAQ,CAACyE,cAAc,EAAE,IAAI,IAAI,CAAC1B,WAAW,GAAG,CAAC;EACxE;EAEA;;;EAGAU,YAAYA,CAAA;IACR,OAAOiB,8BAA8B,CAAC,IAAI,CAAC;EAC/C;EAEA;;;EAGAC,cAAcA,CAACC,KAA2B;IACtCtH,MAAM,CAAC,CAACsH,KAAK,IAAIA,KAAK,CAACpC,OAAO,EAAE,EAAE,+CAA+C,EAC7E,uBAAuB,EAAE;MAAEf,SAAS,EAAE;IAAuB,CAAE,CAAC;IACpE,OAAOoD,gCAAgC,CAAC,IAAI,EAAED,KAAK,CAAC;EACxD;;AA8BJ;;;;;;;;;AASA,OAAM,MAAOvD,mBAAmB;EAC5B;;;;EAISrB,QAAQ;EAEjB;;;;;EAKS+C,WAAW;EAEpB;;;;;EAKSD,SAAS;EAElB;;;EAGSlB,KAAK;EAEd;;;EAGS1B,IAAI;EAEb;;;;EAIS8D,IAAI;EAEb;;;;;;;;EAQSrF,EAAE;EAEX;;;;;EAKSC,IAAI;EAEb;;;;;;;;EAQS0B,KAAK;EAEd;;;;;EAKSE,QAAQ;EAEjB;;;;;;;;;;;;EAYStC,QAAQ;EAEjB;;;;;EAKSE,oBAAoB;EAE7B;;;;EAISD,YAAY;EAErB;;;EAGS2G,gBAAgB;EAEzB;;;EAGSjG,IAAI;EAEb;;;;EAISf,KAAK;EAEd;;;EAGSiH,OAAO;EAEhB;;;EAGSC,SAAS;EAElB;;;;EAIS9F,UAAU;EAEnB;;;EAGSM,mBAAmB;EAE5B;;;EAGSL,iBAAiB;EAE1B,CAAA8F,UAAW;EAEX;;;EAGA5G,YAAY+C,EAA6B,EAAEpB,QAAkB;IACzD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAAC+C,WAAW,GAAI3B,EAAE,CAAC2B,WAAW,IAAI,IAAI,GAAI3B,EAAE,CAAC2B,WAAW,GAAE,IAAI;IAClE,IAAI,CAACD,SAAS,GAAI1B,EAAE,CAAC0B,SAAS,IAAI,IAAI,GAAI1B,EAAE,CAAC0B,SAAS,GAAE,IAAI;IAE5D,IAAI,CAAC5C,IAAI,GAAGkB,EAAE,CAAClB,IAAI;IACnB,IAAI,CAAC0B,KAAK,GAAGR,EAAE,CAACQ,KAAK;IAErB,IAAI,CAACoC,IAAI,GAAG5C,EAAE,CAAC4C,IAAI;IAEnB,IAAI,CAACpF,IAAI,GAAGwC,EAAE,CAACxC,IAAI;IACnB,IAAI,CAACD,EAAE,GAAGyC,EAAE,CAACzC,EAAE,IAAI,IAAI;IAEvB,IAAI,CAAC6B,QAAQ,GAAGY,EAAE,CAACZ,QAAQ;IAC3B,IAAI,CAACF,KAAK,GAAGc,EAAE,CAACd,KAAK;IACrB,IAAI,CAACzB,IAAI,GAAGuC,EAAE,CAACvC,IAAI;IACnB,IAAI,CAACf,KAAK,GAAGsD,EAAE,CAACtD,KAAK;IAErB,IAAI,CAACI,QAAQ,GAAGkD,EAAE,CAAClD,QAAQ;IAC3B,IAAI,CAACE,oBAAoB,GAAIgD,EAAE,CAAChD,oBAAoB,IAAI,IAAI,GAAIgD,EAAE,CAAChD,oBAAoB,GAAE,IAAI;IAC7F,IAAI,CAACD,YAAY,GAAIiD,EAAE,CAACjD,YAAY,IAAI,IAAI,GAAIiD,EAAE,CAACjD,YAAY,GAAE,IAAI;IACrE,IAAI,CAAC2G,gBAAgB,GAAI1D,EAAE,CAAC0D,gBAAgB,IAAI,IAAI,GAAI1D,EAAE,CAAC0D,gBAAgB,GAAE,IAAI;IAEjF,IAAI,CAACC,OAAO,GAAG3D,EAAE,CAAC2D,OAAO;IACzB,IAAI,CAACC,SAAS,GAAG5D,EAAE,CAAC4D,SAAS;IAE7B,IAAI,CAAC9F,UAAU,GAAIkC,EAAE,CAAClC,UAAU,IAAI,IAAI,GAAIkC,EAAE,CAAClC,UAAU,GAAE,IAAI;IAC/D,IAAI,CAACM,mBAAmB,GAAI4B,EAAE,CAAC5B,mBAAmB,IAAI,IAAI,GAAI4B,EAAE,CAAC5B,mBAAmB,GAAE,IAAI;IAE1F,IAAI,CAACL,iBAAiB,GAAIiC,EAAE,CAACjC,iBAAiB,IAAI,IAAI,GAAIiC,EAAE,CAACjC,iBAAiB,GAAE,IAAI;IAEpF,IAAI,CAAC,CAAA8F,UAAW,GAAG,CAAC,CAAC;EACzB;EAEA;;;EAGA3G,MAAMA,CAAA;IACF,MAAM;MACFyE,WAAW;MAAED,SAAS;MAAElB,KAAK;MAAE1B,IAAI;MAAE8D,IAAI;MAAErF,EAAE;MAAEC,IAAI;MAAE0B,KAAK;MAC1DzB,IAAI;MAAEmG,SAAS;MAAE9F,UAAU;MAAEM;IAAmB,CACnD,GAAG,IAAI;IAER,OAAO;MACHjB,KAAK,EAAE,qBAAqB;MAC5BW,UAAU;MAAE6D,WAAW;MAAED,SAAS;MAClCtD,mBAAmB;MACnBuF,OAAO,EAAEhH,MAAM,CAAC,IAAI,CAACgH,OAAO,CAAC;MAC7BlG,IAAI;MAAED,IAAI;MACV4B,QAAQ,EAAEzC,MAAM,CAAC,IAAI,CAACyC,QAAQ,CAAC;MAC/BtC,QAAQ,EAAEH,MAAM,CAAC,IAAI,CAACG,QAAQ,CAAC;MAC/BgC,IAAI;MACJ/B,YAAY,EAAEJ,MAAM,CAAC,IAAI,CAACI,YAAY,CAAC;MACvCC,oBAAoB,EAAEL,MAAM,CAAC,IAAI,CAACK,oBAAoB,CAAC;MACvD0G,gBAAgB,EAAE/G,MAAM,CAAC,IAAI,CAAC+G,gBAAgB,CAAC;MAC/CxE,KAAK;MAAE0E,SAAS;MAAErG,EAAE;MAAEiD,KAAK;MAAEoC,IAAI;MACjClG,KAAK,EAAEC,MAAM,CAAC,IAAI,CAACD,KAAK;KAC3B;EACL;EAEA;;;;;EAKA,MAAMwF,QAAQA,CAAA;IACV,IAAIP,WAAW,GAAG,IAAI,CAACA,WAAW;IAClC,IAAIA,WAAW,IAAI,IAAI,EAAE;MACrB,MAAM3B,EAAE,GAAG,MAAM,IAAI,CAACc,cAAc,EAAE;MACtC,IAAId,EAAE,EAAE;QAAE2B,WAAW,GAAG3B,EAAE,CAAC2B,WAAW;;;IAE1C,IAAIA,WAAW,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACtC,MAAM5B,KAAK,GAAG,IAAI,CAACnB,QAAQ,CAACsD,QAAQ,CAACP,WAAW,CAAC;IACjD,IAAI5B,KAAK,IAAI,IAAI,EAAE;MAAE,MAAM,IAAImB,KAAK,CAAC,MAAM,CAAC;;IAC5C,OAAOnB,KAAK;EAChB;EAEA;;;;;EAKA,MAAMe,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAClC,QAAQ,CAACkC,cAAc,CAAC,IAAI,CAAChC,IAAI,CAAC;EAClD;EAEA;;;EAGA,MAAMsE,aAAaA,CAAA;IACf,IAAI,IAAI,CAACzB,WAAW,IAAI,IAAI,EAAE;MAC1B,MAAM;QAAE3B,EAAE;QAAE2B;MAAW,CAAE,GAAG,MAAM1F,iBAAiB,CAAC;QAChD+D,EAAE,EAAE,IAAI,CAACc,cAAc,EAAE;QACzBa,WAAW,EAAE,IAAI,CAAC/C,QAAQ,CAACyE,cAAc;OAC5C,CAAC;MAEF;MACA,IAAIrD,EAAE,IAAI,IAAI,IAAIA,EAAE,CAAC2B,WAAW,IAAI,IAAI,EAAE;QAAE,OAAO,CAAC;;MAEpD,OAAOA,WAAW,GAAG3B,EAAE,CAAC2B,WAAW,GAAG,CAAC;;IAG3C,MAAMA,WAAW,GAAG,MAAM,IAAI,CAAC/C,QAAQ,CAACyE,cAAc,EAAE;IACxD,OAAO1B,WAAW,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;EAC7C;EAEA;;;;;;;;;EASA,MAAMmC,IAAIA,CAACC,SAAkB,EAAEC,QAAiB;IAC5C,MAAMC,QAAQ,GAAIF,SAAS,IAAI,IAAI,GAAI,CAAC,GAAEA,SAAS;IACnD,MAAMG,OAAO,GAAIF,QAAQ,IAAI,IAAI,GAAI,CAAC,GAAEA,QAAQ;IAEhD,IAAIH,UAAU,GAAG,IAAI,CAAC,CAAAA,UAAW;IACjC,IAAIM,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,YAAY,GAAIP,UAAU,KAAK,CAAC,CAAC,GAAI,IAAI,GAAE,KAAK;IACpD,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAW;MAChC;MACA,IAAID,YAAY,EAAE;QAAE,OAAO,IAAI;;MAC/B,MAAM;QAAEzC,WAAW;QAAEzC;MAAK,CAAE,GAAG,MAAMjD,iBAAiB,CAAC;QACnD0F,WAAW,EAAE,IAAI,CAAC/C,QAAQ,CAACyE,cAAc,EAAE;QAC3CnE,KAAK,EAAE,IAAI,CAACN,QAAQ,CAAC0F,mBAAmB,CAAC,IAAI,CAAC9G,IAAI;OACrD,CAAC;MAEF;MACA;MACA,IAAI0B,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE;QACpB2E,UAAU,GAAGlC,WAAW;QACxB;;MAGJ;MACA,IAAIyC,YAAY,EAAE;QAAE,OAAO,IAAI;;MAC/B,MAAMG,KAAK,GAAG,MAAM,IAAI,CAACzD,cAAc,EAAE;MACzC,IAAIyD,KAAK,IAAIA,KAAK,CAAC5C,WAAW,IAAI,IAAI,EAAE;QAAE;;MAE1C;MAEA;MACA,IAAIwC,QAAQ,KAAK,CAAC,CAAC,EAAE;QACjBA,QAAQ,GAAGN,UAAU,GAAG,CAAC;QACzB,IAAIM,QAAQ,GAAG,IAAI,CAAC,CAAAN,UAAW,EAAE;UAAEM,QAAQ,GAAG,IAAI,CAAC,CAAAN,UAAW;;;MAGlE,OAAOM,QAAQ,IAAIxC,WAAW,EAAE;QAC5B;QACA,IAAIyC,YAAY,EAAE;UAAE,OAAO,IAAI;;QAC/B,MAAMrE,KAAK,GAAG,MAAM,IAAI,CAACnB,QAAQ,CAACsD,QAAQ,CAACiC,QAAQ,EAAE,IAAI,CAAC;QAE1D;QACA,IAAIpE,KAAK,IAAI,IAAI,EAAE;UAAE;;QAErB;QACA,KAAK,MAAMjB,IAAI,IAAIiB,KAAK,EAAE;UACtB,IAAIjB,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;YAAE;;;QAG9B;QACA,KAAK,IAAI0F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzE,KAAK,CAACK,MAAM,EAAEoE,CAAC,EAAE,EAAE;UACnC,MAAMxE,EAAE,GAAwB,MAAMD,KAAK,CAACe,cAAc,CAAC0D,CAAC,CAAC;UAE7D,IAAIxE,EAAE,CAACxC,IAAI,KAAK,IAAI,CAACA,IAAI,IAAIwC,EAAE,CAACd,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;YAClD;YACA,IAAIkF,YAAY,EAAE;cAAE,OAAO,IAAI;;YAC/B,MAAMhC,OAAO,GAAG,MAAM,IAAI,CAACxD,QAAQ,CAACuD,qBAAqB,CAACnC,EAAE,CAAClB,IAAI,CAAC;YAElE;YACA,IAAIsD,OAAO,IAAI,IAAI,EAAE;cAAE;;YAEvB;YACA,IAAKT,WAAW,GAAGS,OAAO,CAACT,WAAW,GAAG,CAAC,GAAIsC,QAAQ,EAAE;cAAE;;YAE1D;YACA,IAAIQ,MAAM,GAA0C,UAAU;YAC9D,IAAIzE,EAAE,CAACvC,IAAI,KAAK,IAAI,CAACA,IAAI,IAAIuC,EAAE,CAACzC,EAAE,KAAK,IAAI,CAACA,EAAE,IAAIyC,EAAE,CAACtD,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;cACvE+H,MAAM,GAAG,UAAU;aACtB,MAAO,IAAIzE,EAAE,CAACvC,IAAI,KAAK,IAAI,IAAIuC,EAAE,CAACxC,IAAI,KAAKwC,EAAE,CAACzC,EAAE,IAAIyC,EAAE,CAACtD,KAAK,KAAKH,IAAI,EAAE;cACpEkI,MAAM,GAAG,WAAW;;YAGxBvI,MAAM,CAAC,KAAK,EAAE,0BAA0B,EAAE,sBAAsB,EAAE;cAC9DwI,SAAS,EAAGD,MAAM,KAAK,UAAU,IAAIA,MAAM,KAAK,WAAY;cAC5DA,MAAM;cACNE,WAAW,EAAE3E,EAAE,CAAC4E,sBAAsB,CAACf,UAAU,CAAC;cAClD/E,IAAI,EAAEkB,EAAE,CAAClB,IAAI;cACbsD;aACH,CAAC;;;QAIV+B,QAAQ,EAAE;;MAEd;IACJ,CAAC;IAED,MAAMU,YAAY,GAAIzC,OAAkC,IAAI;MACxD,IAAIA,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACS,MAAM,KAAK,CAAC,EAAE;QAAE,OAAOT,OAAO;;MAC7DlG,MAAM,CAAC,KAAK,EAAE,gCAAgC,EAAE,gBAAgB,EAAE;QAC9D4I,MAAM,EAAE,iBAAiB;QACzBrH,IAAI,EAAE,IAAI;QAAEgH,MAAM,EAAE,IAAI;QAAEM,UAAU,EAAE,IAAI;QAAEC,MAAM,EAAE,IAAI;QACxDC,WAAW,EAAE;UACT1H,EAAE,EAAE6E,OAAO,CAAC7E,EAAE;UACdC,IAAI,EAAE4E,OAAO,CAAC5E,IAAI;UAClBC,IAAI,EAAE,EAAE,CAAC;SACZ;QAAE2E;OACN,CAAC;IACN,CAAC;IAED,MAAMA,OAAO,GAAG,MAAM,IAAI,CAACxD,QAAQ,CAACuD,qBAAqB,CAAC,IAAI,CAACrD,IAAI,CAAC;IAEpE,IAAImF,QAAQ,KAAK,CAAC,EAAE;MAAE,OAAOY,YAAY,CAACzC,OAAO,CAAC;;IAElD,IAAIA,OAAO,EAAE;MACT,IAAI6B,QAAQ,KAAK,CAAC,IAAI,CAAC,MAAM7B,OAAO,CAACgB,aAAa,EAAE,KAAKa,QAAQ,EAAE;QAC/D,OAAOY,YAAY,CAACzC,OAAO,CAAC;;KAGnC,MAAM;MACH;MACA,MAAMiC,gBAAgB,EAAE;MAExB;MACA,IAAIJ,QAAQ,KAAK,CAAC,EAAE;QAAE,OAAO,IAAI;;;IAGrC,MAAMiB,MAAM,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC3C;MACA,MAAMC,UAAU,GAAsB,EAAG;MACzC,MAAMC,MAAM,GAAGA,CAAA,KAAK;QAAGD,UAAU,CAACE,OAAO,CAAEC,CAAC,IAAKA,CAAC,EAAE,CAAC;MAAE,CAAC;MAExD;MACAH,UAAU,CAACI,IAAI,CAAC,MAAK;QAAGtB,YAAY,GAAG,IAAI;MAAE,CAAC,CAAC;MAE/C;MACA,IAAIF,OAAO,GAAG,CAAC,EAAE;QACb,MAAMyB,KAAK,GAAGC,UAAU,CAAC,MAAK;UAC1BL,MAAM,EAAE;UACRF,MAAM,CAAChJ,SAAS,CAAC,8BAA8B,EAAE,SAAS,CAAC,CAAC;QAChE,CAAC,EAAE6H,OAAO,CAAC;QACXoB,UAAU,CAACI,IAAI,CAAC,MAAK;UAAGG,YAAY,CAACF,KAAK,CAAC;QAAE,CAAC,CAAC;;MAGnD,MAAMG,UAAU,GAAG,MAAO1D,OAA2B,IAAI;QACrD;QACA,IAAI,CAAC,MAAMA,OAAO,CAACgB,aAAa,EAAE,KAAKa,QAAQ,EAAE;UAC7CsB,MAAM,EAAE;UACR,IAAI;YACAH,OAAO,CAACP,YAAY,CAACzC,OAAO,CAAC,CAAC;WACjC,CAAC,OAAO2D,KAAK,EAAE;YAAEV,MAAM,CAACU,KAAK,CAAC;;;MAEvC,CAAC;MACDT,UAAU,CAACI,IAAI,CAAC,MAAK;QAAG,IAAI,CAAC9G,QAAQ,CAACoH,GAAG,CAAC,IAAI,CAAClH,IAAI,EAAEgH,UAAU,CAAC;MAAE,CAAC,CAAC;MACpE,IAAI,CAAClH,QAAQ,CAACqH,EAAE,CAAC,IAAI,CAACnH,IAAI,EAAEgH,UAAU,CAAC;MACvC;MACA,IAAIjC,UAAU,IAAI,CAAC,EAAE;QACjB,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAW;UAC/B,IAAI;YACA;YACA,MAAM7B,gBAAgB,EAAE;WAE3B,CAAC,OAAO0B,KAAK,EAAE;YACZ;YACA,IAAI3J,OAAO,CAAC2J,KAAK,EAAE,sBAAsB,CAAC,EAAE;cACxCR,MAAM,EAAE;cACRF,MAAM,CAACU,KAAK,CAAC;cACb;;;UAIR;UACA,IAAI,CAAC3B,YAAY,EAAE;YACf,IAAI,CAACxF,QAAQ,CAACuH,IAAI,CAAC,OAAO,EAAED,eAAe,CAAC;;QAEpD,CAAC;QACDZ,UAAU,CAACI,IAAI,CAAC,MAAK;UAAG,IAAI,CAAC9G,QAAQ,CAACoH,GAAG,CAAC,OAAO,EAAEE,eAAe,CAAC;QAAE,CAAC,CAAC;QACvE,IAAI,CAACtH,QAAQ,CAACuH,IAAI,CAAC,OAAO,EAAED,eAAe,CAAC;;IAEpD,CAAC,CAAC;IAEF,OAAO,MAAmChB,MAAM;EACpD;EAEA;;;;;;;;;;;EAWA9D,OAAOA,CAAA;IACH,OAAQ,IAAI,CAACM,SAAS,IAAI,IAAI;EAClC;EAEA;;;;;;;EAOA0E,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACxD,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;;;;EAOAyD,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACzD,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;;;;EAOAvB,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAACuB,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;EAIA0D,QAAQA,CAAA;IACJ,OAAQ,IAAI,CAAC1D,IAAI,KAAK,CAAC;EAC3B;EAEA;;;;EAIAP,YAAYA,CAAA;IACRnG,MAAM,CAAC,IAAI,CAACkF,OAAO,EAAE,EAAE,uCAAuC,EAC1D,uBAAuB,EAAE;MAAEf,SAAS,EAAE;IAAe,CAAE,CAAC;IAC5D,OAAOiD,8BAA8B,CAAC,IAAI,CAAC;EAC/C;EAEA;;;;EAIAC,cAAcA,CAACC,KAA2B;IACtCtH,MAAM,CAAC,IAAI,CAACkF,OAAO,EAAE,EAAE,uCAAuC,EAC1D,uBAAuB,EAAE;MAAEf,SAAS,EAAE;IAAe,CAAE,CAAC;IAE5DnE,MAAM,CAAC,CAACsH,KAAK,IAAIA,KAAK,CAACpC,OAAO,EAAE,EAAE,+CAA+C,EAC7E,uBAAuB,EAAE;MAAEf,SAAS,EAAE;IAAe,CAAE,CAAC;IAE5D,OAAOoD,gCAAgC,CAAC,IAAI,EAAED,KAAK,CAAC;EACxD;EAEA;;;;;;;;;EASAoB,sBAAsBA,CAACf,UAAkB;IACrC1H,cAAc,CAACoK,MAAM,CAACC,SAAS,CAAC3C,UAAU,CAAC,IAAIA,UAAU,IAAI,CAAC,EAAE,oBAAoB,EAAE,YAAY,EAAEA,UAAU,CAAC;IAC/G,MAAM7D,EAAE,GAAG,IAAIC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACrB,QAAQ,CAAC;IACvDoB,EAAE,CAAC,CAAA6D,UAAW,GAAGA,UAAU;IAC3B,OAAO7D,EAAE;EACb;;AAuCJ,SAASuB,yBAAyBA,CAACxB,KAAuC;EACtE,OAAO;IAAE0G,MAAM,EAAE,YAAY;IAAE3H,IAAI,EAAEiB,KAAK,CAACjB,IAAI;IAAED,MAAM,EAAEkB,KAAK,CAAClB;EAAM,CAAE;AAC3E;AAEA,SAAS4E,gCAAgCA,CAACzD,EAA4D,EAAEwD,KAAgE;EACpK,OAAO;IAAEiD,MAAM,EAAE,qBAAqB;IAAEzG,EAAE;IAAEwD;EAAK,CAAE;AACvD;AAEA,SAASF,8BAA8BA,CAACtD,EAA4D;EAChG,OAAO;IAAEyG,MAAM,EAAE,kBAAkB;IAAEzG;EAAE,CAAE;AAC7C;AAEA,SAASsC,sBAAsBA,CAACN,GAAqJ;EACjL,OAAO;IAAEyE,MAAM,EAAE,UAAU;IAAEzE,GAAG,EAAE;MAC9BP,eAAe,EAAEO,GAAG,CAACP,eAAe;MACpCC,SAAS,EAAEM,GAAG,CAACN,SAAS;MACxBC,WAAW,EAAEK,GAAG,CAACL,WAAW;MAC5BE,OAAO,EAAEG,GAAG,CAACH,OAAO;MACpBpE,IAAI,EAAEuE,GAAG,CAACvE,IAAI;MACdqE,MAAM,EAAErD,MAAM,CAACwD,MAAM,CAACD,GAAG,CAACF,MAAM,CAAC9D,KAAK,EAAE,CAAC;MACzCwC,KAAK,EAAEwB,GAAG,CAACxB;;EACd,CAAE;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}