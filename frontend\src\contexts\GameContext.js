import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../utils/api';
import { useAuth } from './AuthContext';

const GameContext = createContext();

export const useGame = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
};

export const GameProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [heroes, setHeroes] = useState([]);
  const [userStats, setUserStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [battleHistory, setBattleHistory] = useState([]);
  const [dailyQuests, setDailyQuests] = useState([]);

  // Load game data when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadGameData();
    } else {
      // Clear data when user logs out
      setHeroes([]);
      setUserStats(null);
      setBattleHistory([]);
      setDailyQuests([]);
    }
  }, [isAuthenticated]);

  const loadGameData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadHeroes(),
        loadUserStats(),
        loadBattleHistory(),
        loadDailyQuests(),
      ]);
    } catch (error) {
      console.error('Failed to load game data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadHeroes = async () => {
    try {
      const response = await api.get('/api/heroes');
      setHeroes(response.data.heroes);
      return response.data.heroes;
    } catch (error) {
      console.error('Failed to load heroes:', error);
      return [];
    }
  };

  const loadUserStats = async () => {
    try {
      const response = await api.get('/api/users/profile');
      setUserStats(response.data.stats);
      return response.data.stats;
    } catch (error) {
      console.error('Failed to load user stats:', error);
      return null;
    }
  };

  const loadBattleHistory = async () => {
    try {
      const response = await api.get('/api/battles/history?limit=10');
      setBattleHistory(response.data.battles);
      return response.data.battles;
    } catch (error) {
      console.error('Failed to load battle history:', error);
      return [];
    }
  };

  const loadDailyQuests = async () => {
    try {
      const response = await api.get('/api/quests/daily');
      setDailyQuests(response.data.quests);
      return response.data.quests;
    } catch (error) {
      console.error('Failed to load daily quests:', error);
      return [];
    }
  };

  const upgradeHero = async (heroId) => {
    try {
      const response = await api.post(`/api/heroes/${heroId}/upgrade`);

      // Reload heroes to get updated stats
      await loadHeroes();
      await loadUserStats();

      return response.data;
    } catch (error) {
      console.error('Failed to upgrade hero:', error);
      throw error;
    }
  };

  const unlockSkill = async (heroId, skillId) => {
    try {
      const response = await api.post(`/api/heroes/${heroId}/unlock-skill/${skillId}`);

      // Reload heroes to get updated skills
      await loadHeroes();
      await loadUserStats();

      return response.data;
    } catch (error) {
      console.error('Failed to unlock skill:', error);
      throw error;
    }
  };

  const startPvEBattle = async (heroIds, difficulty) => {
    try {
      const response = await api.post('/api/battles/pve/start', {
        heroIds,
        difficulty,
      });

      // Reload battle history and user stats
      await loadBattleHistory();
      await loadUserStats();

      return response.data;
    } catch (error) {
      console.error('Failed to start PvE battle:', error);
      throw error;
    }
  };

  const claimQuestReward = async (questId) => {
    try {
      const response = await api.post(`/api/quests/${questId}/claim`);

      // Reload quests and user stats
      await loadDailyQuests();
      await loadUserStats();

      return response.data;
    } catch (error) {
      console.error('Failed to claim quest reward:', error);
      throw error;
    }
  };

  const getHeroById = (heroId) => {
    return heroes.find(hero => hero.id === heroId);
  };

  const getActiveHeroes = () => {
    return heroes.filter(hero => hero.isActive !== false);
  };

  const getTotalHeroPower = (heroIds) => {
    return heroIds.reduce((total, heroId) => {
      const hero = getHeroById(heroId);
      if (hero) {
        const power = hero.stats.hp + hero.stats.atk + hero.stats.def + hero.stats.spd + hero.stats.luk;
        return total + power;
      }
      return total;
    }, 0);
  };

  const value = {
    heroes,
    userStats,
    battleHistory,
    dailyQuests,
    loading,
    loadGameData,
    loadHeroes,
    loadUserStats,
    loadBattleHistory,
    loadDailyQuests,
    upgradeHero,
    unlockSkill,
    startPvEBattle,
    claimQuestReward,
    getHeroById,
    getActiveHeroes,
    getTotalHeroPower,
  };

  return (
    <GameContext.Provider value={value}>
      {children}
    </GameContext.Provider>
  );
};
