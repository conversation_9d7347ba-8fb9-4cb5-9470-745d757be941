[{"C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\Web3Context.js": "3", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Heroes.js": "5", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Arena.js": "6", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Staking.js": "9", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\GameContext.js": "10", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Battle.js": "11", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Leaderboard.js": "12", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Home.js": "13", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Tournaments.js": "14", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\ProtectedRoute.js": "15", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\Navbar.js": "16", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\HeroCard.js": "17", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\BattleArena.js": "18", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\TokenBalance.js": "19", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\QuestCard.js": "20", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\LoadingSpinner.js": "21"}, {"size": 928, "mtime": 1748530343010, "results": "22", "hashOfConfig": "23"}, {"size": 3001, "mtime": 1748530356207, "results": "24", "hashOfConfig": "23"}, {"size": 8828, "mtime": 1748530416312, "results": "25", "hashOfConfig": "23"}, {"size": 5301, "mtime": 1748530379396, "results": "26", "hashOfConfig": "23"}, {"size": 5504, "mtime": 1748531219706, "results": "27", "hashOfConfig": "23"}, {"size": 1042, "mtime": 1748530703976, "results": "28", "hashOfConfig": "23"}, {"size": 5753, "mtime": 1748533406452, "results": "29", "hashOfConfig": "23"}, {"size": 8589, "mtime": 1748531316039, "results": "30", "hashOfConfig": "23"}, {"size": 10792, "mtime": 1748530758603, "results": "31", "hashOfConfig": "23"}, {"size": 5122, "mtime": 1748530442578, "results": "32", "hashOfConfig": "23"}, {"size": 10301, "mtime": 1748531138652, "results": "33", "hashOfConfig": "23"}, {"size": 7685, "mtime": 1748530794839, "results": "34", "hashOfConfig": "23"}, {"size": 6052, "mtime": 1748533390914, "results": "35", "hashOfConfig": "23"}, {"size": 1079, "mtime": 1748530713314, "results": "36", "hashOfConfig": "23"}, {"size": 763, "mtime": 1748530487997, "results": "37", "hashOfConfig": "23"}, {"size": 7108, "mtime": 1748533374038, "results": "38", "hashOfConfig": "23"}, {"size": 5565, "mtime": 1748530981080, "results": "39", "hashOfConfig": "23"}, {"size": 12065, "mtime": 1748531032637, "results": "40", "hashOfConfig": "23"}, {"size": 2648, "mtime": 1748531062276, "results": "41", "hashOfConfig": "23"}, {"size": 5570, "mtime": 1748531095661, "results": "42", "hashOfConfig": "23"}, {"size": 1048, "mtime": 1748531045073, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fg3tg6", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\Web3Context.js", ["107"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Heroes.js", ["108", "109", "110", "111", "112", "113", "114", "115"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Arena.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Dashboard.js", ["116", "117", "118"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Staking.js", ["119"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\GameContext.js", ["120"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Battle.js", ["121", "122", "123", "124", "125", "126"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Leaderboard.js", ["127"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Tournaments.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\Navbar.js", ["128"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\HeroCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\BattleArena.js", ["129", "130", "131"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\TokenBalance.js", ["132"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\QuestCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\LoadingSpinner.js", [], [], {"ruleId": "133", "severity": 1, "message": "134", "line": 63, "column": 6, "nodeType": "135", "endLine": 63, "endColumn": 8, "suggestions": "136"}, {"ruleId": "137", "severity": 1, "message": "138", "line": 7, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 7, "endColumn": 14}, {"ruleId": "137", "severity": 1, "message": "141", "line": 28, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 28, "endColumn": 25}, {"ruleId": "137", "severity": 1, "message": "142", "line": 39, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 39, "endColumn": 20}, {"ruleId": "143", "severity": 2, "message": "144", "line": 41, "column": 11, "nodeType": "139", "messageId": "145", "endLine": 41, "endColumn": 20}, {"ruleId": "143", "severity": 2, "message": "146", "line": 42, "column": 12, "nodeType": "139", "messageId": "145", "endLine": 42, "endColumn": 20}, {"ruleId": "143", "severity": 2, "message": "147", "line": 44, "column": 12, "nodeType": "139", "messageId": "145", "endLine": 44, "endColumn": 20}, {"ruleId": "143", "severity": 2, "message": "147", "line": 45, "column": 12, "nodeType": "139", "messageId": "145", "endLine": 45, "endColumn": 20}, {"ruleId": "143", "severity": 2, "message": "147", "line": 47, "column": 27, "nodeType": "139", "messageId": "145", "endLine": 47, "endColumn": 35}, {"ruleId": "137", "severity": 1, "message": "148", "line": 1, "column": 17, "nodeType": "139", "messageId": "140", "endLine": 1, "endColumn": 26}, {"ruleId": "137", "severity": 1, "message": "149", "line": 5, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 5, "endColumn": 17}, {"ruleId": "137", "severity": 1, "message": "150", "line": 20, "column": 19, "nodeType": "139", "messageId": "140", "endLine": 20, "endColumn": 28}, {"ruleId": "133", "severity": 1, "message": "151", "line": 23, "column": 6, "nodeType": "135", "endLine": 23, "endColumn": 27, "suggestions": "152"}, {"ruleId": "133", "severity": 1, "message": "153", "line": 34, "column": 6, "nodeType": "135", "endLine": 34, "endColumn": 23, "suggestions": "154"}, {"ruleId": "137", "severity": 1, "message": "155", "line": 3, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 16}, {"ruleId": "137", "severity": 1, "message": "156", "line": 4, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 19}, {"ruleId": "137", "severity": 1, "message": "157", "line": 5, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 5, "endColumn": 22}, {"ruleId": "137", "severity": 1, "message": "158", "line": 11, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 11, "endColumn": 16}, {"ruleId": "137", "severity": 1, "message": "159", "line": 21, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 21, "endColumn": 25}, {"ruleId": "137", "severity": 1, "message": "160", "line": 22, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 22, "endColumn": 21}, {"ruleId": "133", "severity": 1, "message": "161", "line": 17, "column": 6, "nodeType": "135", "endLine": 17, "endColumn": 20, "suggestions": "162"}, {"ruleId": "137", "severity": 1, "message": "163", "line": 7, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 7, "endColumn": 11}, {"ruleId": "137", "severity": 1, "message": "164", "line": 4, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 12}, {"ruleId": "133", "severity": 1, "message": "165", "line": 20, "column": 6, "nodeType": "135", "endLine": 20, "endColumn": 44, "suggestions": "166"}, {"ruleId": "133", "severity": 1, "message": "167", "line": 33, "column": 6, "nodeType": "135", "endLine": 33, "endColumn": 45, "suggestions": "168"}, {"ruleId": "133", "severity": 1, "message": "169", "line": 16, "column": 6, "nodeType": "135", "endLine": 16, "endColumn": 27, "suggestions": "170"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'handleAccountsChanged' and 'initializeWeb3'. Either include them or remove the dependency array.", "ArrayExpression", ["171"], "no-unused-vars", "'ArrowUpIcon' is defined but never used.", "Identifier", "unusedVar", "'getHeroTypeColor' is assigned a value but never used.", "'getStatIcon' is assigned a value but never used.", "no-undef", "'HeartIcon' is not defined.", "undef", "'BoltIcon' is not defined.", "'StarIcon' is not defined.", "'useEffect' is defined but never used.", "'useWeb3' is defined but never used.", "'userStats' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStakingData'. Either include it or remove the dependency array.", ["172"], "React Hook useEffect has a missing dependency: 'loadGameData'. Either include it or remove the dependency array.", ["173"], "'HeroCard' is defined but never used.", "'BattleArena' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'ArrowLeftIcon' is defined but never used.", "'showBattleArena' is assigned a value but never used.", "'enemyHeroes' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboard'. Either include it or remove the dependency array.", ["174"], "'UserIcon' is defined but never used.", "'HeartIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'initializeHeroStates'. Either include it or remove the dependency array.", ["175"], "React Hook useEffect has missing dependencies: 'onBattleEnd' and 'processNextLogEntry'. Either include them or remove the dependency array. If 'onBattleEnd' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["176"], "React Hook useEffect has a missing dependency: 'loadBalance'. Either include it or remove the dependency array.", ["177"], {"desc": "178", "fix": "179"}, {"desc": "180", "fix": "181"}, {"desc": "182", "fix": "183"}, {"desc": "184", "fix": "185"}, {"desc": "186", "fix": "187"}, {"desc": "188", "fix": "189"}, {"desc": "190", "fix": "191"}, "Update the dependencies array to be: [handleAccountsChanged, initializeWeb3]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [loadStakingData, user.walletAddress]", {"range": "194", "text": "195"}, "Update the dependencies array to be: [isAuthenticated, loadGameData]", {"range": "196", "text": "197"}, "Update the dependencies array to be: [loadLeaderboard, selectedType]", {"range": "198", "text": "199"}, "Update the dependencies array to be: [battleLog, playerHeroes, enemyHeroes, initializeHeroStates]", {"range": "200", "text": "201"}, "Update the dependencies array to be: [isPlaying, currentLogIndex, battleLog, onBattleEnd, processNextLogEntry]", {"range": "202", "text": "203"}, "Update the dependencies array to be: [loadBalance, user.walletAddress]", {"range": "204", "text": "205"}, [2218, 2220], "[handleAccountsChanged, initializeWeb3]", [745, 766], "[loadStaking<PERSON><PERSON>, user.walletAddress]", [998, 1015], "[isAuthenticated, loadGameData]", [428, 442], "[loadLeaderboard, selectedType]", [600, 638], "[battleLog, player<PERSON><PERSON><PERSON>, enemy<PERSON><PERSON><PERSON>, initializeHeroStates]", [967, 1006], "[isPlaying, currentLogIndex, battleLog, onBattleEnd, processNextLogEntry]", [611, 632], "[load<PERSON><PERSON>ce, user.walletAddress]"]