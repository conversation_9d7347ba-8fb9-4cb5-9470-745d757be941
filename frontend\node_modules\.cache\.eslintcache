[{"C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\Web3Context.js": "3", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Heroes.js": "5", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Arena.js": "6", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Login.js": "7", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Staking.js": "9", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\GameContext.js": "10", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Battle.js": "11", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Leaderboard.js": "12", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Home.js": "13", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Tournaments.js": "14", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\ProtectedRoute.js": "15", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\Navbar.js": "16", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\HeroCard.js": "17", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\BattleArena.js": "18", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\TokenBalance.js": "19", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\QuestCard.js": "20", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\LoadingSpinner.js": "21", "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\utils\\api.js": "22"}, {"size": 928, "mtime": 1748530343010, "results": "23", "hashOfConfig": "24"}, {"size": 3001, "mtime": 1748530356207, "results": "25", "hashOfConfig": "24"}, {"size": 8828, "mtime": 1748530416312, "results": "26", "hashOfConfig": "24"}, {"size": 4990, "mtime": 1748535688023, "results": "27", "hashOfConfig": "24"}, {"size": 5541, "mtime": 1748534370555, "results": "28", "hashOfConfig": "24"}, {"size": 1042, "mtime": 1748530703976, "results": "29", "hashOfConfig": "24"}, {"size": 5753, "mtime": 1748533406452, "results": "30", "hashOfConfig": "24"}, {"size": 8589, "mtime": 1748531316039, "results": "31", "hashOfConfig": "24"}, {"size": 10792, "mtime": 1748530758603, "results": "32", "hashOfConfig": "24"}, {"size": 5063, "mtime": 1748535616565, "results": "33", "hashOfConfig": "24"}, {"size": 10301, "mtime": 1748531138652, "results": "34", "hashOfConfig": "24"}, {"size": 7685, "mtime": 1748530794839, "results": "35", "hashOfConfig": "24"}, {"size": 6052, "mtime": 1748533390914, "results": "36", "hashOfConfig": "24"}, {"size": 1079, "mtime": 1748530713314, "results": "37", "hashOfConfig": "24"}, {"size": 763, "mtime": 1748530487997, "results": "38", "hashOfConfig": "24"}, {"size": 7108, "mtime": 1748533374038, "results": "39", "hashOfConfig": "24"}, {"size": 5565, "mtime": 1748530981080, "results": "40", "hashOfConfig": "24"}, {"size": 12065, "mtime": 1748531032637, "results": "41", "hashOfConfig": "24"}, {"size": 2648, "mtime": 1748531062276, "results": "42", "hashOfConfig": "24"}, {"size": 5570, "mtime": 1748531095661, "results": "43", "hashOfConfig": "24"}, {"size": 1048, "mtime": 1748531045073, "results": "44", "hashOfConfig": "24"}, {"size": 884, "mtime": 1748535573666, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fg3tg6", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\Web3Context.js", ["112"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Heroes.js", ["113", "114", "115"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Arena.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Dashboard.js", ["116", "117", "118"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Staking.js", ["119"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\contexts\\GameContext.js", ["120"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Battle.js", ["121", "122", "123", "124", "125", "126"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Leaderboard.js", ["127"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\pages\\Tournaments.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\Navbar.js", ["128"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\HeroCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\BattleArena.js", ["129", "130", "131"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\TokenBalance.js", ["132"], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\QuestCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\src\\utils\\api.js", [], [], {"ruleId": "133", "severity": 1, "message": "134", "line": 63, "column": 6, "nodeType": "135", "endLine": 63, "endColumn": 8, "suggestions": "136"}, {"ruleId": "137", "severity": 1, "message": "138", "line": 7, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 7, "endColumn": 14}, {"ruleId": "137", "severity": 1, "message": "141", "line": 31, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 31, "endColumn": 25}, {"ruleId": "137", "severity": 1, "message": "142", "line": 42, "column": 9, "nodeType": "139", "messageId": "140", "endLine": 42, "endColumn": 20}, {"ruleId": "137", "severity": 1, "message": "143", "line": 1, "column": 17, "nodeType": "139", "messageId": "140", "endLine": 1, "endColumn": 26}, {"ruleId": "137", "severity": 1, "message": "144", "line": 5, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 5, "endColumn": 17}, {"ruleId": "137", "severity": 1, "message": "145", "line": 20, "column": 19, "nodeType": "139", "messageId": "140", "endLine": 20, "endColumn": 28}, {"ruleId": "133", "severity": 1, "message": "146", "line": 23, "column": 6, "nodeType": "135", "endLine": 23, "endColumn": 27, "suggestions": "147"}, {"ruleId": "133", "severity": 1, "message": "148", "line": 34, "column": 6, "nodeType": "135", "endLine": 34, "endColumn": 23, "suggestions": "149"}, {"ruleId": "137", "severity": 1, "message": "150", "line": 3, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 3, "endColumn": 16}, {"ruleId": "137", "severity": 1, "message": "151", "line": 4, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 19}, {"ruleId": "137", "severity": 1, "message": "152", "line": 5, "column": 8, "nodeType": "139", "messageId": "140", "endLine": 5, "endColumn": 22}, {"ruleId": "137", "severity": 1, "message": "153", "line": 11, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 11, "endColumn": 16}, {"ruleId": "137", "severity": 1, "message": "154", "line": 21, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 21, "endColumn": 25}, {"ruleId": "137", "severity": 1, "message": "155", "line": 22, "column": 10, "nodeType": "139", "messageId": "140", "endLine": 22, "endColumn": 21}, {"ruleId": "133", "severity": 1, "message": "156", "line": 17, "column": 6, "nodeType": "135", "endLine": 17, "endColumn": 20, "suggestions": "157"}, {"ruleId": "137", "severity": 1, "message": "158", "line": 7, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 7, "endColumn": 11}, {"ruleId": "137", "severity": 1, "message": "159", "line": 4, "column": 3, "nodeType": "139", "messageId": "140", "endLine": 4, "endColumn": 12}, {"ruleId": "133", "severity": 1, "message": "160", "line": 20, "column": 6, "nodeType": "135", "endLine": 20, "endColumn": 44, "suggestions": "161"}, {"ruleId": "133", "severity": 1, "message": "162", "line": 33, "column": 6, "nodeType": "135", "endLine": 33, "endColumn": 45, "suggestions": "163"}, {"ruleId": "133", "severity": 1, "message": "164", "line": 16, "column": 6, "nodeType": "135", "endLine": 16, "endColumn": 27, "suggestions": "165"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'handleAccountsChanged' and 'initializeWeb3'. Either include them or remove the dependency array.", "ArrayExpression", ["166"], "no-unused-vars", "'ArrowUpIcon' is defined but never used.", "Identifier", "unusedVar", "'getHeroTypeColor' is assigned a value but never used.", "'getStatIcon' is assigned a value but never used.", "'useEffect' is defined but never used.", "'useWeb3' is defined but never used.", "'userStats' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadStakingData'. Either include it or remove the dependency array.", ["167"], "React Hook useEffect has a missing dependency: 'loadGameData'. Either include it or remove the dependency array.", ["168"], "'HeroCard' is defined but never used.", "'BattleArena' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'ArrowLeftIcon' is defined but never used.", "'showBattleArena' is assigned a value but never used.", "'enemyHeroes' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadLeaderboard'. Either include it or remove the dependency array.", ["169"], "'UserIcon' is defined but never used.", "'HeartIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'initializeHeroStates'. Either include it or remove the dependency array.", ["170"], "React Hook useEffect has missing dependencies: 'onBattleEnd' and 'processNextLogEntry'. Either include them or remove the dependency array. If 'onBattleEnd' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["171"], "React Hook useEffect has a missing dependency: 'loadBalance'. Either include it or remove the dependency array.", ["172"], {"desc": "173", "fix": "174"}, {"desc": "175", "fix": "176"}, {"desc": "177", "fix": "178"}, {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, {"desc": "185", "fix": "186"}, "Update the dependencies array to be: [handleAccountsChanged, initializeWeb3]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [loadStakingData, user.walletAddress]", {"range": "189", "text": "190"}, "Update the dependencies array to be: [isAuthenticated, loadGameData]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [loadLeaderboard, selectedType]", {"range": "193", "text": "194"}, "Update the dependencies array to be: [battleLog, playerHeroes, enemyHeroes, initializeHeroStates]", {"range": "195", "text": "196"}, "Update the dependencies array to be: [isPlaying, currentLogIndex, battleLog, onBattleEnd, processNextLogEntry]", {"range": "197", "text": "198"}, "Update the dependencies array to be: [loadBalance, user.walletAddress]", {"range": "199", "text": "200"}, [2218, 2220], "[handleAccountsChanged, initializeWeb3]", [745, 766], "[loadStaking<PERSON><PERSON>, user.walletAddress]", [1003, 1020], "[isAuthenticated, loadGameData]", [428, 442], "[loadLeaderboard, selectedType]", [600, 638], "[battleLog, player<PERSON><PERSON><PERSON>, enemy<PERSON><PERSON><PERSON>, initializeHeroStates]", [967, 1006], "[isPlaying, currentLogIndex, battleLog, onBattleEnd, processNextLogEntry]", [611, 632], "[load<PERSON><PERSON>ce, user.walletAddress]"]