{"ast": null, "code": "const Base64 = \")!@#$%^&*(ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_\";\n/**\n *  @_ignore\n */\nexport function decodeBits(width, data) {\n  const maxValue = (1 << width) - 1;\n  const result = [];\n  let accum = 0,\n    bits = 0,\n    flood = 0;\n  for (let i = 0; i < data.length; i++) {\n    // Accumulate 6 bits of data\n    accum = accum << 6 | Base64.indexOf(data[i]);\n    bits += 6;\n    // While we have enough for a word...\n    while (bits >= width) {\n      // ...read the word\n      const value = accum >> bits - width;\n      accum &= (1 << bits - width) - 1;\n      bits -= width;\n      // A value of 0 indicates we exceeded maxValue, it\n      // floods over into the next value\n      if (value === 0) {\n        flood += maxValue;\n      } else {\n        result.push(value + flood);\n        flood = 0;\n      }\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["Base64", "decodeBits", "width", "data", "maxValue", "result", "accum", "bits", "flood", "i", "length", "indexOf", "value", "push"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\bit-reader.ts"], "sourcesContent": ["const Base64 = \")!@#$%^&*(ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_\";\n\n/**\n *  @_ignore\n */\nexport function decodeBits(width: number, data: string): Array<number> {\n    const maxValue = (1 << width) - 1;\n    const result: Array<number> = [ ];\n    let accum = 0, bits = 0, flood = 0;\n    for (let i = 0; i < data.length; i++) {\n\n        // Accumulate 6 bits of data\n        accum = ((accum << 6) | Base64.indexOf(data[i]));\n        bits += 6;\n\n        // While we have enough for a word...\n        while (bits >= width) {\n            // ...read the word\n            const value = (accum >> (bits - width));\n            accum &= (1 << (bits - width)) - 1;\n            bits -= width;\n\n            // A value of 0 indicates we exceeded maxValue, it\n            // floods over into the next value\n            if (value === 0) {\n                flood += maxValue;\n            } else {\n                result.push(value + flood);\n                flood = 0;\n            }\n        }\n    }\n\n    return result;\n}\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,kEAAkE;AAEjF;;;AAGA,OAAM,SAAUC,UAAUA,CAACC,KAAa,EAAEC,IAAY;EAClD,MAAMC,QAAQ,GAAG,CAAC,CAAC,IAAIF,KAAK,IAAI,CAAC;EACjC,MAAMG,MAAM,GAAkB,EAAG;EACjC,IAAIC,KAAK,GAAG,CAAC;IAAEC,IAAI,GAAG,CAAC;IAAEC,KAAK,GAAG,CAAC;EAClC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IAElC;IACAH,KAAK,GAAKA,KAAK,IAAI,CAAC,GAAIN,MAAM,CAACW,OAAO,CAACR,IAAI,CAACM,CAAC,CAAC,CAAE;IAChDF,IAAI,IAAI,CAAC;IAET;IACA,OAAOA,IAAI,IAAIL,KAAK,EAAE;MAClB;MACA,MAAMU,KAAK,GAAIN,KAAK,IAAKC,IAAI,GAAGL,KAAO;MACvCI,KAAK,IAAI,CAAC,CAAC,IAAKC,IAAI,GAAGL,KAAM,IAAI,CAAC;MAClCK,IAAI,IAAIL,KAAK;MAEb;MACA;MACA,IAAIU,KAAK,KAAK,CAAC,EAAE;QACbJ,KAAK,IAAIJ,QAAQ;OACpB,MAAM;QACHC,MAAM,CAACQ,IAAI,CAACD,KAAK,GAAGJ,KAAK,CAAC;QAC1BA,KAAK,GAAG,CAAC;;;;EAKrB,OAAOH,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}