{"ast": null, "code": "// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-ens data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: 0565ed049b9cf1614bb9e11ba7d8ac6a6fb96c893253d890f7e2b2884b9ded32\nvar COMPRESSED$1 = '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';\nconst FENCED = new Map([[8217, \"apostrophe\"], [8260, \"fraction slash\"], [12539, \"middle dot\"]]);\nconst NSM_MAX = 4;\nfunction decode_arithmetic(bytes) {\n  let pos = 0;\n  function u16() {\n    return bytes[pos++] << 8 | bytes[pos++];\n  }\n\n  // decode the frequency table\n  let symbol_count = u16();\n  let total = 1;\n  let acc = [0, 1]; // first symbol has frequency 1\n  for (let i = 1; i < symbol_count; i++) {\n    acc.push(total += u16());\n  }\n\n  // skip the sized-payload that the last 3 symbols index into\n  let skip = u16();\n  let pos_payload = pos;\n  pos += skip;\n  let read_width = 0;\n  let read_buffer = 0;\n  function read_bit() {\n    if (read_width == 0) {\n      // this will read beyond end of buffer\n      // but (undefined|0) => zero pad\n      read_buffer = read_buffer << 8 | bytes[pos++];\n      read_width = 8;\n    }\n    return read_buffer >> --read_width & 1;\n  }\n  const N = 31;\n  const FULL = 2 ** N;\n  const HALF = FULL >>> 1;\n  const QRTR = HALF >> 1;\n  const MASK = FULL - 1;\n\n  // fill register\n  let register = 0;\n  for (let i = 0; i < N; i++) register = register << 1 | read_bit();\n  let symbols = [];\n  let low = 0;\n  let range = FULL; // treat like a float\n  while (true) {\n    let value = Math.floor(((register - low + 1) * total - 1) / range);\n    let start = 0;\n    let end = symbol_count;\n    while (end - start > 1) {\n      // binary search\n      let mid = start + end >>> 1;\n      if (value < acc[mid]) {\n        end = mid;\n      } else {\n        start = mid;\n      }\n    }\n    if (start == 0) break; // first symbol is end mark\n    symbols.push(start);\n    let a = low + Math.floor(range * acc[start] / total);\n    let b = low + Math.floor(range * acc[start + 1] / total) - 1;\n    while (((a ^ b) & HALF) == 0) {\n      register = register << 1 & MASK | read_bit();\n      a = a << 1 & MASK;\n      b = b << 1 & MASK | 1;\n    }\n    while (a & ~b & QRTR) {\n      register = register & HALF | register << 1 & MASK >>> 1 | read_bit();\n      a = a << 1 ^ HALF;\n      b = (b ^ HALF) << 1 | HALF | 1;\n    }\n    low = a;\n    range = 1 + b - a;\n  }\n  let offset = symbol_count - 4;\n  return symbols.map(x => {\n    // index into payload\n    switch (x - offset) {\n      case 3:\n        return offset + 0x10100 + (bytes[pos_payload++] << 16 | bytes[pos_payload++] << 8 | bytes[pos_payload++]);\n      case 2:\n        return offset + 0x100 + (bytes[pos_payload++] << 8 | bytes[pos_payload++]);\n      case 1:\n        return offset + bytes[pos_payload++];\n      default:\n        return x - 1;\n    }\n  });\n}\n\n// returns an iterator which returns the next symbol\nfunction read_payload(v) {\n  let pos = 0;\n  return () => v[pos++];\n}\nfunction read_compressed_payload(s) {\n  return read_payload(decode_arithmetic(unsafe_atob(s)));\n}\n\n// unsafe in the sense:\n// expected well-formed Base64 w/o padding \n// 20220922: added for https://github.com/adraffy/ens-normalize.js/issues/4\nfunction unsafe_atob(s) {\n  let lookup = [];\n  [...'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'].forEach((c, i) => lookup[c.charCodeAt(0)] = i);\n  let n = s.length;\n  let ret = new Uint8Array(6 * n >> 3);\n  for (let i = 0, pos = 0, width = 0, carry = 0; i < n; i++) {\n    carry = carry << 6 | lookup[s.charCodeAt(i)];\n    width += 6;\n    if (width >= 8) {\n      ret[pos++] = carry >> (width -= 8);\n    }\n  }\n  return ret;\n}\n\n// eg. [0,1,2,3...] => [0,-1,1,-2,...]\nfunction signed(i) {\n  return i & 1 ? ~i >> 1 : i >> 1;\n}\nfunction read_deltas(n, next) {\n  let v = Array(n);\n  for (let i = 0, x = 0; i < n; i++) v[i] = x += signed(next());\n  return v;\n}\n\n// [123][5] => [0 3] [1 1] [0 0]\nfunction read_sorted(next, prev = 0) {\n  let ret = [];\n  while (true) {\n    let x = next();\n    let n = next();\n    if (!n) break;\n    prev += x;\n    for (let i = 0; i < n; i++) {\n      ret.push(prev + i);\n    }\n    prev += n + 1;\n  }\n  return ret;\n}\nfunction read_sorted_arrays(next) {\n  return read_array_while(() => {\n    let v = read_sorted(next);\n    if (v.length) return v;\n  });\n}\n\n// returns map of x => ys\nfunction read_mapped(next) {\n  let ret = [];\n  while (true) {\n    let w = next();\n    if (w == 0) break;\n    ret.push(read_linear_table(w, next));\n  }\n  while (true) {\n    let w = next() - 1;\n    if (w < 0) break;\n    ret.push(read_replacement_table(w, next));\n  }\n  return ret.flat();\n}\n\n// read until next is falsy\n// return array of read values\nfunction read_array_while(next) {\n  let v = [];\n  while (true) {\n    let x = next(v.length);\n    if (!x) break;\n    v.push(x);\n  }\n  return v;\n}\n\n// read w columns of length n\n// return as n rows of length w\nfunction read_transposed(n, w, next) {\n  let m = Array(n).fill().map(() => []);\n  for (let i = 0; i < w; i++) {\n    read_deltas(n, next).forEach((x, j) => m[j].push(x));\n  }\n  return m;\n}\n\n// returns [[x, ys], [x+dx, ys+dy], [x+2*dx, ys+2*dy], ...]\n// where dx/dy = steps, n = run size, w = length of y\nfunction read_linear_table(w, next) {\n  let dx = 1 + next();\n  let dy = next();\n  let vN = read_array_while(next);\n  let m = read_transposed(vN.length, 1 + w, next);\n  return m.flatMap((v, i) => {\n    let [x, ...ys] = v;\n    return Array(vN[i]).fill().map((_, j) => {\n      let j_dy = j * dy;\n      return [x + j * dx, ys.map(y => y + j_dy)];\n    });\n  });\n}\n\n// return [[x, ys...], ...]\n// where w = length of y\nfunction read_replacement_table(w, next) {\n  let n = 1 + next();\n  let m = read_transposed(n, 1 + w, next);\n  return m.map(v => [v[0], v.slice(1)]);\n}\nfunction read_trie(next) {\n  let ret = [];\n  let sorted = read_sorted(next);\n  expand(decode([]), []);\n  return ret; // not sorted\n  function decode(Q) {\n    // characters that lead into this node\n    let S = next(); // state: valid, save, check\n    let B = read_array_while(() => {\n      // buckets leading to new nodes\n      let cps = read_sorted(next).map(i => sorted[i]);\n      if (cps.length) return decode(cps);\n    });\n    return {\n      S,\n      B,\n      Q\n    };\n  }\n  function expand({\n    S,\n    B\n  }, cps, saved) {\n    if (S & 4 && saved === cps[cps.length - 1]) return;\n    if (S & 2) saved = cps[cps.length - 1];\n    if (S & 1) ret.push(cps);\n    for (let br of B) {\n      for (let cp of br.Q) {\n        expand(br, [...cps, cp], saved);\n      }\n    }\n  }\n}\nfunction hex_cp(cp) {\n  return cp.toString(16).toUpperCase().padStart(2, '0');\n}\nfunction quote_cp(cp) {\n  return `{${hex_cp(cp)}}`; // raffy convention: like \"\\u{X}\" w/o the \"\\u\"\n}\n\n/*\r\nexport function explode_cp(s) {\r\n\treturn [...s].map(c => c.codePointAt(0));\r\n}\r\n*/\nfunction explode_cp(s) {\n  // this is about 2x faster\n  let cps = [];\n  for (let pos = 0, len = s.length; pos < len;) {\n    let cp = s.codePointAt(pos);\n    pos += cp < 0x10000 ? 1 : 2;\n    cps.push(cp);\n  }\n  return cps;\n}\nfunction str_from_cps(cps) {\n  const chunk = 4096;\n  let len = cps.length;\n  if (len < chunk) return String.fromCodePoint(...cps);\n  let buf = [];\n  for (let i = 0; i < len;) {\n    buf.push(String.fromCodePoint(...cps.slice(i, i += chunk)));\n  }\n  return buf.join('');\n}\nfunction compare_arrays(a, b) {\n  let n = a.length;\n  let c = n - b.length;\n  for (let i = 0; c == 0 && i < n; i++) c = a[i] - b[i];\n  return c;\n}\n\n// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-nf data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: a974b6f8541fc29d919bc85118af0a44015851fab5343f8679cb31be2bdb209e\nvar COMPRESSED = '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';\n\n// https://unicode.org/reports/tr15/\n// for reference implementation\n// see: /derive/nf.js\n\n// algorithmic hangul\n// https://www.unicode.org/versions/Unicode15.0.0/ch03.pdf (page 144)\nconst S0 = 0xAC00;\nconst L0 = 0x1100;\nconst V0 = 0x1161;\nconst T0 = 0x11A7;\nconst L_COUNT = 19;\nconst V_COUNT = 21;\nconst T_COUNT = 28;\nconst N_COUNT = V_COUNT * T_COUNT;\nconst S_COUNT = L_COUNT * N_COUNT;\nconst S1 = S0 + S_COUNT;\nconst L1 = L0 + L_COUNT;\nconst V1 = V0 + V_COUNT;\nconst T1 = T0 + T_COUNT;\nfunction unpack_cc(packed) {\n  return packed >> 24 & 0xFF;\n}\nfunction unpack_cp(packed) {\n  return packed & 0xFFFFFF;\n}\nlet SHIFTED_RANK, EXCLUSIONS, DECOMP, RECOMP;\nfunction init$1() {\n  //console.time('nf');\n  let r = read_compressed_payload(COMPRESSED);\n  SHIFTED_RANK = new Map(read_sorted_arrays(r).flatMap((v, i) => v.map(x => [x, i + 1 << 24]))); // pre-shifted\n  EXCLUSIONS = new Set(read_sorted(r));\n  DECOMP = new Map();\n  RECOMP = new Map();\n  for (let [cp, cps] of read_mapped(r)) {\n    if (!EXCLUSIONS.has(cp) && cps.length == 2) {\n      let [a, b] = cps;\n      let bucket = RECOMP.get(a);\n      if (!bucket) {\n        bucket = new Map();\n        RECOMP.set(a, bucket);\n      }\n      bucket.set(b, cp);\n    }\n    DECOMP.set(cp, cps.reverse()); // stored reversed\n  }\n  //console.timeEnd('nf');\n  // 20230905: 11ms\n}\nfunction is_hangul(cp) {\n  return cp >= S0 && cp < S1;\n}\nfunction compose_pair(a, b) {\n  if (a >= L0 && a < L1 && b >= V0 && b < V1) {\n    return S0 + (a - L0) * N_COUNT + (b - V0) * T_COUNT;\n  } else if (is_hangul(a) && b > T0 && b < T1 && (a - S0) % T_COUNT == 0) {\n    return a + (b - T0);\n  } else {\n    let recomp = RECOMP.get(a);\n    if (recomp) {\n      recomp = recomp.get(b);\n      if (recomp) {\n        return recomp;\n      }\n    }\n    return -1;\n  }\n}\nfunction decomposed(cps) {\n  if (!SHIFTED_RANK) init$1();\n  let ret = [];\n  let buf = [];\n  let check_order = false;\n  function add(cp) {\n    let cc = SHIFTED_RANK.get(cp);\n    if (cc) {\n      check_order = true;\n      cp |= cc;\n    }\n    ret.push(cp);\n  }\n  for (let cp of cps) {\n    while (true) {\n      if (cp < 0x80) {\n        ret.push(cp);\n      } else if (is_hangul(cp)) {\n        let s_index = cp - S0;\n        let l_index = s_index / N_COUNT | 0;\n        let v_index = s_index % N_COUNT / T_COUNT | 0;\n        let t_index = s_index % T_COUNT;\n        add(L0 + l_index);\n        add(V0 + v_index);\n        if (t_index > 0) add(T0 + t_index);\n      } else {\n        let mapped = DECOMP.get(cp);\n        if (mapped) {\n          buf.push(...mapped);\n        } else {\n          add(cp);\n        }\n      }\n      if (!buf.length) break;\n      cp = buf.pop();\n    }\n  }\n  if (check_order && ret.length > 1) {\n    let prev_cc = unpack_cc(ret[0]);\n    for (let i = 1; i < ret.length; i++) {\n      let cc = unpack_cc(ret[i]);\n      if (cc == 0 || prev_cc <= cc) {\n        prev_cc = cc;\n        continue;\n      }\n      let j = i - 1;\n      while (true) {\n        let tmp = ret[j + 1];\n        ret[j + 1] = ret[j];\n        ret[j] = tmp;\n        if (!j) break;\n        prev_cc = unpack_cc(ret[--j]);\n        if (prev_cc <= cc) break;\n      }\n      prev_cc = unpack_cc(ret[i]);\n    }\n  }\n  return ret;\n}\nfunction composed_from_decomposed(v) {\n  let ret = [];\n  let stack = [];\n  let prev_cp = -1;\n  let prev_cc = 0;\n  for (let packed of v) {\n    let cc = unpack_cc(packed);\n    let cp = unpack_cp(packed);\n    if (prev_cp == -1) {\n      if (cc == 0) {\n        prev_cp = cp;\n      } else {\n        ret.push(cp);\n      }\n    } else if (prev_cc > 0 && prev_cc >= cc) {\n      if (cc == 0) {\n        ret.push(prev_cp, ...stack);\n        stack.length = 0;\n        prev_cp = cp;\n      } else {\n        stack.push(cp);\n      }\n      prev_cc = cc;\n    } else {\n      let composed = compose_pair(prev_cp, cp);\n      if (composed >= 0) {\n        prev_cp = composed;\n      } else if (prev_cc == 0 && cc == 0) {\n        ret.push(prev_cp);\n        prev_cp = cp;\n      } else {\n        stack.push(cp);\n        prev_cc = cc;\n      }\n    }\n  }\n  if (prev_cp >= 0) {\n    ret.push(prev_cp, ...stack);\n  }\n  return ret;\n}\n\n// note: cps can be iterable\nfunction nfd(cps) {\n  return decomposed(cps).map(unpack_cp);\n}\nfunction nfc(cps) {\n  return composed_from_decomposed(decomposed(cps));\n}\nconst HYPHEN = 0x2D;\nconst STOP = 0x2E;\nconst STOP_CH = '.';\nconst FE0F = 0xFE0F;\nconst UNIQUE_PH = 1;\n\n// 20230913: replace [...v] with Array_from(v) to avoid large spreads\nconst Array_from = x => Array.from(x); // Array.from.bind(Array);\n\nfunction group_has_cp(g, cp) {\n  // 20230913: keep primary and secondary distinct instead of creating valid union\n  return g.P.has(cp) || g.Q.has(cp);\n}\nclass Emoji extends Array {\n  get is_emoji() {\n    return true;\n  } // free tagging system\n}\nlet MAPPED, IGNORED, CM, NSM, ESCAPE, NFC_CHECK, GROUPS, WHOLE_VALID, WHOLE_MAP, VALID, EMOJI_LIST, EMOJI_ROOT;\nfunction init() {\n  if (MAPPED) return;\n  let r = read_compressed_payload(COMPRESSED$1);\n  const read_sorted_array = () => read_sorted(r);\n  const read_sorted_set = () => new Set(read_sorted_array());\n  const set_add_many = (set, v) => v.forEach(x => set.add(x));\n  MAPPED = new Map(read_mapped(r));\n  IGNORED = read_sorted_set(); // ignored characters are not valid, so just read raw codepoints\n\n  /*\r\n  // direct include from payload is smaller than the decompression code\r\n  const FENCED = new Map(read_array_while(() => {\r\n  \tlet cp = r();\r\n  \tif (cp) return [cp, read_str(r())];\r\n  }));\r\n  */\n  // 20230217: we still need all CM for proper error formatting\n  // but norm only needs NSM subset that are potentially-valid\n  CM = read_sorted_array();\n  NSM = new Set(read_sorted_array().map(i => CM[i]));\n  CM = new Set(CM);\n  ESCAPE = read_sorted_set(); // characters that should not be printed\n  NFC_CHECK = read_sorted_set(); // only needed to illustrate ens_tokenize() transformations\n\n  let chunks = read_sorted_arrays(r);\n  let unrestricted = r();\n  //const read_chunked = () => new Set(read_sorted_array().flatMap(i => chunks[i]).concat(read_sorted_array()));\n  const read_chunked = () => {\n    // 20230921: build set in parts, 2x faster\n    let set = new Set();\n    read_sorted_array().forEach(i => set_add_many(set, chunks[i]));\n    set_add_many(set, read_sorted_array());\n    return set;\n  };\n  GROUPS = read_array_while(i => {\n    // minifier property mangling seems unsafe\n    // so these are manually renamed to single chars\n    let N = read_array_while(r).map(x => x + 0x60);\n    if (N.length) {\n      let R = i >= unrestricted; // unrestricted then restricted\n      N[0] -= 32; // capitalize\n      N = str_from_cps(N);\n      if (R) N = `Restricted[${N}]`;\n      let P = read_chunked(); // primary\n      let Q = read_chunked(); // secondary\n      let M = !r(); // not-whitelisted, check for NSM\n      // *** this code currently isn't needed ***\n      /*\r\n      let V = [...P, ...Q].sort((a, b) => a-b); // derive: sorted valid\r\n      let M = r()-1; // number of combining mark\r\n      if (M < 0) { // whitelisted\r\n      \tM = new Map(read_array_while(() => {\r\n      \t\tlet i = r();\r\n      \t\tif (i) return [V[i-1], read_array_while(() => {\r\n      \t\t\tlet v = read_array_while(r);\r\n      \t\t\tif (v.length) return v.map(x => x-1);\r\n      \t\t})];\r\n      \t}));\r\n      }*/\n      return {\n        N,\n        P,\n        Q,\n        M,\n        R\n      };\n    }\n  });\n\n  // decode compressed wholes\n  WHOLE_VALID = read_sorted_set();\n  WHOLE_MAP = new Map();\n  let wholes = read_sorted_array().concat(Array_from(WHOLE_VALID)).sort((a, b) => a - b); // must be sorted\n  wholes.forEach((cp, i) => {\n    let d = r();\n    let w = wholes[i] = d ? wholes[i - d] : {\n      V: [],\n      M: new Map()\n    };\n    w.V.push(cp); // add to member set\n    if (!WHOLE_VALID.has(cp)) {\n      WHOLE_MAP.set(cp, w); // register with whole map\n    }\n  });\n\n  // compute confusable-extent complements\n  // usage: WHOLE_MAP.get(cp).M.get(cp) = complement set\n  for (let {\n    V,\n    M\n  } of new Set(WHOLE_MAP.values())) {\n    // connect all groups that have each whole character\n    let recs = [];\n    for (let cp of V) {\n      let gs = GROUPS.filter(g => group_has_cp(g, cp));\n      let rec = recs.find(({\n        G\n      }) => gs.some(g => G.has(g)));\n      if (!rec) {\n        rec = {\n          G: new Set(),\n          V: []\n        };\n        recs.push(rec);\n      }\n      rec.V.push(cp);\n      set_add_many(rec.G, gs);\n    }\n    // per character cache groups which are not a member of the extent\n    let union = recs.flatMap(x => Array_from(x.G)); // all of the groups used by this whole\n    for (let {\n      G,\n      V\n    } of recs) {\n      let complement = new Set(union.filter(g => !G.has(g))); // groups not covered by the extent\n      for (let cp of V) {\n        M.set(cp, complement); // this is the same reference\n      }\n    }\n  }\n\n  // compute valid set\n  // 20230924: VALID was union but can be re-used\n  VALID = new Set(); // exists in 1+ groups\n  let multi = new Set(); // exists in 2+ groups\n  const add_to_union = cp => VALID.has(cp) ? multi.add(cp) : VALID.add(cp);\n  for (let g of GROUPS) {\n    for (let cp of g.P) add_to_union(cp);\n    for (let cp of g.Q) add_to_union(cp);\n  }\n  // dual purpose WHOLE_MAP: return placeholder if unique non-confusable\n  for (let cp of VALID) {\n    if (!WHOLE_MAP.has(cp) && !multi.has(cp)) {\n      WHOLE_MAP.set(cp, UNIQUE_PH);\n    }\n  }\n  // add all decomposed parts\n  // see derive: \"Valid is Closed (via Brute-force)\"\n  set_add_many(VALID, nfd(VALID));\n\n  // decode emoji\n  // 20230719: emoji are now fully-expanded to avoid quirk logic \n  EMOJI_LIST = read_trie(r).map(v => Emoji.from(v)).sort(compare_arrays);\n  EMOJI_ROOT = new Map(); // this has approx 7K nodes (2+ per emoji)\n  for (let cps of EMOJI_LIST) {\n    // 20230719: change to *slightly* stricter algorithm which disallows \n    // insertion of misplaced FE0F in emoji sequences (matching ENSIP-15)\n    // example: beautified [A B] (eg. flag emoji) \n    //  before: allow: [A FE0F B], error: [A FE0F FE0F B] \n    //   after: error: both\n    // note: this code now matches ENSNormalize.{cs,java} logic\n    let prev = [EMOJI_ROOT];\n    for (let cp of cps) {\n      let next = prev.map(node => {\n        let child = node.get(cp);\n        if (!child) {\n          // should this be object? \n          // (most have 1-2 items, few have many)\n          // 20230719: no, v8 default map is 4?\n          child = new Map();\n          node.set(cp, child);\n        }\n        return child;\n      });\n      if (cp === FE0F) {\n        prev.push(...next); // less than 20 elements\n      } else {\n        prev = next;\n      }\n    }\n    for (let x of prev) {\n      x.V = cps;\n    }\n  }\n}\n\n// if escaped: {HEX}\n//       else: \"x\" {HEX}\nfunction quoted_cp(cp) {\n  return (should_escape(cp) ? '' : `${bidi_qq(safe_str_from_cps([cp]))} `) + quote_cp(cp);\n}\n\n// 20230211: some messages can be mixed-directional and result in spillover\n// use 200E after a quoted string to force the remainder of a string from \n// acquring the direction of the quote\n// https://www.w3.org/International/questions/qa-bidi-unicode-controls#exceptions\nfunction bidi_qq(s) {\n  return `\"${s}\"\\u200E`; // strong LTR\n}\nfunction check_label_extension(cps) {\n  if (cps.length >= 4 && cps[2] == HYPHEN && cps[3] == HYPHEN) {\n    throw new Error(`invalid label extension: \"${str_from_cps(cps.slice(0, 4))}\"`); // this can only be ascii so cant be bidi\n  }\n}\nfunction check_leading_underscore(cps) {\n  const UNDERSCORE = 0x5F;\n  for (let i = cps.lastIndexOf(UNDERSCORE); i > 0;) {\n    if (cps[--i] !== UNDERSCORE) {\n      throw new Error('underscore allowed only at start');\n    }\n  }\n}\n// check that a fenced cp is not leading, trailing, or touching another fenced cp\nfunction check_fenced(cps) {\n  let cp = cps[0];\n  let prev = FENCED.get(cp);\n  if (prev) throw error_placement(`leading ${prev}`);\n  let n = cps.length;\n  let last = -1; // prevents trailing from throwing\n  for (let i = 1; i < n; i++) {\n    cp = cps[i];\n    let match = FENCED.get(cp);\n    if (match) {\n      // since cps[0] isn't fenced, cps[1] cannot throw\n      if (last == i) throw error_placement(`${prev} + ${match}`);\n      last = i + 1;\n      prev = match;\n    }\n  }\n  if (last == n) throw error_placement(`trailing ${prev}`);\n}\n\n// create a safe to print string \n// invisibles are escaped\n// leading cm uses placeholder\n// if cps exceed max, middle truncate with ellipsis\n// quoter(cp) => string, eg. 3000 => \"{3000}\"\n// note: in html, you'd call this function then replace [<>&] with entities\nfunction safe_str_from_cps(cps, max = Infinity, quoter = quote_cp) {\n  //if (Number.isInteger(cps)) cps = [cps];\n  //if (!Array.isArray(cps)) throw new TypeError(`expected codepoints`);\n  let buf = [];\n  if (is_combining_mark(cps[0])) buf.push('◌');\n  if (cps.length > max) {\n    max >>= 1;\n    cps = [...cps.slice(0, max), 0x2026, ...cps.slice(-max)];\n  }\n  let prev = 0;\n  let n = cps.length;\n  for (let i = 0; i < n; i++) {\n    let cp = cps[i];\n    if (should_escape(cp)) {\n      buf.push(str_from_cps(cps.slice(prev, i)));\n      buf.push(quoter(cp));\n      prev = i + 1;\n    }\n  }\n  buf.push(str_from_cps(cps.slice(prev, n)));\n  return buf.join('');\n}\n\n// note: set(s) cannot be exposed because they can be modified\n// note: Object.freeze() doesn't work\nfunction is_combining_mark(cp) {\n  init();\n  return CM.has(cp);\n}\nfunction should_escape(cp) {\n  init();\n  return ESCAPE.has(cp);\n}\n\n// return all supported emoji as fully-qualified emoji \n// ordered by length then lexicographic \nfunction ens_emoji() {\n  init();\n  return EMOJI_LIST.map(x => x.slice()); // emoji are exposed so copy\n}\nfunction ens_normalize_fragment(frag, decompose) {\n  init();\n  let nf = decompose ? nfd : nfc;\n  return frag.split(STOP_CH).map(label => str_from_cps(tokens_from_str(explode_cp(label), nf, filter_fe0f).flat())).join(STOP_CH);\n}\nfunction ens_normalize(name) {\n  return flatten(split(name, nfc, filter_fe0f));\n}\nfunction ens_beautify(name) {\n  let labels = split(name, nfc, x => x); // emoji not exposed\n  for (let {\n    type,\n    output,\n    error\n  } of labels) {\n    if (error) break; // flatten will throw\n\n    // replace leading/trailing hyphen\n    // 20230121: consider beautifing all or leading/trailing hyphen to unicode variant\n    // not exactly the same in every font, but very similar: \"-\" vs \"‐\"\n    /*\r\n    const UNICODE_HYPHEN = 0x2010;\r\n    // maybe this should replace all for visual consistancy?\r\n    // `node tools/reg-count.js regex ^-\\{2,\\}` => 592\r\n    //for (let i = 0; i < output.length; i++) if (output[i] == 0x2D) output[i] = 0x2010;\r\n    if (output[0] == HYPHEN) output[0] = UNICODE_HYPHEN;\r\n    let end = output.length-1;\r\n    if (output[end] == HYPHEN) output[end] = UNICODE_HYPHEN;\r\n    */\n    // 20230123: WHATWG URL uses \"CheckHyphens\" false\n    // https://url.spec.whatwg.org/#idna\n\n    // update ethereum symbol\n    // ξ => Ξ if not greek\n    if (type !== 'Greek') array_replace(output, 0x3BE, 0x39E);\n\n    // 20221213: fixes bidi subdomain issue, but breaks invariant (200E is disallowed)\n    // could be fixed with special case for: 2D (.) + 200E (LTR)\n    // https://discuss.ens.domains/t/bidi-label-ordering-spoof/15824\n    //output.splice(0, 0, 0x200E);\n  }\n  return flatten(labels);\n}\nfunction array_replace(v, a, b) {\n  let prev = 0;\n  while (true) {\n    let next = v.indexOf(a, prev);\n    if (next < 0) break;\n    v[next] = b;\n    prev = next + 1;\n  }\n}\nfunction ens_split(name, preserve_emoji) {\n  return split(name, nfc, preserve_emoji ? x => x.slice() : filter_fe0f); // emoji are exposed so copy\n}\nfunction split(name, nf, ef) {\n  if (!name) return []; // 20230719: empty name allowance\n  init();\n  let offset = 0;\n  // https://unicode.org/reports/tr46/#Validity_Criteria\n  // 4.) \"The label must not contain a U+002E ( . ) FULL STOP.\"\n  return name.split(STOP_CH).map(label => {\n    let input = explode_cp(label);\n    let info = {\n      input,\n      offset // codepoint, not substring!\n    };\n    offset += input.length + 1; // + stop\n    try {\n      // 1.) \"The label must be in Unicode Normalization Form NFC\"\n      let tokens = info.tokens = tokens_from_str(input, nf, ef);\n      let token_count = tokens.length;\n      let type;\n      if (!token_count) {\n        // the label was effectively empty (could of had ignored characters)\n        //norm = [];\n        //type = 'None'; // use this instead of next match, \"ASCII\"\n        // 20230120: change to strict\n        // https://discuss.ens.domains/t/ens-name-normalization-2nd/14564/59\n        throw new Error(`empty label`);\n      }\n      let norm = info.output = tokens.flat();\n      check_leading_underscore(norm);\n      let emoji = info.emoji = token_count > 1 || tokens[0].is_emoji; // same as: tokens.some(x => x.is_emoji);\n      if (!emoji && norm.every(cp => cp < 0x80)) {\n        // special case for ascii\n        // 20230123: matches matches WHATWG, see note 3.3\n        check_label_extension(norm); // only needed for ascii\n        // cant have fenced\n        // cant have cm\n        // cant have wholes\n        // see derive: \"Fastpath ASCII\"\n        type = 'ASCII';\n      } else {\n        let chars = tokens.flatMap(x => x.is_emoji ? [] : x); // all of the nfc tokens concat together\n        if (!chars.length) {\n          // theres no text, just emoji\n          type = 'Emoji';\n        } else {\n          // 5.) \"The label must not begin with a combining mark, that is: General_Category=Mark.\"\n          if (CM.has(norm[0])) throw error_placement('leading combining mark');\n          for (let i = 1; i < token_count; i++) {\n            // we've already checked the first token\n            let cps = tokens[i];\n            if (!cps.is_emoji && CM.has(cps[0])) {\n              // every text token has emoji neighbors, eg. EtEEEtEt...\n              // bidi_qq() not needed since emoji is LTR and cps is a CM\n              throw error_placement(`emoji + combining mark: \"${str_from_cps(tokens[i - 1])} + ${safe_str_from_cps([cps[0]])}\"`);\n            }\n          }\n          check_fenced(norm);\n          let unique = Array_from(new Set(chars));\n          let [g] = determine_group(unique); // take the first match\n          // see derive: \"Matching Groups have Same CM Style\"\n          // alternative: could form a hybrid type: Latin/Japanese/...\t\n          check_group(g, chars); // need text in order\n          check_whole(g, unique); // only need unique text (order would be required for multiple-char confusables)\n          type = g.N;\n          // 20230121: consider exposing restricted flag\n          // it's simpler to just check for 'Restricted'\n          // or even better: type.endsWith(']')\n          //if (g.R) info.restricted = true;\n        }\n      }\n      info.type = type;\n    } catch (err) {\n      info.error = err; // use full error object\n    }\n    return info;\n  });\n}\nfunction check_whole(group, unique) {\n  let maker;\n  let shared = [];\n  for (let cp of unique) {\n    let whole = WHOLE_MAP.get(cp);\n    if (whole === UNIQUE_PH) return; // unique, non-confusable\n    if (whole) {\n      let set = whole.M.get(cp); // groups which have a character that look-like this character\n      maker = maker ? maker.filter(g => set.has(g)) : Array_from(set);\n      if (!maker.length) return; // confusable intersection is empty\n    } else {\n      shared.push(cp);\n    }\n  }\n  if (maker) {\n    // we have 1+ confusable\n    // check if any of the remaining groups\n    // contain the shared characters too\n    for (let g of maker) {\n      if (shared.every(cp => group_has_cp(g, cp))) {\n        throw new Error(`whole-script confusable: ${group.N}/${g.N}`);\n      }\n    }\n  }\n}\n\n// assumption: unique.size > 0\n// returns list of matching groups\nfunction determine_group(unique) {\n  let groups = GROUPS;\n  for (let cp of unique) {\n    // note: we need to dodge CM that are whitelisted\n    // but that code isn't currently necessary\n    let gs = groups.filter(g => group_has_cp(g, cp));\n    if (!gs.length) {\n      if (!GROUPS.some(g => group_has_cp(g, cp))) {\n        // the character was composed of valid parts\n        // but it's NFC form is invalid\n        // 20230716: change to more exact statement, see: ENSNormalize.{cs,java}\n        // note: this doesn't have to be a composition\n        // 20230720: change to full check\n        throw error_disallowed(cp); // this should be rare\n      } else {\n        // there is no group that contains all these characters\n        // throw using the highest priority group that matched\n        // https://www.unicode.org/reports/tr39/#mixed_script_confusables\n        throw error_group_member(groups[0], cp);\n      }\n    }\n    groups = gs;\n    if (gs.length == 1) break; // there is only one group left\n  }\n  // there are at least 1 group(s) with all of these characters\n  return groups;\n}\n\n// throw on first error\nfunction flatten(split) {\n  return split.map(({\n    input,\n    error,\n    output\n  }) => {\n    if (error) {\n      // don't print label again if just a single label\n      let msg = error.message;\n      // bidi_qq() only necessary if msg is digits\n      throw new Error(split.length == 1 ? msg : `Invalid label ${bidi_qq(safe_str_from_cps(input, 63))}: ${msg}`);\n    }\n    return str_from_cps(output);\n  }).join(STOP_CH);\n}\nfunction error_disallowed(cp) {\n  // TODO: add cp to error?\n  return new Error(`disallowed character: ${quoted_cp(cp)}`);\n}\nfunction error_group_member(g, cp) {\n  let quoted = quoted_cp(cp);\n  let gg = GROUPS.find(g => g.P.has(cp)); // only check primary\n  if (gg) {\n    quoted = `${gg.N} ${quoted}`;\n  }\n  return new Error(`illegal mixture: ${g.N} + ${quoted}`);\n}\nfunction error_placement(where) {\n  return new Error(`illegal placement: ${where}`);\n}\n\n// assumption: cps.length > 0\n// assumption: cps[0] isn't a CM\n// assumption: the previous character isn't an emoji\nfunction check_group(g, cps) {\n  for (let cp of cps) {\n    if (!group_has_cp(g, cp)) {\n      // for whitelisted scripts, this will throw illegal mixture on invalid cm, eg. \"e{300}{300}\"\n      // at the moment, it's unnecessary to introduce an extra error type\n      // until there exists a whitelisted multi-character\n      //   eg. if (M < 0 && is_combining_mark(cp)) { ... }\n      // there are 3 cases:\n      //   1. illegal cm for wrong group => mixture error\n      //   2. illegal cm for same group => cm error\n      //       requires set of whitelist cm per group: \n      //        eg. new Set([...g.P, ...g.Q].flatMap(nfc).filter(cp => CM.has(cp)))\n      //   3. wrong group => mixture error\n      throw error_group_member(g, cp);\n    }\n  }\n  //if (M >= 0) { // we have a known fixed cm count\n  if (g.M) {\n    // we need to check for NSM\n    let decomposed = nfd(cps);\n    for (let i = 1, e = decomposed.length; i < e; i++) {\n      // see: assumption\n      // 20230210: bugfix: using cps instead of decomposed h/t Carbon225\n      /*\r\n      if (CM.has(decomposed[i])) {\r\n      \tlet j = i + 1;\r\n      \twhile (j < e && CM.has(decomposed[j])) j++;\r\n      \tif (j - i > M) {\r\n      \t\tthrow new Error(`too many combining marks: ${g.N} ${bidi_qq(str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${M})`);\r\n      \t}\r\n      \ti = j;\r\n      }\r\n      */\n      // 20230217: switch to NSM counting\n      // https://www.unicode.org/reports/tr39/#Optional_Detection\n      if (NSM.has(decomposed[i])) {\n        let j = i + 1;\n        for (let cp; j < e && NSM.has(cp = decomposed[j]); j++) {\n          // a. Forbid sequences of the same nonspacing mark.\n          for (let k = i; k < j; k++) {\n            // O(n^2) but n < 100\n            if (decomposed[k] == cp) {\n              throw new Error(`duplicate non-spacing marks: ${quoted_cp(cp)}`);\n            }\n          }\n        }\n        // parse to end so we have full nsm count\n        // b. Forbid sequences of more than 4 nonspacing marks (gc=Mn or gc=Me).\n        if (j - i > NSM_MAX) {\n          // note: this slice starts with a base char or spacing-mark cm\n          throw new Error(`excessive non-spacing marks: ${bidi_qq(safe_str_from_cps(decomposed.slice(i - 1, j)))} (${j - i}/${NSM_MAX})`);\n        }\n        i = j;\n      }\n    }\n  }\n  // *** this code currently isn't needed ***\n  /*\r\n  let cm_whitelist = M instanceof Map;\r\n  for (let i = 0, e = cps.length; i < e; ) {\r\n  \tlet cp = cps[i++];\r\n  \tlet seqs = cm_whitelist && M.get(cp);\r\n  \tif (seqs) { \r\n  \t\t// list of codepoints that can follow\r\n  \t\t// if this exists, this will always be 1+\r\n  \t\tlet j = i;\r\n  \t\twhile (j < e && CM.has(cps[j])) j++;\r\n  \t\tlet cms = cps.slice(i, j);\r\n  \t\tlet match = seqs.find(seq => !compare_arrays(seq, cms));\r\n  \t\tif (!match) throw new Error(`disallowed combining mark sequence: \"${safe_str_from_cps([cp, ...cms])}\"`);\r\n  \t\ti = j;\r\n  \t} else if (!V.has(cp)) {\r\n  \t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n  \t\tlet quoted = quoted_cp(cp);\r\n  \t\tfor (let cp of cps) {\r\n  \t\t\tlet u = UNIQUE.get(cp);\r\n  \t\t\tif (u && u !== g) {\r\n  \t\t\t\t// if both scripts are restricted this error is confusing\r\n  \t\t\t\t// because we don't differentiate RestrictedA from RestrictedB \r\n  \t\t\t\tif (!u.R) quoted = `${quoted} is ${u.N}`;\r\n  \t\t\t\tbreak;\r\n  \t\t\t}\r\n  \t\t}\r\n  \t\tthrow new Error(`disallowed ${g.N} character: ${quoted}`);\r\n  \t\t//throw new Error(`disallowed character: ${quoted} (expected ${g.N})`);\r\n  \t\t//throw new Error(`${g.N} does not allow: ${quoted}`);\r\n  \t}\r\n  }\r\n  if (!cm_whitelist) {\r\n  \tlet decomposed = nfd(cps);\r\n  \tfor (let i = 1, e = decomposed.length; i < e; i++) { // we know it can't be cm leading\r\n  \t\tif (CM.has(decomposed[i])) {\r\n  \t\t\tlet j = i + 1;\r\n  \t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n  \t\t\tif (j - i > M) {\r\n  \t\t\t\tthrow new Error(`too many combining marks: \"${str_from_cps(decomposed.slice(i-1, j))}\" (${j-i}/${M})`);\r\n  \t\t\t}\r\n  \t\t\ti = j;\r\n  \t\t}\r\n  \t}\r\n  }\r\n  */\n}\n\n// given a list of codepoints\n// returns a list of lists, where emoji are a fully-qualified (as Array subclass)\n// eg. explode_cp(\"abc💩d\") => [[61, 62, 63], Emoji[1F4A9, FE0F], [64]]\n// 20230818: rename for 'process' name collision h/t Javarome\n// https://github.com/adraffy/ens-normalize.js/issues/23\nfunction tokens_from_str(input, nf, ef) {\n  let ret = [];\n  let chars = [];\n  input = input.slice().reverse(); // flip so we can pop\n  while (input.length) {\n    let emoji = consume_emoji_reversed(input);\n    if (emoji) {\n      if (chars.length) {\n        ret.push(nf(chars));\n        chars = [];\n      }\n      ret.push(ef(emoji));\n    } else {\n      let cp = input.pop();\n      if (VALID.has(cp)) {\n        chars.push(cp);\n      } else {\n        let cps = MAPPED.get(cp);\n        if (cps) {\n          chars.push(...cps); // less than 10 elements\n        } else if (!IGNORED.has(cp)) {\n          // 20230912: unicode 15.1 changed the order of processing such that\n          // disallowed parts are only rejected after NFC\n          // https://unicode.org/reports/tr46/#Validity_Criteria\n          // this doesn't impact normalization as of today\n          // technically, this error can be removed as the group logic will apply similar logic\n          // however the error type might be less clear\n          throw error_disallowed(cp);\n        }\n      }\n    }\n  }\n  if (chars.length) {\n    ret.push(nf(chars));\n  }\n  return ret;\n}\nfunction filter_fe0f(cps) {\n  return cps.filter(cp => cp != FE0F);\n}\n\n// given array of codepoints\n// returns the longest valid emoji sequence (or undefined if no match)\n// *MUTATES* the supplied array\n// disallows interleaved ignored characters\n// fills (optional) eaten array with matched codepoints\nfunction consume_emoji_reversed(cps, eaten) {\n  let node = EMOJI_ROOT;\n  let emoji;\n  let pos = cps.length;\n  while (pos) {\n    node = node.get(cps[--pos]);\n    if (!node) break;\n    let {\n      V\n    } = node;\n    if (V) {\n      // this is a valid emoji (so far)\n      emoji = V;\n      if (eaten) eaten.push(...cps.slice(pos).reverse()); // (optional) copy input, used for ens_tokenize()\n      cps.length = pos; // truncate\n    }\n  }\n  return emoji;\n}\n\n// ************************************************************\n// tokenizer \n\nconst TY_VALID = 'valid';\nconst TY_MAPPED = 'mapped';\nconst TY_IGNORED = 'ignored';\nconst TY_DISALLOWED = 'disallowed';\nconst TY_EMOJI = 'emoji';\nconst TY_NFC = 'nfc';\nconst TY_STOP = 'stop';\nfunction ens_tokenize(name, {\n  nf = true // collapse unnormalized runs into a single token\n} = {}) {\n  init();\n  let input = explode_cp(name).reverse();\n  let eaten = [];\n  let tokens = [];\n  while (input.length) {\n    let emoji = consume_emoji_reversed(input, eaten);\n    if (emoji) {\n      tokens.push({\n        type: TY_EMOJI,\n        emoji: emoji.slice(),\n        // copy emoji\n        input: eaten,\n        cps: filter_fe0f(emoji)\n      });\n      eaten = []; // reset buffer\n    } else {\n      let cp = input.pop();\n      if (cp == STOP) {\n        tokens.push({\n          type: TY_STOP,\n          cp\n        });\n      } else if (VALID.has(cp)) {\n        tokens.push({\n          type: TY_VALID,\n          cps: [cp]\n        });\n      } else if (IGNORED.has(cp)) {\n        tokens.push({\n          type: TY_IGNORED,\n          cp\n        });\n      } else {\n        let cps = MAPPED.get(cp);\n        if (cps) {\n          tokens.push({\n            type: TY_MAPPED,\n            cp,\n            cps: cps.slice()\n          });\n        } else {\n          tokens.push({\n            type: TY_DISALLOWED,\n            cp\n          });\n        }\n      }\n    }\n  }\n  if (nf) {\n    for (let i = 0, start = -1; i < tokens.length; i++) {\n      let token = tokens[i];\n      if (is_valid_or_mapped(token.type)) {\n        if (requires_check(token.cps)) {\n          // normalization might be needed\n          let end = i + 1;\n          for (let pos = end; pos < tokens.length; pos++) {\n            // find adjacent text\n            let {\n              type,\n              cps\n            } = tokens[pos];\n            if (is_valid_or_mapped(type)) {\n              if (!requires_check(cps)) break;\n              end = pos + 1;\n            } else if (type !== TY_IGNORED) {\n              // || type !== TY_DISALLOWED) { \n              break;\n            }\n          }\n          if (start < 0) start = i;\n          let slice = tokens.slice(start, end);\n          let cps0 = slice.flatMap(x => is_valid_or_mapped(x.type) ? x.cps : []); // strip junk tokens\n          let cps = nfc(cps0);\n          if (compare_arrays(cps, cps0)) {\n            // bundle into an nfc token\n            tokens.splice(start, end - start, {\n              type: TY_NFC,\n              input: cps0,\n              // there are 3 states: tokens0 ==(process)=> input ==(nfc)=> tokens/cps\n              cps,\n              tokens0: collapse_valid_tokens(slice),\n              tokens: ens_tokenize(str_from_cps(cps), {\n                nf: false\n              })\n            });\n            i = start;\n          } else {\n            i = end - 1; // skip to end of slice\n          }\n          start = -1; // reset\n        } else {\n          start = i; // remember last\n        }\n      } else if (token.type !== TY_IGNORED) {\n        // 20221024: is this correct?\n        start = -1; // reset\n      }\n    }\n  }\n  return collapse_valid_tokens(tokens);\n}\nfunction is_valid_or_mapped(type) {\n  return type == TY_VALID || type == TY_MAPPED;\n}\nfunction requires_check(cps) {\n  return cps.some(cp => NFC_CHECK.has(cp));\n}\nfunction collapse_valid_tokens(tokens) {\n  for (let i = 0; i < tokens.length; i++) {\n    if (tokens[i].type == TY_VALID) {\n      let j = i + 1;\n      while (j < tokens.length && tokens[j].type == TY_VALID) j++;\n      tokens.splice(i, j - i, {\n        type: TY_VALID,\n        cps: tokens.slice(i, j).flatMap(x => x.cps)\n      });\n    }\n  }\n  return tokens;\n}\nexport { ens_beautify, ens_emoji, ens_normalize, ens_normalize_fragment, ens_split, ens_tokenize, is_combining_mark, nfc, nfd, safe_str_from_cps, should_escape };", "map": {"version": 3, "names": ["COMPRESSED$1", "FENCED", "Map", "NSM_MAX", "decode_arithmetic", "bytes", "pos", "u16", "symbol_count", "total", "acc", "i", "push", "skip", "pos_payload", "read_width", "read_buffer", "read_bit", "N", "FULL", "HALF", "QRTR", "MASK", "register", "symbols", "low", "range", "value", "Math", "floor", "start", "end", "mid", "a", "b", "offset", "map", "x", "read_payload", "v", "read_compressed_payload", "s", "unsafe_atob", "lookup", "for<PERSON>ach", "c", "charCodeAt", "n", "length", "ret", "Uint8Array", "width", "carry", "signed", "read_deltas", "next", "Array", "read_sorted", "prev", "read_sorted_arrays", "read_array_while", "read_mapped", "w", "read_linear_table", "read_replacement_table", "flat", "read_transposed", "m", "fill", "j", "dx", "dy", "vN", "flatMap", "ys", "_", "j_dy", "y", "slice", "read_trie", "sorted", "expand", "decode", "Q", "S", "B", "cps", "saved", "br", "cp", "hex_cp", "toString", "toUpperCase", "padStart", "quote_cp", "explode_cp", "len", "codePointAt", "str_from_cps", "chunk", "String", "fromCodePoint", "buf", "join", "compare_arrays", "COMPRESSED", "S0", "L0", "V0", "T0", "L_COUNT", "V_COUNT", "T_COUNT", "N_COUNT", "S_COUNT", "S1", "L1", "V1", "T1", "unpack_cc", "packed", "unpack_cp", "SHIFTED_RANK", "EXCLUSIONS", "DECOMP", "RECOMP", "init$1", "r", "Set", "has", "bucket", "get", "set", "reverse", "is_hangul", "compose_pair", "recomp", "decomposed", "check_order", "add", "cc", "s_index", "l_index", "v_index", "t_index", "mapped", "pop", "prev_cc", "tmp", "composed_from_decomposed", "stack", "prev_cp", "composed", "nfd", "nfc", "HYPHEN", "STOP", "STOP_CH", "FE0F", "UNIQUE_PH", "Array_from", "from", "group_has_cp", "g", "P", "<PERSON><PERSON><PERSON>", "is_emoji", "MAPPED", "IGNORED", "CM", "NSM", "ESCAPE", "NFC_CHECK", "GROUPS", "WHOLE_VALID", "WHOLE_MAP", "VALID", "EMOJI_LIST", "EMOJI_ROOT", "init", "read_sorted_array", "read_sorted_set", "set_add_many", "chunks", "unrestricted", "read_chunked", "R", "M", "wholes", "concat", "sort", "d", "V", "values", "recs", "gs", "filter", "rec", "find", "G", "some", "union", "complement", "multi", "add_to_union", "node", "child", "quoted_cp", "should_escape", "bidi_qq", "safe_str_from_cps", "check_label_extension", "Error", "check_leading_underscore", "UNDERSCORE", "lastIndexOf", "check_fenced", "error_placement", "last", "match", "max", "Infinity", "quoter", "is_combining_mark", "ens_emoji", "ens_normalize_fragment", "frag", "decompose", "nf", "split", "label", "tokens_from_str", "filter_fe0f", "ens_normalize", "name", "flatten", "ens_beautify", "labels", "type", "output", "error", "array_replace", "indexOf", "ens_split", "preserve_emoji", "ef", "input", "info", "tokens", "token_count", "norm", "emoji", "every", "chars", "unique", "determine_group", "check_group", "check_whole", "err", "group", "maker", "shared", "whole", "groups", "error_disallowed", "error_group_member", "msg", "message", "quoted", "gg", "where", "e", "k", "consume_emoji_reversed", "eaten", "TY_VALID", "TY_MAPPED", "TY_IGNORED", "TY_DISALLOWED", "TY_EMOJI", "TY_NFC", "TY_STOP", "ens_tokenize", "token", "is_valid_or_mapped", "requires_check", "cps0", "splice", "tokens0", "collapse_valid_tokens"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/@adraffy/ens-normalize/dist/index.mjs"], "sourcesContent": ["// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-ens data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: 0565ed049b9cf1614bb9e11ba7d8ac6a6fb96c893253d890f7e2b2884b9ded32\nvar COMPRESSED$1 = '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';\nconst FENCED = new Map([[8217,\"apostrophe\"],[8260,\"fraction slash\"],[12539,\"middle dot\"]]);\nconst NSM_MAX = 4;\n\nfunction decode_arithmetic(bytes) {\r\n\tlet pos = 0;\r\n\tfunction u16() { return (bytes[pos++] << 8) | bytes[pos++]; }\r\n\t\r\n\t// decode the frequency table\r\n\tlet symbol_count = u16();\r\n\tlet total = 1;\r\n\tlet acc = [0, 1]; // first symbol has frequency 1\r\n\tfor (let i = 1; i < symbol_count; i++) {\r\n\t\tacc.push(total += u16());\r\n\t}\r\n\r\n\t// skip the sized-payload that the last 3 symbols index into\r\n\tlet skip = u16();\r\n\tlet pos_payload = pos;\r\n\tpos += skip;\r\n\r\n\tlet read_width = 0;\r\n\tlet read_buffer = 0; \r\n\tfunction read_bit() {\r\n\t\tif (read_width == 0) {\r\n\t\t\t// this will read beyond end of buffer\r\n\t\t\t// but (undefined|0) => zero pad\r\n\t\t\tread_buffer = (read_buffer << 8) | bytes[pos++];\r\n\t\t\tread_width = 8;\r\n\t\t}\r\n\t\treturn (read_buffer >> --read_width) & 1;\r\n\t}\r\n\r\n\tconst N = 31;\r\n\tconst FULL = 2**N;\r\n\tconst HALF = FULL >>> 1;\r\n\tconst QRTR = HALF >> 1;\r\n\tconst MASK = FULL - 1;\r\n\r\n\t// fill register\r\n\tlet register = 0;\r\n\tfor (let i = 0; i < N; i++) register = (register << 1) | read_bit();\r\n\r\n\tlet symbols = [];\r\n\tlet low = 0;\r\n\tlet range = FULL; // treat like a float\r\n\twhile (true) {\r\n\t\tlet value = Math.floor((((register - low + 1) * total) - 1) / range);\r\n\t\tlet start = 0;\r\n\t\tlet end = symbol_count;\r\n\t\twhile (end - start > 1) { // binary search\r\n\t\t\tlet mid = (start + end) >>> 1;\r\n\t\t\tif (value < acc[mid]) {\r\n\t\t\t\tend = mid;\r\n\t\t\t} else {\r\n\t\t\t\tstart = mid;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (start == 0) break; // first symbol is end mark\r\n\t\tsymbols.push(start);\r\n\t\tlet a = low + Math.floor(range * acc[start]   / total);\r\n\t\tlet b = low + Math.floor(range * acc[start+1] / total) - 1;\r\n\t\twhile (((a ^ b) & HALF) == 0) {\r\n\t\t\tregister = (register << 1) & MASK | read_bit();\r\n\t\t\ta = (a << 1) & MASK;\r\n\t\t\tb = (b << 1) & MASK | 1;\r\n\t\t}\r\n\t\twhile (a & ~b & QRTR) {\r\n\t\t\tregister = (register & HALF) | ((register << 1) & (MASK >>> 1)) | read_bit();\r\n\t\t\ta = (a << 1) ^ HALF;\r\n\t\t\tb = ((b ^ HALF) << 1) | HALF | 1;\r\n\t\t}\r\n\t\tlow = a;\r\n\t\trange = 1 + b - a;\r\n\t}\r\n\tlet offset = symbol_count - 4;\r\n\treturn symbols.map(x => { // index into payload\r\n\t\tswitch (x - offset) {\r\n\t\t\tcase 3: return offset + 0x10100 + ((bytes[pos_payload++] << 16) | (bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 2: return offset + 0x100 + ((bytes[pos_payload++] << 8) | bytes[pos_payload++]);\r\n\t\t\tcase 1: return offset + bytes[pos_payload++];\r\n\t\t\tdefault: return x - 1;\r\n\t\t}\r\n\t});\r\n}\t\r\n\r\n// returns an iterator which returns the next symbol\r\nfunction read_payload(v) {\r\n\tlet pos = 0;\r\n\treturn () => v[pos++];\r\n}\r\nfunction read_compressed_payload(s) {\r\n\treturn read_payload(decode_arithmetic(unsafe_atob(s)));\r\n}\r\n\r\n// unsafe in the sense:\r\n// expected well-formed Base64 w/o padding \r\n// 20220922: added for https://github.com/adraffy/ens-normalize.js/issues/4\r\nfunction unsafe_atob(s) {\r\n\tlet lookup = [];\r\n\t[...'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'].forEach((c, i) => lookup[c.charCodeAt(0)] = i);\r\n\tlet n = s.length;\r\n\tlet ret = new Uint8Array((6 * n) >> 3);\r\n\tfor (let i = 0, pos = 0, width = 0, carry = 0; i < n; i++) {\r\n\t\tcarry = (carry << 6) | lookup[s.charCodeAt(i)];\r\n\t\twidth += 6;\r\n\t\tif (width >= 8) {\r\n\t\t\tret[pos++] = (carry >> (width -= 8));\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// eg. [0,1,2,3...] => [0,-1,1,-2,...]\r\nfunction signed(i) { \r\n\treturn (i & 1) ? (~i >> 1) : (i >> 1);\r\n}\r\n\r\nfunction read_deltas(n, next) {\r\n\tlet v = Array(n);\r\n\tfor (let i = 0, x = 0; i < n; i++) v[i] = x += signed(next());\r\n\treturn v;\r\n}\r\n\r\n// [123][5] => [0 3] [1 1] [0 0]\r\nfunction read_sorted(next, prev = 0) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet x = next();\r\n\t\tlet n = next();\r\n\t\tif (!n) break;\r\n\t\tprev += x;\r\n\t\tfor (let i = 0; i < n; i++) {\r\n\t\t\tret.push(prev + i);\r\n\t\t}\r\n\t\tprev += n + 1;\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction read_sorted_arrays(next) {\r\n\treturn read_array_while(() => { \r\n\t\tlet v = read_sorted(next);\r\n\t\tif (v.length) return v;\r\n\t});\r\n}\r\n\r\n// returns map of x => ys\r\nfunction read_mapped(next) {\r\n\tlet ret = [];\r\n\twhile (true) {\r\n\t\tlet w = next();\r\n\t\tif (w == 0) break;\r\n\t\tret.push(read_linear_table(w, next));\r\n\t}\r\n\twhile (true) {\r\n\t\tlet w = next() - 1;\r\n\t\tif (w < 0) break;\r\n\t\tret.push(read_replacement_table(w, next));\r\n\t}\r\n\treturn ret.flat();\r\n}\r\n\r\n// read until next is falsy\r\n// return array of read values\r\nfunction read_array_while(next) {\r\n\tlet v = [];\r\n\twhile (true) {\r\n\t\tlet x = next(v.length);\r\n\t\tif (!x) break;\r\n\t\tv.push(x);\r\n\t}\r\n\treturn v;\r\n}\r\n\r\n// read w columns of length n\r\n// return as n rows of length w\r\nfunction read_transposed(n, w, next) {\r\n\tlet m = Array(n).fill().map(() => []);\r\n\tfor (let i = 0; i < w; i++) {\r\n\t\tread_deltas(n, next).forEach((x, j) => m[j].push(x));\r\n\t}\r\n\treturn m;\r\n}\r\n \r\n// returns [[x, ys], [x+dx, ys+dy], [x+2*dx, ys+2*dy], ...]\r\n// where dx/dy = steps, n = run size, w = length of y\r\nfunction read_linear_table(w, next) {\r\n\tlet dx = 1 + next();\r\n\tlet dy = next();\r\n\tlet vN = read_array_while(next);\r\n\tlet m = read_transposed(vN.length, 1+w, next);\r\n\treturn m.flatMap((v, i) => {\r\n\t\tlet [x, ...ys] = v;\r\n\t\treturn Array(vN[i]).fill().map((_, j) => {\r\n\t\t\tlet j_dy = j * dy;\r\n\t\t\treturn [x + j * dx, ys.map(y => y + j_dy)];\r\n\t\t});\r\n\t});\r\n}\r\n\r\n// return [[x, ys...], ...]\r\n// where w = length of y\r\nfunction read_replacement_table(w, next) { \r\n\tlet n = 1 + next();\r\n\tlet m = read_transposed(n, 1+w, next);\r\n\treturn m.map(v => [v[0], v.slice(1)]);\r\n}\r\n\r\n\r\nfunction read_trie(next) {\r\n\tlet ret = [];\r\n\tlet sorted = read_sorted(next); \r\n\texpand(decode([]), []);\r\n\treturn ret; // not sorted\r\n\tfunction decode(Q) { // characters that lead into this node\r\n\t\tlet S = next(); // state: valid, save, check\r\n\t\tlet B = read_array_while(() => { // buckets leading to new nodes\r\n\t\t\tlet cps = read_sorted(next).map(i => sorted[i]);\r\n\t\t\tif (cps.length) return decode(cps);\r\n\t\t});\r\n\t\treturn {S, B, Q};\r\n\t}\r\n\tfunction expand({S, B}, cps, saved) {\r\n\t\tif (S & 4 && saved === cps[cps.length-1]) return;\r\n\t\tif (S & 2) saved = cps[cps.length-1];\r\n\t\tif (S & 1) ret.push(cps); \r\n\t\tfor (let br of B) {\r\n\t\t\tfor (let cp of br.Q) {\r\n\t\t\t\texpand(br, [...cps, cp], saved);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\n\nfunction hex_cp(cp) {\r\n\treturn cp.toString(16).toUpperCase().padStart(2, '0');\r\n}\r\n\r\nfunction quote_cp(cp) {\r\n\treturn `{${hex_cp(cp)}}`; // raffy convention: like \"\\u{X}\" w/o the \"\\u\"\r\n}\r\n\r\n/*\r\nexport function explode_cp(s) {\r\n\treturn [...s].map(c => c.codePointAt(0));\r\n}\r\n*/\r\nfunction explode_cp(s) { // this is about 2x faster\r\n\tlet cps = [];\r\n\tfor (let pos = 0, len = s.length; pos < len; ) {\r\n\t\tlet cp = s.codePointAt(pos);\r\n\t\tpos += cp < 0x10000 ? 1 : 2;\r\n\t\tcps.push(cp);\r\n\t}\r\n\treturn cps;\r\n}\r\n\r\nfunction str_from_cps(cps) {\r\n\tconst chunk = 4096;\r\n\tlet len = cps.length;\r\n\tif (len < chunk) return String.fromCodePoint(...cps);\r\n\tlet buf = [];\r\n\tfor (let i = 0; i < len; ) {\r\n\t\tbuf.push(String.fromCodePoint(...cps.slice(i, i += chunk)));\r\n\t}\r\n\treturn buf.join('');\r\n}\r\n\r\nfunction compare_arrays(a, b) {\r\n\tlet n = a.length;\r\n\tlet c = n - b.length;\r\n\tfor (let i = 0; c == 0 && i < n; i++) c = a[i] - b[i];\r\n\treturn c;\r\n}\n\n// created 2023-09-25T01:01:55.148Z\n// compressed base64-encoded blob for include-nf data\n// source: https://github.com/adraffy/ens-normalize.js/blob/main/src/make.js\n// see: https://github.com/adraffy/ens-normalize.js#security\n// SHA-256: a974b6f8541fc29d919bc85118af0a44015851fab5343f8679cb31be2bdb209e\nvar COMPRESSED = '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';\n\n// https://unicode.org/reports/tr15/\r\n// for reference implementation\r\n// see: /derive/nf.js\r\n\r\n\r\n// algorithmic hangul\r\n// https://www.unicode.org/versions/Unicode15.0.0/ch03.pdf (page 144)\r\nconst S0 = 0xAC00;\r\nconst L0 = 0x1100;\r\nconst V0 = 0x1161;\r\nconst T0 = 0x11A7;\r\nconst L_COUNT = 19;\r\nconst V_COUNT = 21;\r\nconst T_COUNT = 28;\r\nconst N_COUNT = V_COUNT * T_COUNT;\r\nconst S_COUNT = L_COUNT * N_COUNT;\r\nconst S1 = S0 + S_COUNT;\r\nconst L1 = L0 + L_COUNT;\r\nconst V1 = V0 + V_COUNT;\r\nconst T1 = T0 + T_COUNT;\r\n\r\nfunction unpack_cc(packed) {\r\n\treturn (packed >> 24) & 0xFF;\r\n}\r\nfunction unpack_cp(packed) {\r\n\treturn packed & 0xFFFFFF;\r\n}\r\n\r\nlet SHIFTED_RANK, EXCLUSIONS, DECOMP, RECOMP;\r\n\r\nfunction init$1() {\r\n\t//console.time('nf');\r\n\tlet r = read_compressed_payload(COMPRESSED);\r\n\tSHIFTED_RANK = new Map(read_sorted_arrays(r).flatMap((v, i) => v.map(x => [x, (i+1) << 24]))); // pre-shifted\r\n\tEXCLUSIONS = new Set(read_sorted(r));\r\n\tDECOMP = new Map();\r\n\tRECOMP = new Map();\r\n\tfor (let [cp, cps] of read_mapped(r)) {\r\n\t\tif (!EXCLUSIONS.has(cp) && cps.length == 2) {\r\n\t\t\tlet [a, b] = cps;\r\n\t\t\tlet bucket = RECOMP.get(a);\r\n\t\t\tif (!bucket) {\r\n\t\t\t\tbucket = new Map();\r\n\t\t\t\tRECOMP.set(a, bucket);\r\n\t\t\t}\r\n\t\t\tbucket.set(b, cp);\r\n\t\t}\r\n\t\tDECOMP.set(cp, cps.reverse()); // stored reversed\r\n\t}\r\n\t//console.timeEnd('nf');\r\n\t// 20230905: 11ms\r\n}\r\n\r\nfunction is_hangul(cp) {\r\n\treturn cp >= S0 && cp < S1;\r\n}\r\n\r\nfunction compose_pair(a, b) {\r\n\tif (a >= L0 && a < L1 && b >= V0 && b < V1) {\r\n\t\treturn S0 + (a - L0) * N_COUNT + (b - V0) * T_COUNT;\r\n\t} else if (is_hangul(a) && b > T0 && b < T1 && (a - S0) % T_COUNT == 0) {\r\n\t\treturn a + (b - T0);\r\n\t} else {\r\n\t\tlet recomp = RECOMP.get(a);\r\n\t\tif (recomp) {\r\n\t\t\trecomp = recomp.get(b);\r\n\t\t\tif (recomp) {\r\n\t\t\t\treturn recomp;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn -1;\r\n\t}\r\n}\r\n\r\nfunction decomposed(cps) {\r\n\tif (!SHIFTED_RANK) init$1();\r\n\tlet ret = [];\r\n\tlet buf = [];\r\n\tlet check_order = false;\r\n\tfunction add(cp) {\r\n\t\tlet cc = SHIFTED_RANK.get(cp);\r\n\t\tif (cc) {\r\n\t\t\tcheck_order = true;\r\n\t\t\tcp |= cc;\r\n\t\t}\r\n\t\tret.push(cp);\r\n\t}\r\n\tfor (let cp of cps) {\r\n\t\twhile (true) {\r\n\t\t\tif (cp < 0x80) {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t} else if (is_hangul(cp)) {\r\n\t\t\t\tlet s_index = cp - S0;\r\n\t\t\t\tlet l_index = s_index / N_COUNT | 0;\r\n\t\t\t\tlet v_index = (s_index % N_COUNT) / T_COUNT | 0;\r\n\t\t\t\tlet t_index = s_index % T_COUNT;\r\n\t\t\t\tadd(L0 + l_index);\r\n\t\t\t\tadd(V0 + v_index);\r\n\t\t\t\tif (t_index > 0) add(T0 + t_index);\r\n\t\t\t} else {\r\n\t\t\t\tlet mapped = DECOMP.get(cp);\r\n\t\t\t\tif (mapped) {\r\n\t\t\t\t\tbuf.push(...mapped);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tadd(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!buf.length) break;\r\n\t\t\tcp = buf.pop();\r\n\t\t}\r\n\t}\r\n\tif (check_order && ret.length > 1) {\r\n\t\tlet prev_cc = unpack_cc(ret[0]);\r\n\t\tfor (let i = 1; i < ret.length; i++) {\r\n\t\t\tlet cc = unpack_cc(ret[i]);\r\n\t\t\tif (cc == 0 || prev_cc <= cc) {\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\t\t\tlet j = i-1;\r\n\t\t\twhile (true) {\r\n\t\t\t\tlet tmp = ret[j+1];\r\n\t\t\t\tret[j+1] = ret[j];\r\n\t\t\t\tret[j] = tmp;\r\n\t\t\t\tif (!j) break;\r\n\t\t\t\tprev_cc = unpack_cc(ret[--j]);\r\n\t\t\t\tif (prev_cc <= cc) break;\r\n\t\t\t}\r\n\t\t\tprev_cc = unpack_cc(ret[i]);\r\n\t\t}\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction composed_from_decomposed(v) {\r\n\tlet ret = [];\r\n\tlet stack = [];\r\n\tlet prev_cp = -1;\r\n\tlet prev_cc = 0;\r\n\tfor (let packed of v) {\r\n\t\tlet cc = unpack_cc(packed);\r\n\t\tlet cp = unpack_cp(packed);\r\n\t\tif (prev_cp == -1) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tret.push(cp);\r\n\t\t\t}\r\n\t\t} else if (prev_cc > 0 && prev_cc >= cc) {\r\n\t\t\tif (cc == 0) {\r\n\t\t\t\tret.push(prev_cp, ...stack);\r\n\t\t\t\tstack.length = 0;\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t}\r\n\t\t\tprev_cc = cc;\r\n\t\t} else {\r\n\t\t\tlet composed = compose_pair(prev_cp, cp);\r\n\t\t\tif (composed >= 0) {\r\n\t\t\t\tprev_cp = composed;\r\n\t\t\t} else if (prev_cc == 0 && cc == 0) {\r\n\t\t\t\tret.push(prev_cp);\r\n\t\t\t\tprev_cp = cp;\r\n\t\t\t} else {\r\n\t\t\t\tstack.push(cp);\r\n\t\t\t\tprev_cc = cc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (prev_cp >= 0) {\r\n\t\tret.push(prev_cp, ...stack);\t\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\n// note: cps can be iterable\r\nfunction nfd(cps) {\r\n\treturn decomposed(cps).map(unpack_cp);\r\n}\r\nfunction nfc(cps) {\r\n\treturn composed_from_decomposed(decomposed(cps));\r\n}\n\nconst HYPHEN = 0x2D;\r\nconst STOP = 0x2E;\r\nconst STOP_CH = '.';\r\nconst FE0F = 0xFE0F;\r\nconst UNIQUE_PH = 1;\r\n\r\n// 20230913: replace [...v] with Array_from(v) to avoid large spreads\r\nconst Array_from = x => Array.from(x); // Array.from.bind(Array);\r\n\r\nfunction group_has_cp(g, cp) {\r\n\t// 20230913: keep primary and secondary distinct instead of creating valid union\r\n\treturn g.P.has(cp) || g.Q.has(cp);\r\n}\r\n\r\nclass Emoji extends Array {\r\n\tget is_emoji() { return true; } // free tagging system\r\n}\r\n\r\nlet MAPPED, IGNORED, CM, NSM, ESCAPE, NFC_CHECK, GROUPS, WHOLE_VALID, WHOLE_MAP, VALID, EMOJI_LIST, EMOJI_ROOT;\r\n\r\nfunction init() {\r\n\tif (MAPPED) return;\r\n\t\r\n\tlet r = read_compressed_payload(COMPRESSED$1);\r\n\tconst read_sorted_array = () => read_sorted(r);\r\n\tconst read_sorted_set = () => new Set(read_sorted_array());\r\n\tconst set_add_many = (set, v) => v.forEach(x => set.add(x));\r\n\r\n\tMAPPED = new Map(read_mapped(r)); \r\n\tIGNORED = read_sorted_set(); // ignored characters are not valid, so just read raw codepoints\r\n\r\n\t/*\r\n\t// direct include from payload is smaller than the decompression code\r\n\tconst FENCED = new Map(read_array_while(() => {\r\n\t\tlet cp = r();\r\n\t\tif (cp) return [cp, read_str(r())];\r\n\t}));\r\n\t*/\r\n\t// 20230217: we still need all CM for proper error formatting\r\n\t// but norm only needs NSM subset that are potentially-valid\r\n\tCM = read_sorted_array();\r\n\tNSM = new Set(read_sorted_array().map(i => CM[i]));\r\n\tCM = new Set(CM);\r\n\t\r\n\tESCAPE = read_sorted_set(); // characters that should not be printed\r\n\tNFC_CHECK = read_sorted_set(); // only needed to illustrate ens_tokenize() transformations\r\n\r\n\tlet chunks = read_sorted_arrays(r);\r\n\tlet unrestricted = r();\r\n\t//const read_chunked = () => new Set(read_sorted_array().flatMap(i => chunks[i]).concat(read_sorted_array()));\r\n\tconst read_chunked = () => {\r\n\t\t// 20230921: build set in parts, 2x faster\r\n\t\tlet set = new Set();\r\n\t\tread_sorted_array().forEach(i => set_add_many(set, chunks[i]));\r\n\t\tset_add_many(set, read_sorted_array());\r\n\t\treturn set; \r\n\t};\r\n\tGROUPS = read_array_while(i => {\r\n\t\t// minifier property mangling seems unsafe\r\n\t\t// so these are manually renamed to single chars\r\n\t\tlet N = read_array_while(r).map(x => x+0x60);\r\n\t\tif (N.length) {\r\n\t\t\tlet R = i >= unrestricted; // unrestricted then restricted\r\n\t\t\tN[0] -= 32; // capitalize\r\n\t\t\tN = str_from_cps(N);\r\n\t\t\tif (R) N=`Restricted[${N}]`;\r\n\t\t\tlet P = read_chunked(); // primary\r\n\t\t\tlet Q = read_chunked(); // secondary\r\n\t\t\tlet M = !r(); // not-whitelisted, check for NSM\r\n\t\t\t// *** this code currently isn't needed ***\r\n\t\t\t/*\r\n\t\t\tlet V = [...P, ...Q].sort((a, b) => a-b); // derive: sorted valid\r\n\t\t\tlet M = r()-1; // number of combining mark\r\n\t\t\tif (M < 0) { // whitelisted\r\n\t\t\t\tM = new Map(read_array_while(() => {\r\n\t\t\t\t\tlet i = r();\r\n\t\t\t\t\tif (i) return [V[i-1], read_array_while(() => {\r\n\t\t\t\t\t\tlet v = read_array_while(r);\r\n\t\t\t\t\t\tif (v.length) return v.map(x => x-1);\r\n\t\t\t\t\t})];\r\n\t\t\t\t}));\r\n\t\t\t}*/\r\n\t\t\treturn {N, P, Q, M, R};\r\n\t\t}\r\n\t});\r\n\r\n\t// decode compressed wholes\r\n\tWHOLE_VALID = read_sorted_set();\r\n\tWHOLE_MAP = new Map();\r\n\tlet wholes = read_sorted_array().concat(Array_from(WHOLE_VALID)).sort((a, b) => a-b); // must be sorted\r\n\twholes.forEach((cp, i) => {\r\n\t\tlet d = r(); \r\n\t\tlet w = wholes[i] = d ? wholes[i-d] : {V: [], M: new Map()};\r\n\t\tw.V.push(cp); // add to member set\r\n\t\tif (!WHOLE_VALID.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, w);  // register with whole map\r\n\t\t}\r\n\t});\r\n\r\n\t// compute confusable-extent complements\r\n\t// usage: WHOLE_MAP.get(cp).M.get(cp) = complement set\r\n\tfor (let {V, M} of new Set(WHOLE_MAP.values())) {\r\n\t\t// connect all groups that have each whole character\r\n\t\tlet recs = [];\r\n\t\tfor (let cp of V) {\r\n\t\t\tlet gs = GROUPS.filter(g => group_has_cp(g, cp));\r\n\t\t\tlet rec = recs.find(({G}) => gs.some(g => G.has(g)));\r\n\t\t\tif (!rec) {\r\n\t\t\t\trec = {G: new Set(), V: []};\r\n\t\t\t\trecs.push(rec);\r\n\t\t\t}\r\n\t\t\trec.V.push(cp);\r\n\t\t\tset_add_many(rec.G, gs);\r\n\t\t}\r\n\t\t// per character cache groups which are not a member of the extent\r\n\t\tlet union = recs.flatMap(x => Array_from(x.G)); // all of the groups used by this whole\r\n\t\tfor (let {G, V} of recs) {\r\n\t\t\tlet complement = new Set(union.filter(g => !G.has(g))); // groups not covered by the extent\r\n\t\t\tfor (let cp of V) {\r\n\t\t\t\tM.set(cp, complement); // this is the same reference\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// compute valid set\r\n\t// 20230924: VALID was union but can be re-used\r\n\tVALID = new Set(); // exists in 1+ groups\r\n\tlet multi = new Set(); // exists in 2+ groups\r\n\tconst add_to_union = cp => VALID.has(cp) ? multi.add(cp) : VALID.add(cp);\r\n\tfor (let g of GROUPS) {\r\n\t\tfor (let cp of g.P) add_to_union(cp);\r\n\t\tfor (let cp of g.Q) add_to_union(cp);\r\n\t}\r\n\t// dual purpose WHOLE_MAP: return placeholder if unique non-confusable\r\n\tfor (let cp of VALID) {\r\n\t\tif (!WHOLE_MAP.has(cp) && !multi.has(cp)) {\r\n\t\t\tWHOLE_MAP.set(cp, UNIQUE_PH);\r\n\t\t}\r\n\t}\r\n\t// add all decomposed parts\r\n\t// see derive: \"Valid is Closed (via Brute-force)\"\r\n\tset_add_many(VALID, nfd(VALID));\r\n\t\r\n\t// decode emoji\r\n\t// 20230719: emoji are now fully-expanded to avoid quirk logic \r\n\tEMOJI_LIST = read_trie(r).map(v => Emoji.from(v)).sort(compare_arrays);\r\n\tEMOJI_ROOT = new Map(); // this has approx 7K nodes (2+ per emoji)\r\n\tfor (let cps of EMOJI_LIST) {\r\n\t\t// 20230719: change to *slightly* stricter algorithm which disallows \r\n\t\t// insertion of misplaced FE0F in emoji sequences (matching ENSIP-15)\r\n\t\t// example: beautified [A B] (eg. flag emoji) \r\n\t\t//  before: allow: [A FE0F B], error: [A FE0F FE0F B] \r\n\t\t//   after: error: both\r\n\t\t// note: this code now matches ENSNormalize.{cs,java} logic\r\n\t\tlet prev = [EMOJI_ROOT];\r\n\t\tfor (let cp of cps) {\r\n\t\t\tlet next = prev.map(node => {\r\n\t\t\t\tlet child = node.get(cp);\r\n\t\t\t\tif (!child) {\r\n\t\t\t\t\t// should this be object? \r\n\t\t\t\t\t// (most have 1-2 items, few have many)\r\n\t\t\t\t\t// 20230719: no, v8 default map is 4?\r\n\t\t\t\t\tchild = new Map();\r\n\t\t\t\t\tnode.set(cp, child);\r\n\t\t\t\t}\r\n\t\t\t\treturn child;\r\n\t\t\t});\r\n\t\t\tif (cp === FE0F) {\r\n\t\t\t\tprev.push(...next); // less than 20 elements\r\n\t\t\t} else {\r\n\t\t\t\tprev = next;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor (let x of prev) {\r\n\t\t\tx.V = cps;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// if escaped: {HEX}\r\n//       else: \"x\" {HEX}\r\nfunction quoted_cp(cp) {\r\n\treturn (should_escape(cp) ? '' : `${bidi_qq(safe_str_from_cps([cp]))} `) + quote_cp(cp);\r\n}\r\n\r\n// 20230211: some messages can be mixed-directional and result in spillover\r\n// use 200E after a quoted string to force the remainder of a string from \r\n// acquring the direction of the quote\r\n// https://www.w3.org/International/questions/qa-bidi-unicode-controls#exceptions\r\nfunction bidi_qq(s) {\r\n\treturn `\"${s}\"\\u200E`; // strong LTR\r\n}\r\n\r\nfunction check_label_extension(cps) {\r\n\tif (cps.length >= 4 && cps[2] == HYPHEN && cps[3] == HYPHEN) {\r\n\t\tthrow new Error(`invalid label extension: \"${str_from_cps(cps.slice(0, 4))}\"`); // this can only be ascii so cant be bidi\r\n\t}\r\n}\r\nfunction check_leading_underscore(cps) {\r\n\tconst UNDERSCORE = 0x5F;\r\n\tfor (let i = cps.lastIndexOf(UNDERSCORE); i > 0; ) {\r\n\t\tif (cps[--i] !== UNDERSCORE) {\r\n\t\t\tthrow new Error('underscore allowed only at start');\r\n\t\t}\r\n\t}\r\n}\r\n// check that a fenced cp is not leading, trailing, or touching another fenced cp\r\nfunction check_fenced(cps) {\r\n\tlet cp = cps[0];\r\n\tlet prev = FENCED.get(cp);\r\n\tif (prev) throw error_placement(`leading ${prev}`);\r\n\tlet n = cps.length;\r\n\tlet last = -1; // prevents trailing from throwing\r\n\tfor (let i = 1; i < n; i++) {\r\n\t\tcp = cps[i];\r\n\t\tlet match = FENCED.get(cp);\r\n\t\tif (match) {\r\n\t\t\t// since cps[0] isn't fenced, cps[1] cannot throw\r\n\t\t\tif (last == i) throw error_placement(`${prev} + ${match}`);\r\n\t\t\tlast = i + 1;\r\n\t\t\tprev = match;\r\n\t\t}\r\n\t}\r\n\tif (last == n) throw error_placement(`trailing ${prev}`);\r\n}\r\n\r\n// create a safe to print string \r\n// invisibles are escaped\r\n// leading cm uses placeholder\r\n// if cps exceed max, middle truncate with ellipsis\r\n// quoter(cp) => string, eg. 3000 => \"{3000}\"\r\n// note: in html, you'd call this function then replace [<>&] with entities\r\nfunction safe_str_from_cps(cps, max = Infinity, quoter = quote_cp) {\r\n\t//if (Number.isInteger(cps)) cps = [cps];\r\n\t//if (!Array.isArray(cps)) throw new TypeError(`expected codepoints`);\r\n\tlet buf = [];\r\n\tif (is_combining_mark(cps[0])) buf.push('◌');\r\n\tif (cps.length > max) {\r\n\t\tmax >>= 1;\r\n\t\tcps = [...cps.slice(0, max), 0x2026, ...cps.slice(-max)];\r\n\t}\r\n\tlet prev = 0;\r\n\tlet n = cps.length;\r\n\tfor (let i = 0; i < n; i++) {\r\n\t\tlet cp = cps[i];\r\n\t\tif (should_escape(cp)) {\r\n\t\t\tbuf.push(str_from_cps(cps.slice(prev, i)));\r\n\t\t\tbuf.push(quoter(cp));\r\n\t\t\tprev = i + 1;\r\n\t\t}\r\n\t}\r\n\tbuf.push(str_from_cps(cps.slice(prev, n)));\r\n\treturn buf.join('');\r\n}\r\n\r\n// note: set(s) cannot be exposed because they can be modified\r\n// note: Object.freeze() doesn't work\r\nfunction is_combining_mark(cp) {\r\n\tinit();\r\n\treturn CM.has(cp);\r\n}\r\nfunction should_escape(cp) {\r\n\tinit();\r\n\treturn ESCAPE.has(cp);\r\n}\r\n\r\n// return all supported emoji as fully-qualified emoji \r\n// ordered by length then lexicographic \r\nfunction ens_emoji() {\r\n\tinit();\r\n\treturn EMOJI_LIST.map(x => x.slice()); // emoji are exposed so copy\r\n}\r\n\r\nfunction ens_normalize_fragment(frag, decompose) {\r\n\tinit();\r\n\tlet nf = decompose ? nfd : nfc;\r\n\treturn frag.split(STOP_CH).map(label => str_from_cps(tokens_from_str(explode_cp(label), nf, filter_fe0f).flat())).join(STOP_CH);\r\n}\r\n\r\nfunction ens_normalize(name) {\r\n\treturn flatten(split(name, nfc, filter_fe0f));\r\n}\r\n\r\nfunction ens_beautify(name) {\r\n\tlet labels = split(name, nfc, x => x); // emoji not exposed\r\n\tfor (let {type, output, error} of labels) {\r\n\t\tif (error) break; // flatten will throw\r\n\r\n\t\t// replace leading/trailing hyphen\r\n\t\t// 20230121: consider beautifing all or leading/trailing hyphen to unicode variant\r\n\t\t// not exactly the same in every font, but very similar: \"-\" vs \"‐\"\r\n\t\t/*\r\n\t\tconst UNICODE_HYPHEN = 0x2010;\r\n\t\t// maybe this should replace all for visual consistancy?\r\n\t\t// `node tools/reg-count.js regex ^-\\{2,\\}` => 592\r\n\t\t//for (let i = 0; i < output.length; i++) if (output[i] == 0x2D) output[i] = 0x2010;\r\n\t\tif (output[0] == HYPHEN) output[0] = UNICODE_HYPHEN;\r\n\t\tlet end = output.length-1;\r\n\t\tif (output[end] == HYPHEN) output[end] = UNICODE_HYPHEN;\r\n\t\t*/\r\n\t\t// 20230123: WHATWG URL uses \"CheckHyphens\" false\r\n\t\t// https://url.spec.whatwg.org/#idna\r\n\r\n\t\t// update ethereum symbol\r\n\t\t// ξ => Ξ if not greek\r\n\t\tif (type !== 'Greek') array_replace(output, 0x3BE, 0x39E);\r\n\r\n\t\t// 20221213: fixes bidi subdomain issue, but breaks invariant (200E is disallowed)\r\n\t\t// could be fixed with special case for: 2D (.) + 200E (LTR)\r\n\t\t// https://discuss.ens.domains/t/bidi-label-ordering-spoof/15824\r\n\t\t//output.splice(0, 0, 0x200E);\r\n\t}\r\n\treturn flatten(labels);\r\n}\r\n\r\nfunction array_replace(v, a, b) {\r\n\tlet prev = 0;\r\n\twhile (true) {\r\n\t\tlet next = v.indexOf(a, prev);\r\n\t\tif (next < 0) break;\r\n\t\tv[next] = b; \r\n\t\tprev = next + 1;\r\n\t}\r\n}\r\n\r\nfunction ens_split(name, preserve_emoji) {\r\n\treturn split(name, nfc, preserve_emoji ? x => x.slice() : filter_fe0f); // emoji are exposed so copy\r\n}\r\n\r\nfunction split(name, nf, ef) {\r\n\tif (!name) return []; // 20230719: empty name allowance\r\n\tinit();\r\n\tlet offset = 0;\r\n\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t// 4.) \"The label must not contain a U+002E ( . ) FULL STOP.\"\r\n\treturn name.split(STOP_CH).map(label => {\r\n\t\tlet input = explode_cp(label);\r\n\t\tlet info = {\r\n\t\t\tinput,\r\n\t\t\toffset, // codepoint, not substring!\r\n\t\t};\r\n\t\toffset += input.length + 1; // + stop\r\n\t\ttry {\r\n\t\t\t// 1.) \"The label must be in Unicode Normalization Form NFC\"\r\n\t\t\tlet tokens = info.tokens = tokens_from_str(input, nf, ef);\r\n\t\t\tlet token_count = tokens.length;\r\n\t\t\tlet type;\r\n\t\t\tif (!token_count) { // the label was effectively empty (could of had ignored characters)\r\n\t\t\t\t//norm = [];\r\n\t\t\t\t//type = 'None'; // use this instead of next match, \"ASCII\"\r\n\t\t\t\t// 20230120: change to strict\r\n\t\t\t\t// https://discuss.ens.domains/t/ens-name-normalization-2nd/14564/59\r\n\t\t\t\tthrow new Error(`empty label`);\r\n\t\t\t} \r\n\t\t\tlet norm = info.output = tokens.flat();\r\n\t\t\tcheck_leading_underscore(norm);\r\n\t\t\tlet emoji = info.emoji = token_count > 1 || tokens[0].is_emoji; // same as: tokens.some(x => x.is_emoji);\r\n\t\t\tif (!emoji && norm.every(cp => cp < 0x80)) { // special case for ascii\r\n\t\t\t\t// 20230123: matches matches WHATWG, see note 3.3\r\n\t\t\t\tcheck_label_extension(norm); // only needed for ascii\r\n\t\t\t\t// cant have fenced\r\n\t\t\t\t// cant have cm\r\n\t\t\t\t// cant have wholes\r\n\t\t\t\t// see derive: \"Fastpath ASCII\"\r\n\t\t\t\ttype = 'ASCII';\r\n\t\t\t} else {\r\n\t\t\t\tlet chars = tokens.flatMap(x => x.is_emoji ? [] : x); // all of the nfc tokens concat together\r\n\t\t\t\tif (!chars.length) { // theres no text, just emoji\r\n\t\t\t\t\ttype = 'Emoji';\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 5.) \"The label must not begin with a combining mark, that is: General_Category=Mark.\"\r\n\t\t\t\t\tif (CM.has(norm[0])) throw error_placement('leading combining mark');\r\n\t\t\t\t\tfor (let i = 1; i < token_count; i++) { // we've already checked the first token\r\n\t\t\t\t\t\tlet cps = tokens[i];\r\n\t\t\t\t\t\tif (!cps.is_emoji && CM.has(cps[0])) { // every text token has emoji neighbors, eg. EtEEEtEt...\r\n\t\t\t\t\t\t\t// bidi_qq() not needed since emoji is LTR and cps is a CM\r\n\t\t\t\t\t\t\tthrow error_placement(`emoji + combining mark: \"${str_from_cps(tokens[i-1])} + ${safe_str_from_cps([cps[0]])}\"`); \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcheck_fenced(norm);\r\n\t\t\t\t\tlet unique = Array_from(new Set(chars));\r\n\t\t\t\t\tlet [g] = determine_group(unique); // take the first match\r\n\t\t\t\t\t// see derive: \"Matching Groups have Same CM Style\"\r\n\t\t\t\t\t// alternative: could form a hybrid type: Latin/Japanese/...\t\r\n\t\t\t\t\tcheck_group(g, chars); // need text in order\r\n\t\t\t\t\tcheck_whole(g, unique); // only need unique text (order would be required for multiple-char confusables)\r\n\t\t\t\t\ttype = g.N;\r\n\t\t\t\t\t// 20230121: consider exposing restricted flag\r\n\t\t\t\t\t// it's simpler to just check for 'Restricted'\r\n\t\t\t\t\t// or even better: type.endsWith(']')\r\n\t\t\t\t\t//if (g.R) info.restricted = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinfo.type = type;\r\n\t\t} catch (err) {\r\n\t\t\tinfo.error = err; // use full error object\r\n\t\t}\r\n\t\treturn info;\r\n\t});\r\n}\r\n\r\nfunction check_whole(group, unique) {\r\n\tlet maker;\r\n\tlet shared = [];\r\n\tfor (let cp of unique) {\r\n\t\tlet whole = WHOLE_MAP.get(cp);\r\n\t\tif (whole === UNIQUE_PH) return; // unique, non-confusable\r\n\t\tif (whole) {\r\n\t\t\tlet set = whole.M.get(cp); // groups which have a character that look-like this character\r\n\t\t\tmaker = maker ? maker.filter(g => set.has(g)) : Array_from(set);\r\n\t\t\tif (!maker.length) return; // confusable intersection is empty\r\n\t\t} else {\r\n\t\t\tshared.push(cp); \r\n\t\t}\r\n\t}\r\n\tif (maker) {\r\n\t\t// we have 1+ confusable\r\n\t\t// check if any of the remaining groups\r\n\t\t// contain the shared characters too\r\n\t\tfor (let g of maker) {\r\n\t\t\tif (shared.every(cp => group_has_cp(g, cp))) {\r\n\t\t\t\tthrow new Error(`whole-script confusable: ${group.N}/${g.N}`);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// assumption: unique.size > 0\r\n// returns list of matching groups\r\nfunction determine_group(unique) {\r\n\tlet groups = GROUPS;\r\n\tfor (let cp of unique) {\r\n\t\t// note: we need to dodge CM that are whitelisted\r\n\t\t// but that code isn't currently necessary\r\n\t\tlet gs = groups.filter(g => group_has_cp(g, cp));\r\n\t\tif (!gs.length) {\r\n\t\t\tif (!GROUPS.some(g => group_has_cp(g, cp))) { \r\n\t\t\t\t// the character was composed of valid parts\r\n\t\t\t\t// but it's NFC form is invalid\r\n\t\t\t\t// 20230716: change to more exact statement, see: ENSNormalize.{cs,java}\r\n\t\t\t\t// note: this doesn't have to be a composition\r\n\t\t\t\t// 20230720: change to full check\r\n\t\t\t\tthrow error_disallowed(cp); // this should be rare\r\n\t\t\t} else {\r\n\t\t\t\t// there is no group that contains all these characters\r\n\t\t\t\t// throw using the highest priority group that matched\r\n\t\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\t\tthrow error_group_member(groups[0], cp);\r\n\t\t\t}\r\n\t\t}\r\n\t\tgroups = gs;\r\n\t\tif (gs.length == 1) break; // there is only one group left\r\n\t}\r\n\t// there are at least 1 group(s) with all of these characters\r\n\treturn groups;\r\n}\r\n\r\n// throw on first error\r\nfunction flatten(split) {\r\n\treturn split.map(({input, error, output}) => {\r\n\t\tif (error) {\r\n\t\t\t// don't print label again if just a single label\r\n\t\t\tlet msg = error.message;\r\n\t\t\t// bidi_qq() only necessary if msg is digits\r\n\t\t\tthrow new Error(split.length == 1 ? msg : `Invalid label ${bidi_qq(safe_str_from_cps(input, 63))}: ${msg}`); \r\n\t\t}\r\n\t\treturn str_from_cps(output);\r\n\t}).join(STOP_CH);\r\n}\r\n\r\nfunction error_disallowed(cp) {\r\n\t// TODO: add cp to error?\r\n\treturn new Error(`disallowed character: ${quoted_cp(cp)}`); \r\n}\r\nfunction error_group_member(g, cp) {\r\n\tlet quoted = quoted_cp(cp);\r\n\tlet gg = GROUPS.find(g => g.P.has(cp)); // only check primary\r\n\tif (gg) {\r\n\t\tquoted = `${gg.N} ${quoted}`;\r\n\t}\r\n\treturn new Error(`illegal mixture: ${g.N} + ${quoted}`);\r\n}\r\nfunction error_placement(where) {\r\n\treturn new Error(`illegal placement: ${where}`);\r\n}\r\n\r\n// assumption: cps.length > 0\r\n// assumption: cps[0] isn't a CM\r\n// assumption: the previous character isn't an emoji\r\nfunction check_group(g, cps) {\r\n\tfor (let cp of cps) {\r\n\t\tif (!group_has_cp(g, cp)) {\r\n\t\t\t// for whitelisted scripts, this will throw illegal mixture on invalid cm, eg. \"e{300}{300}\"\r\n\t\t\t// at the moment, it's unnecessary to introduce an extra error type\r\n\t\t\t// until there exists a whitelisted multi-character\r\n\t\t\t//   eg. if (M < 0 && is_combining_mark(cp)) { ... }\r\n\t\t\t// there are 3 cases:\r\n\t\t\t//   1. illegal cm for wrong group => mixture error\r\n\t\t\t//   2. illegal cm for same group => cm error\r\n\t\t\t//       requires set of whitelist cm per group: \r\n\t\t\t//        eg. new Set([...g.P, ...g.Q].flatMap(nfc).filter(cp => CM.has(cp)))\r\n\t\t\t//   3. wrong group => mixture error\r\n\t\t\tthrow error_group_member(g, cp);\r\n\t\t}\r\n\t}\r\n\t//if (M >= 0) { // we have a known fixed cm count\r\n\tif (g.M) { // we need to check for NSM\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // see: assumption\r\n\t\t\t// 20230210: bugfix: using cps instead of decomposed h/t Carbon225\r\n\t\t\t/*\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: ${g.N} ${bidi_qq(str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t\t*/\r\n\t\t\t// 20230217: switch to NSM counting\r\n\t\t\t// https://www.unicode.org/reports/tr39/#Optional_Detection\r\n\t\t\tif (NSM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\tfor (let cp; j < e && NSM.has(cp = decomposed[j]); j++) {\r\n\t\t\t\t\t// a. Forbid sequences of the same nonspacing mark.\r\n\t\t\t\t\tfor (let k = i; k < j; k++) { // O(n^2) but n < 100\r\n\t\t\t\t\t\tif (decomposed[k] == cp) {\r\n\t\t\t\t\t\t\tthrow new Error(`duplicate non-spacing marks: ${quoted_cp(cp)}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// parse to end so we have full nsm count\r\n\t\t\t\t// b. Forbid sequences of more than 4 nonspacing marks (gc=Mn or gc=Me).\r\n\t\t\t\tif (j - i > NSM_MAX) {\r\n\t\t\t\t\t// note: this slice starts with a base char or spacing-mark cm\r\n\t\t\t\t\tthrow new Error(`excessive non-spacing marks: ${bidi_qq(safe_str_from_cps(decomposed.slice(i-1, j)))} (${j-i}/${NSM_MAX})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t// *** this code currently isn't needed ***\r\n\t/*\r\n\tlet cm_whitelist = M instanceof Map;\r\n\tfor (let i = 0, e = cps.length; i < e; ) {\r\n\t\tlet cp = cps[i++];\r\n\t\tlet seqs = cm_whitelist && M.get(cp);\r\n\t\tif (seqs) { \r\n\t\t\t// list of codepoints that can follow\r\n\t\t\t// if this exists, this will always be 1+\r\n\t\t\tlet j = i;\r\n\t\t\twhile (j < e && CM.has(cps[j])) j++;\r\n\t\t\tlet cms = cps.slice(i, j);\r\n\t\t\tlet match = seqs.find(seq => !compare_arrays(seq, cms));\r\n\t\t\tif (!match) throw new Error(`disallowed combining mark sequence: \"${safe_str_from_cps([cp, ...cms])}\"`);\r\n\t\t\ti = j;\r\n\t\t} else if (!V.has(cp)) {\r\n\t\t\t// https://www.unicode.org/reports/tr39/#mixed_script_confusables\r\n\t\t\tlet quoted = quoted_cp(cp);\r\n\t\t\tfor (let cp of cps) {\r\n\t\t\t\tlet u = UNIQUE.get(cp);\r\n\t\t\t\tif (u && u !== g) {\r\n\t\t\t\t\t// if both scripts are restricted this error is confusing\r\n\t\t\t\t\t// because we don't differentiate RestrictedA from RestrictedB \r\n\t\t\t\t\tif (!u.R) quoted = `${quoted} is ${u.N}`;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthrow new Error(`disallowed ${g.N} character: ${quoted}`);\r\n\t\t\t//throw new Error(`disallowed character: ${quoted} (expected ${g.N})`);\r\n\t\t\t//throw new Error(`${g.N} does not allow: ${quoted}`);\r\n\t\t}\r\n\t}\r\n\tif (!cm_whitelist) {\r\n\t\tlet decomposed = nfd(cps);\r\n\t\tfor (let i = 1, e = decomposed.length; i < e; i++) { // we know it can't be cm leading\r\n\t\t\tif (CM.has(decomposed[i])) {\r\n\t\t\t\tlet j = i + 1;\r\n\t\t\t\twhile (j < e && CM.has(decomposed[j])) j++;\r\n\t\t\t\tif (j - i > M) {\r\n\t\t\t\t\tthrow new Error(`too many combining marks: \"${str_from_cps(decomposed.slice(i-1, j))}\" (${j-i}/${M})`);\r\n\t\t\t\t}\r\n\t\t\t\ti = j;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t*/\r\n}\r\n\r\n// given a list of codepoints\r\n// returns a list of lists, where emoji are a fully-qualified (as Array subclass)\r\n// eg. explode_cp(\"abc💩d\") => [[61, 62, 63], Emoji[1F4A9, FE0F], [64]]\r\n// 20230818: rename for 'process' name collision h/t Javarome\r\n// https://github.com/adraffy/ens-normalize.js/issues/23\r\nfunction tokens_from_str(input, nf, ef) {\r\n\tlet ret = [];\r\n\tlet chars = [];\r\n\tinput = input.slice().reverse(); // flip so we can pop\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input);\r\n\t\tif (emoji) {\r\n\t\t\tif (chars.length) {\r\n\t\t\t\tret.push(nf(chars));\r\n\t\t\t\tchars = [];\r\n\t\t\t}\r\n\t\t\tret.push(ef(emoji));\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (VALID.has(cp)) {\r\n\t\t\t\tchars.push(cp);\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\tchars.push(...cps); // less than 10 elements\r\n\t\t\t\t} else if (!IGNORED.has(cp)) {\r\n\t\t\t\t\t// 20230912: unicode 15.1 changed the order of processing such that\r\n\t\t\t\t\t// disallowed parts are only rejected after NFC\r\n\t\t\t\t\t// https://unicode.org/reports/tr46/#Validity_Criteria\r\n\t\t\t\t\t// this doesn't impact normalization as of today\r\n\t\t\t\t\t// technically, this error can be removed as the group logic will apply similar logic\r\n\t\t\t\t\t// however the error type might be less clear\r\n\t\t\t\t\tthrow error_disallowed(cp);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (chars.length) {\r\n\t\tret.push(nf(chars));\r\n\t}\r\n\treturn ret;\r\n}\r\n\r\nfunction filter_fe0f(cps) {\r\n\treturn cps.filter(cp => cp != FE0F);\r\n}\r\n\r\n// given array of codepoints\r\n// returns the longest valid emoji sequence (or undefined if no match)\r\n// *MUTATES* the supplied array\r\n// disallows interleaved ignored characters\r\n// fills (optional) eaten array with matched codepoints\r\nfunction consume_emoji_reversed(cps, eaten) {\r\n\tlet node = EMOJI_ROOT;\r\n\tlet emoji;\r\n\tlet pos = cps.length;\r\n\twhile (pos) {\r\n\t\tnode = node.get(cps[--pos]);\r\n\t\tif (!node) break;\r\n\t\tlet {V} = node;\r\n\t\tif (V) { // this is a valid emoji (so far)\r\n\t\t\temoji = V;\r\n\t\t\tif (eaten) eaten.push(...cps.slice(pos).reverse()); // (optional) copy input, used for ens_tokenize()\r\n\t\t\tcps.length = pos; // truncate\r\n\t\t}\r\n\t}\r\n\treturn emoji;\r\n}\r\n\r\n// ************************************************************\r\n// tokenizer \r\n\r\nconst TY_VALID = 'valid';\r\nconst TY_MAPPED = 'mapped';\r\nconst TY_IGNORED = 'ignored';\r\nconst TY_DISALLOWED = 'disallowed';\r\nconst TY_EMOJI = 'emoji';\r\nconst TY_NFC = 'nfc';\r\nconst TY_STOP = 'stop';\r\n\r\nfunction ens_tokenize(name, {\r\n\tnf = true, // collapse unnormalized runs into a single token\r\n} = {}) {\r\n\tinit();\r\n\tlet input = explode_cp(name).reverse();\r\n\tlet eaten = [];\r\n\tlet tokens = [];\r\n\twhile (input.length) {\r\n\t\tlet emoji = consume_emoji_reversed(input, eaten);\r\n\t\tif (emoji) {\r\n\t\t\ttokens.push({\r\n\t\t\t\ttype: TY_EMOJI,\r\n\t\t\t\temoji: emoji.slice(), // copy emoji\r\n\t\t\t\tinput: eaten,\r\n\t\t\t\tcps: filter_fe0f(emoji)\r\n\t\t\t});\r\n\t\t\teaten = []; // reset buffer\r\n\t\t} else {\r\n\t\t\tlet cp = input.pop();\r\n\t\t\tif (cp == STOP) {\r\n\t\t\t\ttokens.push({type: TY_STOP, cp});\r\n\t\t\t} else if (VALID.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_VALID, cps: [cp]});\r\n\t\t\t} else if (IGNORED.has(cp)) {\r\n\t\t\t\ttokens.push({type: TY_IGNORED, cp});\r\n\t\t\t} else {\r\n\t\t\t\tlet cps = MAPPED.get(cp);\r\n\t\t\t\tif (cps) {\r\n\t\t\t\t\ttokens.push({type: TY_MAPPED, cp, cps: cps.slice()});\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttokens.push({type: TY_DISALLOWED, cp});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tif (nf) {\r\n\t\tfor (let i = 0, start = -1; i < tokens.length; i++) {\r\n\t\t\tlet token = tokens[i];\r\n\t\t\tif (is_valid_or_mapped(token.type)) {\r\n\t\t\t\tif (requires_check(token.cps)) { // normalization might be needed\r\n\t\t\t\t\tlet end = i + 1;\r\n\t\t\t\t\tfor (let pos = end; pos < tokens.length; pos++) { // find adjacent text\r\n\t\t\t\t\t\tlet {type, cps} = tokens[pos];\r\n\t\t\t\t\t\tif (is_valid_or_mapped(type)) {\r\n\t\t\t\t\t\t\tif (!requires_check(cps)) break;\r\n\t\t\t\t\t\t\tend = pos + 1;\r\n\t\t\t\t\t\t} else if (type !== TY_IGNORED) { // || type !== TY_DISALLOWED) { \r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (start < 0) start = i;\r\n\t\t\t\t\tlet slice = tokens.slice(start, end);\r\n\t\t\t\t\tlet cps0 = slice.flatMap(x => is_valid_or_mapped(x.type) ? x.cps : []); // strip junk tokens\r\n\t\t\t\t\tlet cps = nfc(cps0);\r\n\t\t\t\t\tif (compare_arrays(cps, cps0)) { // bundle into an nfc token\r\n\t\t\t\t\t\ttokens.splice(start, end - start, {\r\n\t\t\t\t\t\t\ttype: TY_NFC, \r\n\t\t\t\t\t\t\tinput: cps0, // there are 3 states: tokens0 ==(process)=> input ==(nfc)=> tokens/cps\r\n\t\t\t\t\t\t\tcps, \r\n\t\t\t\t\t\t\ttokens0: collapse_valid_tokens(slice),\r\n\t\t\t\t\t\t\ttokens: ens_tokenize(str_from_cps(cps), {nf: false})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\ti = start;\r\n\t\t\t\t\t} else { \r\n\t\t\t\t\t\ti = end - 1; // skip to end of slice\r\n\t\t\t\t\t}\r\n\t\t\t\t\tstart = -1; // reset\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstart = i; // remember last\r\n\t\t\t\t}\r\n\t\t\t} else if (token.type !== TY_IGNORED) { // 20221024: is this correct?\r\n\t\t\t\tstart = -1; // reset\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn collapse_valid_tokens(tokens);\r\n}\r\n\r\nfunction is_valid_or_mapped(type) {\r\n\treturn type == TY_VALID || type == TY_MAPPED;\r\n}\r\n\r\nfunction requires_check(cps) {\r\n\treturn cps.some(cp => NFC_CHECK.has(cp));\r\n}\r\n\r\nfunction collapse_valid_tokens(tokens) {\r\n\tfor (let i = 0; i < tokens.length; i++) {\r\n\t\tif (tokens[i].type == TY_VALID) {\r\n\t\t\tlet j = i + 1;\r\n\t\t\twhile (j < tokens.length && tokens[j].type == TY_VALID) j++;\r\n\t\t\ttokens.splice(i, j - i, {type: TY_VALID, cps: tokens.slice(i, j).flatMap(x => x.cps)});\r\n\t\t}\r\n\t}\r\n\treturn tokens;\r\n}\n\nexport { ens_beautify, ens_emoji, ens_normalize, ens_normalize_fragment, ens_split, ens_tokenize, is_combining_mark, nfc, nfd, safe_str_from_cps, should_escape };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,YAAY,GAAG,8mmBAA8mmB;AACjomB,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAC,YAAY,CAAC,EAAC,CAAC,IAAI,EAAC,gBAAgB,CAAC,EAAC,CAAC,KAAK,EAAC,YAAY,CAAC,CAAC,CAAC;AAC1F,MAAMC,OAAO,GAAG,CAAC;AAEjB,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACjC,IAAIC,GAAG,GAAG,CAAC;EACX,SAASC,GAAGA,CAAA,EAAG;IAAE,OAAQF,KAAK,CAACC,GAAG,EAAE,CAAC,IAAI,CAAC,GAAID,KAAK,CAACC,GAAG,EAAE,CAAC;EAAE;;EAE5D;EACA,IAAIE,YAAY,GAAGD,GAAG,CAAC,CAAC;EACxB,IAAIE,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,YAAY,EAAEG,CAAC,EAAE,EAAE;IACtCD,GAAG,CAACE,IAAI,CAACH,KAAK,IAAIF,GAAG,CAAC,CAAC,CAAC;EACzB;;EAEA;EACA,IAAIM,IAAI,GAAGN,GAAG,CAAC,CAAC;EAChB,IAAIO,WAAW,GAAGR,GAAG;EACrBA,GAAG,IAAIO,IAAI;EAEX,IAAIE,UAAU,GAAG,CAAC;EAClB,IAAIC,WAAW,GAAG,CAAC;EACnB,SAASC,QAAQA,CAAA,EAAG;IACnB,IAAIF,UAAU,IAAI,CAAC,EAAE;MACpB;MACA;MACAC,WAAW,GAAIA,WAAW,IAAI,CAAC,GAAIX,KAAK,CAACC,GAAG,EAAE,CAAC;MAC/CS,UAAU,GAAG,CAAC;IACf;IACA,OAAQC,WAAW,IAAI,EAAED,UAAU,GAAI,CAAC;EACzC;EAEA,MAAMG,CAAC,GAAG,EAAE;EACZ,MAAMC,IAAI,GAAG,CAAC,IAAED,CAAC;EACjB,MAAME,IAAI,GAAGD,IAAI,KAAK,CAAC;EACvB,MAAME,IAAI,GAAGD,IAAI,IAAI,CAAC;EACtB,MAAME,IAAI,GAAGH,IAAI,GAAG,CAAC;;EAErB;EACA,IAAII,QAAQ,GAAG,CAAC;EAChB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,CAAC,EAAEP,CAAC,EAAE,EAAEY,QAAQ,GAAIA,QAAQ,IAAI,CAAC,GAAIN,QAAQ,CAAC,CAAC;EAEnE,IAAIO,OAAO,GAAG,EAAE;EAChB,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,KAAK,GAAGP,IAAI,CAAC,CAAC;EAClB,OAAO,IAAI,EAAE;IACZ,IAAIQ,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,CAAE,CAACN,QAAQ,GAAGE,GAAG,GAAG,CAAC,IAAIhB,KAAK,GAAI,CAAC,IAAIiB,KAAK,CAAC;IACpE,IAAII,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAGvB,YAAY;IACtB,OAAOuB,GAAG,GAAGD,KAAK,GAAG,CAAC,EAAE;MAAE;MACzB,IAAIE,GAAG,GAAIF,KAAK,GAAGC,GAAG,KAAM,CAAC;MAC7B,IAAIJ,KAAK,GAAGjB,GAAG,CAACsB,GAAG,CAAC,EAAE;QACrBD,GAAG,GAAGC,GAAG;MACV,CAAC,MAAM;QACNF,KAAK,GAAGE,GAAG;MACZ;IACD;IACA,IAAIF,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC;IACvBN,OAAO,CAACZ,IAAI,CAACkB,KAAK,CAAC;IACnB,IAAIG,CAAC,GAAGR,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGhB,GAAG,CAACoB,KAAK,CAAC,GAAKrB,KAAK,CAAC;IACtD,IAAIyB,CAAC,GAAGT,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACH,KAAK,GAAGhB,GAAG,CAACoB,KAAK,GAAC,CAAC,CAAC,GAAGrB,KAAK,CAAC,GAAG,CAAC;IAC1D,OAAO,CAAC,CAACwB,CAAC,GAAGC,CAAC,IAAId,IAAI,KAAK,CAAC,EAAE;MAC7BG,QAAQ,GAAIA,QAAQ,IAAI,CAAC,GAAID,IAAI,GAAGL,QAAQ,CAAC,CAAC;MAC9CgB,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAIX,IAAI;MACnBY,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAIZ,IAAI,GAAG,CAAC;IACxB;IACA,OAAOW,CAAC,GAAG,CAACC,CAAC,GAAGb,IAAI,EAAE;MACrBE,QAAQ,GAAIA,QAAQ,GAAGH,IAAI,GAAMG,QAAQ,IAAI,CAAC,GAAKD,IAAI,KAAK,CAAG,GAAGL,QAAQ,CAAC,CAAC;MAC5EgB,CAAC,GAAIA,CAAC,IAAI,CAAC,GAAIb,IAAI;MACnBc,CAAC,GAAI,CAACA,CAAC,GAAGd,IAAI,KAAK,CAAC,GAAIA,IAAI,GAAG,CAAC;IACjC;IACAK,GAAG,GAAGQ,CAAC;IACPP,KAAK,GAAG,CAAC,GAAGQ,CAAC,GAAGD,CAAC;EAClB;EACA,IAAIE,MAAM,GAAG3B,YAAY,GAAG,CAAC;EAC7B,OAAOgB,OAAO,CAACY,GAAG,CAACC,CAAC,IAAI;IAAE;IACzB,QAAQA,CAAC,GAAGF,MAAM;MACjB,KAAK,CAAC;QAAE,OAAOA,MAAM,GAAG,OAAO,IAAK9B,KAAK,CAACS,WAAW,EAAE,CAAC,IAAI,EAAE,GAAKT,KAAK,CAACS,WAAW,EAAE,CAAC,IAAI,CAAE,GAAGT,KAAK,CAACS,WAAW,EAAE,CAAC,CAAC;MACrH,KAAK,CAAC;QAAE,OAAOqB,MAAM,GAAG,KAAK,IAAK9B,KAAK,CAACS,WAAW,EAAE,CAAC,IAAI,CAAC,GAAIT,KAAK,CAACS,WAAW,EAAE,CAAC,CAAC;MACpF,KAAK,CAAC;QAAE,OAAOqB,MAAM,GAAG9B,KAAK,CAACS,WAAW,EAAE,CAAC;MAC5C;QAAS,OAAOuB,CAAC,GAAG,CAAC;IACtB;EACD,CAAC,CAAC;AACH;;AAEA;AACA,SAASC,YAAYA,CAACC,CAAC,EAAE;EACxB,IAAIjC,GAAG,GAAG,CAAC;EACX,OAAO,MAAMiC,CAAC,CAACjC,GAAG,EAAE,CAAC;AACtB;AACA,SAASkC,uBAAuBA,CAACC,CAAC,EAAE;EACnC,OAAOH,YAAY,CAAClC,iBAAiB,CAACsC,WAAW,CAACD,CAAC,CAAC,CAAC,CAAC;AACvD;;AAEA;AACA;AACA;AACA,SAASC,WAAWA,CAACD,CAAC,EAAE;EACvB,IAAIE,MAAM,GAAG,EAAE;EACf,CAAC,GAAG,kEAAkE,CAAC,CAACC,OAAO,CAAC,CAACC,CAAC,EAAElC,CAAC,KAAKgC,MAAM,CAACE,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGnC,CAAC,CAAC;EACtH,IAAIoC,CAAC,GAAGN,CAAC,CAACO,MAAM;EAChB,IAAIC,GAAG,GAAG,IAAIC,UAAU,CAAE,CAAC,GAAGH,CAAC,IAAK,CAAC,CAAC;EACtC,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAEL,GAAG,GAAG,CAAC,EAAE6C,KAAK,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEzC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAE;IAC1DyC,KAAK,GAAIA,KAAK,IAAI,CAAC,GAAIT,MAAM,CAACF,CAAC,CAACK,UAAU,CAACnC,CAAC,CAAC,CAAC;IAC9CwC,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,IAAI,CAAC,EAAE;MACfF,GAAG,CAAC3C,GAAG,EAAE,CAAC,GAAI8C,KAAK,KAAKD,KAAK,IAAI,CAAC,CAAE;IACrC;EACD;EACA,OAAOF,GAAG;AACX;;AAEA;AACA,SAASI,MAAMA,CAAC1C,CAAC,EAAE;EAClB,OAAQA,CAAC,GAAG,CAAC,GAAK,CAACA,CAAC,IAAI,CAAC,GAAKA,CAAC,IAAI,CAAE;AACtC;AAEA,SAAS2C,WAAWA,CAACP,CAAC,EAAEQ,IAAI,EAAE;EAC7B,IAAIhB,CAAC,GAAGiB,KAAK,CAACT,CAAC,CAAC;EAChB,KAAK,IAAIpC,CAAC,GAAG,CAAC,EAAE0B,CAAC,GAAG,CAAC,EAAE1B,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAE4B,CAAC,CAAC5B,CAAC,CAAC,GAAG0B,CAAC,IAAIgB,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC;EAC7D,OAAOhB,CAAC;AACT;;AAEA;AACA,SAASkB,WAAWA,CAACF,IAAI,EAAEG,IAAI,GAAG,CAAC,EAAE;EACpC,IAAIT,GAAG,GAAG,EAAE;EACZ,OAAO,IAAI,EAAE;IACZ,IAAIZ,CAAC,GAAGkB,IAAI,CAAC,CAAC;IACd,IAAIR,CAAC,GAAGQ,IAAI,CAAC,CAAC;IACd,IAAI,CAACR,CAAC,EAAE;IACRW,IAAI,IAAIrB,CAAC;IACT,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAE;MAC3BsC,GAAG,CAACrC,IAAI,CAAC8C,IAAI,GAAG/C,CAAC,CAAC;IACnB;IACA+C,IAAI,IAAIX,CAAC,GAAG,CAAC;EACd;EACA,OAAOE,GAAG;AACX;AAEA,SAASU,kBAAkBA,CAACJ,IAAI,EAAE;EACjC,OAAOK,gBAAgB,CAAC,MAAM;IAC7B,IAAIrB,CAAC,GAAGkB,WAAW,CAACF,IAAI,CAAC;IACzB,IAAIhB,CAAC,CAACS,MAAM,EAAE,OAAOT,CAAC;EACvB,CAAC,CAAC;AACH;;AAEA;AACA,SAASsB,WAAWA,CAACN,IAAI,EAAE;EAC1B,IAAIN,GAAG,GAAG,EAAE;EACZ,OAAO,IAAI,EAAE;IACZ,IAAIa,CAAC,GAAGP,IAAI,CAAC,CAAC;IACd,IAAIO,CAAC,IAAI,CAAC,EAAE;IACZb,GAAG,CAACrC,IAAI,CAACmD,iBAAiB,CAACD,CAAC,EAAEP,IAAI,CAAC,CAAC;EACrC;EACA,OAAO,IAAI,EAAE;IACZ,IAAIO,CAAC,GAAGP,IAAI,CAAC,CAAC,GAAG,CAAC;IAClB,IAAIO,CAAC,GAAG,CAAC,EAAE;IACXb,GAAG,CAACrC,IAAI,CAACoD,sBAAsB,CAACF,CAAC,EAAEP,IAAI,CAAC,CAAC;EAC1C;EACA,OAAON,GAAG,CAACgB,IAAI,CAAC,CAAC;AAClB;;AAEA;AACA;AACA,SAASL,gBAAgBA,CAACL,IAAI,EAAE;EAC/B,IAAIhB,CAAC,GAAG,EAAE;EACV,OAAO,IAAI,EAAE;IACZ,IAAIF,CAAC,GAAGkB,IAAI,CAAChB,CAAC,CAACS,MAAM,CAAC;IACtB,IAAI,CAACX,CAAC,EAAE;IACRE,CAAC,CAAC3B,IAAI,CAACyB,CAAC,CAAC;EACV;EACA,OAAOE,CAAC;AACT;;AAEA;AACA;AACA,SAAS2B,eAAeA,CAACnB,CAAC,EAAEe,CAAC,EAAEP,IAAI,EAAE;EACpC,IAAIY,CAAC,GAAGX,KAAK,CAACT,CAAC,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAChC,GAAG,CAAC,MAAM,EAAE,CAAC;EACrC,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,CAAC,EAAEnD,CAAC,EAAE,EAAE;IAC3B2C,WAAW,CAACP,CAAC,EAAEQ,IAAI,CAAC,CAACX,OAAO,CAAC,CAACP,CAAC,EAAEgC,CAAC,KAAKF,CAAC,CAACE,CAAC,CAAC,CAACzD,IAAI,CAACyB,CAAC,CAAC,CAAC;EACrD;EACA,OAAO8B,CAAC;AACT;;AAEA;AACA;AACA,SAASJ,iBAAiBA,CAACD,CAAC,EAAEP,IAAI,EAAE;EACnC,IAAIe,EAAE,GAAG,CAAC,GAAGf,IAAI,CAAC,CAAC;EACnB,IAAIgB,EAAE,GAAGhB,IAAI,CAAC,CAAC;EACf,IAAIiB,EAAE,GAAGZ,gBAAgB,CAACL,IAAI,CAAC;EAC/B,IAAIY,CAAC,GAAGD,eAAe,CAACM,EAAE,CAACxB,MAAM,EAAE,CAAC,GAACc,CAAC,EAAEP,IAAI,CAAC;EAC7C,OAAOY,CAAC,CAACM,OAAO,CAAC,CAAClC,CAAC,EAAE5B,CAAC,KAAK;IAC1B,IAAI,CAAC0B,CAAC,EAAE,GAAGqC,EAAE,CAAC,GAAGnC,CAAC;IAClB,OAAOiB,KAAK,CAACgB,EAAE,CAAC7D,CAAC,CAAC,CAAC,CAACyD,IAAI,CAAC,CAAC,CAAChC,GAAG,CAAC,CAACuC,CAAC,EAAEN,CAAC,KAAK;MACxC,IAAIO,IAAI,GAAGP,CAAC,GAAGE,EAAE;MACjB,OAAO,CAAClC,CAAC,GAAGgC,CAAC,GAAGC,EAAE,EAAEI,EAAE,CAACtC,GAAG,CAACyC,CAAC,IAAIA,CAAC,GAAGD,IAAI,CAAC,CAAC;IAC3C,CAAC,CAAC;EACH,CAAC,CAAC;AACH;;AAEA;AACA;AACA,SAASZ,sBAAsBA,CAACF,CAAC,EAAEP,IAAI,EAAE;EACxC,IAAIR,CAAC,GAAG,CAAC,GAAGQ,IAAI,CAAC,CAAC;EAClB,IAAIY,CAAC,GAAGD,eAAe,CAACnB,CAAC,EAAE,CAAC,GAACe,CAAC,EAAEP,IAAI,CAAC;EACrC,OAAOY,CAAC,CAAC/B,GAAG,CAACG,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAACuC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC;AAGA,SAASC,SAASA,CAACxB,IAAI,EAAE;EACxB,IAAIN,GAAG,GAAG,EAAE;EACZ,IAAI+B,MAAM,GAAGvB,WAAW,CAACF,IAAI,CAAC;EAC9B0B,MAAM,CAACC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACtB,OAAOjC,GAAG,CAAC,CAAC;EACZ,SAASiC,MAAMA,CAACC,CAAC,EAAE;IAAE;IACpB,IAAIC,CAAC,GAAG7B,IAAI,CAAC,CAAC,CAAC,CAAC;IAChB,IAAI8B,CAAC,GAAGzB,gBAAgB,CAAC,MAAM;MAAE;MAChC,IAAI0B,GAAG,GAAG7B,WAAW,CAACF,IAAI,CAAC,CAACnB,GAAG,CAACzB,CAAC,IAAIqE,MAAM,CAACrE,CAAC,CAAC,CAAC;MAC/C,IAAI2E,GAAG,CAACtC,MAAM,EAAE,OAAOkC,MAAM,CAACI,GAAG,CAAC;IACnC,CAAC,CAAC;IACF,OAAO;MAACF,CAAC;MAAEC,CAAC;MAAEF;IAAC,CAAC;EACjB;EACA,SAASF,MAAMA,CAAC;IAACG,CAAC;IAAEC;EAAC,CAAC,EAAEC,GAAG,EAAEC,KAAK,EAAE;IACnC,IAAIH,CAAC,GAAG,CAAC,IAAIG,KAAK,KAAKD,GAAG,CAACA,GAAG,CAACtC,MAAM,GAAC,CAAC,CAAC,EAAE;IAC1C,IAAIoC,CAAC,GAAG,CAAC,EAAEG,KAAK,GAAGD,GAAG,CAACA,GAAG,CAACtC,MAAM,GAAC,CAAC,CAAC;IACpC,IAAIoC,CAAC,GAAG,CAAC,EAAEnC,GAAG,CAACrC,IAAI,CAAC0E,GAAG,CAAC;IACxB,KAAK,IAAIE,EAAE,IAAIH,CAAC,EAAE;MACjB,KAAK,IAAII,EAAE,IAAID,EAAE,CAACL,CAAC,EAAE;QACpBF,MAAM,CAACO,EAAE,EAAE,CAAC,GAAGF,GAAG,EAAEG,EAAE,CAAC,EAAEF,KAAK,CAAC;MAChC;IACD;EACD;AACD;AAEA,SAASG,MAAMA,CAACD,EAAE,EAAE;EACnB,OAAOA,EAAE,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;AACtD;AAEA,SAASC,QAAQA,CAACL,EAAE,EAAE;EACrB,OAAO,IAAIC,MAAM,CAACD,EAAE,CAAC,GAAG,CAAC,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASM,UAAUA,CAACtD,CAAC,EAAE;EAAE;EACxB,IAAI6C,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIhF,GAAG,GAAG,CAAC,EAAE0F,GAAG,GAAGvD,CAAC,CAACO,MAAM,EAAE1C,GAAG,GAAG0F,GAAG,GAAI;IAC9C,IAAIP,EAAE,GAAGhD,CAAC,CAACwD,WAAW,CAAC3F,GAAG,CAAC;IAC3BA,GAAG,IAAImF,EAAE,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC;IAC3BH,GAAG,CAAC1E,IAAI,CAAC6E,EAAE,CAAC;EACb;EACA,OAAOH,GAAG;AACX;AAEA,SAASY,YAAYA,CAACZ,GAAG,EAAE;EAC1B,MAAMa,KAAK,GAAG,IAAI;EAClB,IAAIH,GAAG,GAAGV,GAAG,CAACtC,MAAM;EACpB,IAAIgD,GAAG,GAAGG,KAAK,EAAE,OAAOC,MAAM,CAACC,aAAa,CAAC,GAAGf,GAAG,CAAC;EACpD,IAAIgB,GAAG,GAAG,EAAE;EACZ,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqF,GAAG,GAAI;IAC1BM,GAAG,CAAC1F,IAAI,CAACwF,MAAM,CAACC,aAAa,CAAC,GAAGf,GAAG,CAACR,KAAK,CAACnE,CAAC,EAAEA,CAAC,IAAIwF,KAAK,CAAC,CAAC,CAAC;EAC5D;EACA,OAAOG,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC;AACpB;AAEA,SAASC,cAAcA,CAACvE,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAIa,CAAC,GAAGd,CAAC,CAACe,MAAM;EAChB,IAAIH,CAAC,GAAGE,CAAC,GAAGb,CAAC,CAACc,MAAM;EACpB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEkC,CAAC,IAAI,CAAC,IAAIlC,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAEkC,CAAC,GAAGZ,CAAC,CAACtB,CAAC,CAAC,GAAGuB,CAAC,CAACvB,CAAC,CAAC;EACrD,OAAOkC,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAI4D,UAAU,GAAG,6xOAA6xO;;AAE9yO;AACA;AACA;;AAGA;AACA;AACA,MAAMC,EAAE,GAAG,MAAM;AACjB,MAAMC,EAAE,GAAG,MAAM;AACjB,MAAMC,EAAE,GAAG,MAAM;AACjB,MAAMC,EAAE,GAAG,MAAM;AACjB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAG,EAAE;AAClB,MAAMC,OAAO,GAAGF,OAAO,GAAGC,OAAO;AACjC,MAAME,OAAO,GAAGJ,OAAO,GAAGG,OAAO;AACjC,MAAME,EAAE,GAAGT,EAAE,GAAGQ,OAAO;AACvB,MAAME,EAAE,GAAGT,EAAE,GAAGG,OAAO;AACvB,MAAMO,EAAE,GAAGT,EAAE,GAAGG,OAAO;AACvB,MAAMO,EAAE,GAAGT,EAAE,GAAGG,OAAO;AAEvB,SAASO,SAASA,CAACC,MAAM,EAAE;EAC1B,OAAQA,MAAM,IAAI,EAAE,GAAI,IAAI;AAC7B;AACA,SAASC,SAASA,CAACD,MAAM,EAAE;EAC1B,OAAOA,MAAM,GAAG,QAAQ;AACzB;AAEA,IAAIE,YAAY,EAAEC,UAAU,EAAEC,MAAM,EAAEC,MAAM;AAE5C,SAASC,MAAMA,CAAA,EAAG;EACjB;EACA,IAAIC,CAAC,GAAGvF,uBAAuB,CAACiE,UAAU,CAAC;EAC3CiB,YAAY,GAAG,IAAIxH,GAAG,CAACyD,kBAAkB,CAACoE,CAAC,CAAC,CAACtD,OAAO,CAAC,CAAClC,CAAC,EAAE5B,CAAC,KAAK4B,CAAC,CAACH,GAAG,CAACC,CAAC,IAAI,CAACA,CAAC,EAAG1B,CAAC,GAAC,CAAC,IAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/FgH,UAAU,GAAG,IAAIK,GAAG,CAACvE,WAAW,CAACsE,CAAC,CAAC,CAAC;EACpCH,MAAM,GAAG,IAAI1H,GAAG,CAAC,CAAC;EAClB2H,MAAM,GAAG,IAAI3H,GAAG,CAAC,CAAC;EAClB,KAAK,IAAI,CAACuF,EAAE,EAAEH,GAAG,CAAC,IAAIzB,WAAW,CAACkE,CAAC,CAAC,EAAE;IACrC,IAAI,CAACJ,UAAU,CAACM,GAAG,CAACxC,EAAE,CAAC,IAAIH,GAAG,CAACtC,MAAM,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACf,CAAC,EAAEC,CAAC,CAAC,GAAGoD,GAAG;MAChB,IAAI4C,MAAM,GAAGL,MAAM,CAACM,GAAG,CAAClG,CAAC,CAAC;MAC1B,IAAI,CAACiG,MAAM,EAAE;QACZA,MAAM,GAAG,IAAIhI,GAAG,CAAC,CAAC;QAClB2H,MAAM,CAACO,GAAG,CAACnG,CAAC,EAAEiG,MAAM,CAAC;MACtB;MACAA,MAAM,CAACE,GAAG,CAAClG,CAAC,EAAEuD,EAAE,CAAC;IAClB;IACAmC,MAAM,CAACQ,GAAG,CAAC3C,EAAE,EAAEH,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC;EACA;EACA;AACD;AAEA,SAASC,SAASA,CAAC7C,EAAE,EAAE;EACtB,OAAOA,EAAE,IAAIiB,EAAE,IAAIjB,EAAE,GAAG0B,EAAE;AAC3B;AAEA,SAASoB,YAAYA,CAACtG,CAAC,EAAEC,CAAC,EAAE;EAC3B,IAAID,CAAC,IAAI0E,EAAE,IAAI1E,CAAC,GAAGmF,EAAE,IAAIlF,CAAC,IAAI0E,EAAE,IAAI1E,CAAC,GAAGmF,EAAE,EAAE;IAC3C,OAAOX,EAAE,GAAG,CAACzE,CAAC,GAAG0E,EAAE,IAAIM,OAAO,GAAG,CAAC/E,CAAC,GAAG0E,EAAE,IAAII,OAAO;EACpD,CAAC,MAAM,IAAIsB,SAAS,CAACrG,CAAC,CAAC,IAAIC,CAAC,GAAG2E,EAAE,IAAI3E,CAAC,GAAGoF,EAAE,IAAI,CAACrF,CAAC,GAAGyE,EAAE,IAAIM,OAAO,IAAI,CAAC,EAAE;IACvE,OAAO/E,CAAC,IAAIC,CAAC,GAAG2E,EAAE,CAAC;EACpB,CAAC,MAAM;IACN,IAAI2B,MAAM,GAAGX,MAAM,CAACM,GAAG,CAAClG,CAAC,CAAC;IAC1B,IAAIuG,MAAM,EAAE;MACXA,MAAM,GAAGA,MAAM,CAACL,GAAG,CAACjG,CAAC,CAAC;MACtB,IAAIsG,MAAM,EAAE;QACX,OAAOA,MAAM;MACd;IACD;IACA,OAAO,CAAC,CAAC;EACV;AACD;AAEA,SAASC,UAAUA,CAACnD,GAAG,EAAE;EACxB,IAAI,CAACoC,YAAY,EAAEI,MAAM,CAAC,CAAC;EAC3B,IAAI7E,GAAG,GAAG,EAAE;EACZ,IAAIqD,GAAG,GAAG,EAAE;EACZ,IAAIoC,WAAW,GAAG,KAAK;EACvB,SAASC,GAAGA,CAAClD,EAAE,EAAE;IAChB,IAAImD,EAAE,GAAGlB,YAAY,CAACS,GAAG,CAAC1C,EAAE,CAAC;IAC7B,IAAImD,EAAE,EAAE;MACPF,WAAW,GAAG,IAAI;MAClBjD,EAAE,IAAImD,EAAE;IACT;IACA3F,GAAG,CAACrC,IAAI,CAAC6E,EAAE,CAAC;EACb;EACA,KAAK,IAAIA,EAAE,IAAIH,GAAG,EAAE;IACnB,OAAO,IAAI,EAAE;MACZ,IAAIG,EAAE,GAAG,IAAI,EAAE;QACdxC,GAAG,CAACrC,IAAI,CAAC6E,EAAE,CAAC;MACb,CAAC,MAAM,IAAI6C,SAAS,CAAC7C,EAAE,CAAC,EAAE;QACzB,IAAIoD,OAAO,GAAGpD,EAAE,GAAGiB,EAAE;QACrB,IAAIoC,OAAO,GAAGD,OAAO,GAAG5B,OAAO,GAAG,CAAC;QACnC,IAAI8B,OAAO,GAAIF,OAAO,GAAG5B,OAAO,GAAID,OAAO,GAAG,CAAC;QAC/C,IAAIgC,OAAO,GAAGH,OAAO,GAAG7B,OAAO;QAC/B2B,GAAG,CAAChC,EAAE,GAAGmC,OAAO,CAAC;QACjBH,GAAG,CAAC/B,EAAE,GAAGmC,OAAO,CAAC;QACjB,IAAIC,OAAO,GAAG,CAAC,EAAEL,GAAG,CAAC9B,EAAE,GAAGmC,OAAO,CAAC;MACnC,CAAC,MAAM;QACN,IAAIC,MAAM,GAAGrB,MAAM,CAACO,GAAG,CAAC1C,EAAE,CAAC;QAC3B,IAAIwD,MAAM,EAAE;UACX3C,GAAG,CAAC1F,IAAI,CAAC,GAAGqI,MAAM,CAAC;QACpB,CAAC,MAAM;UACNN,GAAG,CAAClD,EAAE,CAAC;QACR;MACD;MACA,IAAI,CAACa,GAAG,CAACtD,MAAM,EAAE;MACjByC,EAAE,GAAGa,GAAG,CAAC4C,GAAG,CAAC,CAAC;IACf;EACD;EACA,IAAIR,WAAW,IAAIzF,GAAG,CAACD,MAAM,GAAG,CAAC,EAAE;IAClC,IAAImG,OAAO,GAAG5B,SAAS,CAACtE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/B,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,GAAG,CAACD,MAAM,EAAErC,CAAC,EAAE,EAAE;MACpC,IAAIiI,EAAE,GAAGrB,SAAS,CAACtE,GAAG,CAACtC,CAAC,CAAC,CAAC;MAC1B,IAAIiI,EAAE,IAAI,CAAC,IAAIO,OAAO,IAAIP,EAAE,EAAE;QAC7BO,OAAO,GAAGP,EAAE;QACZ;MACD;MACA,IAAIvE,CAAC,GAAG1D,CAAC,GAAC,CAAC;MACX,OAAO,IAAI,EAAE;QACZ,IAAIyI,GAAG,GAAGnG,GAAG,CAACoB,CAAC,GAAC,CAAC,CAAC;QAClBpB,GAAG,CAACoB,CAAC,GAAC,CAAC,CAAC,GAAGpB,GAAG,CAACoB,CAAC,CAAC;QACjBpB,GAAG,CAACoB,CAAC,CAAC,GAAG+E,GAAG;QACZ,IAAI,CAAC/E,CAAC,EAAE;QACR8E,OAAO,GAAG5B,SAAS,CAACtE,GAAG,CAAC,EAAEoB,CAAC,CAAC,CAAC;QAC7B,IAAI8E,OAAO,IAAIP,EAAE,EAAE;MACpB;MACAO,OAAO,GAAG5B,SAAS,CAACtE,GAAG,CAACtC,CAAC,CAAC,CAAC;IAC5B;EACD;EACA,OAAOsC,GAAG;AACX;AAEA,SAASoG,wBAAwBA,CAAC9G,CAAC,EAAE;EACpC,IAAIU,GAAG,GAAG,EAAE;EACZ,IAAIqG,KAAK,GAAG,EAAE;EACd,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIJ,OAAO,GAAG,CAAC;EACf,KAAK,IAAI3B,MAAM,IAAIjF,CAAC,EAAE;IACrB,IAAIqG,EAAE,GAAGrB,SAAS,CAACC,MAAM,CAAC;IAC1B,IAAI/B,EAAE,GAAGgC,SAAS,CAACD,MAAM,CAAC;IAC1B,IAAI+B,OAAO,IAAI,CAAC,CAAC,EAAE;MAClB,IAAIX,EAAE,IAAI,CAAC,EAAE;QACZW,OAAO,GAAG9D,EAAE;MACb,CAAC,MAAM;QACNxC,GAAG,CAACrC,IAAI,CAAC6E,EAAE,CAAC;MACb;IACD,CAAC,MAAM,IAAI0D,OAAO,GAAG,CAAC,IAAIA,OAAO,IAAIP,EAAE,EAAE;MACxC,IAAIA,EAAE,IAAI,CAAC,EAAE;QACZ3F,GAAG,CAACrC,IAAI,CAAC2I,OAAO,EAAE,GAAGD,KAAK,CAAC;QAC3BA,KAAK,CAACtG,MAAM,GAAG,CAAC;QAChBuG,OAAO,GAAG9D,EAAE;MACb,CAAC,MAAM;QACN6D,KAAK,CAAC1I,IAAI,CAAC6E,EAAE,CAAC;MACf;MACA0D,OAAO,GAAGP,EAAE;IACb,CAAC,MAAM;MACN,IAAIY,QAAQ,GAAGjB,YAAY,CAACgB,OAAO,EAAE9D,EAAE,CAAC;MACxC,IAAI+D,QAAQ,IAAI,CAAC,EAAE;QAClBD,OAAO,GAAGC,QAAQ;MACnB,CAAC,MAAM,IAAIL,OAAO,IAAI,CAAC,IAAIP,EAAE,IAAI,CAAC,EAAE;QACnC3F,GAAG,CAACrC,IAAI,CAAC2I,OAAO,CAAC;QACjBA,OAAO,GAAG9D,EAAE;MACb,CAAC,MAAM;QACN6D,KAAK,CAAC1I,IAAI,CAAC6E,EAAE,CAAC;QACd0D,OAAO,GAAGP,EAAE;MACb;IACD;EACD;EACA,IAAIW,OAAO,IAAI,CAAC,EAAE;IACjBtG,GAAG,CAACrC,IAAI,CAAC2I,OAAO,EAAE,GAAGD,KAAK,CAAC;EAC5B;EACA,OAAOrG,GAAG;AACX;;AAEA;AACA,SAASwG,GAAGA,CAACnE,GAAG,EAAE;EACjB,OAAOmD,UAAU,CAACnD,GAAG,CAAC,CAAClD,GAAG,CAACqF,SAAS,CAAC;AACtC;AACA,SAASiC,GAAGA,CAACpE,GAAG,EAAE;EACjB,OAAO+D,wBAAwB,CAACZ,UAAU,CAACnD,GAAG,CAAC,CAAC;AACjD;AAEA,MAAMqE,MAAM,GAAG,IAAI;AACnB,MAAMC,IAAI,GAAG,IAAI;AACjB,MAAMC,OAAO,GAAG,GAAG;AACnB,MAAMC,IAAI,GAAG,MAAM;AACnB,MAAMC,SAAS,GAAG,CAAC;;AAEnB;AACA,MAAMC,UAAU,GAAG3H,CAAC,IAAImB,KAAK,CAACyG,IAAI,CAAC5H,CAAC,CAAC,CAAC,CAAC;;AAEvC,SAAS6H,YAAYA,CAACC,CAAC,EAAE1E,EAAE,EAAE;EAC5B;EACA,OAAO0E,CAAC,CAACC,CAAC,CAACnC,GAAG,CAACxC,EAAE,CAAC,IAAI0E,CAAC,CAAChF,CAAC,CAAC8C,GAAG,CAACxC,EAAE,CAAC;AAClC;AAEA,MAAM4E,KAAK,SAAS7G,KAAK,CAAC;EACzB,IAAI8G,QAAQA,CAAA,EAAG;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC;AACjC;AAEA,IAAIC,MAAM,EAAEC,OAAO,EAAEC,EAAE,EAAEC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,UAAU;AAE9G,SAASC,IAAIA,CAAA,EAAG;EACf,IAAIZ,MAAM,EAAE;EAEZ,IAAIxC,CAAC,GAAGvF,uBAAuB,CAACxC,YAAY,CAAC;EAC7C,MAAMoL,iBAAiB,GAAGA,CAAA,KAAM3H,WAAW,CAACsE,CAAC,CAAC;EAC9C,MAAMsD,eAAe,GAAGA,CAAA,KAAM,IAAIrD,GAAG,CAACoD,iBAAiB,CAAC,CAAC,CAAC;EAC1D,MAAME,YAAY,GAAGA,CAAClD,GAAG,EAAE7F,CAAC,KAAKA,CAAC,CAACK,OAAO,CAACP,CAAC,IAAI+F,GAAG,CAACO,GAAG,CAACtG,CAAC,CAAC,CAAC;EAE3DkI,MAAM,GAAG,IAAIrK,GAAG,CAAC2D,WAAW,CAACkE,CAAC,CAAC,CAAC;EAChCyC,OAAO,GAAGa,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE7B;AACD;AACA;AACA;AACA;AACA;AACA;EACC;EACA;EACAZ,EAAE,GAAGW,iBAAiB,CAAC,CAAC;EACxBV,GAAG,GAAG,IAAI1C,GAAG,CAACoD,iBAAiB,CAAC,CAAC,CAAChJ,GAAG,CAACzB,CAAC,IAAI8J,EAAE,CAAC9J,CAAC,CAAC,CAAC,CAAC;EAClD8J,EAAE,GAAG,IAAIzC,GAAG,CAACyC,EAAE,CAAC;EAEhBE,MAAM,GAAGU,eAAe,CAAC,CAAC,CAAC,CAAC;EAC5BT,SAAS,GAAGS,eAAe,CAAC,CAAC,CAAC,CAAC;;EAE/B,IAAIE,MAAM,GAAG5H,kBAAkB,CAACoE,CAAC,CAAC;EAClC,IAAIyD,YAAY,GAAGzD,CAAC,CAAC,CAAC;EACtB;EACA,MAAM0D,YAAY,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIrD,GAAG,GAAG,IAAIJ,GAAG,CAAC,CAAC;IACnBoD,iBAAiB,CAAC,CAAC,CAACxI,OAAO,CAACjC,CAAC,IAAI2K,YAAY,CAAClD,GAAG,EAAEmD,MAAM,CAAC5K,CAAC,CAAC,CAAC,CAAC;IAC9D2K,YAAY,CAAClD,GAAG,EAAEgD,iBAAiB,CAAC,CAAC,CAAC;IACtC,OAAOhD,GAAG;EACX,CAAC;EACDyC,MAAM,GAAGjH,gBAAgB,CAACjD,CAAC,IAAI;IAC9B;IACA;IACA,IAAIO,CAAC,GAAG0C,gBAAgB,CAACmE,CAAC,CAAC,CAAC3F,GAAG,CAACC,CAAC,IAAIA,CAAC,GAAC,IAAI,CAAC;IAC5C,IAAInB,CAAC,CAAC8B,MAAM,EAAE;MACb,IAAI0I,CAAC,GAAG/K,CAAC,IAAI6K,YAAY,CAAC,CAAC;MAC3BtK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;MACZA,CAAC,GAAGgF,YAAY,CAAChF,CAAC,CAAC;MACnB,IAAIwK,CAAC,EAAExK,CAAC,GAAC,cAAcA,CAAC,GAAG;MAC3B,IAAIkJ,CAAC,GAAGqB,YAAY,CAAC,CAAC,CAAC,CAAC;MACxB,IAAItG,CAAC,GAAGsG,YAAY,CAAC,CAAC,CAAC,CAAC;MACxB,IAAIE,CAAC,GAAG,CAAC5D,CAAC,CAAC,CAAC,CAAC,CAAC;MACd;MACA;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACG,OAAO;QAAC7G,CAAC;QAAEkJ,CAAC;QAAEjF,CAAC;QAAEwG,CAAC;QAAED;MAAC,CAAC;IACvB;EACD,CAAC,CAAC;;EAEF;EACAZ,WAAW,GAAGO,eAAe,CAAC,CAAC;EAC/BN,SAAS,GAAG,IAAI7K,GAAG,CAAC,CAAC;EACrB,IAAI0L,MAAM,GAAGR,iBAAiB,CAAC,CAAC,CAACS,MAAM,CAAC7B,UAAU,CAACc,WAAW,CAAC,CAAC,CAACgB,IAAI,CAAC,CAAC7J,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAACC,CAAC,CAAC,CAAC,CAAC;EACtF0J,MAAM,CAAChJ,OAAO,CAAC,CAAC6C,EAAE,EAAE9E,CAAC,KAAK;IACzB,IAAIoL,CAAC,GAAGhE,CAAC,CAAC,CAAC;IACX,IAAIjE,CAAC,GAAG8H,MAAM,CAACjL,CAAC,CAAC,GAAGoL,CAAC,GAAGH,MAAM,CAACjL,CAAC,GAACoL,CAAC,CAAC,GAAG;MAACC,CAAC,EAAE,EAAE;MAAEL,CAAC,EAAE,IAAIzL,GAAG,CAAC;IAAC,CAAC;IAC3D4D,CAAC,CAACkI,CAAC,CAACpL,IAAI,CAAC6E,EAAE,CAAC,CAAC,CAAC;IACd,IAAI,CAACqF,WAAW,CAAC7C,GAAG,CAACxC,EAAE,CAAC,EAAE;MACzBsF,SAAS,CAAC3C,GAAG,CAAC3C,EAAE,EAAE3B,CAAC,CAAC,CAAC,CAAE;IACxB;EACD,CAAC,CAAC;;EAEF;EACA;EACA,KAAK,IAAI;IAACkI,CAAC;IAAEL;EAAC,CAAC,IAAI,IAAI3D,GAAG,CAAC+C,SAAS,CAACkB,MAAM,CAAC,CAAC,CAAC,EAAE;IAC/C;IACA,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIzG,EAAE,IAAIuG,CAAC,EAAE;MACjB,IAAIG,EAAE,GAAGtB,MAAM,CAACuB,MAAM,CAACjC,CAAC,IAAID,YAAY,CAACC,CAAC,EAAE1E,EAAE,CAAC,CAAC;MAChD,IAAI4G,GAAG,GAAGH,IAAI,CAACI,IAAI,CAAC,CAAC;QAACC;MAAC,CAAC,KAAKJ,EAAE,CAACK,IAAI,CAACrC,CAAC,IAAIoC,CAAC,CAACtE,GAAG,CAACkC,CAAC,CAAC,CAAC,CAAC;MACpD,IAAI,CAACkC,GAAG,EAAE;QACTA,GAAG,GAAG;UAACE,CAAC,EAAE,IAAIvE,GAAG,CAAC,CAAC;UAAEgE,CAAC,EAAE;QAAE,CAAC;QAC3BE,IAAI,CAACtL,IAAI,CAACyL,GAAG,CAAC;MACf;MACAA,GAAG,CAACL,CAAC,CAACpL,IAAI,CAAC6E,EAAE,CAAC;MACd6F,YAAY,CAACe,GAAG,CAACE,CAAC,EAAEJ,EAAE,CAAC;IACxB;IACA;IACA,IAAIM,KAAK,GAAGP,IAAI,CAACzH,OAAO,CAACpC,CAAC,IAAI2H,UAAU,CAAC3H,CAAC,CAACkK,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,KAAK,IAAI;MAACA,CAAC;MAAEP;IAAC,CAAC,IAAIE,IAAI,EAAE;MACxB,IAAIQ,UAAU,GAAG,IAAI1E,GAAG,CAACyE,KAAK,CAACL,MAAM,CAACjC,CAAC,IAAI,CAACoC,CAAC,CAACtE,GAAG,CAACkC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxD,KAAK,IAAI1E,EAAE,IAAIuG,CAAC,EAAE;QACjBL,CAAC,CAACvD,GAAG,CAAC3C,EAAE,EAAEiH,UAAU,CAAC,CAAC,CAAC;MACxB;IACD;EACD;;EAEA;EACA;EACA1B,KAAK,GAAG,IAAIhD,GAAG,CAAC,CAAC,CAAC,CAAC;EACnB,IAAI2E,KAAK,GAAG,IAAI3E,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB,MAAM4E,YAAY,GAAGnH,EAAE,IAAIuF,KAAK,CAAC/C,GAAG,CAACxC,EAAE,CAAC,GAAGkH,KAAK,CAAChE,GAAG,CAAClD,EAAE,CAAC,GAAGuF,KAAK,CAACrC,GAAG,CAAClD,EAAE,CAAC;EACxE,KAAK,IAAI0E,CAAC,IAAIU,MAAM,EAAE;IACrB,KAAK,IAAIpF,EAAE,IAAI0E,CAAC,CAACC,CAAC,EAAEwC,YAAY,CAACnH,EAAE,CAAC;IACpC,KAAK,IAAIA,EAAE,IAAI0E,CAAC,CAAChF,CAAC,EAAEyH,YAAY,CAACnH,EAAE,CAAC;EACrC;EACA;EACA,KAAK,IAAIA,EAAE,IAAIuF,KAAK,EAAE;IACrB,IAAI,CAACD,SAAS,CAAC9C,GAAG,CAACxC,EAAE,CAAC,IAAI,CAACkH,KAAK,CAAC1E,GAAG,CAACxC,EAAE,CAAC,EAAE;MACzCsF,SAAS,CAAC3C,GAAG,CAAC3C,EAAE,EAAEsE,SAAS,CAAC;IAC7B;EACD;EACA;EACA;EACAuB,YAAY,CAACN,KAAK,EAAEvB,GAAG,CAACuB,KAAK,CAAC,CAAC;;EAE/B;EACA;EACAC,UAAU,GAAGlG,SAAS,CAACgD,CAAC,CAAC,CAAC3F,GAAG,CAACG,CAAC,IAAI8H,KAAK,CAACJ,IAAI,CAAC1H,CAAC,CAAC,CAAC,CAACuJ,IAAI,CAACtF,cAAc,CAAC;EACtE0E,UAAU,GAAG,IAAIhL,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB,KAAK,IAAIoF,GAAG,IAAI2F,UAAU,EAAE;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA,IAAIvH,IAAI,GAAG,CAACwH,UAAU,CAAC;IACvB,KAAK,IAAIzF,EAAE,IAAIH,GAAG,EAAE;MACnB,IAAI/B,IAAI,GAAGG,IAAI,CAACtB,GAAG,CAACyK,IAAI,IAAI;QAC3B,IAAIC,KAAK,GAAGD,IAAI,CAAC1E,GAAG,CAAC1C,EAAE,CAAC;QACxB,IAAI,CAACqH,KAAK,EAAE;UACX;UACA;UACA;UACAA,KAAK,GAAG,IAAI5M,GAAG,CAAC,CAAC;UACjB2M,IAAI,CAACzE,GAAG,CAAC3C,EAAE,EAAEqH,KAAK,CAAC;QACpB;QACA,OAAOA,KAAK;MACb,CAAC,CAAC;MACF,IAAIrH,EAAE,KAAKqE,IAAI,EAAE;QAChBpG,IAAI,CAAC9C,IAAI,CAAC,GAAG2C,IAAI,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACNG,IAAI,GAAGH,IAAI;MACZ;IACD;IACA,KAAK,IAAIlB,CAAC,IAAIqB,IAAI,EAAE;MACnBrB,CAAC,CAAC2J,CAAC,GAAG1G,GAAG;IACV;EACD;AACD;;AAEA;AACA;AACA,SAASyH,SAASA,CAACtH,EAAE,EAAE;EACtB,OAAO,CAACuH,aAAa,CAACvH,EAAE,CAAC,GAAG,EAAE,GAAG,GAAGwH,OAAO,CAACC,iBAAiB,CAAC,CAACzH,EAAE,CAAC,CAAC,CAAC,GAAG,IAAIK,QAAQ,CAACL,EAAE,CAAC;AACxF;;AAEA;AACA;AACA;AACA;AACA,SAASwH,OAAOA,CAACxK,CAAC,EAAE;EACnB,OAAO,IAAIA,CAAC,SAAS,CAAC,CAAC;AACxB;AAEA,SAAS0K,qBAAqBA,CAAC7H,GAAG,EAAE;EACnC,IAAIA,GAAG,CAACtC,MAAM,IAAI,CAAC,IAAIsC,GAAG,CAAC,CAAC,CAAC,IAAIqE,MAAM,IAAIrE,GAAG,CAAC,CAAC,CAAC,IAAIqE,MAAM,EAAE;IAC5D,MAAM,IAAIyD,KAAK,CAAC,6BAA6BlH,YAAY,CAACZ,GAAG,CAACR,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EACjF;AACD;AACA,SAASuI,wBAAwBA,CAAC/H,GAAG,EAAE;EACtC,MAAMgI,UAAU,GAAG,IAAI;EACvB,KAAK,IAAI3M,CAAC,GAAG2E,GAAG,CAACiI,WAAW,CAACD,UAAU,CAAC,EAAE3M,CAAC,GAAG,CAAC,GAAI;IAClD,IAAI2E,GAAG,CAAC,EAAE3E,CAAC,CAAC,KAAK2M,UAAU,EAAE;MAC5B,MAAM,IAAIF,KAAK,CAAC,kCAAkC,CAAC;IACpD;EACD;AACD;AACA;AACA,SAASI,YAAYA,CAAClI,GAAG,EAAE;EAC1B,IAAIG,EAAE,GAAGH,GAAG,CAAC,CAAC,CAAC;EACf,IAAI5B,IAAI,GAAGzD,MAAM,CAACkI,GAAG,CAAC1C,EAAE,CAAC;EACzB,IAAI/B,IAAI,EAAE,MAAM+J,eAAe,CAAC,WAAW/J,IAAI,EAAE,CAAC;EAClD,IAAIX,CAAC,GAAGuC,GAAG,CAACtC,MAAM;EAClB,IAAI0K,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;EACf,KAAK,IAAI/M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAE;IAC3B8E,EAAE,GAAGH,GAAG,CAAC3E,CAAC,CAAC;IACX,IAAIgN,KAAK,GAAG1N,MAAM,CAACkI,GAAG,CAAC1C,EAAE,CAAC;IAC1B,IAAIkI,KAAK,EAAE;MACV;MACA,IAAID,IAAI,IAAI/M,CAAC,EAAE,MAAM8M,eAAe,CAAC,GAAG/J,IAAI,MAAMiK,KAAK,EAAE,CAAC;MAC1DD,IAAI,GAAG/M,CAAC,GAAG,CAAC;MACZ+C,IAAI,GAAGiK,KAAK;IACb;EACD;EACA,IAAID,IAAI,IAAI3K,CAAC,EAAE,MAAM0K,eAAe,CAAC,YAAY/J,IAAI,EAAE,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwJ,iBAAiBA,CAAC5H,GAAG,EAAEsI,GAAG,GAAGC,QAAQ,EAAEC,MAAM,GAAGhI,QAAQ,EAAE;EAClE;EACA;EACA,IAAIQ,GAAG,GAAG,EAAE;EACZ,IAAIyH,iBAAiB,CAACzI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEgB,GAAG,CAAC1F,IAAI,CAAC,GAAG,CAAC;EAC5C,IAAI0E,GAAG,CAACtC,MAAM,GAAG4K,GAAG,EAAE;IACrBA,GAAG,KAAK,CAAC;IACTtI,GAAG,GAAG,CAAC,GAAGA,GAAG,CAACR,KAAK,CAAC,CAAC,EAAE8I,GAAG,CAAC,EAAE,MAAM,EAAE,GAAGtI,GAAG,CAACR,KAAK,CAAC,CAAC8I,GAAG,CAAC,CAAC;EACzD;EACA,IAAIlK,IAAI,GAAG,CAAC;EACZ,IAAIX,CAAC,GAAGuC,GAAG,CAACtC,MAAM;EAClB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoC,CAAC,EAAEpC,CAAC,EAAE,EAAE;IAC3B,IAAI8E,EAAE,GAAGH,GAAG,CAAC3E,CAAC,CAAC;IACf,IAAIqM,aAAa,CAACvH,EAAE,CAAC,EAAE;MACtBa,GAAG,CAAC1F,IAAI,CAACsF,YAAY,CAACZ,GAAG,CAACR,KAAK,CAACpB,IAAI,EAAE/C,CAAC,CAAC,CAAC,CAAC;MAC1C2F,GAAG,CAAC1F,IAAI,CAACkN,MAAM,CAACrI,EAAE,CAAC,CAAC;MACpB/B,IAAI,GAAG/C,CAAC,GAAG,CAAC;IACb;EACD;EACA2F,GAAG,CAAC1F,IAAI,CAACsF,YAAY,CAACZ,GAAG,CAACR,KAAK,CAACpB,IAAI,EAAEX,CAAC,CAAC,CAAC,CAAC;EAC1C,OAAOuD,GAAG,CAACC,IAAI,CAAC,EAAE,CAAC;AACpB;;AAEA;AACA;AACA,SAASwH,iBAAiBA,CAACtI,EAAE,EAAE;EAC9B0F,IAAI,CAAC,CAAC;EACN,OAAOV,EAAE,CAACxC,GAAG,CAACxC,EAAE,CAAC;AAClB;AACA,SAASuH,aAAaA,CAACvH,EAAE,EAAE;EAC1B0F,IAAI,CAAC,CAAC;EACN,OAAOR,MAAM,CAAC1C,GAAG,CAACxC,EAAE,CAAC;AACtB;;AAEA;AACA;AACA,SAASuI,SAASA,CAAA,EAAG;EACpB7C,IAAI,CAAC,CAAC;EACN,OAAOF,UAAU,CAAC7I,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACyC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC;AAEA,SAASmJ,sBAAsBA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAChDhD,IAAI,CAAC,CAAC;EACN,IAAIiD,EAAE,GAAGD,SAAS,GAAG1E,GAAG,GAAGC,GAAG;EAC9B,OAAOwE,IAAI,CAACG,KAAK,CAACxE,OAAO,CAAC,CAACzH,GAAG,CAACkM,KAAK,IAAIpI,YAAY,CAACqI,eAAe,CAACxI,UAAU,CAACuI,KAAK,CAAC,EAAEF,EAAE,EAAEI,WAAW,CAAC,CAACvK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsC,IAAI,CAACsD,OAAO,CAAC;AAChI;AAEA,SAAS4E,aAAaA,CAACC,IAAI,EAAE;EAC5B,OAAOC,OAAO,CAACN,KAAK,CAACK,IAAI,EAAEhF,GAAG,EAAE8E,WAAW,CAAC,CAAC;AAC9C;AAEA,SAASI,YAAYA,CAACF,IAAI,EAAE;EAC3B,IAAIG,MAAM,GAAGR,KAAK,CAACK,IAAI,EAAEhF,GAAG,EAAErH,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;EACvC,KAAK,IAAI;IAACyM,IAAI;IAAEC,MAAM;IAAEC;EAAK,CAAC,IAAIH,MAAM,EAAE;IACzC,IAAIG,KAAK,EAAE,MAAM,CAAC;;IAElB;IACA;IACA;IACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACE;IACA;;IAEA;IACA;IACA,IAAIF,IAAI,KAAK,OAAO,EAAEG,aAAa,CAACF,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;;IAEzD;IACA;IACA;IACA;EACD;EACA,OAAOJ,OAAO,CAACE,MAAM,CAAC;AACvB;AAEA,SAASI,aAAaA,CAAC1M,CAAC,EAAEN,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIwB,IAAI,GAAG,CAAC;EACZ,OAAO,IAAI,EAAE;IACZ,IAAIH,IAAI,GAAGhB,CAAC,CAAC2M,OAAO,CAACjN,CAAC,EAAEyB,IAAI,CAAC;IAC7B,IAAIH,IAAI,GAAG,CAAC,EAAE;IACdhB,CAAC,CAACgB,IAAI,CAAC,GAAGrB,CAAC;IACXwB,IAAI,GAAGH,IAAI,GAAG,CAAC;EAChB;AACD;AAEA,SAAS4L,SAASA,CAACT,IAAI,EAAEU,cAAc,EAAE;EACxC,OAAOf,KAAK,CAACK,IAAI,EAAEhF,GAAG,EAAE0F,cAAc,GAAG/M,CAAC,IAAIA,CAAC,CAACyC,KAAK,CAAC,CAAC,GAAG0J,WAAW,CAAC,CAAC,CAAC;AACzE;AAEA,SAASH,KAAKA,CAACK,IAAI,EAAEN,EAAE,EAAEiB,EAAE,EAAE;EAC5B,IAAI,CAACX,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;EACtBvD,IAAI,CAAC,CAAC;EACN,IAAIhJ,MAAM,GAAG,CAAC;EACd;EACA;EACA,OAAOuM,IAAI,CAACL,KAAK,CAACxE,OAAO,CAAC,CAACzH,GAAG,CAACkM,KAAK,IAAI;IACvC,IAAIgB,KAAK,GAAGvJ,UAAU,CAACuI,KAAK,CAAC;IAC7B,IAAIiB,IAAI,GAAG;MACVD,KAAK;MACLnN,MAAM,CAAE;IACT,CAAC;IACDA,MAAM,IAAImN,KAAK,CAACtM,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI;MACH;MACA,IAAIwM,MAAM,GAAGD,IAAI,CAACC,MAAM,GAAGjB,eAAe,CAACe,KAAK,EAAElB,EAAE,EAAEiB,EAAE,CAAC;MACzD,IAAII,WAAW,GAAGD,MAAM,CAACxM,MAAM;MAC/B,IAAI8L,IAAI;MACR,IAAI,CAACW,WAAW,EAAE;QAAE;QACnB;QACA;QACA;QACA;QACA,MAAM,IAAIrC,KAAK,CAAC,aAAa,CAAC;MAC/B;MACA,IAAIsC,IAAI,GAAGH,IAAI,CAACR,MAAM,GAAGS,MAAM,CAACvL,IAAI,CAAC,CAAC;MACtCoJ,wBAAwB,CAACqC,IAAI,CAAC;MAC9B,IAAIC,KAAK,GAAGJ,IAAI,CAACI,KAAK,GAAGF,WAAW,GAAG,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC,CAAClF,QAAQ,CAAC,CAAC;MAChE,IAAI,CAACqF,KAAK,IAAID,IAAI,CAACE,KAAK,CAACnK,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC,EAAE;QAAE;QAC5C;QACA0H,qBAAqB,CAACuC,IAAI,CAAC,CAAC,CAAC;QAC7B;QACA;QACA;QACA;QACAZ,IAAI,GAAG,OAAO;MACf,CAAC,MAAM;QACN,IAAIe,KAAK,GAAGL,MAAM,CAAC/K,OAAO,CAACpC,CAAC,IAAIA,CAAC,CAACiI,QAAQ,GAAG,EAAE,GAAGjI,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI,CAACwN,KAAK,CAAC7M,MAAM,EAAE;UAAE;UACpB8L,IAAI,GAAG,OAAO;QACf,CAAC,MAAM;UACN;UACA,IAAIrE,EAAE,CAACxC,GAAG,CAACyH,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,MAAMjC,eAAe,CAAC,wBAAwB,CAAC;UACpE,KAAK,IAAI9M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8O,WAAW,EAAE9O,CAAC,EAAE,EAAE;YAAE;YACvC,IAAI2E,GAAG,GAAGkK,MAAM,CAAC7O,CAAC,CAAC;YACnB,IAAI,CAAC2E,GAAG,CAACgF,QAAQ,IAAIG,EAAE,CAACxC,GAAG,CAAC3C,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;cAAE;cACtC;cACA,MAAMmI,eAAe,CAAC,4BAA4BvH,YAAY,CAACsJ,MAAM,CAAC7O,CAAC,GAAC,CAAC,CAAC,CAAC,MAAMuM,iBAAiB,CAAC,CAAC5H,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;YACjH;UACD;UACAkI,YAAY,CAACkC,IAAI,CAAC;UAClB,IAAII,MAAM,GAAG9F,UAAU,CAAC,IAAIhC,GAAG,CAAC6H,KAAK,CAAC,CAAC;UACvC,IAAI,CAAC1F,CAAC,CAAC,GAAG4F,eAAe,CAACD,MAAM,CAAC,CAAC,CAAC;UACnC;UACA;UACAE,WAAW,CAAC7F,CAAC,EAAE0F,KAAK,CAAC,CAAC,CAAC;UACvBI,WAAW,CAAC9F,CAAC,EAAE2F,MAAM,CAAC,CAAC,CAAC;UACxBhB,IAAI,GAAG3E,CAAC,CAACjJ,CAAC;UACV;UACA;UACA;UACA;QACD;MACD;MACAqO,IAAI,CAACT,IAAI,GAAGA,IAAI;IACjB,CAAC,CAAC,OAAOoB,GAAG,EAAE;MACbX,IAAI,CAACP,KAAK,GAAGkB,GAAG,CAAC,CAAC;IACnB;IACA,OAAOX,IAAI;EACZ,CAAC,CAAC;AACH;AAEA,SAASU,WAAWA,CAACE,KAAK,EAAEL,MAAM,EAAE;EACnC,IAAIM,KAAK;EACT,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAI5K,EAAE,IAAIqK,MAAM,EAAE;IACtB,IAAIQ,KAAK,GAAGvF,SAAS,CAAC5C,GAAG,CAAC1C,EAAE,CAAC;IAC7B,IAAI6K,KAAK,KAAKvG,SAAS,EAAE,OAAO,CAAC;IACjC,IAAIuG,KAAK,EAAE;MACV,IAAIlI,GAAG,GAAGkI,KAAK,CAAC3E,CAAC,CAACxD,GAAG,CAAC1C,EAAE,CAAC,CAAC,CAAC;MAC3B2K,KAAK,GAAGA,KAAK,GAAGA,KAAK,CAAChE,MAAM,CAACjC,CAAC,IAAI/B,GAAG,CAACH,GAAG,CAACkC,CAAC,CAAC,CAAC,GAAGH,UAAU,CAAC5B,GAAG,CAAC;MAC/D,IAAI,CAACgI,KAAK,CAACpN,MAAM,EAAE,OAAO,CAAC;IAC5B,CAAC,MAAM;MACNqN,MAAM,CAACzP,IAAI,CAAC6E,EAAE,CAAC;IAChB;EACD;EACA,IAAI2K,KAAK,EAAE;IACV;IACA;IACA;IACA,KAAK,IAAIjG,CAAC,IAAIiG,KAAK,EAAE;MACpB,IAAIC,MAAM,CAACT,KAAK,CAACnK,EAAE,IAAIyE,YAAY,CAACC,CAAC,EAAE1E,EAAE,CAAC,CAAC,EAAE;QAC5C,MAAM,IAAI2H,KAAK,CAAC,4BAA4B+C,KAAK,CAACjP,CAAC,IAAIiJ,CAAC,CAACjJ,CAAC,EAAE,CAAC;MAC9D;IACD;EACD;AACD;;AAEA;AACA;AACA,SAAS6O,eAAeA,CAACD,MAAM,EAAE;EAChC,IAAIS,MAAM,GAAG1F,MAAM;EACnB,KAAK,IAAIpF,EAAE,IAAIqK,MAAM,EAAE;IACtB;IACA;IACA,IAAI3D,EAAE,GAAGoE,MAAM,CAACnE,MAAM,CAACjC,CAAC,IAAID,YAAY,CAACC,CAAC,EAAE1E,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC0G,EAAE,CAACnJ,MAAM,EAAE;MACf,IAAI,CAAC6H,MAAM,CAAC2B,IAAI,CAACrC,CAAC,IAAID,YAAY,CAACC,CAAC,EAAE1E,EAAE,CAAC,CAAC,EAAE;QAC3C;QACA;QACA;QACA;QACA;QACA,MAAM+K,gBAAgB,CAAC/K,EAAE,CAAC,CAAC,CAAC;MAC7B,CAAC,MAAM;QACN;QACA;QACA;QACA,MAAMgL,kBAAkB,CAACF,MAAM,CAAC,CAAC,CAAC,EAAE9K,EAAE,CAAC;MACxC;IACD;IACA8K,MAAM,GAAGpE,EAAE;IACX,IAAIA,EAAE,CAACnJ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC;EAC5B;EACA;EACA,OAAOuN,MAAM;AACd;;AAEA;AACA,SAAS5B,OAAOA,CAACN,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACjM,GAAG,CAAC,CAAC;IAACkN,KAAK;IAAEN,KAAK;IAAED;EAAM,CAAC,KAAK;IAC5C,IAAIC,KAAK,EAAE;MACV;MACA,IAAI0B,GAAG,GAAG1B,KAAK,CAAC2B,OAAO;MACvB;MACA,MAAM,IAAIvD,KAAK,CAACiB,KAAK,CAACrL,MAAM,IAAI,CAAC,GAAG0N,GAAG,GAAG,iBAAiBzD,OAAO,CAACC,iBAAiB,CAACoC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAKoB,GAAG,EAAE,CAAC;IAC5G;IACA,OAAOxK,YAAY,CAAC6I,MAAM,CAAC;EAC5B,CAAC,CAAC,CAACxI,IAAI,CAACsD,OAAO,CAAC;AACjB;AAEA,SAAS2G,gBAAgBA,CAAC/K,EAAE,EAAE;EAC7B;EACA,OAAO,IAAI2H,KAAK,CAAC,yBAAyBL,SAAS,CAACtH,EAAE,CAAC,EAAE,CAAC;AAC3D;AACA,SAASgL,kBAAkBA,CAACtG,CAAC,EAAE1E,EAAE,EAAE;EAClC,IAAImL,MAAM,GAAG7D,SAAS,CAACtH,EAAE,CAAC;EAC1B,IAAIoL,EAAE,GAAGhG,MAAM,CAACyB,IAAI,CAACnC,CAAC,IAAIA,CAAC,CAACC,CAAC,CAACnC,GAAG,CAACxC,EAAE,CAAC,CAAC,CAAC,CAAC;EACxC,IAAIoL,EAAE,EAAE;IACPD,MAAM,GAAG,GAAGC,EAAE,CAAC3P,CAAC,IAAI0P,MAAM,EAAE;EAC7B;EACA,OAAO,IAAIxD,KAAK,CAAC,oBAAoBjD,CAAC,CAACjJ,CAAC,MAAM0P,MAAM,EAAE,CAAC;AACxD;AACA,SAASnD,eAAeA,CAACqD,KAAK,EAAE;EAC/B,OAAO,IAAI1D,KAAK,CAAC,sBAAsB0D,KAAK,EAAE,CAAC;AAChD;;AAEA;AACA;AACA;AACA,SAASd,WAAWA,CAAC7F,CAAC,EAAE7E,GAAG,EAAE;EAC5B,KAAK,IAAIG,EAAE,IAAIH,GAAG,EAAE;IACnB,IAAI,CAAC4E,YAAY,CAACC,CAAC,EAAE1E,EAAE,CAAC,EAAE;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMgL,kBAAkB,CAACtG,CAAC,EAAE1E,EAAE,CAAC;IAChC;EACD;EACA;EACA,IAAI0E,CAAC,CAACwB,CAAC,EAAE;IAAE;IACV,IAAIlD,UAAU,GAAGgB,GAAG,CAACnE,GAAG,CAAC;IACzB,KAAK,IAAI3E,CAAC,GAAG,CAAC,EAAEoQ,CAAC,GAAGtI,UAAU,CAACzF,MAAM,EAAErC,CAAC,GAAGoQ,CAAC,EAAEpQ,CAAC,EAAE,EAAE;MAAE;MACpD;MACA;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACG;MACA;MACA,IAAI+J,GAAG,CAACzC,GAAG,CAACQ,UAAU,CAAC9H,CAAC,CAAC,CAAC,EAAE;QAC3B,IAAI0D,CAAC,GAAG1D,CAAC,GAAG,CAAC;QACb,KAAK,IAAI8E,EAAE,EAAEpB,CAAC,GAAG0M,CAAC,IAAIrG,GAAG,CAACzC,GAAG,CAACxC,EAAE,GAAGgD,UAAU,CAACpE,CAAC,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;UACvD;UACA,KAAK,IAAI2M,CAAC,GAAGrQ,CAAC,EAAEqQ,CAAC,GAAG3M,CAAC,EAAE2M,CAAC,EAAE,EAAE;YAAE;YAC7B,IAAIvI,UAAU,CAACuI,CAAC,CAAC,IAAIvL,EAAE,EAAE;cACxB,MAAM,IAAI2H,KAAK,CAAC,gCAAgCL,SAAS,CAACtH,EAAE,CAAC,EAAE,CAAC;YACjE;UACD;QACD;QACA;QACA;QACA,IAAIpB,CAAC,GAAG1D,CAAC,GAAGR,OAAO,EAAE;UACpB;UACA,MAAM,IAAIiN,KAAK,CAAC,gCAAgCH,OAAO,CAACC,iBAAiB,CAACzE,UAAU,CAAC3D,KAAK,CAACnE,CAAC,GAAC,CAAC,EAAE0D,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,GAAC1D,CAAC,IAAIR,OAAO,GAAG,CAAC;QAC5H;QACAQ,CAAC,GAAG0D,CAAC;MACN;IACD;EACD;EACA;EACA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASkK,eAAeA,CAACe,KAAK,EAAElB,EAAE,EAAEiB,EAAE,EAAE;EACvC,IAAIpM,GAAG,GAAG,EAAE;EACZ,IAAI4M,KAAK,GAAG,EAAE;EACdP,KAAK,GAAGA,KAAK,CAACxK,KAAK,CAAC,CAAC,CAACuD,OAAO,CAAC,CAAC,CAAC,CAAC;EACjC,OAAOiH,KAAK,CAACtM,MAAM,EAAE;IACpB,IAAI2M,KAAK,GAAGsB,sBAAsB,CAAC3B,KAAK,CAAC;IACzC,IAAIK,KAAK,EAAE;MACV,IAAIE,KAAK,CAAC7M,MAAM,EAAE;QACjBC,GAAG,CAACrC,IAAI,CAACwN,EAAE,CAACyB,KAAK,CAAC,CAAC;QACnBA,KAAK,GAAG,EAAE;MACX;MACA5M,GAAG,CAACrC,IAAI,CAACyO,EAAE,CAACM,KAAK,CAAC,CAAC;IACpB,CAAC,MAAM;MACN,IAAIlK,EAAE,GAAG6J,KAAK,CAACpG,GAAG,CAAC,CAAC;MACpB,IAAI8B,KAAK,CAAC/C,GAAG,CAACxC,EAAE,CAAC,EAAE;QAClBoK,KAAK,CAACjP,IAAI,CAAC6E,EAAE,CAAC;MACf,CAAC,MAAM;QACN,IAAIH,GAAG,GAAGiF,MAAM,CAACpC,GAAG,CAAC1C,EAAE,CAAC;QACxB,IAAIH,GAAG,EAAE;UACRuK,KAAK,CAACjP,IAAI,CAAC,GAAG0E,GAAG,CAAC,CAAC,CAAC;QACrB,CAAC,MAAM,IAAI,CAACkF,OAAO,CAACvC,GAAG,CAACxC,EAAE,CAAC,EAAE;UAC5B;UACA;UACA;UACA;UACA;UACA;UACA,MAAM+K,gBAAgB,CAAC/K,EAAE,CAAC;QAC3B;MACD;IACD;EACD;EACA,IAAIoK,KAAK,CAAC7M,MAAM,EAAE;IACjBC,GAAG,CAACrC,IAAI,CAACwN,EAAE,CAACyB,KAAK,CAAC,CAAC;EACpB;EACA,OAAO5M,GAAG;AACX;AAEA,SAASuL,WAAWA,CAAClJ,GAAG,EAAE;EACzB,OAAOA,GAAG,CAAC8G,MAAM,CAAC3G,EAAE,IAAIA,EAAE,IAAIqE,IAAI,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmH,sBAAsBA,CAAC3L,GAAG,EAAE4L,KAAK,EAAE;EAC3C,IAAIrE,IAAI,GAAG3B,UAAU;EACrB,IAAIyE,KAAK;EACT,IAAIrP,GAAG,GAAGgF,GAAG,CAACtC,MAAM;EACpB,OAAO1C,GAAG,EAAE;IACXuM,IAAI,GAAGA,IAAI,CAAC1E,GAAG,CAAC7C,GAAG,CAAC,EAAEhF,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACuM,IAAI,EAAE;IACX,IAAI;MAACb;IAAC,CAAC,GAAGa,IAAI;IACd,IAAIb,CAAC,EAAE;MAAE;MACR2D,KAAK,GAAG3D,CAAC;MACT,IAAIkF,KAAK,EAAEA,KAAK,CAACtQ,IAAI,CAAC,GAAG0E,GAAG,CAACR,KAAK,CAACxE,GAAG,CAAC,CAAC+H,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD/C,GAAG,CAACtC,MAAM,GAAG1C,GAAG,CAAC,CAAC;IACnB;EACD;EACA,OAAOqP,KAAK;AACb;;AAEA;AACA;;AAEA,MAAMwB,QAAQ,GAAG,OAAO;AACxB,MAAMC,SAAS,GAAG,QAAQ;AAC1B,MAAMC,UAAU,GAAG,SAAS;AAC5B,MAAMC,aAAa,GAAG,YAAY;AAClC,MAAMC,QAAQ,GAAG,OAAO;AACxB,MAAMC,MAAM,GAAG,KAAK;AACpB,MAAMC,OAAO,GAAG,MAAM;AAEtB,SAASC,YAAYA,CAAChD,IAAI,EAAE;EAC3BN,EAAE,GAAG,IAAI,CAAE;AACZ,CAAC,GAAG,CAAC,CAAC,EAAE;EACPjD,IAAI,CAAC,CAAC;EACN,IAAImE,KAAK,GAAGvJ,UAAU,CAAC2I,IAAI,CAAC,CAACrG,OAAO,CAAC,CAAC;EACtC,IAAI6I,KAAK,GAAG,EAAE;EACd,IAAI1B,MAAM,GAAG,EAAE;EACf,OAAOF,KAAK,CAACtM,MAAM,EAAE;IACpB,IAAI2M,KAAK,GAAGsB,sBAAsB,CAAC3B,KAAK,EAAE4B,KAAK,CAAC;IAChD,IAAIvB,KAAK,EAAE;MACVH,MAAM,CAAC5O,IAAI,CAAC;QACXkO,IAAI,EAAEyC,QAAQ;QACd5B,KAAK,EAAEA,KAAK,CAAC7K,KAAK,CAAC,CAAC;QAAE;QACtBwK,KAAK,EAAE4B,KAAK;QACZ5L,GAAG,EAAEkJ,WAAW,CAACmB,KAAK;MACvB,CAAC,CAAC;MACFuB,KAAK,GAAG,EAAE,CAAC,CAAC;IACb,CAAC,MAAM;MACN,IAAIzL,EAAE,GAAG6J,KAAK,CAACpG,GAAG,CAAC,CAAC;MACpB,IAAIzD,EAAE,IAAImE,IAAI,EAAE;QACf4F,MAAM,CAAC5O,IAAI,CAAC;UAACkO,IAAI,EAAE2C,OAAO;UAAEhM;QAAE,CAAC,CAAC;MACjC,CAAC,MAAM,IAAIuF,KAAK,CAAC/C,GAAG,CAACxC,EAAE,CAAC,EAAE;QACzB+J,MAAM,CAAC5O,IAAI,CAAC;UAACkO,IAAI,EAAEqC,QAAQ;UAAE7L,GAAG,EAAE,CAACG,EAAE;QAAC,CAAC,CAAC;MACzC,CAAC,MAAM,IAAI+E,OAAO,CAACvC,GAAG,CAACxC,EAAE,CAAC,EAAE;QAC3B+J,MAAM,CAAC5O,IAAI,CAAC;UAACkO,IAAI,EAAEuC,UAAU;UAAE5L;QAAE,CAAC,CAAC;MACpC,CAAC,MAAM;QACN,IAAIH,GAAG,GAAGiF,MAAM,CAACpC,GAAG,CAAC1C,EAAE,CAAC;QACxB,IAAIH,GAAG,EAAE;UACRkK,MAAM,CAAC5O,IAAI,CAAC;YAACkO,IAAI,EAAEsC,SAAS;YAAE3L,EAAE;YAAEH,GAAG,EAAEA,GAAG,CAACR,KAAK,CAAC;UAAC,CAAC,CAAC;QACrD,CAAC,MAAM;UACN0K,MAAM,CAAC5O,IAAI,CAAC;YAACkO,IAAI,EAAEwC,aAAa;YAAE7L;UAAE,CAAC,CAAC;QACvC;MACD;IACD;EACD;EACA,IAAI2I,EAAE,EAAE;IACP,KAAK,IAAIzN,CAAC,GAAG,CAAC,EAAEmB,KAAK,GAAG,CAAC,CAAC,EAAEnB,CAAC,GAAG6O,MAAM,CAACxM,MAAM,EAAErC,CAAC,EAAE,EAAE;MACnD,IAAIgR,KAAK,GAAGnC,MAAM,CAAC7O,CAAC,CAAC;MACrB,IAAIiR,kBAAkB,CAACD,KAAK,CAAC7C,IAAI,CAAC,EAAE;QACnC,IAAI+C,cAAc,CAACF,KAAK,CAACrM,GAAG,CAAC,EAAE;UAAE;UAChC,IAAIvD,GAAG,GAAGpB,CAAC,GAAG,CAAC;UACf,KAAK,IAAIL,GAAG,GAAGyB,GAAG,EAAEzB,GAAG,GAAGkP,MAAM,CAACxM,MAAM,EAAE1C,GAAG,EAAE,EAAE;YAAE;YACjD,IAAI;cAACwO,IAAI;cAAExJ;YAAG,CAAC,GAAGkK,MAAM,CAAClP,GAAG,CAAC;YAC7B,IAAIsR,kBAAkB,CAAC9C,IAAI,CAAC,EAAE;cAC7B,IAAI,CAAC+C,cAAc,CAACvM,GAAG,CAAC,EAAE;cAC1BvD,GAAG,GAAGzB,GAAG,GAAG,CAAC;YACd,CAAC,MAAM,IAAIwO,IAAI,KAAKuC,UAAU,EAAE;cAAE;cACjC;YACD;UACD;UACA,IAAIvP,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGnB,CAAC;UACxB,IAAImE,KAAK,GAAG0K,MAAM,CAAC1K,KAAK,CAAChD,KAAK,EAAEC,GAAG,CAAC;UACpC,IAAI+P,IAAI,GAAGhN,KAAK,CAACL,OAAO,CAACpC,CAAC,IAAIuP,kBAAkB,CAACvP,CAAC,CAACyM,IAAI,CAAC,GAAGzM,CAAC,CAACiD,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;UACxE,IAAIA,GAAG,GAAGoE,GAAG,CAACoI,IAAI,CAAC;UACnB,IAAItL,cAAc,CAAClB,GAAG,EAAEwM,IAAI,CAAC,EAAE;YAAE;YAChCtC,MAAM,CAACuC,MAAM,CAACjQ,KAAK,EAAEC,GAAG,GAAGD,KAAK,EAAE;cACjCgN,IAAI,EAAE0C,MAAM;cACZlC,KAAK,EAAEwC,IAAI;cAAE;cACbxM,GAAG;cACH0M,OAAO,EAAEC,qBAAqB,CAACnN,KAAK,CAAC;cACrC0K,MAAM,EAAEkC,YAAY,CAACxL,YAAY,CAACZ,GAAG,CAAC,EAAE;gBAAC8I,EAAE,EAAE;cAAK,CAAC;YACpD,CAAC,CAAC;YACFzN,CAAC,GAAGmB,KAAK;UACV,CAAC,MAAM;YACNnB,CAAC,GAAGoB,GAAG,GAAG,CAAC,CAAC,CAAC;UACd;UACAD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,MAAM;UACNA,KAAK,GAAGnB,CAAC,CAAC,CAAC;QACZ;MACD,CAAC,MAAM,IAAIgR,KAAK,CAAC7C,IAAI,KAAKuC,UAAU,EAAE;QAAE;QACvCvP,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;MACb;IACD;EACD;EACA,OAAOmQ,qBAAqB,CAACzC,MAAM,CAAC;AACrC;AAEA,SAASoC,kBAAkBA,CAAC9C,IAAI,EAAE;EACjC,OAAOA,IAAI,IAAIqC,QAAQ,IAAIrC,IAAI,IAAIsC,SAAS;AAC7C;AAEA,SAASS,cAAcA,CAACvM,GAAG,EAAE;EAC5B,OAAOA,GAAG,CAACkH,IAAI,CAAC/G,EAAE,IAAImF,SAAS,CAAC3C,GAAG,CAACxC,EAAE,CAAC,CAAC;AACzC;AAEA,SAASwM,qBAAqBA,CAACzC,MAAM,EAAE;EACtC,KAAK,IAAI7O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6O,MAAM,CAACxM,MAAM,EAAErC,CAAC,EAAE,EAAE;IACvC,IAAI6O,MAAM,CAAC7O,CAAC,CAAC,CAACmO,IAAI,IAAIqC,QAAQ,EAAE;MAC/B,IAAI9M,CAAC,GAAG1D,CAAC,GAAG,CAAC;MACb,OAAO0D,CAAC,GAAGmL,MAAM,CAACxM,MAAM,IAAIwM,MAAM,CAACnL,CAAC,CAAC,CAACyK,IAAI,IAAIqC,QAAQ,EAAE9M,CAAC,EAAE;MAC3DmL,MAAM,CAACuC,MAAM,CAACpR,CAAC,EAAE0D,CAAC,GAAG1D,CAAC,EAAE;QAACmO,IAAI,EAAEqC,QAAQ;QAAE7L,GAAG,EAAEkK,MAAM,CAAC1K,KAAK,CAACnE,CAAC,EAAE0D,CAAC,CAAC,CAACI,OAAO,CAACpC,CAAC,IAAIA,CAAC,CAACiD,GAAG;MAAC,CAAC,CAAC;IACvF;EACD;EACA,OAAOkK,MAAM;AACd;AAEA,SAASZ,YAAY,EAAEZ,SAAS,EAAES,aAAa,EAAER,sBAAsB,EAAEkB,SAAS,EAAEuC,YAAY,EAAE3D,iBAAiB,EAAErE,GAAG,EAAED,GAAG,EAAEyD,iBAAiB,EAAEF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}