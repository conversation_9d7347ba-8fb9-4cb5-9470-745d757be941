{"ast": null, "code": "import { addDomEvent } from '../events/add-dom-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { pipe } from '../utils/pipe.mjs';\nclass FocusGesture extends Feature {\n  constructor() {\n    super(...arguments);\n    this.isActive = false;\n  }\n  onFocus() {\n    let isFocusVisible = false;\n    /**\n     * If this element doesn't match focus-visible then don't\n     * apply whileHover. But, if matches throws that focus-visible\n     * is not a valid selector then in that browser outline styles will be applied\n     * to the element by default and we want to match that behaviour with whileFocus.\n     */\n    try {\n      isFocusVisible = this.node.current.matches(\":focus-visible\");\n    } catch (e) {\n      isFocusVisible = true;\n    }\n    if (!isFocusVisible || !this.node.animationState) return;\n    this.node.animationState.setActive(\"whileFocus\", true);\n    this.isActive = true;\n  }\n  onBlur() {\n    if (!this.isActive || !this.node.animationState) return;\n    this.node.animationState.setActive(\"whileFocus\", false);\n    this.isActive = false;\n  }\n  mount() {\n    this.unmount = pipe(addDomEvent(this.node.current, \"focus\", () => this.onFocus()), addDomEvent(this.node.current, \"blur\", () => this.onBlur()));\n  }\n  unmount() {}\n}\nexport { FocusGesture };", "map": {"version": 3, "names": ["addDomEvent", "Feature", "pipe", "FocusGesture", "constructor", "arguments", "isActive", "onFocus", "isFocusVisible", "node", "current", "matches", "e", "animationState", "setActive", "onBlur", "mount", "unmount"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/gestures/focus.mjs"], "sourcesContent": ["import { addDomEvent } from '../events/add-dom-event.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nimport { pipe } from '../utils/pipe.mjs';\n\nclass FocusGesture extends Feature {\n    constructor() {\n        super(...arguments);\n        this.isActive = false;\n    }\n    onFocus() {\n        let isFocusVisible = false;\n        /**\n         * If this element doesn't match focus-visible then don't\n         * apply whileHover. But, if matches throws that focus-visible\n         * is not a valid selector then in that browser outline styles will be applied\n         * to the element by default and we want to match that behaviour with whileFocus.\n         */\n        try {\n            isFocusVisible = this.node.current.matches(\":focus-visible\");\n        }\n        catch (e) {\n            isFocusVisible = true;\n        }\n        if (!isFocusVisible || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", true);\n        this.isActive = true;\n    }\n    onBlur() {\n        if (!this.isActive || !this.node.animationState)\n            return;\n        this.node.animationState.setActive(\"whileFocus\", false);\n        this.isActive = false;\n    }\n    mount() {\n        this.unmount = pipe(addDomEvent(this.node.current, \"focus\", () => this.onFocus()), addDomEvent(this.node.current, \"blur\", () => this.onBlur()));\n    }\n    unmount() { }\n}\n\nexport { FocusGesture };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,6BAA6B;AACzD,SAASC,OAAO,QAAQ,gCAAgC;AACxD,SAASC,IAAI,QAAQ,mBAAmB;AAExC,MAAMC,YAAY,SAASF,OAAO,CAAC;EAC/BG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;EACzB;EACAC,OAAOA,CAAA,EAAG;IACN,IAAIC,cAAc,GAAG,KAAK;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI;MACAA,cAAc,GAAG,IAAI,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAChE,CAAC,CACD,OAAOC,CAAC,EAAE;MACNJ,cAAc,GAAG,IAAI;IACzB;IACA,IAAI,CAACA,cAAc,IAAI,CAAC,IAAI,CAACC,IAAI,CAACI,cAAc,EAC5C;IACJ,IAAI,CAACJ,IAAI,CAACI,cAAc,CAACC,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC;IACtD,IAAI,CAACR,QAAQ,GAAG,IAAI;EACxB;EACAS,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC,IAAI,CAACT,QAAQ,IAAI,CAAC,IAAI,CAACG,IAAI,CAACI,cAAc,EAC3C;IACJ,IAAI,CAACJ,IAAI,CAACI,cAAc,CAACC,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC;IACvD,IAAI,CAACR,QAAQ,GAAG,KAAK;EACzB;EACAU,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,OAAO,GAAGf,IAAI,CAACF,WAAW,CAAC,IAAI,CAACS,IAAI,CAACC,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAACH,OAAO,CAAC,CAAC,CAAC,EAAEP,WAAW,CAAC,IAAI,CAACS,IAAI,CAACC,OAAO,EAAE,MAAM,EAAE,MAAM,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EACnJ;EACAE,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASd,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}