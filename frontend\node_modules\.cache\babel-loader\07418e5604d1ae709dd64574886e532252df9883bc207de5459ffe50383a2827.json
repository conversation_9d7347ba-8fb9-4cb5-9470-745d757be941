{"ast": null, "code": "import { keccak256 } from \"../crypto/index.js\";\nimport { getBytes, assertArgument } from \"../utils/index.js\";\nconst BN_0 = BigInt(0);\nconst BN_36 = BigInt(36);\nfunction getChecksumAddress(address) {\n  //    if (!isHexString(address, 20)) {\n  //        logger.throwArgumentError(\"invalid address\", \"address\", address);\n  //    }\n  address = address.toLowerCase();\n  const chars = address.substring(2).split(\"\");\n  const expanded = new Uint8Array(40);\n  for (let i = 0; i < 40; i++) {\n    expanded[i] = chars[i].charCodeAt(0);\n  }\n  const hashed = getBytes(keccak256(expanded));\n  for (let i = 0; i < 40; i += 2) {\n    if (hashed[i >> 1] >> 4 >= 8) {\n      chars[i] = chars[i].toUpperCase();\n    }\n    if ((hashed[i >> 1] & 0x0f) >= 8) {\n      chars[i + 1] = chars[i + 1].toUpperCase();\n    }\n  }\n  return \"0x\" + chars.join(\"\");\n}\n// See: https://en.wikipedia.org/wiki/International_Bank_Account_Number\n// Create lookup table\nconst ibanLookup = {};\nfor (let i = 0; i < 10; i++) {\n  ibanLookup[String(i)] = String(i);\n}\nfor (let i = 0; i < 26; i++) {\n  ibanLookup[String.fromCharCode(65 + i)] = String(10 + i);\n}\n// How many decimal digits can we process? (for 64-bit float, this is 15)\n// i.e. Math.floor(Math.log10(Number.MAX_SAFE_INTEGER));\nconst safeDigits = 15;\nfunction ibanChecksum(address) {\n  address = address.toUpperCase();\n  address = address.substring(4) + address.substring(0, 2) + \"00\";\n  let expanded = address.split(\"\").map(c => {\n    return ibanLookup[c];\n  }).join(\"\");\n  // Javascript can handle integers safely up to 15 (decimal) digits\n  while (expanded.length >= safeDigits) {\n    let block = expanded.substring(0, safeDigits);\n    expanded = parseInt(block, 10) % 97 + expanded.substring(block.length);\n  }\n  let checksum = String(98 - parseInt(expanded, 10) % 97);\n  while (checksum.length < 2) {\n    checksum = \"0\" + checksum;\n  }\n  return checksum;\n}\n;\nconst Base36 = function () {\n  ;\n  const result = {};\n  for (let i = 0; i < 36; i++) {\n    const key = \"0123456789abcdefghijklmnopqrstuvwxyz\"[i];\n    result[key] = BigInt(i);\n  }\n  return result;\n}();\nfunction fromBase36(value) {\n  value = value.toLowerCase();\n  let result = BN_0;\n  for (let i = 0; i < value.length; i++) {\n    result = result * BN_36 + Base36[value[i]];\n  }\n  return result;\n}\n/**\n *  Returns a normalized and checksumed address for %%address%%.\n *  This accepts non-checksum addresses, checksum addresses and\n *  [[getIcapAddress]] formats.\n *\n *  The checksum in Ethereum uses the capitalization (upper-case\n *  vs lower-case) of the characters within an address to encode\n *  its checksum, which offers, on average, a checksum of 15-bits.\n *\n *  If %%address%% contains both upper-case and lower-case, it is\n *  assumed to already be a checksum address and its checksum is\n *  validated, and if the address fails its expected checksum an\n *  error is thrown.\n *\n *  If you wish the checksum of %%address%% to be ignore, it should\n *  be converted to lower-case (i.e. ``.toLowercase()``) before\n *  being passed in. This should be a very rare situation though,\n *  that you wish to bypass the safegaurds in place to protect\n *  against an address that has been incorrectly copied from another\n *  source.\n *\n *  @example:\n *    // Adds the checksum (via upper-casing specific letters)\n *    getAddress(\"******************************************\")\n *    //_result:\n *\n *    // Converts ICAP address and adds checksum\n *    getAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\");\n *    //_result:\n *\n *    // Throws an error if an address contains mixed case,\n *    // but the checksum fails\n *    getAddress(\"0x8Ba1f109551bD432803012645Ac136ddd64DBA72\")\n *    //_error:\n */\nexport function getAddress(address) {\n  assertArgument(typeof address === \"string\", \"invalid address\", \"address\", address);\n  if (address.match(/^(0x)?[0-9a-fA-F]{40}$/)) {\n    // Missing the 0x prefix\n    if (!address.startsWith(\"0x\")) {\n      address = \"0x\" + address;\n    }\n    const result = getChecksumAddress(address);\n    // It is a checksummed address with a bad checksum\n    assertArgument(!address.match(/([A-F].*[a-f])|([a-f].*[A-F])/) || result === address, \"bad address checksum\", \"address\", address);\n    return result;\n  }\n  // Maybe ICAP? (we only support direct mode)\n  if (address.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)) {\n    // It is an ICAP address with a bad checksum\n    assertArgument(address.substring(2, 4) === ibanChecksum(address), \"bad icap checksum\", \"address\", address);\n    let result = fromBase36(address.substring(4)).toString(16);\n    while (result.length < 40) {\n      result = \"0\" + result;\n    }\n    return getChecksumAddress(\"0x\" + result);\n  }\n  assertArgument(false, \"invalid address\", \"address\", address);\n}\n/**\n *  The [ICAP Address format](link-icap) format is an early checksum\n *  format which attempts to be compatible with the banking\n *  industry [IBAN format](link-wiki-iban) for bank accounts.\n *\n *  It is no longer common or a recommended format.\n *\n *  @example:\n *    getIcapAddress(\"******************************************\");\n *    //_result:\n *\n *    getIcapAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\");\n *    //_result:\n *\n *    // Throws an error if the ICAP checksum is wrong\n *    getIcapAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK37\");\n *    //_error:\n */\nexport function getIcapAddress(address) {\n  //let base36 = _base16To36(getAddress(address).substring(2)).toUpperCase();\n  let base36 = BigInt(getAddress(address)).toString(36).toUpperCase();\n  while (base36.length < 30) {\n    base36 = \"0\" + base36;\n  }\n  return \"XE\" + ibanChecksum(\"XE00\" + base36) + base36;\n}", "map": {"version": 3, "names": ["keccak256", "getBytes", "assertArgument", "BN_0", "BigInt", "BN_36", "getChecksumAddress", "address", "toLowerCase", "chars", "substring", "split", "expanded", "Uint8Array", "i", "charCodeAt", "hashed", "toUpperCase", "join", "ibanLookup", "String", "fromCharCode", "safeDigits", "ibanChecksum", "map", "c", "length", "block", "parseInt", "checksum", "Base36", "result", "key", "fromBase36", "value", "get<PERSON><PERSON><PERSON>", "match", "startsWith", "toString", "getIcapAddress", "base36"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\address\\address.ts"], "sourcesContent": ["import { keccak256 } from \"../crypto/index.js\";\nimport { getBytes, assertArgument } from \"../utils/index.js\";\n\n\nconst BN_0 = BigInt(0);\nconst BN_36 = BigInt(36);\n\nfunction getChecksumAddress(address: string): string {\n//    if (!isHexString(address, 20)) {\n//        logger.throwArgumentError(\"invalid address\", \"address\", address);\n//    }\n\n    address = address.toLowerCase();\n\n    const chars = address.substring(2).split(\"\");\n\n    const expanded = new Uint8Array(40);\n    for (let i = 0; i < 40; i++) {\n        expanded[i] = chars[i].charCodeAt(0);\n    }\n\n    const hashed = getBytes(keccak256(expanded));\n\n    for (let i = 0; i < 40; i += 2) {\n        if ((hashed[i >> 1] >> 4) >= 8) {\n            chars[i] = chars[i].toUpperCase();\n        }\n        if ((hashed[i >> 1] & 0x0f) >= 8) {\n            chars[i + 1] = chars[i + 1].toUpperCase();\n        }\n    }\n\n    return \"0x\" + chars.join(\"\");\n}\n\n// See: https://en.wikipedia.org/wiki/International_Bank_Account_Number\n\n// Create lookup table\nconst ibanLookup: { [character: string]: string } = { };\nfor (let i = 0; i < 10; i++) { ibanLookup[String(i)] = String(i); }\nfor (let i = 0; i < 26; i++) { ibanLookup[String.fromCharCode(65 + i)] = String(10 + i); }\n\n// How many decimal digits can we process? (for 64-bit float, this is 15)\n// i.e. Math.floor(Math.log10(Number.MAX_SAFE_INTEGER));\nconst safeDigits = 15;\n\nfunction ibanChecksum(address: string): string {\n    address = address.toUpperCase();\n    address = address.substring(4) + address.substring(0, 2) + \"00\";\n\n    let expanded = address.split(\"\").map((c) => { return ibanLookup[c]; }).join(\"\");\n\n    // Javascript can handle integers safely up to 15 (decimal) digits\n    while (expanded.length >= safeDigits){\n        let block = expanded.substring(0, safeDigits);\n        expanded = parseInt(block, 10) % 97 + expanded.substring(block.length);\n    }\n\n    let checksum = String(98 - (parseInt(expanded, 10) % 97));\n    while (checksum.length < 2) { checksum = \"0\" + checksum; }\n\n    return checksum;\n};\n\nconst Base36 = (function() {;\n    const result: Record<string, bigint> = { };\n    for (let i = 0; i < 36; i++) {\n        const key = \"0123456789abcdefghijklmnopqrstuvwxyz\"[i];\n        result[key] = BigInt(i);\n    }\n    return result;\n})();\n\nfunction fromBase36(value: string): bigint {\n    value = value.toLowerCase();\n\n    let result = BN_0;\n    for (let i = 0; i < value.length; i++) {\n        result = result * BN_36 + Base36[value[i]];\n    }\n    return result;\n}\n\n/**\n *  Returns a normalized and checksumed address for %%address%%.\n *  This accepts non-checksum addresses, checksum addresses and\n *  [[getIcapAddress]] formats.\n *\n *  The checksum in Ethereum uses the capitalization (upper-case\n *  vs lower-case) of the characters within an address to encode\n *  its checksum, which offers, on average, a checksum of 15-bits.\n *\n *  If %%address%% contains both upper-case and lower-case, it is\n *  assumed to already be a checksum address and its checksum is\n *  validated, and if the address fails its expected checksum an\n *  error is thrown.\n *\n *  If you wish the checksum of %%address%% to be ignore, it should\n *  be converted to lower-case (i.e. ``.toLowercase()``) before\n *  being passed in. This should be a very rare situation though,\n *  that you wish to bypass the safegaurds in place to protect\n *  against an address that has been incorrectly copied from another\n *  source.\n *\n *  @example:\n *    // Adds the checksum (via upper-casing specific letters)\n *    getAddress(\"******************************************\")\n *    //_result:\n *\n *    // Converts ICAP address and adds checksum\n *    getAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\");\n *    //_result:\n *\n *    // Throws an error if an address contains mixed case,\n *    // but the checksum fails\n *    getAddress(\"0x8Ba1f109551bD432803012645Ac136ddd64DBA72\")\n *    //_error:\n */\nexport function getAddress(address: string): string {\n\n    assertArgument(typeof(address) === \"string\", \"invalid address\", \"address\", address);\n\n    if (address.match(/^(0x)?[0-9a-fA-F]{40}$/)) {\n\n        // Missing the 0x prefix\n        if (!address.startsWith(\"0x\")) { address = \"0x\" + address; }\n\n        const result = getChecksumAddress(address);\n\n        // It is a checksummed address with a bad checksum\n        assertArgument(!address.match(/([A-F].*[a-f])|([a-f].*[A-F])/) || result === address,\n            \"bad address checksum\", \"address\", address);\n\n        return result;\n    }\n\n    // Maybe ICAP? (we only support direct mode)\n    if (address.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)) {\n        // It is an ICAP address with a bad checksum\n        assertArgument(address.substring(2, 4) === ibanChecksum(address), \"bad icap checksum\", \"address\", address);\n\n        let result = fromBase36(address.substring(4)).toString(16);\n        while (result.length < 40) { result = \"0\" + result; }\n        return  getChecksumAddress(\"0x\" + result);\n    }\n\n    assertArgument(false, \"invalid address\", \"address\", address);\n}\n\n/**\n *  The [ICAP Address format](link-icap) format is an early checksum\n *  format which attempts to be compatible with the banking\n *  industry [IBAN format](link-wiki-iban) for bank accounts.\n *\n *  It is no longer common or a recommended format.\n *\n *  @example:\n *    getIcapAddress(\"******************************************\");\n *    //_result:\n *\n *    getIcapAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\");\n *    //_result:\n *\n *    // Throws an error if the ICAP checksum is wrong\n *    getIcapAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK37\");\n *    //_error:\n */\nexport function getIcapAddress(address: string): string {\n    //let base36 = _base16To36(getAddress(address).substring(2)).toUpperCase();\n    let base36 = BigInt(getAddress(address)).toString(36).toUpperCase();\n    while (base36.length < 30) { base36 = \"0\" + base36; }\n    return \"XE\" + ibanChecksum(\"XE00\" + base36) + base36;\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,QAAQ,EAAEC,cAAc,QAAQ,mBAAmB;AAG5D,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMC,KAAK,GAAGD,MAAM,CAAC,EAAE,CAAC;AAExB,SAASE,kBAAkBA,CAACC,OAAe;EAC3C;EACA;EACA;EAEIA,OAAO,GAAGA,OAAO,CAACC,WAAW,EAAE;EAE/B,MAAMC,KAAK,GAAGF,OAAO,CAACG,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC;EAE5C,MAAMC,QAAQ,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACzBF,QAAQ,CAACE,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;;EAGxC,MAAMC,MAAM,GAAGf,QAAQ,CAACD,SAAS,CAACY,QAAQ,CAAC,CAAC;EAE5C,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAKE,MAAM,CAACF,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAK,CAAC,EAAE;MAC5BL,KAAK,CAACK,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,CAAC,CAACG,WAAW,EAAE;;IAErC,IAAI,CAACD,MAAM,CAACF,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;MAC9BL,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,GAAGL,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,CAACG,WAAW,EAAE;;;EAIjD,OAAO,IAAI,GAAGR,KAAK,CAACS,IAAI,CAAC,EAAE,CAAC;AAChC;AAEA;AAEA;AACA,MAAMC,UAAU,GAAoC,EAAG;AACvD,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;EAAEK,UAAU,CAACC,MAAM,CAACN,CAAC,CAAC,CAAC,GAAGM,MAAM,CAACN,CAAC,CAAC;;AAChE,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;EAAEK,UAAU,CAACC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGP,CAAC,CAAC,CAAC,GAAGM,MAAM,CAAC,EAAE,GAAGN,CAAC,CAAC;;AAEvF;AACA;AACA,MAAMQ,UAAU,GAAG,EAAE;AAErB,SAASC,YAAYA,CAAChB,OAAe;EACjCA,OAAO,GAAGA,OAAO,CAACU,WAAW,EAAE;EAC/BV,OAAO,GAAGA,OAAO,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI;EAE/D,IAAIE,QAAQ,GAAGL,OAAO,CAACI,KAAK,CAAC,EAAE,CAAC,CAACa,GAAG,CAAEC,CAAC,IAAI;IAAG,OAAON,UAAU,CAACM,CAAC,CAAC;EAAE,CAAC,CAAC,CAACP,IAAI,CAAC,EAAE,CAAC;EAE/E;EACA,OAAON,QAAQ,CAACc,MAAM,IAAIJ,UAAU,EAAC;IACjC,IAAIK,KAAK,GAAGf,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAEY,UAAU,CAAC;IAC7CV,QAAQ,GAAGgB,QAAQ,CAACD,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,GAAGf,QAAQ,CAACF,SAAS,CAACiB,KAAK,CAACD,MAAM,CAAC;;EAG1E,IAAIG,QAAQ,GAAGT,MAAM,CAAC,EAAE,GAAIQ,QAAQ,CAAChB,QAAQ,EAAE,EAAE,CAAC,GAAG,EAAG,CAAC;EACzD,OAAOiB,QAAQ,CAACH,MAAM,GAAG,CAAC,EAAE;IAAEG,QAAQ,GAAG,GAAG,GAAGA,QAAQ;;EAEvD,OAAOA,QAAQ;AACnB;AAAC;AAED,MAAMC,MAAM,GAAI;EAAY;EACxB,MAAMC,MAAM,GAA2B,EAAG;EAC1C,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACzB,MAAMkB,GAAG,GAAG,sCAAsC,CAAClB,CAAC,CAAC;IACrDiB,MAAM,CAACC,GAAG,CAAC,GAAG5B,MAAM,CAACU,CAAC,CAAC;;EAE3B,OAAOiB,MAAM;AACjB,CAAC,CAAC,CAAE;AAEJ,SAASE,UAAUA,CAACC,KAAa;EAC7BA,KAAK,GAAGA,KAAK,CAAC1B,WAAW,EAAE;EAE3B,IAAIuB,MAAM,GAAG5B,IAAI;EACjB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,KAAK,CAACR,MAAM,EAAEZ,CAAC,EAAE,EAAE;IACnCiB,MAAM,GAAGA,MAAM,GAAG1B,KAAK,GAAGyB,MAAM,CAACI,KAAK,CAACpB,CAAC,CAAC,CAAC;;EAE9C,OAAOiB,MAAM;AACjB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAM,SAAUI,UAAUA,CAAC5B,OAAe;EAEtCL,cAAc,CAAC,OAAOK,OAAQ,KAAK,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAEA,OAAO,CAAC;EAEnF,IAAIA,OAAO,CAAC6B,KAAK,CAAC,wBAAwB,CAAC,EAAE;IAEzC;IACA,IAAI,CAAC7B,OAAO,CAAC8B,UAAU,CAAC,IAAI,CAAC,EAAE;MAAE9B,OAAO,GAAG,IAAI,GAAGA,OAAO;;IAEzD,MAAMwB,MAAM,GAAGzB,kBAAkB,CAACC,OAAO,CAAC;IAE1C;IACAL,cAAc,CAAC,CAACK,OAAO,CAAC6B,KAAK,CAAC,+BAA+B,CAAC,IAAIL,MAAM,KAAKxB,OAAO,EAChF,sBAAsB,EAAE,SAAS,EAAEA,OAAO,CAAC;IAE/C,OAAOwB,MAAM;;EAGjB;EACA,IAAIxB,OAAO,CAAC6B,KAAK,CAAC,gCAAgC,CAAC,EAAE;IACjD;IACAlC,cAAc,CAACK,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAKa,YAAY,CAAChB,OAAO,CAAC,EAAE,mBAAmB,EAAE,SAAS,EAAEA,OAAO,CAAC;IAE1G,IAAIwB,MAAM,GAAGE,UAAU,CAAC1B,OAAO,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC4B,QAAQ,CAAC,EAAE,CAAC;IAC1D,OAAOP,MAAM,CAACL,MAAM,GAAG,EAAE,EAAE;MAAEK,MAAM,GAAG,GAAG,GAAGA,MAAM;;IAClD,OAAQzB,kBAAkB,CAAC,IAAI,GAAGyB,MAAM,CAAC;;EAG7C7B,cAAc,CAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAEK,OAAO,CAAC;AAChE;AAEA;;;;;;;;;;;;;;;;;;AAkBA,OAAM,SAAUgC,cAAcA,CAAChC,OAAe;EAC1C;EACA,IAAIiC,MAAM,GAAGpC,MAAM,CAAC+B,UAAU,CAAC5B,OAAO,CAAC,CAAC,CAAC+B,QAAQ,CAAC,EAAE,CAAC,CAACrB,WAAW,EAAE;EACnE,OAAOuB,MAAM,CAACd,MAAM,GAAG,EAAE,EAAE;IAAEc,MAAM,GAAG,GAAG,GAAGA,MAAM;;EAClD,OAAO,IAAI,GAAGjB,YAAY,CAAC,MAAM,GAAGiB,MAAM,CAAC,GAAGA,MAAM;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}