import React, { useState } from 'react';
import { useGame } from '../contexts/GameContext';
import HeroCard from '../components/HeroCard';
import LoadingSpinner from '../components/LoadingSpinner';
import {
  ShieldCheckIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

const Heroes = () => {
  const { heroes, upgradeHero, loading } = useGame();
  const [selectedHero, setSelectedHero] = useState(null);
  const [upgrading, setUpgrading] = useState(false);

  const handleUpgrade = async (heroId) => {
    try {
      setUpgrading(true);
      await upgradeHero(heroId);
      toast.success('Hero upgraded successfully!');
    } catch (error) {
      toast.error(error.response?.data?.error || 'Failed to upgrade hero');
    } finally {
      setUpgrading(false);
    }
  };

  const getHeroTypeColor = (type) => {
    const colors = {
      warrior: 'from-red-500 to-red-600',
      mage: 'from-blue-500 to-blue-600',
      archer: 'from-green-500 to-green-600',
      assassin: 'from-purple-500 to-purple-600',
      tank: 'from-gray-500 to-gray-600',
    };
    return colors[type] || 'from-gray-500 to-gray-600';
  };

  const getStatIcon = (stat) => {
    const icons = {
      hp: HeartIcon,
      atk: BoltIcon,
      def: ShieldCheckIcon,
      spd: StarIcon,
      luk: StarIcon,
    };
    return icons[stat] || StarIcon;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" text="Loading heroes..." />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-2">Your Heroes</h1>
        <p className="text-dark-300">
          Manage and upgrade your battle-ready heroes
        </p>
      </div>

      {heroes && heroes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {heroes.map((hero) => (
            <HeroCard
              key={hero.id}
              hero={hero}
              onSelect={() => setSelectedHero(hero)}
              onUpgrade={handleUpgrade}
              isSelected={selectedHero?.id === hero.id}
              showUpgrade={true}
              upgrading={upgrading}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-20">
          <ShieldCheckIcon className="w-16 h-16 text-dark-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">No Heroes Found</h2>
          <p className="text-dark-400 mb-6">
            It looks like you don't have any heroes yet. New players should receive starter heroes automatically.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="game-button"
          >
            Refresh Page
          </button>
        </div>
      )}

      {/* Hero Detail Modal */}
      {selectedHero && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="game-card max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-white">{selectedHero.name}</h2>
              <button
                onClick={() => setSelectedHero(null)}
                className="text-dark-400 hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Detailed Stats</h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(selectedHero.stats).map(([stat, value]) => (
                    <div key={stat} className="text-center p-3 bg-dark-700 rounded-lg">
                      <div className="text-2xl font-bold text-white">{value}</div>
                      <div className="text-dark-400 uppercase text-sm">{stat}</div>
                    </div>
                  ))}
                </div>
              </div>

              {selectedHero.skills && selectedHero.skills.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-2">Skills</h3>
                  <div className="space-y-2">
                    {selectedHero.skills.map((skill, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg ${
                          skill.isUnlocked ? 'bg-green-900/30 border border-green-600' : 'bg-dark-700'
                        }`}
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-white">{skill.name}</span>
                          <span className={`text-sm ${skill.isUnlocked ? 'text-green-400' : 'text-dark-400'}`}>
                            {skill.isUnlocked ? 'Unlocked' : 'Locked'}
                          </span>
                        </div>
                        <div className="text-sm text-dark-400 mt-1">
                          Damage: {skill.damage} • Cooldown: {skill.cooldown}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Heroes;
