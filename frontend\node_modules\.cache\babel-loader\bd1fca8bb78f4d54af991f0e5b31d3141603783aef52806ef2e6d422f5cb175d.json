{"ast": null, "code": "import { defineProperties, isError, assert, assertArgument, assertArgumentCount } from \"../../utils/index.js\";\nimport { Typed } from \"../typed.js\";\nimport { Coder, Result, WordSize, Writer } from \"./abstract-coder.js\";\nimport { AnonymousCoder } from \"./anonymous.js\";\n/**\n *  @_ignore\n */\nexport function pack(writer, coders, values) {\n  let arrayValues = [];\n  if (Array.isArray(values)) {\n    arrayValues = values;\n  } else if (values && typeof values === \"object\") {\n    let unique = {};\n    arrayValues = coders.map(coder => {\n      const name = coder.localName;\n      assert(name, \"cannot encode object for signature with missing names\", \"INVALID_ARGUMENT\", {\n        argument: \"values\",\n        info: {\n          coder\n        },\n        value: values\n      });\n      assert(!unique[name], \"cannot encode object for signature with duplicate names\", \"INVALID_ARGUMENT\", {\n        argument: \"values\",\n        info: {\n          coder\n        },\n        value: values\n      });\n      unique[name] = true;\n      return values[name];\n    });\n  } else {\n    assertArgument(false, \"invalid tuple value\", \"tuple\", values);\n  }\n  assertArgument(coders.length === arrayValues.length, \"types/value length mismatch\", \"tuple\", values);\n  let staticWriter = new Writer();\n  let dynamicWriter = new Writer();\n  let updateFuncs = [];\n  coders.forEach((coder, index) => {\n    let value = arrayValues[index];\n    if (coder.dynamic) {\n      // Get current dynamic offset (for the future pointer)\n      let dynamicOffset = dynamicWriter.length;\n      // Encode the dynamic value into the dynamicWriter\n      coder.encode(dynamicWriter, value);\n      // Prepare to populate the correct offset once we are done\n      let updateFunc = staticWriter.writeUpdatableValue();\n      updateFuncs.push(baseOffset => {\n        updateFunc(baseOffset + dynamicOffset);\n      });\n    } else {\n      coder.encode(staticWriter, value);\n    }\n  });\n  // Backfill all the dynamic offsets, now that we know the static length\n  updateFuncs.forEach(func => {\n    func(staticWriter.length);\n  });\n  let length = writer.appendWriter(staticWriter);\n  length += writer.appendWriter(dynamicWriter);\n  return length;\n}\n/**\n *  @_ignore\n */\nexport function unpack(reader, coders) {\n  let values = [];\n  let keys = [];\n  // A reader anchored to this base\n  let baseReader = reader.subReader(0);\n  coders.forEach(coder => {\n    let value = null;\n    if (coder.dynamic) {\n      let offset = reader.readIndex();\n      let offsetReader = baseReader.subReader(offset);\n      try {\n        value = coder.decode(offsetReader);\n      } catch (error) {\n        // Cannot recover from this\n        if (isError(error, \"BUFFER_OVERRUN\")) {\n          throw error;\n        }\n        value = error;\n        value.baseType = coder.name;\n        value.name = coder.localName;\n        value.type = coder.type;\n      }\n    } else {\n      try {\n        value = coder.decode(reader);\n      } catch (error) {\n        // Cannot recover from this\n        if (isError(error, \"BUFFER_OVERRUN\")) {\n          throw error;\n        }\n        value = error;\n        value.baseType = coder.name;\n        value.name = coder.localName;\n        value.type = coder.type;\n      }\n    }\n    if (value == undefined) {\n      throw new Error(\"investigate\");\n    }\n    values.push(value);\n    keys.push(coder.localName || null);\n  });\n  return Result.fromItems(values, keys);\n}\n/**\n *  @_ignore\n */\nexport class ArrayCoder extends Coder {\n  coder;\n  length;\n  constructor(coder, length, localName) {\n    const type = coder.type + \"[\" + (length >= 0 ? length : \"\") + \"]\";\n    const dynamic = length === -1 || coder.dynamic;\n    super(\"array\", type, localName, dynamic);\n    defineProperties(this, {\n      coder,\n      length\n    });\n  }\n  defaultValue() {\n    // Verifies the child coder is valid (even if the array is dynamic or 0-length)\n    const defaultChild = this.coder.defaultValue();\n    const result = [];\n    for (let i = 0; i < this.length; i++) {\n      result.push(defaultChild);\n    }\n    return result;\n  }\n  encode(writer, _value) {\n    const value = Typed.dereference(_value, \"array\");\n    if (!Array.isArray(value)) {\n      this._throwError(\"expected array value\", value);\n    }\n    let count = this.length;\n    if (count === -1) {\n      count = value.length;\n      writer.writeValue(value.length);\n    }\n    assertArgumentCount(value.length, count, \"coder array\" + (this.localName ? \" \" + this.localName : \"\"));\n    let coders = [];\n    for (let i = 0; i < value.length; i++) {\n      coders.push(this.coder);\n    }\n    return pack(writer, coders, value);\n  }\n  decode(reader) {\n    let count = this.length;\n    if (count === -1) {\n      count = reader.readIndex();\n      // Check that there is *roughly* enough data to ensure\n      // stray random data is not being read as a length. Each\n      // slot requires at least 32 bytes for their value (or 32\n      // bytes as a link to the data). This could use a much\n      // tighter bound, but we are erroring on the side of safety.\n      assert(count * WordSize <= reader.dataLength, \"insufficient data length\", \"BUFFER_OVERRUN\", {\n        buffer: reader.bytes,\n        offset: count * WordSize,\n        length: reader.dataLength\n      });\n    }\n    let coders = [];\n    for (let i = 0; i < count; i++) {\n      coders.push(new AnonymousCoder(this.coder));\n    }\n    return unpack(reader, coders);\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "isError", "assert", "assertArgument", "assertArgumentCount", "Typed", "Coder", "Result", "WordSize", "Writer", "AnonymousCoder", "pack", "writer", "coders", "values", "arrayValues", "Array", "isArray", "unique", "map", "coder", "name", "localName", "argument", "info", "value", "length", "staticWriter", "dynamicWriter", "updateFuncs", "for<PERSON>ach", "index", "dynamic", "dynamicOffset", "encode", "updateFunc", "writeUpdatableValue", "push", "baseOffset", "func", "appendWriter", "unpack", "reader", "keys", "baseR<PERSON>er", "subReader", "offset", "readIndex", "offsetReader", "decode", "error", "baseType", "type", "undefined", "Error", "fromItems", "ArrayCoder", "constructor", "defaultValue", "defaultChild", "result", "i", "_value", "dereference", "_throwError", "count", "writeValue", "dataLength", "buffer", "bytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\array.ts"], "sourcesContent": ["import {\n    defineProperties, isError, assert, assertArgument, assertArgumentCount\n} from \"../../utils/index.js\";\n\nimport { Typed } from \"../typed.js\";\n\nimport { Coder, Result, WordSize, Writer } from \"./abstract-coder.js\";\nimport { AnonymousCoder } from \"./anonymous.js\";\n\nimport type { Reader } from \"./abstract-coder.js\";\n\n/**\n *  @_ignore\n */\nexport function pack(writer: Writer, coders: ReadonlyArray<Coder>, values: Array<any> | { [ name: string ]: any }): number {\n    let arrayValues: Array<any> = [ ];\n\n    if (Array.isArray(values)) {\n       arrayValues = values;\n\n    } else if (values && typeof(values) === \"object\") {\n        let unique: { [ name: string ]: boolean } = { };\n\n        arrayValues = coders.map((coder) => {\n            const name = coder.localName;\n            assert(name, \"cannot encode object for signature with missing names\",\n                \"INVALID_ARGUMENT\", { argument: \"values\", info: { coder }, value: values });\n\n            assert(!unique[name], \"cannot encode object for signature with duplicate names\",\n                \"INVALID_ARGUMENT\", { argument: \"values\", info: { coder }, value: values });\n\n            unique[name] = true;\n\n            return values[name];\n        });\n\n    } else {\n        assertArgument(false, \"invalid tuple value\", \"tuple\", values);\n    }\n\n    assertArgument(coders.length === arrayValues.length, \"types/value length mismatch\", \"tuple\", values);\n\n    let staticWriter = new Writer();\n    let dynamicWriter = new Writer();\n\n    let updateFuncs: Array<(baseOffset: number) => void> = [];\n    coders.forEach((coder, index) => {\n        let value = arrayValues[index];\n\n        if (coder.dynamic) {\n            // Get current dynamic offset (for the future pointer)\n            let dynamicOffset = dynamicWriter.length;\n\n            // Encode the dynamic value into the dynamicWriter\n            coder.encode(dynamicWriter, value);\n\n            // Prepare to populate the correct offset once we are done\n            let updateFunc = staticWriter.writeUpdatableValue();\n            updateFuncs.push((baseOffset: number) => {\n                updateFunc(baseOffset + dynamicOffset);\n            });\n\n        } else {\n            coder.encode(staticWriter, value);\n        }\n    });\n\n    // Backfill all the dynamic offsets, now that we know the static length\n    updateFuncs.forEach((func) => { func(staticWriter.length); });\n\n    let length = writer.appendWriter(staticWriter);\n    length += writer.appendWriter(dynamicWriter);\n    return length;\n}\n\n/**\n *  @_ignore\n */\nexport function unpack(reader: Reader, coders: ReadonlyArray<Coder>): Result {\n    let values: Array<any> = [];\n    let keys: Array<null | string> = [ ];\n\n    // A reader anchored to this base\n    let baseReader = reader.subReader(0);\n\n    coders.forEach((coder) => {\n        let value: any = null;\n\n        if (coder.dynamic) {\n            let offset = reader.readIndex();\n            let offsetReader = baseReader.subReader(offset);\n            try {\n                value = coder.decode(offsetReader);\n            } catch (error: any) {\n                // Cannot recover from this\n                if (isError(error, \"BUFFER_OVERRUN\")) {\n                    throw error;\n                }\n\n                value = error;\n                value.baseType = coder.name;\n                value.name = coder.localName;\n                value.type = coder.type;\n            }\n\n        } else {\n            try {\n                value = coder.decode(reader);\n            } catch (error: any) {\n                // Cannot recover from this\n                if (isError(error, \"BUFFER_OVERRUN\")) {\n                    throw error;\n                }\n\n                value = error;\n                value.baseType = coder.name;\n                value.name = coder.localName;\n                value.type = coder.type;\n            }\n        }\n\n        if (value == undefined) {\n            throw new Error(\"investigate\");\n        }\n\n        values.push(value);\n        keys.push(coder.localName || null);\n    });\n\n    return Result.fromItems(values, keys);\n}\n\n/**\n *  @_ignore\n */\nexport class ArrayCoder extends Coder {\n    readonly coder!: Coder;\n    readonly length!: number;\n\n    constructor(coder: Coder, length: number, localName: string) {\n        const type = (coder.type + \"[\" + (length >= 0 ? length: \"\") + \"]\");\n        const dynamic = (length === -1 || coder.dynamic);\n        super(\"array\", type, localName, dynamic);\n        defineProperties<ArrayCoder>(this, { coder, length });\n    }\n\n    defaultValue(): Array<any> {\n        // Verifies the child coder is valid (even if the array is dynamic or 0-length)\n        const defaultChild = this.coder.defaultValue();\n\n        const result: Array<any> = [];\n        for (let i = 0; i < this.length; i++) {\n            result.push(defaultChild);\n        }\n        return result;\n    }\n\n    encode(writer: Writer, _value: Array<any> | Typed): number {\n        const value = Typed.dereference(_value, \"array\");\n\n        if(!Array.isArray(value)) {\n            this._throwError(\"expected array value\", value);\n        }\n\n        let count = this.length;\n\n        if (count === -1) {\n            count = value.length;\n            writer.writeValue(value.length);\n        }\n\n        assertArgumentCount(value.length, count, \"coder array\" + (this.localName? (\" \"+ this.localName): \"\"));\n\n        let coders: Array<Coder> = [ ];\n        for (let i = 0; i < value.length; i++) { coders.push(this.coder); }\n\n        return pack(writer, coders, value);\n    }\n\n    decode(reader: Reader): any {\n        let count = this.length;\n        if (count === -1) {\n            count = reader.readIndex();\n\n            // Check that there is *roughly* enough data to ensure\n            // stray random data is not being read as a length. Each\n            // slot requires at least 32 bytes for their value (or 32\n            // bytes as a link to the data). This could use a much\n            // tighter bound, but we are erroring on the side of safety.\n            assert(count * WordSize <= reader.dataLength, \"insufficient data length\",\n                \"BUFFER_OVERRUN\", { buffer: reader.bytes, offset: count * WordSize, length: reader.dataLength });\n        }\n        let coders: Array<Coder> = [];\n        for (let i = 0; i < count; i++) { coders.push(new AnonymousCoder(this.coder)); }\n\n        return unpack(reader, coders);\n    }\n}\n\n"], "mappings": "AAAA,SACIA,gBAAgB,EAAEC,OAAO,EAAEC,MAAM,EAAEC,cAAc,EAAEC,mBAAmB,QACnE,sBAAsB;AAE7B,SAASC,KAAK,QAAQ,aAAa;AAEnC,SAASC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,qBAAqB;AACrE,SAASC,cAAc,QAAQ,gBAAgB;AAI/C;;;AAGA,OAAM,SAAUC,IAAIA,CAACC,MAAc,EAAEC,MAA4B,EAAEC,MAA8C;EAC7G,IAAIC,WAAW,GAAe,EAAG;EAEjC,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;IACxBC,WAAW,GAAGD,MAAM;GAEtB,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAO,KAAK,QAAQ,EAAE;IAC9C,IAAII,MAAM,GAAkC,EAAG;IAE/CH,WAAW,GAAGF,MAAM,CAACM,GAAG,CAAEC,KAAK,IAAI;MAC/B,MAAMC,IAAI,GAAGD,KAAK,CAACE,SAAS;MAC5BpB,MAAM,CAACmB,IAAI,EAAE,uDAAuD,EAChE,kBAAkB,EAAE;QAAEE,QAAQ,EAAE,QAAQ;QAAEC,IAAI,EAAE;UAAEJ;QAAK,CAAE;QAAEK,KAAK,EAAEX;MAAM,CAAE,CAAC;MAE/EZ,MAAM,CAAC,CAACgB,MAAM,CAACG,IAAI,CAAC,EAAE,yDAAyD,EAC3E,kBAAkB,EAAE;QAAEE,QAAQ,EAAE,QAAQ;QAAEC,IAAI,EAAE;UAAEJ;QAAK,CAAE;QAAEK,KAAK,EAAEX;MAAM,CAAE,CAAC;MAE/EI,MAAM,CAACG,IAAI,CAAC,GAAG,IAAI;MAEnB,OAAOP,MAAM,CAACO,IAAI,CAAC;IACvB,CAAC,CAAC;GAEL,MAAM;IACHlB,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAEW,MAAM,CAAC;;EAGjEX,cAAc,CAACU,MAAM,CAACa,MAAM,KAAKX,WAAW,CAACW,MAAM,EAAE,6BAA6B,EAAE,OAAO,EAAEZ,MAAM,CAAC;EAEpG,IAAIa,YAAY,GAAG,IAAIlB,MAAM,EAAE;EAC/B,IAAImB,aAAa,GAAG,IAAInB,MAAM,EAAE;EAEhC,IAAIoB,WAAW,GAAwC,EAAE;EACzDhB,MAAM,CAACiB,OAAO,CAAC,CAACV,KAAK,EAAEW,KAAK,KAAI;IAC5B,IAAIN,KAAK,GAAGV,WAAW,CAACgB,KAAK,CAAC;IAE9B,IAAIX,KAAK,CAACY,OAAO,EAAE;MACf;MACA,IAAIC,aAAa,GAAGL,aAAa,CAACF,MAAM;MAExC;MACAN,KAAK,CAACc,MAAM,CAACN,aAAa,EAAEH,KAAK,CAAC;MAElC;MACA,IAAIU,UAAU,GAAGR,YAAY,CAACS,mBAAmB,EAAE;MACnDP,WAAW,CAACQ,IAAI,CAAEC,UAAkB,IAAI;QACpCH,UAAU,CAACG,UAAU,GAAGL,aAAa,CAAC;MAC1C,CAAC,CAAC;KAEL,MAAM;MACHb,KAAK,CAACc,MAAM,CAACP,YAAY,EAAEF,KAAK,CAAC;;EAEzC,CAAC,CAAC;EAEF;EACAI,WAAW,CAACC,OAAO,CAAES,IAAI,IAAI;IAAGA,IAAI,CAACZ,YAAY,CAACD,MAAM,CAAC;EAAE,CAAC,CAAC;EAE7D,IAAIA,MAAM,GAAGd,MAAM,CAAC4B,YAAY,CAACb,YAAY,CAAC;EAC9CD,MAAM,IAAId,MAAM,CAAC4B,YAAY,CAACZ,aAAa,CAAC;EAC5C,OAAOF,MAAM;AACjB;AAEA;;;AAGA,OAAM,SAAUe,MAAMA,CAACC,MAAc,EAAE7B,MAA4B;EAC/D,IAAIC,MAAM,GAAe,EAAE;EAC3B,IAAI6B,IAAI,GAAyB,EAAG;EAEpC;EACA,IAAIC,UAAU,GAAGF,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC;EAEpChC,MAAM,CAACiB,OAAO,CAAEV,KAAK,IAAI;IACrB,IAAIK,KAAK,GAAQ,IAAI;IAErB,IAAIL,KAAK,CAACY,OAAO,EAAE;MACf,IAAIc,MAAM,GAAGJ,MAAM,CAACK,SAAS,EAAE;MAC/B,IAAIC,YAAY,GAAGJ,UAAU,CAACC,SAAS,CAACC,MAAM,CAAC;MAC/C,IAAI;QACArB,KAAK,GAAGL,KAAK,CAAC6B,MAAM,CAACD,YAAY,CAAC;OACrC,CAAC,OAAOE,KAAU,EAAE;QACjB;QACA,IAAIjD,OAAO,CAACiD,KAAK,EAAE,gBAAgB,CAAC,EAAE;UAClC,MAAMA,KAAK;;QAGfzB,KAAK,GAAGyB,KAAK;QACbzB,KAAK,CAAC0B,QAAQ,GAAG/B,KAAK,CAACC,IAAI;QAC3BI,KAAK,CAACJ,IAAI,GAAGD,KAAK,CAACE,SAAS;QAC5BG,KAAK,CAAC2B,IAAI,GAAGhC,KAAK,CAACgC,IAAI;;KAG9B,MAAM;MACH,IAAI;QACA3B,KAAK,GAAGL,KAAK,CAAC6B,MAAM,CAACP,MAAM,CAAC;OAC/B,CAAC,OAAOQ,KAAU,EAAE;QACjB;QACA,IAAIjD,OAAO,CAACiD,KAAK,EAAE,gBAAgB,CAAC,EAAE;UAClC,MAAMA,KAAK;;QAGfzB,KAAK,GAAGyB,KAAK;QACbzB,KAAK,CAAC0B,QAAQ,GAAG/B,KAAK,CAACC,IAAI;QAC3BI,KAAK,CAACJ,IAAI,GAAGD,KAAK,CAACE,SAAS;QAC5BG,KAAK,CAAC2B,IAAI,GAAGhC,KAAK,CAACgC,IAAI;;;IAI/B,IAAI3B,KAAK,IAAI4B,SAAS,EAAE;MACpB,MAAM,IAAIC,KAAK,CAAC,aAAa,CAAC;;IAGlCxC,MAAM,CAACuB,IAAI,CAACZ,KAAK,CAAC;IAClBkB,IAAI,CAACN,IAAI,CAACjB,KAAK,CAACE,SAAS,IAAI,IAAI,CAAC;EACtC,CAAC,CAAC;EAEF,OAAOf,MAAM,CAACgD,SAAS,CAACzC,MAAM,EAAE6B,IAAI,CAAC;AACzC;AAEA;;;AAGA,OAAM,MAAOa,UAAW,SAAQlD,KAAK;EACxBc,KAAK;EACLM,MAAM;EAEf+B,YAAYrC,KAAY,EAAEM,MAAc,EAAEJ,SAAiB;IACvD,MAAM8B,IAAI,GAAIhC,KAAK,CAACgC,IAAI,GAAG,GAAG,IAAI1B,MAAM,IAAI,CAAC,GAAGA,MAAM,GAAE,EAAE,CAAC,GAAG,GAAI;IAClE,MAAMM,OAAO,GAAIN,MAAM,KAAK,CAAC,CAAC,IAAIN,KAAK,CAACY,OAAQ;IAChD,KAAK,CAAC,OAAO,EAAEoB,IAAI,EAAE9B,SAAS,EAAEU,OAAO,CAAC;IACxChC,gBAAgB,CAAa,IAAI,EAAE;MAAEoB,KAAK;MAAEM;IAAM,CAAE,CAAC;EACzD;EAEAgC,YAAYA,CAAA;IACR;IACA,MAAMC,YAAY,GAAG,IAAI,CAACvC,KAAK,CAACsC,YAAY,EAAE;IAE9C,MAAME,MAAM,GAAe,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnC,MAAM,EAAEmC,CAAC,EAAE,EAAE;MAClCD,MAAM,CAACvB,IAAI,CAACsB,YAAY,CAAC;;IAE7B,OAAOC,MAAM;EACjB;EAEA1B,MAAMA,CAACtB,MAAc,EAAEkD,MAA0B;IAC7C,MAAMrC,KAAK,GAAGpB,KAAK,CAAC0D,WAAW,CAACD,MAAM,EAAE,OAAO,CAAC;IAEhD,IAAG,CAAC9C,KAAK,CAACC,OAAO,CAACQ,KAAK,CAAC,EAAE;MACtB,IAAI,CAACuC,WAAW,CAAC,sBAAsB,EAAEvC,KAAK,CAAC;;IAGnD,IAAIwC,KAAK,GAAG,IAAI,CAACvC,MAAM;IAEvB,IAAIuC,KAAK,KAAK,CAAC,CAAC,EAAE;MACdA,KAAK,GAAGxC,KAAK,CAACC,MAAM;MACpBd,MAAM,CAACsD,UAAU,CAACzC,KAAK,CAACC,MAAM,CAAC;;IAGnCtB,mBAAmB,CAACqB,KAAK,CAACC,MAAM,EAAEuC,KAAK,EAAE,aAAa,IAAI,IAAI,CAAC3C,SAAS,GAAG,GAAG,GAAE,IAAI,CAACA,SAAS,GAAG,EAAE,CAAC,CAAC;IAErG,IAAIT,MAAM,GAAiB,EAAG;IAC9B,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpC,KAAK,CAACC,MAAM,EAAEmC,CAAC,EAAE,EAAE;MAAEhD,MAAM,CAACwB,IAAI,CAAC,IAAI,CAACjB,KAAK,CAAC;;IAEhE,OAAOT,IAAI,CAACC,MAAM,EAAEC,MAAM,EAAEY,KAAK,CAAC;EACtC;EAEAwB,MAAMA,CAACP,MAAc;IACjB,IAAIuB,KAAK,GAAG,IAAI,CAACvC,MAAM;IACvB,IAAIuC,KAAK,KAAK,CAAC,CAAC,EAAE;MACdA,KAAK,GAAGvB,MAAM,CAACK,SAAS,EAAE;MAE1B;MACA;MACA;MACA;MACA;MACA7C,MAAM,CAAC+D,KAAK,GAAGzD,QAAQ,IAAIkC,MAAM,CAACyB,UAAU,EAAE,0BAA0B,EACpE,gBAAgB,EAAE;QAAEC,MAAM,EAAE1B,MAAM,CAAC2B,KAAK;QAAEvB,MAAM,EAAEmB,KAAK,GAAGzD,QAAQ;QAAEkB,MAAM,EAAEgB,MAAM,CAACyB;MAAU,CAAE,CAAC;;IAExG,IAAItD,MAAM,GAAiB,EAAE;IAC7B,KAAK,IAAIgD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,EAAEJ,CAAC,EAAE,EAAE;MAAEhD,MAAM,CAACwB,IAAI,CAAC,IAAI3B,cAAc,CAAC,IAAI,CAACU,KAAK,CAAC,CAAC;;IAE7E,OAAOqB,MAAM,CAACC,MAAM,EAAE7B,MAAM,CAAC;EACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}