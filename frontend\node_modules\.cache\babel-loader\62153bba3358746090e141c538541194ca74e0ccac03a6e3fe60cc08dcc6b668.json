{"ast": null, "code": "/**\n *  The Application Programming Interface (API) is the collection of\n *  functions, classes and types offered by the Ethers library.\n *\n *  @_section: api:Application Programming Interface  [about-api]\n *  @_navTitle: API\n */\nimport * as ethers from \"./ethers.js\";\nexport { ethers };\nexport * from \"./ethers.js\";", "map": {"version": 3, "names": ["ethers"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\index.ts"], "sourcesContent": ["/**\n *  The Application Programming Interface (API) is the collection of\n *  functions, classes and types offered by the Ethers library.\n *\n *  @_section: api:Application Programming Interface  [about-api]\n *  @_navTitle: API\n */\nimport * as ethers from \"./ethers.js\";\n\nexport { ethers };\n\nexport * from \"./ethers.js\";\n"], "mappings": "AAAA;;;;;;;AAOA,OAAO,KAAKA,MAAM,MAAM,aAAa;AAErC,SAASA,MAAM;AAEf,cAAc,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}