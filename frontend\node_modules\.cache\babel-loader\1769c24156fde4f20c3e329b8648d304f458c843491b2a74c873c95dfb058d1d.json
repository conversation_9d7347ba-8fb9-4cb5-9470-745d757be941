{"ast": null, "code": "import { Interface } from \"../abi/index.js\";\nimport { getCreateAddress } from \"../address/index.js\";\nimport { concat, defineProperties, getBytes, hexlify, assert, assertArgument } from \"../utils/index.js\";\nimport { BaseContract, copyOverrides, resolveArgs } from \"./contract.js\";\n// A = Arguments to the constructor\n// I = Interface of deployed contracts\n/**\n *  A **ContractFactory** is used to deploy a Contract to the blockchain.\n */\nexport class ContractFactory {\n  /**\n   *  The Contract Interface.\n   */\n  interface;\n  /**\n   *  The Contract deployment bytecode. Often called the initcode.\n   */\n  bytecode;\n  /**\n   *  The ContractRunner to deploy the Contract as.\n   */\n  runner;\n  /**\n   *  Create a new **ContractFactory** with %%abi%% and %%bytecode%%,\n   *  optionally connected to %%runner%%.\n   *\n   *  The %%bytecode%% may be the ``bytecode`` property within the\n   *  standard Solidity JSON output.\n   */\n  constructor(abi, bytecode, runner) {\n    const iface = Interface.from(abi);\n    // Dereference Solidity bytecode objects and allow a missing `0x`-prefix\n    if (bytecode instanceof Uint8Array) {\n      bytecode = hexlify(getBytes(bytecode));\n    } else {\n      if (typeof bytecode === \"object\") {\n        bytecode = bytecode.object;\n      }\n      if (!bytecode.startsWith(\"0x\")) {\n        bytecode = \"0x\" + bytecode;\n      }\n      bytecode = hexlify(getBytes(bytecode));\n    }\n    defineProperties(this, {\n      bytecode,\n      interface: iface,\n      runner: runner || null\n    });\n  }\n  attach(target) {\n    return new BaseContract(target, this.interface, this.runner);\n  }\n  /**\n   *  Resolves to the transaction to deploy the contract, passing %%args%%\n   *  into the constructor.\n   */\n  async getDeployTransaction(...args) {\n    let overrides = {};\n    const fragment = this.interface.deploy;\n    if (fragment.inputs.length + 1 === args.length) {\n      overrides = await copyOverrides(args.pop());\n    }\n    if (fragment.inputs.length !== args.length) {\n      throw new Error(\"incorrect number of arguments to constructor\");\n    }\n    const resolvedArgs = await resolveArgs(this.runner, fragment.inputs, args);\n    const data = concat([this.bytecode, this.interface.encodeDeploy(resolvedArgs)]);\n    return Object.assign({}, overrides, {\n      data\n    });\n  }\n  /**\n   *  Resolves to the Contract deployed by passing %%args%% into the\n   *  constructor.\n   *\n   *  This will resolve to the Contract before it has been deployed to the\n   *  network, so the [[BaseContract-waitForDeployment]] should be used before\n   *  sending any transactions to it.\n   */\n  async deploy(...args) {\n    const tx = await this.getDeployTransaction(...args);\n    assert(this.runner && typeof this.runner.sendTransaction === \"function\", \"factory runner does not support sending transactions\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"sendTransaction\"\n    });\n    const sentTx = await this.runner.sendTransaction(tx);\n    const address = getCreateAddress(sentTx);\n    return new BaseContract(address, this.interface, this.runner, sentTx);\n  }\n  /**\n   *  Return a new **ContractFactory** with the same ABI and bytecode,\n   *  but connected to %%runner%%.\n   */\n  connect(runner) {\n    return new ContractFactory(this.interface, this.bytecode, runner);\n  }\n  /**\n   *  Create a new **ContractFactory** from the standard Solidity JSON output.\n   */\n  static fromSolidity(output, runner) {\n    assertArgument(output != null, \"bad compiler output\", \"output\", output);\n    if (typeof output === \"string\") {\n      output = JSON.parse(output);\n    }\n    const abi = output.abi;\n    let bytecode = \"\";\n    if (output.bytecode) {\n      bytecode = output.bytecode;\n    } else if (output.evm && output.evm.bytecode) {\n      bytecode = output.evm.bytecode;\n    }\n    return new this(abi, bytecode, runner);\n  }\n}", "map": {"version": 3, "names": ["Interface", "getCreateAddress", "concat", "defineProperties", "getBytes", "hexlify", "assert", "assertArgument", "BaseContract", "copyOverrides", "resolveArgs", "ContractFactory", "interface", "bytecode", "runner", "constructor", "abi", "iface", "from", "Uint8Array", "object", "startsWith", "attach", "target", "getDeployTransaction", "args", "overrides", "fragment", "deploy", "inputs", "length", "pop", "Error", "<PERSON><PERSON><PERSON><PERSON>", "data", "encodeDeploy", "Object", "assign", "tx", "sendTransaction", "operation", "sentTx", "address", "connect", "fromSolidity", "output", "JSON", "parse", "evm"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\contract\\factory.ts"], "sourcesContent": ["\nimport { Interface } from \"../abi/index.js\";\nimport { getCreateAddress } from \"../address/index.js\";\nimport {\n    concat, defineProperties, getBytes, hexlify,\n    assert, assertArgument\n} from \"../utils/index.js\";\n\nimport { BaseContract, copyOverrides, resolveArgs } from \"./contract.js\";\n\nimport type { InterfaceAbi } from \"../abi/index.js\";\nimport type { Addressable } from \"../address/index.js\";\nimport type { ContractRunner } from \"../providers/index.js\";\nimport type { BytesLike } from \"../utils/index.js\";\n\nimport type {\n    ContractInterface, ContractMethodArgs, ContractDeployTransaction,\n} from \"./types.js\";\nimport type { ContractTransactionResponse } from \"./wrappers.js\";\n\n\n// A = Arguments to the constructor\n// I = Interface of deployed contracts\n\n/**\n *  A **ContractFactory** is used to deploy a Contract to the blockchain.\n */\nexport class ContractFactory<A extends Array<any> = Array<any>, I = BaseContract> {\n\n    /**\n     *  The Contract Interface.\n     */\n    readonly interface!: Interface;\n\n    /**\n     *  The Contract deployment bytecode. Often called the initcode.\n     */\n    readonly bytecode!: string;\n\n    /**\n     *  The ContractRunner to deploy the Contract as.\n     */\n    readonly runner!: null | ContractRunner;\n\n    /**\n     *  Create a new **ContractFactory** with %%abi%% and %%bytecode%%,\n     *  optionally connected to %%runner%%.\n     *\n     *  The %%bytecode%% may be the ``bytecode`` property within the\n     *  standard Solidity JSON output.\n     */\n    constructor(abi: Interface | InterfaceAbi, bytecode: BytesLike | { object: string }, runner?: null | ContractRunner) {\n        const iface = Interface.from(abi);\n\n        // Dereference Solidity bytecode objects and allow a missing `0x`-prefix\n        if (bytecode instanceof Uint8Array) {\n            bytecode = hexlify(getBytes(bytecode));\n        } else {\n            if (typeof(bytecode) === \"object\") { bytecode = bytecode.object; }\n            if (!bytecode.startsWith(\"0x\")) { bytecode = \"0x\" + bytecode; }\n            bytecode = hexlify(getBytes(bytecode));\n        }\n\n        defineProperties<ContractFactory>(this, {\n            bytecode, interface: iface, runner: (runner || null)\n        });\n    }\n\n    attach(target: string | Addressable): BaseContract & Omit<I, keyof BaseContract> {\n        return new (<any>BaseContract)(target, this.interface, this.runner);\n    }\n\n    /**\n     *  Resolves to the transaction to deploy the contract, passing %%args%%\n     *  into the constructor.\n     */\n    async getDeployTransaction(...args: ContractMethodArgs<A>): Promise<ContractDeployTransaction> {\n        let overrides: Omit<ContractDeployTransaction, \"data\"> = { };\n\n        const fragment = this.interface.deploy;\n\n        if (fragment.inputs.length + 1 === args.length) {\n            overrides = await copyOverrides(args.pop());\n        }\n\n        if (fragment.inputs.length !== args.length) {\n            throw new Error(\"incorrect number of arguments to constructor\");\n        }\n\n        const resolvedArgs = await resolveArgs(this.runner, fragment.inputs, args);\n\n        const data = concat([ this.bytecode, this.interface.encodeDeploy(resolvedArgs) ]);\n        return Object.assign({ }, overrides, { data });\n    }\n\n    /**\n     *  Resolves to the Contract deployed by passing %%args%% into the\n     *  constructor.\n     *\n     *  This will resolve to the Contract before it has been deployed to the\n     *  network, so the [[BaseContract-waitForDeployment]] should be used before\n     *  sending any transactions to it.\n     */\n    async deploy(...args: ContractMethodArgs<A>): Promise<BaseContract & { deploymentTransaction(): ContractTransactionResponse } & Omit<I, keyof BaseContract>> {\n        const tx = await this.getDeployTransaction(...args);\n\n        assert(this.runner && typeof(this.runner.sendTransaction) === \"function\",\n            \"factory runner does not support sending transactions\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"sendTransaction\" });\n\n        const sentTx = await this.runner.sendTransaction(tx);\n        const address = getCreateAddress(sentTx);\n        return new (<any>BaseContract)(address, this.interface, this.runner, sentTx);\n    }\n\n    /**\n     *  Return a new **ContractFactory** with the same ABI and bytecode,\n     *  but connected to %%runner%%.\n     */\n    connect(runner: null | ContractRunner): ContractFactory<A, I> {\n        return new ContractFactory(this.interface, this.bytecode, runner);\n    }\n\n    /**\n     *  Create a new **ContractFactory** from the standard Solidity JSON output.\n     */\n    static fromSolidity<A extends Array<any> = Array<any>, I = ContractInterface>(output: any, runner?: ContractRunner): ContractFactory<A, I> {\n        assertArgument(output != null, \"bad compiler output\", \"output\", output);\n\n        if (typeof(output) === \"string\") { output = JSON.parse(output); }\n\n        const abi = output.abi;\n\n        let bytecode = \"\";\n        if (output.bytecode) {\n            bytecode = output.bytecode;\n        } else if (output.evm && output.evm.bytecode) {\n            bytecode = output.evm.bytecode;\n        }\n\n        return new this(abi, bytecode, runner);\n    }\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SACIC,MAAM,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,EAC3CC,MAAM,EAAEC,cAAc,QACnB,mBAAmB;AAE1B,SAASC,YAAY,EAAEC,aAAa,EAAEC,WAAW,QAAQ,eAAe;AAaxE;AACA;AAEA;;;AAGA,OAAM,MAAOC,eAAe;EAExB;;;EAGSC,SAAS;EAElB;;;EAGSC,QAAQ;EAEjB;;;EAGSC,MAAM;EAEf;;;;;;;EAOAC,YAAYC,GAA6B,EAAEH,QAAwC,EAAEC,MAA8B;IAC/G,MAAMG,KAAK,GAAGjB,SAAS,CAACkB,IAAI,CAACF,GAAG,CAAC;IAEjC;IACA,IAAIH,QAAQ,YAAYM,UAAU,EAAE;MAChCN,QAAQ,GAAGR,OAAO,CAACD,QAAQ,CAACS,QAAQ,CAAC,CAAC;KACzC,MAAM;MACH,IAAI,OAAOA,QAAS,KAAK,QAAQ,EAAE;QAAEA,QAAQ,GAAGA,QAAQ,CAACO,MAAM;;MAC/D,IAAI,CAACP,QAAQ,CAACQ,UAAU,CAAC,IAAI,CAAC,EAAE;QAAER,QAAQ,GAAG,IAAI,GAAGA,QAAQ;;MAC5DA,QAAQ,GAAGR,OAAO,CAACD,QAAQ,CAACS,QAAQ,CAAC,CAAC;;IAG1CV,gBAAgB,CAAkB,IAAI,EAAE;MACpCU,QAAQ;MAAED,SAAS,EAAEK,KAAK;MAAEH,MAAM,EAAGA,MAAM,IAAI;KAClD,CAAC;EACN;EAEAQ,MAAMA,CAACC,MAA4B;IAC/B,OAAO,IAAUf,YAAa,CAACe,MAAM,EAAE,IAAI,CAACX,SAAS,EAAE,IAAI,CAACE,MAAM,CAAC;EACvE;EAEA;;;;EAIA,MAAMU,oBAAoBA,CAAC,GAAGC,IAA2B;IACrD,IAAIC,SAAS,GAA4C,EAAG;IAE5D,MAAMC,QAAQ,GAAG,IAAI,CAACf,SAAS,CAACgB,MAAM;IAEtC,IAAID,QAAQ,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,KAAKL,IAAI,CAACK,MAAM,EAAE;MAC5CJ,SAAS,GAAG,MAAMjB,aAAa,CAACgB,IAAI,CAACM,GAAG,EAAE,CAAC;;IAG/C,IAAIJ,QAAQ,CAACE,MAAM,CAACC,MAAM,KAAKL,IAAI,CAACK,MAAM,EAAE;MACxC,MAAM,IAAIE,KAAK,CAAC,8CAA8C,CAAC;;IAGnE,MAAMC,YAAY,GAAG,MAAMvB,WAAW,CAAC,IAAI,CAACI,MAAM,EAAEa,QAAQ,CAACE,MAAM,EAAEJ,IAAI,CAAC;IAE1E,MAAMS,IAAI,GAAGhC,MAAM,CAAC,CAAE,IAAI,CAACW,QAAQ,EAAE,IAAI,CAACD,SAAS,CAACuB,YAAY,CAACF,YAAY,CAAC,CAAE,CAAC;IACjF,OAAOG,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEX,SAAS,EAAE;MAAEQ;IAAI,CAAE,CAAC;EAClD;EAEA;;;;;;;;EAQA,MAAMN,MAAMA,CAAC,GAAGH,IAA2B;IACvC,MAAMa,EAAE,GAAG,MAAM,IAAI,CAACd,oBAAoB,CAAC,GAAGC,IAAI,CAAC;IAEnDnB,MAAM,CAAC,IAAI,CAACQ,MAAM,IAAI,OAAO,IAAI,CAACA,MAAM,CAACyB,eAAgB,KAAK,UAAU,EACpE,sDAAsD,EAAE,uBAAuB,EAAE;MACjFC,SAAS,EAAE;KAAmB,CAAC;IAEnC,MAAMC,MAAM,GAAG,MAAM,IAAI,CAAC3B,MAAM,CAACyB,eAAe,CAACD,EAAE,CAAC;IACpD,MAAMI,OAAO,GAAGzC,gBAAgB,CAACwC,MAAM,CAAC;IACxC,OAAO,IAAUjC,YAAa,CAACkC,OAAO,EAAE,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAACE,MAAM,EAAE2B,MAAM,CAAC;EAChF;EAEA;;;;EAIAE,OAAOA,CAAC7B,MAA6B;IACjC,OAAO,IAAIH,eAAe,CAAC,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,QAAQ,EAAEC,MAAM,CAAC;EACrE;EAEA;;;EAGA,OAAO8B,YAAYA,CAA2DC,MAAW,EAAE/B,MAAuB;IAC9GP,cAAc,CAACsC,MAAM,IAAI,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAEA,MAAM,CAAC;IAEvE,IAAI,OAAOA,MAAO,KAAK,QAAQ,EAAE;MAAEA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;;IAE9D,MAAM7B,GAAG,GAAG6B,MAAM,CAAC7B,GAAG;IAEtB,IAAIH,QAAQ,GAAG,EAAE;IACjB,IAAIgC,MAAM,CAAChC,QAAQ,EAAE;MACjBA,QAAQ,GAAGgC,MAAM,CAAChC,QAAQ;KAC7B,MAAM,IAAIgC,MAAM,CAACG,GAAG,IAAIH,MAAM,CAACG,GAAG,CAACnC,QAAQ,EAAE;MAC1CA,QAAQ,GAAGgC,MAAM,CAACG,GAAG,CAACnC,QAAQ;;IAGlC,OAAO,IAAI,IAAI,CAACG,GAAG,EAAEH,QAAQ,EAAEC,MAAM,CAAC;EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}