const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery } = require('../config/database');
const { verifyToken } = require('../middleware/auth');
const blockchainService = require('../config/blockchain');

const router = express.Router();

// Get user's heroes
router.get('/', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    
    const heroes = await executeQuery(
      `SELECT h.*, 
              GROUP_CONCAT(
                JSON_OBJECT(
                  'id', hs.id,
                  'name', hs.skill_name,
                  'type', hs.skill_type,
                  'damage', hs.damage,
                  'cooldown', hs.cooldown,
                  'isUnlocked', hs.is_unlocked
                )
              ) as skills
       FROM heroes h
       LEFT JOIN hero_skills hs ON h.id = hs.hero_id
       WHERE h.owner_address = ? AND h.is_active = TRUE
       GROUP BY h.id
       ORDER BY h.created_at ASC`,
      [walletAddress]
    );
    
    const formattedHeroes = heroes.map(hero => ({
      id: hero.id,
      type: hero.hero_type,
      name: hero.name,
      level: hero.level,
      stats: {
        hp: hero.hp,
        atk: hero.atk,
        def: hero.def,
        spd: hero.spd,
        luk: hero.luk
      },
      experience: hero.experience,
      skills: hero.skills ? JSON.parse(`[${hero.skills}]`) : [],
      createdAt: hero.created_at,
      updatedAt: hero.updated_at
    }));
    
    res.json({ heroes: formattedHeroes });
  } catch (error) {
    console.error('Get heroes error:', error);
    res.status(500).json({ error: 'Failed to get heroes' });
  }
});

// Get specific hero
router.get('/:heroId', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    const { heroId } = req.params;
    
    const heroes = await executeQuery(
      'SELECT * FROM heroes WHERE id = ? AND owner_address = ? AND is_active = TRUE',
      [heroId, walletAddress]
    );
    
    if (heroes.length === 0) {
      return res.status(404).json({ error: 'Hero not found' });
    }
    
    const hero = heroes[0];
    
    // Get hero skills
    const skills = await executeQuery(
      'SELECT * FROM hero_skills WHERE hero_id = ?',
      [heroId]
    );
    
    res.json({
      hero: {
        id: hero.id,
        type: hero.hero_type,
        name: hero.name,
        level: hero.level,
        stats: {
          hp: hero.hp,
          atk: hero.atk,
          def: hero.def,
          spd: hero.spd,
          luk: hero.luk
        },
        experience: hero.experience,
        skills: skills.map(skill => ({
          id: skill.id,
          name: skill.skill_name,
          type: skill.skill_type,
          damage: skill.damage,
          cooldown: skill.cooldown,
          manaCost: skill.mana_cost,
          isUnlocked: skill.is_unlocked,
          unlockLevel: skill.unlock_level
        })),
        createdAt: hero.created_at,
        updatedAt: hero.updated_at
      }
    });
  } catch (error) {
    console.error('Get hero error:', error);
    res.status(500).json({ error: 'Failed to get hero' });
  }
});

// Upgrade hero
router.post('/:heroId/upgrade', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    const { heroId } = req.params;
    
    // Verify hero ownership
    const heroes = await executeQuery(
      'SELECT * FROM heroes WHERE id = ? AND owner_address = ? AND is_active = TRUE',
      [heroId, walletAddress]
    );
    
    if (heroes.length === 0) {
      return res.status(404).json({ error: 'Hero not found' });
    }
    
    const hero = heroes[0];
    
    // Check if user has enough tokens
    let hasEnoughTokens = false;
    try {
      if (blockchainService.initialized) {
        const balance = await blockchainService.getTokenBalance(walletAddress);
        const upgradeCost = blockchainService.parseEther(process.env.HERO_UPGRADE_COST || '50');
        hasEnoughTokens = balance >= upgradeCost;
      }
    } catch (error) {
      console.warn('Failed to check token balance:', error.message);
      return res.status(500).json({ error: 'Failed to verify token balance' });
    }
    
    if (!hasEnoughTokens) {
      return res.status(400).json({ error: 'Insufficient CQT tokens for upgrade' });
    }
    
    try {
      // Process blockchain transaction
      if (blockchainService.initialized) {
        await blockchainService.upgradeHero(walletAddress);
      }
      
      // Calculate stat increases (random within range)
      const statIncrease = {
        hp: Math.floor(Math.random() * 10) + 5,  // 5-14 HP
        atk: Math.floor(Math.random() * 5) + 2,  // 2-6 ATK
        def: Math.floor(Math.random() * 4) + 2,  // 2-5 DEF
        spd: Math.floor(Math.random() * 3) + 1,  // 1-3 SPD
        luk: Math.floor(Math.random() * 2) + 1   // 1-2 LUK
      };
      
      // Update hero stats
      await executeQuery(
        `UPDATE heroes 
         SET hp = hp + ?, atk = atk + ?, def = def + ?, spd = spd + ?, luk = luk + ?,
             level = level + 1, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [statIncrease.hp, statIncrease.atk, statIncrease.def, statIncrease.spd, statIncrease.luk, heroId]
      );
      
      // Record transaction
      await executeQuery(
        `INSERT INTO transactions (user_address, transaction_type, amount, description)
         VALUES (?, 'spend', ?, ?)`,
        [walletAddress, process.env.HERO_UPGRADE_COST || '50', `Hero upgrade: ${hero.name}`]
      );
      
      res.json({
        message: 'Hero upgraded successfully',
        statIncrease,
        newLevel: hero.level + 1
      });
    } catch (error) {
      console.error('Blockchain upgrade error:', error);
      res.status(500).json({ error: 'Failed to process upgrade transaction' });
    }
  } catch (error) {
    console.error('Upgrade hero error:', error);
    res.status(500).json({ error: 'Failed to upgrade hero' });
  }
});

// Unlock skill
router.post('/:heroId/unlock-skill/:skillId', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    const { heroId, skillId } = req.params;
    
    // Verify hero ownership
    const heroes = await executeQuery(
      'SELECT * FROM heroes WHERE id = ? AND owner_address = ? AND is_active = TRUE',
      [heroId, walletAddress]
    );
    
    if (heroes.length === 0) {
      return res.status(404).json({ error: 'Hero not found' });
    }
    
    // Verify skill exists and belongs to hero
    const skills = await executeQuery(
      'SELECT * FROM hero_skills WHERE id = ? AND hero_id = ?',
      [skillId, heroId]
    );
    
    if (skills.length === 0) {
      return res.status(404).json({ error: 'Skill not found' });
    }
    
    const skill = skills[0];
    
    if (skill.is_unlocked) {
      return res.status(400).json({ error: 'Skill already unlocked' });
    }
    
    // Check hero level requirement
    const hero = heroes[0];
    if (hero.level < skill.unlock_level) {
      return res.status(400).json({ 
        error: `Hero must be level ${skill.unlock_level} to unlock this skill` 
      });
    }
    
    // Check if user has enough tokens
    let hasEnoughTokens = false;
    try {
      if (blockchainService.initialized) {
        const balance = await blockchainService.getTokenBalance(walletAddress);
        const unlockCost = blockchainService.parseEther(process.env.SKILL_UNLOCK_COST || '100');
        hasEnoughTokens = balance >= unlockCost;
      }
    } catch (error) {
      console.warn('Failed to check token balance:', error.message);
      return res.status(500).json({ error: 'Failed to verify token balance' });
    }
    
    if (!hasEnoughTokens) {
      return res.status(400).json({ error: 'Insufficient CQT tokens for skill unlock' });
    }
    
    try {
      // Process blockchain transaction
      if (blockchainService.initialized) {
        await blockchainService.unlockSkill(walletAddress);
      }
      
      // Unlock skill
      await executeQuery(
        'UPDATE hero_skills SET is_unlocked = TRUE WHERE id = ?',
        [skillId]
      );
      
      // Record transaction
      await executeQuery(
        `INSERT INTO transactions (user_address, transaction_type, amount, description)
         VALUES (?, 'spend', ?, ?)`,
        [walletAddress, process.env.SKILL_UNLOCK_COST || '100', `Skill unlock: ${skill.skill_name}`]
      );
      
      res.json({
        message: 'Skill unlocked successfully',
        skill: {
          id: skill.id,
          name: skill.skill_name,
          type: skill.skill_type,
          damage: skill.damage,
          cooldown: skill.cooldown
        }
      });
    } catch (error) {
      console.error('Blockchain skill unlock error:', error);
      res.status(500).json({ error: 'Failed to process skill unlock transaction' });
    }
  } catch (error) {
    console.error('Unlock skill error:', error);
    res.status(500).json({ error: 'Failed to unlock skill' });
  }
});

// Create new hero (for future expansion)
router.post('/create', verifyToken, [
  body('heroType').isIn(['warrior', 'mage', 'archer', 'assassin', 'tank']),
  body('name').isString().isLength({ min: 1, max: 50 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { walletAddress } = req.user;
    const { heroType, name } = req.body;
    
    // Check if user already has maximum heroes (limit to 10 for now)
    const heroCount = await executeQuery(
      'SELECT COUNT(*) as count FROM heroes WHERE owner_address = ? AND is_active = TRUE',
      [walletAddress]
    );
    
    if (heroCount[0].count >= 10) {
      return res.status(400).json({ error: 'Maximum hero limit reached' });
    }
    
    // Define base stats for each hero type
    const baseStats = {
      warrior: { hp: 120, atk: 25, def: 20, spd: 12, luk: 8 },
      mage: { hp: 80, atk: 35, def: 10, spd: 15, luk: 12 },
      archer: { hp: 100, atk: 30, def: 15, spd: 20, luk: 15 },
      assassin: { hp: 90, atk: 32, def: 12, spd: 25, luk: 18 },
      tank: { hp: 150, atk: 20, def: 30, spd: 8, luk: 5 }
    };
    
    const stats = baseStats[heroType];
    
    // Create hero
    const result = await executeQuery(
      `INSERT INTO heroes (owner_address, hero_type, name, hp, atk, def, spd, luk)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [walletAddress, heroType, name, stats.hp, stats.atk, stats.def, stats.spd, stats.luk]
    );
    
    const heroId = result.insertId;
    
    // Add default skills for the hero type
    await addDefaultSkillsToHero(heroId, heroType);
    
    res.json({
      message: 'Hero created successfully',
      heroId,
      hero: {
        id: heroId,
        type: heroType,
        name,
        level: 1,
        stats
      }
    });
  } catch (error) {
    console.error('Create hero error:', error);
    res.status(500).json({ error: 'Failed to create hero' });
  }
});

// Add default skills to a hero
async function addDefaultSkillsToHero(heroId, heroType) {
  try {
    const defaultSkills = await executeQuery(
      'SELECT * FROM default_hero_skills WHERE hero_type = ?',
      [heroType]
    );
    
    for (const skill of defaultSkills) {
      await executeQuery(
        `INSERT INTO hero_skills (hero_id, skill_name, skill_type, damage, cooldown, mana_cost, unlock_level)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [heroId, skill.skill_name, skill.skill_type, skill.damage, skill.cooldown, skill.mana_cost, skill.unlock_level]
      );
    }
  } catch (error) {
    console.error('Add default skills error:', error);
  }
}

module.exports = router;
