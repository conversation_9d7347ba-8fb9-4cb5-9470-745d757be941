{"ast": null, "code": "import { assert, assertArgument } from \"../utils/index.js\";\nimport { getAddress } from \"./address.js\";\n/**\n *  Returns true if %%value%% is an object which implements the\n *  [[Addressable]] interface.\n *\n *  @example:\n *    // Wallets and AbstractSigner sub-classes\n *    isAddressable(Wallet.createRandom())\n *    //_result:\n *\n *    // Contracts\n *    contract = new Contract(\"dai.tokens.ethers.eth\", [ ], provider)\n *    isAddressable(contract)\n *    //_result:\n */\nexport function isAddressable(value) {\n  return value && typeof value.getAddress === \"function\";\n}\n/**\n *  Returns true if %%value%% is a valid address.\n *\n *  @example:\n *    // Valid address\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Valid ICAP address\n *    isAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\")\n *    //_result:\n *\n *    // Invalid checksum\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Invalid ICAP checksum\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Not an address (an ENS name requires a provided and an\n *    // asynchronous API to access)\n *    isAddress(\"ricmoo.eth\")\n *    //_result:\n */\nexport function isAddress(value) {\n  try {\n    getAddress(value);\n    return true;\n  } catch (error) {}\n  return false;\n}\nasync function checkAddress(target, promise) {\n  const result = await promise;\n  if (result == null || result === \"******************************************\") {\n    assert(typeof target !== \"string\", \"unconfigured name\", \"UNCONFIGURED_NAME\", {\n      value: target\n    });\n    assertArgument(false, \"invalid AddressLike value; did not resolve to a value address\", \"target\", target);\n  }\n  return getAddress(result);\n}\n/**\n *  Resolves to an address for the %%target%%, which may be any\n *  supported address type, an [[Addressable]] or a Promise which\n *  resolves to an address.\n *\n *  If an ENS name is provided, but that name has not been correctly\n *  configured a [[UnconfiguredNameError]] is thrown.\n *\n *  @example:\n *    addr = \"******************************************\"\n *\n *    // Addresses are return synchronously\n *    resolveAddress(addr, provider)\n *    //_result:\n *\n *    // Address promises are resolved asynchronously\n *    resolveAddress(Promise.resolve(addr))\n *    //_result:\n *\n *    // ENS names are resolved asynchronously\n *    resolveAddress(\"dai.tokens.ethers.eth\", provider)\n *    //_result:\n *\n *    // Addressable objects are resolved asynchronously\n *    contract = new Contract(addr, [ ])\n *    resolveAddress(contract, provider)\n *    //_result:\n *\n *    // Unconfigured ENS names reject\n *    resolveAddress(\"nothing-here.ricmoo.eth\", provider)\n *    //_error:\n *\n *    // ENS names require a NameResolver object passed in\n *    // (notice the provider was omitted)\n *    resolveAddress(\"nothing-here.ricmoo.eth\")\n *    //_error:\n */\nexport function resolveAddress(target, resolver) {\n  if (typeof target === \"string\") {\n    if (target.match(/^0x[0-9a-f]{40}$/i)) {\n      return getAddress(target);\n    }\n    assert(resolver != null, \"ENS resolution requires a provider\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"resolveName\"\n    });\n    return checkAddress(target, resolver.resolveName(target));\n  } else if (isAddressable(target)) {\n    return checkAddress(target, target.getAddress());\n  } else if (target && typeof target.then === \"function\") {\n    return checkAddress(target, target);\n  }\n  assertArgument(false, \"unsupported addressable value\", \"target\", target);\n}", "map": {"version": 3, "names": ["assert", "assertArgument", "get<PERSON><PERSON><PERSON>", "isAddressable", "value", "is<PERSON>dd<PERSON>", "error", "checkAddress", "target", "promise", "result", "resolve<PERSON>ddress", "resolver", "match", "operation", "resolveName", "then"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\address\\checks.ts"], "sourcesContent": ["import { assert, assertArgument } from \"../utils/index.js\";\n\nimport { getAddress } from \"./address.js\";\n\nimport type { Addressable, AddressLike, NameResolver } from \"./index.js\";\n\n\n/**\n *  Returns true if %%value%% is an object which implements the\n *  [[Addressable]] interface.\n *\n *  @example:\n *    // Wallets and AbstractSigner sub-classes\n *    isAddressable(Wallet.createRandom())\n *    //_result:\n *\n *    // Contracts\n *    contract = new Contract(\"dai.tokens.ethers.eth\", [ ], provider)\n *    isAddressable(contract)\n *    //_result:\n */\nexport function isAddressable(value: any): value is Addressable {\n    return (value && typeof(value.getAddress) === \"function\");\n}\n\n/**\n *  Returns true if %%value%% is a valid address.\n *\n *  @example:\n *    // Valid address\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Valid ICAP address\n *    isAddress(\"XE65GB6LDNXYOFTX0NSV3FUWKOWIXAMJK36\")\n *    //_result:\n *\n *    // Invalid checksum\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Invalid ICAP checksum\n *    isAddress(\"******************************************\")\n *    //_result:\n *\n *    // Not an address (an ENS name requires a provided and an\n *    // asynchronous API to access)\n *    isAddress(\"ricmoo.eth\")\n *    //_result:\n */\nexport function isAddress(value: any): value is string {\n    try {\n        getAddress(value);\n        return true;\n    } catch (error) { }\n    return false;\n}\n\nasync function checkAddress(target: any, promise: Promise<null | string>): Promise<string> {\n    const result = await promise;\n    if (result == null || result === \"******************************************\") {\n        assert(typeof(target) !== \"string\", \"unconfigured name\", \"UNCONFIGURED_NAME\", { value: target });\n        assertArgument(false, \"invalid AddressLike value; did not resolve to a value address\", \"target\", target);\n    }\n    return getAddress(result);\n}\n\n/**\n *  Resolves to an address for the %%target%%, which may be any\n *  supported address type, an [[Addressable]] or a Promise which\n *  resolves to an address.\n *\n *  If an ENS name is provided, but that name has not been correctly\n *  configured a [[UnconfiguredNameError]] is thrown.\n *\n *  @example:\n *    addr = \"******************************************\"\n *\n *    // Addresses are return synchronously\n *    resolveAddress(addr, provider)\n *    //_result:\n *\n *    // Address promises are resolved asynchronously\n *    resolveAddress(Promise.resolve(addr))\n *    //_result:\n *\n *    // ENS names are resolved asynchronously\n *    resolveAddress(\"dai.tokens.ethers.eth\", provider)\n *    //_result:\n *\n *    // Addressable objects are resolved asynchronously\n *    contract = new Contract(addr, [ ])\n *    resolveAddress(contract, provider)\n *    //_result:\n *\n *    // Unconfigured ENS names reject\n *    resolveAddress(\"nothing-here.ricmoo.eth\", provider)\n *    //_error:\n *\n *    // ENS names require a NameResolver object passed in\n *    // (notice the provider was omitted)\n *    resolveAddress(\"nothing-here.ricmoo.eth\")\n *    //_error:\n */\nexport function resolveAddress(target: AddressLike, resolver?: null | NameResolver): string | Promise<string> {\n\n    if (typeof(target) === \"string\") {\n        if (target.match(/^0x[0-9a-f]{40}$/i)) { return getAddress(target); }\n\n        assert(resolver != null, \"ENS resolution requires a provider\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"resolveName\" });\n\n        return checkAddress(target, resolver.resolveName(target));\n\n    } else if (isAddressable(target)) {\n        return checkAddress(target, target.getAddress());\n\n    } else if (target && typeof(target.then) === \"function\") {\n        return checkAddress(target, target);\n    }\n\n    assertArgument(false, \"unsupported addressable value\", \"target\", target);\n}\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,cAAc,QAAQ,mBAAmB;AAE1D,SAASC,UAAU,QAAQ,cAAc;AAKzC;;;;;;;;;;;;;;AAcA,OAAM,SAAUC,aAAaA,CAACC,KAAU;EACpC,OAAQA,KAAK,IAAI,OAAOA,KAAK,CAACF,UAAW,KAAK,UAAU;AAC5D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAM,SAAUG,SAASA,CAACD,KAAU;EAChC,IAAI;IACAF,UAAU,CAACE,KAAK,CAAC;IACjB,OAAO,IAAI;GACd,CAAC,OAAOE,KAAK,EAAE;EAChB,OAAO,KAAK;AAChB;AAEA,eAAeC,YAAYA,CAACC,MAAW,EAAEC,OAA+B;EACpE,MAAMC,MAAM,GAAG,MAAMD,OAAO;EAC5B,IAAIC,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,4CAA4C,EAAE;IAC3EV,MAAM,CAAC,OAAOQ,MAAO,KAAK,QAAQ,EAAE,mBAAmB,EAAE,mBAAmB,EAAE;MAAEJ,KAAK,EAAEI;IAAM,CAAE,CAAC;IAChGP,cAAc,CAAC,KAAK,EAAE,+DAA+D,EAAE,QAAQ,EAAEO,MAAM,CAAC;;EAE5G,OAAON,UAAU,CAACQ,MAAM,CAAC;AAC7B;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,OAAM,SAAUC,cAAcA,CAACH,MAAmB,EAAEI,QAA8B;EAE9E,IAAI,OAAOJ,MAAO,KAAK,QAAQ,EAAE;IAC7B,IAAIA,MAAM,CAACK,KAAK,CAAC,mBAAmB,CAAC,EAAE;MAAE,OAAOX,UAAU,CAACM,MAAM,CAAC;;IAElER,MAAM,CAACY,QAAQ,IAAI,IAAI,EAAE,oCAAoC,EACzD,uBAAuB,EAAE;MAAEE,SAAS,EAAE;IAAa,CAAE,CAAC;IAE1D,OAAOP,YAAY,CAACC,MAAM,EAAEI,QAAQ,CAACG,WAAW,CAACP,MAAM,CAAC,CAAC;GAE5D,MAAM,IAAIL,aAAa,CAACK,MAAM,CAAC,EAAE;IAC9B,OAAOD,YAAY,CAACC,MAAM,EAAEA,MAAM,CAACN,UAAU,EAAE,CAAC;GAEnD,MAAM,IAAIM,MAAM,IAAI,OAAOA,MAAM,CAACQ,IAAK,KAAK,UAAU,EAAE;IACrD,OAAOT,YAAY,CAACC,MAAM,EAAEA,MAAM,CAAC;;EAGvCP,cAAc,CAAC,KAAK,EAAE,+BAA+B,EAAE,QAAQ,EAAEO,MAAM,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}