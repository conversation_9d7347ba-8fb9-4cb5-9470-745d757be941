{"ast": null, "code": "/**\n *  [[link-infura]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Linea (``linea``)\n *  - Linea Goerli Testnet (``linea-goerli``)\n *  - Linea Sepolia Testnet (``linea-sepolia``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:INFURA  [providers-infura]\n */\nimport { defineProperties, FetchRequest, assert, assertArgument } from \"../utils/index.js\";\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nimport { WebSocketProvider } from \"./provider-websocket.js\";\nconst defaultProjectId = \"84842078b09946638c03157f83405213\";\nfunction getHost(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"mainnet.infura.io\";\n    case \"goerli\":\n      return \"goerli.infura.io\";\n    case \"sepolia\":\n      return \"sepolia.infura.io\";\n    case \"arbitrum\":\n      return \"arbitrum-mainnet.infura.io\";\n    case \"arbitrum-goerli\":\n      return \"arbitrum-goerli.infura.io\";\n    case \"arbitrum-sepolia\":\n      return \"arbitrum-sepolia.infura.io\";\n    case \"base\":\n      return \"base-mainnet.infura.io\";\n    case \"base-goerlia\": // @TODO: Remove this typo in the future!\n    case \"base-goerli\":\n      return \"base-goerli.infura.io\";\n    case \"base-sepolia\":\n      return \"base-sepolia.infura.io\";\n    case \"bnb\":\n      return \"bsc-mainnet.infura.io\";\n    case \"bnbt\":\n      return \"bsc-testnet.infura.io\";\n    case \"linea\":\n      return \"linea-mainnet.infura.io\";\n    case \"linea-goerli\":\n      return \"linea-goerli.infura.io\";\n    case \"linea-sepolia\":\n      return \"linea-sepolia.infura.io\";\n    case \"matic\":\n      return \"polygon-mainnet.infura.io\";\n    case \"matic-amoy\":\n      return \"polygon-amoy.infura.io\";\n    case \"matic-mumbai\":\n      return \"polygon-mumbai.infura.io\";\n    case \"optimism\":\n      return \"optimism-mainnet.infura.io\";\n    case \"optimism-goerli\":\n      return \"optimism-goerli.infura.io\";\n    case \"optimism-sepolia\":\n      return \"optimism-sepolia.infura.io\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **InfuraWebSocketProvider** connects to the [[link-infura]]\n *  WebSocket end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-infura-signup).\n */\nexport class InfuraWebSocketProvider extends WebSocketProvider {\n  /**\n   *  The Project ID for the INFURA connection.\n   */\n  projectId;\n  /**\n   *  The Project Secret.\n   *\n   *  If null, no authenticated requests are made. This should not\n   *  be used outside of private contexts.\n   */\n  projectSecret;\n  /**\n   *  Creates a new **InfuraWebSocketProvider**.\n   */\n  constructor(network, projectId) {\n    const provider = new InfuraProvider(network, projectId);\n    const req = provider._getConnection();\n    assert(!req.credentials, \"INFURA WebSocket project secrets unsupported\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"InfuraProvider.getWebSocketProvider()\"\n    });\n    const url = req.url.replace(/^http/i, \"ws\").replace(\"/v3/\", \"/ws/v3/\");\n    super(url, provider._network);\n    defineProperties(this, {\n      projectId: provider.projectId,\n      projectSecret: provider.projectSecret\n    });\n  }\n  isCommunityResource() {\n    return this.projectId === defaultProjectId;\n  }\n}\n/**\n *  The **InfuraProvider** connects to the [[link-infura]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-infura-signup).\n */\nexport class InfuraProvider extends JsonRpcProvider {\n  /**\n   *  The Project ID for the INFURA connection.\n   */\n  projectId;\n  /**\n   *  The Project Secret.\n   *\n   *  If null, no authenticated requests are made. This should not\n   *  be used outside of private contexts.\n   */\n  projectSecret;\n  /**\n   *  Creates a new **InfuraProvider**.\n   */\n  constructor(_network, projectId, projectSecret) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (projectId == null) {\n      projectId = defaultProjectId;\n    }\n    if (projectSecret == null) {\n      projectSecret = null;\n    }\n    const request = InfuraProvider.getRequest(network, projectId, projectSecret);\n    super(request, network, {\n      staticNetwork: network\n    });\n    defineProperties(this, {\n      projectId,\n      projectSecret\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new InfuraProvider(chainId, this.projectId, this.projectSecret);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  isCommunityResource() {\n    return this.projectId === defaultProjectId;\n  }\n  /**\n   *  Creates a new **InfuraWebSocketProvider**.\n   */\n  static getWebSocketProvider(network, projectId) {\n    return new InfuraWebSocketProvider(network, projectId);\n  }\n  /**\n   *  Returns a prepared request for connecting to %%network%%\n   *  with %%projectId%% and %%projectSecret%%.\n   */\n  static getRequest(network, projectId, projectSecret) {\n    if (projectId == null) {\n      projectId = defaultProjectId;\n    }\n    if (projectSecret == null) {\n      projectSecret = null;\n    }\n    const request = new FetchRequest(`https:/\\/${getHost(network.name)}/v3/${projectId}`);\n    request.allowGzip = true;\n    if (projectSecret) {\n      request.setCredentials(\"\", projectSecret);\n    }\n    if (projectId === defaultProjectId) {\n      request.retryFunc = async (request, response, attempt) => {\n        showThrottleMessage(\"InfuraProvider\");\n        return true;\n      };\n    }\n    return request;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "FetchRequest", "assert", "assertArgument", "showThrottleMessage", "Network", "JsonRpcProvider", "WebSocketProvider", "defaultProjectId", "getHost", "name", "InfuraWebSocketProvider", "projectId", "projectSecret", "constructor", "network", "provider", "InfuraProvider", "req", "_getConnection", "credentials", "operation", "url", "replace", "_network", "isCommunityResource", "from", "request", "getRequest", "staticNetwork", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "getWebSocketProvider", "allowGzip", "setCredentials", "retryFunc", "response", "attempt"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-infura.ts"], "sourcesContent": ["/**\n *  [[link-infura]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Goerli Testnet (``goerli``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Arbitrum (``arbitrum``)\n *  - Arbitrum Goerli Testnet (``arbitrum-goerli``)\n *  - Arbitrum Sepolia Testnet (``arbitrum-sepolia``)\n *  - Base (``base``)\n *  - Base Goerlia Testnet (``base-goerli``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - BNB Smart Chain Mainnet (``bnb``)\n *  - BNB Smart Chain Testnet (``bnbt``)\n *  - Linea (``linea``)\n *  - Linea Goerli Testnet (``linea-goerli``)\n *  - Linea Sepolia Testnet (``linea-sepolia``)\n *  - Optimism (``optimism``)\n *  - Optimism Goerli Testnet (``optimism-goerli``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *  - Polygon Amoy Testnet (``matic-amoy``)\n *  - Polygon Mumbai Testnet (``matic-mumbai``)\n *\n *  @_subsection: api/providers/thirdparty:INFURA  [providers-infura]\n */\nimport {\n    defineProperties, FetchRequest, assert, assertArgument\n} from \"../utils/index.js\";\n\nimport { showThrottleMessage } from \"./community.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nimport { WebSocketProvider } from \"./provider-websocket.js\";\n\nimport type { AbstractProvider } from \"./abstract-provider.js\";\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\n\n\nconst defaultProjectId = \"84842078b09946638c03157f83405213\";\n\nfunction getHost(name: string): string {\n    switch(name) {\n        case \"mainnet\":\n            return \"mainnet.infura.io\";\n        case \"goerli\":\n            return \"goerli.infura.io\";\n        case \"sepolia\":\n            return \"sepolia.infura.io\";\n\n        case \"arbitrum\":\n            return \"arbitrum-mainnet.infura.io\";\n        case \"arbitrum-goerli\":\n            return \"arbitrum-goerli.infura.io\";\n        case \"arbitrum-sepolia\":\n            return \"arbitrum-sepolia.infura.io\";\n        case \"base\":\n            return \"base-mainnet.infura.io\";\n        case \"base-goerlia\": // @TODO: Remove this typo in the future!\n        case \"base-goerli\":\n            return \"base-goerli.infura.io\";\n        case \"base-sepolia\":\n            return \"base-sepolia.infura.io\";\n        case \"bnb\":\n            return \"bsc-mainnet.infura.io\";\n        case \"bnbt\":\n            return \"bsc-testnet.infura.io\";\n        case \"linea\":\n            return \"linea-mainnet.infura.io\";\n        case \"linea-goerli\":\n            return \"linea-goerli.infura.io\";\n        case \"linea-sepolia\":\n            return \"linea-sepolia.infura.io\";\n        case \"matic\":\n            return \"polygon-mainnet.infura.io\";\n        case \"matic-amoy\":\n            return \"polygon-amoy.infura.io\";\n        case \"matic-mumbai\":\n            return \"polygon-mumbai.infura.io\";\n        case \"optimism\":\n            return \"optimism-mainnet.infura.io\";\n        case \"optimism-goerli\":\n            return \"optimism-goerli.infura.io\";\n        case \"optimism-sepolia\":\n            return \"optimism-sepolia.infura.io\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n/**\n *  The **InfuraWebSocketProvider** connects to the [[link-infura]]\n *  WebSocket end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-infura-signup).\n */\nexport class InfuraWebSocketProvider extends WebSocketProvider implements CommunityResourcable {\n\n    /**\n     *  The Project ID for the INFURA connection.\n     */\n    readonly projectId!: string;\n\n    /**\n     *  The Project Secret.\n     *\n     *  If null, no authenticated requests are made. This should not\n     *  be used outside of private contexts.\n     */\n    readonly projectSecret!: null | string;\n\n    /**\n     *  Creates a new **InfuraWebSocketProvider**.\n     */\n    constructor(network?: Networkish, projectId?: string) {\n        const provider = new InfuraProvider(network, projectId);\n\n        const req = provider._getConnection();\n        assert(!req.credentials, \"INFURA WebSocket project secrets unsupported\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"InfuraProvider.getWebSocketProvider()\" });\n\n        const url = req.url.replace(/^http/i, \"ws\").replace(\"/v3/\", \"/ws/v3/\");\n        super(url, provider._network);\n\n        defineProperties<InfuraWebSocketProvider>(this, {\n            projectId: provider.projectId,\n            projectSecret: provider.projectSecret\n        });\n    }\n\n    isCommunityResource(): boolean {\n        return (this.projectId === defaultProjectId);\n    }\n}\n\n/**\n *  The **InfuraProvider** connects to the [[link-infura]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-infura-signup).\n */\nexport class InfuraProvider extends JsonRpcProvider implements CommunityResourcable {\n    /**\n     *  The Project ID for the INFURA connection.\n     */\n    readonly projectId!: string;\n\n    /**\n     *  The Project Secret.\n     *\n     *  If null, no authenticated requests are made. This should not\n     *  be used outside of private contexts.\n     */\n    readonly projectSecret!: null | string;\n\n    /**\n     *  Creates a new **InfuraProvider**.\n     */\n    constructor(_network?: Networkish, projectId?: null | string, projectSecret?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        if (projectId == null) { projectId = defaultProjectId; }\n        if (projectSecret == null) { projectSecret = null; }\n\n        const request = InfuraProvider.getRequest(network, projectId, projectSecret);\n        super(request, network, { staticNetwork: network });\n\n        defineProperties<InfuraProvider>(this, { projectId, projectSecret });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new InfuraProvider(chainId, this.projectId, this.projectSecret);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.projectId === defaultProjectId);\n    }\n\n    /**\n     *  Creates a new **InfuraWebSocketProvider**.\n     */\n    static getWebSocketProvider(network?: Networkish, projectId?: string): InfuraWebSocketProvider {\n        return new InfuraWebSocketProvider(network, projectId);\n    }\n\n    /**\n     *  Returns a prepared request for connecting to %%network%%\n     *  with %%projectId%% and %%projectSecret%%.\n     */\n    static getRequest(network: Network, projectId?: null | string, projectSecret?: null | string): FetchRequest {\n        if (projectId == null) { projectId = defaultProjectId; }\n        if (projectSecret == null) { projectSecret = null; }\n\n        const request = new FetchRequest(`https:/\\/${ getHost(network.name) }/v3/${ projectId }`);\n        request.allowGzip = true;\n        if (projectSecret) { request.setCredentials(\"\", projectSecret); }\n\n        if (projectId === defaultProjectId) {\n            request.retryFunc = async (request, response, attempt) => {\n                showThrottleMessage(\"InfuraProvider\");\n                return true;\n            };\n        }\n\n        return request;\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,SACIA,gBAAgB,EAAEC,YAAY,EAAEC,MAAM,EAAEC,cAAc,QACnD,mBAAmB;AAE1B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,iBAAiB,QAAQ,yBAAyB;AAO3D,MAAMC,gBAAgB,GAAG,kCAAkC;AAE3D,SAASC,OAAOA,CAACC,IAAY;EACzB,QAAOA,IAAI;IACP,KAAK,SAAS;MACV,OAAO,mBAAmB;IAC9B,KAAK,QAAQ;MACT,OAAO,kBAAkB;IAC7B,KAAK,SAAS;MACV,OAAO,mBAAmB;IAE9B,KAAK,UAAU;MACX,OAAO,4BAA4B;IACvC,KAAK,iBAAiB;MAClB,OAAO,2BAA2B;IACtC,KAAK,kBAAkB;MACnB,OAAO,4BAA4B;IACvC,KAAK,MAAM;MACP,OAAO,wBAAwB;IACnC,KAAK,cAAc,CAAC,CAAC;IACrB,KAAK,aAAa;MACd,OAAO,uBAAuB;IAClC,KAAK,cAAc;MACf,OAAO,wBAAwB;IACnC,KAAK,KAAK;MACN,OAAO,uBAAuB;IAClC,KAAK,MAAM;MACP,OAAO,uBAAuB;IAClC,KAAK,OAAO;MACR,OAAO,yBAAyB;IACpC,KAAK,cAAc;MACf,OAAO,wBAAwB;IACnC,KAAK,eAAe;MAChB,OAAO,yBAAyB;IACpC,KAAK,OAAO;MACR,OAAO,2BAA2B;IACtC,KAAK,YAAY;MACb,OAAO,wBAAwB;IACnC,KAAK,cAAc;MACf,OAAO,0BAA0B;IACrC,KAAK,UAAU;MACX,OAAO,4BAA4B;IACvC,KAAK,iBAAiB;MAClB,OAAO,2BAA2B;IACtC,KAAK,kBAAkB;MACnB,OAAO,4BAA4B;;EAG3CP,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEO,IAAI,CAAC;AACjE;AAEA;;;;;;;;;AASA,OAAM,MAAOC,uBAAwB,SAAQJ,iBAAiB;EAE1D;;;EAGSK,SAAS;EAElB;;;;;;EAMSC,aAAa;EAEtB;;;EAGAC,YAAYC,OAAoB,EAAEH,SAAkB;IAChD,MAAMI,QAAQ,GAAG,IAAIC,cAAc,CAACF,OAAO,EAAEH,SAAS,CAAC;IAEvD,MAAMM,GAAG,GAAGF,QAAQ,CAACG,cAAc,EAAE;IACrCjB,MAAM,CAAC,CAACgB,GAAG,CAACE,WAAW,EAAE,8CAA8C,EACnE,uBAAuB,EAAE;MAAEC,SAAS,EAAE;IAAuC,CAAE,CAAC;IAEpF,MAAMC,GAAG,GAAGJ,GAAG,CAACI,GAAG,CAACC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC;IACtE,KAAK,CAACD,GAAG,EAAEN,QAAQ,CAACQ,QAAQ,CAAC;IAE7BxB,gBAAgB,CAA0B,IAAI,EAAE;MAC5CY,SAAS,EAAEI,QAAQ,CAACJ,SAAS;MAC7BC,aAAa,EAAEG,QAAQ,CAACH;KAC3B,CAAC;EACN;EAEAY,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACb,SAAS,KAAKJ,gBAAgB;EAC/C;;AAGJ;;;;;;;;;AASA,OAAM,MAAOS,cAAe,SAAQX,eAAe;EAC/C;;;EAGSM,SAAS;EAElB;;;;;;EAMSC,aAAa;EAEtB;;;EAGAC,YAAYU,QAAqB,EAAEZ,SAAyB,EAAEC,aAA6B;IACvF,IAAIW,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMT,OAAO,GAAGV,OAAO,CAACqB,IAAI,CAACF,QAAQ,CAAC;IACtC,IAAIZ,SAAS,IAAI,IAAI,EAAE;MAAEA,SAAS,GAAGJ,gBAAgB;;IACrD,IAAIK,aAAa,IAAI,IAAI,EAAE;MAAEA,aAAa,GAAG,IAAI;;IAEjD,MAAMc,OAAO,GAAGV,cAAc,CAACW,UAAU,CAACb,OAAO,EAAEH,SAAS,EAAEC,aAAa,CAAC;IAC5E,KAAK,CAACc,OAAO,EAAEZ,OAAO,EAAE;MAAEc,aAAa,EAAEd;IAAO,CAAE,CAAC;IAEnDf,gBAAgB,CAAiB,IAAI,EAAE;MAAEY,SAAS;MAAEC;IAAa,CAAE,CAAC;EACxE;EAEAiB,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAId,cAAc,CAACc,OAAO,EAAE,IAAI,CAACnB,SAAS,EAAE,IAAI,CAACC,aAAa,CAAC;KACzE,CAAC,OAAOmB,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEAN,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACb,SAAS,KAAKJ,gBAAgB;EAC/C;EAEA;;;EAGA,OAAOyB,oBAAoBA,CAAClB,OAAoB,EAAEH,SAAkB;IAChE,OAAO,IAAID,uBAAuB,CAACI,OAAO,EAAEH,SAAS,CAAC;EAC1D;EAEA;;;;EAIA,OAAOgB,UAAUA,CAACb,OAAgB,EAAEH,SAAyB,EAAEC,aAA6B;IACxF,IAAID,SAAS,IAAI,IAAI,EAAE;MAAEA,SAAS,GAAGJ,gBAAgB;;IACrD,IAAIK,aAAa,IAAI,IAAI,EAAE;MAAEA,aAAa,GAAG,IAAI;;IAEjD,MAAMc,OAAO,GAAG,IAAI1B,YAAY,CAAC,YAAaQ,OAAO,CAACM,OAAO,CAACL,IAAI,CAAE,OAAQE,SAAU,EAAE,CAAC;IACzFe,OAAO,CAACO,SAAS,GAAG,IAAI;IACxB,IAAIrB,aAAa,EAAE;MAAEc,OAAO,CAACQ,cAAc,CAAC,EAAE,EAAEtB,aAAa,CAAC;;IAE9D,IAAID,SAAS,KAAKJ,gBAAgB,EAAE;MAChCmB,OAAO,CAACS,SAAS,GAAG,OAAOT,OAAO,EAAEU,QAAQ,EAAEC,OAAO,KAAI;QACrDlC,mBAAmB,CAAC,gBAAgB,CAAC;QACrC,OAAO,IAAI;MACf,CAAC;;IAGL,OAAOuB,OAAO;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}