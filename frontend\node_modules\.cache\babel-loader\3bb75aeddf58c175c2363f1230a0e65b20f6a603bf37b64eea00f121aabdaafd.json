{"ast": null, "code": "/**\n *  The **FixedNumber** class permits using values with decimal places,\n *  using fixed-pont math.\n *\n *  Fixed-point math is still based on integers under-the-hood, but uses an\n *  internal offset to store fractional components below, and each operation\n *  corrects for this after each operation.\n *\n *  @_section: api/utils/fixed-point-math:Fixed-Point Maths  [about-fixed-point-math]\n */\nimport { getBytes } from \"./data.js\";\nimport { assert, assertArgument, assertPrivate } from \"./errors.js\";\nimport { getBigInt, getNumber, fromTwos, mask, toBigInt } from \"./maths.js\";\nimport { defineProperties } from \"./properties.js\";\nconst BN_N1 = BigInt(-1);\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_5 = BigInt(5);\nconst _guard = {};\n// Constant to pull zeros from for multipliers\nlet Zeros = \"0000\";\nwhile (Zeros.length < 80) {\n  Zeros += Zeros;\n}\n// Returns a string \"1\" followed by decimal \"0\"s\nfunction getTens(decimals) {\n  let result = Zeros;\n  while (result.length < decimals) {\n    result += result;\n  }\n  return BigInt(\"1\" + result.substring(0, decimals));\n}\nfunction checkValue(val, format, safeOp) {\n  const width = BigInt(format.width);\n  if (format.signed) {\n    const limit = BN_1 << width - BN_1;\n    assert(safeOp == null || val >= -limit && val < limit, \"overflow\", \"NUMERIC_FAULT\", {\n      operation: safeOp,\n      fault: \"overflow\",\n      value: val\n    });\n    if (val > BN_0) {\n      val = fromTwos(mask(val, width), width);\n    } else {\n      val = -fromTwos(mask(-val, width), width);\n    }\n  } else {\n    const limit = BN_1 << width;\n    assert(safeOp == null || val >= 0 && val < limit, \"overflow\", \"NUMERIC_FAULT\", {\n      operation: safeOp,\n      fault: \"overflow\",\n      value: val\n    });\n    val = (val % limit + limit) % limit & limit - BN_1;\n  }\n  return val;\n}\nfunction getFormat(value) {\n  if (typeof value === \"number\") {\n    value = `fixed128x${value}`;\n  }\n  let signed = true;\n  let width = 128;\n  let decimals = 18;\n  if (typeof value === \"string\") {\n    // Parse the format string\n    if (value === \"fixed\") {\n      // defaults...\n    } else if (value === \"ufixed\") {\n      signed = false;\n    } else {\n      const match = value.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);\n      assertArgument(match, \"invalid fixed format\", \"format\", value);\n      signed = match[1] !== \"u\";\n      width = parseInt(match[2]);\n      decimals = parseInt(match[3]);\n    }\n  } else if (value) {\n    // Extract the values from the object\n    const v = value;\n    const check = (key, type, defaultValue) => {\n      if (v[key] == null) {\n        return defaultValue;\n      }\n      assertArgument(typeof v[key] === type, \"invalid fixed format (\" + key + \" not \" + type + \")\", \"format.\" + key, v[key]);\n      return v[key];\n    };\n    signed = check(\"signed\", \"boolean\", signed);\n    width = check(\"width\", \"number\", width);\n    decimals = check(\"decimals\", \"number\", decimals);\n  }\n  assertArgument(width % 8 === 0, \"invalid FixedNumber width (not byte aligned)\", \"format.width\", width);\n  assertArgument(decimals <= 80, \"invalid FixedNumber decimals (too large)\", \"format.decimals\", decimals);\n  const name = (signed ? \"\" : \"u\") + \"fixed\" + String(width) + \"x\" + String(decimals);\n  return {\n    signed,\n    width,\n    decimals,\n    name\n  };\n}\nfunction toString(val, decimals) {\n  let negative = \"\";\n  if (val < BN_0) {\n    negative = \"-\";\n    val *= BN_N1;\n  }\n  let str = val.toString();\n  // No decimal point for whole values\n  if (decimals === 0) {\n    return negative + str;\n  }\n  // Pad out to the whole component (including a whole digit)\n  while (str.length <= decimals) {\n    str = Zeros + str;\n  }\n  // Insert the decimal point\n  const index = str.length - decimals;\n  str = str.substring(0, index) + \".\" + str.substring(index);\n  // Trim the whole component (leaving at least one 0)\n  while (str[0] === \"0\" && str[1] !== \".\") {\n    str = str.substring(1);\n  }\n  // Trim the decimal component (leaving at least one 0)\n  while (str[str.length - 1] === \"0\" && str[str.length - 2] !== \".\") {\n    str = str.substring(0, str.length - 1);\n  }\n  return negative + str;\n}\n/**\n *  A FixedNumber represents a value over its [[FixedFormat]]\n *  arithmetic field.\n *\n *  A FixedNumber can be used to perform math, losslessly, on\n *  values which have decmial places.\n *\n *  A FixedNumber has a fixed bit-width to store values in, and stores all\n *  values internally by multiplying the value by 10 raised to the power of\n *  %%decimals%%.\n *\n *  If operations are performed that cause a value to grow too high (close to\n *  positive infinity) or too low (close to negative infinity), the value\n *  is said to //overflow//.\n *\n *  For example, an 8-bit signed value, with 0 decimals may only be within\n *  the range ``-128`` to ``127``; so ``-128 - 1`` will overflow and become\n *  ``127``. Likewise, ``127 + 1`` will overflow and become ``-127``.\n *\n *  Many operation have a normal and //unsafe// variant. The normal variant\n *  will throw a [[NumericFaultError]] on any overflow, while the //unsafe//\n *  variant will silently allow overflow, corrupting its value value.\n *\n *  If operations are performed that cause a value to become too small\n *  (close to zero), the value loses precison and is said to //underflow//.\n *\n *  For example, a value with 1 decimal place may store a number as small\n *  as ``0.1``, but the value of ``0.1 / 2`` is ``0.05``, which cannot fit\n *  into 1 decimal place, so underflow occurs which means precision is lost\n *  and the value becomes ``0``.\n *\n *  Some operations have a normal and //signalling// variant. The normal\n *  variant will silently ignore underflow, while the //signalling// variant\n *  will thow a [[NumericFaultError]] on underflow.\n */\nexport class FixedNumber {\n  /**\n   *  The specific fixed-point arithmetic field for this value.\n   */\n  format;\n  #format;\n  // The actual value (accounting for decimals)\n  #val;\n  // A base-10 value to multiple values by to maintain the magnitude\n  #tens;\n  /**\n   *  This is a property so console.log shows a human-meaningful value.\n   *\n   *  @private\n   */\n  _value;\n  // Use this when changing this file to get some typing info,\n  // but then switch to any to mask the internal type\n  //constructor(guard: any, value: bigint, format: _FixedFormat) {\n  /**\n   *  @private\n   */\n  constructor(guard, value, format) {\n    assertPrivate(guard, _guard, \"FixedNumber\");\n    this.#val = value;\n    this.#format = format;\n    const _value = toString(value, format.decimals);\n    defineProperties(this, {\n      format: format.name,\n      _value\n    });\n    this.#tens = getTens(format.decimals);\n  }\n  /**\n   *  If true, negative values are permitted, otherwise only\n   *  positive values and zero are allowed.\n   */\n  get signed() {\n    return this.#format.signed;\n  }\n  /**\n   *  The number of bits available to store the value.\n   */\n  get width() {\n    return this.#format.width;\n  }\n  /**\n   *  The number of decimal places in the fixed-point arithment field.\n   */\n  get decimals() {\n    return this.#format.decimals;\n  }\n  /**\n   *  The value as an integer, based on the smallest unit the\n   *  [[decimals]] allow.\n   */\n  get value() {\n    return this.#val;\n  }\n  #checkFormat(other) {\n    assertArgument(this.format === other.format, \"incompatible format; use fixedNumber.toFormat\", \"other\", other);\n  }\n  #checkValue(val, safeOp) {\n    /*\n            const width = BigInt(this.width);\n            if (this.signed) {\n                const limit = (BN_1 << (width - BN_1));\n                assert(safeOp == null || (val >= -limit  && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n                    operation: <string>safeOp, fault: \"overflow\", value: val\n                });\n    \n                if (val > BN_0) {\n                    val = fromTwos(mask(val, width), width);\n                } else {\n                    val = -fromTwos(mask(-val, width), width);\n                }\n    \n            } else {\n                const masked = mask(val, width);\n                assert(safeOp == null || (val >= 0 && val === masked), \"overflow\", \"NUMERIC_FAULT\", {\n                    operation: <string>safeOp, fault: \"overflow\", value: val\n                });\n                val = masked;\n            }\n    */\n    val = checkValue(val, this.#format, safeOp);\n    return new FixedNumber(_guard, val, this.#format);\n  }\n  #add(o, safeOp) {\n    this.#checkFormat(o);\n    return this.#checkValue(this.#val + o.#val, safeOp);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% added\n   *  to %%other%%, ignoring overflow.\n   */\n  addUnsafe(other) {\n    return this.#add(other);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% added\n   *  to %%other%%. A [[NumericFaultError]] is thrown if overflow\n   *  occurs.\n   */\n  add(other) {\n    return this.#add(other, \"add\");\n  }\n  #sub(o, safeOp) {\n    this.#checkFormat(o);\n    return this.#checkValue(this.#val - o.#val, safeOp);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n   *  from %%this%%, ignoring overflow.\n   */\n  subUnsafe(other) {\n    return this.#sub(other);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n   *  from %%this%%. A [[NumericFaultError]] is thrown if overflow\n   *  occurs.\n   */\n  sub(other) {\n    return this.#sub(other, \"sub\");\n  }\n  #mul(o, safeOp) {\n    this.#checkFormat(o);\n    return this.#checkValue(this.#val * o.#val / this.#tens, safeOp);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n   *  by %%other%%, ignoring overflow and underflow (precision loss).\n   */\n  mulUnsafe(other) {\n    return this.#mul(other);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n   *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n   *  occurs.\n   */\n  mul(other) {\n    return this.#mul(other, \"mul\");\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n   *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n   *  occurs or if underflow (precision loss) occurs.\n   */\n  mulSignal(other) {\n    this.#checkFormat(other);\n    const value = this.#val * other.#val;\n    assert(value % this.#tens === BN_0, \"precision lost during signalling mul\", \"NUMERIC_FAULT\", {\n      operation: \"mulSignal\",\n      fault: \"underflow\",\n      value: this\n    });\n    return this.#checkValue(value / this.#tens, \"mulSignal\");\n  }\n  #div(o, safeOp) {\n    assert(o.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n      operation: \"div\",\n      fault: \"divide-by-zero\",\n      value: this\n    });\n    this.#checkFormat(o);\n    return this.#checkValue(this.#val * this.#tens / o.#val, safeOp);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n   *  by %%other%%, ignoring underflow (precision loss). A\n   *  [[NumericFaultError]] is thrown if overflow occurs.\n   */\n  divUnsafe(other) {\n    return this.#div(other);\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n   *  by %%other%%, ignoring underflow (precision loss). A\n   *  [[NumericFaultError]] is thrown if overflow occurs.\n   */\n  div(other) {\n    return this.#div(other, \"div\");\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n   *  by %%other%%. A [[NumericFaultError]] is thrown if underflow\n   *  (precision loss) occurs.\n   */\n  divSignal(other) {\n    assert(other.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n      operation: \"div\",\n      fault: \"divide-by-zero\",\n      value: this\n    });\n    this.#checkFormat(other);\n    const value = this.#val * this.#tens;\n    assert(value % other.#val === BN_0, \"precision lost during signalling div\", \"NUMERIC_FAULT\", {\n      operation: \"divSignal\",\n      fault: \"underflow\",\n      value: this\n    });\n    return this.#checkValue(value / other.#val, \"divSignal\");\n  }\n  /**\n   *  Returns a comparison result between %%this%% and %%other%%.\n   *\n   *  This is suitable for use in sorting, where ``-1`` implies %%this%%\n   *  is smaller, ``1`` implies %%this%% is larger and ``0`` implies\n   *  both are equal.\n   */\n  cmp(other) {\n    let a = this.value,\n      b = other.value;\n    // Coerce a and b to the same magnitude\n    const delta = this.decimals - other.decimals;\n    if (delta > 0) {\n      b *= getTens(delta);\n    } else if (delta < 0) {\n      a *= getTens(-delta);\n    }\n    // Comnpare\n    if (a < b) {\n      return -1;\n    }\n    if (a > b) {\n      return 1;\n    }\n    return 0;\n  }\n  /**\n   *  Returns true if %%other%% is equal to %%this%%.\n   */\n  eq(other) {\n    return this.cmp(other) === 0;\n  }\n  /**\n   *  Returns true if %%other%% is less than to %%this%%.\n   */\n  lt(other) {\n    return this.cmp(other) < 0;\n  }\n  /**\n   *  Returns true if %%other%% is less than or equal to %%this%%.\n   */\n  lte(other) {\n    return this.cmp(other) <= 0;\n  }\n  /**\n   *  Returns true if %%other%% is greater than to %%this%%.\n   */\n  gt(other) {\n    return this.cmp(other) > 0;\n  }\n  /**\n   *  Returns true if %%other%% is greater than or equal to %%this%%.\n   */\n  gte(other) {\n    return this.cmp(other) >= 0;\n  }\n  /**\n   *  Returns a new [[FixedNumber]] which is the largest **integer**\n   *  that is less than or equal to %%this%%.\n   *\n   *  The decimal component of the result will always be ``0``.\n   */\n  floor() {\n    let val = this.#val;\n    if (this.#val < BN_0) {\n      val -= this.#tens - BN_1;\n    }\n    val = this.#val / this.#tens * this.#tens;\n    return this.#checkValue(val, \"floor\");\n  }\n  /**\n   *  Returns a new [[FixedNumber]] which is the smallest **integer**\n   *  that is greater than or equal to %%this%%.\n   *\n   *  The decimal component of the result will always be ``0``.\n   */\n  ceiling() {\n    let val = this.#val;\n    if (this.#val > BN_0) {\n      val += this.#tens - BN_1;\n    }\n    val = this.#val / this.#tens * this.#tens;\n    return this.#checkValue(val, \"ceiling\");\n  }\n  /**\n   *  Returns a new [[FixedNumber]] with the decimal component\n   *  rounded up on ties at %%decimals%% places.\n   */\n  round(decimals) {\n    if (decimals == null) {\n      decimals = 0;\n    }\n    // Not enough precision to not already be rounded\n    if (decimals >= this.decimals) {\n      return this;\n    }\n    const delta = this.decimals - decimals;\n    const bump = BN_5 * getTens(delta - 1);\n    let value = this.value + bump;\n    const tens = getTens(delta);\n    value = value / tens * tens;\n    checkValue(value, this.#format, \"round\");\n    return new FixedNumber(_guard, value, this.#format);\n  }\n  /**\n   *  Returns true if %%this%% is equal to ``0``.\n   */\n  isZero() {\n    return this.#val === BN_0;\n  }\n  /**\n   *  Returns true if %%this%% is less than ``0``.\n   */\n  isNegative() {\n    return this.#val < BN_0;\n  }\n  /**\n   *  Returns the string representation of %%this%%.\n   */\n  toString() {\n    return this._value;\n  }\n  /**\n   *  Returns a float approximation.\n   *\n   *  Due to IEEE 754 precission (or lack thereof), this function\n   *  can only return an approximation and most values will contain\n   *  rounding errors.\n   */\n  toUnsafeFloat() {\n    return parseFloat(this.toString());\n  }\n  /**\n   *  Return a new [[FixedNumber]] with the same value but has had\n   *  its field set to %%format%%.\n   *\n   *  This will throw if the value cannot fit into %%format%%.\n   */\n  toFormat(format) {\n    return FixedNumber.fromString(this.toString(), format);\n  }\n  /**\n   *  Creates a new [[FixedNumber]] for %%value%% divided by\n   *  %%decimal%% places with %%format%%.\n   *\n   *  This will throw a [[NumericFaultError]] if %%value%% (once adjusted\n   *  for %%decimals%%) cannot fit in %%format%%, either due to overflow\n   *  or underflow (precision loss).\n   */\n  static fromValue(_value, _decimals, _format) {\n    const decimals = _decimals == null ? 0 : getNumber(_decimals);\n    const format = getFormat(_format);\n    let value = getBigInt(_value, \"value\");\n    const delta = decimals - format.decimals;\n    if (delta > 0) {\n      const tens = getTens(delta);\n      assert(value % tens === BN_0, \"value loses precision for format\", \"NUMERIC_FAULT\", {\n        operation: \"fromValue\",\n        fault: \"underflow\",\n        value: _value\n      });\n      value /= tens;\n    } else if (delta < 0) {\n      value *= getTens(-delta);\n    }\n    checkValue(value, format, \"fromValue\");\n    return new FixedNumber(_guard, value, format);\n  }\n  /**\n   *  Creates a new [[FixedNumber]] for %%value%% with %%format%%.\n   *\n   *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n   *  in %%format%%, either due to overflow or underflow (precision loss).\n   */\n  static fromString(_value, _format) {\n    const match = _value.match(/^(-?)([0-9]*)\\.?([0-9]*)$/);\n    assertArgument(match && match[2].length + match[3].length > 0, \"invalid FixedNumber string value\", \"value\", _value);\n    const format = getFormat(_format);\n    let whole = match[2] || \"0\",\n      decimal = match[3] || \"\";\n    // Pad out the decimals\n    while (decimal.length < format.decimals) {\n      decimal += Zeros;\n    }\n    // Check precision is safe\n    assert(decimal.substring(format.decimals).match(/^0*$/), \"too many decimals for format\", \"NUMERIC_FAULT\", {\n      operation: \"fromString\",\n      fault: \"underflow\",\n      value: _value\n    });\n    // Remove extra padding\n    decimal = decimal.substring(0, format.decimals);\n    const value = BigInt(match[1] + whole + decimal);\n    checkValue(value, format, \"fromString\");\n    return new FixedNumber(_guard, value, format);\n  }\n  /**\n   *  Creates a new [[FixedNumber]] with the big-endian representation\n   *  %%value%% with %%format%%.\n   *\n   *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n   *  in %%format%% due to overflow.\n   */\n  static fromBytes(_value, _format) {\n    let value = toBigInt(getBytes(_value, \"value\"));\n    const format = getFormat(_format);\n    if (format.signed) {\n      value = fromTwos(value, format.width);\n    }\n    checkValue(value, format, \"fromBytes\");\n    return new FixedNumber(_guard, value, format);\n  }\n}\n//const f1 = FixedNumber.fromString(\"12.56\", \"fixed16x2\");\n//const f2 = FixedNumber.fromString(\"0.3\", \"fixed16x2\");\n//console.log(f1.divSignal(f2));\n//const BUMP = FixedNumber.from(\"0.5\");", "map": {"version": 3, "names": ["getBytes", "assert", "assertArgument", "assertPrivate", "getBigInt", "getNumber", "fromTwos", "mask", "toBigInt", "defineProperties", "BN_N1", "BigInt", "BN_0", "BN_1", "BN_5", "_guard", "Zeros", "length", "getTens", "decimals", "result", "substring", "checkValue", "val", "format", "safeOp", "width", "signed", "limit", "operation", "fault", "value", "getFormat", "match", "parseInt", "v", "check", "key", "type", "defaultValue", "name", "String", "toString", "negative", "str", "index", "FixedNumber", "tens", "_value", "constructor", "guard", "checkFormat", "#checkFormat", "other", "#checkValue", "add", "#add", "o", "addUnsafe", "sub", "#sub", "subUnsafe", "mul", "#mul", "mulUnsafe", "mul<PERSON><PERSON><PERSON>", "div", "#div", "divUnsafe", "divSignal", "cmp", "a", "b", "delta", "eq", "lt", "lte", "gt", "gte", "floor", "ceiling", "round", "bump", "isZero", "isNegative", "toUnsafeFloat", "parseFloat", "toFormat", "fromString", "fromValue", "_decimals", "_format", "whole", "decimal", "fromBytes"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\fixednumber.ts"], "sourcesContent": ["/**\n *  The **FixedNumber** class permits using values with decimal places,\n *  using fixed-pont math.\n *\n *  Fixed-point math is still based on integers under-the-hood, but uses an\n *  internal offset to store fractional components below, and each operation\n *  corrects for this after each operation.\n *\n *  @_section: api/utils/fixed-point-math:Fixed-Point Maths  [about-fixed-point-math]\n */\nimport { getBytes } from \"./data.js\";\nimport { assert, assertArgument, assertPrivate } from \"./errors.js\";\nimport {\n    getBigInt, getNumber, fromTwos, mask, toBigInt\n} from \"./maths.js\";\nimport { defineProperties } from \"./properties.js\";\n\nimport type { BigNumberish, BytesLike, Numeric } from \"./index.js\";\n\nconst BN_N1 = BigInt(-1);\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_5 = BigInt(5);\n\nconst _guard = { };\n\n\n// Constant to pull zeros from for multipliers\nlet Zeros = \"0000\";\nwhile (Zeros.length < 80) { Zeros += Zeros; }\n\n// Returns a string \"1\" followed by decimal \"0\"s\nfunction getTens(decimals: number): bigint {\n    let result = Zeros;\n    while (result.length < decimals) { result += result; }\n    return BigInt(\"1\" + result.substring(0, decimals));\n}\n\n\n\n    /*\n     *  Returns a new FixedFormat for %%value%%.\n     *\n     *  If %%value%% is specified as a ``number``, the bit-width is\n     *  128 bits and %%value%% is used for the ``decimals``.\n     *\n     *  A string %%value%% may begin with ``fixed`` or ``ufixed``\n     *  for signed and unsigned respectfully. If no other properties\n     *  are specified, the bit-width is 128-bits with 18 decimals.\n     *\n     *  To specify the bit-width and demicals, append them separated\n     *  by an ``\"x\"`` to the %%value%%.\n     *\n     *  For example, ``ufixed128x18`` describes an unsigned, 128-bit\n     *  wide format with 18 decimals.\n     *\n     *  If %%value%% is an other object, its properties for ``signed``,\n     *  ``width`` and ``decimals`` are checked.\n     */\n\n/**\n *  A description of a fixed-point arithmetic field.\n *\n *  When specifying the fixed format, the values override the default of\n *  a ``fixed128x18``, which implies a signed 128-bit value with 18\n *  decimals of precision.\n *\n *  The alias ``fixed`` and ``ufixed`` can be used for ``fixed128x18`` and\n *  ``ufixed128x18`` respectively.\n *\n *  When a fixed format string begins with a ``u``, it indicates the field\n *  is unsigned, so any negative values will overflow. The first number\n *  indicates the bit-width and the second number indicates the decimal\n *  precision.\n *\n *  When a ``number`` is used for a fixed format, it indicates the number\n *  of decimal places, and the default width and signed-ness will be used.\n *\n *  The bit-width must be byte aligned and the decimals can be at most 80.\n */\nexport type FixedFormat = number | string | {\n    signed?: boolean,\n    width?: number,\n    decimals?: number\n};\n\nfunction checkValue(val: bigint, format: _FixedFormat, safeOp?: string): bigint {\n    const width = BigInt(format.width);\n    if (format.signed) {\n        const limit = (BN_1 << (width - BN_1));\n        assert(safeOp == null || (val >= -limit  && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n            operation: <string>safeOp, fault: \"overflow\", value: val\n        });\n\n        if (val > BN_0) {\n            val = fromTwos(mask(val, width), width);\n        } else {\n            val = -fromTwos(mask(-val, width), width);\n        }\n\n    } else {\n        const limit = (BN_1 << width);\n        assert(safeOp == null || (val >= 0 && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n            operation: <string>safeOp, fault: \"overflow\", value: val\n        });\n        val = (((val % limit) + limit) % limit) & (limit - BN_1);\n    }\n\n    return val;\n}\n\ntype _FixedFormat = { signed: boolean, width: number, decimals: number, name: string }\n\nfunction getFormat(value?: FixedFormat): _FixedFormat {\n    if (typeof(value) === \"number\") { value = `fixed128x${value}` }\n\n    let signed = true;\n    let width = 128;\n    let decimals = 18;\n\n    if (typeof(value) === \"string\") {\n        // Parse the format string\n        if (value === \"fixed\") {\n            // defaults...\n        } else if (value === \"ufixed\") {\n            signed = false;\n        } else {\n            const match = value.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);\n            assertArgument(match, \"invalid fixed format\", \"format\", value);\n            signed = (match[1] !== \"u\");\n            width = parseInt(match[2]);\n            decimals = parseInt(match[3]);\n        }\n    } else if (value) {\n        // Extract the values from the object\n        const v: any = value;\n        const check = (key: string, type: string, defaultValue: any): any => {\n            if (v[key] == null) { return defaultValue; }\n            assertArgument(typeof(v[key]) === type,\n                \"invalid fixed format (\" + key + \" not \" + type +\")\", \"format.\" + key, v[key]);\n            return v[key];\n        }\n        signed = check(\"signed\", \"boolean\", signed);\n        width = check(\"width\", \"number\", width);\n        decimals = check(\"decimals\", \"number\", decimals);\n    }\n\n    assertArgument((width % 8) === 0, \"invalid FixedNumber width (not byte aligned)\", \"format.width\", width);\n    assertArgument(decimals <= 80, \"invalid FixedNumber decimals (too large)\", \"format.decimals\", decimals);\n\n    const name = (signed ? \"\": \"u\") + \"fixed\" + String(width) + \"x\" + String(decimals);\n\n    return { signed, width, decimals, name };\n}\n\nfunction toString(val: bigint, decimals: number) {\n    let negative = \"\";\n    if (val < BN_0) {\n        negative = \"-\";\n        val *= BN_N1;\n    }\n\n    let str = val.toString();\n\n    // No decimal point for whole values\n    if (decimals === 0) { return (negative + str); }\n\n    // Pad out to the whole component (including a whole digit)\n    while (str.length <= decimals) { str = Zeros + str; }\n\n    // Insert the decimal point\n    const index = str.length - decimals;\n    str = str.substring(0, index) + \".\" + str.substring(index);\n\n    // Trim the whole component (leaving at least one 0)\n    while (str[0] === \"0\" && str[1] !== \".\") {\n        str = str.substring(1);\n    }\n\n    // Trim the decimal component (leaving at least one 0)\n    while (str[str.length - 1] === \"0\" && str[str.length - 2] !== \".\") {\n        str = str.substring(0, str.length - 1);\n    }\n\n    return (negative + str);\n}\n\n\n/**\n *  A FixedNumber represents a value over its [[FixedFormat]]\n *  arithmetic field.\n *\n *  A FixedNumber can be used to perform math, losslessly, on\n *  values which have decmial places.\n *\n *  A FixedNumber has a fixed bit-width to store values in, and stores all\n *  values internally by multiplying the value by 10 raised to the power of\n *  %%decimals%%.\n *\n *  If operations are performed that cause a value to grow too high (close to\n *  positive infinity) or too low (close to negative infinity), the value\n *  is said to //overflow//.\n *\n *  For example, an 8-bit signed value, with 0 decimals may only be within\n *  the range ``-128`` to ``127``; so ``-128 - 1`` will overflow and become\n *  ``127``. Likewise, ``127 + 1`` will overflow and become ``-127``.\n *\n *  Many operation have a normal and //unsafe// variant. The normal variant\n *  will throw a [[NumericFaultError]] on any overflow, while the //unsafe//\n *  variant will silently allow overflow, corrupting its value value.\n *\n *  If operations are performed that cause a value to become too small\n *  (close to zero), the value loses precison and is said to //underflow//.\n *\n *  For example, a value with 1 decimal place may store a number as small\n *  as ``0.1``, but the value of ``0.1 / 2`` is ``0.05``, which cannot fit\n *  into 1 decimal place, so underflow occurs which means precision is lost\n *  and the value becomes ``0``.\n *\n *  Some operations have a normal and //signalling// variant. The normal\n *  variant will silently ignore underflow, while the //signalling// variant\n *  will thow a [[NumericFaultError]] on underflow.\n */\nexport class FixedNumber {\n\n    /**\n     *  The specific fixed-point arithmetic field for this value.\n     */\n    readonly format!: string;\n\n    readonly #format: _FixedFormat;\n\n    // The actual value (accounting for decimals)\n    #val: bigint;\n\n    // A base-10 value to multiple values by to maintain the magnitude\n    readonly #tens: bigint;\n\n    /**\n     *  This is a property so console.log shows a human-meaningful value.\n     *\n     *  @private\n     */\n    readonly _value!: string;\n\n    // Use this when changing this file to get some typing info,\n    // but then switch to any to mask the internal type\n    //constructor(guard: any, value: bigint, format: _FixedFormat) {\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, value: bigint, format: any) {\n        assertPrivate(guard, _guard, \"FixedNumber\");\n\n        this.#val = value;\n\n        this.#format = format;\n\n        const _value = toString(value, format.decimals);\n\n        defineProperties<FixedNumber>(this, { format: format.name, _value });\n\n        this.#tens = getTens(format.decimals);\n    }\n\n    /**\n     *  If true, negative values are permitted, otherwise only\n     *  positive values and zero are allowed.\n     */\n    get signed(): boolean { return this.#format.signed; }\n\n    /**\n     *  The number of bits available to store the value.\n     */\n    get width(): number { return this.#format.width; }\n\n    /**\n     *  The number of decimal places in the fixed-point arithment field.\n     */\n    get decimals(): number { return this.#format.decimals; }\n\n    /**\n     *  The value as an integer, based on the smallest unit the\n     *  [[decimals]] allow.\n     */\n    get value(): bigint { return this.#val; }\n\n    #checkFormat(other: FixedNumber): void {\n        assertArgument(this.format === other.format,\n            \"incompatible format; use fixedNumber.toFormat\", \"other\", other);\n    }\n\n    #checkValue(val: bigint, safeOp?: string): FixedNumber {\n/*\n        const width = BigInt(this.width);\n        if (this.signed) {\n            const limit = (BN_1 << (width - BN_1));\n            assert(safeOp == null || (val >= -limit  && val < limit), \"overflow\", \"NUMERIC_FAULT\", {\n                operation: <string>safeOp, fault: \"overflow\", value: val\n            });\n\n            if (val > BN_0) {\n                val = fromTwos(mask(val, width), width);\n            } else {\n                val = -fromTwos(mask(-val, width), width);\n            }\n\n        } else {\n            const masked = mask(val, width);\n            assert(safeOp == null || (val >= 0 && val === masked), \"overflow\", \"NUMERIC_FAULT\", {\n                operation: <string>safeOp, fault: \"overflow\", value: val\n            });\n            val = masked;\n        }\n*/\n        val = checkValue(val, this.#format, safeOp);\n        return new FixedNumber(_guard, val, this.#format);\n    }\n\n    #add(o: FixedNumber, safeOp?: string): FixedNumber {\n        this.#checkFormat(o);\n        return this.#checkValue(this.#val + o.#val, safeOp);\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% added\n     *  to %%other%%, ignoring overflow.\n     */\n    addUnsafe(other: FixedNumber): FixedNumber { return this.#add(other); }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% added\n     *  to %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    add(other: FixedNumber): FixedNumber { return this.#add(other, \"add\"); }\n\n    #sub(o: FixedNumber, safeOp?: string): FixedNumber {\n        this.#checkFormat(o);\n        return this.#checkValue(this.#val - o.#val, safeOp);\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n     *  from %%this%%, ignoring overflow.\n     */\n    subUnsafe(other: FixedNumber): FixedNumber { return this.#sub(other); }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%other%% subtracted\n     *  from %%this%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    sub(other: FixedNumber): FixedNumber { return this.#sub(other, \"sub\"); }\n\n    #mul(o: FixedNumber, safeOp?: string): FixedNumber {\n        this.#checkFormat(o);\n        return this.#checkValue((this.#val * o.#val) / this.#tens, safeOp);\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%, ignoring overflow and underflow (precision loss).\n     */\n    mulUnsafe(other: FixedNumber): FixedNumber { return this.#mul(other); }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs.\n     */\n    mul(other: FixedNumber): FixedNumber { return this.#mul(other, \"mul\"); }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% multiplied\n     *  by %%other%%. A [[NumericFaultError]] is thrown if overflow\n     *  occurs or if underflow (precision loss) occurs.\n     */\n    mulSignal(other: FixedNumber): FixedNumber {\n        this.#checkFormat(other);\n        const value = this.#val * other.#val;\n        assert((value % this.#tens) === BN_0, \"precision lost during signalling mul\", \"NUMERIC_FAULT\", {\n            operation: \"mulSignal\", fault: \"underflow\", value: this\n        });\n        return this.#checkValue(value / this.#tens, \"mulSignal\");\n    }\n\n    #div(o: FixedNumber, safeOp?: string): FixedNumber {\n        assert(o.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n            operation: \"div\", fault: \"divide-by-zero\", value: this\n        });\n        this.#checkFormat(o);\n        return this.#checkValue((this.#val * this.#tens) / o.#val, safeOp);\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%, ignoring underflow (precision loss). A\n     *  [[NumericFaultError]] is thrown if overflow occurs.\n     */\n    divUnsafe(other: FixedNumber): FixedNumber { return this.#div(other); }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%, ignoring underflow (precision loss). A\n     *  [[NumericFaultError]] is thrown if overflow occurs.\n     */\n    div(other: FixedNumber): FixedNumber { return this.#div(other, \"div\"); }\n\n\n    /**\n     *  Returns a new [[FixedNumber]] with the result of %%this%% divided\n     *  by %%other%%. A [[NumericFaultError]] is thrown if underflow\n     *  (precision loss) occurs.\n     */\n    divSignal(other: FixedNumber): FixedNumber {\n        assert(other.#val !== BN_0, \"division by zero\", \"NUMERIC_FAULT\", {\n            operation: \"div\", fault: \"divide-by-zero\", value: this\n        });\n        this.#checkFormat(other);\n        const value = (this.#val * this.#tens);\n        assert((value % other.#val) === BN_0, \"precision lost during signalling div\", \"NUMERIC_FAULT\", {\n            operation: \"divSignal\", fault: \"underflow\", value: this\n        });\n        return this.#checkValue(value / other.#val, \"divSignal\");\n    }\n\n    /**\n     *  Returns a comparison result between %%this%% and %%other%%.\n     *\n     *  This is suitable for use in sorting, where ``-1`` implies %%this%%\n     *  is smaller, ``1`` implies %%this%% is larger and ``0`` implies\n     *  both are equal.\n     */\n     cmp(other: FixedNumber): number {\n         let a = this.value, b = other.value;\n\n         // Coerce a and b to the same magnitude\n         const delta = this.decimals - other.decimals;\n         if (delta > 0) {\n             b *= getTens(delta);\n         } else if (delta < 0) {\n             a *= getTens(-delta);\n         }\n\n         // Comnpare\n         if (a < b) { return -1; }\n         if (a > b) { return 1; }\n         return 0;\n     }\n\n    /**\n     *  Returns true if %%other%% is equal to %%this%%.\n     */\n     eq(other: FixedNumber): boolean { return this.cmp(other) === 0; }\n\n    /**\n     *  Returns true if %%other%% is less than to %%this%%.\n     */\n     lt(other: FixedNumber): boolean { return this.cmp(other) < 0; }\n\n    /**\n     *  Returns true if %%other%% is less than or equal to %%this%%.\n     */\n     lte(other: FixedNumber): boolean { return this.cmp(other) <= 0; }\n\n    /**\n     *  Returns true if %%other%% is greater than to %%this%%.\n     */\n     gt(other: FixedNumber): boolean { return this.cmp(other) > 0; }\n\n    /**\n     *  Returns true if %%other%% is greater than or equal to %%this%%.\n     */\n     gte(other: FixedNumber): boolean { return this.cmp(other) >= 0; }\n\n    /**\n     *  Returns a new [[FixedNumber]] which is the largest **integer**\n     *  that is less than or equal to %%this%%.\n     *\n     *  The decimal component of the result will always be ``0``.\n     */\n    floor(): FixedNumber {\n        let val = this.#val;\n        if (this.#val < BN_0) { val -= this.#tens - BN_1; }\n        val = (this.#val / this.#tens) * this.#tens;\n        return this.#checkValue(val, \"floor\");\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] which is the smallest **integer**\n     *  that is greater than or equal to %%this%%.\n     *\n     *  The decimal component of the result will always be ``0``.\n     */\n    ceiling(): FixedNumber {\n        let val = this.#val;\n        if (this.#val > BN_0) { val += this.#tens - BN_1; }\n        val = (this.#val / this.#tens) * this.#tens;\n        return this.#checkValue(val, \"ceiling\");\n    }\n\n    /**\n     *  Returns a new [[FixedNumber]] with the decimal component\n     *  rounded up on ties at %%decimals%% places.\n     */\n    round(decimals?: number): FixedNumber {\n        if (decimals == null) { decimals = 0; }\n\n        // Not enough precision to not already be rounded\n        if (decimals >= this.decimals) { return this; }\n\n        const delta = this.decimals - decimals;\n        const bump = BN_5 * getTens(delta - 1);\n\n        let value = this.value + bump;\n        const tens = getTens(delta);\n        value = (value / tens) * tens;\n\n        checkValue(value, this.#format, \"round\");\n\n        return new FixedNumber(_guard, value, this.#format);\n    }\n\n    /**\n     *  Returns true if %%this%% is equal to ``0``.\n     */\n    isZero(): boolean { return (this.#val === BN_0); }\n\n    /**\n     *  Returns true if %%this%% is less than ``0``.\n     */\n    isNegative(): boolean { return (this.#val < BN_0); }\n\n    /**\n     *  Returns the string representation of %%this%%.\n     */\n    toString(): string { return this._value; }\n\n    /**\n     *  Returns a float approximation.\n     *\n     *  Due to IEEE 754 precission (or lack thereof), this function\n     *  can only return an approximation and most values will contain\n     *  rounding errors.\n     */\n    toUnsafeFloat(): number { return parseFloat(this.toString()); }\n\n    /**\n     *  Return a new [[FixedNumber]] with the same value but has had\n     *  its field set to %%format%%.\n     *\n     *  This will throw if the value cannot fit into %%format%%.\n     */\n    toFormat(format: FixedFormat): FixedNumber {\n        return FixedNumber.fromString(this.toString(), format);\n    }\n\n    /**\n     *  Creates a new [[FixedNumber]] for %%value%% divided by\n     *  %%decimal%% places with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% (once adjusted\n     *  for %%decimals%%) cannot fit in %%format%%, either due to overflow\n     *  or underflow (precision loss).\n     */\n    static fromValue(_value: BigNumberish, _decimals?: Numeric, _format?: FixedFormat): FixedNumber {\n        const decimals = (_decimals == null) ? 0: getNumber(_decimals);\n        const format = getFormat(_format);\n\n        let value = getBigInt(_value, \"value\");\n        const delta = decimals - format.decimals;\n        if (delta > 0) {\n            const tens = getTens(delta);\n            assert((value % tens) === BN_0, \"value loses precision for format\", \"NUMERIC_FAULT\", {\n                operation: \"fromValue\", fault: \"underflow\", value: _value\n            });\n            value /= tens;\n        } else if (delta < 0) {\n            value *= getTens(-delta);\n        }\n\n        checkValue(value, format, \"fromValue\");\n\n        return new FixedNumber(_guard, value, format);\n    }\n\n    /**\n     *  Creates a new [[FixedNumber]] for %%value%% with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n     *  in %%format%%, either due to overflow or underflow (precision loss).\n     */\n    static fromString(_value: string, _format?: FixedFormat): FixedNumber {\n        const match = _value.match(/^(-?)([0-9]*)\\.?([0-9]*)$/);\n        assertArgument(match && (match[2].length + match[3].length) > 0, \"invalid FixedNumber string value\", \"value\", _value);\n\n        const format = getFormat(_format);\n\n        let whole = (match[2] || \"0\"), decimal = (match[3] || \"\");\n\n        // Pad out the decimals\n        while (decimal.length < format.decimals) { decimal += Zeros; }\n\n        // Check precision is safe\n        assert(decimal.substring(format.decimals).match(/^0*$/), \"too many decimals for format\", \"NUMERIC_FAULT\", {\n            operation: \"fromString\", fault: \"underflow\", value: _value\n        });\n\n        // Remove extra padding\n        decimal = decimal.substring(0, format.decimals);\n\n        const value = BigInt(match[1] + whole + decimal)\n\n        checkValue(value, format, \"fromString\");\n\n        return new FixedNumber(_guard, value, format);\n    }\n\n    /**\n     *  Creates a new [[FixedNumber]] with the big-endian representation\n     *  %%value%% with %%format%%.\n     *\n     *  This will throw a [[NumericFaultError]] if %%value%% cannot fit\n     *  in %%format%% due to overflow.\n     */\n    static fromBytes(_value: BytesLike, _format?: FixedFormat): FixedNumber {\n        let value = toBigInt(getBytes(_value, \"value\"));\n        const format = getFormat(_format);\n\n        if (format.signed) { value = fromTwos(value, format.width); }\n\n        checkValue(value, format, \"fromBytes\");\n\n        return new FixedNumber(_guard, value, format);\n    }\n}\n\n//const f1 = FixedNumber.fromString(\"12.56\", \"fixed16x2\");\n//const f2 = FixedNumber.fromString(\"0.3\", \"fixed16x2\");\n//console.log(f1.divSignal(f2));\n//const BUMP = FixedNumber.from(\"0.5\");\n"], "mappings": "AAAA;;;;;;;;;;AAUA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,MAAM,EAAEC,cAAc,EAAEC,aAAa,QAAQ,aAAa;AACnE,SACIC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,QAC3C,YAAY;AACnB,SAASC,gBAAgB,QAAQ,iBAAiB;AAIlD,MAAMC,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AACtB,MAAME,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMG,IAAI,GAAGH,MAAM,CAAC,CAAC,CAAC;AAEtB,MAAMI,MAAM,GAAG,EAAG;AAGlB;AACA,IAAIC,KAAK,GAAG,MAAM;AAClB,OAAOA,KAAK,CAACC,MAAM,GAAG,EAAE,EAAE;EAAED,KAAK,IAAIA,KAAK;;AAE1C;AACA,SAASE,OAAOA,CAACC,QAAgB;EAC7B,IAAIC,MAAM,GAAGJ,KAAK;EAClB,OAAOI,MAAM,CAACH,MAAM,GAAGE,QAAQ,EAAE;IAAEC,MAAM,IAAIA,MAAM;;EACnD,OAAOT,MAAM,CAAC,GAAG,GAAGS,MAAM,CAACC,SAAS,CAAC,CAAC,EAAEF,QAAQ,CAAC,CAAC;AACtD;AAkDA,SAASG,UAAUA,CAACC,GAAW,EAAEC,MAAoB,EAAEC,MAAe;EAClE,MAAMC,KAAK,GAAGf,MAAM,CAACa,MAAM,CAACE,KAAK,CAAC;EAClC,IAAIF,MAAM,CAACG,MAAM,EAAE;IACf,MAAMC,KAAK,GAAIf,IAAI,IAAKa,KAAK,GAAGb,IAAM;IACtCZ,MAAM,CAACwB,MAAM,IAAI,IAAI,IAAKF,GAAG,IAAI,CAACK,KAAK,IAAKL,GAAG,GAAGK,KAAM,EAAE,UAAU,EAAE,eAAe,EAAE;MACnFC,SAAS,EAAUJ,MAAM;MAAEK,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAER;KACxD,CAAC;IAEF,IAAIA,GAAG,GAAGX,IAAI,EAAE;MACZW,GAAG,GAAGjB,QAAQ,CAACC,IAAI,CAACgB,GAAG,EAAEG,KAAK,CAAC,EAAEA,KAAK,CAAC;KAC1C,MAAM;MACHH,GAAG,GAAG,CAACjB,QAAQ,CAACC,IAAI,CAAC,CAACgB,GAAG,EAAEG,KAAK,CAAC,EAAEA,KAAK,CAAC;;GAGhD,MAAM;IACH,MAAME,KAAK,GAAIf,IAAI,IAAIa,KAAM;IAC7BzB,MAAM,CAACwB,MAAM,IAAI,IAAI,IAAKF,GAAG,IAAI,CAAC,IAAIA,GAAG,GAAGK,KAAM,EAAE,UAAU,EAAE,eAAe,EAAE;MAC7EC,SAAS,EAAUJ,MAAM;MAAEK,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAER;KACxD,CAAC;IACFA,GAAG,GAAI,CAAEA,GAAG,GAAGK,KAAK,GAAIA,KAAK,IAAIA,KAAK,GAAKA,KAAK,GAAGf,IAAK;;EAG5D,OAAOU,GAAG;AACd;AAIA,SAASS,SAASA,CAACD,KAAmB;EAClC,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;IAAEA,KAAK,GAAG,YAAYA,KAAK,EAAE;;EAE7D,IAAIJ,MAAM,GAAG,IAAI;EACjB,IAAID,KAAK,GAAG,GAAG;EACf,IAAIP,QAAQ,GAAG,EAAE;EAEjB,IAAI,OAAOY,KAAM,KAAK,QAAQ,EAAE;IAC5B;IACA,IAAIA,KAAK,KAAK,OAAO,EAAE;MACnB;IAAA,CACH,MAAM,IAAIA,KAAK,KAAK,QAAQ,EAAE;MAC3BJ,MAAM,GAAG,KAAK;KACjB,MAAM;MACH,MAAMM,KAAK,GAAGF,KAAK,CAACE,KAAK,CAAC,8BAA8B,CAAC;MACzD/B,cAAc,CAAC+B,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAEF,KAAK,CAAC;MAC9DJ,MAAM,GAAIM,KAAK,CAAC,CAAC,CAAC,KAAK,GAAI;MAC3BP,KAAK,GAAGQ,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1Bd,QAAQ,GAAGe,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;;GAEpC,MAAM,IAAIF,KAAK,EAAE;IACd;IACA,MAAMI,CAAC,GAAQJ,KAAK;IACpB,MAAMK,KAAK,GAAGA,CAACC,GAAW,EAAEC,IAAY,EAAEC,YAAiB,KAAS;MAChE,IAAIJ,CAAC,CAACE,GAAG,CAAC,IAAI,IAAI,EAAE;QAAE,OAAOE,YAAY;;MACzCrC,cAAc,CAAC,OAAOiC,CAAC,CAACE,GAAG,CAAE,KAAKC,IAAI,EAClC,wBAAwB,GAAGD,GAAG,GAAG,OAAO,GAAGC,IAAI,GAAE,GAAG,EAAE,SAAS,GAAGD,GAAG,EAAEF,CAAC,CAACE,GAAG,CAAC,CAAC;MAClF,OAAOF,CAAC,CAACE,GAAG,CAAC;IACjB,CAAC;IACDV,MAAM,GAAGS,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAET,MAAM,CAAC;IAC3CD,KAAK,GAAGU,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAEV,KAAK,CAAC;IACvCP,QAAQ,GAAGiB,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAEjB,QAAQ,CAAC;;EAGpDjB,cAAc,CAAEwB,KAAK,GAAG,CAAC,KAAM,CAAC,EAAE,8CAA8C,EAAE,cAAc,EAAEA,KAAK,CAAC;EACxGxB,cAAc,CAACiB,QAAQ,IAAI,EAAE,EAAE,0CAA0C,EAAE,iBAAiB,EAAEA,QAAQ,CAAC;EAEvG,MAAMqB,IAAI,GAAG,CAACb,MAAM,GAAG,EAAE,GAAE,GAAG,IAAI,OAAO,GAAGc,MAAM,CAACf,KAAK,CAAC,GAAG,GAAG,GAAGe,MAAM,CAACtB,QAAQ,CAAC;EAElF,OAAO;IAAEQ,MAAM;IAAED,KAAK;IAAEP,QAAQ;IAAEqB;EAAI,CAAE;AAC5C;AAEA,SAASE,QAAQA,CAACnB,GAAW,EAAEJ,QAAgB;EAC3C,IAAIwB,QAAQ,GAAG,EAAE;EACjB,IAAIpB,GAAG,GAAGX,IAAI,EAAE;IACZ+B,QAAQ,GAAG,GAAG;IACdpB,GAAG,IAAIb,KAAK;;EAGhB,IAAIkC,GAAG,GAAGrB,GAAG,CAACmB,QAAQ,EAAE;EAExB;EACA,IAAIvB,QAAQ,KAAK,CAAC,EAAE;IAAE,OAAQwB,QAAQ,GAAGC,GAAG;;EAE5C;EACA,OAAOA,GAAG,CAAC3B,MAAM,IAAIE,QAAQ,EAAE;IAAEyB,GAAG,GAAG5B,KAAK,GAAG4B,GAAG;;EAElD;EACA,MAAMC,KAAK,GAAGD,GAAG,CAAC3B,MAAM,GAAGE,QAAQ;EACnCyB,GAAG,GAAGA,GAAG,CAACvB,SAAS,CAAC,CAAC,EAAEwB,KAAK,CAAC,GAAG,GAAG,GAAGD,GAAG,CAACvB,SAAS,CAACwB,KAAK,CAAC;EAE1D;EACA,OAAOD,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACrCA,GAAG,GAAGA,GAAG,CAACvB,SAAS,CAAC,CAAC,CAAC;;EAG1B;EACA,OAAOuB,GAAG,CAACA,GAAG,CAAC3B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI2B,GAAG,CAACA,GAAG,CAAC3B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;IAC/D2B,GAAG,GAAGA,GAAG,CAACvB,SAAS,CAAC,CAAC,EAAEuB,GAAG,CAAC3B,MAAM,GAAG,CAAC,CAAC;;EAG1C,OAAQ0B,QAAQ,GAAGC,GAAG;AAC1B;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAM,MAAOE,WAAW;EAEpB;;;EAGStB,MAAM;EAEN,CAAAA,MAAO;EAEhB;EACA,CAAAD,GAAI;EAEJ;EACS,CAAAwB,IAAK;EAEd;;;;;EAKSC,MAAM;EAEf;EACA;EACA;EAEA;;;EAGAC,YAAYC,KAAU,EAAEnB,KAAa,EAAEP,MAAW;IAC9CrB,aAAa,CAAC+C,KAAK,EAAEnC,MAAM,EAAE,aAAa,CAAC;IAE3C,IAAI,CAAC,CAAAQ,GAAI,GAAGQ,KAAK;IAEjB,IAAI,CAAC,CAAAP,MAAO,GAAGA,MAAM;IAErB,MAAMwB,MAAM,GAAGN,QAAQ,CAACX,KAAK,EAAEP,MAAM,CAACL,QAAQ,CAAC;IAE/CV,gBAAgB,CAAc,IAAI,EAAE;MAAEe,MAAM,EAAEA,MAAM,CAACgB,IAAI;MAAEQ;IAAM,CAAE,CAAC;IAEpE,IAAI,CAAC,CAAAD,IAAK,GAAG7B,OAAO,CAACM,MAAM,CAACL,QAAQ,CAAC;EACzC;EAEA;;;;EAIA,IAAIQ,MAAMA,CAAA;IAAc,OAAO,IAAI,CAAC,CAAAH,MAAO,CAACG,MAAM;EAAE;EAEpD;;;EAGA,IAAID,KAAKA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAF,MAAO,CAACE,KAAK;EAAE;EAEjD;;;EAGA,IAAIP,QAAQA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAK,MAAO,CAACL,QAAQ;EAAE;EAEvD;;;;EAIA,IAAIY,KAAKA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAR,GAAI;EAAE;EAExC,CAAA4B,WAAYC,CAACC,KAAkB;IAC3BnD,cAAc,CAAC,IAAI,CAACsB,MAAM,KAAK6B,KAAK,CAAC7B,MAAM,EACvC,+CAA+C,EAAE,OAAO,EAAE6B,KAAK,CAAC;EACxE;EAEA,CAAA/B,UAAWgC,CAAC/B,GAAW,EAAEE,MAAe;IAC5C;;;;;;;;;;;;;;;;;;;;;;IAsBQF,GAAG,GAAGD,UAAU,CAACC,GAAG,EAAE,IAAI,CAAC,CAAAC,MAAO,EAAEC,MAAM,CAAC;IAC3C,OAAO,IAAIqB,WAAW,CAAC/B,MAAM,EAAEQ,GAAG,EAAE,IAAI,CAAC,CAAAC,MAAO,CAAC;EACrD;EAEA,CAAA+B,GAAIC,CAACC,CAAc,EAAEhC,MAAe;IAChC,IAAI,CAAC,CAAA0B,WAAY,CAACM,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC,CAAAnC,UAAW,CAAC,IAAI,CAAC,CAAAC,GAAI,GAAGkC,CAAC,CAAC,CAAAlC,GAAI,EAAEE,MAAM,CAAC;EACvD;EAEA;;;;EAIAiC,SAASA,CAACL,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAE,GAAI,CAACF,KAAK,CAAC;EAAE;EAEtE;;;;;EAKAE,GAAGA,CAACF,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAE,GAAI,CAACF,KAAK,EAAE,KAAK,CAAC;EAAE;EAEvE,CAAAM,GAAIC,CAACH,CAAc,EAAEhC,MAAe;IAChC,IAAI,CAAC,CAAA0B,WAAY,CAACM,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC,CAAAnC,UAAW,CAAC,IAAI,CAAC,CAAAC,GAAI,GAAGkC,CAAC,CAAC,CAAAlC,GAAI,EAAEE,MAAM,CAAC;EACvD;EAEA;;;;EAIAoC,SAASA,CAACR,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAM,GAAI,CAACN,KAAK,CAAC;EAAE;EAEtE;;;;;EAKAM,GAAGA,CAACN,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAM,GAAI,CAACN,KAAK,EAAE,KAAK,CAAC;EAAE;EAEvE,CAAAS,GAAIC,CAACN,CAAc,EAAEhC,MAAe;IAChC,IAAI,CAAC,CAAA0B,WAAY,CAACM,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC,CAAAnC,UAAW,CAAE,IAAI,CAAC,CAAAC,GAAI,GAAGkC,CAAC,CAAC,CAAAlC,GAAI,GAAI,IAAI,CAAC,CAAAwB,IAAK,EAAEtB,MAAM,CAAC;EACtE;EAEA;;;;EAIAuC,SAASA,CAACX,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAS,GAAI,CAACT,KAAK,CAAC;EAAE;EAEtE;;;;;EAKAS,GAAGA,CAACT,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAS,GAAI,CAACT,KAAK,EAAE,KAAK,CAAC;EAAE;EAEvE;;;;;EAKAY,SAASA,CAACZ,KAAkB;IACxB,IAAI,CAAC,CAAAF,WAAY,CAACE,KAAK,CAAC;IACxB,MAAMtB,KAAK,GAAG,IAAI,CAAC,CAAAR,GAAI,GAAG8B,KAAK,CAAC,CAAA9B,GAAI;IACpCtB,MAAM,CAAE8B,KAAK,GAAG,IAAI,CAAC,CAAAgB,IAAK,KAAMnC,IAAI,EAAE,sCAAsC,EAAE,eAAe,EAAE;MAC3FiB,SAAS,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;KACtD,CAAC;IACF,OAAO,IAAI,CAAC,CAAAT,UAAW,CAACS,KAAK,GAAG,IAAI,CAAC,CAAAgB,IAAK,EAAE,WAAW,CAAC;EAC5D;EAEA,CAAAmB,GAAIC,CAACV,CAAc,EAAEhC,MAAe;IAChCxB,MAAM,CAACwD,CAAC,CAAC,CAAAlC,GAAI,KAAKX,IAAI,EAAE,kBAAkB,EAAE,eAAe,EAAE;MACzDiB,SAAS,EAAE,KAAK;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;KACrD,CAAC;IACF,IAAI,CAAC,CAAAoB,WAAY,CAACM,CAAC,CAAC;IACpB,OAAO,IAAI,CAAC,CAAAnC,UAAW,CAAE,IAAI,CAAC,CAAAC,GAAI,GAAG,IAAI,CAAC,CAAAwB,IAAK,GAAIU,CAAC,CAAC,CAAAlC,GAAI,EAAEE,MAAM,CAAC;EACtE;EAEA;;;;;EAKA2C,SAASA,CAACf,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAa,GAAI,CAACb,KAAK,CAAC;EAAE;EAEtE;;;;;EAKAa,GAAGA,CAACb,KAAkB;IAAiB,OAAO,IAAI,CAAC,CAAAa,GAAI,CAACb,KAAK,EAAE,KAAK,CAAC;EAAE;EAGvE;;;;;EAKAgB,SAASA,CAAChB,KAAkB;IACxBpD,MAAM,CAACoD,KAAK,CAAC,CAAA9B,GAAI,KAAKX,IAAI,EAAE,kBAAkB,EAAE,eAAe,EAAE;MAC7DiB,SAAS,EAAE,KAAK;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,KAAK,EAAE;KACrD,CAAC;IACF,IAAI,CAAC,CAAAoB,WAAY,CAACE,KAAK,CAAC;IACxB,MAAMtB,KAAK,GAAI,IAAI,CAAC,CAAAR,GAAI,GAAG,IAAI,CAAC,CAAAwB,IAAM;IACtC9C,MAAM,CAAE8B,KAAK,GAAGsB,KAAK,CAAC,CAAA9B,GAAI,KAAMX,IAAI,EAAE,sCAAsC,EAAE,eAAe,EAAE;MAC3FiB,SAAS,EAAE,WAAW;MAAEC,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;KACtD,CAAC;IACF,OAAO,IAAI,CAAC,CAAAT,UAAW,CAACS,KAAK,GAAGsB,KAAK,CAAC,CAAA9B,GAAI,EAAE,WAAW,CAAC;EAC5D;EAEA;;;;;;;EAOC+C,GAAGA,CAACjB,KAAkB;IAClB,IAAIkB,CAAC,GAAG,IAAI,CAACxC,KAAK;MAAEyC,CAAC,GAAGnB,KAAK,CAACtB,KAAK;IAEnC;IACA,MAAM0C,KAAK,GAAG,IAAI,CAACtD,QAAQ,GAAGkC,KAAK,CAAClC,QAAQ;IAC5C,IAAIsD,KAAK,GAAG,CAAC,EAAE;MACXD,CAAC,IAAItD,OAAO,CAACuD,KAAK,CAAC;KACtB,MAAM,IAAIA,KAAK,GAAG,CAAC,EAAE;MAClBF,CAAC,IAAIrD,OAAO,CAAC,CAACuD,KAAK,CAAC;;IAGxB;IACA,IAAIF,CAAC,GAAGC,CAAC,EAAE;MAAE,OAAO,CAAC,CAAC;;IACtB,IAAID,CAAC,GAAGC,CAAC,EAAE;MAAE,OAAO,CAAC;;IACrB,OAAO,CAAC;EACZ;EAED;;;EAGCE,EAAEA,CAACrB,KAAkB;IAAa,OAAO,IAAI,CAACiB,GAAG,CAACjB,KAAK,CAAC,KAAK,CAAC;EAAE;EAEjE;;;EAGCsB,EAAEA,CAACtB,KAAkB;IAAa,OAAO,IAAI,CAACiB,GAAG,CAACjB,KAAK,CAAC,GAAG,CAAC;EAAE;EAE/D;;;EAGCuB,GAAGA,CAACvB,KAAkB;IAAa,OAAO,IAAI,CAACiB,GAAG,CAACjB,KAAK,CAAC,IAAI,CAAC;EAAE;EAEjE;;;EAGCwB,EAAEA,CAACxB,KAAkB;IAAa,OAAO,IAAI,CAACiB,GAAG,CAACjB,KAAK,CAAC,GAAG,CAAC;EAAE;EAE/D;;;EAGCyB,GAAGA,CAACzB,KAAkB;IAAa,OAAO,IAAI,CAACiB,GAAG,CAACjB,KAAK,CAAC,IAAI,CAAC;EAAE;EAEjE;;;;;;EAMA0B,KAAKA,CAAA;IACD,IAAIxD,GAAG,GAAG,IAAI,CAAC,CAAAA,GAAI;IACnB,IAAI,IAAI,CAAC,CAAAA,GAAI,GAAGX,IAAI,EAAE;MAAEW,GAAG,IAAI,IAAI,CAAC,CAAAwB,IAAK,GAAGlC,IAAI;;IAChDU,GAAG,GAAI,IAAI,CAAC,CAAAA,GAAI,GAAG,IAAI,CAAC,CAAAwB,IAAK,GAAI,IAAI,CAAC,CAAAA,IAAK;IAC3C,OAAO,IAAI,CAAC,CAAAzB,UAAW,CAACC,GAAG,EAAE,OAAO,CAAC;EACzC;EAEA;;;;;;EAMAyD,OAAOA,CAAA;IACH,IAAIzD,GAAG,GAAG,IAAI,CAAC,CAAAA,GAAI;IACnB,IAAI,IAAI,CAAC,CAAAA,GAAI,GAAGX,IAAI,EAAE;MAAEW,GAAG,IAAI,IAAI,CAAC,CAAAwB,IAAK,GAAGlC,IAAI;;IAChDU,GAAG,GAAI,IAAI,CAAC,CAAAA,GAAI,GAAG,IAAI,CAAC,CAAAwB,IAAK,GAAI,IAAI,CAAC,CAAAA,IAAK;IAC3C,OAAO,IAAI,CAAC,CAAAzB,UAAW,CAACC,GAAG,EAAE,SAAS,CAAC;EAC3C;EAEA;;;;EAIA0D,KAAKA,CAAC9D,QAAiB;IACnB,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,CAAC;;IAEpC;IACA,IAAIA,QAAQ,IAAI,IAAI,CAACA,QAAQ,EAAE;MAAE,OAAO,IAAI;;IAE5C,MAAMsD,KAAK,GAAG,IAAI,CAACtD,QAAQ,GAAGA,QAAQ;IACtC,MAAM+D,IAAI,GAAGpE,IAAI,GAAGI,OAAO,CAACuD,KAAK,GAAG,CAAC,CAAC;IAEtC,IAAI1C,KAAK,GAAG,IAAI,CAACA,KAAK,GAAGmD,IAAI;IAC7B,MAAMnC,IAAI,GAAG7B,OAAO,CAACuD,KAAK,CAAC;IAC3B1C,KAAK,GAAIA,KAAK,GAAGgB,IAAI,GAAIA,IAAI;IAE7BzB,UAAU,CAACS,KAAK,EAAE,IAAI,CAAC,CAAAP,MAAO,EAAE,OAAO,CAAC;IAExC,OAAO,IAAIsB,WAAW,CAAC/B,MAAM,EAAEgB,KAAK,EAAE,IAAI,CAAC,CAAAP,MAAO,CAAC;EACvD;EAEA;;;EAGA2D,MAAMA,CAAA;IAAc,OAAQ,IAAI,CAAC,CAAA5D,GAAI,KAAKX,IAAI;EAAG;EAEjD;;;EAGAwE,UAAUA,CAAA;IAAc,OAAQ,IAAI,CAAC,CAAA7D,GAAI,GAAGX,IAAI;EAAG;EAEnD;;;EAGA8B,QAAQA,CAAA;IAAa,OAAO,IAAI,CAACM,MAAM;EAAE;EAEzC;;;;;;;EAOAqC,aAAaA,CAAA;IAAa,OAAOC,UAAU,CAAC,IAAI,CAAC5C,QAAQ,EAAE,CAAC;EAAE;EAE9D;;;;;;EAMA6C,QAAQA,CAAC/D,MAAmB;IACxB,OAAOsB,WAAW,CAAC0C,UAAU,CAAC,IAAI,CAAC9C,QAAQ,EAAE,EAAElB,MAAM,CAAC;EAC1D;EAEA;;;;;;;;EAQA,OAAOiE,SAASA,CAACzC,MAAoB,EAAE0C,SAAmB,EAAEC,OAAqB;IAC7E,MAAMxE,QAAQ,GAAIuE,SAAS,IAAI,IAAI,GAAI,CAAC,GAAErF,SAAS,CAACqF,SAAS,CAAC;IAC9D,MAAMlE,MAAM,GAAGQ,SAAS,CAAC2D,OAAO,CAAC;IAEjC,IAAI5D,KAAK,GAAG3B,SAAS,CAAC4C,MAAM,EAAE,OAAO,CAAC;IACtC,MAAMyB,KAAK,GAAGtD,QAAQ,GAAGK,MAAM,CAACL,QAAQ;IACxC,IAAIsD,KAAK,GAAG,CAAC,EAAE;MACX,MAAM1B,IAAI,GAAG7B,OAAO,CAACuD,KAAK,CAAC;MAC3BxE,MAAM,CAAE8B,KAAK,GAAGgB,IAAI,KAAMnC,IAAI,EAAE,kCAAkC,EAAE,eAAe,EAAE;QACjFiB,SAAS,EAAE,WAAW;QAAEC,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAEiB;OACtD,CAAC;MACFjB,KAAK,IAAIgB,IAAI;KAChB,MAAM,IAAI0B,KAAK,GAAG,CAAC,EAAE;MAClB1C,KAAK,IAAIb,OAAO,CAAC,CAACuD,KAAK,CAAC;;IAG5BnD,UAAU,CAACS,KAAK,EAAEP,MAAM,EAAE,WAAW,CAAC;IAEtC,OAAO,IAAIsB,WAAW,CAAC/B,MAAM,EAAEgB,KAAK,EAAEP,MAAM,CAAC;EACjD;EAEA;;;;;;EAMA,OAAOgE,UAAUA,CAACxC,MAAc,EAAE2C,OAAqB;IACnD,MAAM1D,KAAK,GAAGe,MAAM,CAACf,KAAK,CAAC,2BAA2B,CAAC;IACvD/B,cAAc,CAAC+B,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,CAAChB,MAAM,GAAGgB,KAAK,CAAC,CAAC,CAAC,CAAChB,MAAM,GAAI,CAAC,EAAE,kCAAkC,EAAE,OAAO,EAAE+B,MAAM,CAAC;IAErH,MAAMxB,MAAM,GAAGQ,SAAS,CAAC2D,OAAO,CAAC;IAEjC,IAAIC,KAAK,GAAI3D,KAAK,CAAC,CAAC,CAAC,IAAI,GAAI;MAAE4D,OAAO,GAAI5D,KAAK,CAAC,CAAC,CAAC,IAAI,EAAG;IAEzD;IACA,OAAO4D,OAAO,CAAC5E,MAAM,GAAGO,MAAM,CAACL,QAAQ,EAAE;MAAE0E,OAAO,IAAI7E,KAAK;;IAE3D;IACAf,MAAM,CAAC4F,OAAO,CAACxE,SAAS,CAACG,MAAM,CAACL,QAAQ,CAAC,CAACc,KAAK,CAAC,MAAM,CAAC,EAAE,8BAA8B,EAAE,eAAe,EAAE;MACtGJ,SAAS,EAAE,YAAY;MAAEC,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAEiB;KACvD,CAAC;IAEF;IACA6C,OAAO,GAAGA,OAAO,CAACxE,SAAS,CAAC,CAAC,EAAEG,MAAM,CAACL,QAAQ,CAAC;IAE/C,MAAMY,KAAK,GAAGpB,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC,GAAG2D,KAAK,GAAGC,OAAO,CAAC;IAEhDvE,UAAU,CAACS,KAAK,EAAEP,MAAM,EAAE,YAAY,CAAC;IAEvC,OAAO,IAAIsB,WAAW,CAAC/B,MAAM,EAAEgB,KAAK,EAAEP,MAAM,CAAC;EACjD;EAEA;;;;;;;EAOA,OAAOsE,SAASA,CAAC9C,MAAiB,EAAE2C,OAAqB;IACrD,IAAI5D,KAAK,GAAGvB,QAAQ,CAACR,QAAQ,CAACgD,MAAM,EAAE,OAAO,CAAC,CAAC;IAC/C,MAAMxB,MAAM,GAAGQ,SAAS,CAAC2D,OAAO,CAAC;IAEjC,IAAInE,MAAM,CAACG,MAAM,EAAE;MAAEI,KAAK,GAAGzB,QAAQ,CAACyB,KAAK,EAAEP,MAAM,CAACE,KAAK,CAAC;;IAE1DJ,UAAU,CAACS,KAAK,EAAEP,MAAM,EAAE,WAAW,CAAC;IAEtC,OAAO,IAAIsB,WAAW,CAAC/B,MAAM,EAAEgB,KAAK,EAAEP,MAAM,CAAC;EACjD;;AAGJ;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}