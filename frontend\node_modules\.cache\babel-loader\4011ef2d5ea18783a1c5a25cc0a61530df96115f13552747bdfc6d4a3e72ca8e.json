{"ast": null, "code": "import { isPrimaryPointer } from './utils/is-primary-pointer.mjs';\nfunction extractEventInfo(event, pointType = \"page\") {\n  return {\n    point: {\n      x: event[pointType + \"X\"],\n      y: event[pointType + \"Y\"]\n    }\n  };\n}\nconst addPointerInfo = handler => {\n  return event => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\nexport { addPointerInfo, extractEventInfo };", "map": {"version": 3, "names": ["isPrimaryPointer", "extractEventInfo", "event", "pointType", "point", "x", "y", "addPointerInfo", "handler"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/events/event-info.mjs"], "sourcesContent": ["import { isPrimaryPointer } from './utils/is-primary-pointer.mjs';\n\nfunction extractEventInfo(event, pointType = \"page\") {\n    return {\n        point: {\n            x: event[pointType + \"X\"],\n            y: event[pointType + \"Y\"],\n        },\n    };\n}\nconst addPointerInfo = (handler) => {\n    return (event) => isPrimaryPointer(event) && handler(event, extractEventInfo(event));\n};\n\nexport { addPointerInfo, extractEventInfo };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,gCAAgC;AAEjE,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,GAAG,MAAM,EAAE;EACjD,OAAO;IACHC,KAAK,EAAE;MACHC,CAAC,EAAEH,KAAK,CAACC,SAAS,GAAG,GAAG,CAAC;MACzBG,CAAC,EAAEJ,KAAK,CAACC,SAAS,GAAG,GAAG;IAC5B;EACJ,CAAC;AACL;AACA,MAAMI,cAAc,GAAIC,OAAO,IAAK;EAChC,OAAQN,KAAK,IAAKF,gBAAgB,CAACE,KAAK,CAAC,IAAIM,OAAO,CAACN,KAAK,EAAED,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACxF,CAAC;AAED,SAASK,cAAc,EAAEN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}