{"ast": null, "code": "import { useContext, useRef, useInsertionEffect } from 'react';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { animateValue } from '../animation/animators/js/index.mjs';\nimport { millisecondsToSeconds } from '../utils/time-conversion.mjs';\nimport { frameData } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that, when `set`, will use a spring animation to animate to its new state.\n *\n * It can either work as a stand-alone `MotionValue` by initialising it with a value, or as a subscriber\n * to another `MotionValue`.\n *\n * @remarks\n *\n * ```jsx\n * const x = useSpring(0, { stiffness: 300 })\n * const y = useSpring(x, { damping: 10 })\n * ```\n *\n * @param inputValue - `MotionValue` or number. If provided a `MotionValue`, when the input `MotionValue` changes, the created `MotionValue` will spring towards that value.\n * @param springConfig - Configuration options for the spring.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction useSpring(source, config = {}) {\n  const {\n    isStatic\n  } = useContext(MotionConfigContext);\n  const activeSpringAnimation = useRef(null);\n  const value = useMotionValue(isMotionValue(source) ? source.get() : source);\n  const stopAnimation = () => {\n    if (activeSpringAnimation.current) {\n      activeSpringAnimation.current.stop();\n    }\n  };\n  useInsertionEffect(() => {\n    return value.attach((v, set) => {\n      /**\n       * A more hollistic approach to this might be to use isStatic to fix VisualElement animations\n       * at that level, but this will work for now\n       */\n      if (isStatic) return set(v);\n      stopAnimation();\n      activeSpringAnimation.current = animateValue({\n        keyframes: [value.get(), v],\n        velocity: value.getVelocity(),\n        type: \"spring\",\n        restDelta: 0.001,\n        restSpeed: 0.01,\n        ...config,\n        onUpdate: set\n      });\n      /**\n       * If we're between frames, resync the animation to the frameloop.\n       */\n      if (!frameData.isProcessing) {\n        const delta = performance.now() - frameData.timestamp;\n        if (delta < 30) {\n          activeSpringAnimation.current.time = millisecondsToSeconds(delta);\n        }\n      }\n      return value.get();\n    }, stopAnimation);\n  }, [JSON.stringify(config)]);\n  useIsomorphicLayoutEffect(() => {\n    if (isMotionValue(source)) {\n      return source.on(\"change\", v => value.set(parseFloat(v)));\n    }\n  }, [value]);\n  return value;\n}\nexport { useSpring };", "map": {"version": 3, "names": ["useContext", "useRef", "useInsertionEffect", "isMotionValue", "useMotionValue", "MotionConfigContext", "useIsomorphicLayoutEffect", "animateValue", "millisecondsToSeconds", "frameData", "useSpring", "source", "config", "isStatic", "activeSpringAnimation", "value", "get", "stopAnimation", "current", "stop", "attach", "v", "set", "keyframes", "velocity", "getVelocity", "type", "restDelta", "restSpeed", "onUpdate", "isProcessing", "delta", "performance", "now", "timestamp", "time", "JSON", "stringify", "on", "parseFloat"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/value/use-spring.mjs"], "sourcesContent": ["import { useContext, useRef, useInsertionEffect } from 'react';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\nimport { useMotionValue } from './use-motion-value.mjs';\nimport { MotionConfigContext } from '../context/MotionConfigContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../utils/use-isomorphic-effect.mjs';\nimport { animateValue } from '../animation/animators/js/index.mjs';\nimport { millisecondsToSeconds } from '../utils/time-conversion.mjs';\nimport { frameData } from '../frameloop/frame.mjs';\n\n/**\n * Creates a `MotionValue` that, when `set`, will use a spring animation to animate to its new state.\n *\n * It can either work as a stand-alone `MotionValue` by initialising it with a value, or as a subscriber\n * to another `MotionValue`.\n *\n * @remarks\n *\n * ```jsx\n * const x = useSpring(0, { stiffness: 300 })\n * const y = useSpring(x, { damping: 10 })\n * ```\n *\n * @param inputValue - `MotionValue` or number. If provided a `MotionValue`, when the input `MotionValue` changes, the created `MotionValue` will spring towards that value.\n * @param springConfig - Configuration options for the spring.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction useSpring(source, config = {}) {\n    const { isStatic } = useContext(MotionConfigContext);\n    const activeSpringAnimation = useRef(null);\n    const value = useMotionValue(isMotionValue(source) ? source.get() : source);\n    const stopAnimation = () => {\n        if (activeSpringAnimation.current) {\n            activeSpringAnimation.current.stop();\n        }\n    };\n    useInsertionEffect(() => {\n        return value.attach((v, set) => {\n            /**\n             * A more hollistic approach to this might be to use isStatic to fix VisualElement animations\n             * at that level, but this will work for now\n             */\n            if (isStatic)\n                return set(v);\n            stopAnimation();\n            activeSpringAnimation.current = animateValue({\n                keyframes: [value.get(), v],\n                velocity: value.getVelocity(),\n                type: \"spring\",\n                restDelta: 0.001,\n                restSpeed: 0.01,\n                ...config,\n                onUpdate: set,\n            });\n            /**\n             * If we're between frames, resync the animation to the frameloop.\n             */\n            if (!frameData.isProcessing) {\n                const delta = performance.now() - frameData.timestamp;\n                if (delta < 30) {\n                    activeSpringAnimation.current.time =\n                        millisecondsToSeconds(delta);\n                }\n            }\n            return value.get();\n        }, stopAnimation);\n    }, [JSON.stringify(config)]);\n    useIsomorphicLayoutEffect(() => {\n        if (isMotionValue(source)) {\n            return source.on(\"change\", (v) => value.set(parseFloat(v)));\n        }\n    }, [value]);\n    return value;\n}\n\nexport { useSpring };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,OAAO;AAC9D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,mBAAmB,QAAQ,oCAAoC;AACxE,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,YAAY,QAAQ,qCAAqC;AAClE,SAASC,qBAAqB,QAAQ,8BAA8B;AACpE,SAASC,SAAS,QAAQ,wBAAwB;;AAElD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE;EACpC,MAAM;IAAEC;EAAS,CAAC,GAAGb,UAAU,CAACK,mBAAmB,CAAC;EACpD,MAAMS,qBAAqB,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAMc,KAAK,GAAGX,cAAc,CAACD,aAAa,CAACQ,MAAM,CAAC,GAAGA,MAAM,CAACK,GAAG,CAAC,CAAC,GAAGL,MAAM,CAAC;EAC3E,MAAMM,aAAa,GAAGA,CAAA,KAAM;IACxB,IAAIH,qBAAqB,CAACI,OAAO,EAAE;MAC/BJ,qBAAqB,CAACI,OAAO,CAACC,IAAI,CAAC,CAAC;IACxC;EACJ,CAAC;EACDjB,kBAAkB,CAAC,MAAM;IACrB,OAAOa,KAAK,CAACK,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;MAC5B;AACZ;AACA;AACA;MACY,IAAIT,QAAQ,EACR,OAAOS,GAAG,CAACD,CAAC,CAAC;MACjBJ,aAAa,CAAC,CAAC;MACfH,qBAAqB,CAACI,OAAO,GAAGX,YAAY,CAAC;QACzCgB,SAAS,EAAE,CAACR,KAAK,CAACC,GAAG,CAAC,CAAC,EAAEK,CAAC,CAAC;QAC3BG,QAAQ,EAAET,KAAK,CAACU,WAAW,CAAC,CAAC;QAC7BC,IAAI,EAAE,QAAQ;QACdC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,IAAI;QACf,GAAGhB,MAAM;QACTiB,QAAQ,EAAEP;MACd,CAAC,CAAC;MACF;AACZ;AACA;MACY,IAAI,CAACb,SAAS,CAACqB,YAAY,EAAE;QACzB,MAAMC,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGxB,SAAS,CAACyB,SAAS;QACrD,IAAIH,KAAK,GAAG,EAAE,EAAE;UACZjB,qBAAqB,CAACI,OAAO,CAACiB,IAAI,GAC9B3B,qBAAqB,CAACuB,KAAK,CAAC;QACpC;MACJ;MACA,OAAOhB,KAAK,CAACC,GAAG,CAAC,CAAC;IACtB,CAAC,EAAEC,aAAa,CAAC;EACrB,CAAC,EAAE,CAACmB,IAAI,CAACC,SAAS,CAACzB,MAAM,CAAC,CAAC,CAAC;EAC5BN,yBAAyB,CAAC,MAAM;IAC5B,IAAIH,aAAa,CAACQ,MAAM,CAAC,EAAE;MACvB,OAAOA,MAAM,CAAC2B,EAAE,CAAC,QAAQ,EAAGjB,CAAC,IAAKN,KAAK,CAACO,GAAG,CAACiB,UAAU,CAAClB,CAAC,CAAC,CAAC,CAAC;IAC/D;EACJ,CAAC,EAAE,CAACN,KAAK,CAAC,CAAC;EACX,OAAOA,KAAK;AAChB;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}