{"ast": null, "code": "/**\n *  A fragment is a single item from an ABI, which may represent any of:\n *\n *  - [Functions](FunctionFragment)\n *  - [Events](EventFragment)\n *  - [Constructors](ConstructorFragment)\n *  - Custom [Errors](ErrorFragment)\n *  - [Fallback or Receive](FallbackFragment) functions\n *\n *  @_subsection api/abi/abi-coder:Fragments  [about-fragments]\n */\nimport { defineProperties, getBigInt, getNumber, assert, assertPrivate, assertArgument } from \"../utils/index.js\";\nimport { id } from \"../hash/index.js\";\n;\n// [ \"a\", \"b\" ] => { \"a\": 1, \"b\": 1 }\nfunction setify(items) {\n  const result = new Set();\n  items.forEach(k => result.add(k));\n  return Object.freeze(result);\n}\nconst _kwVisibDeploy = \"external public payable override\";\nconst KwVisibDeploy = setify(_kwVisibDeploy.split(\" \"));\n// Visibility Keywords\nconst _kwVisib = \"constant external internal payable private public pure view override\";\nconst KwVisib = setify(_kwVisib.split(\" \"));\nconst _kwTypes = \"constructor error event fallback function receive struct\";\nconst KwTypes = setify(_kwTypes.split(\" \"));\nconst _kwModifiers = \"calldata memory storage payable indexed\";\nconst KwModifiers = setify(_kwModifiers.split(\" \"));\nconst _kwOther = \"tuple returns\";\n// All Keywords\nconst _keywords = [_kwTypes, _kwModifiers, _kwOther, _kwVisib].join(\" \");\nconst Keywords = setify(_keywords.split(\" \"));\n// Single character tokens\nconst SimpleTokens = {\n  \"(\": \"OPEN_PAREN\",\n  \")\": \"CLOSE_PAREN\",\n  \"[\": \"OPEN_BRACKET\",\n  \"]\": \"CLOSE_BRACKET\",\n  \",\": \"COMMA\",\n  \"@\": \"AT\"\n};\n// Parser regexes to consume the next token\nconst regexWhitespacePrefix = new RegExp(\"^(\\\\s*)\");\nconst regexNumberPrefix = new RegExp(\"^([0-9]+)\");\nconst regexIdPrefix = new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)\");\n// Parser regexs to check validity\nconst regexId = new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)$\");\nconst regexType = new RegExp(\"^(address|bool|bytes([0-9]*)|string|u?int([0-9]*))$\");\nclass TokenString {\n  #offset;\n  #tokens;\n  get offset() {\n    return this.#offset;\n  }\n  get length() {\n    return this.#tokens.length - this.#offset;\n  }\n  constructor(tokens) {\n    this.#offset = 0;\n    this.#tokens = tokens.slice();\n  }\n  clone() {\n    return new TokenString(this.#tokens);\n  }\n  reset() {\n    this.#offset = 0;\n  }\n  #subTokenString(from = 0, to = 0) {\n    return new TokenString(this.#tokens.slice(from, to).map(t => {\n      return Object.freeze(Object.assign({}, t, {\n        match: t.match - from,\n        linkBack: t.linkBack - from,\n        linkNext: t.linkNext - from\n      }));\n    }));\n  }\n  // Pops and returns the value of the next token, if it is a keyword in allowed; throws if out of tokens\n  popKeyword(allowed) {\n    const top = this.peek();\n    if (top.type !== \"KEYWORD\" || !allowed.has(top.text)) {\n      throw new Error(`expected keyword ${top.text}`);\n    }\n    return this.pop().text;\n  }\n  // Pops and returns the value of the next token if it is `type`; throws if out of tokens\n  popType(type) {\n    if (this.peek().type !== type) {\n      const top = this.peek();\n      throw new Error(`expected ${type}; got ${top.type} ${JSON.stringify(top.text)}`);\n    }\n    return this.pop().text;\n  }\n  // Pops and returns a \"(\" TOKENS \")\"\n  popParen() {\n    const top = this.peek();\n    if (top.type !== \"OPEN_PAREN\") {\n      throw new Error(\"bad start\");\n    }\n    const result = this.#subTokenString(this.#offset + 1, top.match + 1);\n    this.#offset = top.match + 1;\n    return result;\n  }\n  // Pops and returns the items within \"(\" ITEM1 \",\" ITEM2 \",\" ... \")\"\n  popParams() {\n    const top = this.peek();\n    if (top.type !== \"OPEN_PAREN\") {\n      throw new Error(\"bad start\");\n    }\n    const result = [];\n    while (this.#offset < top.match - 1) {\n      const link = this.peek().linkNext;\n      result.push(this.#subTokenString(this.#offset + 1, link));\n      this.#offset = link;\n    }\n    this.#offset = top.match + 1;\n    return result;\n  }\n  // Returns the top Token, throwing if out of tokens\n  peek() {\n    if (this.#offset >= this.#tokens.length) {\n      throw new Error(\"out-of-bounds\");\n    }\n    return this.#tokens[this.#offset];\n  }\n  // Returns the next value, if it is a keyword in `allowed`\n  peekKeyword(allowed) {\n    const top = this.peekType(\"KEYWORD\");\n    return top != null && allowed.has(top) ? top : null;\n  }\n  // Returns the value of the next token if it is `type`\n  peekType(type) {\n    if (this.length === 0) {\n      return null;\n    }\n    const top = this.peek();\n    return top.type === type ? top.text : null;\n  }\n  // Returns the next token; throws if out of tokens\n  pop() {\n    const result = this.peek();\n    this.#offset++;\n    return result;\n  }\n  toString() {\n    const tokens = [];\n    for (let i = this.#offset; i < this.#tokens.length; i++) {\n      const token = this.#tokens[i];\n      tokens.push(`${token.type}:${token.text}`);\n    }\n    return `<TokenString ${tokens.join(\" \")}>`;\n  }\n}\nfunction lex(text) {\n  const tokens = [];\n  const throwError = message => {\n    const token = offset < text.length ? JSON.stringify(text[offset]) : \"$EOI\";\n    throw new Error(`invalid token ${token} at ${offset}: ${message}`);\n  };\n  let brackets = [];\n  let commas = [];\n  let offset = 0;\n  while (offset < text.length) {\n    // Strip off any leading whitespace\n    let cur = text.substring(offset);\n    let match = cur.match(regexWhitespacePrefix);\n    if (match) {\n      offset += match[1].length;\n      cur = text.substring(offset);\n    }\n    const token = {\n      depth: brackets.length,\n      linkBack: -1,\n      linkNext: -1,\n      match: -1,\n      type: \"\",\n      text: \"\",\n      offset,\n      value: -1\n    };\n    tokens.push(token);\n    let type = SimpleTokens[cur[0]] || \"\";\n    if (type) {\n      token.type = type;\n      token.text = cur[0];\n      offset++;\n      if (type === \"OPEN_PAREN\") {\n        brackets.push(tokens.length - 1);\n        commas.push(tokens.length - 1);\n      } else if (type == \"CLOSE_PAREN\") {\n        if (brackets.length === 0) {\n          throwError(\"no matching open bracket\");\n        }\n        token.match = brackets.pop();\n        tokens[token.match].match = tokens.length - 1;\n        token.depth--;\n        token.linkBack = commas.pop();\n        tokens[token.linkBack].linkNext = tokens.length - 1;\n      } else if (type === \"COMMA\") {\n        token.linkBack = commas.pop();\n        tokens[token.linkBack].linkNext = tokens.length - 1;\n        commas.push(tokens.length - 1);\n      } else if (type === \"OPEN_BRACKET\") {\n        token.type = \"BRACKET\";\n      } else if (type === \"CLOSE_BRACKET\") {\n        // Remove the CLOSE_BRACKET\n        let suffix = tokens.pop().text;\n        if (tokens.length > 0 && tokens[tokens.length - 1].type === \"NUMBER\") {\n          const value = tokens.pop().text;\n          suffix = value + suffix;\n          tokens[tokens.length - 1].value = getNumber(value);\n        }\n        if (tokens.length === 0 || tokens[tokens.length - 1].type !== \"BRACKET\") {\n          throw new Error(\"missing opening bracket\");\n        }\n        tokens[tokens.length - 1].text += suffix;\n      }\n      continue;\n    }\n    match = cur.match(regexIdPrefix);\n    if (match) {\n      token.text = match[1];\n      offset += token.text.length;\n      if (Keywords.has(token.text)) {\n        token.type = \"KEYWORD\";\n        continue;\n      }\n      if (token.text.match(regexType)) {\n        token.type = \"TYPE\";\n        continue;\n      }\n      token.type = \"ID\";\n      continue;\n    }\n    match = cur.match(regexNumberPrefix);\n    if (match) {\n      token.text = match[1];\n      token.type = \"NUMBER\";\n      offset += token.text.length;\n      continue;\n    }\n    throw new Error(`unexpected token ${JSON.stringify(cur[0])} at position ${offset}`);\n  }\n  return new TokenString(tokens.map(t => Object.freeze(t)));\n}\n// Check only one of `allowed` is in `set`\nfunction allowSingle(set, allowed) {\n  let included = [];\n  for (const key in allowed.keys()) {\n    if (set.has(key)) {\n      included.push(key);\n    }\n  }\n  if (included.length > 1) {\n    throw new Error(`conflicting types: ${included.join(\", \")}`);\n  }\n}\n// Functions to process a Solidity Signature TokenString from left-to-right for...\n// ...the name with an optional type, returning the name\nfunction consumeName(type, tokens) {\n  if (tokens.peekKeyword(KwTypes)) {\n    const keyword = tokens.pop().text;\n    if (keyword !== type) {\n      throw new Error(`expected ${type}, got ${keyword}`);\n    }\n  }\n  return tokens.popType(\"ID\");\n}\n// ...all keywords matching allowed, returning the keywords\nfunction consumeKeywords(tokens, allowed) {\n  const keywords = new Set();\n  while (true) {\n    const keyword = tokens.peekType(\"KEYWORD\");\n    if (keyword == null || allowed && !allowed.has(keyword)) {\n      break;\n    }\n    tokens.pop();\n    if (keywords.has(keyword)) {\n      throw new Error(`duplicate keywords: ${JSON.stringify(keyword)}`);\n    }\n    keywords.add(keyword);\n  }\n  return Object.freeze(keywords);\n}\n// ...all visibility keywords, returning the coalesced mutability\nfunction consumeMutability(tokens) {\n  let modifiers = consumeKeywords(tokens, KwVisib);\n  // Detect conflicting modifiers\n  allowSingle(modifiers, setify(\"constant payable nonpayable\".split(\" \")));\n  allowSingle(modifiers, setify(\"pure view payable nonpayable\".split(\" \")));\n  // Process mutability states\n  if (modifiers.has(\"view\")) {\n    return \"view\";\n  }\n  if (modifiers.has(\"pure\")) {\n    return \"pure\";\n  }\n  if (modifiers.has(\"payable\")) {\n    return \"payable\";\n  }\n  if (modifiers.has(\"nonpayable\")) {\n    return \"nonpayable\";\n  }\n  // Process legacy `constant` last\n  if (modifiers.has(\"constant\")) {\n    return \"view\";\n  }\n  return \"nonpayable\";\n}\n// ...a parameter list, returning the ParamType list\nfunction consumeParams(tokens, allowIndexed) {\n  return tokens.popParams().map(t => ParamType.from(t, allowIndexed));\n}\n// ...a gas limit, returning a BigNumber or null if none\nfunction consumeGas(tokens) {\n  if (tokens.peekType(\"AT\")) {\n    tokens.pop();\n    if (tokens.peekType(\"NUMBER\")) {\n      return getBigInt(tokens.pop().text);\n    }\n    throw new Error(\"invalid gas\");\n  }\n  return null;\n}\nfunction consumeEoi(tokens) {\n  if (tokens.length) {\n    throw new Error(`unexpected tokens at offset ${tokens.offset}: ${tokens.toString()}`);\n  }\n}\nconst regexArrayType = new RegExp(/^(.*)\\[([0-9]*)\\]$/);\nfunction verifyBasicType(type) {\n  const match = type.match(regexType);\n  assertArgument(match, \"invalid type\", \"type\", type);\n  if (type === \"uint\") {\n    return \"uint256\";\n  }\n  if (type === \"int\") {\n    return \"int256\";\n  }\n  if (match[2]) {\n    // bytesXX\n    const length = parseInt(match[2]);\n    assertArgument(length !== 0 && length <= 32, \"invalid bytes length\", \"type\", type);\n  } else if (match[3]) {\n    // intXX or uintXX\n    const size = parseInt(match[3]);\n    assertArgument(size !== 0 && size <= 256 && size % 8 === 0, \"invalid numeric width\", \"type\", type);\n  }\n  return type;\n}\n// Make the Fragment constructors effectively private\nconst _guard = {};\nconst internal = Symbol.for(\"_ethers_internal\");\nconst ParamTypeInternal = \"_ParamTypeInternal\";\nconst ErrorFragmentInternal = \"_ErrorInternal\";\nconst EventFragmentInternal = \"_EventInternal\";\nconst ConstructorFragmentInternal = \"_ConstructorInternal\";\nconst FallbackFragmentInternal = \"_FallbackInternal\";\nconst FunctionFragmentInternal = \"_FunctionInternal\";\nconst StructFragmentInternal = \"_StructInternal\";\n/**\n *  Each input and output of a [[Fragment]] is an Array of **ParamType**.\n */\nexport class ParamType {\n  /**\n   *  The local name of the parameter (or ``\"\"`` if unbound)\n   */\n  name;\n  /**\n   *  The fully qualified type (e.g. ``\"address\"``, ``\"tuple(address)\"``,\n   *  ``\"uint256[3][]\"``)\n   */\n  type;\n  /**\n   *  The base type (e.g. ``\"address\"``, ``\"tuple\"``, ``\"array\"``)\n   */\n  baseType;\n  /**\n   *  True if the parameters is indexed.\n   *\n   *  For non-indexable types this is ``null``.\n   */\n  indexed;\n  /**\n   *  The components for the tuple.\n   *\n   *  For non-tuple types this is ``null``.\n   */\n  components;\n  /**\n   *  The array length, or ``-1`` for dynamic-lengthed arrays.\n   *\n   *  For non-array types this is ``null``.\n   */\n  arrayLength;\n  /**\n   *  The type of each child in the array.\n   *\n   *  For non-array types this is ``null``.\n   */\n  arrayChildren;\n  /**\n   *  @private\n   */\n  constructor(guard, name, type, baseType, indexed, components, arrayLength, arrayChildren) {\n    assertPrivate(guard, _guard, \"ParamType\");\n    Object.defineProperty(this, internal, {\n      value: ParamTypeInternal\n    });\n    if (components) {\n      components = Object.freeze(components.slice());\n    }\n    if (baseType === \"array\") {\n      if (arrayLength == null || arrayChildren == null) {\n        throw new Error(\"\");\n      }\n    } else if (arrayLength != null || arrayChildren != null) {\n      throw new Error(\"\");\n    }\n    if (baseType === \"tuple\") {\n      if (components == null) {\n        throw new Error(\"\");\n      }\n    } else if (components != null) {\n      throw new Error(\"\");\n    }\n    defineProperties(this, {\n      name,\n      type,\n      baseType,\n      indexed,\n      components,\n      arrayLength,\n      arrayChildren\n    });\n  }\n  /**\n   *  Return a string representation of this type.\n   *\n   *  For example,\n   *\n   *  ``sighash\" => \"(uint256,address)\"``\n   *\n   *  ``\"minimal\" => \"tuple(uint256,address) indexed\"``\n   *\n   *  ``\"full\" => \"tuple(uint256 foo, address bar) indexed baz\"``\n   */\n  format(format) {\n    if (format == null) {\n      format = \"sighash\";\n    }\n    if (format === \"json\") {\n      const name = this.name || \"\";\n      if (this.isArray()) {\n        const result = JSON.parse(this.arrayChildren.format(\"json\"));\n        result.name = name;\n        result.type += `[${this.arrayLength < 0 ? \"\" : String(this.arrayLength)}]`;\n        return JSON.stringify(result);\n      }\n      const result = {\n        type: this.baseType === \"tuple\" ? \"tuple\" : this.type,\n        name\n      };\n      if (typeof this.indexed === \"boolean\") {\n        result.indexed = this.indexed;\n      }\n      if (this.isTuple()) {\n        result.components = this.components.map(c => JSON.parse(c.format(format)));\n      }\n      return JSON.stringify(result);\n    }\n    let result = \"\";\n    // Array\n    if (this.isArray()) {\n      result += this.arrayChildren.format(format);\n      result += `[${this.arrayLength < 0 ? \"\" : String(this.arrayLength)}]`;\n    } else {\n      if (this.isTuple()) {\n        result += \"(\" + this.components.map(comp => comp.format(format)).join(format === \"full\" ? \", \" : \",\") + \")\";\n      } else {\n        result += this.type;\n      }\n    }\n    if (format !== \"sighash\") {\n      if (this.indexed === true) {\n        result += \" indexed\";\n      }\n      if (format === \"full\" && this.name) {\n        result += \" \" + this.name;\n      }\n    }\n    return result;\n  }\n  /**\n   *  Returns true if %%this%% is an Array type.\n   *\n   *  This provides a type gaurd ensuring that [[arrayChildren]]\n   *  and [[arrayLength]] are non-null.\n   */\n  isArray() {\n    return this.baseType === \"array\";\n  }\n  /**\n   *  Returns true if %%this%% is a Tuple type.\n   *\n   *  This provides a type gaurd ensuring that [[components]]\n   *  is non-null.\n   */\n  isTuple() {\n    return this.baseType === \"tuple\";\n  }\n  /**\n   *  Returns true if %%this%% is an Indexable type.\n   *\n   *  This provides a type gaurd ensuring that [[indexed]]\n   *  is non-null.\n   */\n  isIndexable() {\n    return this.indexed != null;\n  }\n  /**\n   *  Walks the **ParamType** with %%value%%, calling %%process%%\n   *  on each type, destructing the %%value%% recursively.\n   */\n  walk(value, process) {\n    if (this.isArray()) {\n      if (!Array.isArray(value)) {\n        throw new Error(\"invalid array value\");\n      }\n      if (this.arrayLength !== -1 && value.length !== this.arrayLength) {\n        throw new Error(\"array is wrong length\");\n      }\n      const _this = this;\n      return value.map(v => _this.arrayChildren.walk(v, process));\n    }\n    if (this.isTuple()) {\n      if (!Array.isArray(value)) {\n        throw new Error(\"invalid tuple value\");\n      }\n      if (value.length !== this.components.length) {\n        throw new Error(\"array is wrong length\");\n      }\n      const _this = this;\n      return value.map((v, i) => _this.components[i].walk(v, process));\n    }\n    return process(this.type, value);\n  }\n  #walkAsync(promises, value, process, setValue) {\n    if (this.isArray()) {\n      if (!Array.isArray(value)) {\n        throw new Error(\"invalid array value\");\n      }\n      if (this.arrayLength !== -1 && value.length !== this.arrayLength) {\n        throw new Error(\"array is wrong length\");\n      }\n      const childType = this.arrayChildren;\n      const result = value.slice();\n      result.forEach((value, index) => {\n        childType.#walkAsync(promises, value, process, value => {\n          result[index] = value;\n        });\n      });\n      setValue(result);\n      return;\n    }\n    if (this.isTuple()) {\n      const components = this.components;\n      // Convert the object into an array\n      let result;\n      if (Array.isArray(value)) {\n        result = value.slice();\n      } else {\n        if (value == null || typeof value !== \"object\") {\n          throw new Error(\"invalid tuple value\");\n        }\n        result = components.map(param => {\n          if (!param.name) {\n            throw new Error(\"cannot use object value with unnamed components\");\n          }\n          if (!(param.name in value)) {\n            throw new Error(`missing value for component ${param.name}`);\n          }\n          return value[param.name];\n        });\n      }\n      if (result.length !== this.components.length) {\n        throw new Error(\"array is wrong length\");\n      }\n      result.forEach((value, index) => {\n        components[index].#walkAsync(promises, value, process, value => {\n          result[index] = value;\n        });\n      });\n      setValue(result);\n      return;\n    }\n    const result = process(this.type, value);\n    if (result.then) {\n      promises.push(async function () {\n        setValue(await result);\n      }());\n    } else {\n      setValue(result);\n    }\n  }\n  /**\n   *  Walks the **ParamType** with %%value%%, asynchronously calling\n   *  %%process%% on each type, destructing the %%value%% recursively.\n   *\n   *  This can be used to resolve ENS names by walking and resolving each\n   *  ``\"address\"`` type.\n   */\n  async walkAsync(value, process) {\n    const promises = [];\n    const result = [value];\n    this.#walkAsync(promises, value, process, value => {\n      result[0] = value;\n    });\n    if (promises.length) {\n      await Promise.all(promises);\n    }\n    return result[0];\n  }\n  /**\n   *  Creates a new **ParamType** for %%obj%%.\n   *\n   *  If %%allowIndexed%% then the ``indexed`` keyword is permitted,\n   *  otherwise the ``indexed`` keyword will throw an error.\n   */\n  static from(obj, allowIndexed) {\n    if (ParamType.isParamType(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      try {\n        return ParamType.from(lex(obj), allowIndexed);\n      } catch (error) {\n        assertArgument(false, \"invalid param type\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      let type = \"\",\n        baseType = \"\";\n      let comps = null;\n      if (consumeKeywords(obj, setify([\"tuple\"])).has(\"tuple\") || obj.peekType(\"OPEN_PAREN\")) {\n        // Tuple\n        baseType = \"tuple\";\n        comps = obj.popParams().map(t => ParamType.from(t));\n        type = `tuple(${comps.map(c => c.format()).join(\",\")})`;\n      } else {\n        // Normal\n        type = verifyBasicType(obj.popType(\"TYPE\"));\n        baseType = type;\n      }\n      // Check for Array\n      let arrayChildren = null;\n      let arrayLength = null;\n      while (obj.length && obj.peekType(\"BRACKET\")) {\n        const bracket = obj.pop(); //arrays[i];\n        arrayChildren = new ParamType(_guard, \"\", type, baseType, null, comps, arrayLength, arrayChildren);\n        arrayLength = bracket.value;\n        type += bracket.text;\n        baseType = \"array\";\n        comps = null;\n      }\n      let indexed = null;\n      const keywords = consumeKeywords(obj, KwModifiers);\n      if (keywords.has(\"indexed\")) {\n        if (!allowIndexed) {\n          throw new Error(\"\");\n        }\n        indexed = true;\n      }\n      const name = obj.peekType(\"ID\") ? obj.pop().text : \"\";\n      if (obj.length) {\n        throw new Error(\"leftover tokens\");\n      }\n      return new ParamType(_guard, name, type, baseType, indexed, comps, arrayLength, arrayChildren);\n    }\n    const name = obj.name;\n    assertArgument(!name || typeof name === \"string\" && name.match(regexId), \"invalid name\", \"obj.name\", name);\n    let indexed = obj.indexed;\n    if (indexed != null) {\n      assertArgument(allowIndexed, \"parameter cannot be indexed\", \"obj.indexed\", obj.indexed);\n      indexed = !!indexed;\n    }\n    let type = obj.type;\n    let arrayMatch = type.match(regexArrayType);\n    if (arrayMatch) {\n      const arrayLength = parseInt(arrayMatch[2] || \"-1\");\n      const arrayChildren = ParamType.from({\n        type: arrayMatch[1],\n        components: obj.components\n      });\n      return new ParamType(_guard, name || \"\", type, \"array\", indexed, null, arrayLength, arrayChildren);\n    }\n    if (type === \"tuple\" || type.startsWith(\"tuple(\" /* fix: ) */) || type.startsWith(\"(\" /* fix: ) */)) {\n      const comps = obj.components != null ? obj.components.map(c => ParamType.from(c)) : null;\n      const tuple = new ParamType(_guard, name || \"\", type, \"tuple\", indexed, comps, null, null);\n      // @TODO: use lexer to validate and normalize type\n      return tuple;\n    }\n    type = verifyBasicType(obj.type);\n    return new ParamType(_guard, name || \"\", type, type, indexed, null, null, null);\n  }\n  /**\n   *  Returns true if %%value%% is a **ParamType**.\n   */\n  static isParamType(value) {\n    return value && value[internal] === ParamTypeInternal;\n  }\n}\n/**\n *  An abstract class to represent An individual fragment from a parse ABI.\n */\nexport class Fragment {\n  /**\n   *  The type of the fragment.\n   */\n  type;\n  /**\n   *  The inputs for the fragment.\n   */\n  inputs;\n  /**\n   *  @private\n   */\n  constructor(guard, type, inputs) {\n    assertPrivate(guard, _guard, \"Fragment\");\n    inputs = Object.freeze(inputs.slice());\n    defineProperties(this, {\n      type,\n      inputs\n    });\n  }\n  /**\n   *  Creates a new **Fragment** for %%obj%%, wich can be any supported\n   *  ABI frgament type.\n   */\n  static from(obj) {\n    if (typeof obj === \"string\") {\n      // Try parsing JSON...\n      try {\n        Fragment.from(JSON.parse(obj));\n      } catch (e) {}\n      // ...otherwise, use the human-readable lexer\n      return Fragment.from(lex(obj));\n    }\n    if (obj instanceof TokenString) {\n      // Human-readable ABI (already lexed)\n      const type = obj.peekKeyword(KwTypes);\n      switch (type) {\n        case \"constructor\":\n          return ConstructorFragment.from(obj);\n        case \"error\":\n          return ErrorFragment.from(obj);\n        case \"event\":\n          return EventFragment.from(obj);\n        case \"fallback\":\n        case \"receive\":\n          return FallbackFragment.from(obj);\n        case \"function\":\n          return FunctionFragment.from(obj);\n        case \"struct\":\n          return StructFragment.from(obj);\n      }\n    } else if (typeof obj === \"object\") {\n      // JSON ABI\n      switch (obj.type) {\n        case \"constructor\":\n          return ConstructorFragment.from(obj);\n        case \"error\":\n          return ErrorFragment.from(obj);\n        case \"event\":\n          return EventFragment.from(obj);\n        case \"fallback\":\n        case \"receive\":\n          return FallbackFragment.from(obj);\n        case \"function\":\n          return FunctionFragment.from(obj);\n        case \"struct\":\n          return StructFragment.from(obj);\n      }\n      assert(false, `unsupported type: ${obj.type}`, \"UNSUPPORTED_OPERATION\", {\n        operation: \"Fragment.from\"\n      });\n    }\n    assertArgument(false, \"unsupported frgament object\", \"obj\", obj);\n  }\n  /**\n   *  Returns true if %%value%% is a [[ConstructorFragment]].\n   */\n  static isConstructor(value) {\n    return ConstructorFragment.isFragment(value);\n  }\n  /**\n   *  Returns true if %%value%% is an [[ErrorFragment]].\n   */\n  static isError(value) {\n    return ErrorFragment.isFragment(value);\n  }\n  /**\n   *  Returns true if %%value%% is an [[EventFragment]].\n   */\n  static isEvent(value) {\n    return EventFragment.isFragment(value);\n  }\n  /**\n   *  Returns true if %%value%% is a [[FunctionFragment]].\n   */\n  static isFunction(value) {\n    return FunctionFragment.isFragment(value);\n  }\n  /**\n   *  Returns true if %%value%% is a [[StructFragment]].\n   */\n  static isStruct(value) {\n    return StructFragment.isFragment(value);\n  }\n}\n/**\n *  An abstract class to represent An individual fragment\n *  which has a name from a parse ABI.\n */\nexport class NamedFragment extends Fragment {\n  /**\n   *  The name of the fragment.\n   */\n  name;\n  /**\n   *  @private\n   */\n  constructor(guard, type, name, inputs) {\n    super(guard, type, inputs);\n    assertArgument(typeof name === \"string\" && name.match(regexId), \"invalid identifier\", \"name\", name);\n    inputs = Object.freeze(inputs.slice());\n    defineProperties(this, {\n      name\n    });\n  }\n}\nfunction joinParams(format, params) {\n  return \"(\" + params.map(p => p.format(format)).join(format === \"full\" ? \", \" : \",\") + \")\";\n}\n/**\n *  A Fragment which represents a //Custom Error//.\n */\nexport class ErrorFragment extends NamedFragment {\n  /**\n   *  @private\n   */\n  constructor(guard, name, inputs) {\n    super(guard, \"error\", name, inputs);\n    Object.defineProperty(this, internal, {\n      value: ErrorFragmentInternal\n    });\n  }\n  /**\n   *  The Custom Error selector.\n   */\n  get selector() {\n    return id(this.format(\"sighash\")).substring(0, 10);\n  }\n  /**\n   *  Returns a string representation of this fragment as %%format%%.\n   */\n  format(format) {\n    if (format == null) {\n      format = \"sighash\";\n    }\n    if (format === \"json\") {\n      return JSON.stringify({\n        type: \"error\",\n        name: this.name,\n        inputs: this.inputs.map(input => JSON.parse(input.format(format)))\n      });\n    }\n    const result = [];\n    if (format !== \"sighash\") {\n      result.push(\"error\");\n    }\n    result.push(this.name + joinParams(format, this.inputs));\n    return result.join(\" \");\n  }\n  /**\n   *  Returns a new **ErrorFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (ErrorFragment.isFragment(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      return ErrorFragment.from(lex(obj));\n    } else if (obj instanceof TokenString) {\n      const name = consumeName(\"error\", obj);\n      const inputs = consumeParams(obj);\n      consumeEoi(obj);\n      return new ErrorFragment(_guard, name, inputs);\n    }\n    return new ErrorFragment(_guard, obj.name, obj.inputs ? obj.inputs.map(ParamType.from) : []);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is an\n   *  **ErrorFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === ErrorFragmentInternal;\n  }\n}\n/**\n *  A Fragment which represents an Event.\n */\nexport class EventFragment extends NamedFragment {\n  /**\n   *  Whether this event is anonymous.\n   */\n  anonymous;\n  /**\n   *  @private\n   */\n  constructor(guard, name, inputs, anonymous) {\n    super(guard, \"event\", name, inputs);\n    Object.defineProperty(this, internal, {\n      value: EventFragmentInternal\n    });\n    defineProperties(this, {\n      anonymous\n    });\n  }\n  /**\n   *  The Event topic hash.\n   */\n  get topicHash() {\n    return id(this.format(\"sighash\"));\n  }\n  /**\n   *  Returns a string representation of this event as %%format%%.\n   */\n  format(format) {\n    if (format == null) {\n      format = \"sighash\";\n    }\n    if (format === \"json\") {\n      return JSON.stringify({\n        type: \"event\",\n        anonymous: this.anonymous,\n        name: this.name,\n        inputs: this.inputs.map(i => JSON.parse(i.format(format)))\n      });\n    }\n    const result = [];\n    if (format !== \"sighash\") {\n      result.push(\"event\");\n    }\n    result.push(this.name + joinParams(format, this.inputs));\n    if (format !== \"sighash\" && this.anonymous) {\n      result.push(\"anonymous\");\n    }\n    return result.join(\" \");\n  }\n  /**\n   *  Return the topic hash for an event with %%name%% and %%params%%.\n   */\n  static getTopicHash(name, params) {\n    params = (params || []).map(p => ParamType.from(p));\n    const fragment = new EventFragment(_guard, name, params, false);\n    return fragment.topicHash;\n  }\n  /**\n   *  Returns a new **EventFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (EventFragment.isFragment(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      try {\n        return EventFragment.from(lex(obj));\n      } catch (error) {\n        assertArgument(false, \"invalid event fragment\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      const name = consumeName(\"event\", obj);\n      const inputs = consumeParams(obj, true);\n      const anonymous = !!consumeKeywords(obj, setify([\"anonymous\"])).has(\"anonymous\");\n      consumeEoi(obj);\n      return new EventFragment(_guard, name, inputs, anonymous);\n    }\n    return new EventFragment(_guard, obj.name, obj.inputs ? obj.inputs.map(p => ParamType.from(p, true)) : [], !!obj.anonymous);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is an\n   *  **EventFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === EventFragmentInternal;\n  }\n}\n/**\n *  A Fragment which represents a constructor.\n */\nexport class ConstructorFragment extends Fragment {\n  /**\n   *  Whether the constructor can receive an endowment.\n   */\n  payable;\n  /**\n   *  The recommended gas limit for deployment or ``null``.\n   */\n  gas;\n  /**\n   *  @private\n   */\n  constructor(guard, type, inputs, payable, gas) {\n    super(guard, type, inputs);\n    Object.defineProperty(this, internal, {\n      value: ConstructorFragmentInternal\n    });\n    defineProperties(this, {\n      payable,\n      gas\n    });\n  }\n  /**\n   *  Returns a string representation of this constructor as %%format%%.\n   */\n  format(format) {\n    assert(format != null && format !== \"sighash\", \"cannot format a constructor for sighash\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"format(sighash)\"\n    });\n    if (format === \"json\") {\n      return JSON.stringify({\n        type: \"constructor\",\n        stateMutability: this.payable ? \"payable\" : \"undefined\",\n        payable: this.payable,\n        gas: this.gas != null ? this.gas : undefined,\n        inputs: this.inputs.map(i => JSON.parse(i.format(format)))\n      });\n    }\n    const result = [`constructor${joinParams(format, this.inputs)}`];\n    if (this.payable) {\n      result.push(\"payable\");\n    }\n    if (this.gas != null) {\n      result.push(`@${this.gas.toString()}`);\n    }\n    return result.join(\" \");\n  }\n  /**\n   *  Returns a new **ConstructorFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (ConstructorFragment.isFragment(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      try {\n        return ConstructorFragment.from(lex(obj));\n      } catch (error) {\n        assertArgument(false, \"invalid constuctor fragment\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      consumeKeywords(obj, setify([\"constructor\"]));\n      const inputs = consumeParams(obj);\n      const payable = !!consumeKeywords(obj, KwVisibDeploy).has(\"payable\");\n      const gas = consumeGas(obj);\n      consumeEoi(obj);\n      return new ConstructorFragment(_guard, \"constructor\", inputs, payable, gas);\n    }\n    return new ConstructorFragment(_guard, \"constructor\", obj.inputs ? obj.inputs.map(ParamType.from) : [], !!obj.payable, obj.gas != null ? obj.gas : null);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is a\n   *  **ConstructorFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === ConstructorFragmentInternal;\n  }\n}\n/**\n *  A Fragment which represents a method.\n */\nexport class FallbackFragment extends Fragment {\n  /**\n   *  If the function can be sent value during invocation.\n   */\n  payable;\n  constructor(guard, inputs, payable) {\n    super(guard, \"fallback\", inputs);\n    Object.defineProperty(this, internal, {\n      value: FallbackFragmentInternal\n    });\n    defineProperties(this, {\n      payable\n    });\n  }\n  /**\n   *  Returns a string representation of this fallback as %%format%%.\n   */\n  format(format) {\n    const type = this.inputs.length === 0 ? \"receive\" : \"fallback\";\n    if (format === \"json\") {\n      const stateMutability = this.payable ? \"payable\" : \"nonpayable\";\n      return JSON.stringify({\n        type,\n        stateMutability\n      });\n    }\n    return `${type}()${this.payable ? \" payable\" : \"\"}`;\n  }\n  /**\n   *  Returns a new **FallbackFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (FallbackFragment.isFragment(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      try {\n        return FallbackFragment.from(lex(obj));\n      } catch (error) {\n        assertArgument(false, \"invalid fallback fragment\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      const errorObj = obj.toString();\n      const topIsValid = obj.peekKeyword(setify([\"fallback\", \"receive\"]));\n      assertArgument(topIsValid, \"type must be fallback or receive\", \"obj\", errorObj);\n      const type = obj.popKeyword(setify([\"fallback\", \"receive\"]));\n      // receive()\n      if (type === \"receive\") {\n        const inputs = consumeParams(obj);\n        assertArgument(inputs.length === 0, `receive cannot have arguments`, \"obj.inputs\", inputs);\n        consumeKeywords(obj, setify([\"payable\"]));\n        consumeEoi(obj);\n        return new FallbackFragment(_guard, [], true);\n      }\n      // fallback() [payable]\n      // fallback(bytes) [payable] returns (bytes)\n      let inputs = consumeParams(obj);\n      if (inputs.length) {\n        assertArgument(inputs.length === 1 && inputs[0].type === \"bytes\", \"invalid fallback inputs\", \"obj.inputs\", inputs.map(i => i.format(\"minimal\")).join(\", \"));\n      } else {\n        inputs = [ParamType.from(\"bytes\")];\n      }\n      const mutability = consumeMutability(obj);\n      assertArgument(mutability === \"nonpayable\" || mutability === \"payable\", \"fallback cannot be constants\", \"obj.stateMutability\", mutability);\n      if (consumeKeywords(obj, setify([\"returns\"])).has(\"returns\")) {\n        const outputs = consumeParams(obj);\n        assertArgument(outputs.length === 1 && outputs[0].type === \"bytes\", \"invalid fallback outputs\", \"obj.outputs\", outputs.map(i => i.format(\"minimal\")).join(\", \"));\n      }\n      consumeEoi(obj);\n      return new FallbackFragment(_guard, inputs, mutability === \"payable\");\n    }\n    if (obj.type === \"receive\") {\n      return new FallbackFragment(_guard, [], true);\n    }\n    if (obj.type === \"fallback\") {\n      const inputs = [ParamType.from(\"bytes\")];\n      const payable = obj.stateMutability === \"payable\";\n      return new FallbackFragment(_guard, inputs, payable);\n    }\n    assertArgument(false, \"invalid fallback description\", \"obj\", obj);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is a\n   *  **FallbackFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === FallbackFragmentInternal;\n  }\n}\n/**\n *  A Fragment which represents a method.\n */\nexport class FunctionFragment extends NamedFragment {\n  /**\n   *  If the function is constant (e.g. ``pure`` or ``view`` functions).\n   */\n  constant;\n  /**\n   *  The returned types for the result of calling this function.\n   */\n  outputs;\n  /**\n   *  The state mutability (e.g. ``payable``, ``nonpayable``, ``view``\n   *  or ``pure``)\n   */\n  stateMutability;\n  /**\n   *  If the function can be sent value during invocation.\n   */\n  payable;\n  /**\n   *  The recommended gas limit to send when calling this function.\n   */\n  gas;\n  /**\n   *  @private\n   */\n  constructor(guard, name, stateMutability, inputs, outputs, gas) {\n    super(guard, \"function\", name, inputs);\n    Object.defineProperty(this, internal, {\n      value: FunctionFragmentInternal\n    });\n    outputs = Object.freeze(outputs.slice());\n    const constant = stateMutability === \"view\" || stateMutability === \"pure\";\n    const payable = stateMutability === \"payable\";\n    defineProperties(this, {\n      constant,\n      gas,\n      outputs,\n      payable,\n      stateMutability\n    });\n  }\n  /**\n   *  The Function selector.\n   */\n  get selector() {\n    return id(this.format(\"sighash\")).substring(0, 10);\n  }\n  /**\n   *  Returns a string representation of this function as %%format%%.\n   */\n  format(format) {\n    if (format == null) {\n      format = \"sighash\";\n    }\n    if (format === \"json\") {\n      return JSON.stringify({\n        type: \"function\",\n        name: this.name,\n        constant: this.constant,\n        stateMutability: this.stateMutability !== \"nonpayable\" ? this.stateMutability : undefined,\n        payable: this.payable,\n        gas: this.gas != null ? this.gas : undefined,\n        inputs: this.inputs.map(i => JSON.parse(i.format(format))),\n        outputs: this.outputs.map(o => JSON.parse(o.format(format)))\n      });\n    }\n    const result = [];\n    if (format !== \"sighash\") {\n      result.push(\"function\");\n    }\n    result.push(this.name + joinParams(format, this.inputs));\n    if (format !== \"sighash\") {\n      if (this.stateMutability !== \"nonpayable\") {\n        result.push(this.stateMutability);\n      }\n      if (this.outputs && this.outputs.length) {\n        result.push(\"returns\");\n        result.push(joinParams(format, this.outputs));\n      }\n      if (this.gas != null) {\n        result.push(`@${this.gas.toString()}`);\n      }\n    }\n    return result.join(\" \");\n  }\n  /**\n   *  Return the selector for a function with %%name%% and %%params%%.\n   */\n  static getSelector(name, params) {\n    params = (params || []).map(p => ParamType.from(p));\n    const fragment = new FunctionFragment(_guard, name, \"view\", params, [], null);\n    return fragment.selector;\n  }\n  /**\n   *  Returns a new **FunctionFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (FunctionFragment.isFragment(obj)) {\n      return obj;\n    }\n    if (typeof obj === \"string\") {\n      try {\n        return FunctionFragment.from(lex(obj));\n      } catch (error) {\n        assertArgument(false, \"invalid function fragment\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      const name = consumeName(\"function\", obj);\n      const inputs = consumeParams(obj);\n      const mutability = consumeMutability(obj);\n      let outputs = [];\n      if (consumeKeywords(obj, setify([\"returns\"])).has(\"returns\")) {\n        outputs = consumeParams(obj);\n      }\n      const gas = consumeGas(obj);\n      consumeEoi(obj);\n      return new FunctionFragment(_guard, name, mutability, inputs, outputs, gas);\n    }\n    let stateMutability = obj.stateMutability;\n    // Use legacy Solidity ABI logic if stateMutability is missing\n    if (stateMutability == null) {\n      stateMutability = \"payable\";\n      if (typeof obj.constant === \"boolean\") {\n        stateMutability = \"view\";\n        if (!obj.constant) {\n          stateMutability = \"payable\";\n          if (typeof obj.payable === \"boolean\" && !obj.payable) {\n            stateMutability = \"nonpayable\";\n          }\n        }\n      } else if (typeof obj.payable === \"boolean\" && !obj.payable) {\n        stateMutability = \"nonpayable\";\n      }\n    }\n    // @TODO: verifyState for stateMutability (e.g. throw if\n    //        payable: false but stateMutability is \"nonpayable\")\n    return new FunctionFragment(_guard, obj.name, stateMutability, obj.inputs ? obj.inputs.map(ParamType.from) : [], obj.outputs ? obj.outputs.map(ParamType.from) : [], obj.gas != null ? obj.gas : null);\n  }\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is a\n   *  **FunctionFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === FunctionFragmentInternal;\n  }\n}\n/**\n *  A Fragment which represents a structure.\n */\nexport class StructFragment extends NamedFragment {\n  /**\n   *  @private\n   */\n  constructor(guard, name, inputs) {\n    super(guard, \"struct\", name, inputs);\n    Object.defineProperty(this, internal, {\n      value: StructFragmentInternal\n    });\n  }\n  /**\n   *  Returns a string representation of this struct as %%format%%.\n   */\n  format() {\n    throw new Error(\"@TODO\");\n  }\n  /**\n   *  Returns a new **StructFragment** for %%obj%%.\n   */\n  static from(obj) {\n    if (typeof obj === \"string\") {\n      try {\n        return StructFragment.from(lex(obj));\n      } catch (error) {\n        assertArgument(false, \"invalid struct fragment\", \"obj\", obj);\n      }\n    } else if (obj instanceof TokenString) {\n      const name = consumeName(\"struct\", obj);\n      const inputs = consumeParams(obj);\n      consumeEoi(obj);\n      return new StructFragment(_guard, name, inputs);\n    }\n    return new StructFragment(_guard, obj.name, obj.inputs ? obj.inputs.map(ParamType.from) : []);\n  }\n  // @TODO: fix this return type\n  /**\n   *  Returns ``true`` and provides a type guard if %%value%% is a\n   *  **StructFragment**.\n   */\n  static isFragment(value) {\n    return value && value[internal] === StructFragmentInternal;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "getBigInt", "getNumber", "assert", "assertPrivate", "assertArgument", "id", "setify", "items", "result", "Set", "for<PERSON>ach", "k", "add", "Object", "freeze", "_kwVisibDeploy", "KwVisibDeploy", "split", "_kwVisib", "KwVisib", "_kwTypes", "KwTypes", "_kwModifiers", "KwModifiers", "_kwOther", "_keywords", "join", "Keywords", "SimpleTokens", "regexWhitespacePrefix", "RegExp", "regexNumberPrefix", "regexIdPrefix", "regexId", "regexType", "TokenString", "offset", "tokens", "length", "constructor", "slice", "clone", "reset", "subTokenString", "#subTokenString", "from", "to", "map", "t", "assign", "match", "linkBack", "linkNext", "popKeyword", "allowed", "top", "peek", "type", "has", "text", "Error", "pop", "popType", "JSON", "stringify", "popParen", "popParams", "link", "push", "peek<PERSON>ey<PERSON>", "peekType", "toString", "i", "token", "lex", "throwError", "message", "brackets", "commas", "cur", "substring", "depth", "value", "suffix", "allowSingle", "set", "included", "key", "keys", "consumeName", "keyword", "consumeKeywords", "keywords", "consumeMutability", "modifiers", "consumeParams", "allowIndexed", "ParamType", "consumeGas", "consumeEoi", "regexArrayType", "verifyBasicType", "parseInt", "size", "_guard", "internal", "Symbol", "for", "ParamTypeInternal", "ErrorFragmentInternal", "EventFragmentInternal", "ConstructorFragmentInternal", "FallbackFragmentInternal", "FunctionFragmentInternal", "StructFragmentInternal", "name", "baseType", "indexed", "components", "array<PERSON>ength", "array<PERSON><PERSON><PERSON>n", "guard", "defineProperty", "format", "isArray", "parse", "String", "isTuple", "c", "comp", "isIndexable", "walk", "process", "Array", "_this", "v", "walkAsync", "#walkAsync", "promises", "setValue", "childType", "index", "param", "then", "Promise", "all", "obj", "isParamType", "error", "comps", "bracket", "arrayMatch", "startsWith", "tuple", "Fragment", "inputs", "e", "ConstructorFragment", "ErrorFragment", "EventFragment", "FallbackFragment", "FunctionFragment", "StructFragment", "operation", "isConstructor", "isFragment", "isError", "isEvent", "isFunction", "isStruct", "NamedFragment", "joinParams", "params", "p", "selector", "input", "anonymous", "topicHash", "getTopicHash", "fragment", "payable", "gas", "stateMutability", "undefined", "errorObj", "topIsValid", "mutability", "outputs", "constant", "o", "getSelector"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\fragments.ts"], "sourcesContent": ["/**\n *  A fragment is a single item from an ABI, which may represent any of:\n *\n *  - [Functions](FunctionFragment)\n *  - [Events](EventFragment)\n *  - [Constructors](ConstructorFragment)\n *  - Custom [Errors](ErrorFragment)\n *  - [Fallback or Receive](FallbackFragment) functions\n *\n *  @_subsection api/abi/abi-coder:Fragments  [about-fragments]\n */\n\nimport {\n    defineProperties, getBigInt, getNumber,\n    assert, assertPrivate, assertArgument\n} from \"../utils/index.js\";\nimport { id } from \"../hash/index.js\";\n\n/**\n *  A Type description in a [JSON ABI format](link-solc-jsonabi).\n */\nexport interface JsonFragmentType {\n    /**\n     *  The parameter name.\n     */\n    readonly name?: string;\n\n    /**\n     *  If the parameter is indexed.\n     */\n    readonly indexed?: boolean;\n\n    /**\n     *  The type of the parameter.\n     */\n    readonly type?: string;\n\n    /**\n     *  The internal Solidity type.\n     */\n    readonly internalType?: string;\n\n    /**\n     *  The components for a tuple.\n     */\n    readonly components?: ReadonlyArray<JsonFragmentType>;\n}\n\n/**\n *  A fragment for a method, event or error in a [JSON ABI format](link-solc-jsonabi).\n */\nexport interface JsonFragment {\n    /**\n     *  The name of the error, event, function, etc.\n     */\n    readonly name?: string;\n\n    /**\n     *  The type of the fragment (e.g. ``event``, ``\"function\"``, etc.)\n     */\n    readonly type?: string;\n\n    /**\n     *  If the event is anonymous.\n     */\n    readonly anonymous?: boolean;\n\n    /**\n     *  If the function is payable.\n     */\n    readonly payable?: boolean;\n\n    /**\n     *  If the function is constant.\n     */\n    readonly constant?: boolean;\n\n    /**\n     *  The mutability state of the function.\n     */\n    readonly stateMutability?: string;\n\n    /**\n     *  The input parameters.\n     */\n    readonly inputs?: ReadonlyArray<JsonFragmentType>;\n\n    /**\n     *  The output parameters.\n     */\n    readonly outputs?: ReadonlyArray<JsonFragmentType>;\n\n    /**\n     *  The gas limit to use when sending a transaction for this function.\n     */\n    readonly gas?: string;\n};\n\n/**\n *  The format to serialize the output as.\n *\n *  **``\"sighash\"``** - the bare formatting, used to compute the selector\n *  or topic hash; this format cannot be reversed (as it discards ``indexed``)\n *  so cannot by used to export an [[Interface]].\n *\n *  **``\"minimal\"``** - Human-Readable ABI with minimal spacing and without\n *  names, so it is compact, but will result in Result objects that cannot\n *  be accessed by name.\n *\n *  **``\"full\"``** - Full Human-Readable ABI, with readable spacing and names\n *  intact; this is generally the recommended format.\n *\n *  **``\"json\"``** - The [JSON ABI format](link-solc-jsonabi).\n */\nexport type FormatType = \"sighash\" | \"minimal\" | \"full\" | \"json\";\n\n// [ \"a\", \"b\" ] => { \"a\": 1, \"b\": 1 }\nfunction setify(items: Array<string>): ReadonlySet<string> {\n    const result: Set<string> = new Set();\n    items.forEach((k) => result.add(k));\n    return Object.freeze(result);\n}\n\nconst _kwVisibDeploy = \"external public payable override\";\nconst KwVisibDeploy = setify(_kwVisibDeploy.split(\" \"));\n\n// Visibility Keywords\nconst _kwVisib = \"constant external internal payable private public pure view override\";\nconst KwVisib = setify(_kwVisib.split(\" \"));\n\nconst _kwTypes = \"constructor error event fallback function receive struct\";\nconst KwTypes = setify(_kwTypes.split(\" \"));\n\nconst _kwModifiers = \"calldata memory storage payable indexed\";\nconst KwModifiers = setify(_kwModifiers.split(\" \"));\n\nconst _kwOther = \"tuple returns\";\n\n// All Keywords\nconst _keywords = [ _kwTypes, _kwModifiers, _kwOther, _kwVisib ].join(\" \");\nconst Keywords = setify(_keywords.split(\" \"));\n\n// Single character tokens\nconst SimpleTokens: Record<string, string> = {\n  \"(\": \"OPEN_PAREN\", \")\": \"CLOSE_PAREN\",\n  \"[\": \"OPEN_BRACKET\", \"]\": \"CLOSE_BRACKET\",\n  \",\": \"COMMA\", \"@\": \"AT\"\n};\n\n// Parser regexes to consume the next token\nconst regexWhitespacePrefix = new RegExp(\"^(\\\\s*)\");\nconst regexNumberPrefix = new RegExp(\"^([0-9]+)\");\nconst regexIdPrefix = new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)\");\n\n// Parser regexs to check validity\nconst regexId = new RegExp(\"^([a-zA-Z$_][a-zA-Z0-9$_]*)$\");\nconst regexType = new RegExp(\"^(address|bool|bytes([0-9]*)|string|u?int([0-9]*))$\");\n\n/**\n *  @ignore:\n */\ntype Token = Readonly<{\n    // Type of token (e.g. TYPE, KEYWORD, NUMBER, etc)\n    type: string;\n\n    // Offset into the original source code\n    offset: number;\n\n    // Actual text content of the token\n    text: string;\n\n    // The parenthesis depth\n    depth: number;\n\n    // If a parenthesis, the offset (in tokens) that balances it\n    match: number;\n\n    // For parenthesis and commas, the offset (in tokens) to the\n    // previous/next parenthesis or comma in the list\n    linkBack: number;\n    linkNext: number;\n\n    // If a BRACKET, the value inside\n    value: number;\n}>;\n\nclass TokenString {\n    #offset: number;\n    #tokens: ReadonlyArray<Token>;\n\n    get offset(): number { return this.#offset; }\n    get length(): number { return this.#tokens.length - this.#offset; }\n\n    constructor(tokens: ReadonlyArray<Token>) {\n        this.#offset = 0;\n        this.#tokens = tokens.slice();\n    }\n\n    clone(): TokenString { return new TokenString(this.#tokens); }\n    reset(): void { this.#offset = 0; }\n\n    #subTokenString(from: number = 0, to: number = 0): TokenString {\n        return new TokenString(this.#tokens.slice(from, to).map((t) => {\n            return Object.freeze(Object.assign({ }, t, {\n                match: (t.match - from),\n                linkBack: (t.linkBack - from),\n                linkNext: (t.linkNext - from),\n            }));\n        }));\n    }\n\n    // Pops and returns the value of the next token, if it is a keyword in allowed; throws if out of tokens\n    popKeyword(allowed: ReadonlySet<string>): string {\n        const top = this.peek();\n        if (top.type !== \"KEYWORD\" || !allowed.has(top.text)) { throw new Error(`expected keyword ${ top.text }`); }\n        return this.pop().text;\n    }\n\n    // Pops and returns the value of the next token if it is `type`; throws if out of tokens\n    popType(type: string): string {\n        if (this.peek().type !== type) {\n            const top = this.peek();\n            throw new Error(`expected ${ type }; got ${ top.type } ${ JSON.stringify(top.text) }`);\n        }\n        return this.pop().text;\n    }\n\n    // Pops and returns a \"(\" TOKENS \")\"\n    popParen(): TokenString {\n        const top = this.peek();\n        if (top.type !== \"OPEN_PAREN\") { throw new Error(\"bad start\"); }\n        const result = this.#subTokenString(this.#offset + 1, top.match + 1);\n        this.#offset = top.match + 1;\n        return result;\n    }\n\n    // Pops and returns the items within \"(\" ITEM1 \",\" ITEM2 \",\" ... \")\"\n    popParams(): Array<TokenString> {\n        const top = this.peek();\n\n        if (top.type !== \"OPEN_PAREN\") { throw new Error(\"bad start\"); }\n\n        const result: Array<TokenString> = [ ];\n\n        while(this.#offset < top.match - 1) {\n            const link = this.peek().linkNext;\n            result.push(this.#subTokenString(this.#offset + 1, link));\n            this.#offset = link;\n        }\n\n        this.#offset = top.match + 1;\n\n        return result;\n    }\n\n    // Returns the top Token, throwing if out of tokens\n    peek(): Token {\n        if (this.#offset >= this.#tokens.length) {\n            throw new Error(\"out-of-bounds\");\n        }\n        return this.#tokens[this.#offset];\n    }\n\n    // Returns the next value, if it is a keyword in `allowed`\n    peekKeyword(allowed: ReadonlySet<string>): null | string {\n        const top = this.peekType(\"KEYWORD\");\n        return (top != null && allowed.has(top)) ? top: null;\n    }\n\n    // Returns the value of the next token if it is `type`\n    peekType(type: string): null | string {\n        if (this.length === 0) { return null; }\n        const top = this.peek();\n        return (top.type === type) ? top.text: null;\n    }\n\n    // Returns the next token; throws if out of tokens\n    pop(): Token {\n        const result = this.peek();\n        this.#offset++;\n        return result;\n    }\n\n    toString(): string {\n        const tokens: Array<string> = [ ];\n        for (let i = this.#offset; i < this.#tokens.length; i++) {\n            const token = this.#tokens[i];\n            tokens.push(`${ token.type }:${ token.text }`);\n        }\n        return `<TokenString ${ tokens.join(\" \") }>`\n    }\n}\n\ntype Writeable<T> = { -readonly [P in keyof T]: T[P] };\n\nfunction lex(text: string): TokenString {\n    const tokens: Array<Token> = [ ];\n\n    const throwError = (message: string) => {\n        const token = (offset < text.length) ? JSON.stringify(text[offset]): \"$EOI\";\n        throw new Error(`invalid token ${ token } at ${ offset }: ${ message }`);\n    };\n\n    let brackets: Array<number> = [ ];\n    let commas: Array<number> = [ ];\n\n    let offset = 0;\n    while (offset < text.length) {\n\n        // Strip off any leading whitespace\n        let cur = text.substring(offset);\n        let match = cur.match(regexWhitespacePrefix);\n        if (match) {\n            offset += match[1].length;\n            cur = text.substring(offset);\n        }\n\n        const token = { depth: brackets.length, linkBack: -1, linkNext: -1, match: -1, type: \"\", text: \"\", offset, value: -1 };\n        tokens.push(token);\n\n        let type = (SimpleTokens[cur[0]] || \"\");\n        if (type) {\n            token.type = type;\n            token.text = cur[0];\n            offset++;\n\n            if (type === \"OPEN_PAREN\") {\n                brackets.push(tokens.length - 1);\n                commas.push(tokens.length - 1);\n\n            } else if (type == \"CLOSE_PAREN\") {\n                if (brackets.length === 0) { throwError(\"no matching open bracket\"); }\n\n                token.match = brackets.pop() as number;\n                (<Writeable<Token>>(tokens[token.match])).match = tokens.length - 1;\n                token.depth--;\n\n                token.linkBack = commas.pop() as number;\n                (<Writeable<Token>>(tokens[token.linkBack])).linkNext = tokens.length - 1;\n\n            } else if (type === \"COMMA\") {\n                token.linkBack = commas.pop() as number;\n                (<Writeable<Token>>(tokens[token.linkBack])).linkNext = tokens.length - 1;\n                commas.push(tokens.length - 1);\n\n            } else if (type === \"OPEN_BRACKET\") {\n                token.type = \"BRACKET\";\n\n            } else if (type === \"CLOSE_BRACKET\") {\n                // Remove the CLOSE_BRACKET\n                let suffix = (tokens.pop() as Token).text;\n                if (tokens.length > 0 && tokens[tokens.length - 1].type === \"NUMBER\") {\n                    const value = (tokens.pop() as Token).text;\n                    suffix = value + suffix;\n                    (<Writeable<Token>>(tokens[tokens.length - 1])).value = getNumber(value);\n                }\n                if (tokens.length === 0 || tokens[tokens.length - 1].type !== \"BRACKET\") {\n                    throw new Error(\"missing opening bracket\");\n                }\n                (<Writeable<Token>>(tokens[tokens.length - 1])).text += suffix;\n            }\n\n            continue;\n        }\n\n        match = cur.match(regexIdPrefix);\n        if (match) {\n            token.text = match[1];\n            offset += token.text.length;\n\n            if (Keywords.has(token.text)) {\n                token.type = \"KEYWORD\";\n                continue;\n            }\n\n            if (token.text.match(regexType)) {\n                token.type = \"TYPE\";\n                continue;\n            }\n\n            token.type = \"ID\";\n            continue;\n        }\n\n        match = cur.match(regexNumberPrefix);\n        if (match) {\n            token.text = match[1];\n            token.type = \"NUMBER\";\n            offset += token.text.length;\n            continue;\n        }\n\n        throw new Error(`unexpected token ${ JSON.stringify(cur[0]) } at position ${ offset }`);\n    }\n\n    return new TokenString(tokens.map((t) => Object.freeze(t)));\n}\n\n// Check only one of `allowed` is in `set`\nfunction allowSingle(set: ReadonlySet<string>, allowed: ReadonlySet<string>): void {\n    let included: Array<string> = [ ];\n    for (const key in allowed.keys()) {\n        if (set.has(key)) { included.push(key); }\n    }\n    if (included.length > 1) { throw new Error(`conflicting types: ${ included.join(\", \") }`); }\n}\n\n// Functions to process a Solidity Signature TokenString from left-to-right for...\n\n// ...the name with an optional type, returning the name\nfunction consumeName(type: string, tokens: TokenString): string {\n    if (tokens.peekKeyword(KwTypes)) {\n        const keyword = tokens.pop().text;\n        if (keyword !== type) {\n            throw new Error(`expected ${ type }, got ${ keyword }`);\n        }\n    }\n\n    return tokens.popType(\"ID\");\n}\n\n// ...all keywords matching allowed, returning the keywords\nfunction consumeKeywords(tokens: TokenString, allowed?: ReadonlySet<string>): ReadonlySet<string> {\n    const keywords: Set<string> = new Set();\n    while (true) {\n        const keyword = tokens.peekType(\"KEYWORD\");\n\n        if (keyword == null || (allowed && !allowed.has(keyword))) { break; }\n        tokens.pop();\n\n        if (keywords.has(keyword)) { throw new Error(`duplicate keywords: ${ JSON.stringify(keyword) }`); }\n        keywords.add(keyword);\n    }\n\n    return Object.freeze(keywords);\n}\n\n// ...all visibility keywords, returning the coalesced mutability\nfunction consumeMutability(tokens: TokenString): \"payable\" | \"nonpayable\" | \"view\" | \"pure\" {\n    let modifiers = consumeKeywords(tokens, KwVisib);\n\n    // Detect conflicting modifiers\n    allowSingle(modifiers, setify(\"constant payable nonpayable\".split(\" \")));\n    allowSingle(modifiers, setify(\"pure view payable nonpayable\".split(\" \")));\n\n    // Process mutability states\n    if (modifiers.has(\"view\")) { return \"view\"; }\n    if (modifiers.has(\"pure\")) { return \"pure\"; }\n    if (modifiers.has(\"payable\")) { return \"payable\"; }\n    if (modifiers.has(\"nonpayable\")) { return \"nonpayable\"; }\n\n    // Process legacy `constant` last\n    if (modifiers.has(\"constant\")) { return \"view\"; }\n\n    return \"nonpayable\";\n}\n\n// ...a parameter list, returning the ParamType list\nfunction consumeParams(tokens: TokenString, allowIndexed?: boolean): Array<ParamType> {\n    return tokens.popParams().map((t) => ParamType.from(t, allowIndexed));\n}\n\n// ...a gas limit, returning a BigNumber or null if none\nfunction consumeGas(tokens: TokenString): null | bigint {\n    if (tokens.peekType(\"AT\")) {\n        tokens.pop();\n        if (tokens.peekType(\"NUMBER\")) {\n            return getBigInt(tokens.pop().text);\n        }\n        throw new Error(\"invalid gas\");\n    }\n    return null;\n}\n\nfunction consumeEoi(tokens: TokenString): void {\n    if (tokens.length) {\n        throw new Error(`unexpected tokens at offset ${ tokens.offset }: ${ tokens.toString() }`);\n    }\n}\n\nconst regexArrayType = new RegExp(/^(.*)\\[([0-9]*)\\]$/);\n\nfunction verifyBasicType(type: string): string {\n    const match = type.match(regexType);\n    assertArgument(match, \"invalid type\", \"type\", type);\n    if (type === \"uint\") { return \"uint256\"; }\n    if (type === \"int\") { return \"int256\"; }\n\n    if (match[2]) {\n        // bytesXX\n        const length = parseInt(match[2]);\n        assertArgument(length !== 0 && length <= 32, \"invalid bytes length\", \"type\", type);\n\n    } else if (match[3]) {\n        // intXX or uintXX\n        const size = parseInt(match[3] as string);\n        assertArgument(size !== 0 && size <= 256 && (size % 8) === 0, \"invalid numeric width\", \"type\", type);\n    }\n\n    return type;\n}\n\n// Make the Fragment constructors effectively private\nconst _guard = { };\n\n\n/**\n *  When [walking](ParamType-walk) a [[ParamType]], this is called\n *  on each component.\n */\nexport type ParamTypeWalkFunc = (type: string, value: any) => any;\n\n/**\n *  When [walking asynchronously](ParamType-walkAsync) a [[ParamType]],\n *  this is called on each component.\n */\nexport type ParamTypeWalkAsyncFunc = (type: string, value: any) => any | Promise<any>;\n\nconst internal = Symbol.for(\"_ethers_internal\");\n\nconst ParamTypeInternal = \"_ParamTypeInternal\";\nconst ErrorFragmentInternal = \"_ErrorInternal\";\nconst EventFragmentInternal = \"_EventInternal\";\nconst ConstructorFragmentInternal = \"_ConstructorInternal\";\nconst FallbackFragmentInternal = \"_FallbackInternal\";\nconst FunctionFragmentInternal = \"_FunctionInternal\";\nconst StructFragmentInternal = \"_StructInternal\";\n\n/**\n *  Each input and output of a [[Fragment]] is an Array of **ParamType**.\n */\nexport class ParamType {\n\n    /**\n     *  The local name of the parameter (or ``\"\"`` if unbound)\n     */\n    readonly name!: string;\n\n    /**\n     *  The fully qualified type (e.g. ``\"address\"``, ``\"tuple(address)\"``,\n     *  ``\"uint256[3][]\"``)\n     */\n    readonly type!: string;\n\n    /**\n     *  The base type (e.g. ``\"address\"``, ``\"tuple\"``, ``\"array\"``)\n     */\n    readonly baseType!: string;\n\n    /**\n     *  True if the parameters is indexed.\n     *\n     *  For non-indexable types this is ``null``.\n     */\n    readonly indexed!: null | boolean;\n\n    /**\n     *  The components for the tuple.\n     *\n     *  For non-tuple types this is ``null``.\n     */\n    readonly components!: null | ReadonlyArray<ParamType>;\n\n    /**\n     *  The array length, or ``-1`` for dynamic-lengthed arrays.\n     *\n     *  For non-array types this is ``null``.\n     */\n    readonly arrayLength!: null | number;\n\n    /**\n     *  The type of each child in the array.\n     *\n     *  For non-array types this is ``null``.\n     */\n    readonly arrayChildren!: null | ParamType;\n\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, name: string, type: string, baseType: string, indexed: null | boolean, components: null | ReadonlyArray<ParamType>, arrayLength: null | number, arrayChildren: null | ParamType) {\n        assertPrivate(guard, _guard, \"ParamType\");\n        Object.defineProperty(this, internal, { value: ParamTypeInternal });\n\n        if (components) { components = Object.freeze(components.slice()); }\n\n        if (baseType === \"array\") {\n            if (arrayLength == null || arrayChildren == null) {\n                throw new Error(\"\");\n            }\n        } else if (arrayLength != null || arrayChildren != null) {\n            throw new Error(\"\");\n        }\n\n        if (baseType === \"tuple\") {\n            if (components == null) { throw new Error(\"\"); }\n        } else if (components != null) {\n            throw new Error(\"\");\n        }\n\n        defineProperties<ParamType>(this, {\n            name, type, baseType, indexed, components, arrayLength, arrayChildren\n        });\n    }\n\n    /**\n     *  Return a string representation of this type.\n     *\n     *  For example,\n     *\n     *  ``sighash\" => \"(uint256,address)\"``\n     *\n     *  ``\"minimal\" => \"tuple(uint256,address) indexed\"``\n     *\n     *  ``\"full\" => \"tuple(uint256 foo, address bar) indexed baz\"``\n     */\n    format(format?: FormatType): string {\n        if (format == null) { format = \"sighash\"; }\n        if (format === \"json\") {\n            const name = this.name || \"\";\n\n            if (this.isArray()) {\n                const result = JSON.parse(this.arrayChildren.format(\"json\"));\n                result.name = name;\n                result.type += `[${ (this.arrayLength < 0 ? \"\": String(this.arrayLength)) }]`;\n                return JSON.stringify(result);\n            }\n\n            const result: any = {\n                type: ((this.baseType === \"tuple\") ? \"tuple\": this.type),\n                name\n            };\n\n\n            if (typeof(this.indexed) === \"boolean\") { result.indexed = this.indexed; }\n            if (this.isTuple()) {\n                result.components = this.components.map((c) => JSON.parse(c.format(format)));\n            }\n            return JSON.stringify(result);\n        }\n\n        let result = \"\";\n\n        // Array\n        if (this.isArray()) {\n            result += this.arrayChildren.format(format);\n            result += `[${ (this.arrayLength < 0 ? \"\": String(this.arrayLength)) }]`;\n        } else {\n            if (this.isTuple()) {\n                result += \"(\" + this.components.map(\n                    (comp) => comp.format(format)\n                ).join((format === \"full\") ? \", \": \",\") + \")\";\n            } else {\n                result += this.type;\n            }\n        }\n\n        if (format !== \"sighash\") {\n            if (this.indexed === true) { result += \" indexed\"; }\n            if (format === \"full\" && this.name) {\n                result += \" \" + this.name;\n            }\n        }\n\n        return result;\n    }\n\n    /**\n     *  Returns true if %%this%% is an Array type.\n     *\n     *  This provides a type gaurd ensuring that [[arrayChildren]]\n     *  and [[arrayLength]] are non-null.\n     */\n    isArray(): this is (ParamType & { arrayChildren: ParamType, arrayLength: number }) {\n        return (this.baseType === \"array\")\n    }\n\n    /**\n     *  Returns true if %%this%% is a Tuple type.\n     *\n     *  This provides a type gaurd ensuring that [[components]]\n     *  is non-null.\n     */\n    isTuple(): this is (ParamType & { components: ReadonlyArray<ParamType> }) {\n        return (this.baseType === \"tuple\");\n    }\n\n    /**\n     *  Returns true if %%this%% is an Indexable type.\n     *\n     *  This provides a type gaurd ensuring that [[indexed]]\n     *  is non-null.\n     */\n    isIndexable(): this is (ParamType & { indexed: boolean }) {\n        return (this.indexed != null);\n    }\n\n    /**\n     *  Walks the **ParamType** with %%value%%, calling %%process%%\n     *  on each type, destructing the %%value%% recursively.\n     */\n    walk(value: any, process: ParamTypeWalkFunc): any {\n        if (this.isArray()) {\n            if (!Array.isArray(value)) { throw new Error(\"invalid array value\"); }\n            if (this.arrayLength !== -1 && value.length !== this.arrayLength) {\n                throw new Error(\"array is wrong length\");\n            }\n            const _this = this;\n            return value.map((v) => (_this.arrayChildren.walk(v, process)));\n        }\n\n        if (this.isTuple()) {\n            if (!Array.isArray(value)) { throw new Error(\"invalid tuple value\"); }\n            if (value.length !== this.components.length) {\n                throw new Error(\"array is wrong length\");\n            }\n            const _this = this;\n            return value.map((v, i) => (_this.components[i].walk(v, process)));\n        }\n\n        return process(this.type, value);\n    }\n\n    #walkAsync(promises: Array<Promise<void>>, value: any, process: ParamTypeWalkAsyncFunc, setValue: (value: any) => void): void {\n\n        if (this.isArray()) {\n            if (!Array.isArray(value)) { throw new Error(\"invalid array value\"); }\n            if (this.arrayLength !== -1 && value.length !== this.arrayLength) {\n                throw new Error(\"array is wrong length\");\n            }\n            const childType = this.arrayChildren;\n\n            const result = value.slice();\n            result.forEach((value, index) => {\n                childType.#walkAsync(promises, value, process, (value: any) => {\n                    result[index] = value;\n                });\n            });\n            setValue(result);\n            return;\n        }\n\n        if (this.isTuple()) {\n            const components = this.components;\n\n            // Convert the object into an array\n            let result: Array<any>;\n            if (Array.isArray(value)) {\n                result = value.slice();\n\n            } else {\n                if (value == null || typeof(value) !== \"object\") {\n                    throw new Error(\"invalid tuple value\");\n                }\n\n                result = components.map((param) => {\n                    if (!param.name) { throw new Error(\"cannot use object value with unnamed components\"); }\n                    if (!(param.name in value)) {\n                        throw new Error(`missing value for component ${ param.name }`);\n                    }\n                    return value[param.name];\n                });\n            }\n\n            if (result.length !== this.components.length) {\n                throw new Error(\"array is wrong length\");\n            }\n\n            result.forEach((value, index) => {\n                components[index].#walkAsync(promises, value, process, (value: any) => {\n                    result[index] = value;\n                });\n            });\n            setValue(result);\n            return;\n        }\n\n        const result = process(this.type, value);\n        if (result.then) {\n            promises.push((async function() { setValue(await result); })());\n        } else {\n            setValue(result);\n        }\n    }\n\n    /**\n     *  Walks the **ParamType** with %%value%%, asynchronously calling\n     *  %%process%% on each type, destructing the %%value%% recursively.\n     *\n     *  This can be used to resolve ENS names by walking and resolving each\n     *  ``\"address\"`` type.\n     */\n    async walkAsync(value: any, process: ParamTypeWalkAsyncFunc): Promise<any> {\n        const promises: Array<Promise<void>> = [ ];\n        const result: [ any ] = [ value ];\n        this.#walkAsync(promises, value, process, (value: any) => {\n            result[0] = value;\n        });\n        if (promises.length) { await Promise.all(promises); }\n        return result[0];\n    }\n\n    /**\n     *  Creates a new **ParamType** for %%obj%%.\n     *\n     *  If %%allowIndexed%% then the ``indexed`` keyword is permitted,\n     *  otherwise the ``indexed`` keyword will throw an error.\n     */\n    static from(obj: any, allowIndexed?: boolean): ParamType {\n        if (ParamType.isParamType(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            try {\n                return ParamType.from(lex(obj), allowIndexed);\n            } catch (error) {\n                assertArgument(false, \"invalid param type\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            let type = \"\", baseType = \"\";\n            let comps: null | Array<ParamType> = null;\n\n            if (consumeKeywords(obj, setify([ \"tuple\" ])).has(\"tuple\") || obj.peekType(\"OPEN_PAREN\")) {\n                // Tuple\n                baseType = \"tuple\";\n                comps = obj.popParams().map((t) => ParamType.from(t));\n                type = `tuple(${ comps.map((c) => c.format()).join(\",\") })`;\n            } else {\n                // Normal\n                type = verifyBasicType(obj.popType(\"TYPE\"));\n                baseType = type;\n            }\n\n            // Check for Array\n            let arrayChildren: null | ParamType  = null;\n            let arrayLength: null | number = null;\n\n            while (obj.length && obj.peekType(\"BRACKET\")) {\n                const bracket = obj.pop(); //arrays[i];\n                arrayChildren = new ParamType(_guard, \"\", type, baseType, null, comps, arrayLength, arrayChildren);\n                arrayLength = bracket.value;\n                type += bracket.text;\n                baseType = \"array\";\n                comps = null;\n            }\n\n            let indexed: null | boolean = null;\n            const keywords = consumeKeywords(obj, KwModifiers);\n            if (keywords.has(\"indexed\")) {\n                if (!allowIndexed) { throw new Error(\"\"); }\n                indexed = true;\n            }\n\n            const name = (obj.peekType(\"ID\") ? obj.pop().text: \"\");\n\n            if (obj.length) { throw new Error(\"leftover tokens\"); }\n\n            return new ParamType(_guard, name, type, baseType, indexed, comps, arrayLength, arrayChildren);\n        }\n\n        const name = obj.name;\n        assertArgument(!name || (typeof(name) === \"string\" && name.match(regexId)),\n            \"invalid name\", \"obj.name\", name);\n\n        let indexed = obj.indexed;\n        if (indexed != null) {\n            assertArgument(allowIndexed, \"parameter cannot be indexed\", \"obj.indexed\", obj.indexed);\n            indexed = !!indexed;\n        }\n\n        let type = obj.type;\n\n        let arrayMatch = type.match(regexArrayType);\n        if (arrayMatch) {\n            const arrayLength = parseInt(arrayMatch[2] || \"-1\");\n            const arrayChildren = ParamType.from({\n                type: arrayMatch[1],\n                components: obj.components\n            });\n\n            return new ParamType(_guard, name || \"\", type, \"array\", indexed, null, arrayLength, arrayChildren);\n        }\n\n        if (type === \"tuple\" || type.startsWith(\"tuple(\"/* fix: ) */) || type.startsWith(\"(\" /* fix: ) */)) {\n            const comps = (obj.components != null) ? obj.components.map((c: any) => ParamType.from(c)): null;\n            const tuple = new ParamType(_guard, name || \"\", type, \"tuple\", indexed, comps, null, null);\n            // @TODO: use lexer to validate and normalize type\n            return tuple;\n        }\n\n        type = verifyBasicType(obj.type);\n\n        return new ParamType(_guard, name || \"\", type, type, indexed, null, null, null);\n    }\n\n    /**\n     *  Returns true if %%value%% is a **ParamType**.\n     */\n    static isParamType(value: any): value is ParamType {\n        return (value && value[internal] === ParamTypeInternal);\n    }\n}\n\n/**\n *  The type of a [[Fragment]].\n */\nexport type FragmentType = \"constructor\" | \"error\" | \"event\" | \"fallback\" | \"function\" | \"struct\";\n\n/**\n *  An abstract class to represent An individual fragment from a parse ABI.\n */\nexport abstract class Fragment {\n    /**\n     *  The type of the fragment.\n     */\n    readonly type!: FragmentType;\n\n    /**\n     *  The inputs for the fragment.\n     */\n    readonly inputs!: ReadonlyArray<ParamType>;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, type: FragmentType, inputs: ReadonlyArray<ParamType>) {\n        assertPrivate(guard, _guard, \"Fragment\");\n        inputs = Object.freeze(inputs.slice());\n        defineProperties<Fragment>(this, { type, inputs });\n    }\n\n    /**\n     *  Returns a string representation of this fragment as %%format%%.\n     */\n    abstract format(format?: FormatType): string;\n\n    /**\n     *  Creates a new **Fragment** for %%obj%%, wich can be any supported\n     *  ABI frgament type.\n     */\n    static from(obj: any): Fragment {\n        if (typeof(obj) === \"string\") {\n\n            // Try parsing JSON...\n            try {\n                Fragment.from(JSON.parse(obj));\n            } catch (e) { }\n\n            // ...otherwise, use the human-readable lexer\n            return Fragment.from(lex(obj));\n        }\n\n        if (obj instanceof TokenString) {\n            // Human-readable ABI (already lexed)\n\n            const type = obj.peekKeyword(KwTypes);\n\n            switch (type) {\n                case \"constructor\": return ConstructorFragment.from(obj);\n                case \"error\": return ErrorFragment.from(obj);\n                case \"event\": return EventFragment.from(obj);\n                case \"fallback\": case \"receive\":\n                    return FallbackFragment.from(obj);\n                case \"function\": return FunctionFragment.from(obj);\n                case \"struct\": return StructFragment.from(obj);\n            }\n\n        } else if (typeof(obj) === \"object\") {\n            // JSON ABI\n\n            switch (obj.type) {\n                case \"constructor\": return ConstructorFragment.from(obj);\n                case \"error\": return ErrorFragment.from(obj);\n                case \"event\": return EventFragment.from(obj);\n                case \"fallback\": case \"receive\":\n                    return FallbackFragment.from(obj);\n                case \"function\": return FunctionFragment.from(obj);\n                case \"struct\": return StructFragment.from(obj);\n            }\n\n            assert(false, `unsupported type: ${ obj.type }`, \"UNSUPPORTED_OPERATION\", {\n                operation: \"Fragment.from\"\n            });\n        }\n\n        assertArgument(false, \"unsupported frgament object\", \"obj\", obj);\n    }\n\n    /**\n     *  Returns true if %%value%% is a [[ConstructorFragment]].\n     */\n    static isConstructor(value: any): value is ConstructorFragment {\n        return ConstructorFragment.isFragment(value);\n    }\n\n    /**\n     *  Returns true if %%value%% is an [[ErrorFragment]].\n     */\n    static isError(value: any): value is ErrorFragment {\n        return ErrorFragment.isFragment(value);\n    }\n\n    /**\n     *  Returns true if %%value%% is an [[EventFragment]].\n     */\n    static isEvent(value: any): value is EventFragment {\n        return EventFragment.isFragment(value);\n    }\n\n    /**\n     *  Returns true if %%value%% is a [[FunctionFragment]].\n     */\n    static isFunction(value: any): value is FunctionFragment {\n        return FunctionFragment.isFragment(value);\n    }\n\n    /**\n     *  Returns true if %%value%% is a [[StructFragment]].\n     */\n    static isStruct(value: any): value is StructFragment {\n        return StructFragment.isFragment(value);\n    }\n}\n\n/**\n *  An abstract class to represent An individual fragment\n *  which has a name from a parse ABI.\n */\nexport abstract class NamedFragment extends Fragment {\n    /**\n     *  The name of the fragment.\n     */\n    readonly name!: string;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, type: FragmentType, name: string, inputs: ReadonlyArray<ParamType>) {\n        super(guard, type, inputs);\n        assertArgument(typeof(name) === \"string\" && name.match(regexId),\n            \"invalid identifier\", \"name\", name);\n        inputs = Object.freeze(inputs.slice());\n        defineProperties<NamedFragment>(this, { name });\n    }\n}\n\nfunction joinParams(format: FormatType, params: ReadonlyArray<ParamType>): string { \n    return \"(\" + params.map((p) => p.format(format)).join((format === \"full\") ? \", \": \",\") + \")\";\n}\n\n/**\n *  A Fragment which represents a //Custom Error//.\n */\nexport class ErrorFragment extends NamedFragment {\n    /**\n     *  @private\n     */\n    constructor(guard: any, name: string, inputs: ReadonlyArray<ParamType>) {\n        super(guard, \"error\", name, inputs);\n        Object.defineProperty(this, internal, { value: ErrorFragmentInternal });\n    }\n\n    /**\n     *  The Custom Error selector.\n     */\n    get selector(): string {\n        return id(this.format(\"sighash\")).substring(0, 10);\n    }\n\n    /**\n     *  Returns a string representation of this fragment as %%format%%.\n     */\n    format(format?: FormatType): string {\n        if (format == null) { format = \"sighash\"; }\n        if (format === \"json\") {\n            return JSON.stringify({\n                type: \"error\",\n                name: this.name,\n                inputs: this.inputs.map((input) => JSON.parse(input.format(format))),\n            });\n        }\n\n        const result: Array<string> = [ ];\n        if (format !== \"sighash\") { result.push(\"error\"); }\n        result.push(this.name + joinParams(format, this.inputs));\n        return result.join(\" \");\n    }\n\n    /**\n     *  Returns a new **ErrorFragment** for %%obj%%.\n     */\n    static from(obj: any): ErrorFragment {\n        if (ErrorFragment.isFragment(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            return ErrorFragment.from(lex(obj));\n\n        } else if (obj instanceof TokenString) {\n            const name = consumeName(\"error\", obj);\n            const inputs = consumeParams(obj);\n            consumeEoi(obj);\n\n            return new ErrorFragment(_guard, name, inputs);\n        }\n\n        return new ErrorFragment(_guard, obj.name,\n            obj.inputs ? obj.inputs.map(ParamType.from): [ ]);\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is an\n     *  **ErrorFragment**.\n     */\n    static isFragment(value: any): value is ErrorFragment {\n        return (value && value[internal] === ErrorFragmentInternal);\n    }\n}\n\n/**\n *  A Fragment which represents an Event.\n */\nexport class EventFragment extends NamedFragment {\n    /**\n     *  Whether this event is anonymous.\n     */\n    readonly anonymous!: boolean;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, name: string, inputs: ReadonlyArray<ParamType>, anonymous: boolean) {\n        super(guard, \"event\", name, inputs);\n        Object.defineProperty(this, internal, { value: EventFragmentInternal });\n        defineProperties<EventFragment>(this, { anonymous });\n    }\n\n    /**\n     *  The Event topic hash.\n     */\n    get topicHash(): string {\n        return id(this.format(\"sighash\"));\n    }\n\n    /**\n     *  Returns a string representation of this event as %%format%%.\n     */\n    format(format?: FormatType): string {\n        if (format == null) { format = \"sighash\"; }\n        if (format === \"json\") {\n            return JSON.stringify({\n                type: \"event\",\n                anonymous: this.anonymous,\n                name: this.name,\n                inputs: this.inputs.map((i) => JSON.parse(i.format(format)))\n            });\n        }\n\n        const result: Array<string> = [ ];\n        if (format !== \"sighash\") { result.push(\"event\"); }\n        result.push(this.name + joinParams(format, this.inputs));\n        if (format !== \"sighash\" && this.anonymous) { result.push(\"anonymous\"); }\n        return result.join(\" \");\n    }\n\n    /**\n     *  Return the topic hash for an event with %%name%% and %%params%%.\n     */\n    static getTopicHash(name: string, params?: Array<any>): string {\n        params = (params || []).map((p) => ParamType.from(p));\n        const fragment = new EventFragment(_guard, name, params, false);\n        return fragment.topicHash;\n    }\n\n    /**\n     *  Returns a new **EventFragment** for %%obj%%.\n     */\n    static from(obj: any): EventFragment {\n        if (EventFragment.isFragment(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            try {\n                return EventFragment.from(lex(obj));\n            } catch (error) {\n                assertArgument(false, \"invalid event fragment\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            const name = consumeName(\"event\", obj);\n            const inputs = consumeParams(obj, true);\n            const anonymous = !!consumeKeywords(obj, setify([ \"anonymous\" ])).has(\"anonymous\");\n            consumeEoi(obj);\n\n            return new EventFragment(_guard, name, inputs, anonymous);\n        }\n\n        return new EventFragment(_guard, obj.name,\n            obj.inputs ? obj.inputs.map((p: any) => ParamType.from(p, true)): [ ], !!obj.anonymous);\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is an\n     *  **EventFragment**.\n     */\n    static isFragment(value: any): value is EventFragment {\n        return (value && value[internal] === EventFragmentInternal);\n    }\n}\n\n/**\n *  A Fragment which represents a constructor.\n */\nexport class ConstructorFragment extends Fragment {\n\n    /**\n     *  Whether the constructor can receive an endowment.\n     */\n    readonly payable!: boolean;\n\n    /**\n     *  The recommended gas limit for deployment or ``null``.\n     */\n    readonly gas!: null | bigint;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, type: FragmentType, inputs: ReadonlyArray<ParamType>, payable: boolean, gas: null | bigint) {\n        super(guard, type, inputs);\n        Object.defineProperty(this, internal, { value: ConstructorFragmentInternal });\n        defineProperties<ConstructorFragment>(this, { payable, gas });\n    }\n\n    /**\n     *  Returns a string representation of this constructor as %%format%%.\n     */\n    format(format?: FormatType): string {\n        assert(format != null && format !== \"sighash\", \"cannot format a constructor for sighash\",\n            \"UNSUPPORTED_OPERATION\", { operation: \"format(sighash)\" });\n\n        if (format === \"json\") {\n            return JSON.stringify({\n                type: \"constructor\",\n                stateMutability: (this.payable ? \"payable\": \"undefined\"),\n                payable: this.payable,\n                gas: ((this.gas != null) ? this.gas: undefined),\n                inputs: this.inputs.map((i) => JSON.parse(i.format(format)))\n            });\n        }\n\n        const result = [ `constructor${ joinParams(format, this.inputs) }` ];\n        if (this.payable) { result.push(\"payable\"); }\n        if (this.gas != null) { result.push(`@${ this.gas.toString() }`); }\n        return result.join(\" \");\n    }\n\n    /**\n     *  Returns a new **ConstructorFragment** for %%obj%%.\n     */\n    static from(obj: any): ConstructorFragment {\n        if (ConstructorFragment.isFragment(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            try {\n                return ConstructorFragment.from(lex(obj));\n            } catch (error) {\n                assertArgument(false, \"invalid constuctor fragment\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            consumeKeywords(obj, setify([ \"constructor\" ]));\n            const inputs = consumeParams(obj);\n            const payable = !!consumeKeywords(obj, KwVisibDeploy).has(\"payable\");\n            const gas = consumeGas(obj);\n            consumeEoi(obj);\n\n            return new ConstructorFragment(_guard, \"constructor\", inputs, payable, gas);\n        }\n\n        return new ConstructorFragment(_guard, \"constructor\",\n            obj.inputs ? obj.inputs.map(ParamType.from): [ ],\n            !!obj.payable, (obj.gas != null) ? obj.gas: null);\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is a\n     *  **ConstructorFragment**.\n     */\n    static isFragment(value: any): value is ConstructorFragment {\n        return (value && value[internal] === ConstructorFragmentInternal);\n    }\n}\n\n/**\n *  A Fragment which represents a method.\n */\nexport class FallbackFragment extends Fragment {\n\n    /**\n     *  If the function can be sent value during invocation.\n     */\n    readonly payable!: boolean;\n\n    constructor(guard: any, inputs: ReadonlyArray<ParamType>, payable: boolean) {\n        super(guard, \"fallback\", inputs);\n        Object.defineProperty(this, internal, { value: FallbackFragmentInternal });\n        defineProperties<FallbackFragment>(this, { payable });\n    }\n\n    /**\n     *  Returns a string representation of this fallback as %%format%%.\n     */\n    format(format?: FormatType): string {\n        const type = ((this.inputs.length === 0) ? \"receive\": \"fallback\");\n\n        if (format === \"json\") {\n            const stateMutability = (this.payable ? \"payable\": \"nonpayable\");\n            return JSON.stringify({ type, stateMutability });\n        }\n\n        return `${ type }()${ this.payable ? \" payable\": \"\" }`;\n    }\n\n    /**\n     *  Returns a new **FallbackFragment** for %%obj%%.\n     */\n    static from(obj: any): FallbackFragment {\n        if (FallbackFragment.isFragment(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            try {\n                return FallbackFragment.from(lex(obj));\n            } catch (error) {\n                assertArgument(false, \"invalid fallback fragment\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            const errorObj = obj.toString();\n\n            const topIsValid = obj.peekKeyword(setify([ \"fallback\", \"receive\" ]));\n            assertArgument(topIsValid, \"type must be fallback or receive\", \"obj\", errorObj);\n\n            const type = obj.popKeyword(setify([ \"fallback\", \"receive\" ]));\n\n            // receive()\n            if (type === \"receive\") {\n                const inputs = consumeParams(obj);\n                assertArgument(inputs.length === 0, `receive cannot have arguments`, \"obj.inputs\", inputs);\n                consumeKeywords(obj, setify([ \"payable\" ]));\n                consumeEoi(obj);\n                return new FallbackFragment(_guard, [ ], true);\n            }\n\n            // fallback() [payable]\n            // fallback(bytes) [payable] returns (bytes)\n            let inputs = consumeParams(obj);\n            if (inputs.length) {\n                assertArgument(inputs.length === 1 && inputs[0].type === \"bytes\",\n                    \"invalid fallback inputs\", \"obj.inputs\",\n                    inputs.map((i) => i.format(\"minimal\")).join(\", \"));\n            } else {\n                inputs = [ ParamType.from(\"bytes\") ];\n            }\n\n            const mutability = consumeMutability(obj);\n            assertArgument(mutability === \"nonpayable\" || mutability === \"payable\", \"fallback cannot be constants\", \"obj.stateMutability\", mutability);\n\n            if (consumeKeywords(obj, setify([ \"returns\" ])).has(\"returns\")) {\n                const outputs = consumeParams(obj);\n                assertArgument(outputs.length === 1 && outputs[0].type === \"bytes\",\n                    \"invalid fallback outputs\", \"obj.outputs\",\n                    outputs.map((i) => i.format(\"minimal\")).join(\", \"));\n            }\n\n            consumeEoi(obj);\n\n            return new FallbackFragment(_guard, inputs, mutability === \"payable\");\n        }\n\n        if (obj.type === \"receive\") {\n            return new FallbackFragment(_guard, [ ], true);\n        }\n\n        if (obj.type === \"fallback\") {\n            const inputs = [ ParamType.from(\"bytes\") ];\n            const payable = (obj.stateMutability === \"payable\");\n            return new FallbackFragment(_guard, inputs, payable);\n        }\n\n        assertArgument(false, \"invalid fallback description\", \"obj\", obj);\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is a\n     *  **FallbackFragment**.\n     */\n    static isFragment(value: any): value is FallbackFragment {\n        return (value && value[internal] === FallbackFragmentInternal);\n    }\n}\n\n\n/**\n *  A Fragment which represents a method.\n */\nexport class FunctionFragment extends NamedFragment {\n    /**\n     *  If the function is constant (e.g. ``pure`` or ``view`` functions).\n     */\n    readonly constant!: boolean;\n\n    /**\n     *  The returned types for the result of calling this function.\n     */\n    readonly outputs!: ReadonlyArray<ParamType>;\n\n    /**\n     *  The state mutability (e.g. ``payable``, ``nonpayable``, ``view``\n     *  or ``pure``)\n     */\n    readonly stateMutability!: \"payable\" | \"nonpayable\" | \"view\" | \"pure\";\n\n    /**\n     *  If the function can be sent value during invocation.\n     */\n    readonly payable!: boolean;\n\n    /**\n     *  The recommended gas limit to send when calling this function.\n     */\n    readonly gas!: null | bigint;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, name: string, stateMutability: \"payable\" | \"nonpayable\" | \"view\" | \"pure\", inputs: ReadonlyArray<ParamType>, outputs: ReadonlyArray<ParamType>, gas: null | bigint) {\n        super(guard, \"function\", name, inputs);\n        Object.defineProperty(this, internal, { value: FunctionFragmentInternal });\n        outputs = Object.freeze(outputs.slice());\n        const constant = (stateMutability === \"view\" || stateMutability === \"pure\");\n        const payable = (stateMutability === \"payable\");\n        defineProperties<FunctionFragment>(this, { constant, gas, outputs, payable, stateMutability });\n    }\n\n    /**\n     *  The Function selector.\n     */\n    get selector(): string {\n        return id(this.format(\"sighash\")).substring(0, 10);\n    }\n\n    /**\n     *  Returns a string representation of this function as %%format%%.\n     */\n    format(format?: FormatType): string {\n        if (format == null) { format = \"sighash\"; }\n        if (format === \"json\") {\n            return JSON.stringify({\n                type: \"function\",\n                name: this.name,\n                constant: this.constant,\n                stateMutability: ((this.stateMutability !== \"nonpayable\") ? this.stateMutability: undefined),\n                payable: this.payable,\n                gas: ((this.gas != null) ? this.gas: undefined),\n                inputs: this.inputs.map((i) => JSON.parse(i.format(format))),\n                outputs: this.outputs.map((o) => JSON.parse(o.format(format))),\n            });\n        }\n\n        const result: Array<string> = [];\n\n        if (format !== \"sighash\") { result.push(\"function\"); }\n\n        result.push(this.name + joinParams(format, this.inputs));\n\n        if (format !== \"sighash\") {\n            if (this.stateMutability !== \"nonpayable\") {\n                result.push(this.stateMutability);\n            }\n\n            if (this.outputs && this.outputs.length) {\n                result.push(\"returns\");\n                result.push(joinParams(format, this.outputs));\n            }\n\n            if (this.gas != null) { result.push(`@${ this.gas.toString() }`); }\n        }\n        return result.join(\" \");\n    }\n\n    /**\n     *  Return the selector for a function with %%name%% and %%params%%.\n     */\n    static getSelector(name: string, params?: Array<any>): string {\n        params = (params || []).map((p) => ParamType.from(p));\n        const fragment = new FunctionFragment(_guard, name, \"view\", params, [ ], null);\n        return fragment.selector;\n    }\n\n    /**\n     *  Returns a new **FunctionFragment** for %%obj%%.\n     */\n    static from(obj: any): FunctionFragment {\n        if (FunctionFragment.isFragment(obj)) { return obj; }\n\n        if (typeof(obj) === \"string\") {\n            try {\n                return FunctionFragment.from(lex(obj));\n            } catch (error) {\n                assertArgument(false, \"invalid function fragment\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            const name = consumeName(\"function\", obj);\n            const inputs = consumeParams(obj);\n            const mutability = consumeMutability(obj);\n\n            let outputs: Array<ParamType> = [ ];\n            if (consumeKeywords(obj, setify([ \"returns\" ])).has(\"returns\")) {\n                outputs = consumeParams(obj);\n            }\n\n            const gas = consumeGas(obj);\n\n            consumeEoi(obj);\n\n            return new FunctionFragment(_guard, name, mutability, inputs, outputs, gas);\n        }\n\n        let stateMutability = obj.stateMutability;\n\n        // Use legacy Solidity ABI logic if stateMutability is missing\n        if (stateMutability == null) {\n            stateMutability = \"payable\";\n\n            if (typeof(obj.constant) === \"boolean\") {\n                stateMutability = \"view\";\n                if (!obj.constant) {\n                    stateMutability = \"payable\"\n                    if (typeof(obj.payable) === \"boolean\" && !obj.payable) {\n                        stateMutability = \"nonpayable\";\n                    }\n                }\n            } else if (typeof(obj.payable) === \"boolean\" && !obj.payable) {\n                stateMutability = \"nonpayable\";\n            }\n        }\n\n        // @TODO: verifyState for stateMutability (e.g. throw if\n        //        payable: false but stateMutability is \"nonpayable\")\n\n        return new FunctionFragment(_guard, obj.name, stateMutability,\n             obj.inputs ? obj.inputs.map(ParamType.from): [ ],\n             obj.outputs ? obj.outputs.map(ParamType.from): [ ],\n             (obj.gas != null) ? obj.gas: null);\n    }\n\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is a\n     *  **FunctionFragment**.\n     */\n    static isFragment(value: any): value is FunctionFragment {\n        return (value && value[internal] === FunctionFragmentInternal);\n    }\n}\n\n/**\n *  A Fragment which represents a structure.\n */\nexport class StructFragment extends NamedFragment {\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, name: string, inputs: ReadonlyArray<ParamType>) {\n        super(guard, \"struct\", name, inputs);\n        Object.defineProperty(this, internal, { value: StructFragmentInternal });\n    }\n\n    /**\n     *  Returns a string representation of this struct as %%format%%.\n     */\n    format(): string {\n        throw new Error(\"@TODO\");\n    }\n\n    /**\n     *  Returns a new **StructFragment** for %%obj%%.\n     */\n    static from(obj: any): StructFragment {\n        if (typeof(obj) === \"string\") {\n            try {\n                return StructFragment.from(lex(obj));\n            } catch (error) {\n                assertArgument(false, \"invalid struct fragment\", \"obj\", obj);\n            }\n\n        } else if (obj instanceof TokenString) {\n            const name = consumeName(\"struct\", obj);\n            const inputs = consumeParams(obj);\n            consumeEoi(obj);\n            return new StructFragment(_guard, name, inputs);\n        }\n\n        return new StructFragment(_guard, obj.name, obj.inputs ? obj.inputs.map(ParamType.from): [ ]);\n    }\n\n// @TODO: fix this return type\n    /**\n     *  Returns ``true`` and provides a type guard if %%value%% is a\n     *  **StructFragment**.\n     */\n    static isFragment(value: any): value is FunctionFragment {\n        return (value && value[internal] === StructFragmentInternal);\n    }\n}\n\n"], "mappings": "AAAA;;;;;;;;;;;AAYA,SACIA,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EACtCC,MAAM,EAAEC,aAAa,EAAEC,cAAc,QAClC,mBAAmB;AAC1B,SAASC,EAAE,QAAQ,kBAAkB;AAgFpC;AAoBD;AACA,SAASC,MAAMA,CAACC,KAAoB;EAChC,MAAMC,MAAM,GAAgB,IAAIC,GAAG,EAAE;EACrCF,KAAK,CAACG,OAAO,CAAEC,CAAC,IAAKH,MAAM,CAACI,GAAG,CAACD,CAAC,CAAC,CAAC;EACnC,OAAOE,MAAM,CAACC,MAAM,CAACN,MAAM,CAAC;AAChC;AAEA,MAAMO,cAAc,GAAG,kCAAkC;AACzD,MAAMC,aAAa,GAAGV,MAAM,CAACS,cAAc,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC;AAEvD;AACA,MAAMC,QAAQ,GAAG,sEAAsE;AACvF,MAAMC,OAAO,GAAGb,MAAM,CAACY,QAAQ,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC;AAE3C,MAAMG,QAAQ,GAAG,0DAA0D;AAC3E,MAAMC,OAAO,GAAGf,MAAM,CAACc,QAAQ,CAACH,KAAK,CAAC,GAAG,CAAC,CAAC;AAE3C,MAAMK,YAAY,GAAG,yCAAyC;AAC9D,MAAMC,WAAW,GAAGjB,MAAM,CAACgB,YAAY,CAACL,KAAK,CAAC,GAAG,CAAC,CAAC;AAEnD,MAAMO,QAAQ,GAAG,eAAe;AAEhC;AACA,MAAMC,SAAS,GAAG,CAAEL,QAAQ,EAAEE,YAAY,EAAEE,QAAQ,EAAEN,QAAQ,CAAE,CAACQ,IAAI,CAAC,GAAG,CAAC;AAC1E,MAAMC,QAAQ,GAAGrB,MAAM,CAACmB,SAAS,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC;AAE7C;AACA,MAAMW,YAAY,GAA2B;EAC3C,GAAG,EAAE,YAAY;EAAE,GAAG,EAAE,aAAa;EACrC,GAAG,EAAE,cAAc;EAAE,GAAG,EAAE,eAAe;EACzC,GAAG,EAAE,OAAO;EAAE,GAAG,EAAE;CACpB;AAED;AACA,MAAMC,qBAAqB,GAAG,IAAIC,MAAM,CAAC,SAAS,CAAC;AACnD,MAAMC,iBAAiB,GAAG,IAAID,MAAM,CAAC,WAAW,CAAC;AACjD,MAAME,aAAa,GAAG,IAAIF,MAAM,CAAC,6BAA6B,CAAC;AAE/D;AACA,MAAMG,OAAO,GAAG,IAAIH,MAAM,CAAC,8BAA8B,CAAC;AAC1D,MAAMI,SAAS,GAAG,IAAIJ,MAAM,CAAC,qDAAqD,CAAC;AA8BnF,MAAMK,WAAW;EACb,CAAAC,MAAO;EACP,CAAAC,MAAO;EAEP,IAAID,MAAMA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,MAAO;EAAE;EAC5C,IAAIE,MAAMA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAD,MAAO,CAACC,MAAM,GAAG,IAAI,CAAC,CAAAF,MAAO;EAAE;EAElEG,YAAYF,MAA4B;IACpC,IAAI,CAAC,CAAAD,MAAO,GAAG,CAAC;IAChB,IAAI,CAAC,CAAAC,MAAO,GAAGA,MAAM,CAACG,KAAK,EAAE;EACjC;EAEAC,KAAKA,CAAA;IAAkB,OAAO,IAAIN,WAAW,CAAC,IAAI,CAAC,CAAAE,MAAO,CAAC;EAAE;EAC7DK,KAAKA,CAAA;IAAW,IAAI,CAAC,CAAAN,MAAO,GAAG,CAAC;EAAE;EAElC,CAAAO,cAAeC,CAACC,IAAA,GAAe,CAAC,EAAEC,EAAA,GAAa,CAAC;IAC5C,OAAO,IAAIX,WAAW,CAAC,IAAI,CAAC,CAAAE,MAAO,CAACG,KAAK,CAACK,IAAI,EAAEC,EAAE,CAAC,CAACC,GAAG,CAAEC,CAAC,IAAI;MAC1D,OAAOnC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACoC,MAAM,CAAC,EAAG,EAAED,CAAC,EAAE;QACvCE,KAAK,EAAGF,CAAC,CAACE,KAAK,GAAGL,IAAK;QACvBM,QAAQ,EAAGH,CAAC,CAACG,QAAQ,GAAGN,IAAK;QAC7BO,QAAQ,EAAGJ,CAAC,CAACI,QAAQ,GAAGP;OAC3B,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACP;EAEA;EACAQ,UAAUA,CAACC,OAA4B;IACnC,MAAMC,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE;IACvB,IAAID,GAAG,CAACE,IAAI,KAAK,SAAS,IAAI,CAACH,OAAO,CAACI,GAAG,CAACH,GAAG,CAACI,IAAI,CAAC,EAAE;MAAE,MAAM,IAAIC,KAAK,CAAC,oBAAqBL,GAAG,CAACI,IAAK,EAAE,CAAC;;IACzG,OAAO,IAAI,CAACE,GAAG,EAAE,CAACF,IAAI;EAC1B;EAEA;EACAG,OAAOA,CAACL,IAAY;IAChB,IAAI,IAAI,CAACD,IAAI,EAAE,CAACC,IAAI,KAAKA,IAAI,EAAE;MAC3B,MAAMF,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE;MACvB,MAAM,IAAII,KAAK,CAAC,YAAaH,IAAK,SAAUF,GAAG,CAACE,IAAK,IAAKM,IAAI,CAACC,SAAS,CAACT,GAAG,CAACI,IAAI,CAAE,EAAE,CAAC;;IAE1F,OAAO,IAAI,CAACE,GAAG,EAAE,CAACF,IAAI;EAC1B;EAEA;EACAM,QAAQA,CAAA;IACJ,MAAMV,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE;IACvB,IAAID,GAAG,CAACE,IAAI,KAAK,YAAY,EAAE;MAAE,MAAM,IAAIG,KAAK,CAAC,WAAW,CAAC;;IAC7D,MAAMpD,MAAM,GAAG,IAAI,CAAC,CAAAmC,cAAe,CAAC,IAAI,CAAC,CAAAP,MAAO,GAAG,CAAC,EAAEmB,GAAG,CAACL,KAAK,GAAG,CAAC,CAAC;IACpE,IAAI,CAAC,CAAAd,MAAO,GAAGmB,GAAG,CAACL,KAAK,GAAG,CAAC;IAC5B,OAAO1C,MAAM;EACjB;EAEA;EACA0D,SAASA,CAAA;IACL,MAAMX,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE;IAEvB,IAAID,GAAG,CAACE,IAAI,KAAK,YAAY,EAAE;MAAE,MAAM,IAAIG,KAAK,CAAC,WAAW,CAAC;;IAE7D,MAAMpD,MAAM,GAAuB,EAAG;IAEtC,OAAM,IAAI,CAAC,CAAA4B,MAAO,GAAGmB,GAAG,CAACL,KAAK,GAAG,CAAC,EAAE;MAChC,MAAMiB,IAAI,GAAG,IAAI,CAACX,IAAI,EAAE,CAACJ,QAAQ;MACjC5C,MAAM,CAAC4D,IAAI,CAAC,IAAI,CAAC,CAAAzB,cAAe,CAAC,IAAI,CAAC,CAAAP,MAAO,GAAG,CAAC,EAAE+B,IAAI,CAAC,CAAC;MACzD,IAAI,CAAC,CAAA/B,MAAO,GAAG+B,IAAI;;IAGvB,IAAI,CAAC,CAAA/B,MAAO,GAAGmB,GAAG,CAACL,KAAK,GAAG,CAAC;IAE5B,OAAO1C,MAAM;EACjB;EAEA;EACAgD,IAAIA,CAAA;IACA,IAAI,IAAI,CAAC,CAAApB,MAAO,IAAI,IAAI,CAAC,CAAAC,MAAO,CAACC,MAAM,EAAE;MACrC,MAAM,IAAIsB,KAAK,CAAC,eAAe,CAAC;;IAEpC,OAAO,IAAI,CAAC,CAAAvB,MAAO,CAAC,IAAI,CAAC,CAAAD,MAAO,CAAC;EACrC;EAEA;EACAiC,WAAWA,CAACf,OAA4B;IACpC,MAAMC,GAAG,GAAG,IAAI,CAACe,QAAQ,CAAC,SAAS,CAAC;IACpC,OAAQf,GAAG,IAAI,IAAI,IAAID,OAAO,CAACI,GAAG,CAACH,GAAG,CAAC,GAAIA,GAAG,GAAE,IAAI;EACxD;EAEA;EACAe,QAAQA,CAACb,IAAY;IACjB,IAAI,IAAI,CAACnB,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO,IAAI;;IACpC,MAAMiB,GAAG,GAAG,IAAI,CAACC,IAAI,EAAE;IACvB,OAAQD,GAAG,CAACE,IAAI,KAAKA,IAAI,GAAIF,GAAG,CAACI,IAAI,GAAE,IAAI;EAC/C;EAEA;EACAE,GAAGA,CAAA;IACC,MAAMrD,MAAM,GAAG,IAAI,CAACgD,IAAI,EAAE;IAC1B,IAAI,CAAC,CAAApB,MAAO,EAAE;IACd,OAAO5B,MAAM;EACjB;EAEA+D,QAAQA,CAAA;IACJ,MAAMlC,MAAM,GAAkB,EAAG;IACjC,KAAK,IAAImC,CAAC,GAAG,IAAI,CAAC,CAAApC,MAAO,EAAEoC,CAAC,GAAG,IAAI,CAAC,CAAAnC,MAAO,CAACC,MAAM,EAAEkC,CAAC,EAAE,EAAE;MACrD,MAAMC,KAAK,GAAG,IAAI,CAAC,CAAApC,MAAO,CAACmC,CAAC,CAAC;MAC7BnC,MAAM,CAAC+B,IAAI,CAAC,GAAIK,KAAK,CAAChB,IAAK,IAAKgB,KAAK,CAACd,IAAK,EAAE,CAAC;;IAElD,OAAO,gBAAiBtB,MAAM,CAACX,IAAI,CAAC,GAAG,CAAE,GAAG;EAChD;;AAKJ,SAASgD,GAAGA,CAACf,IAAY;EACrB,MAAMtB,MAAM,GAAiB,EAAG;EAEhC,MAAMsC,UAAU,GAAIC,OAAe,IAAI;IACnC,MAAMH,KAAK,GAAIrC,MAAM,GAAGuB,IAAI,CAACrB,MAAM,GAAIyB,IAAI,CAACC,SAAS,CAACL,IAAI,CAACvB,MAAM,CAAC,CAAC,GAAE,MAAM;IAC3E,MAAM,IAAIwB,KAAK,CAAC,iBAAkBa,KAAM,OAAQrC,MAAO,KAAMwC,OAAQ,EAAE,CAAC;EAC5E,CAAC;EAED,IAAIC,QAAQ,GAAkB,EAAG;EACjC,IAAIC,MAAM,GAAkB,EAAG;EAE/B,IAAI1C,MAAM,GAAG,CAAC;EACd,OAAOA,MAAM,GAAGuB,IAAI,CAACrB,MAAM,EAAE;IAEzB;IACA,IAAIyC,GAAG,GAAGpB,IAAI,CAACqB,SAAS,CAAC5C,MAAM,CAAC;IAChC,IAAIc,KAAK,GAAG6B,GAAG,CAAC7B,KAAK,CAACrB,qBAAqB,CAAC;IAC5C,IAAIqB,KAAK,EAAE;MACPd,MAAM,IAAIc,KAAK,CAAC,CAAC,CAAC,CAACZ,MAAM;MACzByC,GAAG,GAAGpB,IAAI,CAACqB,SAAS,CAAC5C,MAAM,CAAC;;IAGhC,MAAMqC,KAAK,GAAG;MAAEQ,KAAK,EAAEJ,QAAQ,CAACvC,MAAM;MAAEa,QAAQ,EAAE,CAAC,CAAC;MAAEC,QAAQ,EAAE,CAAC,CAAC;MAAEF,KAAK,EAAE,CAAC,CAAC;MAAEO,IAAI,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAEvB,MAAM;MAAE8C,KAAK,EAAE,CAAC;IAAC,CAAE;IACtH7C,MAAM,CAAC+B,IAAI,CAACK,KAAK,CAAC;IAElB,IAAIhB,IAAI,GAAI7B,YAAY,CAACmD,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAG;IACvC,IAAItB,IAAI,EAAE;MACNgB,KAAK,CAAChB,IAAI,GAAGA,IAAI;MACjBgB,KAAK,CAACd,IAAI,GAAGoB,GAAG,CAAC,CAAC,CAAC;MACnB3C,MAAM,EAAE;MAER,IAAIqB,IAAI,KAAK,YAAY,EAAE;QACvBoB,QAAQ,CAACT,IAAI,CAAC/B,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;QAChCwC,MAAM,CAACV,IAAI,CAAC/B,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;OAEjC,MAAM,IAAImB,IAAI,IAAI,aAAa,EAAE;QAC9B,IAAIoB,QAAQ,CAACvC,MAAM,KAAK,CAAC,EAAE;UAAEqC,UAAU,CAAC,0BAA0B,CAAC;;QAEnEF,KAAK,CAACvB,KAAK,GAAG2B,QAAQ,CAAChB,GAAG,EAAY;QAClBxB,MAAM,CAACoC,KAAK,CAACvB,KAAK,CAAC,CAAGA,KAAK,GAAGb,MAAM,CAACC,MAAM,GAAG,CAAC;QACnEmC,KAAK,CAACQ,KAAK,EAAE;QAEbR,KAAK,CAACtB,QAAQ,GAAG2B,MAAM,CAACjB,GAAG,EAAY;QACnBxB,MAAM,CAACoC,KAAK,CAACtB,QAAQ,CAAC,CAAGC,QAAQ,GAAGf,MAAM,CAACC,MAAM,GAAG,CAAC;OAE5E,MAAM,IAAImB,IAAI,KAAK,OAAO,EAAE;QACzBgB,KAAK,CAACtB,QAAQ,GAAG2B,MAAM,CAACjB,GAAG,EAAY;QACnBxB,MAAM,CAACoC,KAAK,CAACtB,QAAQ,CAAC,CAAGC,QAAQ,GAAGf,MAAM,CAACC,MAAM,GAAG,CAAC;QACzEwC,MAAM,CAACV,IAAI,CAAC/B,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;OAEjC,MAAM,IAAImB,IAAI,KAAK,cAAc,EAAE;QAChCgB,KAAK,CAAChB,IAAI,GAAG,SAAS;OAEzB,MAAM,IAAIA,IAAI,KAAK,eAAe,EAAE;QACjC;QACA,IAAI0B,MAAM,GAAI9C,MAAM,CAACwB,GAAG,EAAY,CAACF,IAAI;QACzC,IAAItB,MAAM,CAACC,MAAM,GAAG,CAAC,IAAID,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACmB,IAAI,KAAK,QAAQ,EAAE;UAClE,MAAMyB,KAAK,GAAI7C,MAAM,CAACwB,GAAG,EAAY,CAACF,IAAI;UAC1CwB,MAAM,GAAGD,KAAK,GAAGC,MAAM;UACH9C,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAG4C,KAAK,GAAGjF,SAAS,CAACiF,KAAK,CAAC;;QAE5E,IAAI7C,MAAM,CAACC,MAAM,KAAK,CAAC,IAAID,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAACmB,IAAI,KAAK,SAAS,EAAE;UACrE,MAAM,IAAIG,KAAK,CAAC,yBAAyB,CAAC;;QAE1BvB,MAAM,CAACA,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC,CAAGqB,IAAI,IAAIwB,MAAM;;MAGlE;;IAGJjC,KAAK,GAAG6B,GAAG,CAAC7B,KAAK,CAAClB,aAAa,CAAC;IAChC,IAAIkB,KAAK,EAAE;MACPuB,KAAK,CAACd,IAAI,GAAGT,KAAK,CAAC,CAAC,CAAC;MACrBd,MAAM,IAAIqC,KAAK,CAACd,IAAI,CAACrB,MAAM;MAE3B,IAAIX,QAAQ,CAAC+B,GAAG,CAACe,KAAK,CAACd,IAAI,CAAC,EAAE;QAC1Bc,KAAK,CAAChB,IAAI,GAAG,SAAS;QACtB;;MAGJ,IAAIgB,KAAK,CAACd,IAAI,CAACT,KAAK,CAAChB,SAAS,CAAC,EAAE;QAC7BuC,KAAK,CAAChB,IAAI,GAAG,MAAM;QACnB;;MAGJgB,KAAK,CAAChB,IAAI,GAAG,IAAI;MACjB;;IAGJP,KAAK,GAAG6B,GAAG,CAAC7B,KAAK,CAACnB,iBAAiB,CAAC;IACpC,IAAImB,KAAK,EAAE;MACPuB,KAAK,CAACd,IAAI,GAAGT,KAAK,CAAC,CAAC,CAAC;MACrBuB,KAAK,CAAChB,IAAI,GAAG,QAAQ;MACrBrB,MAAM,IAAIqC,KAAK,CAACd,IAAI,CAACrB,MAAM;MAC3B;;IAGJ,MAAM,IAAIsB,KAAK,CAAC,oBAAqBG,IAAI,CAACC,SAAS,CAACe,GAAG,CAAC,CAAC,CAAC,CAAE,gBAAiB3C,MAAO,EAAE,CAAC;;EAG3F,OAAO,IAAID,WAAW,CAACE,MAAM,CAACU,GAAG,CAAEC,CAAC,IAAKnC,MAAM,CAACC,MAAM,CAACkC,CAAC,CAAC,CAAC,CAAC;AAC/D;AAEA;AACA,SAASoC,WAAWA,CAACC,GAAwB,EAAE/B,OAA4B;EACvE,IAAIgC,QAAQ,GAAkB,EAAG;EACjC,KAAK,MAAMC,GAAG,IAAIjC,OAAO,CAACkC,IAAI,EAAE,EAAE;IAC9B,IAAIH,GAAG,CAAC3B,GAAG,CAAC6B,GAAG,CAAC,EAAE;MAAED,QAAQ,CAAClB,IAAI,CAACmB,GAAG,CAAC;;;EAE1C,IAAID,QAAQ,CAAChD,MAAM,GAAG,CAAC,EAAE;IAAE,MAAM,IAAIsB,KAAK,CAAC,sBAAuB0B,QAAQ,CAAC5D,IAAI,CAAC,IAAI,CAAE,EAAE,CAAC;;AAC7F;AAEA;AAEA;AACA,SAAS+D,WAAWA,CAAChC,IAAY,EAAEpB,MAAmB;EAClD,IAAIA,MAAM,CAACgC,WAAW,CAAChD,OAAO,CAAC,EAAE;IAC7B,MAAMqE,OAAO,GAAGrD,MAAM,CAACwB,GAAG,EAAE,CAACF,IAAI;IACjC,IAAI+B,OAAO,KAAKjC,IAAI,EAAE;MAClB,MAAM,IAAIG,KAAK,CAAC,YAAaH,IAAK,SAAUiC,OAAQ,EAAE,CAAC;;;EAI/D,OAAOrD,MAAM,CAACyB,OAAO,CAAC,IAAI,CAAC;AAC/B;AAEA;AACA,SAAS6B,eAAeA,CAACtD,MAAmB,EAAEiB,OAA6B;EACvE,MAAMsC,QAAQ,GAAgB,IAAInF,GAAG,EAAE;EACvC,OAAO,IAAI,EAAE;IACT,MAAMiF,OAAO,GAAGrD,MAAM,CAACiC,QAAQ,CAAC,SAAS,CAAC;IAE1C,IAAIoB,OAAO,IAAI,IAAI,IAAKpC,OAAO,IAAI,CAACA,OAAO,CAACI,GAAG,CAACgC,OAAO,CAAE,EAAE;MAAE;;IAC7DrD,MAAM,CAACwB,GAAG,EAAE;IAEZ,IAAI+B,QAAQ,CAAClC,GAAG,CAACgC,OAAO,CAAC,EAAE;MAAE,MAAM,IAAI9B,KAAK,CAAC,uBAAwBG,IAAI,CAACC,SAAS,CAAC0B,OAAO,CAAE,EAAE,CAAC;;IAChGE,QAAQ,CAAChF,GAAG,CAAC8E,OAAO,CAAC;;EAGzB,OAAO7E,MAAM,CAACC,MAAM,CAAC8E,QAAQ,CAAC;AAClC;AAEA;AACA,SAASC,iBAAiBA,CAACxD,MAAmB;EAC1C,IAAIyD,SAAS,GAAGH,eAAe,CAACtD,MAAM,EAAElB,OAAO,CAAC;EAEhD;EACAiE,WAAW,CAACU,SAAS,EAAExF,MAAM,CAAC,6BAA6B,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EACxEmE,WAAW,CAACU,SAAS,EAAExF,MAAM,CAAC,8BAA8B,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;EAEzE;EACA,IAAI6E,SAAS,CAACpC,GAAG,CAAC,MAAM,CAAC,EAAE;IAAE,OAAO,MAAM;;EAC1C,IAAIoC,SAAS,CAACpC,GAAG,CAAC,MAAM,CAAC,EAAE;IAAE,OAAO,MAAM;;EAC1C,IAAIoC,SAAS,CAACpC,GAAG,CAAC,SAAS,CAAC,EAAE;IAAE,OAAO,SAAS;;EAChD,IAAIoC,SAAS,CAACpC,GAAG,CAAC,YAAY,CAAC,EAAE;IAAE,OAAO,YAAY;;EAEtD;EACA,IAAIoC,SAAS,CAACpC,GAAG,CAAC,UAAU,CAAC,EAAE;IAAE,OAAO,MAAM;;EAE9C,OAAO,YAAY;AACvB;AAEA;AACA,SAASqC,aAAaA,CAAC1D,MAAmB,EAAE2D,YAAsB;EAC9D,OAAO3D,MAAM,CAAC6B,SAAS,EAAE,CAACnB,GAAG,CAAEC,CAAC,IAAKiD,SAAS,CAACpD,IAAI,CAACG,CAAC,EAAEgD,YAAY,CAAC,CAAC;AACzE;AAEA;AACA,SAASE,UAAUA,CAAC7D,MAAmB;EACnC,IAAIA,MAAM,CAACiC,QAAQ,CAAC,IAAI,CAAC,EAAE;IACvBjC,MAAM,CAACwB,GAAG,EAAE;IACZ,IAAIxB,MAAM,CAACiC,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC3B,OAAOtE,SAAS,CAACqC,MAAM,CAACwB,GAAG,EAAE,CAACF,IAAI,CAAC;;IAEvC,MAAM,IAAIC,KAAK,CAAC,aAAa,CAAC;;EAElC,OAAO,IAAI;AACf;AAEA,SAASuC,UAAUA,CAAC9D,MAAmB;EACnC,IAAIA,MAAM,CAACC,MAAM,EAAE;IACf,MAAM,IAAIsB,KAAK,CAAC,+BAAgCvB,MAAM,CAACD,MAAO,KAAMC,MAAM,CAACkC,QAAQ,EAAG,EAAE,CAAC;;AAEjG;AAEA,MAAM6B,cAAc,GAAG,IAAItE,MAAM,CAAC,oBAAoB,CAAC;AAEvD,SAASuE,eAAeA,CAAC5C,IAAY;EACjC,MAAMP,KAAK,GAAGO,IAAI,CAACP,KAAK,CAAChB,SAAS,CAAC;EACnC9B,cAAc,CAAC8C,KAAK,EAAE,cAAc,EAAE,MAAM,EAAEO,IAAI,CAAC;EACnD,IAAIA,IAAI,KAAK,MAAM,EAAE;IAAE,OAAO,SAAS;;EACvC,IAAIA,IAAI,KAAK,KAAK,EAAE;IAAE,OAAO,QAAQ;;EAErC,IAAIP,KAAK,CAAC,CAAC,CAAC,EAAE;IACV;IACA,MAAMZ,MAAM,GAAGgE,QAAQ,CAACpD,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC9C,cAAc,CAACkC,MAAM,KAAK,CAAC,IAAIA,MAAM,IAAI,EAAE,EAAE,sBAAsB,EAAE,MAAM,EAAEmB,IAAI,CAAC;GAErF,MAAM,IAAIP,KAAK,CAAC,CAAC,CAAC,EAAE;IACjB;IACA,MAAMqD,IAAI,GAAGD,QAAQ,CAACpD,KAAK,CAAC,CAAC,CAAW,CAAC;IACzC9C,cAAc,CAACmG,IAAI,KAAK,CAAC,IAAIA,IAAI,IAAI,GAAG,IAAKA,IAAI,GAAG,CAAC,KAAM,CAAC,EAAE,uBAAuB,EAAE,MAAM,EAAE9C,IAAI,CAAC;;EAGxG,OAAOA,IAAI;AACf;AAEA;AACA,MAAM+C,MAAM,GAAG,EAAG;AAelB,MAAMC,QAAQ,GAAGC,MAAM,CAACC,GAAG,CAAC,kBAAkB,CAAC;AAE/C,MAAMC,iBAAiB,GAAG,oBAAoB;AAC9C,MAAMC,qBAAqB,GAAG,gBAAgB;AAC9C,MAAMC,qBAAqB,GAAG,gBAAgB;AAC9C,MAAMC,2BAA2B,GAAG,sBAAsB;AAC1D,MAAMC,wBAAwB,GAAG,mBAAmB;AACpD,MAAMC,wBAAwB,GAAG,mBAAmB;AACpD,MAAMC,sBAAsB,GAAG,iBAAiB;AAEhD;;;AAGA,OAAM,MAAOjB,SAAS;EAElB;;;EAGSkB,IAAI;EAEb;;;;EAIS1D,IAAI;EAEb;;;EAGS2D,QAAQ;EAEjB;;;;;EAKSC,OAAO;EAEhB;;;;;EAKSC,UAAU;EAEnB;;;;;EAKSC,WAAW;EAEpB;;;;;EAKSC,aAAa;EAGtB;;;EAGAjF,YAAYkF,KAAU,EAAEN,IAAY,EAAE1D,IAAY,EAAE2D,QAAgB,EAAEC,OAAuB,EAAEC,UAA2C,EAAEC,WAA0B,EAAEC,aAA+B;IACnMrH,aAAa,CAACsH,KAAK,EAAEjB,MAAM,EAAE,WAAW,CAAC;IACzC3F,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE0B;IAAiB,CAAE,CAAC;IAEnE,IAAIU,UAAU,EAAE;MAAEA,UAAU,GAAGzG,MAAM,CAACC,MAAM,CAACwG,UAAU,CAAC9E,KAAK,EAAE,CAAC;;IAEhE,IAAI4E,QAAQ,KAAK,OAAO,EAAE;MACtB,IAAIG,WAAW,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;QAC9C,MAAM,IAAI5D,KAAK,CAAC,EAAE,CAAC;;KAE1B,MAAM,IAAI2D,WAAW,IAAI,IAAI,IAAIC,aAAa,IAAI,IAAI,EAAE;MACrD,MAAM,IAAI5D,KAAK,CAAC,EAAE,CAAC;;IAGvB,IAAIwD,QAAQ,KAAK,OAAO,EAAE;MACtB,IAAIE,UAAU,IAAI,IAAI,EAAE;QAAE,MAAM,IAAI1D,KAAK,CAAC,EAAE,CAAC;;KAChD,MAAM,IAAI0D,UAAU,IAAI,IAAI,EAAE;MAC3B,MAAM,IAAI1D,KAAK,CAAC,EAAE,CAAC;;IAGvB7D,gBAAgB,CAAY,IAAI,EAAE;MAC9BoH,IAAI;MAAE1D,IAAI;MAAE2D,QAAQ;MAAEC,OAAO;MAAEC,UAAU;MAAEC,WAAW;MAAEC;KAC3D,CAAC;EACN;EAEA;;;;;;;;;;;EAWAG,MAAMA,CAACA,MAAmB;IACtB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,SAAS;;IACxC,IAAIA,MAAM,KAAK,MAAM,EAAE;MACnB,MAAMR,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,EAAE;MAE5B,IAAI,IAAI,CAACS,OAAO,EAAE,EAAE;QAChB,MAAMpH,MAAM,GAAGuD,IAAI,CAAC8D,KAAK,CAAC,IAAI,CAACL,aAAa,CAACG,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5DnH,MAAM,CAAC2G,IAAI,GAAGA,IAAI;QAClB3G,MAAM,CAACiD,IAAI,IAAI,IAAM,IAAI,CAAC8D,WAAW,GAAG,CAAC,GAAG,EAAE,GAAEO,MAAM,CAAC,IAAI,CAACP,WAAW,CAAC,GAAK;QAC7E,OAAOxD,IAAI,CAACC,SAAS,CAACxD,MAAM,CAAC;;MAGjC,MAAMA,MAAM,GAAQ;QAChBiD,IAAI,EAAI,IAAI,CAAC2D,QAAQ,KAAK,OAAO,GAAI,OAAO,GAAE,IAAI,CAAC3D,IAAK;QACxD0D;OACH;MAGD,IAAI,OAAO,IAAI,CAACE,OAAQ,KAAK,SAAS,EAAE;QAAE7G,MAAM,CAAC6G,OAAO,GAAG,IAAI,CAACA,OAAO;;MACvE,IAAI,IAAI,CAACU,OAAO,EAAE,EAAE;QAChBvH,MAAM,CAAC8G,UAAU,GAAG,IAAI,CAACA,UAAU,CAACvE,GAAG,CAAEiF,CAAC,IAAKjE,IAAI,CAAC8D,KAAK,CAACG,CAAC,CAACL,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC;;MAEhF,OAAO5D,IAAI,CAACC,SAAS,CAACxD,MAAM,CAAC;;IAGjC,IAAIA,MAAM,GAAG,EAAE;IAEf;IACA,IAAI,IAAI,CAACoH,OAAO,EAAE,EAAE;MAChBpH,MAAM,IAAI,IAAI,CAACgH,aAAa,CAACG,MAAM,CAACA,MAAM,CAAC;MAC3CnH,MAAM,IAAI,IAAM,IAAI,CAAC+G,WAAW,GAAG,CAAC,GAAG,EAAE,GAAEO,MAAM,CAAC,IAAI,CAACP,WAAW,CAAC,GAAK;KAC3E,MAAM;MACH,IAAI,IAAI,CAACQ,OAAO,EAAE,EAAE;QAChBvH,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC8G,UAAU,CAACvE,GAAG,CAC9BkF,IAAI,IAAKA,IAAI,CAACN,MAAM,CAACA,MAAM,CAAC,CAChC,CAACjG,IAAI,CAAEiG,MAAM,KAAK,MAAM,GAAI,IAAI,GAAE,GAAG,CAAC,GAAG,GAAG;OAChD,MAAM;QACHnH,MAAM,IAAI,IAAI,CAACiD,IAAI;;;IAI3B,IAAIkE,MAAM,KAAK,SAAS,EAAE;MACtB,IAAI,IAAI,CAACN,OAAO,KAAK,IAAI,EAAE;QAAE7G,MAAM,IAAI,UAAU;;MACjD,IAAImH,MAAM,KAAK,MAAM,IAAI,IAAI,CAACR,IAAI,EAAE;QAChC3G,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC2G,IAAI;;;IAIjC,OAAO3G,MAAM;EACjB;EAEA;;;;;;EAMAoH,OAAOA,CAAA;IACH,OAAQ,IAAI,CAACR,QAAQ,KAAK,OAAO;EACrC;EAEA;;;;;;EAMAW,OAAOA,CAAA;IACH,OAAQ,IAAI,CAACX,QAAQ,KAAK,OAAO;EACrC;EAEA;;;;;;EAMAc,WAAWA,CAAA;IACP,OAAQ,IAAI,CAACb,OAAO,IAAI,IAAI;EAChC;EAEA;;;;EAIAc,IAAIA,CAACjD,KAAU,EAAEkD,OAA0B;IACvC,IAAI,IAAI,CAACR,OAAO,EAAE,EAAE;MAChB,IAAI,CAACS,KAAK,CAACT,OAAO,CAAC1C,KAAK,CAAC,EAAE;QAAE,MAAM,IAAItB,KAAK,CAAC,qBAAqB,CAAC;;MACnE,IAAI,IAAI,CAAC2D,WAAW,KAAK,CAAC,CAAC,IAAIrC,KAAK,CAAC5C,MAAM,KAAK,IAAI,CAACiF,WAAW,EAAE;QAC9D,MAAM,IAAI3D,KAAK,CAAC,uBAAuB,CAAC;;MAE5C,MAAM0E,KAAK,GAAG,IAAI;MAClB,OAAOpD,KAAK,CAACnC,GAAG,CAAEwF,CAAC,IAAMD,KAAK,CAACd,aAAa,CAACW,IAAI,CAACI,CAAC,EAAEH,OAAO,CAAE,CAAC;;IAGnE,IAAI,IAAI,CAACL,OAAO,EAAE,EAAE;MAChB,IAAI,CAACM,KAAK,CAACT,OAAO,CAAC1C,KAAK,CAAC,EAAE;QAAE,MAAM,IAAItB,KAAK,CAAC,qBAAqB,CAAC;;MACnE,IAAIsB,KAAK,CAAC5C,MAAM,KAAK,IAAI,CAACgF,UAAU,CAAChF,MAAM,EAAE;QACzC,MAAM,IAAIsB,KAAK,CAAC,uBAAuB,CAAC;;MAE5C,MAAM0E,KAAK,GAAG,IAAI;MAClB,OAAOpD,KAAK,CAACnC,GAAG,CAAC,CAACwF,CAAC,EAAE/D,CAAC,KAAM8D,KAAK,CAAChB,UAAU,CAAC9C,CAAC,CAAC,CAAC2D,IAAI,CAACI,CAAC,EAAEH,OAAO,CAAE,CAAC;;IAGtE,OAAOA,OAAO,CAAC,IAAI,CAAC3E,IAAI,EAAEyB,KAAK,CAAC;EACpC;EAEA,CAAAsD,SAAUC,CAACC,QAA8B,EAAExD,KAAU,EAAEkD,OAA+B,EAAEO,QAA8B;IAElH,IAAI,IAAI,CAACf,OAAO,EAAE,EAAE;MAChB,IAAI,CAACS,KAAK,CAACT,OAAO,CAAC1C,KAAK,CAAC,EAAE;QAAE,MAAM,IAAItB,KAAK,CAAC,qBAAqB,CAAC;;MACnE,IAAI,IAAI,CAAC2D,WAAW,KAAK,CAAC,CAAC,IAAIrC,KAAK,CAAC5C,MAAM,KAAK,IAAI,CAACiF,WAAW,EAAE;QAC9D,MAAM,IAAI3D,KAAK,CAAC,uBAAuB,CAAC;;MAE5C,MAAMgF,SAAS,GAAG,IAAI,CAACpB,aAAa;MAEpC,MAAMhH,MAAM,GAAG0E,KAAK,CAAC1C,KAAK,EAAE;MAC5BhC,MAAM,CAACE,OAAO,CAAC,CAACwE,KAAK,EAAE2D,KAAK,KAAI;QAC5BD,SAAS,CAAC,CAAAJ,SAAU,CAACE,QAAQ,EAAExD,KAAK,EAAEkD,OAAO,EAAGlD,KAAU,IAAI;UAC1D1E,MAAM,CAACqI,KAAK,CAAC,GAAG3D,KAAK;QACzB,CAAC,CAAC;MACN,CAAC,CAAC;MACFyD,QAAQ,CAACnI,MAAM,CAAC;MAChB;;IAGJ,IAAI,IAAI,CAACuH,OAAO,EAAE,EAAE;MAChB,MAAMT,UAAU,GAAG,IAAI,CAACA,UAAU;MAElC;MACA,IAAI9G,MAAkB;MACtB,IAAI6H,KAAK,CAACT,OAAO,CAAC1C,KAAK,CAAC,EAAE;QACtB1E,MAAM,GAAG0E,KAAK,CAAC1C,KAAK,EAAE;OAEzB,MAAM;QACH,IAAI0C,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAM,KAAK,QAAQ,EAAE;UAC7C,MAAM,IAAItB,KAAK,CAAC,qBAAqB,CAAC;;QAG1CpD,MAAM,GAAG8G,UAAU,CAACvE,GAAG,CAAE+F,KAAK,IAAI;UAC9B,IAAI,CAACA,KAAK,CAAC3B,IAAI,EAAE;YAAE,MAAM,IAAIvD,KAAK,CAAC,iDAAiD,CAAC;;UACrF,IAAI,EAAEkF,KAAK,CAAC3B,IAAI,IAAIjC,KAAK,CAAC,EAAE;YACxB,MAAM,IAAItB,KAAK,CAAC,+BAAgCkF,KAAK,CAAC3B,IAAK,EAAE,CAAC;;UAElE,OAAOjC,KAAK,CAAC4D,KAAK,CAAC3B,IAAI,CAAC;QAC5B,CAAC,CAAC;;MAGN,IAAI3G,MAAM,CAAC8B,MAAM,KAAK,IAAI,CAACgF,UAAU,CAAChF,MAAM,EAAE;QAC1C,MAAM,IAAIsB,KAAK,CAAC,uBAAuB,CAAC;;MAG5CpD,MAAM,CAACE,OAAO,CAAC,CAACwE,KAAK,EAAE2D,KAAK,KAAI;QAC5BvB,UAAU,CAACuB,KAAK,CAAC,CAAC,CAAAL,SAAU,CAACE,QAAQ,EAAExD,KAAK,EAAEkD,OAAO,EAAGlD,KAAU,IAAI;UAClE1E,MAAM,CAACqI,KAAK,CAAC,GAAG3D,KAAK;QACzB,CAAC,CAAC;MACN,CAAC,CAAC;MACFyD,QAAQ,CAACnI,MAAM,CAAC;MAChB;;IAGJ,MAAMA,MAAM,GAAG4H,OAAO,CAAC,IAAI,CAAC3E,IAAI,EAAEyB,KAAK,CAAC;IACxC,IAAI1E,MAAM,CAACuI,IAAI,EAAE;MACbL,QAAQ,CAACtE,IAAI,CAAE,kBAAK;QAAcuE,QAAQ,CAAC,MAAMnI,MAAM,CAAC;MAAE,CAAC,CAAC,CAAE,CAAC;KAClE,MAAM;MACHmI,QAAQ,CAACnI,MAAM,CAAC;;EAExB;EAEA;;;;;;;EAOA,MAAMgI,SAASA,CAACtD,KAAU,EAAEkD,OAA+B;IACvD,MAAMM,QAAQ,GAAyB,EAAG;IAC1C,MAAMlI,MAAM,GAAY,CAAE0E,KAAK,CAAE;IACjC,IAAI,CAAC,CAAAsD,SAAU,CAACE,QAAQ,EAAExD,KAAK,EAAEkD,OAAO,EAAGlD,KAAU,IAAI;MACrD1E,MAAM,CAAC,CAAC,CAAC,GAAG0E,KAAK;IACrB,CAAC,CAAC;IACF,IAAIwD,QAAQ,CAACpG,MAAM,EAAE;MAAE,MAAM0G,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;;IAClD,OAAOlI,MAAM,CAAC,CAAC,CAAC;EACpB;EAEA;;;;;;EAMA,OAAOqC,IAAIA,CAACqG,GAAQ,EAAElD,YAAsB;IACxC,IAAIC,SAAS,CAACkD,WAAW,CAACD,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAE5C,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOjD,SAAS,CAACpD,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,EAAElD,YAAY,CAAC;OAChD,CAAC,OAAOoD,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAG9D,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,IAAIsB,IAAI,GAAG,EAAE;QAAE2D,QAAQ,GAAG,EAAE;MAC5B,IAAIiC,KAAK,GAA4B,IAAI;MAEzC,IAAI1D,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,OAAO,CAAE,CAAC,CAAC,CAACoD,GAAG,CAAC,OAAO,CAAC,IAAIwF,GAAG,CAAC5E,QAAQ,CAAC,YAAY,CAAC,EAAE;QACtF;QACA8C,QAAQ,GAAG,OAAO;QAClBiC,KAAK,GAAGH,GAAG,CAAChF,SAAS,EAAE,CAACnB,GAAG,CAAEC,CAAC,IAAKiD,SAAS,CAACpD,IAAI,CAACG,CAAC,CAAC,CAAC;QACrDS,IAAI,GAAG,SAAU4F,KAAK,CAACtG,GAAG,CAAEiF,CAAC,IAAKA,CAAC,CAACL,MAAM,EAAE,CAAC,CAACjG,IAAI,CAAC,GAAG,CAAE,GAAG;OAC9D,MAAM;QACH;QACA+B,IAAI,GAAG4C,eAAe,CAAC6C,GAAG,CAACpF,OAAO,CAAC,MAAM,CAAC,CAAC;QAC3CsD,QAAQ,GAAG3D,IAAI;;MAGnB;MACA,IAAI+D,aAAa,GAAsB,IAAI;MAC3C,IAAID,WAAW,GAAkB,IAAI;MAErC,OAAO2B,GAAG,CAAC5G,MAAM,IAAI4G,GAAG,CAAC5E,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC1C,MAAMgF,OAAO,GAAGJ,GAAG,CAACrF,GAAG,EAAE,CAAC,CAAC;QAC3B2D,aAAa,GAAG,IAAIvB,SAAS,CAACO,MAAM,EAAE,EAAE,EAAE/C,IAAI,EAAE2D,QAAQ,EAAE,IAAI,EAAEiC,KAAK,EAAE9B,WAAW,EAAEC,aAAa,CAAC;QAClGD,WAAW,GAAG+B,OAAO,CAACpE,KAAK;QAC3BzB,IAAI,IAAI6F,OAAO,CAAC3F,IAAI;QACpByD,QAAQ,GAAG,OAAO;QAClBiC,KAAK,GAAG,IAAI;;MAGhB,IAAIhC,OAAO,GAAmB,IAAI;MAClC,MAAMzB,QAAQ,GAAGD,eAAe,CAACuD,GAAG,EAAE3H,WAAW,CAAC;MAClD,IAAIqE,QAAQ,CAAClC,GAAG,CAAC,SAAS,CAAC,EAAE;QACzB,IAAI,CAACsC,YAAY,EAAE;UAAE,MAAM,IAAIpC,KAAK,CAAC,EAAE,CAAC;;QACxCyD,OAAO,GAAG,IAAI;;MAGlB,MAAMF,IAAI,GAAI+B,GAAG,CAAC5E,QAAQ,CAAC,IAAI,CAAC,GAAG4E,GAAG,CAACrF,GAAG,EAAE,CAACF,IAAI,GAAE,EAAG;MAEtD,IAAIuF,GAAG,CAAC5G,MAAM,EAAE;QAAE,MAAM,IAAIsB,KAAK,CAAC,iBAAiB,CAAC;;MAEpD,OAAO,IAAIqC,SAAS,CAACO,MAAM,EAAEW,IAAI,EAAE1D,IAAI,EAAE2D,QAAQ,EAAEC,OAAO,EAAEgC,KAAK,EAAE9B,WAAW,EAAEC,aAAa,CAAC;;IAGlG,MAAML,IAAI,GAAG+B,GAAG,CAAC/B,IAAI;IACrB/G,cAAc,CAAC,CAAC+G,IAAI,IAAK,OAAOA,IAAK,KAAK,QAAQ,IAAIA,IAAI,CAACjE,KAAK,CAACjB,OAAO,CAAE,EACtE,cAAc,EAAE,UAAU,EAAEkF,IAAI,CAAC;IAErC,IAAIE,OAAO,GAAG6B,GAAG,CAAC7B,OAAO;IACzB,IAAIA,OAAO,IAAI,IAAI,EAAE;MACjBjH,cAAc,CAAC4F,YAAY,EAAE,6BAA6B,EAAE,aAAa,EAAEkD,GAAG,CAAC7B,OAAO,CAAC;MACvFA,OAAO,GAAG,CAAC,CAACA,OAAO;;IAGvB,IAAI5D,IAAI,GAAGyF,GAAG,CAACzF,IAAI;IAEnB,IAAI8F,UAAU,GAAG9F,IAAI,CAACP,KAAK,CAACkD,cAAc,CAAC;IAC3C,IAAImD,UAAU,EAAE;MACZ,MAAMhC,WAAW,GAAGjB,QAAQ,CAACiD,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MACnD,MAAM/B,aAAa,GAAGvB,SAAS,CAACpD,IAAI,CAAC;QACjCY,IAAI,EAAE8F,UAAU,CAAC,CAAC,CAAC;QACnBjC,UAAU,EAAE4B,GAAG,CAAC5B;OACnB,CAAC;MAEF,OAAO,IAAIrB,SAAS,CAACO,MAAM,EAAEW,IAAI,IAAI,EAAE,EAAE1D,IAAI,EAAE,OAAO,EAAE4D,OAAO,EAAE,IAAI,EAAEE,WAAW,EAAEC,aAAa,CAAC;;IAGtG,IAAI/D,IAAI,KAAK,OAAO,IAAIA,IAAI,CAAC+F,UAAU,CAAC,QAAQ,aAAY,CAAC,IAAI/F,IAAI,CAAC+F,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;MAChG,MAAMH,KAAK,GAAIH,GAAG,CAAC5B,UAAU,IAAI,IAAI,GAAI4B,GAAG,CAAC5B,UAAU,CAACvE,GAAG,CAAEiF,CAAM,IAAK/B,SAAS,CAACpD,IAAI,CAACmF,CAAC,CAAC,CAAC,GAAE,IAAI;MAChG,MAAMyB,KAAK,GAAG,IAAIxD,SAAS,CAACO,MAAM,EAAEW,IAAI,IAAI,EAAE,EAAE1D,IAAI,EAAE,OAAO,EAAE4D,OAAO,EAAEgC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;MAC1F;MACA,OAAOI,KAAK;;IAGhBhG,IAAI,GAAG4C,eAAe,CAAC6C,GAAG,CAACzF,IAAI,CAAC;IAEhC,OAAO,IAAIwC,SAAS,CAACO,MAAM,EAAEW,IAAI,IAAI,EAAE,EAAE1D,IAAI,EAAEA,IAAI,EAAE4D,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnF;EAEA;;;EAGA,OAAO8B,WAAWA,CAACjE,KAAU;IACzB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKG,iBAAiB;EAC1D;;AAQJ;;;AAGA,OAAM,MAAgB8C,QAAQ;EAC1B;;;EAGSjG,IAAI;EAEb;;;EAGSkG,MAAM;EAEf;;;EAGApH,YAAYkF,KAAU,EAAEhE,IAAkB,EAAEkG,MAAgC;IACxExJ,aAAa,CAACsH,KAAK,EAAEjB,MAAM,EAAE,UAAU,CAAC;IACxCmD,MAAM,GAAG9I,MAAM,CAACC,MAAM,CAAC6I,MAAM,CAACnH,KAAK,EAAE,CAAC;IACtCzC,gBAAgB,CAAW,IAAI,EAAE;MAAE0D,IAAI;MAAEkG;IAAM,CAAE,CAAC;EACtD;EAOA;;;;EAIA,OAAO9G,IAAIA,CAACqG,GAAQ;IAChB,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAE1B;MACA,IAAI;QACAQ,QAAQ,CAAC7G,IAAI,CAACkB,IAAI,CAAC8D,KAAK,CAACqB,GAAG,CAAC,CAAC;OACjC,CAAC,OAAOU,CAAC,EAAE;MAEZ;MACA,OAAOF,QAAQ,CAAC7G,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;;IAGlC,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MAC5B;MAEA,MAAMsB,IAAI,GAAGyF,GAAG,CAAC7E,WAAW,CAAChD,OAAO,CAAC;MAErC,QAAQoC,IAAI;QACR,KAAK,aAAa;UAAE,OAAOoG,mBAAmB,CAAChH,IAAI,CAACqG,GAAG,CAAC;QACxD,KAAK,OAAO;UAAE,OAAOY,aAAa,CAACjH,IAAI,CAACqG,GAAG,CAAC;QAC5C,KAAK,OAAO;UAAE,OAAOa,aAAa,CAAClH,IAAI,CAACqG,GAAG,CAAC;QAC5C,KAAK,UAAU;QAAE,KAAK,SAAS;UAC3B,OAAOc,gBAAgB,CAACnH,IAAI,CAACqG,GAAG,CAAC;QACrC,KAAK,UAAU;UAAE,OAAOe,gBAAgB,CAACpH,IAAI,CAACqG,GAAG,CAAC;QAClD,KAAK,QAAQ;UAAE,OAAOgB,cAAc,CAACrH,IAAI,CAACqG,GAAG,CAAC;;KAGrD,MAAM,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MACjC;MAEA,QAAQA,GAAG,CAACzF,IAAI;QACZ,KAAK,aAAa;UAAE,OAAOoG,mBAAmB,CAAChH,IAAI,CAACqG,GAAG,CAAC;QACxD,KAAK,OAAO;UAAE,OAAOY,aAAa,CAACjH,IAAI,CAACqG,GAAG,CAAC;QAC5C,KAAK,OAAO;UAAE,OAAOa,aAAa,CAAClH,IAAI,CAACqG,GAAG,CAAC;QAC5C,KAAK,UAAU;QAAE,KAAK,SAAS;UAC3B,OAAOc,gBAAgB,CAACnH,IAAI,CAACqG,GAAG,CAAC;QACrC,KAAK,UAAU;UAAE,OAAOe,gBAAgB,CAACpH,IAAI,CAACqG,GAAG,CAAC;QAClD,KAAK,QAAQ;UAAE,OAAOgB,cAAc,CAACrH,IAAI,CAACqG,GAAG,CAAC;;MAGlDhJ,MAAM,CAAC,KAAK,EAAE,qBAAsBgJ,GAAG,CAACzF,IAAK,EAAE,EAAE,uBAAuB,EAAE;QACtE0G,SAAS,EAAE;OACd,CAAC;;IAGN/J,cAAc,CAAC,KAAK,EAAE,6BAA6B,EAAE,KAAK,EAAE8I,GAAG,CAAC;EACpE;EAEA;;;EAGA,OAAOkB,aAAaA,CAAClF,KAAU;IAC3B,OAAO2E,mBAAmB,CAACQ,UAAU,CAACnF,KAAK,CAAC;EAChD;EAEA;;;EAGA,OAAOoF,OAAOA,CAACpF,KAAU;IACrB,OAAO4E,aAAa,CAACO,UAAU,CAACnF,KAAK,CAAC;EAC1C;EAEA;;;EAGA,OAAOqF,OAAOA,CAACrF,KAAU;IACrB,OAAO6E,aAAa,CAACM,UAAU,CAACnF,KAAK,CAAC;EAC1C;EAEA;;;EAGA,OAAOsF,UAAUA,CAACtF,KAAU;IACxB,OAAO+E,gBAAgB,CAACI,UAAU,CAACnF,KAAK,CAAC;EAC7C;EAEA;;;EAGA,OAAOuF,QAAQA,CAACvF,KAAU;IACtB,OAAOgF,cAAc,CAACG,UAAU,CAACnF,KAAK,CAAC;EAC3C;;AAGJ;;;;AAIA,OAAM,MAAgBwF,aAAc,SAAQhB,QAAQ;EAChD;;;EAGSvC,IAAI;EAEb;;;EAGA5E,YAAYkF,KAAU,EAAEhE,IAAkB,EAAE0D,IAAY,EAAEwC,MAAgC;IACtF,KAAK,CAAClC,KAAK,EAAEhE,IAAI,EAAEkG,MAAM,CAAC;IAC1BvJ,cAAc,CAAC,OAAO+G,IAAK,KAAK,QAAQ,IAAIA,IAAI,CAACjE,KAAK,CAACjB,OAAO,CAAC,EAC3D,oBAAoB,EAAE,MAAM,EAAEkF,IAAI,CAAC;IACvCwC,MAAM,GAAG9I,MAAM,CAACC,MAAM,CAAC6I,MAAM,CAACnH,KAAK,EAAE,CAAC;IACtCzC,gBAAgB,CAAgB,IAAI,EAAE;MAAEoH;IAAI,CAAE,CAAC;EACnD;;AAGJ,SAASwD,UAAUA,CAAChD,MAAkB,EAAEiD,MAAgC;EACpE,OAAO,GAAG,GAAGA,MAAM,CAAC7H,GAAG,CAAE8H,CAAC,IAAKA,CAAC,CAAClD,MAAM,CAACA,MAAM,CAAC,CAAC,CAACjG,IAAI,CAAEiG,MAAM,KAAK,MAAM,GAAI,IAAI,GAAE,GAAG,CAAC,GAAG,GAAG;AAChG;AAEA;;;AAGA,OAAM,MAAOmC,aAAc,SAAQY,aAAa;EAC5C;;;EAGAnI,YAAYkF,KAAU,EAAEN,IAAY,EAAEwC,MAAgC;IAClE,KAAK,CAAClC,KAAK,EAAE,OAAO,EAAEN,IAAI,EAAEwC,MAAM,CAAC;IACnC9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE2B;IAAqB,CAAE,CAAC;EAC3E;EAEA;;;EAGA,IAAIiE,QAAQA,CAAA;IACR,OAAOzK,EAAE,CAAC,IAAI,CAACsH,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC3C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACtD;EAEA;;;EAGA2C,MAAMA,CAACA,MAAmB;IACtB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,SAAS;;IACxC,IAAIA,MAAM,KAAK,MAAM,EAAE;MACnB,OAAO5D,IAAI,CAACC,SAAS,CAAC;QAClBP,IAAI,EAAE,OAAO;QACb0D,IAAI,EAAE,IAAI,CAACA,IAAI;QACfwC,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC5G,GAAG,CAAEgI,KAAK,IAAKhH,IAAI,CAAC8D,KAAK,CAACkD,KAAK,CAACpD,MAAM,CAACA,MAAM,CAAC,CAAC;OACtE,CAAC;;IAGN,MAAMnH,MAAM,GAAkB,EAAG;IACjC,IAAImH,MAAM,KAAK,SAAS,EAAE;MAAEnH,MAAM,CAAC4D,IAAI,CAAC,OAAO,CAAC;;IAChD5D,MAAM,CAAC4D,IAAI,CAAC,IAAI,CAAC+C,IAAI,GAAGwD,UAAU,CAAChD,MAAM,EAAE,IAAI,CAACgC,MAAM,CAAC,CAAC;IACxD,OAAOnJ,MAAM,CAACkB,IAAI,CAAC,GAAG,CAAC;EAC3B;EAEA;;;EAGA,OAAOmB,IAAIA,CAACqG,GAAQ;IAChB,IAAIY,aAAa,CAACO,UAAU,CAACnB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAE/C,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,OAAOY,aAAa,CAACjH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;KAEtC,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,MAAMgF,IAAI,GAAG1B,WAAW,CAAC,OAAO,EAAEyD,GAAG,CAAC;MACtC,MAAMS,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;MACjC/C,UAAU,CAAC+C,GAAG,CAAC;MAEf,OAAO,IAAIY,aAAa,CAACtD,MAAM,EAAEW,IAAI,EAAEwC,MAAM,CAAC;;IAGlD,OAAO,IAAIG,aAAa,CAACtD,MAAM,EAAE0C,GAAG,CAAC/B,IAAI,EACrC+B,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,CAAC5G,GAAG,CAACkD,SAAS,CAACpD,IAAI,CAAC,GAAE,EAAG,CAAC;EACzD;EAEA;;;;EAIA,OAAOwH,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKI,qBAAqB;EAC9D;;AAGJ;;;AAGA,OAAM,MAAOkD,aAAc,SAAQW,aAAa;EAC5C;;;EAGSM,SAAS;EAElB;;;EAGAzI,YAAYkF,KAAU,EAAEN,IAAY,EAAEwC,MAAgC,EAAEqB,SAAkB;IACtF,KAAK,CAACvD,KAAK,EAAE,OAAO,EAAEN,IAAI,EAAEwC,MAAM,CAAC;IACnC9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE4B;IAAqB,CAAE,CAAC;IACvE/G,gBAAgB,CAAgB,IAAI,EAAE;MAAEiL;IAAS,CAAE,CAAC;EACxD;EAEA;;;EAGA,IAAIC,SAASA,CAAA;IACT,OAAO5K,EAAE,CAAC,IAAI,CAACsH,MAAM,CAAC,SAAS,CAAC,CAAC;EACrC;EAEA;;;EAGAA,MAAMA,CAACA,MAAmB;IACtB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,SAAS;;IACxC,IAAIA,MAAM,KAAK,MAAM,EAAE;MACnB,OAAO5D,IAAI,CAACC,SAAS,CAAC;QAClBP,IAAI,EAAE,OAAO;QACbuH,SAAS,EAAE,IAAI,CAACA,SAAS;QACzB7D,IAAI,EAAE,IAAI,CAACA,IAAI;QACfwC,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC5G,GAAG,CAAEyB,CAAC,IAAKT,IAAI,CAAC8D,KAAK,CAACrD,CAAC,CAACmD,MAAM,CAACA,MAAM,CAAC,CAAC;OAC9D,CAAC;;IAGN,MAAMnH,MAAM,GAAkB,EAAG;IACjC,IAAImH,MAAM,KAAK,SAAS,EAAE;MAAEnH,MAAM,CAAC4D,IAAI,CAAC,OAAO,CAAC;;IAChD5D,MAAM,CAAC4D,IAAI,CAAC,IAAI,CAAC+C,IAAI,GAAGwD,UAAU,CAAChD,MAAM,EAAE,IAAI,CAACgC,MAAM,CAAC,CAAC;IACxD,IAAIhC,MAAM,KAAK,SAAS,IAAI,IAAI,CAACqD,SAAS,EAAE;MAAExK,MAAM,CAAC4D,IAAI,CAAC,WAAW,CAAC;;IACtE,OAAO5D,MAAM,CAACkB,IAAI,CAAC,GAAG,CAAC;EAC3B;EAEA;;;EAGA,OAAOwJ,YAAYA,CAAC/D,IAAY,EAAEyD,MAAmB;IACjDA,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAE7H,GAAG,CAAE8H,CAAC,IAAK5E,SAAS,CAACpD,IAAI,CAACgI,CAAC,CAAC,CAAC;IACrD,MAAMM,QAAQ,GAAG,IAAIpB,aAAa,CAACvD,MAAM,EAAEW,IAAI,EAAEyD,MAAM,EAAE,KAAK,CAAC;IAC/D,OAAOO,QAAQ,CAACF,SAAS;EAC7B;EAEA;;;EAGA,OAAOpI,IAAIA,CAACqG,GAAQ;IAChB,IAAIa,aAAa,CAACM,UAAU,CAACnB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAE/C,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOa,aAAa,CAAClH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;OACtC,CAAC,OAAOE,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAGlE,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,MAAMgF,IAAI,GAAG1B,WAAW,CAAC,OAAO,EAAEyD,GAAG,CAAC;MACtC,MAAMS,MAAM,GAAG5D,aAAa,CAACmD,GAAG,EAAE,IAAI,CAAC;MACvC,MAAM8B,SAAS,GAAG,CAAC,CAACrF,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,WAAW,CAAE,CAAC,CAAC,CAACoD,GAAG,CAAC,WAAW,CAAC;MAClFyC,UAAU,CAAC+C,GAAG,CAAC;MAEf,OAAO,IAAIa,aAAa,CAACvD,MAAM,EAAEW,IAAI,EAAEwC,MAAM,EAAEqB,SAAS,CAAC;;IAG7D,OAAO,IAAIjB,aAAa,CAACvD,MAAM,EAAE0C,GAAG,CAAC/B,IAAI,EACrC+B,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,CAAC5G,GAAG,CAAE8H,CAAM,IAAK5E,SAAS,CAACpD,IAAI,CAACgI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAE,EAAG,EAAE,CAAC,CAAC3B,GAAG,CAAC8B,SAAS,CAAC;EAC/F;EAEA;;;;EAIA,OAAOX,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKK,qBAAqB;EAC9D;;AAGJ;;;AAGA,OAAM,MAAO+C,mBAAoB,SAAQH,QAAQ;EAE7C;;;EAGS0B,OAAO;EAEhB;;;EAGSC,GAAG;EAEZ;;;EAGA9I,YAAYkF,KAAU,EAAEhE,IAAkB,EAAEkG,MAAgC,EAAEyB,OAAgB,EAAEC,GAAkB;IAC9G,KAAK,CAAC5D,KAAK,EAAEhE,IAAI,EAAEkG,MAAM,CAAC;IAC1B9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE6B;IAA2B,CAAE,CAAC;IAC7EhH,gBAAgB,CAAsB,IAAI,EAAE;MAAEqL,OAAO;MAAEC;IAAG,CAAE,CAAC;EACjE;EAEA;;;EAGA1D,MAAMA,CAACA,MAAmB;IACtBzH,MAAM,CAACyH,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAK,SAAS,EAAE,yCAAyC,EACpF,uBAAuB,EAAE;MAAEwC,SAAS,EAAE;IAAiB,CAAE,CAAC;IAE9D,IAAIxC,MAAM,KAAK,MAAM,EAAE;MACnB,OAAO5D,IAAI,CAACC,SAAS,CAAC;QAClBP,IAAI,EAAE,aAAa;QACnB6H,eAAe,EAAG,IAAI,CAACF,OAAO,GAAG,SAAS,GAAE,WAAY;QACxDA,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,GAAG,EAAI,IAAI,CAACA,GAAG,IAAI,IAAI,GAAI,IAAI,CAACA,GAAG,GAAEE,SAAU;QAC/C5B,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC5G,GAAG,CAAEyB,CAAC,IAAKT,IAAI,CAAC8D,KAAK,CAACrD,CAAC,CAACmD,MAAM,CAACA,MAAM,CAAC,CAAC;OAC9D,CAAC;;IAGN,MAAMnH,MAAM,GAAG,CAAE,cAAemK,UAAU,CAAChD,MAAM,EAAE,IAAI,CAACgC,MAAM,CAAE,EAAE,CAAE;IACpE,IAAI,IAAI,CAACyB,OAAO,EAAE;MAAE5K,MAAM,CAAC4D,IAAI,CAAC,SAAS,CAAC;;IAC1C,IAAI,IAAI,CAACiH,GAAG,IAAI,IAAI,EAAE;MAAE7K,MAAM,CAAC4D,IAAI,CAAC,IAAK,IAAI,CAACiH,GAAG,CAAC9G,QAAQ,EAAG,EAAE,CAAC;;IAChE,OAAO/D,MAAM,CAACkB,IAAI,CAAC,GAAG,CAAC;EAC3B;EAEA;;;EAGA,OAAOmB,IAAIA,CAACqG,GAAQ;IAChB,IAAIW,mBAAmB,CAACQ,UAAU,CAACnB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAErD,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOW,mBAAmB,CAAChH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;OAC5C,CAAC,OAAOE,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,6BAA6B,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAGvE,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnCwD,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,aAAa,CAAE,CAAC,CAAC;MAC/C,MAAMqJ,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;MACjC,MAAMkC,OAAO,GAAG,CAAC,CAACzF,eAAe,CAACuD,GAAG,EAAElI,aAAa,CAAC,CAAC0C,GAAG,CAAC,SAAS,CAAC;MACpE,MAAM2H,GAAG,GAAGnF,UAAU,CAACgD,GAAG,CAAC;MAC3B/C,UAAU,CAAC+C,GAAG,CAAC;MAEf,OAAO,IAAIW,mBAAmB,CAACrD,MAAM,EAAE,aAAa,EAAEmD,MAAM,EAAEyB,OAAO,EAAEC,GAAG,CAAC;;IAG/E,OAAO,IAAIxB,mBAAmB,CAACrD,MAAM,EAAE,aAAa,EAChD0C,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,CAAC5G,GAAG,CAACkD,SAAS,CAACpD,IAAI,CAAC,GAAE,EAAG,EAChD,CAAC,CAACqG,GAAG,CAACkC,OAAO,EAAGlC,GAAG,CAACmC,GAAG,IAAI,IAAI,GAAInC,GAAG,CAACmC,GAAG,GAAE,IAAI,CAAC;EACzD;EAEA;;;;EAIA,OAAOhB,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKM,2BAA2B;EACpE;;AAGJ;;;AAGA,OAAM,MAAOiD,gBAAiB,SAAQN,QAAQ;EAE1C;;;EAGS0B,OAAO;EAEhB7I,YAAYkF,KAAU,EAAEkC,MAAgC,EAAEyB,OAAgB;IACtE,KAAK,CAAC3D,KAAK,EAAE,UAAU,EAAEkC,MAAM,CAAC;IAChC9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE8B;IAAwB,CAAE,CAAC;IAC1EjH,gBAAgB,CAAmB,IAAI,EAAE;MAAEqL;IAAO,CAAE,CAAC;EACzD;EAEA;;;EAGAzD,MAAMA,CAACA,MAAmB;IACtB,MAAMlE,IAAI,GAAK,IAAI,CAACkG,MAAM,CAACrH,MAAM,KAAK,CAAC,GAAI,SAAS,GAAE,UAAW;IAEjE,IAAIqF,MAAM,KAAK,MAAM,EAAE;MACnB,MAAM2D,eAAe,GAAI,IAAI,CAACF,OAAO,GAAG,SAAS,GAAE,YAAa;MAChE,OAAOrH,IAAI,CAACC,SAAS,CAAC;QAAEP,IAAI;QAAE6H;MAAe,CAAE,CAAC;;IAGpD,OAAO,GAAI7H,IAAK,KAAM,IAAI,CAAC2H,OAAO,GAAG,UAAU,GAAE,EAAG,EAAE;EAC1D;EAEA;;;EAGA,OAAOvI,IAAIA,CAACqG,GAAQ;IAChB,IAAIc,gBAAgB,CAACK,UAAU,CAACnB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAElD,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOc,gBAAgB,CAACnH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;OACzC,CAAC,OAAOE,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,2BAA2B,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAGrE,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,MAAMqJ,QAAQ,GAAGtC,GAAG,CAAC3E,QAAQ,EAAE;MAE/B,MAAMkH,UAAU,GAAGvC,GAAG,CAAC7E,WAAW,CAAC/D,MAAM,CAAC,CAAE,UAAU,EAAE,SAAS,CAAE,CAAC,CAAC;MACrEF,cAAc,CAACqL,UAAU,EAAE,kCAAkC,EAAE,KAAK,EAAED,QAAQ,CAAC;MAE/E,MAAM/H,IAAI,GAAGyF,GAAG,CAAC7F,UAAU,CAAC/C,MAAM,CAAC,CAAE,UAAU,EAAE,SAAS,CAAE,CAAC,CAAC;MAE9D;MACA,IAAImD,IAAI,KAAK,SAAS,EAAE;QACpB,MAAMkG,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;QACjC9I,cAAc,CAACuJ,MAAM,CAACrH,MAAM,KAAK,CAAC,EAAE,+BAA+B,EAAE,YAAY,EAAEqH,MAAM,CAAC;QAC1FhE,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,SAAS,CAAE,CAAC,CAAC;QAC3C6F,UAAU,CAAC+C,GAAG,CAAC;QACf,OAAO,IAAIc,gBAAgB,CAACxD,MAAM,EAAE,EAAG,EAAE,IAAI,CAAC;;MAGlD;MACA;MACA,IAAImD,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;MAC/B,IAAIS,MAAM,CAACrH,MAAM,EAAE;QACflC,cAAc,CAACuJ,MAAM,CAACrH,MAAM,KAAK,CAAC,IAAIqH,MAAM,CAAC,CAAC,CAAC,CAAClG,IAAI,KAAK,OAAO,EAC5D,yBAAyB,EAAE,YAAY,EACvCkG,MAAM,CAAC5G,GAAG,CAAEyB,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC,SAAS,CAAC,CAAC,CAACjG,IAAI,CAAC,IAAI,CAAC,CAAC;OACzD,MAAM;QACHiI,MAAM,GAAG,CAAE1D,SAAS,CAACpD,IAAI,CAAC,OAAO,CAAC,CAAE;;MAGxC,MAAM6I,UAAU,GAAG7F,iBAAiB,CAACqD,GAAG,CAAC;MACzC9I,cAAc,CAACsL,UAAU,KAAK,YAAY,IAAIA,UAAU,KAAK,SAAS,EAAE,8BAA8B,EAAE,qBAAqB,EAAEA,UAAU,CAAC;MAE1I,IAAI/F,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,SAAS,CAAE,CAAC,CAAC,CAACoD,GAAG,CAAC,SAAS,CAAC,EAAE;QAC5D,MAAMiI,OAAO,GAAG5F,aAAa,CAACmD,GAAG,CAAC;QAClC9I,cAAc,CAACuL,OAAO,CAACrJ,MAAM,KAAK,CAAC,IAAIqJ,OAAO,CAAC,CAAC,CAAC,CAAClI,IAAI,KAAK,OAAO,EAC9D,0BAA0B,EAAE,aAAa,EACzCkI,OAAO,CAAC5I,GAAG,CAAEyB,CAAC,IAAKA,CAAC,CAACmD,MAAM,CAAC,SAAS,CAAC,CAAC,CAACjG,IAAI,CAAC,IAAI,CAAC,CAAC;;MAG3DyE,UAAU,CAAC+C,GAAG,CAAC;MAEf,OAAO,IAAIc,gBAAgB,CAACxD,MAAM,EAAEmD,MAAM,EAAE+B,UAAU,KAAK,SAAS,CAAC;;IAGzE,IAAIxC,GAAG,CAACzF,IAAI,KAAK,SAAS,EAAE;MACxB,OAAO,IAAIuG,gBAAgB,CAACxD,MAAM,EAAE,EAAG,EAAE,IAAI,CAAC;;IAGlD,IAAI0C,GAAG,CAACzF,IAAI,KAAK,UAAU,EAAE;MACzB,MAAMkG,MAAM,GAAG,CAAE1D,SAAS,CAACpD,IAAI,CAAC,OAAO,CAAC,CAAE;MAC1C,MAAMuI,OAAO,GAAIlC,GAAG,CAACoC,eAAe,KAAK,SAAU;MACnD,OAAO,IAAItB,gBAAgB,CAACxD,MAAM,EAAEmD,MAAM,EAAEyB,OAAO,CAAC;;IAGxDhL,cAAc,CAAC,KAAK,EAAE,8BAA8B,EAAE,KAAK,EAAE8I,GAAG,CAAC;EACrE;EAEA;;;;EAIA,OAAOmB,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKO,wBAAwB;EACjE;;AAIJ;;;AAGA,OAAM,MAAOiD,gBAAiB,SAAQS,aAAa;EAC/C;;;EAGSkB,QAAQ;EAEjB;;;EAGSD,OAAO;EAEhB;;;;EAISL,eAAe;EAExB;;;EAGSF,OAAO;EAEhB;;;EAGSC,GAAG;EAEZ;;;EAGA9I,YAAYkF,KAAU,EAAEN,IAAY,EAAEmE,eAA2D,EAAE3B,MAAgC,EAAEgC,OAAiC,EAAEN,GAAkB;IACtL,KAAK,CAAC5D,KAAK,EAAE,UAAU,EAAEN,IAAI,EAAEwC,MAAM,CAAC;IACtC9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAE+B;IAAwB,CAAE,CAAC;IAC1E0E,OAAO,GAAG9K,MAAM,CAACC,MAAM,CAAC6K,OAAO,CAACnJ,KAAK,EAAE,CAAC;IACxC,MAAMoJ,QAAQ,GAAIN,eAAe,KAAK,MAAM,IAAIA,eAAe,KAAK,MAAO;IAC3E,MAAMF,OAAO,GAAIE,eAAe,KAAK,SAAU;IAC/CvL,gBAAgB,CAAmB,IAAI,EAAE;MAAE6L,QAAQ;MAAEP,GAAG;MAAEM,OAAO;MAAEP,OAAO;MAAEE;IAAe,CAAE,CAAC;EAClG;EAEA;;;EAGA,IAAIR,QAAQA,CAAA;IACR,OAAOzK,EAAE,CAAC,IAAI,CAACsH,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC3C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACtD;EAEA;;;EAGA2C,MAAMA,CAACA,MAAmB;IACtB,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,SAAS;;IACxC,IAAIA,MAAM,KAAK,MAAM,EAAE;MACnB,OAAO5D,IAAI,CAACC,SAAS,CAAC;QAClBP,IAAI,EAAE,UAAU;QAChB0D,IAAI,EAAE,IAAI,CAACA,IAAI;QACfyE,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBN,eAAe,EAAI,IAAI,CAACA,eAAe,KAAK,YAAY,GAAI,IAAI,CAACA,eAAe,GAAEC,SAAU;QAC5FH,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,GAAG,EAAI,IAAI,CAACA,GAAG,IAAI,IAAI,GAAI,IAAI,CAACA,GAAG,GAAEE,SAAU;QAC/C5B,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC5G,GAAG,CAAEyB,CAAC,IAAKT,IAAI,CAAC8D,KAAK,CAACrD,CAAC,CAACmD,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC;QAC5DgE,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC5I,GAAG,CAAE8I,CAAC,IAAK9H,IAAI,CAAC8D,KAAK,CAACgE,CAAC,CAAClE,MAAM,CAACA,MAAM,CAAC,CAAC;OAChE,CAAC;;IAGN,MAAMnH,MAAM,GAAkB,EAAE;IAEhC,IAAImH,MAAM,KAAK,SAAS,EAAE;MAAEnH,MAAM,CAAC4D,IAAI,CAAC,UAAU,CAAC;;IAEnD5D,MAAM,CAAC4D,IAAI,CAAC,IAAI,CAAC+C,IAAI,GAAGwD,UAAU,CAAChD,MAAM,EAAE,IAAI,CAACgC,MAAM,CAAC,CAAC;IAExD,IAAIhC,MAAM,KAAK,SAAS,EAAE;MACtB,IAAI,IAAI,CAAC2D,eAAe,KAAK,YAAY,EAAE;QACvC9K,MAAM,CAAC4D,IAAI,CAAC,IAAI,CAACkH,eAAe,CAAC;;MAGrC,IAAI,IAAI,CAACK,OAAO,IAAI,IAAI,CAACA,OAAO,CAACrJ,MAAM,EAAE;QACrC9B,MAAM,CAAC4D,IAAI,CAAC,SAAS,CAAC;QACtB5D,MAAM,CAAC4D,IAAI,CAACuG,UAAU,CAAChD,MAAM,EAAE,IAAI,CAACgE,OAAO,CAAC,CAAC;;MAGjD,IAAI,IAAI,CAACN,GAAG,IAAI,IAAI,EAAE;QAAE7K,MAAM,CAAC4D,IAAI,CAAC,IAAK,IAAI,CAACiH,GAAG,CAAC9G,QAAQ,EAAG,EAAE,CAAC;;;IAEpE,OAAO/D,MAAM,CAACkB,IAAI,CAAC,GAAG,CAAC;EAC3B;EAEA;;;EAGA,OAAOoK,WAAWA,CAAC3E,IAAY,EAAEyD,MAAmB;IAChDA,MAAM,GAAG,CAACA,MAAM,IAAI,EAAE,EAAE7H,GAAG,CAAE8H,CAAC,IAAK5E,SAAS,CAACpD,IAAI,CAACgI,CAAC,CAAC,CAAC;IACrD,MAAMM,QAAQ,GAAG,IAAIlB,gBAAgB,CAACzD,MAAM,EAAEW,IAAI,EAAE,MAAM,EAAEyD,MAAM,EAAE,EAAG,EAAE,IAAI,CAAC;IAC9E,OAAOO,QAAQ,CAACL,QAAQ;EAC5B;EAEA;;;EAGA,OAAOjI,IAAIA,CAACqG,GAAQ;IAChB,IAAIe,gBAAgB,CAACI,UAAU,CAACnB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;;IAElD,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOe,gBAAgB,CAACpH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;OACzC,CAAC,OAAOE,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,2BAA2B,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAGrE,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,MAAMgF,IAAI,GAAG1B,WAAW,CAAC,UAAU,EAAEyD,GAAG,CAAC;MACzC,MAAMS,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;MACjC,MAAMwC,UAAU,GAAG7F,iBAAiB,CAACqD,GAAG,CAAC;MAEzC,IAAIyC,OAAO,GAAqB,EAAG;MACnC,IAAIhG,eAAe,CAACuD,GAAG,EAAE5I,MAAM,CAAC,CAAE,SAAS,CAAE,CAAC,CAAC,CAACoD,GAAG,CAAC,SAAS,CAAC,EAAE;QAC5DiI,OAAO,GAAG5F,aAAa,CAACmD,GAAG,CAAC;;MAGhC,MAAMmC,GAAG,GAAGnF,UAAU,CAACgD,GAAG,CAAC;MAE3B/C,UAAU,CAAC+C,GAAG,CAAC;MAEf,OAAO,IAAIe,gBAAgB,CAACzD,MAAM,EAAEW,IAAI,EAAEuE,UAAU,EAAE/B,MAAM,EAAEgC,OAAO,EAAEN,GAAG,CAAC;;IAG/E,IAAIC,eAAe,GAAGpC,GAAG,CAACoC,eAAe;IAEzC;IACA,IAAIA,eAAe,IAAI,IAAI,EAAE;MACzBA,eAAe,GAAG,SAAS;MAE3B,IAAI,OAAOpC,GAAG,CAAC0C,QAAS,KAAK,SAAS,EAAE;QACpCN,eAAe,GAAG,MAAM;QACxB,IAAI,CAACpC,GAAG,CAAC0C,QAAQ,EAAE;UACfN,eAAe,GAAG,SAAS;UAC3B,IAAI,OAAOpC,GAAG,CAACkC,OAAQ,KAAK,SAAS,IAAI,CAAClC,GAAG,CAACkC,OAAO,EAAE;YACnDE,eAAe,GAAG,YAAY;;;OAGzC,MAAM,IAAI,OAAOpC,GAAG,CAACkC,OAAQ,KAAK,SAAS,IAAI,CAAClC,GAAG,CAACkC,OAAO,EAAE;QAC1DE,eAAe,GAAG,YAAY;;;IAItC;IACA;IAEA,OAAO,IAAIrB,gBAAgB,CAACzD,MAAM,EAAE0C,GAAG,CAAC/B,IAAI,EAAEmE,eAAe,EACxDpC,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,CAAC5G,GAAG,CAACkD,SAAS,CAACpD,IAAI,CAAC,GAAE,EAAG,EAChDqG,GAAG,CAACyC,OAAO,GAAGzC,GAAG,CAACyC,OAAO,CAAC5I,GAAG,CAACkD,SAAS,CAACpD,IAAI,CAAC,GAAE,EAAG,EACjDqG,GAAG,CAACmC,GAAG,IAAI,IAAI,GAAInC,GAAG,CAACmC,GAAG,GAAE,IAAI,CAAC;EAC3C;EAEA;;;;EAIA,OAAOhB,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKQ,wBAAwB;EACjE;;AAGJ;;;AAGA,OAAM,MAAOiD,cAAe,SAAQQ,aAAa;EAE7C;;;EAGAnI,YAAYkF,KAAU,EAAEN,IAAY,EAAEwC,MAAgC;IAClE,KAAK,CAAClC,KAAK,EAAE,QAAQ,EAAEN,IAAI,EAAEwC,MAAM,CAAC;IACpC9I,MAAM,CAAC6G,cAAc,CAAC,IAAI,EAAEjB,QAAQ,EAAE;MAAEvB,KAAK,EAAEgC;IAAsB,CAAE,CAAC;EAC5E;EAEA;;;EAGAS,MAAMA,CAAA;IACF,MAAM,IAAI/D,KAAK,CAAC,OAAO,CAAC;EAC5B;EAEA;;;EAGA,OAAOf,IAAIA,CAACqG,GAAQ;IAChB,IAAI,OAAOA,GAAI,KAAK,QAAQ,EAAE;MAC1B,IAAI;QACA,OAAOgB,cAAc,CAACrH,IAAI,CAAC6B,GAAG,CAACwE,GAAG,CAAC,CAAC;OACvC,CAAC,OAAOE,KAAK,EAAE;QACZhJ,cAAc,CAAC,KAAK,EAAE,yBAAyB,EAAE,KAAK,EAAE8I,GAAG,CAAC;;KAGnE,MAAM,IAAIA,GAAG,YAAY/G,WAAW,EAAE;MACnC,MAAMgF,IAAI,GAAG1B,WAAW,CAAC,QAAQ,EAAEyD,GAAG,CAAC;MACvC,MAAMS,MAAM,GAAG5D,aAAa,CAACmD,GAAG,CAAC;MACjC/C,UAAU,CAAC+C,GAAG,CAAC;MACf,OAAO,IAAIgB,cAAc,CAAC1D,MAAM,EAAEW,IAAI,EAAEwC,MAAM,CAAC;;IAGnD,OAAO,IAAIO,cAAc,CAAC1D,MAAM,EAAE0C,GAAG,CAAC/B,IAAI,EAAE+B,GAAG,CAACS,MAAM,GAAGT,GAAG,CAACS,MAAM,CAAC5G,GAAG,CAACkD,SAAS,CAACpD,IAAI,CAAC,GAAE,EAAG,CAAC;EACjG;EAEJ;EACI;;;;EAIA,OAAOwH,UAAUA,CAACnF,KAAU;IACxB,OAAQA,KAAK,IAAIA,KAAK,CAACuB,QAAQ,CAAC,KAAKS,sBAAsB;EAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}