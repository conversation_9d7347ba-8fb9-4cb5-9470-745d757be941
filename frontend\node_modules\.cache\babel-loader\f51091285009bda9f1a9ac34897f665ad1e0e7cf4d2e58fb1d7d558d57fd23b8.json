{"ast": null, "code": "/**\n *  Fetching content from the web is environment-specific, so E<PERSON>s\n *  provides an abstraction that each environment can implement to provide\n *  this service.\n *\n *  On [Node.js](link-node), the ``http`` and ``https`` libs are used to\n *  create a request object, register event listeners and process data\n *  and populate the [[FetchResponse]].\n *\n *  In a browser, the [DOM fetch](link-js-fetch) is used, and the resulting\n *  ``Promise`` is waited on to retrieve the payload.\n *\n *  The [[FetchRequest]] is responsible for handling many common situations,\n *  such as redirects, server throttling, authentication, etc.\n *\n *  It also handles common gateways, such as IPFS and data URIs.\n *\n *  @_section api/utils/fetching:Fetching Web Content  [about-fetch]\n */\nimport { decodeBase64, encodeBase64 } from \"./base64.js\";\nimport { hexlify } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\nimport { defineProperties } from \"./properties.js\";\nimport { toUtf8Bytes, toUtf8String } from \"./utf8.js\";\nimport { createGetUrl } from \"./geturl.js\";\nconst MAX_ATTEMPTS = 12;\nconst SLOT_INTERVAL = 250;\n// The global FetchGetUrlFunc implementation.\nlet defaultGetUrlFunc = createGetUrl();\nconst reData = new RegExp(\"^data:([^;:]*)?(;base64)?,(.*)$\", \"i\");\nconst reIpfs = new RegExp(\"^ipfs:/\\/(ipfs/)?(.*)$\", \"i\");\n// If locked, new Gateways cannot be added\nlet locked = false;\n// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs\nasync function dataGatewayFunc(url, signal) {\n  try {\n    const match = url.match(reData);\n    if (!match) {\n      throw new Error(\"invalid data\");\n    }\n    return new FetchResponse(200, \"OK\", {\n      \"content-type\": match[1] || \"text/plain\"\n    }, match[2] ? decodeBase64(match[3]) : unpercent(match[3]));\n  } catch (error) {\n    return new FetchResponse(599, \"BAD REQUEST (invalid data: URI)\", {}, null, new FetchRequest(url));\n  }\n}\n/**\n *  Returns a [[FetchGatewayFunc]] for fetching content from a standard\n *  IPFS gateway hosted at %%baseUrl%%.\n */\nfunction getIpfsGatewayFunc(baseUrl) {\n  async function gatewayIpfs(url, signal) {\n    try {\n      const match = url.match(reIpfs);\n      if (!match) {\n        throw new Error(\"invalid link\");\n      }\n      return new FetchRequest(`${baseUrl}${match[2]}`);\n    } catch (error) {\n      return new FetchResponse(599, \"BAD REQUEST (invalid IPFS URI)\", {}, null, new FetchRequest(url));\n    }\n  }\n  return gatewayIpfs;\n}\nconst Gateways = {\n  \"data\": dataGatewayFunc,\n  \"ipfs\": getIpfsGatewayFunc(\"https:/\\/gateway.ipfs.io/ipfs/\")\n};\nconst fetchSignals = new WeakMap();\n/**\n *  @_ignore\n */\nexport class FetchCancelSignal {\n  #listeners;\n  #cancelled;\n  constructor(request) {\n    this.#listeners = [];\n    this.#cancelled = false;\n    fetchSignals.set(request, () => {\n      if (this.#cancelled) {\n        return;\n      }\n      this.#cancelled = true;\n      for (const listener of this.#listeners) {\n        setTimeout(() => {\n          listener();\n        }, 0);\n      }\n      this.#listeners = [];\n    });\n  }\n  addListener(listener) {\n    assert(!this.#cancelled, \"singal already cancelled\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"fetchCancelSignal.addCancelListener\"\n    });\n    this.#listeners.push(listener);\n  }\n  get cancelled() {\n    return this.#cancelled;\n  }\n  checkSignal() {\n    assert(!this.cancelled, \"cancelled\", \"CANCELLED\", {});\n  }\n}\n// Check the signal, throwing if it is cancelled\nfunction checkSignal(signal) {\n  if (signal == null) {\n    throw new Error(\"missing signal; should not happen\");\n  }\n  signal.checkSignal();\n  return signal;\n}\n/**\n *  Represents a request for a resource using a URI.\n *\n *  By default, the supported schemes are ``HTTP``, ``HTTPS``, ``data:``,\n *  and ``IPFS:``.\n *\n *  Additional schemes can be added globally using [[registerGateway]].\n *\n *  @example:\n *    req = new FetchRequest(\"https://www.ricmoo.com\")\n *    resp = await req.send()\n *    resp.body.length\n *    //_result:\n */\nexport class FetchRequest {\n  #allowInsecure;\n  #gzip;\n  #headers;\n  #method;\n  #timeout;\n  #url;\n  #body;\n  #bodyType;\n  #creds;\n  // Hooks\n  #preflight;\n  #process;\n  #retry;\n  #signal;\n  #throttle;\n  #getUrlFunc;\n  /**\n   *  The fetch URL to request.\n   */\n  get url() {\n    return this.#url;\n  }\n  set url(url) {\n    this.#url = String(url);\n  }\n  /**\n   *  The fetch body, if any, to send as the request body. //(default: null)//\n   *\n   *  When setting a body, the intrinsic ``Content-Type`` is automatically\n   *  set and will be used if **not overridden** by setting a custom\n   *  header.\n   *\n   *  If %%body%% is null, the body is cleared (along with the\n   *  intrinsic ``Content-Type``).\n   *\n   *  If %%body%% is a string, the intrinsic ``Content-Type`` is set to\n   *  ``text/plain``.\n   *\n   *  If %%body%% is a Uint8Array, the intrinsic ``Content-Type`` is set to\n   *  ``application/octet-stream``.\n   *\n   *  If %%body%% is any other object, the intrinsic ``Content-Type`` is\n   *  set to ``application/json``.\n   */\n  get body() {\n    if (this.#body == null) {\n      return null;\n    }\n    return new Uint8Array(this.#body);\n  }\n  set body(body) {\n    if (body == null) {\n      this.#body = undefined;\n      this.#bodyType = undefined;\n    } else if (typeof body === \"string\") {\n      this.#body = toUtf8Bytes(body);\n      this.#bodyType = \"text/plain\";\n    } else if (body instanceof Uint8Array) {\n      this.#body = body;\n      this.#bodyType = \"application/octet-stream\";\n    } else if (typeof body === \"object\") {\n      this.#body = toUtf8Bytes(JSON.stringify(body));\n      this.#bodyType = \"application/json\";\n    } else {\n      throw new Error(\"invalid body\");\n    }\n  }\n  /**\n   *  Returns true if the request has a body.\n   */\n  hasBody() {\n    return this.#body != null;\n  }\n  /**\n   *  The HTTP method to use when requesting the URI. If no method\n   *  has been explicitly set, then ``GET`` is used if the body is\n   *  null and ``POST`` otherwise.\n   */\n  get method() {\n    if (this.#method) {\n      return this.#method;\n    }\n    if (this.hasBody()) {\n      return \"POST\";\n    }\n    return \"GET\";\n  }\n  set method(method) {\n    if (method == null) {\n      method = \"\";\n    }\n    this.#method = String(method).toUpperCase();\n  }\n  /**\n   *  The headers that will be used when requesting the URI. All\n   *  keys are lower-case.\n   *\n   *  This object is a copy, so any changes will **NOT** be reflected\n   *  in the ``FetchRequest``.\n   *\n   *  To set a header entry, use the ``setHeader`` method.\n   */\n  get headers() {\n    const headers = Object.assign({}, this.#headers);\n    if (this.#creds) {\n      headers[\"authorization\"] = `Basic ${encodeBase64(toUtf8Bytes(this.#creds))}`;\n    }\n    ;\n    if (this.allowGzip) {\n      headers[\"accept-encoding\"] = \"gzip\";\n    }\n    if (headers[\"content-type\"] == null && this.#bodyType) {\n      headers[\"content-type\"] = this.#bodyType;\n    }\n    if (this.body) {\n      headers[\"content-length\"] = String(this.body.length);\n    }\n    return headers;\n  }\n  /**\n   *  Get the header for %%key%%, ignoring case.\n   */\n  getHeader(key) {\n    return this.headers[key.toLowerCase()];\n  }\n  /**\n   *  Set the header for %%key%% to %%value%%. All values are coerced\n   *  to a string.\n   */\n  setHeader(key, value) {\n    this.#headers[String(key).toLowerCase()] = String(value);\n  }\n  /**\n   *  Clear all headers, resetting all intrinsic headers.\n   */\n  clearHeaders() {\n    this.#headers = {};\n  }\n  [Symbol.iterator]() {\n    const headers = this.headers;\n    const keys = Object.keys(headers);\n    let index = 0;\n    return {\n      next: () => {\n        if (index < keys.length) {\n          const key = keys[index++];\n          return {\n            value: [key, headers[key]],\n            done: false\n          };\n        }\n        return {\n          value: undefined,\n          done: true\n        };\n      }\n    };\n  }\n  /**\n   *  The value that will be sent for the ``Authorization`` header.\n   *\n   *  To set the credentials, use the ``setCredentials`` method.\n   */\n  get credentials() {\n    return this.#creds || null;\n  }\n  /**\n   *  Sets an ``Authorization`` for %%username%% with %%password%%.\n   */\n  setCredentials(username, password) {\n    assertArgument(!username.match(/:/), \"invalid basic authentication username\", \"username\", \"[REDACTED]\");\n    this.#creds = `${username}:${password}`;\n  }\n  /**\n   *  Enable and request gzip-encoded responses. The response will\n   *  automatically be decompressed. //(default: true)//\n   */\n  get allowGzip() {\n    return this.#gzip;\n  }\n  set allowGzip(value) {\n    this.#gzip = !!value;\n  }\n  /**\n   *  Allow ``Authentication`` credentials to be sent over insecure\n   *  channels. //(default: false)//\n   */\n  get allowInsecureAuthentication() {\n    return !!this.#allowInsecure;\n  }\n  set allowInsecureAuthentication(value) {\n    this.#allowInsecure = !!value;\n  }\n  /**\n   *  The timeout (in milliseconds) to wait for a complete response.\n   *  //(default: 5 minutes)//\n   */\n  get timeout() {\n    return this.#timeout;\n  }\n  set timeout(timeout) {\n    assertArgument(timeout >= 0, \"timeout must be non-zero\", \"timeout\", timeout);\n    this.#timeout = timeout;\n  }\n  /**\n   *  This function is called prior to each request, for example\n   *  during a redirection or retry in case of server throttling.\n   *\n   *  This offers an opportunity to populate headers or update\n   *  content before sending a request.\n   */\n  get preflightFunc() {\n    return this.#preflight || null;\n  }\n  set preflightFunc(preflight) {\n    this.#preflight = preflight;\n  }\n  /**\n   *  This function is called after each response, offering an\n   *  opportunity to provide client-level throttling or updating\n   *  response data.\n   *\n   *  Any error thrown in this causes the ``send()`` to throw.\n   *\n   *  To schedule a retry attempt (assuming the maximum retry limit\n   *  has not been reached), use [[response.throwThrottleError]].\n   */\n  get processFunc() {\n    return this.#process || null;\n  }\n  set processFunc(process) {\n    this.#process = process;\n  }\n  /**\n   *  This function is called on each retry attempt.\n   */\n  get retryFunc() {\n    return this.#retry || null;\n  }\n  set retryFunc(retry) {\n    this.#retry = retry;\n  }\n  /**\n   *  This function is called to fetch content from HTTP and\n   *  HTTPS URLs and is platform specific (e.g. nodejs vs\n   *  browsers).\n   *\n   *  This is by default the currently registered global getUrl\n   *  function, which can be changed using [[registerGetUrl]].\n   *  If this has been set, setting is to ``null`` will cause\n   *  this FetchRequest (and any future clones) to revert back to\n   *  using the currently registered global getUrl function.\n   *\n   *  Setting this is generally not necessary, but may be useful\n   *  for developers that wish to intercept requests or to\n   *  configurege a proxy or other agent.\n   */\n  get getUrlFunc() {\n    return this.#getUrlFunc || defaultGetUrlFunc;\n  }\n  set getUrlFunc(value) {\n    this.#getUrlFunc = value;\n  }\n  /**\n   *  Create a new FetchRequest instance with default values.\n   *\n   *  Once created, each property may be set before issuing a\n   *  ``.send()`` to make the request.\n   */\n  constructor(url) {\n    this.#url = String(url);\n    this.#allowInsecure = false;\n    this.#gzip = true;\n    this.#headers = {};\n    this.#method = \"\";\n    this.#timeout = 300000;\n    this.#throttle = {\n      slotInterval: SLOT_INTERVAL,\n      maxAttempts: MAX_ATTEMPTS\n    };\n    this.#getUrlFunc = null;\n  }\n  toString() {\n    return `<FetchRequest method=${JSON.stringify(this.method)} url=${JSON.stringify(this.url)} headers=${JSON.stringify(this.headers)} body=${this.#body ? hexlify(this.#body) : \"null\"}>`;\n  }\n  /**\n   *  Update the throttle parameters used to determine maximum\n   *  attempts and exponential-backoff properties.\n   */\n  setThrottleParams(params) {\n    if (params.slotInterval != null) {\n      this.#throttle.slotInterval = params.slotInterval;\n    }\n    if (params.maxAttempts != null) {\n      this.#throttle.maxAttempts = params.maxAttempts;\n    }\n  }\n  async #send(attempt, expires, delay, _request, _response) {\n    if (attempt >= this.#throttle.maxAttempts) {\n      return _response.makeServerError(\"exceeded maximum retry limit\");\n    }\n    assert(getTime() <= expires, \"timeout\", \"TIMEOUT\", {\n      operation: \"request.send\",\n      reason: \"timeout\",\n      request: _request\n    });\n    if (delay > 0) {\n      await wait(delay);\n    }\n    let req = this.clone();\n    const scheme = (req.url.split(\":\")[0] || \"\").toLowerCase();\n    // Process any Gateways\n    if (scheme in Gateways) {\n      const result = await Gateways[scheme](req.url, checkSignal(_request.#signal));\n      if (result instanceof FetchResponse) {\n        let response = result;\n        if (this.processFunc) {\n          checkSignal(_request.#signal);\n          try {\n            response = await this.processFunc(req, response);\n          } catch (error) {\n            // Something went wrong during processing; throw a 5xx server error\n            if (error.throttle == null || typeof error.stall !== \"number\") {\n              response.makeServerError(\"error in post-processing function\", error).assertOk();\n            }\n            // Ignore throttling\n          }\n        }\n        return response;\n      }\n      req = result;\n    }\n    // We have a preflight function; update the request\n    if (this.preflightFunc) {\n      req = await this.preflightFunc(req);\n    }\n    const resp = await this.getUrlFunc(req, checkSignal(_request.#signal));\n    let response = new FetchResponse(resp.statusCode, resp.statusMessage, resp.headers, resp.body, _request);\n    if (response.statusCode === 301 || response.statusCode === 302) {\n      // Redirect\n      try {\n        const location = response.headers.location || \"\";\n        return req.redirect(location).#send(attempt + 1, expires, 0, _request, response);\n      } catch (error) {}\n      // Things won't get any better on another attempt; abort\n      return response;\n    } else if (response.statusCode === 429) {\n      // Throttle\n      if (this.retryFunc == null || (await this.retryFunc(req, response, attempt))) {\n        const retryAfter = response.headers[\"retry-after\"];\n        let delay = this.#throttle.slotInterval * Math.trunc(Math.random() * Math.pow(2, attempt));\n        if (typeof retryAfter === \"string\" && retryAfter.match(/^[1-9][0-9]*$/)) {\n          delay = parseInt(retryAfter);\n        }\n        return req.clone().#send(attempt + 1, expires, delay, _request, response);\n      }\n    }\n    if (this.processFunc) {\n      checkSignal(_request.#signal);\n      try {\n        response = await this.processFunc(req, response);\n      } catch (error) {\n        // Something went wrong during processing; throw a 5xx server error\n        if (error.throttle == null || typeof error.stall !== \"number\") {\n          response.makeServerError(\"error in post-processing function\", error).assertOk();\n        }\n        // Throttle\n        let delay = this.#throttle.slotInterval * Math.trunc(Math.random() * Math.pow(2, attempt));\n        ;\n        if (error.stall >= 0) {\n          delay = error.stall;\n        }\n        return req.clone().#send(attempt + 1, expires, delay, _request, response);\n      }\n    }\n    return response;\n  }\n  /**\n   *  Resolves to the response by sending the request.\n   */\n  send() {\n    assert(this.#signal == null, \"request already sent\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"fetchRequest.send\"\n    });\n    this.#signal = new FetchCancelSignal(this);\n    return this.#send(0, getTime() + this.timeout, 0, this, new FetchResponse(0, \"\", {}, null, this));\n  }\n  /**\n   *  Cancels the inflight response, causing a ``CANCELLED``\n   *  error to be rejected from the [[send]].\n   */\n  cancel() {\n    assert(this.#signal != null, \"request has not been sent\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"fetchRequest.cancel\"\n    });\n    const signal = fetchSignals.get(this);\n    if (!signal) {\n      throw new Error(\"missing signal; should not happen\");\n    }\n    signal();\n  }\n  /**\n   *  Returns a new [[FetchRequest]] that represents the redirection\n   *  to %%location%%.\n   */\n  redirect(location) {\n    // Redirection; for now we only support absolute locations\n    const current = this.url.split(\":\")[0].toLowerCase();\n    const target = location.split(\":\")[0].toLowerCase();\n    // Don't allow redirecting:\n    // - non-GET requests\n    // - downgrading the security (e.g. https => http)\n    // - to non-HTTP (or non-HTTPS) protocols [this could be relaxed?]\n    assert(this.method === \"GET\" && (current !== \"https\" || target !== \"http\") && location.match(/^https?:/), `unsupported redirect`, \"UNSUPPORTED_OPERATION\", {\n      operation: `redirect(${this.method} ${JSON.stringify(this.url)} => ${JSON.stringify(location)})`\n    });\n    // Create a copy of this request, with a new URL\n    const req = new FetchRequest(location);\n    req.method = \"GET\";\n    req.allowGzip = this.allowGzip;\n    req.timeout = this.timeout;\n    req.#headers = Object.assign({}, this.#headers);\n    if (this.#body) {\n      req.#body = new Uint8Array(this.#body);\n    }\n    req.#bodyType = this.#bodyType;\n    // Do not forward credentials unless on the same domain; only absolute\n    //req.allowInsecure = false;\n    // paths are currently supported; may want a way to specify to forward?\n    //setStore(req.#props, \"creds\", getStore(this.#pros, \"creds\"));\n    return req;\n  }\n  /**\n   *  Create a new copy of this request.\n   */\n  clone() {\n    const clone = new FetchRequest(this.url);\n    // Preserve \"default method\" (i.e. null)\n    clone.#method = this.#method;\n    // Preserve \"default body\" with type, copying the Uint8Array is present\n    if (this.#body) {\n      clone.#body = this.#body;\n    }\n    clone.#bodyType = this.#bodyType;\n    // Preserve \"default headers\"\n    clone.#headers = Object.assign({}, this.#headers);\n    // Credentials is readonly, so we copy internally\n    clone.#creds = this.#creds;\n    if (this.allowGzip) {\n      clone.allowGzip = true;\n    }\n    clone.timeout = this.timeout;\n    if (this.allowInsecureAuthentication) {\n      clone.allowInsecureAuthentication = true;\n    }\n    clone.#preflight = this.#preflight;\n    clone.#process = this.#process;\n    clone.#retry = this.#retry;\n    clone.#throttle = Object.assign({}, this.#throttle);\n    clone.#getUrlFunc = this.#getUrlFunc;\n    return clone;\n  }\n  /**\n   *  Locks all static configuration for gateways and FetchGetUrlFunc\n   *  registration.\n   */\n  static lockConfig() {\n    locked = true;\n  }\n  /**\n   *  Get the current Gateway function for %%scheme%%.\n   */\n  static getGateway(scheme) {\n    return Gateways[scheme.toLowerCase()] || null;\n  }\n  /**\n   *  Use the %%func%% when fetching URIs using %%scheme%%.\n   *\n   *  This method affects all requests globally.\n   *\n   *  If [[lockConfig]] has been called, no change is made and this\n   *  throws.\n   */\n  static registerGateway(scheme, func) {\n    scheme = scheme.toLowerCase();\n    if (scheme === \"http\" || scheme === \"https\") {\n      throw new Error(`cannot intercept ${scheme}; use registerGetUrl`);\n    }\n    if (locked) {\n      throw new Error(\"gateways locked\");\n    }\n    Gateways[scheme] = func;\n  }\n  /**\n   *  Use %%getUrl%% when fetching URIs over HTTP and HTTPS requests.\n   *\n   *  This method affects all requests globally.\n   *\n   *  If [[lockConfig]] has been called, no change is made and this\n   *  throws.\n   */\n  static registerGetUrl(getUrl) {\n    if (locked) {\n      throw new Error(\"gateways locked\");\n    }\n    defaultGetUrlFunc = getUrl;\n  }\n  /**\n   *  Creates a getUrl function that fetches content from HTTP and\n   *  HTTPS URLs.\n   *\n   *  The available %%options%% are dependent on the platform\n   *  implementation of the default getUrl function.\n   *\n   *  This is not generally something that is needed, but is useful\n   *  when trying to customize simple behaviour when fetching HTTP\n   *  content.\n   */\n  static createGetUrlFunc(options) {\n    return createGetUrl(options);\n  }\n  /**\n   *  Creates a function that can \"fetch\" data URIs.\n   *\n   *  Note that this is automatically done internally to support\n   *  data URIs, so it is not necessary to register it.\n   *\n   *  This is not generally something that is needed, but may\n   *  be useful in a wrapper to perfom custom data URI functionality.\n   */\n  static createDataGateway() {\n    return dataGatewayFunc;\n  }\n  /**\n   *  Creates a function that will fetch IPFS (unvalidated) from\n   *  a custom gateway baseUrl.\n   *\n   *  The default IPFS gateway used internally is\n   *  ``\"https:/\\/gateway.ipfs.io/ipfs/\"``.\n   */\n  static createIpfsGatewayFunc(baseUrl) {\n    return getIpfsGatewayFunc(baseUrl);\n  }\n}\n;\n/**\n *  The response for a FetchRequest.\n */\nexport class FetchResponse {\n  #statusCode;\n  #statusMessage;\n  #headers;\n  #body;\n  #request;\n  #error;\n  toString() {\n    return `<FetchResponse status=${this.statusCode} body=${this.#body ? hexlify(this.#body) : \"null\"}>`;\n  }\n  /**\n   *  The response status code.\n   */\n  get statusCode() {\n    return this.#statusCode;\n  }\n  /**\n   *  The response status message.\n   */\n  get statusMessage() {\n    return this.#statusMessage;\n  }\n  /**\n   *  The response headers. All keys are lower-case.\n   */\n  get headers() {\n    return Object.assign({}, this.#headers);\n  }\n  /**\n   *  The response body, or ``null`` if there was no body.\n   */\n  get body() {\n    return this.#body == null ? null : new Uint8Array(this.#body);\n  }\n  /**\n   *  The response body as a UTF-8 encoded string, or the empty\n   *  string (i.e. ``\"\"``) if there was no body.\n   *\n   *  An error is thrown if the body is invalid UTF-8 data.\n   */\n  get bodyText() {\n    try {\n      return this.#body == null ? \"\" : toUtf8String(this.#body);\n    } catch (error) {\n      assert(false, \"response body is not valid UTF-8 data\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"bodyText\",\n        info: {\n          response: this\n        }\n      });\n    }\n  }\n  /**\n   *  The response body, decoded as JSON.\n   *\n   *  An error is thrown if the body is invalid JSON-encoded data\n   *  or if there was no body.\n   */\n  get bodyJson() {\n    try {\n      return JSON.parse(this.bodyText);\n    } catch (error) {\n      assert(false, \"response body is not valid JSON\", \"UNSUPPORTED_OPERATION\", {\n        operation: \"bodyJson\",\n        info: {\n          response: this\n        }\n      });\n    }\n  }\n  [Symbol.iterator]() {\n    const headers = this.headers;\n    const keys = Object.keys(headers);\n    let index = 0;\n    return {\n      next: () => {\n        if (index < keys.length) {\n          const key = keys[index++];\n          return {\n            value: [key, headers[key]],\n            done: false\n          };\n        }\n        return {\n          value: undefined,\n          done: true\n        };\n      }\n    };\n  }\n  constructor(statusCode, statusMessage, headers, body, request) {\n    this.#statusCode = statusCode;\n    this.#statusMessage = statusMessage;\n    this.#headers = Object.keys(headers).reduce((accum, k) => {\n      accum[k.toLowerCase()] = String(headers[k]);\n      return accum;\n    }, {});\n    this.#body = body == null ? null : new Uint8Array(body);\n    this.#request = request || null;\n    this.#error = {\n      message: \"\"\n    };\n  }\n  /**\n   *  Return a Response with matching headers and body, but with\n   *  an error status code (i.e. 599) and %%message%% with an\n   *  optional %%error%%.\n   */\n  makeServerError(message, error) {\n    let statusMessage;\n    if (!message) {\n      message = `${this.statusCode} ${this.statusMessage}`;\n      statusMessage = `CLIENT ESCALATED SERVER ERROR (${message})`;\n    } else {\n      statusMessage = `CLIENT ESCALATED SERVER ERROR (${this.statusCode} ${this.statusMessage}; ${message})`;\n    }\n    const response = new FetchResponse(599, statusMessage, this.headers, this.body, this.#request || undefined);\n    response.#error = {\n      message,\n      error\n    };\n    return response;\n  }\n  /**\n   *  If called within a [request.processFunc](FetchRequest-processFunc)\n   *  call, causes the request to retry as if throttled for %%stall%%\n   *  milliseconds.\n   */\n  throwThrottleError(message, stall) {\n    if (stall == null) {\n      stall = -1;\n    } else {\n      assertArgument(Number.isInteger(stall) && stall >= 0, \"invalid stall timeout\", \"stall\", stall);\n    }\n    const error = new Error(message || \"throttling requests\");\n    defineProperties(error, {\n      stall,\n      throttle: true\n    });\n    throw error;\n  }\n  /**\n   *  Get the header value for %%key%%, ignoring case.\n   */\n  getHeader(key) {\n    return this.headers[key.toLowerCase()];\n  }\n  /**\n   *  Returns true if the response has a body.\n   */\n  hasBody() {\n    return this.#body != null;\n  }\n  /**\n   *  The request made for this response.\n   */\n  get request() {\n    return this.#request;\n  }\n  /**\n   *  Returns true if this response was a success statusCode.\n   */\n  ok() {\n    return this.#error.message === \"\" && this.statusCode >= 200 && this.statusCode < 300;\n  }\n  /**\n   *  Throws a ``SERVER_ERROR`` if this response is not ok.\n   */\n  assertOk() {\n    if (this.ok()) {\n      return;\n    }\n    let {\n      message,\n      error\n    } = this.#error;\n    if (message === \"\") {\n      message = `server response ${this.statusCode} ${this.statusMessage}`;\n    }\n    let requestUrl = null;\n    if (this.request) {\n      requestUrl = this.request.url;\n    }\n    let responseBody = null;\n    try {\n      if (this.#body) {\n        responseBody = toUtf8String(this.#body);\n      }\n    } catch (e) {}\n    assert(false, message, \"SERVER_ERROR\", {\n      request: this.request || \"unknown request\",\n      response: this,\n      error,\n      info: {\n        requestUrl,\n        responseBody,\n        responseStatus: `${this.statusCode} ${this.statusMessage}`\n      }\n    });\n  }\n}\nfunction getTime() {\n  return new Date().getTime();\n}\nfunction unpercent(value) {\n  return toUtf8Bytes(value.replace(/%([0-9a-f][0-9a-f])/gi, (all, code) => {\n    return String.fromCharCode(parseInt(code, 16));\n  }));\n}\nfunction wait(delay) {\n  return new Promise(resolve => setTimeout(resolve, delay));\n}", "map": {"version": 3, "names": ["decodeBase64", "encodeBase64", "hexlify", "assert", "assertArgument", "defineProperties", "toUtf8Bytes", "toUtf8String", "createGetUrl", "MAX_ATTEMPTS", "SLOT_INTERVAL", "defaultGetUrlFunc", "reData", "RegExp", "reIpfs", "locked", "dataGatewayFunc", "url", "signal", "match", "Error", "FetchResponse", "unpercent", "error", "FetchRequest", "getIpfsGatewayFunc", "baseUrl", "gatewayIpfs", "Gateways", "fetchSignals", "WeakMap", "FetchCancelSignal", "listeners", "cancelled", "constructor", "request", "set", "listener", "setTimeout", "addListener", "operation", "push", "checkSignal", "allowInsecure", "gzip", "headers", "method", "timeout", "body", "bodyType", "creds", "preflight", "process", "retry", "throttle", "getUrlFunc", "String", "Uint8Array", "undefined", "JSON", "stringify", "hasBody", "toUpperCase", "Object", "assign", "allowGzip", "length", "<PERSON><PERSON><PERSON><PERSON>", "key", "toLowerCase", "<PERSON><PERSON><PERSON><PERSON>", "value", "clearHeaders", "Symbol", "iterator", "keys", "index", "next", "done", "credentials", "setCredentials", "username", "password", "allowInsecureAuthentication", "preflightFunc", "processFunc", "retryFunc", "slotInterval", "maxAttempts", "toString", "setThrottleParams", "params", "send", "#send", "attempt", "expires", "delay", "_request", "_response", "makeServerError", "getTime", "reason", "wait", "req", "clone", "scheme", "split", "result", "response", "stall", "assertOk", "resp", "statusCode", "statusMessage", "location", "redirect", "retryAfter", "Math", "trunc", "random", "pow", "parseInt", "cancel", "get", "current", "target", "lockConfig", "getGateway", "registerGateway", "func", "registerGetUrl", "getUrl", "createGetUrlFunc", "options", "createDataGateway", "createIpfsGatewayFunc", "bodyText", "info", "bodyJson", "parse", "reduce", "accum", "k", "message", "throwThrottleError", "Number", "isInteger", "ok", "requestUrl", "responseBody", "e", "responseStatus", "Date", "replace", "all", "code", "fromCharCode", "Promise", "resolve"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\utils\\fetch.ts"], "sourcesContent": ["/**\n *  Fetching content from the web is environment-specific, so <PERSON><PERSON>s\n *  provides an abstraction that each environment can implement to provide\n *  this service.\n *\n *  On [Node.js](link-node), the ``http`` and ``https`` libs are used to\n *  create a request object, register event listeners and process data\n *  and populate the [[FetchResponse]].\n *\n *  In a browser, the [DOM fetch](link-js-fetch) is used, and the resulting\n *  ``Promise`` is waited on to retrieve the payload.\n *\n *  The [[FetchRequest]] is responsible for handling many common situations,\n *  such as redirects, server throttling, authentication, etc.\n *\n *  It also handles common gateways, such as IPFS and data URIs.\n *\n *  @_section api/utils/fetching:Fetching Web Content  [about-fetch]\n */\nimport { decodeBase64, encodeBase64 } from \"./base64.js\";\nimport { hexlify } from \"./data.js\";\nimport { assert, assertArgument } from \"./errors.js\";\nimport { defineProperties } from \"./properties.js\";\nimport { toUtf8Bytes, toUtf8String } from \"./utf8.js\";\n\nimport { createGetUrl } from \"./geturl.js\";\n\n/**\n *  An environment's implementation of ``getUrl`` must return this type.\n */\nexport type GetUrlResponse = {\n    statusCode: number,\n    statusMessage: string,\n    headers: Record<string, string>,\n    body: null | Uint8Array\n};\n\n/**\n *  This can be used to control how throttling is handled in\n *  [[FetchRequest-setThrottleParams]].\n */\nexport type FetchThrottleParams = {\n    maxAttempts?: number;\n    slotInterval?: number;\n};\n\n/**\n *  Called before any network request, allowing updated headers (e.g. Bearer tokens), etc.\n */\nexport type FetchPreflightFunc = (req: FetchRequest) => Promise<FetchRequest>;\n\n/**\n *  Called on the response, allowing client-based throttling logic or post-processing.\n */\nexport type FetchProcessFunc = (req: FetchRequest, resp: FetchResponse) => Promise<FetchResponse>;\n\n/**\n *  Called prior to each retry; return true to retry, false to abort.\n */\nexport type FetchRetryFunc = (req: FetchRequest, resp: FetchResponse, attempt: number) => Promise<boolean>;\n\n/**\n *  Called on Gateway URLs.\n */\nexport type FetchGatewayFunc = (url: string, signal?: FetchCancelSignal) => Promise<FetchRequest | FetchResponse>;\n\n/**\n *  Used to perform a fetch; use this to override the underlying network\n *  fetch layer. In NodeJS, the default uses the \"http\" and \"https\" libraries\n *  and in the browser ``fetch`` is used. If you wish to use Axios, this is\n *  how you would register it.\n */\nexport type FetchGetUrlFunc = (req: FetchRequest, signal?: FetchCancelSignal) => Promise<GetUrlResponse>;\n\n\nconst MAX_ATTEMPTS = 12;\nconst SLOT_INTERVAL = 250;\n\n// The global FetchGetUrlFunc implementation.\nlet defaultGetUrlFunc: FetchGetUrlFunc = createGetUrl();\n\nconst reData = new RegExp(\"^data:([^;:]*)?(;base64)?,(.*)$\", \"i\");\nconst reIpfs = new RegExp(\"^ipfs:/\\/(ipfs/)?(.*)$\", \"i\");\n\n// If locked, new Gateways cannot be added\nlet locked = false;\n\n// https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URLs\nasync function dataGatewayFunc(url: string, signal?: FetchCancelSignal): Promise<FetchResponse> {\n    try {\n        const match = url.match(reData);\n        if (!match) { throw new Error(\"invalid data\"); }\n        return new FetchResponse(200, \"OK\", {\n            \"content-type\": (match[1] || \"text/plain\"),\n        }, (match[2] ? decodeBase64(match[3]): unpercent(match[3])));\n    } catch (error) {\n        return new FetchResponse(599, \"BAD REQUEST (invalid data: URI)\", { }, null, new FetchRequest(url));\n    }\n}\n\n/**\n *  Returns a [[FetchGatewayFunc]] for fetching content from a standard\n *  IPFS gateway hosted at %%baseUrl%%.\n */\nfunction getIpfsGatewayFunc(baseUrl: string): FetchGatewayFunc {\n    async function gatewayIpfs(url: string, signal?: FetchCancelSignal): Promise<FetchRequest | FetchResponse> {\n        try {\n            const match = url.match(reIpfs);\n            if (!match) { throw new Error(\"invalid link\"); }\n            return new FetchRequest(`${ baseUrl }${ match[2] }`);\n        } catch (error) {\n            return new FetchResponse(599, \"BAD REQUEST (invalid IPFS URI)\", { }, null, new FetchRequest(url));\n        }\n    }\n\n    return gatewayIpfs;\n}\n\nconst Gateways: Record<string, FetchGatewayFunc> = {\n    \"data\": dataGatewayFunc,\n    \"ipfs\": getIpfsGatewayFunc(\"https:/\\/gateway.ipfs.io/ipfs/\")\n};\n\nconst fetchSignals: WeakMap<FetchRequest, () => void> = new WeakMap();\n\n/**\n *  @_ignore\n */\nexport class FetchCancelSignal {\n    #listeners: Array<() => void>;\n    #cancelled: boolean;\n\n    constructor(request: FetchRequest) {\n        this.#listeners = [ ];\n        this.#cancelled = false;\n\n        fetchSignals.set(request, () => {\n            if (this.#cancelled) { return; }\n            this.#cancelled = true;\n\n            for (const listener of this.#listeners) {\n                setTimeout(() => { listener(); }, 0);\n            }\n            this.#listeners = [ ];\n        });\n    }\n\n    addListener(listener: () => void): void {\n        assert(!this.#cancelled, \"singal already cancelled\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"fetchCancelSignal.addCancelListener\"\n        });\n        this.#listeners.push(listener);\n    }\n\n    get cancelled(): boolean { return this.#cancelled; }\n\n    checkSignal(): void {\n        assert(!this.cancelled, \"cancelled\", \"CANCELLED\", { });\n    }\n}\n\n// Check the signal, throwing if it is cancelled\nfunction checkSignal(signal?: FetchCancelSignal): FetchCancelSignal {\n    if (signal == null) { throw new Error(\"missing signal; should not happen\"); }\n    signal.checkSignal();\n    return signal;\n}\n\n/**\n *  Represents a request for a resource using a URI.\n *\n *  By default, the supported schemes are ``HTTP``, ``HTTPS``, ``data:``,\n *  and ``IPFS:``.\n *\n *  Additional schemes can be added globally using [[registerGateway]].\n *\n *  @example:\n *    req = new FetchRequest(\"https://www.ricmoo.com\")\n *    resp = await req.send()\n *    resp.body.length\n *    //_result:\n */\nexport class FetchRequest implements Iterable<[ key: string, value: string ]> {\n    #allowInsecure: boolean;\n    #gzip: boolean;\n    #headers: Record<string, string>;\n    #method: string;\n    #timeout: number;\n    #url: string;\n\n    #body?: Uint8Array;\n    #bodyType?: string;\n    #creds?: string;\n\n    // Hooks\n    #preflight?: null | FetchPreflightFunc;\n    #process?: null | FetchProcessFunc;\n    #retry?: null | FetchRetryFunc;\n\n    #signal?: FetchCancelSignal;\n\n    #throttle: Required<FetchThrottleParams>;\n\n    #getUrlFunc: null | FetchGetUrlFunc;\n\n    /**\n     *  The fetch URL to request.\n     */\n    get url(): string { return this.#url; }\n    set url(url: string) {\n        this.#url = String(url);\n    }\n\n    /**\n     *  The fetch body, if any, to send as the request body. //(default: null)//\n     *\n     *  When setting a body, the intrinsic ``Content-Type`` is automatically\n     *  set and will be used if **not overridden** by setting a custom\n     *  header.\n     *\n     *  If %%body%% is null, the body is cleared (along with the\n     *  intrinsic ``Content-Type``).\n     *\n     *  If %%body%% is a string, the intrinsic ``Content-Type`` is set to\n     *  ``text/plain``.\n     *\n     *  If %%body%% is a Uint8Array, the intrinsic ``Content-Type`` is set to\n     *  ``application/octet-stream``.\n     *\n     *  If %%body%% is any other object, the intrinsic ``Content-Type`` is\n     *  set to ``application/json``.\n     */\n    get body(): null | Uint8Array {\n        if (this.#body == null) { return null; }\n        return new Uint8Array(this.#body);\n    }\n    set body(body: null | string | Readonly<object> | Readonly<Uint8Array>) {\n        if (body == null) {\n            this.#body = undefined;\n            this.#bodyType = undefined;\n        } else if (typeof(body) === \"string\") {\n            this.#body = toUtf8Bytes(body);\n            this.#bodyType = \"text/plain\";\n        } else if (body instanceof Uint8Array) {\n            this.#body = body;\n            this.#bodyType = \"application/octet-stream\";\n        } else if (typeof(body) === \"object\") {\n            this.#body = toUtf8Bytes(JSON.stringify(body));\n            this.#bodyType = \"application/json\";\n        } else {\n            throw new Error(\"invalid body\");\n        }\n    }\n\n    /**\n     *  Returns true if the request has a body.\n     */\n    hasBody(): this is (FetchRequest & { body: Uint8Array }) {\n        return (this.#body != null);\n    }\n\n    /**\n     *  The HTTP method to use when requesting the URI. If no method\n     *  has been explicitly set, then ``GET`` is used if the body is\n     *  null and ``POST`` otherwise.\n     */\n    get method(): string {\n        if (this.#method) { return this.#method; }\n        if (this.hasBody()) { return \"POST\"; }\n        return \"GET\";\n    }\n    set method(method: null | string) {\n        if (method == null) { method = \"\"; }\n        this.#method = String(method).toUpperCase();\n    }\n\n    /**\n     *  The headers that will be used when requesting the URI. All\n     *  keys are lower-case.\n     *\n     *  This object is a copy, so any changes will **NOT** be reflected\n     *  in the ``FetchRequest``.\n     *\n     *  To set a header entry, use the ``setHeader`` method.\n     */\n    get headers(): Record<string, string> {\n        const headers = Object.assign({ }, this.#headers);\n\n        if (this.#creds) {\n            headers[\"authorization\"] = `Basic ${ encodeBase64(toUtf8Bytes(this.#creds)) }`;\n        };\n\n        if (this.allowGzip) {\n            headers[\"accept-encoding\"] = \"gzip\";\n        }\n\n        if (headers[\"content-type\"] == null && this.#bodyType) {\n            headers[\"content-type\"] = this.#bodyType;\n        }\n        if (this.body) { headers[\"content-length\"] = String(this.body.length); }\n\n        return headers;\n    }\n\n    /**\n     *  Get the header for %%key%%, ignoring case.\n     */\n    getHeader(key: string): string {\n        return this.headers[key.toLowerCase()];\n    }\n\n    /**\n     *  Set the header for %%key%% to %%value%%. All values are coerced\n     *  to a string.\n     */\n    setHeader(key: string, value: string | number): void {\n        this.#headers[String(key).toLowerCase()] = String(value);\n    }\n\n    /**\n     *  Clear all headers, resetting all intrinsic headers.\n     */\n    clearHeaders(): void {\n        this.#headers = { };\n    }\n\n    [Symbol.iterator](): Iterator<[ key: string, value: string ]> {\n        const headers = this.headers;\n        const keys = Object.keys(headers);\n        let index = 0;\n        return {\n            next: () => {\n                if (index < keys.length) {\n                    const key = keys[index++];\n                    return {\n                        value: [ key, headers[key] ], done: false\n                    }\n                }\n                return { value: undefined, done: true };\n            }\n        };\n    }\n\n    /**\n     *  The value that will be sent for the ``Authorization`` header.\n     *\n     *  To set the credentials, use the ``setCredentials`` method.\n     */\n    get credentials(): null | string {\n        return this.#creds || null;\n    }\n\n    /**\n     *  Sets an ``Authorization`` for %%username%% with %%password%%.\n     */\n    setCredentials(username: string, password: string): void {\n        assertArgument(!username.match(/:/), \"invalid basic authentication username\", \"username\", \"[REDACTED]\");\n        this.#creds = `${ username }:${ password }`;\n    }\n\n    /**\n     *  Enable and request gzip-encoded responses. The response will\n     *  automatically be decompressed. //(default: true)//\n     */\n    get allowGzip(): boolean {\n        return this.#gzip;\n    }\n    set allowGzip(value: boolean) {\n        this.#gzip = !!value;\n    }\n\n    /**\n     *  Allow ``Authentication`` credentials to be sent over insecure\n     *  channels. //(default: false)//\n     */\n    get allowInsecureAuthentication(): boolean {\n        return !!this.#allowInsecure;\n    }\n    set allowInsecureAuthentication(value: boolean) {\n        this.#allowInsecure = !!value;\n    }\n\n    /**\n     *  The timeout (in milliseconds) to wait for a complete response.\n     *  //(default: 5 minutes)//\n     */\n    get timeout(): number { return this.#timeout; }\n    set timeout(timeout: number) {\n        assertArgument(timeout >= 0, \"timeout must be non-zero\", \"timeout\", timeout);\n        this.#timeout = timeout;\n    }\n\n    /**\n     *  This function is called prior to each request, for example\n     *  during a redirection or retry in case of server throttling.\n     *\n     *  This offers an opportunity to populate headers or update\n     *  content before sending a request.\n     */\n    get preflightFunc(): null | FetchPreflightFunc {\n        return this.#preflight || null;\n    }\n    set preflightFunc(preflight: null | FetchPreflightFunc) {\n        this.#preflight = preflight;\n    }\n\n    /**\n     *  This function is called after each response, offering an\n     *  opportunity to provide client-level throttling or updating\n     *  response data.\n     *\n     *  Any error thrown in this causes the ``send()`` to throw.\n     *\n     *  To schedule a retry attempt (assuming the maximum retry limit\n     *  has not been reached), use [[response.throwThrottleError]].\n     */\n    get processFunc(): null | FetchProcessFunc {\n        return this.#process || null;\n    }\n    set processFunc(process: null | FetchProcessFunc) {\n        this.#process = process;\n    }\n\n    /**\n     *  This function is called on each retry attempt.\n     */\n    get retryFunc(): null | FetchRetryFunc {\n        return this.#retry || null;\n    }\n    set retryFunc(retry: null | FetchRetryFunc) {\n        this.#retry = retry;\n    }\n\n    /**\n     *  This function is called to fetch content from HTTP and\n     *  HTTPS URLs and is platform specific (e.g. nodejs vs\n     *  browsers).\n     *\n     *  This is by default the currently registered global getUrl\n     *  function, which can be changed using [[registerGetUrl]].\n     *  If this has been set, setting is to ``null`` will cause\n     *  this FetchRequest (and any future clones) to revert back to\n     *  using the currently registered global getUrl function.\n     *\n     *  Setting this is generally not necessary, but may be useful\n     *  for developers that wish to intercept requests or to\n     *  configurege a proxy or other agent.\n     */\n    get getUrlFunc(): FetchGetUrlFunc {\n        return this.#getUrlFunc || defaultGetUrlFunc;\n    }\n    set getUrlFunc(value: null | FetchGetUrlFunc) {\n        this.#getUrlFunc = value;\n    }\n\n    /**\n     *  Create a new FetchRequest instance with default values.\n     *\n     *  Once created, each property may be set before issuing a\n     *  ``.send()`` to make the request.\n     */\n    constructor(url: string) {\n        this.#url = String(url);\n\n        this.#allowInsecure = false;\n        this.#gzip = true;\n        this.#headers = { };\n        this.#method = \"\";\n        this.#timeout = 300000;\n\n        this.#throttle = {\n            slotInterval: SLOT_INTERVAL,\n            maxAttempts: MAX_ATTEMPTS\n        };\n\n        this.#getUrlFunc = null;\n    }\n\n    toString(): string {\n        return `<FetchRequest method=${ JSON.stringify(this.method) } url=${ JSON.stringify(this.url) } headers=${ JSON.stringify(this.headers) } body=${ this.#body ? hexlify(this.#body): \"null\" }>`;\n    }\n\n    /**\n     *  Update the throttle parameters used to determine maximum\n     *  attempts and exponential-backoff properties.\n     */\n    setThrottleParams(params: FetchThrottleParams): void {\n        if (params.slotInterval != null) {\n            this.#throttle.slotInterval = params.slotInterval;\n        }\n        if (params.maxAttempts != null) {\n            this.#throttle.maxAttempts = params.maxAttempts;\n        }\n    }\n\n    async #send(attempt: number, expires: number, delay: number, _request: FetchRequest, _response: FetchResponse): Promise<FetchResponse> {\n        if (attempt >= this.#throttle.maxAttempts) {\n            return _response.makeServerError(\"exceeded maximum retry limit\");\n        }\n\n        assert(getTime() <= expires, \"timeout\", \"TIMEOUT\", {\n            operation: \"request.send\", reason: \"timeout\", request: _request\n        });\n\n        if (delay > 0) { await wait(delay); }\n\n        let req = this.clone();\n        const scheme = (req.url.split(\":\")[0] || \"\").toLowerCase();\n\n        // Process any Gateways\n        if (scheme in Gateways) {\n            const result = await Gateways[scheme](req.url, checkSignal(_request.#signal));\n            if (result instanceof FetchResponse) {\n                let response = result;\n\n                if (this.processFunc) {\n                    checkSignal(_request.#signal);\n                    try {\n                        response = await this.processFunc(req, response);\n                    } catch (error: any) {\n\n                        // Something went wrong during processing; throw a 5xx server error\n                        if (error.throttle == null || typeof(error.stall) !== \"number\") {\n                            response.makeServerError(\"error in post-processing function\", error).assertOk();\n                        }\n\n                        // Ignore throttling\n                    }\n                }\n\n                return response;\n            }\n            req = result;\n        }\n\n        // We have a preflight function; update the request\n        if (this.preflightFunc) { req = await this.preflightFunc(req); }\n\n        const resp = await this.getUrlFunc(req, checkSignal(_request.#signal));\n        let response = new FetchResponse(resp.statusCode, resp.statusMessage, resp.headers, resp.body, _request);\n\n        if (response.statusCode === 301 || response.statusCode === 302) {\n\n            // Redirect\n            try {\n                const location = response.headers.location || \"\";\n                return req.redirect(location).#send(attempt + 1, expires, 0, _request, response);\n            } catch (error) { }\n\n            // Things won't get any better on another attempt; abort\n            return response;\n\n        } else if (response.statusCode === 429) {\n\n            // Throttle\n            if (this.retryFunc == null || (await this.retryFunc(req, response, attempt))) {\n                const retryAfter = response.headers[\"retry-after\"];\n                let delay = this.#throttle.slotInterval * Math.trunc(Math.random() * Math.pow(2, attempt));\n                if (typeof(retryAfter) === \"string\" && retryAfter.match(/^[1-9][0-9]*$/)) {\n                    delay = parseInt(retryAfter);\n                }\n                return req.clone().#send(attempt + 1, expires, delay, _request, response);\n            }\n        }\n\n        if (this.processFunc) {\n            checkSignal(_request.#signal);\n            try {\n                response = await this.processFunc(req, response);\n            } catch (error: any) {\n\n                // Something went wrong during processing; throw a 5xx server error\n                if (error.throttle == null || typeof(error.stall) !== \"number\") {\n                    response.makeServerError(\"error in post-processing function\", error).assertOk();\n                }\n\n                // Throttle\n                let delay = this.#throttle.slotInterval * Math.trunc(Math.random() * Math.pow(2, attempt));;\n                if (error.stall >= 0) { delay = error.stall; }\n\n                return req.clone().#send(attempt + 1, expires, delay, _request, response);\n            }\n        }\n\n        return response;\n    }\n\n    /**\n     *  Resolves to the response by sending the request.\n     */\n    send(): Promise<FetchResponse> {\n        assert(this.#signal == null, \"request already sent\", \"UNSUPPORTED_OPERATION\", { operation: \"fetchRequest.send\" });\n        this.#signal = new FetchCancelSignal(this);\n        return this.#send(0, getTime() + this.timeout, 0, this, new FetchResponse(0, \"\", { }, null, this));\n    }\n\n    /**\n     *  Cancels the inflight response, causing a ``CANCELLED``\n     *  error to be rejected from the [[send]].\n     */\n    cancel(): void {\n        assert(this.#signal != null, \"request has not been sent\", \"UNSUPPORTED_OPERATION\", { operation: \"fetchRequest.cancel\" });\n        const signal = fetchSignals.get(this);\n        if (!signal) { throw new Error(\"missing signal; should not happen\"); }\n        signal();\n    }\n\n    /**\n     *  Returns a new [[FetchRequest]] that represents the redirection\n     *  to %%location%%.\n     */\n    redirect(location: string): FetchRequest {\n        // Redirection; for now we only support absolute locations\n        const current = this.url.split(\":\")[0].toLowerCase();\n        const target = location.split(\":\")[0].toLowerCase();\n\n        // Don't allow redirecting:\n        // - non-GET requests\n        // - downgrading the security (e.g. https => http)\n        // - to non-HTTP (or non-HTTPS) protocols [this could be relaxed?]\n        assert(this.method === \"GET\" && (current !== \"https\" || target !== \"http\") && location.match(/^https?:/), `unsupported redirect`, \"UNSUPPORTED_OPERATION\", {\n            operation: `redirect(${ this.method } ${ JSON.stringify(this.url) } => ${ JSON.stringify(location) })`\n        });\n\n        // Create a copy of this request, with a new URL\n        const req = new FetchRequest(location);\n        req.method = \"GET\";\n        req.allowGzip = this.allowGzip;\n        req.timeout = this.timeout;\n        req.#headers = Object.assign({ }, this.#headers);\n        if (this.#body) { req.#body = new Uint8Array(this.#body); }\n        req.#bodyType = this.#bodyType;\n\n        // Do not forward credentials unless on the same domain; only absolute\n        //req.allowInsecure = false;\n        // paths are currently supported; may want a way to specify to forward?\n        //setStore(req.#props, \"creds\", getStore(this.#pros, \"creds\"));\n\n        return req;\n    }\n\n    /**\n     *  Create a new copy of this request.\n     */\n    clone(): FetchRequest {\n        const clone = new FetchRequest(this.url);\n\n        // Preserve \"default method\" (i.e. null)\n        clone.#method = this.#method;\n\n        // Preserve \"default body\" with type, copying the Uint8Array is present\n        if (this.#body) { clone.#body = this.#body; }\n        clone.#bodyType = this.#bodyType;\n\n        // Preserve \"default headers\"\n        clone.#headers = Object.assign({ }, this.#headers);\n\n        // Credentials is readonly, so we copy internally\n        clone.#creds = this.#creds;\n\n        if (this.allowGzip) { clone.allowGzip = true; }\n\n        clone.timeout = this.timeout;\n        if (this.allowInsecureAuthentication) { clone.allowInsecureAuthentication = true; }\n\n        clone.#preflight = this.#preflight;\n        clone.#process = this.#process;\n        clone.#retry = this.#retry;\n\n        clone.#throttle = Object.assign({ }, this.#throttle);\n\n        clone.#getUrlFunc = this.#getUrlFunc;\n\n        return clone;\n    }\n\n    /**\n     *  Locks all static configuration for gateways and FetchGetUrlFunc\n     *  registration.\n     */\n    static lockConfig(): void {\n        locked = true;\n    }\n\n    /**\n     *  Get the current Gateway function for %%scheme%%.\n     */\n    static getGateway(scheme: string): null | FetchGatewayFunc {\n        return Gateways[scheme.toLowerCase()] || null;\n    }\n\n    /**\n     *  Use the %%func%% when fetching URIs using %%scheme%%.\n     *\n     *  This method affects all requests globally.\n     *\n     *  If [[lockConfig]] has been called, no change is made and this\n     *  throws.\n     */\n    static registerGateway(scheme: string, func: FetchGatewayFunc): void {\n        scheme = scheme.toLowerCase();\n        if (scheme === \"http\" || scheme === \"https\") {\n            throw new Error(`cannot intercept ${ scheme }; use registerGetUrl`);\n        }\n        if (locked) { throw new Error(\"gateways locked\"); }\n        Gateways[scheme] = func;\n    }\n\n    /**\n     *  Use %%getUrl%% when fetching URIs over HTTP and HTTPS requests.\n     *\n     *  This method affects all requests globally.\n     *\n     *  If [[lockConfig]] has been called, no change is made and this\n     *  throws.\n     */\n    static registerGetUrl(getUrl: FetchGetUrlFunc): void {\n        if (locked) { throw new Error(\"gateways locked\"); }\n        defaultGetUrlFunc = getUrl;\n    }\n\n    /**\n     *  Creates a getUrl function that fetches content from HTTP and\n     *  HTTPS URLs.\n     *\n     *  The available %%options%% are dependent on the platform\n     *  implementation of the default getUrl function.\n     *\n     *  This is not generally something that is needed, but is useful\n     *  when trying to customize simple behaviour when fetching HTTP\n     *  content.\n     */\n    static createGetUrlFunc(options?: Record<string, any>): FetchGetUrlFunc {\n        return createGetUrl(options);\n    }\n\n    /**\n     *  Creates a function that can \"fetch\" data URIs.\n     *\n     *  Note that this is automatically done internally to support\n     *  data URIs, so it is not necessary to register it.\n     *\n     *  This is not generally something that is needed, but may\n     *  be useful in a wrapper to perfom custom data URI functionality.\n     */\n    static createDataGateway(): FetchGatewayFunc {\n        return dataGatewayFunc;\n    }\n\n    /**\n     *  Creates a function that will fetch IPFS (unvalidated) from\n     *  a custom gateway baseUrl.\n     *\n     *  The default IPFS gateway used internally is\n     *  ``\"https:/\\/gateway.ipfs.io/ipfs/\"``.\n     */\n    static createIpfsGatewayFunc(baseUrl: string): FetchGatewayFunc {\n        return getIpfsGatewayFunc(baseUrl);\n    }\n}\n\n\ninterface ThrottleError extends Error {\n    stall: number;\n    throttle: true;\n};\n\n/**\n *  The response for a FetchRequest.\n */\nexport class FetchResponse implements Iterable<[ key: string, value: string ]> {\n    #statusCode: number;\n    #statusMessage: string;\n    #headers: Record<string, string>;\n    #body: null | Readonly<Uint8Array>;\n    #request: null | FetchRequest;\n\n    #error: { error?: Error, message: string };\n\n    toString(): string {\n        return `<FetchResponse status=${ this.statusCode } body=${ this.#body ? hexlify(this.#body): \"null\" }>`;\n    }\n\n    /**\n     *  The response status code.\n     */\n    get statusCode(): number { return this.#statusCode; }\n\n    /**\n     *  The response status message.\n     */\n    get statusMessage(): string { return this.#statusMessage; }\n\n    /**\n     *  The response headers. All keys are lower-case.\n     */\n    get headers(): Record<string, string> { return Object.assign({ }, this.#headers); }\n\n    /**\n     *  The response body, or ``null`` if there was no body.\n     */\n    get body(): null | Readonly<Uint8Array> {\n        return (this.#body == null) ? null: new Uint8Array(this.#body);\n    }\n\n    /**\n     *  The response body as a UTF-8 encoded string, or the empty\n     *  string (i.e. ``\"\"``) if there was no body.\n     *\n     *  An error is thrown if the body is invalid UTF-8 data.\n     */\n    get bodyText(): string {\n        try {\n            return (this.#body == null) ? \"\": toUtf8String(this.#body);\n        } catch (error) {\n            assert(false, \"response body is not valid UTF-8 data\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"bodyText\", info: { response: this }\n            });\n        }\n    }\n\n    /**\n     *  The response body, decoded as JSON.\n     *\n     *  An error is thrown if the body is invalid JSON-encoded data\n     *  or if there was no body.\n     */\n    get bodyJson(): any {\n        try {\n            return JSON.parse(this.bodyText);\n        } catch (error) {\n            assert(false, \"response body is not valid JSON\", \"UNSUPPORTED_OPERATION\", {\n                operation: \"bodyJson\", info: { response: this }\n            });\n        }\n    }\n\n    [Symbol.iterator](): Iterator<[ key: string, value: string ]> {\n        const headers = this.headers;\n        const keys = Object.keys(headers);\n        let index = 0;\n        return {\n            next: () => {\n                if (index < keys.length) {\n                    const key = keys[index++];\n                    return {\n                        value: [ key, headers[key] ], done: false\n                    }\n                }\n                return { value: undefined, done: true };\n            }\n        };\n    }\n\n    constructor(statusCode: number, statusMessage: string, headers: Readonly<Record<string, string>>, body: null | Uint8Array, request?: FetchRequest) {\n        this.#statusCode = statusCode;\n        this.#statusMessage = statusMessage;\n        this.#headers = Object.keys(headers).reduce((accum, k) => {\n            accum[k.toLowerCase()] = String(headers[k]);\n            return accum;\n        }, <Record<string, string>>{ });\n        this.#body = ((body == null) ? null: new Uint8Array(body));\n        this.#request = (request || null);\n\n        this.#error = { message: \"\" };\n    }\n\n    /**\n     *  Return a Response with matching headers and body, but with\n     *  an error status code (i.e. 599) and %%message%% with an\n     *  optional %%error%%.\n     */\n    makeServerError(message?: string, error?: Error): FetchResponse {\n        let statusMessage: string;\n        if (!message) {\n            message = `${ this.statusCode } ${ this.statusMessage }`;\n            statusMessage = `CLIENT ESCALATED SERVER ERROR (${ message })`;\n        } else {\n            statusMessage = `CLIENT ESCALATED SERVER ERROR (${ this.statusCode } ${ this.statusMessage }; ${ message })`;\n        }\n        const response = new FetchResponse(599, statusMessage, this.headers,\n            this.body, this.#request || undefined);\n        response.#error = { message, error };\n        return response;\n    }\n\n    /**\n     *  If called within a [request.processFunc](FetchRequest-processFunc)\n     *  call, causes the request to retry as if throttled for %%stall%%\n     *  milliseconds.\n     */\n    throwThrottleError(message?: string, stall?: number): never {\n        if (stall == null) {\n            stall = -1;\n        } else {\n            assertArgument(Number.isInteger(stall) && stall >= 0, \"invalid stall timeout\", \"stall\", stall);\n        }\n\n        const error = new Error(message || \"throttling requests\");\n\n        defineProperties(<ThrottleError>error, { stall, throttle: true });\n\n        throw error;\n    }\n\n    /**\n     *  Get the header value for %%key%%, ignoring case.\n     */\n    getHeader(key: string): string {\n        return this.headers[key.toLowerCase()];\n    }\n\n    /**\n     *  Returns true if the response has a body.\n     */\n    hasBody(): this is (FetchResponse & { body: Uint8Array }) {\n        return (this.#body != null);\n    }\n\n    /**\n     *  The request made for this response.\n     */\n    get request(): null | FetchRequest { return this.#request; }\n\n    /**\n     *  Returns true if this response was a success statusCode.\n     */\n    ok(): boolean {\n        return (this.#error.message === \"\" && this.statusCode >= 200 && this.statusCode < 300);\n    }\n\n    /**\n     *  Throws a ``SERVER_ERROR`` if this response is not ok.\n     */\n    assertOk(): void {\n        if (this.ok()) { return; }\n        let { message, error } = this.#error;\n        if (message === \"\") {\n            message = `server response ${ this.statusCode } ${ this.statusMessage }`;\n        }\n\n        let requestUrl: null | string = null;\n        if (this.request) { requestUrl = this.request.url; }\n\n        let responseBody: null | string = null;\n        try {\n            if (this.#body) { responseBody = toUtf8String(this.#body); }\n        } catch (e) { }\n\n        assert(false, message, \"SERVER_ERROR\", {\n            request: (this.request || \"unknown request\"), response: this, error,\n            info: {\n                requestUrl, responseBody,\n                responseStatus: `${ this.statusCode } ${ this.statusMessage }` }\n        });\n    }\n}\n\n\nfunction getTime(): number { return (new Date()).getTime(); }\n\nfunction unpercent(value: string): Uint8Array {\n    return toUtf8Bytes(value.replace(/%([0-9a-f][0-9a-f])/gi, (all, code) => {\n        return String.fromCharCode(parseInt(code, 16));\n    }));\n}\n\nfunction wait(delay: number): Promise<void> {\n    return new Promise((resolve) => setTimeout(resolve, delay));\n}\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,SAASA,YAAY,EAAEC,YAAY,QAAQ,aAAa;AACxD,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,MAAM,EAAEC,cAAc,QAAQ,aAAa;AACpD,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,WAAW,EAAEC,YAAY,QAAQ,WAAW;AAErD,SAASC,YAAY,QAAQ,aAAa;AAkD1C,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,aAAa,GAAG,GAAG;AAEzB;AACA,IAAIC,iBAAiB,GAAoBH,YAAY,EAAE;AAEvD,MAAMI,MAAM,GAAG,IAAIC,MAAM,CAAC,iCAAiC,EAAE,GAAG,CAAC;AACjE,MAAMC,MAAM,GAAG,IAAID,MAAM,CAAC,wBAAwB,EAAE,GAAG,CAAC;AAExD;AACA,IAAIE,MAAM,GAAG,KAAK;AAElB;AACA,eAAeC,eAAeA,CAACC,GAAW,EAAEC,MAA0B;EAClE,IAAI;IACA,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACP,MAAM,CAAC;IAC/B,IAAI,CAACO,KAAK,EAAE;MAAE,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;;IAC7C,OAAO,IAAIC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE;MAChC,cAAc,EAAGF,KAAK,CAAC,CAAC,CAAC,IAAI;KAChC,EAAGA,KAAK,CAAC,CAAC,CAAC,GAAGnB,YAAY,CAACmB,KAAK,CAAC,CAAC,CAAC,CAAC,GAAEG,SAAS,CAACH,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;GAC/D,CAAC,OAAOI,KAAK,EAAE;IACZ,OAAO,IAAIF,aAAa,CAAC,GAAG,EAAE,iCAAiC,EAAE,EAAG,EAAE,IAAI,EAAE,IAAIG,YAAY,CAACP,GAAG,CAAC,CAAC;;AAE1G;AAEA;;;;AAIA,SAASQ,kBAAkBA,CAACC,OAAe;EACvC,eAAeC,WAAWA,CAACV,GAAW,EAAEC,MAA0B;IAC9D,IAAI;MACA,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACL,MAAM,CAAC;MAC/B,IAAI,CAACK,KAAK,EAAE;QAAE,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;;MAC7C,OAAO,IAAII,YAAY,CAAC,GAAIE,OAAQ,GAAIP,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC;KACvD,CAAC,OAAOI,KAAK,EAAE;MACZ,OAAO,IAAIF,aAAa,CAAC,GAAG,EAAE,gCAAgC,EAAE,EAAG,EAAE,IAAI,EAAE,IAAIG,YAAY,CAACP,GAAG,CAAC,CAAC;;EAEzG;EAEA,OAAOU,WAAW;AACtB;AAEA,MAAMC,QAAQ,GAAqC;EAC/C,MAAM,EAAEZ,eAAe;EACvB,MAAM,EAAES,kBAAkB,CAAC,gCAAgC;CAC9D;AAED,MAAMI,YAAY,GAAsC,IAAIC,OAAO,EAAE;AAErE;;;AAGA,OAAM,MAAOC,iBAAiB;EAC1B,CAAAC,SAAU;EACV,CAAAC,SAAU;EAEVC,YAAYC,OAAqB;IAC7B,IAAI,CAAC,CAAAH,SAAU,GAAG,EAAG;IACrB,IAAI,CAAC,CAAAC,SAAU,GAAG,KAAK;IAEvBJ,YAAY,CAACO,GAAG,CAACD,OAAO,EAAE,MAAK;MAC3B,IAAI,IAAI,CAAC,CAAAF,SAAU,EAAE;QAAE;;MACvB,IAAI,CAAC,CAAAA,SAAU,GAAG,IAAI;MAEtB,KAAK,MAAMI,QAAQ,IAAI,IAAI,CAAC,CAAAL,SAAU,EAAE;QACpCM,UAAU,CAAC,MAAK;UAAGD,QAAQ,EAAE;QAAE,CAAC,EAAE,CAAC,CAAC;;MAExC,IAAI,CAAC,CAAAL,SAAU,GAAG,EAAG;IACzB,CAAC,CAAC;EACN;EAEAO,WAAWA,CAACF,QAAoB;IAC5BlC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAA8B,SAAU,EAAE,0BAA0B,EAAE,uBAAuB,EAAE;MAC1EO,SAAS,EAAE;KACd,CAAC;IACF,IAAI,CAAC,CAAAR,SAAU,CAACS,IAAI,CAACJ,QAAQ,CAAC;EAClC;EAEA,IAAIJ,SAASA,CAAA;IAAc,OAAO,IAAI,CAAC,CAAAA,SAAU;EAAE;EAEnDS,WAAWA,CAAA;IACPvC,MAAM,CAAC,CAAC,IAAI,CAAC8B,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,EAAG,CAAC;EAC1D;;AAGJ;AACA,SAASS,WAAWA,CAACxB,MAA0B;EAC3C,IAAIA,MAAM,IAAI,IAAI,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;EAC1EF,MAAM,CAACwB,WAAW,EAAE;EACpB,OAAOxB,MAAM;AACjB;AAEA;;;;;;;;;;;;;;AAcA,OAAM,MAAOM,YAAY;EACrB,CAAAmB,aAAc;EACd,CAAAC,IAAK;EACL,CAAAC,OAAQ;EACR,CAAAC,MAAO;EACP,CAAAC,OAAQ;EACR,CAAA9B,GAAI;EAEJ,CAAA+B,IAAK;EACL,CAAAC,QAAS;EACT,CAAAC,KAAM;EAEN;EACA,CAAAC,SAAU;EACV,CAAAC,OAAQ;EACR,CAAAC,KAAM;EAEN,CAAAnC,MAAO;EAEP,CAAAoC,QAAS;EAET,CAAAC,UAAW;EAEX;;;EAGA,IAAItC,GAAGA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,GAAI;EAAE;EACtC,IAAIA,GAAGA,CAACA,GAAW;IACf,IAAI,CAAC,CAAAA,GAAI,GAAGuC,MAAM,CAACvC,GAAG,CAAC;EAC3B;EAEA;;;;;;;;;;;;;;;;;;;EAmBA,IAAI+B,IAAIA,CAAA;IACJ,IAAI,IAAI,CAAC,CAAAA,IAAK,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IACrC,OAAO,IAAIS,UAAU,CAAC,IAAI,CAAC,CAAAT,IAAK,CAAC;EACrC;EACA,IAAIA,IAAIA,CAACA,IAA6D;IAClE,IAAIA,IAAI,IAAI,IAAI,EAAE;MACd,IAAI,CAAC,CAAAA,IAAK,GAAGU,SAAS;MACtB,IAAI,CAAC,CAAAT,QAAS,GAAGS,SAAS;KAC7B,MAAM,IAAI,OAAOV,IAAK,KAAK,QAAQ,EAAE;MAClC,IAAI,CAAC,CAAAA,IAAK,GAAG1C,WAAW,CAAC0C,IAAI,CAAC;MAC9B,IAAI,CAAC,CAAAC,QAAS,GAAG,YAAY;KAChC,MAAM,IAAID,IAAI,YAAYS,UAAU,EAAE;MACnC,IAAI,CAAC,CAAAT,IAAK,GAAGA,IAAI;MACjB,IAAI,CAAC,CAAAC,QAAS,GAAG,0BAA0B;KAC9C,MAAM,IAAI,OAAOD,IAAK,KAAK,QAAQ,EAAE;MAClC,IAAI,CAAC,CAAAA,IAAK,GAAG1C,WAAW,CAACqD,IAAI,CAACC,SAAS,CAACZ,IAAI,CAAC,CAAC;MAC9C,IAAI,CAAC,CAAAC,QAAS,GAAG,kBAAkB;KACtC,MAAM;MACH,MAAM,IAAI7B,KAAK,CAAC,cAAc,CAAC;;EAEvC;EAEA;;;EAGAyC,OAAOA,CAAA;IACH,OAAQ,IAAI,CAAC,CAAAb,IAAK,IAAI,IAAI;EAC9B;EAEA;;;;;EAKA,IAAIF,MAAMA,CAAA;IACN,IAAI,IAAI,CAAC,CAAAA,MAAO,EAAE;MAAE,OAAO,IAAI,CAAC,CAAAA,MAAO;;IACvC,IAAI,IAAI,CAACe,OAAO,EAAE,EAAE;MAAE,OAAO,MAAM;;IACnC,OAAO,KAAK;EAChB;EACA,IAAIf,MAAMA,CAACA,MAAqB;IAC5B,IAAIA,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,EAAE;;IACjC,IAAI,CAAC,CAAAA,MAAO,GAAGU,MAAM,CAACV,MAAM,CAAC,CAACgB,WAAW,EAAE;EAC/C;EAEA;;;;;;;;;EASA,IAAIjB,OAAOA,CAAA;IACP,MAAMA,OAAO,GAAGkB,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE,IAAI,CAAC,CAAAnB,OAAQ,CAAC;IAEjD,IAAI,IAAI,CAAC,CAAAK,KAAM,EAAE;MACbL,OAAO,CAAC,eAAe,CAAC,GAAG,SAAU5C,YAAY,CAACK,WAAW,CAAC,IAAI,CAAC,CAAA4C,KAAM,CAAC,CAAE,EAAE;;IACjF;IAED,IAAI,IAAI,CAACe,SAAS,EAAE;MAChBpB,OAAO,CAAC,iBAAiB,CAAC,GAAG,MAAM;;IAGvC,IAAIA,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAAI,QAAS,EAAE;MACnDJ,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAAI,QAAS;;IAE5C,IAAI,IAAI,CAACD,IAAI,EAAE;MAAEH,OAAO,CAAC,gBAAgB,CAAC,GAAGW,MAAM,CAAC,IAAI,CAACR,IAAI,CAACkB,MAAM,CAAC;;IAErE,OAAOrB,OAAO;EAClB;EAEA;;;EAGAsB,SAASA,CAACC,GAAW;IACjB,OAAO,IAAI,CAACvB,OAAO,CAACuB,GAAG,CAACC,WAAW,EAAE,CAAC;EAC1C;EAEA;;;;EAIAC,SAASA,CAACF,GAAW,EAAEG,KAAsB;IACzC,IAAI,CAAC,CAAA1B,OAAQ,CAACW,MAAM,CAACY,GAAG,CAAC,CAACC,WAAW,EAAE,CAAC,GAAGb,MAAM,CAACe,KAAK,CAAC;EAC5D;EAEA;;;EAGAC,YAAYA,CAAA;IACR,IAAI,CAAC,CAAA3B,OAAQ,GAAG,EAAG;EACvB;EAEA,CAAC4B,MAAM,CAACC,QAAQ,IAAC;IACb,MAAM7B,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAM8B,IAAI,GAAGZ,MAAM,CAACY,IAAI,CAAC9B,OAAO,CAAC;IACjC,IAAI+B,KAAK,GAAG,CAAC;IACb,OAAO;MACHC,IAAI,EAAEA,CAAA,KAAK;QACP,IAAID,KAAK,GAAGD,IAAI,CAACT,MAAM,EAAE;UACrB,MAAME,GAAG,GAAGO,IAAI,CAACC,KAAK,EAAE,CAAC;UACzB,OAAO;YACHL,KAAK,EAAE,CAAEH,GAAG,EAAEvB,OAAO,CAACuB,GAAG,CAAC,CAAE;YAAEU,IAAI,EAAE;WACvC;;QAEL,OAAO;UAAEP,KAAK,EAAEb,SAAS;UAAEoB,IAAI,EAAE;QAAI,CAAE;MAC3C;KACH;EACL;EAEA;;;;;EAKA,IAAIC,WAAWA,CAAA;IACX,OAAO,IAAI,CAAC,CAAA7B,KAAM,IAAI,IAAI;EAC9B;EAEA;;;EAGA8B,cAAcA,CAACC,QAAgB,EAAEC,QAAgB;IAC7C9E,cAAc,CAAC,CAAC6E,QAAQ,CAAC9D,KAAK,CAAC,GAAG,CAAC,EAAE,uCAAuC,EAAE,UAAU,EAAE,YAAY,CAAC;IACvG,IAAI,CAAC,CAAA+B,KAAM,GAAG,GAAI+B,QAAS,IAAKC,QAAS,EAAE;EAC/C;EAEA;;;;EAIA,IAAIjB,SAASA,CAAA;IACT,OAAO,IAAI,CAAC,CAAArB,IAAK;EACrB;EACA,IAAIqB,SAASA,CAACM,KAAc;IACxB,IAAI,CAAC,CAAA3B,IAAK,GAAG,CAAC,CAAC2B,KAAK;EACxB;EAEA;;;;EAIA,IAAIY,2BAA2BA,CAAA;IAC3B,OAAO,CAAC,CAAC,IAAI,CAAC,CAAAxC,aAAc;EAChC;EACA,IAAIwC,2BAA2BA,CAACZ,KAAc;IAC1C,IAAI,CAAC,CAAA5B,aAAc,GAAG,CAAC,CAAC4B,KAAK;EACjC;EAEA;;;;EAIA,IAAIxB,OAAOA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,OAAQ;EAAE;EAC9C,IAAIA,OAAOA,CAACA,OAAe;IACvB3C,cAAc,CAAC2C,OAAO,IAAI,CAAC,EAAE,0BAA0B,EAAE,SAAS,EAAEA,OAAO,CAAC;IAC5E,IAAI,CAAC,CAAAA,OAAQ,GAAGA,OAAO;EAC3B;EAEA;;;;;;;EAOA,IAAIqC,aAAaA,CAAA;IACb,OAAO,IAAI,CAAC,CAAAjC,SAAU,IAAI,IAAI;EAClC;EACA,IAAIiC,aAAaA,CAACjC,SAAoC;IAClD,IAAI,CAAC,CAAAA,SAAU,GAAGA,SAAS;EAC/B;EAEA;;;;;;;;;;EAUA,IAAIkC,WAAWA,CAAA;IACX,OAAO,IAAI,CAAC,CAAAjC,OAAQ,IAAI,IAAI;EAChC;EACA,IAAIiC,WAAWA,CAACjC,OAAgC;IAC5C,IAAI,CAAC,CAAAA,OAAQ,GAAGA,OAAO;EAC3B;EAEA;;;EAGA,IAAIkC,SAASA,CAAA;IACT,OAAO,IAAI,CAAC,CAAAjC,KAAM,IAAI,IAAI;EAC9B;EACA,IAAIiC,SAASA,CAACjC,KAA4B;IACtC,IAAI,CAAC,CAAAA,KAAM,GAAGA,KAAK;EACvB;EAEA;;;;;;;;;;;;;;;EAeA,IAAIE,UAAUA,CAAA;IACV,OAAO,IAAI,CAAC,CAAAA,UAAW,IAAI5C,iBAAiB;EAChD;EACA,IAAI4C,UAAUA,CAACgB,KAA6B;IACxC,IAAI,CAAC,CAAAhB,UAAW,GAAGgB,KAAK;EAC5B;EAEA;;;;;;EAMArC,YAAYjB,GAAW;IACnB,IAAI,CAAC,CAAAA,GAAI,GAAGuC,MAAM,CAACvC,GAAG,CAAC;IAEvB,IAAI,CAAC,CAAA0B,aAAc,GAAG,KAAK;IAC3B,IAAI,CAAC,CAAAC,IAAK,GAAG,IAAI;IACjB,IAAI,CAAC,CAAAC,OAAQ,GAAG,EAAG;IACnB,IAAI,CAAC,CAAAC,MAAO,GAAG,EAAE;IACjB,IAAI,CAAC,CAAAC,OAAQ,GAAG,MAAM;IAEtB,IAAI,CAAC,CAAAO,QAAS,GAAG;MACbiC,YAAY,EAAE7E,aAAa;MAC3B8E,WAAW,EAAE/E;KAChB;IAED,IAAI,CAAC,CAAA8C,UAAW,GAAG,IAAI;EAC3B;EAEAkC,QAAQA,CAAA;IACJ,OAAO,wBAAyB9B,IAAI,CAACC,SAAS,CAAC,IAAI,CAACd,MAAM,CAAE,QAASa,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3C,GAAG,CAAE,YAAa0C,IAAI,CAACC,SAAS,CAAC,IAAI,CAACf,OAAO,CAAE,SAAU,IAAI,CAAC,CAAAG,IAAK,GAAG9C,OAAO,CAAC,IAAI,CAAC,CAAA8C,IAAK,CAAC,GAAE,MAAO,GAAG;EAClM;EAEA;;;;EAIA0C,iBAAiBA,CAACC,MAA2B;IACzC,IAAIA,MAAM,CAACJ,YAAY,IAAI,IAAI,EAAE;MAC7B,IAAI,CAAC,CAAAjC,QAAS,CAACiC,YAAY,GAAGI,MAAM,CAACJ,YAAY;;IAErD,IAAII,MAAM,CAACH,WAAW,IAAI,IAAI,EAAE;MAC5B,IAAI,CAAC,CAAAlC,QAAS,CAACkC,WAAW,GAAGG,MAAM,CAACH,WAAW;;EAEvD;EAEA,MAAM,CAAAI,IAAKC,CAACC,OAAe,EAAEC,OAAe,EAAEC,KAAa,EAAEC,QAAsB,EAAEC,SAAwB;IACzG,IAAIJ,OAAO,IAAI,IAAI,CAAC,CAAAxC,QAAS,CAACkC,WAAW,EAAE;MACvC,OAAOU,SAAS,CAACC,eAAe,CAAC,8BAA8B,CAAC;;IAGpEhG,MAAM,CAACiG,OAAO,EAAE,IAAIL,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE;MAC/CvD,SAAS,EAAE,cAAc;MAAE6D,MAAM,EAAE,SAAS;MAAElE,OAAO,EAAE8D;KAC1D,CAAC;IAEF,IAAID,KAAK,GAAG,CAAC,EAAE;MAAE,MAAMM,IAAI,CAACN,KAAK,CAAC;;IAElC,IAAIO,GAAG,GAAG,IAAI,CAACC,KAAK,EAAE;IACtB,MAAMC,MAAM,GAAG,CAACF,GAAG,CAACtF,GAAG,CAACyF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAErC,WAAW,EAAE;IAE1D;IACA,IAAIoC,MAAM,IAAI7E,QAAQ,EAAE;MACpB,MAAM+E,MAAM,GAAG,MAAM/E,QAAQ,CAAC6E,MAAM,CAAC,CAACF,GAAG,CAACtF,GAAG,EAAEyB,WAAW,CAACuD,QAAQ,CAAC,CAAA/E,MAAO,CAAC,CAAC;MAC7E,IAAIyF,MAAM,YAAYtF,aAAa,EAAE;QACjC,IAAIuF,QAAQ,GAAGD,MAAM;QAErB,IAAI,IAAI,CAACtB,WAAW,EAAE;UAClB3C,WAAW,CAACuD,QAAQ,CAAC,CAAA/E,MAAO,CAAC;UAC7B,IAAI;YACA0F,QAAQ,GAAG,MAAM,IAAI,CAACvB,WAAW,CAACkB,GAAG,EAAEK,QAAQ,CAAC;WACnD,CAAC,OAAOrF,KAAU,EAAE;YAEjB;YACA,IAAIA,KAAK,CAAC+B,QAAQ,IAAI,IAAI,IAAI,OAAO/B,KAAK,CAACsF,KAAM,KAAK,QAAQ,EAAE;cAC5DD,QAAQ,CAACT,eAAe,CAAC,mCAAmC,EAAE5E,KAAK,CAAC,CAACuF,QAAQ,EAAE;;YAGnF;;;QAIR,OAAOF,QAAQ;;MAEnBL,GAAG,GAAGI,MAAM;;IAGhB;IACA,IAAI,IAAI,CAACvB,aAAa,EAAE;MAAEmB,GAAG,GAAG,MAAM,IAAI,CAACnB,aAAa,CAACmB,GAAG,CAAC;;IAE7D,MAAMQ,IAAI,GAAG,MAAM,IAAI,CAACxD,UAAU,CAACgD,GAAG,EAAE7D,WAAW,CAACuD,QAAQ,CAAC,CAAA/E,MAAO,CAAC,CAAC;IACtE,IAAI0F,QAAQ,GAAG,IAAIvF,aAAa,CAAC0F,IAAI,CAACC,UAAU,EAAED,IAAI,CAACE,aAAa,EAAEF,IAAI,CAAClE,OAAO,EAAEkE,IAAI,CAAC/D,IAAI,EAAEiD,QAAQ,CAAC;IAExG,IAAIW,QAAQ,CAACI,UAAU,KAAK,GAAG,IAAIJ,QAAQ,CAACI,UAAU,KAAK,GAAG,EAAE;MAE5D;MACA,IAAI;QACA,MAAME,QAAQ,GAAGN,QAAQ,CAAC/D,OAAO,CAACqE,QAAQ,IAAI,EAAE;QAChD,OAAOX,GAAG,CAACY,QAAQ,CAACD,QAAQ,CAAC,CAAC,CAAAtB,IAAK,CAACE,OAAO,GAAG,CAAC,EAAEC,OAAO,EAAE,CAAC,EAAEE,QAAQ,EAAEW,QAAQ,CAAC;OACnF,CAAC,OAAOrF,KAAK,EAAE;MAEhB;MACA,OAAOqF,QAAQ;KAElB,MAAM,IAAIA,QAAQ,CAACI,UAAU,KAAK,GAAG,EAAE;MAEpC;MACA,IAAI,IAAI,CAAC1B,SAAS,IAAI,IAAI,KAAK,MAAM,IAAI,CAACA,SAAS,CAACiB,GAAG,EAAEK,QAAQ,EAAEd,OAAO,CAAC,CAAC,EAAE;QAC1E,MAAMsB,UAAU,GAAGR,QAAQ,CAAC/D,OAAO,CAAC,aAAa,CAAC;QAClD,IAAImD,KAAK,GAAG,IAAI,CAAC,CAAA1C,QAAS,CAACiC,YAAY,GAAG8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE1B,OAAO,CAAC,CAAC;QAC1F,IAAI,OAAOsB,UAAW,KAAK,QAAQ,IAAIA,UAAU,CAACjG,KAAK,CAAC,eAAe,CAAC,EAAE;UACtE6E,KAAK,GAAGyB,QAAQ,CAACL,UAAU,CAAC;;QAEhC,OAAOb,GAAG,CAACC,KAAK,EAAE,CAAC,CAAAZ,IAAK,CAACE,OAAO,GAAG,CAAC,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEW,QAAQ,CAAC;;;IAIjF,IAAI,IAAI,CAACvB,WAAW,EAAE;MAClB3C,WAAW,CAACuD,QAAQ,CAAC,CAAA/E,MAAO,CAAC;MAC7B,IAAI;QACA0F,QAAQ,GAAG,MAAM,IAAI,CAACvB,WAAW,CAACkB,GAAG,EAAEK,QAAQ,CAAC;OACnD,CAAC,OAAOrF,KAAU,EAAE;QAEjB;QACA,IAAIA,KAAK,CAAC+B,QAAQ,IAAI,IAAI,IAAI,OAAO/B,KAAK,CAACsF,KAAM,KAAK,QAAQ,EAAE;UAC5DD,QAAQ,CAACT,eAAe,CAAC,mCAAmC,EAAE5E,KAAK,CAAC,CAACuF,QAAQ,EAAE;;QAGnF;QACA,IAAId,KAAK,GAAG,IAAI,CAAC,CAAA1C,QAAS,CAACiC,YAAY,GAAG8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAE1B,OAAO,CAAC,CAAC;QAAC;QAC3F,IAAIvE,KAAK,CAACsF,KAAK,IAAI,CAAC,EAAE;UAAEb,KAAK,GAAGzE,KAAK,CAACsF,KAAK;;QAE3C,OAAON,GAAG,CAACC,KAAK,EAAE,CAAC,CAAAZ,IAAK,CAACE,OAAO,GAAG,CAAC,EAAEC,OAAO,EAAEC,KAAK,EAAEC,QAAQ,EAAEW,QAAQ,CAAC;;;IAIjF,OAAOA,QAAQ;EACnB;EAEA;;;EAGAhB,IAAIA,CAAA;IACAzF,MAAM,CAAC,IAAI,CAAC,CAAAe,MAAO,IAAI,IAAI,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;MAAEsB,SAAS,EAAE;IAAmB,CAAE,CAAC;IACjH,IAAI,CAAC,CAAAtB,MAAO,GAAG,IAAIa,iBAAiB,CAAC,IAAI,CAAC;IAC1C,OAAO,IAAI,CAAC,CAAA6D,IAAK,CAAC,CAAC,EAAEQ,OAAO,EAAE,GAAG,IAAI,CAACrD,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI1B,aAAa,CAAC,CAAC,EAAE,EAAE,EAAE,EAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACtG;EAEA;;;;EAIAqG,MAAMA,CAAA;IACFvH,MAAM,CAAC,IAAI,CAAC,CAAAe,MAAO,IAAI,IAAI,EAAE,2BAA2B,EAAE,uBAAuB,EAAE;MAAEsB,SAAS,EAAE;IAAqB,CAAE,CAAC;IACxH,MAAMtB,MAAM,GAAGW,YAAY,CAAC8F,GAAG,CAAC,IAAI,CAAC;IACrC,IAAI,CAACzG,MAAM,EAAE;MAAE,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;;IACnEF,MAAM,EAAE;EACZ;EAEA;;;;EAIAiG,QAAQA,CAACD,QAAgB;IACrB;IACA,MAAMU,OAAO,GAAG,IAAI,CAAC3G,GAAG,CAACyF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACrC,WAAW,EAAE;IACpD,MAAMwD,MAAM,GAAGX,QAAQ,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACrC,WAAW,EAAE;IAEnD;IACA;IACA;IACA;IACAlE,MAAM,CAAC,IAAI,CAAC2C,MAAM,KAAK,KAAK,KAAK8E,OAAO,KAAK,OAAO,IAAIC,MAAM,KAAK,MAAM,CAAC,IAAIX,QAAQ,CAAC/F,KAAK,CAAC,UAAU,CAAC,EAAE,sBAAsB,EAAE,uBAAuB,EAAE;MACvJqB,SAAS,EAAE,YAAa,IAAI,CAACM,MAAO,IAAKa,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3C,GAAG,CAAE,OAAQ0C,IAAI,CAACC,SAAS,CAACsD,QAAQ,CAAE;KACtG,CAAC;IAEF;IACA,MAAMX,GAAG,GAAG,IAAI/E,YAAY,CAAC0F,QAAQ,CAAC;IACtCX,GAAG,CAACzD,MAAM,GAAG,KAAK;IAClByD,GAAG,CAACtC,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9BsC,GAAG,CAACxD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1BwD,GAAG,CAAC,CAAA1D,OAAQ,GAAGkB,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE,IAAI,CAAC,CAAAnB,OAAQ,CAAC;IAChD,IAAI,IAAI,CAAC,CAAAG,IAAK,EAAE;MAAEuD,GAAG,CAAC,CAAAvD,IAAK,GAAG,IAAIS,UAAU,CAAC,IAAI,CAAC,CAAAT,IAAK,CAAC;;IACxDuD,GAAG,CAAC,CAAAtD,QAAS,GAAG,IAAI,CAAC,CAAAA,QAAS;IAE9B;IACA;IACA;IACA;IAEA,OAAOsD,GAAG;EACd;EAEA;;;EAGAC,KAAKA,CAAA;IACD,MAAMA,KAAK,GAAG,IAAIhF,YAAY,CAAC,IAAI,CAACP,GAAG,CAAC;IAExC;IACAuF,KAAK,CAAC,CAAA1D,MAAO,GAAG,IAAI,CAAC,CAAAA,MAAO;IAE5B;IACA,IAAI,IAAI,CAAC,CAAAE,IAAK,EAAE;MAAEwD,KAAK,CAAC,CAAAxD,IAAK,GAAG,IAAI,CAAC,CAAAA,IAAK;;IAC1CwD,KAAK,CAAC,CAAAvD,QAAS,GAAG,IAAI,CAAC,CAAAA,QAAS;IAEhC;IACAuD,KAAK,CAAC,CAAA3D,OAAQ,GAAGkB,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE,IAAI,CAAC,CAAAnB,OAAQ,CAAC;IAElD;IACA2D,KAAK,CAAC,CAAAtD,KAAM,GAAG,IAAI,CAAC,CAAAA,KAAM;IAE1B,IAAI,IAAI,CAACe,SAAS,EAAE;MAAEuC,KAAK,CAACvC,SAAS,GAAG,IAAI;;IAE5CuC,KAAK,CAACzD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,IAAI,IAAI,CAACoC,2BAA2B,EAAE;MAAEqB,KAAK,CAACrB,2BAA2B,GAAG,IAAI;;IAEhFqB,KAAK,CAAC,CAAArD,SAAU,GAAG,IAAI,CAAC,CAAAA,SAAU;IAClCqD,KAAK,CAAC,CAAApD,OAAQ,GAAG,IAAI,CAAC,CAAAA,OAAQ;IAC9BoD,KAAK,CAAC,CAAAnD,KAAM,GAAG,IAAI,CAAC,CAAAA,KAAM;IAE1BmD,KAAK,CAAC,CAAAlD,QAAS,GAAGS,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE,IAAI,CAAC,CAAAV,QAAS,CAAC;IAEpDkD,KAAK,CAAC,CAAAjD,UAAW,GAAG,IAAI,CAAC,CAAAA,UAAW;IAEpC,OAAOiD,KAAK;EAChB;EAEA;;;;EAIA,OAAOsB,UAAUA,CAAA;IACb/G,MAAM,GAAG,IAAI;EACjB;EAEA;;;EAGA,OAAOgH,UAAUA,CAACtB,MAAc;IAC5B,OAAO7E,QAAQ,CAAC6E,MAAM,CAACpC,WAAW,EAAE,CAAC,IAAI,IAAI;EACjD;EAEA;;;;;;;;EAQA,OAAO2D,eAAeA,CAACvB,MAAc,EAAEwB,IAAsB;IACzDxB,MAAM,GAAGA,MAAM,CAACpC,WAAW,EAAE;IAC7B,IAAIoC,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,OAAO,EAAE;MACzC,MAAM,IAAIrF,KAAK,CAAC,oBAAqBqF,MAAO,sBAAsB,CAAC;;IAEvE,IAAI1F,MAAM,EAAE;MAAE,MAAM,IAAIK,KAAK,CAAC,iBAAiB,CAAC;;IAChDQ,QAAQ,CAAC6E,MAAM,CAAC,GAAGwB,IAAI;EAC3B;EAEA;;;;;;;;EAQA,OAAOC,cAAcA,CAACC,MAAuB;IACzC,IAAIpH,MAAM,EAAE;MAAE,MAAM,IAAIK,KAAK,CAAC,iBAAiB,CAAC;;IAChDT,iBAAiB,GAAGwH,MAAM;EAC9B;EAEA;;;;;;;;;;;EAWA,OAAOC,gBAAgBA,CAACC,OAA6B;IACjD,OAAO7H,YAAY,CAAC6H,OAAO,CAAC;EAChC;EAEA;;;;;;;;;EASA,OAAOC,iBAAiBA,CAAA;IACpB,OAAOtH,eAAe;EAC1B;EAEA;;;;;;;EAOA,OAAOuH,qBAAqBA,CAAC7G,OAAe;IACxC,OAAOD,kBAAkB,CAACC,OAAO,CAAC;EACtC;;AAOH;AAED;;;AAGA,OAAM,MAAOL,aAAa;EACtB,CAAA2F,UAAW;EACX,CAAAC,aAAc;EACd,CAAApE,OAAQ;EACR,CAAAG,IAAK;EACL,CAAAb,OAAQ;EAER,CAAAZ,KAAM;EAENkE,QAAQA,CAAA;IACJ,OAAO,yBAA0B,IAAI,CAACuB,UAAW,SAAU,IAAI,CAAC,CAAAhE,IAAK,GAAG9C,OAAO,CAAC,IAAI,CAAC,CAAA8C,IAAK,CAAC,GAAE,MAAO,GAAG;EAC3G;EAEA;;;EAGA,IAAIgE,UAAUA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,UAAW;EAAE;EAEpD;;;EAGA,IAAIC,aAAaA,CAAA;IAAa,OAAO,IAAI,CAAC,CAAAA,aAAc;EAAE;EAE1D;;;EAGA,IAAIpE,OAAOA,CAAA;IAA6B,OAAOkB,MAAM,CAACC,MAAM,CAAC,EAAG,EAAE,IAAI,CAAC,CAAAnB,OAAQ,CAAC;EAAE;EAElF;;;EAGA,IAAIG,IAAIA,CAAA;IACJ,OAAQ,IAAI,CAAC,CAAAA,IAAK,IAAI,IAAI,GAAI,IAAI,GAAE,IAAIS,UAAU,CAAC,IAAI,CAAC,CAAAT,IAAK,CAAC;EAClE;EAEA;;;;;;EAMA,IAAIwF,QAAQA,CAAA;IACR,IAAI;MACA,OAAQ,IAAI,CAAC,CAAAxF,IAAK,IAAI,IAAI,GAAI,EAAE,GAAEzC,YAAY,CAAC,IAAI,CAAC,CAAAyC,IAAK,CAAC;KAC7D,CAAC,OAAOzB,KAAK,EAAE;MACZpB,MAAM,CAAC,KAAK,EAAE,uCAAuC,EAAE,uBAAuB,EAAE;QAC5EqC,SAAS,EAAE,UAAU;QAAEiG,IAAI,EAAE;UAAE7B,QAAQ,EAAE;QAAI;OAChD,CAAC;;EAEV;EAEA;;;;;;EAMA,IAAI8B,QAAQA,CAAA;IACR,IAAI;MACA,OAAO/E,IAAI,CAACgF,KAAK,CAAC,IAAI,CAACH,QAAQ,CAAC;KACnC,CAAC,OAAOjH,KAAK,EAAE;MACZpB,MAAM,CAAC,KAAK,EAAE,iCAAiC,EAAE,uBAAuB,EAAE;QACtEqC,SAAS,EAAE,UAAU;QAAEiG,IAAI,EAAE;UAAE7B,QAAQ,EAAE;QAAI;OAChD,CAAC;;EAEV;EAEA,CAACnC,MAAM,CAACC,QAAQ,IAAC;IACb,MAAM7B,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAM8B,IAAI,GAAGZ,MAAM,CAACY,IAAI,CAAC9B,OAAO,CAAC;IACjC,IAAI+B,KAAK,GAAG,CAAC;IACb,OAAO;MACHC,IAAI,EAAEA,CAAA,KAAK;QACP,IAAID,KAAK,GAAGD,IAAI,CAACT,MAAM,EAAE;UACrB,MAAME,GAAG,GAAGO,IAAI,CAACC,KAAK,EAAE,CAAC;UACzB,OAAO;YACHL,KAAK,EAAE,CAAEH,GAAG,EAAEvB,OAAO,CAACuB,GAAG,CAAC,CAAE;YAAEU,IAAI,EAAE;WACvC;;QAEL,OAAO;UAAEP,KAAK,EAAEb,SAAS;UAAEoB,IAAI,EAAE;QAAI,CAAE;MAC3C;KACH;EACL;EAEA5C,YAAY8E,UAAkB,EAAEC,aAAqB,EAAEpE,OAAyC,EAAEG,IAAuB,EAAEb,OAAsB;IAC7I,IAAI,CAAC,CAAA6E,UAAW,GAAGA,UAAU;IAC7B,IAAI,CAAC,CAAAC,aAAc,GAAGA,aAAa;IACnC,IAAI,CAAC,CAAApE,OAAQ,GAAGkB,MAAM,CAACY,IAAI,CAAC9B,OAAO,CAAC,CAAC+F,MAAM,CAAC,CAACC,KAAK,EAAEC,CAAC,KAAI;MACrDD,KAAK,CAACC,CAAC,CAACzE,WAAW,EAAE,CAAC,GAAGb,MAAM,CAACX,OAAO,CAACiG,CAAC,CAAC,CAAC;MAC3C,OAAOD,KAAK;IAChB,CAAC,EAA0B,EAAG,CAAC;IAC/B,IAAI,CAAC,CAAA7F,IAAK,GAAKA,IAAI,IAAI,IAAI,GAAI,IAAI,GAAE,IAAIS,UAAU,CAACT,IAAI,CAAE;IAC1D,IAAI,CAAC,CAAAb,OAAQ,GAAIA,OAAO,IAAI,IAAK;IAEjC,IAAI,CAAC,CAAAZ,KAAM,GAAG;MAAEwH,OAAO,EAAE;IAAE,CAAE;EACjC;EAEA;;;;;EAKA5C,eAAeA,CAAC4C,OAAgB,EAAExH,KAAa;IAC3C,IAAI0F,aAAqB;IACzB,IAAI,CAAC8B,OAAO,EAAE;MACVA,OAAO,GAAG,GAAI,IAAI,CAAC/B,UAAW,IAAK,IAAI,CAACC,aAAc,EAAE;MACxDA,aAAa,GAAG,kCAAmC8B,OAAQ,GAAG;KACjE,MAAM;MACH9B,aAAa,GAAG,kCAAmC,IAAI,CAACD,UAAW,IAAK,IAAI,CAACC,aAAc,KAAM8B,OAAQ,GAAG;;IAEhH,MAAMnC,QAAQ,GAAG,IAAIvF,aAAa,CAAC,GAAG,EAAE4F,aAAa,EAAE,IAAI,CAACpE,OAAO,EAC/D,IAAI,CAACG,IAAI,EAAE,IAAI,CAAC,CAAAb,OAAQ,IAAIuB,SAAS,CAAC;IAC1CkD,QAAQ,CAAC,CAAArF,KAAM,GAAG;MAAEwH,OAAO;MAAExH;IAAK,CAAE;IACpC,OAAOqF,QAAQ;EACnB;EAEA;;;;;EAKAoC,kBAAkBA,CAACD,OAAgB,EAAElC,KAAc;IAC/C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACfA,KAAK,GAAG,CAAC,CAAC;KACb,MAAM;MACHzG,cAAc,CAAC6I,MAAM,CAACC,SAAS,CAACrC,KAAK,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE,uBAAuB,EAAE,OAAO,EAAEA,KAAK,CAAC;;IAGlG,MAAMtF,KAAK,GAAG,IAAIH,KAAK,CAAC2H,OAAO,IAAI,qBAAqB,CAAC;IAEzD1I,gBAAgB,CAAgBkB,KAAK,EAAE;MAAEsF,KAAK;MAAEvD,QAAQ,EAAE;IAAI,CAAE,CAAC;IAEjE,MAAM/B,KAAK;EACf;EAEA;;;EAGA4C,SAASA,CAACC,GAAW;IACjB,OAAO,IAAI,CAACvB,OAAO,CAACuB,GAAG,CAACC,WAAW,EAAE,CAAC;EAC1C;EAEA;;;EAGAR,OAAOA,CAAA;IACH,OAAQ,IAAI,CAAC,CAAAb,IAAK,IAAI,IAAI;EAC9B;EAEA;;;EAGA,IAAIb,OAAOA,CAAA;IAA0B,OAAO,IAAI,CAAC,CAAAA,OAAQ;EAAE;EAE3D;;;EAGAgH,EAAEA,CAAA;IACE,OAAQ,IAAI,CAAC,CAAA5H,KAAM,CAACwH,OAAO,KAAK,EAAE,IAAI,IAAI,CAAC/B,UAAU,IAAI,GAAG,IAAI,IAAI,CAACA,UAAU,GAAG,GAAG;EACzF;EAEA;;;EAGAF,QAAQA,CAAA;IACJ,IAAI,IAAI,CAACqC,EAAE,EAAE,EAAE;MAAE;;IACjB,IAAI;MAAEJ,OAAO;MAAExH;IAAK,CAAE,GAAG,IAAI,CAAC,CAAAA,KAAM;IACpC,IAAIwH,OAAO,KAAK,EAAE,EAAE;MAChBA,OAAO,GAAG,mBAAoB,IAAI,CAAC/B,UAAW,IAAK,IAAI,CAACC,aAAc,EAAE;;IAG5E,IAAImC,UAAU,GAAkB,IAAI;IACpC,IAAI,IAAI,CAACjH,OAAO,EAAE;MAAEiH,UAAU,GAAG,IAAI,CAACjH,OAAO,CAAClB,GAAG;;IAEjD,IAAIoI,YAAY,GAAkB,IAAI;IACtC,IAAI;MACA,IAAI,IAAI,CAAC,CAAArG,IAAK,EAAE;QAAEqG,YAAY,GAAG9I,YAAY,CAAC,IAAI,CAAC,CAAAyC,IAAK,CAAC;;KAC5D,CAAC,OAAOsG,CAAC,EAAE;IAEZnJ,MAAM,CAAC,KAAK,EAAE4I,OAAO,EAAE,cAAc,EAAE;MACnC5G,OAAO,EAAG,IAAI,CAACA,OAAO,IAAI,iBAAkB;MAAEyE,QAAQ,EAAE,IAAI;MAAErF,KAAK;MACnEkH,IAAI,EAAE;QACFW,UAAU;QAAEC,YAAY;QACxBE,cAAc,EAAE,GAAI,IAAI,CAACvC,UAAW,IAAK,IAAI,CAACC,aAAc;;KACnE,CAAC;EACN;;AAIJ,SAASb,OAAOA,CAAA;EAAa,OAAQ,IAAIoD,IAAI,EAAE,CAAEpD,OAAO,EAAE;AAAE;AAE5D,SAAS9E,SAASA,CAACiD,KAAa;EAC5B,OAAOjE,WAAW,CAACiE,KAAK,CAACkF,OAAO,CAAC,uBAAuB,EAAE,CAACC,GAAG,EAAEC,IAAI,KAAI;IACpE,OAAOnG,MAAM,CAACoG,YAAY,CAACnC,QAAQ,CAACkC,IAAI,EAAE,EAAE,CAAC,CAAC;EAClD,CAAC,CAAC,CAAC;AACP;AAEA,SAASrD,IAAIA,CAACN,KAAa;EACvB,OAAO,IAAI6D,OAAO,CAAEC,OAAO,IAAKxH,UAAU,CAACwH,OAAO,EAAE9D,KAAK,CAAC,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}