{"ast": null, "code": "/**\n *  A **Provider** provides a connection to the blockchain, whch can be\n *  used to query its current state, simulate execution and send transactions\n *  to update the state.\n *\n *  It is one of the most fundamental components of interacting with a\n *  blockchain application, and there are many ways to connect, such as over\n *  HTTP, WebSockets or injected providers such as [MetaMask](link-metamask).\n *\n *  @_section: api/providers:Providers  [about-providers]\n */\nexport { AbstractProvider, UnmanagedSubscriber } from \"./abstract-provider.js\";\nexport { AbstractSigner, VoidSigner } from \"./abstract-signer.js\";\nexport { showThrottleMessage } from \"./community.js\";\nexport { getDefaultProvider } from \"./default-provider.js\";\nexport { EnsResolver, MulticoinProviderPlugin } from \"./ens-resolver.js\";\nexport { Network } from \"./network.js\";\nexport { NonceManager } from \"./signer-noncemanager.js\";\nexport { NetworkPlugin, GasCostPlugin, EnsPlugin, FeeDataNetworkPlugin, FetchUrlFeeDataNetworkPlugin } from \"./plugins-network.js\";\nexport { Block, FeeData, Log, TransactionReceipt, TransactionResponse, copyRequest\n//resolveTransactionRequest,\n} from \"./provider.js\";\nexport { FallbackProvider } from \"./provider-fallback.js\";\nexport { JsonRpcApiProvider, JsonRpcProvider, JsonRpcSigner } from \"./provider-jsonrpc.js\";\nexport { BrowserProvider } from \"./provider-browser.js\";\nexport { AlchemyProvider } from \"./provider-alchemy.js\";\nexport { BlockscoutProvider } from \"./provider-blockscout.js\";\nexport { AnkrProvider } from \"./provider-ankr.js\";\nexport { CloudflareProvider } from \"./provider-cloudflare.js\";\nexport { ChainstackProvider } from \"./provider-chainstack.js\";\nexport { EtherscanProvider, EtherscanPlugin } from \"./provider-etherscan.js\";\nexport { InfuraProvider, InfuraWebSocketProvider } from \"./provider-infura.js\";\nexport { PocketProvider } from \"./provider-pocket.js\";\nexport { QuickNodeProvider } from \"./provider-quicknode.js\";\nimport { IpcSocketProvider } from \"./provider-ipcsocket.js\"; /*-browser*/\nexport { IpcSocketProvider };\nexport { SocketProvider } from \"./provider-socket.js\";\nexport { WebSocketProvider } from \"./provider-websocket.js\";\nexport { SocketSubscriber, SocketBlockSubscriber, SocketPendingSubscriber, SocketEventSubscriber } from \"./provider-socket.js\";", "map": {"version": 3, "names": ["AbstractProvider", "UnmanagedSubscriber", "Abstract<PERSON><PERSON><PERSON>", "<PERSON>oid<PERSON><PERSON><PERSON>", "showThrottleMessage", "getDefaultProvider", "EnsResolver", "MulticoinProviderPlugin", "Network", "<PERSON>ceManager", "NetworkPlugin", "GasCostPlugin", "EnsPlugin", "FeeDataNetworkPlugin", "FetchUrlFeeDataNetworkPlugin", "Block", "FeeData", "Log", "TransactionReceipt", "TransactionResponse", "copyRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "JsonRpcApiProvider", "JsonRpcProvider", "JsonRpcSigner", "Browser<PERSON>rovider", "AlchemyProvider", "BlockscoutProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChainstackProvider", "EtherscanProvider", "EtherscanPlugin", "InfuraProvider", "InfuraWebSocketProvider", "PocketProvider", "QuickNodeProvider", "IpcSocketProvider", "SocketProvider", "WebSocketProvider", "SocketSubscriber", "SocketBlockSubscriber", "SocketPendingSubscriber", "SocketEventSubscriber"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\index.ts"], "sourcesContent": ["/**\n *  A **Provider** provides a connection to the blockchain, whch can be\n *  used to query its current state, simulate execution and send transactions\n *  to update the state.\n *\n *  It is one of the most fundamental components of interacting with a\n *  blockchain application, and there are many ways to connect, such as over\n *  HTTP, WebSockets or injected providers such as [MetaMask](link-metamask).\n *\n *  @_section: api/providers:Providers  [about-providers]\n */\n\n\nexport {\n    AbstractProvider, UnmanagedSubscriber\n} from \"./abstract-provider.js\";\n\nexport {\n    AbstractSigner,\n    VoidSigner,\n} from \"./abstract-signer.js\";\n\nexport {\n    showThrottleMessage\n} from \"./community.js\";\n\nexport { getDefaultProvider } from \"./default-provider.js\";\n\nexport {\n    EnsResolver,\n    MulticoinProviderPlugin\n} from \"./ens-resolver.js\";\n\nexport { Network } from \"./network.js\";\n\nexport { NonceManager } from \"./signer-noncemanager.js\";\n\nexport {\n    NetworkPlugin,\n    GasCostPlugin,\n    EnsPlugin,\n    FeeDataNetworkPlugin,\n    FetchUrlFeeDataNetworkPlugin,\n} from \"./plugins-network.js\";\n\nexport {\n    Block,\n    FeeData,\n    Log,\n    TransactionReceipt,\n    TransactionResponse,\n\n    copyRequest,\n    //resolveTransactionRequest,\n} from \"./provider.js\";\n\nexport { FallbackProvider } from \"./provider-fallback.js\";\nexport { JsonRpcApiProvider, JsonRpcProvider, JsonRpcSigner } from \"./provider-jsonrpc.js\"\n\nexport { BrowserProvider } from \"./provider-browser.js\";\n\nexport { AlchemyProvider } from \"./provider-alchemy.js\";\nexport { BlockscoutProvider } from \"./provider-blockscout.js\";\nexport { AnkrProvider } from \"./provider-ankr.js\";\nexport { CloudflareProvider } from \"./provider-cloudflare.js\";\nexport { ChainstackProvider } from \"./provider-chainstack.js\";\nexport { EtherscanProvider, EtherscanPlugin } from \"./provider-etherscan.js\";\nexport { InfuraProvider, InfuraWebSocketProvider } from \"./provider-infura.js\";\nexport { PocketProvider } from \"./provider-pocket.js\";\nexport { QuickNodeProvider } from \"./provider-quicknode.js\";\n\nimport { IpcSocketProvider } from \"./provider-ipcsocket.js\"; /*-browser*/\nexport { IpcSocketProvider };\nexport { SocketProvider } from \"./provider-socket.js\";\nexport { WebSocketProvider } from \"./provider-websocket.js\";\n\nexport {\n    SocketSubscriber, SocketBlockSubscriber, SocketPendingSubscriber,\n    SocketEventSubscriber\n} from \"./provider-socket.js\";\n\nexport type {\n    AbstractProviderOptions, Subscription, Subscriber,\n    AbstractProviderPlugin,\n    PerformActionFilter, PerformActionTransaction, PerformActionRequest,\n} from \"./abstract-provider.js\"\n\nexport type { ContractRunner } from \"./contracts.js\";\n\nexport type {\n    BlockParams, LogParams, TransactionReceiptParams,\n    TransactionResponseParams,\n} from \"./formatting.js\";\n\nexport type {\n    CommunityResourcable\n} from \"./community.js\";\n\n/*\nexport type {\n    AvatarLinkageType, AvatarLinkage, AvatarResult\n} from \"./ens-resolver.js\";\n*/\nexport type { Networkish } from \"./network.js\";\n\nexport type { GasCostParameters } from \"./plugins-network.js\";\n\nexport type {\n    BlockTag,\n    TransactionRequest, PreparedTransactionRequest,\n    EventFilter, Filter, FilterByBlockHash, OrphanFilter, ProviderEvent,\n    TopicFilter,\n    Provider,\n    MinedBlock, MinedTransactionResponse\n} from \"./provider.js\";\n\nexport type {\n    BrowserDiscoverOptions, BrowserProviderOptions, DebugEventBrowserProvider,\n    Eip1193Provider, Eip6963ProviderInfo\n} from \"./provider-browser.js\";\n\nexport type { FallbackProviderOptions } from \"./provider-fallback.js\";\n\nexport type {\n    JsonRpcPayload, JsonRpcResult, JsonRpcError,\n    JsonRpcApiProviderOptions,\n    JsonRpcTransactionRequest,\n} from \"./provider-jsonrpc.js\";\n\nexport type {\n    WebSocketCreator, WebSocketLike\n} from \"./provider-websocket.js\";\n\nexport type { Signer } from \"./signer.js\";\n\n"], "mappings": "AAAA;;;;;;;;;;;AAaA,SACIA,gBAAgB,EAAEC,mBAAmB,QAClC,wBAAwB;AAE/B,SACIC,cAAc,EACdC,UAAU,QACP,sBAAsB;AAE7B,SACIC,mBAAmB,QAChB,gBAAgB;AAEvB,SAASC,kBAAkB,QAAQ,uBAAuB;AAE1D,SACIC,WAAW,EACXC,uBAAuB,QACpB,mBAAmB;AAE1B,SAASC,OAAO,QAAQ,cAAc;AAEtC,SAASC,YAAY,QAAQ,0BAA0B;AAEvD,SACIC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,oBAAoB,EACpBC,4BAA4B,QACzB,sBAAsB;AAE7B,SACIC,KAAK,EACLC,OAAO,EACPC,GAAG,EACHC,kBAAkB,EAClBC,mBAAmB,EAEnBC;AACA;AAAA,OACG,eAAe;AAEtB,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,kBAAkB,EAAEC,eAAe,EAAEC,aAAa,QAAQ,uBAAuB;AAE1F,SAASC,eAAe,QAAQ,uBAAuB;AAEvD,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,iBAAiB,EAAEC,eAAe,QAAQ,yBAAyB;AAC5E,SAASC,cAAc,EAAEC,uBAAuB,QAAQ,sBAAsB;AAC9E,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,iBAAiB,QAAQ,yBAAyB;AAE3D,SAASC,iBAAiB,QAAQ,yBAAyB,CAAC,CAAC;AAC7D,SAASA,iBAAiB;AAC1B,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,iBAAiB,QAAQ,yBAAyB;AAE3D,SACIC,gBAAgB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAChEC,qBAAqB,QAClB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}