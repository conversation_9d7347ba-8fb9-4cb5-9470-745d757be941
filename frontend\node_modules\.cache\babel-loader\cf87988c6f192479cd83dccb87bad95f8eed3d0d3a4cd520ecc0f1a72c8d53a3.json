{"ast": null, "code": "import { createHash } from \"./crypto.js\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\nconst _sha256 = function (data) {\n  return createHash(\"sha256\").update(data).digest();\n};\nconst _sha512 = function (data) {\n  return createHash(\"sha512\").update(data).digest();\n};\nlet __sha256 = _sha256;\nlet __sha512 = _sha512;\nlet locked256 = false,\n  locked512 = false;\n/**\n *  Compute the cryptographic SHA2-256 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    sha256(\"0x\")\n *    //_result:\n *\n *    sha256(\"0x1337\")\n *    //_result:\n *\n *    sha256(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n */\nexport function sha256(_data) {\n  const data = getBytes(_data, \"data\");\n  return hexlify(__sha256(data));\n}\nsha256._ = _sha256;\nsha256.lock = function () {\n  locked256 = true;\n};\nsha256.register = function (func) {\n  if (locked256) {\n    throw new Error(\"sha256 is locked\");\n  }\n  __sha256 = func;\n};\nObject.freeze(sha256);\n/**\n *  Compute the cryptographic SHA2-512 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    sha512(\"0x\")\n *    //_result:\n *\n *    sha512(\"0x1337\")\n *    //_result:\n *\n *    sha512(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n */\nexport function sha512(_data) {\n  const data = getBytes(_data, \"data\");\n  return hexlify(__sha512(data));\n}\nsha512._ = _sha512;\nsha512.lock = function () {\n  locked512 = true;\n};\nsha512.register = function (func) {\n  if (locked512) {\n    throw new Error(\"sha512 is locked\");\n  }\n  __sha512 = func;\n};\nObject.freeze(sha256);", "map": {"version": 3, "names": ["createHash", "getBytes", "hexlify", "_sha256", "data", "update", "digest", "_sha512", "__sha256", "__sha512", "locked256", "locked512", "sha256", "_data", "_", "lock", "register", "func", "Error", "Object", "freeze", "sha512"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\sha2.ts"], "sourcesContent": ["import { createHash } from \"./crypto.js\";\n\nimport { getBytes, hexlify } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n\nconst _sha256 = function(data: Uint8Array): Uint8Array {\n    return createHash(\"sha256\").update(data).digest();\n}\n\nconst _sha512 = function(data: Uint8Array): Uint8Array {\n    return createHash(\"sha512\").update(data).digest();\n}\n\nlet __sha256: (data: Uint8Array) => BytesLike = _sha256;\nlet __sha512: (data: Uint8Array) => BytesLike = _sha512;\n\nlet locked256 = false, locked512 = false;\n\n\n/**\n *  Compute the cryptographic SHA2-256 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    sha256(\"0x\")\n *    //_result:\n *\n *    sha256(\"0x1337\")\n *    //_result:\n *\n *    sha256(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n */\nexport function sha256(_data: BytesLike): string {\n    const data = getBytes(_data, \"data\");\n    return hexlify(__sha256(data));\n}\nsha256._ = _sha256;\nsha256.lock = function(): void { locked256 = true; }\nsha256.register = function(func: (data: Uint8Array) => BytesLike): void {\n    if (locked256) { throw new Error(\"sha256 is locked\"); }\n    __sha256 = func;\n}\nObject.freeze(sha256);\n\n\n/**\n *  Compute the cryptographic SHA2-512 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    sha512(\"0x\")\n *    //_result:\n *\n *    sha512(\"0x1337\")\n *    //_result:\n *\n *    sha512(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n */\nexport function sha512(_data: BytesLike): string {\n    const data = getBytes(_data, \"data\");\n    return hexlify(__sha512(data));\n}\nsha512._ = _sha512;\nsha512.lock = function(): void { locked512 = true; }\nsha512.register = function(func: (data: Uint8Array) => BytesLike): void {\n    if (locked512) { throw new Error(\"sha512 is locked\"); }\n    __sha512 = func;\n}\nObject.freeze(sha256);\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,aAAa;AAExC,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mBAAmB;AAKrD,MAAMC,OAAO,GAAG,SAAAA,CAASC,IAAgB;EACrC,OAAOJ,UAAU,CAAC,QAAQ,CAAC,CAACK,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,EAAE;AACrD,CAAC;AAED,MAAMC,OAAO,GAAG,SAAAA,CAASH,IAAgB;EACrC,OAAOJ,UAAU,CAAC,QAAQ,CAAC,CAACK,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,EAAE;AACrD,CAAC;AAED,IAAIE,QAAQ,GAAoCL,OAAO;AACvD,IAAIM,QAAQ,GAAoCF,OAAO;AAEvD,IAAIG,SAAS,GAAG,KAAK;EAAEC,SAAS,GAAG,KAAK;AAGxC;;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAUC,MAAMA,CAACC,KAAgB;EACnC,MAAMT,IAAI,GAAGH,QAAQ,CAACY,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOX,OAAO,CAACM,QAAQ,CAACJ,IAAI,CAAC,CAAC;AAClC;AACAQ,MAAM,CAACE,CAAC,GAAGX,OAAO;AAClBS,MAAM,CAACG,IAAI,GAAG;EAAmBL,SAAS,GAAG,IAAI;AAAE,CAAC;AACpDE,MAAM,CAACI,QAAQ,GAAG,UAASC,IAAqC;EAC5D,IAAIP,SAAS,EAAE;IAAE,MAAM,IAAIQ,KAAK,CAAC,kBAAkB,CAAC;;EACpDV,QAAQ,GAAGS,IAAI;AACnB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACR,MAAM,CAAC;AAGrB;;;;;;;;;;;;;;;;AAgBA,OAAM,SAAUS,MAAMA,CAACR,KAAgB;EACnC,MAAMT,IAAI,GAAGH,QAAQ,CAACY,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOX,OAAO,CAACO,QAAQ,CAACL,IAAI,CAAC,CAAC;AAClC;AACAiB,MAAM,CAACP,CAAC,GAAGP,OAAO;AAClBc,MAAM,CAACN,IAAI,GAAG;EAAmBJ,SAAS,GAAG,IAAI;AAAE,CAAC;AACpDU,MAAM,CAACL,QAAQ,GAAG,UAASC,IAAqC;EAC5D,IAAIN,SAAS,EAAE;IAAE,MAAM,IAAIO,KAAK,CAAC,kBAAkB,CAAC;;EACpDT,QAAQ,GAAGQ,IAAI;AACnB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACR,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}