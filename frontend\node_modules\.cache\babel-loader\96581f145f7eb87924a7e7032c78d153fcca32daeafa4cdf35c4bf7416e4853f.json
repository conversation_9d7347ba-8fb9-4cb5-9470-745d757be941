{"ast": null, "code": "/**\n *  About Cloudflare\n *\n *  @_subsection: api/providers/thirdparty:Cloudflare  [providers-cloudflare]\n */\nimport { assertArgument } from \"../utils/index.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n/**\n *  About Cloudflare...\n */\nexport class CloudflareProvider extends JsonRpcProvider {\n  constructor(_network) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    assertArgument(network.name === \"mainnet\", \"unsupported network\", \"network\", _network);\n    super(\"https:/\\/cloudflare-eth.com/\", network, {\n      staticNetwork: network\n    });\n  }\n}", "map": {"version": 3, "names": ["assertArgument", "Network", "JsonRpcProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "_network", "network", "from", "name", "staticNetwork"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-cloudflare.ts"], "sourcesContent": ["/**\n *  About Cloudflare\n *\n *  @_subsection: api/providers/thirdparty:Cloudflare  [providers-cloudflare]\n */\n\nimport { assertArgument } from \"../utils/index.js\";\n\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { Networkish } from \"./network.js\";\n\n/**\n *  About Cloudflare...\n */\nexport class CloudflareProvider extends JsonRpcProvider {\n    constructor(_network?: Networkish) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n        assertArgument(network.name === \"mainnet\", \"unsupported network\", \"network\", _network);\n        super(\"https:/\\/cloudflare-eth.com/\", network, { staticNetwork: network });\n    }\n}\n"], "mappings": "AAAA;;;;;AAMA,SAASA,cAAc,QAAQ,mBAAmB;AAElD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAIvD;;;AAGA,OAAM,MAAOC,kBAAmB,SAAQD,eAAe;EACnDE,YAAYC,QAAqB;IAC7B,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGL,OAAO,CAACM,IAAI,CAACF,QAAQ,CAAC;IACtCL,cAAc,CAACM,OAAO,CAACE,IAAI,KAAK,SAAS,EAAE,qBAAqB,EAAE,SAAS,EAAEH,QAAQ,CAAC;IACtF,KAAK,CAAC,8BAA8B,EAAEC,OAAO,EAAE;MAAEG,aAAa,EAAEH;IAAO,CAAE,CAAC;EAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}