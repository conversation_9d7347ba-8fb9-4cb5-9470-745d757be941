{"name": "cryptoquest-backend", "version": "1.0.0", "description": "Backend API for CryptoQuest game", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate-fixed.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "mysql2": "^3.6.5", "ethers": "^6.8.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["web3", "game", "api", "blockchain"], "author": "CryptoQuest Team", "license": "MIT"}