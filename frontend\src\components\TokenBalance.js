import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useWeb3 } from '../contexts/Web3Context';
import { useAuth } from '../contexts/AuthContext';
import { CurrencyDollarIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const TokenBalance = ({ showRefresh = true, className = '' }) => {
  const { user } = useAuth();
  const { getTokenBalance } = useWeb3();
  const [balance, setBalance] = useState('0');
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(null);

  useEffect(() => {
    loadBalance();
  }, [user?.walletAddress]);

  const loadBalance = async () => {
    if (!user?.walletAddress) return;
    
    try {
      setLoading(true);
      const tokenBalance = await getTokenBalance(user.walletAddress);
      setBalance(parseFloat(tokenBalance).toFixed(2));
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to load token balance:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadBalance();
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className="flex items-center space-x-2 bg-dark-800 px-3 py-2 rounded-lg border border-dark-600">
        <div className="w-5 h-5 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center">
          <CurrencyDollarIcon className="w-3 h-3 text-yellow-900" />
        </div>
        
        <div className="flex flex-col">
          <motion.span
            key={balance}
            className="text-white font-bold text-sm"
            initial={{ scale: 1.2, color: '#fbbf24' }}
            animate={{ scale: 1, color: '#ffffff' }}
            transition={{ duration: 0.3 }}
          >
            {loading ? '...' : balance} CQT
          </motion.span>
          {lastUpdated && (
            <span className="text-xs text-dark-400">
              Updated {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {showRefresh && (
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="p-2 text-dark-400 hover:text-white transition-colors disabled:opacity-50"
          title="Refresh balance"
        >
          <motion.div
            animate={loading ? { rotate: 360 } : {}}
            transition={loading ? { duration: 1, repeat: Infinity, ease: 'linear' } : {}}
          >
            <ArrowPathIcon className="w-4 h-4" />
          </motion.div>
        </button>
      )}
    </div>
  );
};

export default TokenBalance;
