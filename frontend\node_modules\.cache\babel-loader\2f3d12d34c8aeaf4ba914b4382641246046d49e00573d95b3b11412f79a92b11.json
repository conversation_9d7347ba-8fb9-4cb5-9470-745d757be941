{"ast": null, "code": "import { defineProperties, fromTwos, getBigInt, mask, toTwos } from \"../../utils/index.js\";\nimport { Typed } from \"../typed.js\";\nimport { Coder, WordSize } from \"./abstract-coder.js\";\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_MAX_UINT256 = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n/**\n *  @_ignore\n */\nexport class NumberCoder extends Coder {\n  size;\n  signed;\n  constructor(size, signed, localName) {\n    const name = (signed ? \"int\" : \"uint\") + size * 8;\n    super(name, name, localName, false);\n    defineProperties(this, {\n      size,\n      signed\n    }, {\n      size: \"number\",\n      signed: \"boolean\"\n    });\n  }\n  defaultValue() {\n    return 0;\n  }\n  encode(writer, _value) {\n    let value = getBigInt(Typed.dereference(_value, this.type));\n    // Check bounds are safe for encoding\n    let maxUintValue = mask(BN_MAX_UINT256, WordSize * 8);\n    if (this.signed) {\n      let bounds = mask(maxUintValue, this.size * 8 - 1);\n      if (value > bounds || value < -(bounds + BN_1)) {\n        this._throwError(\"value out-of-bounds\", _value);\n      }\n      value = toTwos(value, 8 * WordSize);\n    } else if (value < BN_0 || value > mask(maxUintValue, this.size * 8)) {\n      this._throwError(\"value out-of-bounds\", _value);\n    }\n    return writer.writeValue(value);\n  }\n  decode(reader) {\n    let value = mask(reader.readValue(), this.size * 8);\n    if (this.signed) {\n      value = fromTwos(value, this.size * 8);\n    }\n    return value;\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "fromTwos", "getBigInt", "mask", "toTwos", "Typed", "Coder", "WordSize", "BN_0", "BigInt", "BN_1", "BN_MAX_UINT256", "NumberCoder", "size", "signed", "constructor", "localName", "name", "defaultValue", "encode", "writer", "_value", "value", "dereference", "type", "max<PERSON>int<PERSON><PERSON><PERSON>", "bounds", "_throwError", "writeValue", "decode", "reader", "readValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\number.ts"], "sourcesContent": ["import {\n    defineProperties, fromTwos, getBigInt, mask, toTwos\n} from \"../../utils/index.js\";\n\nimport { Typed } from \"../typed.js\";\nimport { Coder, WordSize } from \"./abstract-coder.js\";\n\nimport type { BigNumberish } from \"../../utils/index.js\";\n\nimport type { Reader, Writer } from \"./abstract-coder.js\";\n\n\nconst BN_0 = BigInt(0);\nconst BN_1 = BigInt(1);\nconst BN_MAX_UINT256 = BigInt(\"0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff\");\n\n/**\n *  @_ignore\n */\nexport class NumberCoder extends Coder {\n    readonly size!: number;\n    readonly signed!: boolean;\n\n    constructor(size: number, signed: boolean, localName: string) {\n        const name = ((signed ? \"int\": \"uint\") + (size * 8));\n        super(name, name, localName, false);\n\n        defineProperties<NumberCoder>(this, { size, signed }, { size: \"number\", signed: \"boolean\" });\n    }\n\n    defaultValue(): number {\n        return 0;\n    }\n\n    encode(writer: Writer, _value: BigNumberish | Typed): number {\n        let value = getBigInt(Typed.dereference(_value, this.type));\n\n        // Check bounds are safe for encoding\n        let maxUintValue = mask(BN_MAX_UINT256, WordSize * 8);\n        if (this.signed) {\n            let bounds = mask(maxUintValue, (this.size * 8) - 1);\n            if (value > bounds || value < -(bounds + BN_1)) {\n                this._throwError(\"value out-of-bounds\", _value);\n            }\n            value = toTwos(value, 8 * WordSize);\n        } else if (value < BN_0 || value > mask(maxUintValue, this.size * 8)) {\n            this._throwError(\"value out-of-bounds\", _value);\n        }\n\n        return writer.writeValue(value);\n    }\n\n    decode(reader: Reader): any {\n        let value = mask(reader.readValue(), this.size * 8);\n\n        if (this.signed) {\n            value = fromTwos(value, this.size * 8);\n        }\n\n        return value;\n    }\n}\n\n"], "mappings": "AAAA,SACIA,gBAAgB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,MAAM,QAChD,sBAAsB;AAE7B,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,EAAEC,QAAQ,QAAQ,qBAAqB;AAOrD,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AACtB,MAAMC,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC;AACtB,MAAME,cAAc,GAAGF,MAAM,CAAC,oEAAoE,CAAC;AAEnG;;;AAGA,OAAM,MAAOG,WAAY,SAAQN,KAAK;EACzBO,IAAI;EACJC,MAAM;EAEfC,YAAYF,IAAY,EAAEC,MAAe,EAAEE,SAAiB;IACxD,MAAMC,IAAI,GAAI,CAACH,MAAM,GAAG,KAAK,GAAE,MAAM,IAAKD,IAAI,GAAG,CAAG;IACpD,KAAK,CAACI,IAAI,EAAEA,IAAI,EAAED,SAAS,EAAE,KAAK,CAAC;IAEnChB,gBAAgB,CAAc,IAAI,EAAE;MAAEa,IAAI;MAAEC;IAAM,CAAE,EAAE;MAAED,IAAI,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE,CAAC;EAChG;EAEAI,YAAYA,CAAA;IACR,OAAO,CAAC;EACZ;EAEAC,MAAMA,CAACC,MAAc,EAAEC,MAA4B;IAC/C,IAAIC,KAAK,GAAGpB,SAAS,CAACG,KAAK,CAACkB,WAAW,CAACF,MAAM,EAAE,IAAI,CAACG,IAAI,CAAC,CAAC;IAE3D;IACA,IAAIC,YAAY,GAAGtB,IAAI,CAACQ,cAAc,EAAEJ,QAAQ,GAAG,CAAC,CAAC;IACrD,IAAI,IAAI,CAACO,MAAM,EAAE;MACb,IAAIY,MAAM,GAAGvB,IAAI,CAACsB,YAAY,EAAG,IAAI,CAACZ,IAAI,GAAG,CAAC,GAAI,CAAC,CAAC;MACpD,IAAIS,KAAK,GAAGI,MAAM,IAAIJ,KAAK,GAAG,EAAEI,MAAM,GAAGhB,IAAI,CAAC,EAAE;QAC5C,IAAI,CAACiB,WAAW,CAAC,qBAAqB,EAAEN,MAAM,CAAC;;MAEnDC,KAAK,GAAGlB,MAAM,CAACkB,KAAK,EAAE,CAAC,GAAGf,QAAQ,CAAC;KACtC,MAAM,IAAIe,KAAK,GAAGd,IAAI,IAAIc,KAAK,GAAGnB,IAAI,CAACsB,YAAY,EAAE,IAAI,CAACZ,IAAI,GAAG,CAAC,CAAC,EAAE;MAClE,IAAI,CAACc,WAAW,CAAC,qBAAqB,EAAEN,MAAM,CAAC;;IAGnD,OAAOD,MAAM,CAACQ,UAAU,CAACN,KAAK,CAAC;EACnC;EAEAO,MAAMA,CAACC,MAAc;IACjB,IAAIR,KAAK,GAAGnB,IAAI,CAAC2B,MAAM,CAACC,SAAS,EAAE,EAAE,IAAI,CAAClB,IAAI,GAAG,CAAC,CAAC;IAEnD,IAAI,IAAI,CAACC,MAAM,EAAE;MACbQ,KAAK,GAAGrB,QAAQ,CAACqB,KAAK,EAAE,IAAI,CAACT,IAAI,GAAG,CAAC,CAAC;;IAG1C,OAAOS,KAAK;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}