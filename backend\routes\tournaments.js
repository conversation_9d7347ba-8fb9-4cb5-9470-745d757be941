const express = require('express');
const { executeQuery } = require('../config/database');
const { verifyToken } = require('../middleware/auth');

const router = express.Router();

// Get active tournaments
router.get('/', async (req, res) => {
  try {
    const tournaments = await executeQuery(
      `SELECT t.*, COUNT(tp.user_address) as participants
       FROM tournaments t
       LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
       WHERE t.status IN ('upcoming', 'registration', 'active')
       GROUP BY t.id
       ORDER BY t.start_time ASC`
    );
    
    res.json({ tournaments });
  } catch (error) {
    console.error('Get tournaments error:', error);
    res.status(500).json({ error: 'Failed to get tournaments' });
  }
});

// Join tournament
router.post('/:tournamentId/join', verifyToken, async (req, res) => {
  try {
    const { walletAddress } = req.user;
    const { tournamentId } = req.params;
    const { heroIds } = req.body;
    
    // Check if tournament exists and is open for registration
    const tournaments = await executeQuery(
      'SELECT * FROM tournaments WHERE id = ? AND status = "registration"',
      [tournamentId]
    );
    
    if (tournaments.length === 0) {
      return res.status(404).json({ error: 'Tournament not found or not open for registration' });
    }
    
    const tournament = tournaments[0];
    
    // Check if already joined
    const existing = await executeQuery(
      'SELECT id FROM tournament_participants WHERE tournament_id = ? AND user_address = ?',
      [tournamentId, walletAddress]
    );
    
    if (existing.length > 0) {
      return res.status(400).json({ error: 'Already joined this tournament' });
    }
    
    // Verify heroes
    const heroes = await executeQuery(
      'SELECT * FROM heroes WHERE id IN (?) AND owner_address = ?',
      [heroIds, walletAddress]
    );
    
    if (heroes.length !== heroIds.length) {
      return res.status(400).json({ error: 'Invalid heroes selected' });
    }
    
    // Join tournament
    await executeQuery(
      `INSERT INTO tournament_participants (tournament_id, user_address, heroes)
       VALUES (?, ?, ?)`,
      [tournamentId, walletAddress, JSON.stringify(heroes)]
    );
    
    // Update participant count
    await executeQuery(
      'UPDATE tournaments SET current_participants = current_participants + 1 WHERE id = ?',
      [tournamentId]
    );
    
    res.json({ message: 'Successfully joined tournament' });
  } catch (error) {
    console.error('Join tournament error:', error);
    res.status(500).json({ error: 'Failed to join tournament' });
  }
});

module.exports = router;
