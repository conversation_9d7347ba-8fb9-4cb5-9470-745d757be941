@echo off
REM CryptoQuest Setup Script for Windows
REM This script automates the setup process for the CryptoQuest game

echo 🚀 Starting CryptoQuest Setup...
echo ==================================

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed. Please install Node.js v16 or higher.
    pause
    exit /b 1
) else (
    echo [SUCCESS] Node.js is installed
)

REM Check if MySQL is installed
echo [INFO] Checking MySQL installation...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] MySQL is not installed. Please install MySQL v8.0 or higher.
    pause
    exit /b 1
) else (
    echo [SUCCESS] MySQL is installed
)

REM Install dependencies
echo [INFO] Installing project dependencies...

echo [INFO] Installing root dependencies...
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo [INFO] Installing contract dependencies...
cd contracts
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install contract dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing frontend dependencies...
cd frontend
call npm install
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] All dependencies installed successfully!

REM Setup database
echo [INFO] Setting up database...
echo [INFO] Creating database 'cq_db'...
mysql -u root -e "CREATE DATABASE IF NOT EXISTS cq_db;"
if %errorlevel% neq 0 (
    echo [ERROR] Failed to create database. Please check MySQL connection.
    pause
    exit /b 1
)

echo [INFO] Running database migrations...
cd backend
call npm run migrate
if %errorlevel% neq 0 (
    echo [ERROR] Database migration failed.
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] Database setup completed!

REM Deploy smart contracts
echo [INFO] Deploying smart contracts...
cd contracts

echo [INFO] Starting Hardhat node...
start /b npx hardhat node
timeout /t 5 /nobreak >nul

echo [INFO] Compiling contracts...
call npx hardhat compile
if %errorlevel% neq 0 (
    echo [ERROR] Contract compilation failed
    pause
    exit /b 1
)

echo [INFO] Deploying contracts to local network...
call npx hardhat run scripts/deploy.js --network localhost > ..\deployment.log 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Contract deployment failed. Check deployment.log for details.
    pause
    exit /b 1
)

echo [SUCCESS] Contracts deployed successfully!

REM Extract contract addresses (simplified for Windows)
echo [INFO] Contract addresses saved to deployment.log

cd ..

REM Create startup script
echo [INFO] Creating startup script...
(
echo @echo off
echo echo 🎮 Starting CryptoQuest...
echo echo.
echo echo 🔗 Starting blockchain node...
echo cd contracts
echo start /b npx hardhat node
echo cd ..
echo timeout /t 3 /nobreak ^>nul
echo.
echo echo 🚀 Starting backend and frontend...
echo call npm run dev
) > start.bat

echo [SUCCESS] Startup script created: start.bat

echo.
echo [SUCCESS] 🎉 CryptoQuest setup completed successfully!
echo.
echo 📋 Next steps:
echo 1. Configure MetaMask:
echo    - Add network: Localhost 8545 (http://127.0.0.1:8545, Chain ID: 31337)
echo    - Import account with private key: 0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
echo    - Add CQT token (address in deployment.log)
echo.
echo 2. Start the application:
echo    start.bat
echo.
echo 3. Open your browser and go to: http://localhost:3000
echo.
echo 🎮 Happy gaming!
echo.
pause
