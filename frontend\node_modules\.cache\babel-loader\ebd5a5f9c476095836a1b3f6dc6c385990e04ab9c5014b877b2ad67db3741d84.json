{"ast": null, "code": "/**\n *  When sending values to or receiving values from a [[Contract]], the\n *  data is generally encoded using the [ABI standard](link-solc-abi).\n *\n *  The AbiCoder provides a utility to encode values to ABI data and\n *  decode values from ABI data.\n *\n *  Most of the time, developers should favour the [[Contract]] class,\n *  which further abstracts a lot of the finer details of ABI data.\n *\n *  @_section api/abi/abi-coder:ABI Encoding\n */\n// See: https://github.com/ethereum/wiki/wiki/Ethereum-Contract-ABI\nimport { assertArgumentCount, assertArgument } from \"../utils/index.js\";\nimport { Reader, Writer } from \"./coders/abstract-coder.js\";\nimport { AddressCoder } from \"./coders/address.js\";\nimport { ArrayCoder } from \"./coders/array.js\";\nimport { BooleanCoder } from \"./coders/boolean.js\";\nimport { BytesCoder } from \"./coders/bytes.js\";\nimport { FixedBytesCoder } from \"./coders/fixed-bytes.js\";\nimport { NullCoder } from \"./coders/null.js\";\nimport { NumberCoder } from \"./coders/number.js\";\nimport { StringCoder } from \"./coders/string.js\";\nimport { TupleCoder } from \"./coders/tuple.js\";\nimport { ParamType } from \"./fragments.js\";\nimport { getAddress } from \"../address/index.js\";\nimport { getBytes, hexlify, makeError } from \"../utils/index.js\";\n// https://docs.soliditylang.org/en/v0.8.17/control-structures.html\nconst PanicReasons = new Map();\nPanicReasons.set(0x00, \"GENERIC_PANIC\");\nPanicReasons.set(0x01, \"ASSERT_FALSE\");\nPanicReasons.set(0x11, \"OVERFLOW\");\nPanicReasons.set(0x12, \"DIVIDE_BY_ZERO\");\nPanicReasons.set(0x21, \"ENUM_RANGE_ERROR\");\nPanicReasons.set(0x22, \"BAD_STORAGE_DATA\");\nPanicReasons.set(0x31, \"STACK_UNDERFLOW\");\nPanicReasons.set(0x32, \"ARRAY_RANGE_ERROR\");\nPanicReasons.set(0x41, \"OUT_OF_MEMORY\");\nPanicReasons.set(0x51, \"UNINITIALIZED_FUNCTION_CALL\");\nconst paramTypeBytes = new RegExp(/^bytes([0-9]*)$/);\nconst paramTypeNumber = new RegExp(/^(u?int)([0-9]*)$/);\nlet defaultCoder = null;\nlet defaultMaxInflation = 1024;\nfunction getBuiltinCallException(action, tx, data, abiCoder) {\n  let message = \"missing revert data\";\n  let reason = null;\n  const invocation = null;\n  let revert = null;\n  if (data) {\n    message = \"execution reverted\";\n    const bytes = getBytes(data);\n    data = hexlify(data);\n    if (bytes.length === 0) {\n      message += \" (no data present; likely require(false) occurred\";\n      reason = \"require(false)\";\n    } else if (bytes.length % 32 !== 4) {\n      message += \" (could not decode reason; invalid data length)\";\n    } else if (hexlify(bytes.slice(0, 4)) === \"0x08c379a0\") {\n      // Error(string)\n      try {\n        reason = abiCoder.decode([\"string\"], bytes.slice(4))[0];\n        revert = {\n          signature: \"Error(string)\",\n          name: \"Error\",\n          args: [reason]\n        };\n        message += `: ${JSON.stringify(reason)}`;\n      } catch (error) {\n        message += \" (could not decode reason; invalid string data)\";\n      }\n    } else if (hexlify(bytes.slice(0, 4)) === \"0x4e487b71\") {\n      // Panic(uint256)\n      try {\n        const code = Number(abiCoder.decode([\"uint256\"], bytes.slice(4))[0]);\n        revert = {\n          signature: \"Panic(uint256)\",\n          name: \"Panic\",\n          args: [code]\n        };\n        reason = `Panic due to ${PanicReasons.get(code) || \"UNKNOWN\"}(${code})`;\n        message += `: ${reason}`;\n      } catch (error) {\n        message += \" (could not decode panic code)\";\n      }\n    } else {\n      message += \" (unknown custom error)\";\n    }\n  }\n  const transaction = {\n    to: tx.to ? getAddress(tx.to) : null,\n    data: tx.data || \"0x\"\n  };\n  if (tx.from) {\n    transaction.from = getAddress(tx.from);\n  }\n  return makeError(message, \"CALL_EXCEPTION\", {\n    action,\n    data,\n    reason,\n    transaction,\n    invocation,\n    revert\n  });\n}\n/**\n *  The **AbiCoder** is a low-level class responsible for encoding JavaScript\n *  values into binary data and decoding binary data into JavaScript values.\n */\nexport class AbiCoder {\n  #getCoder(param) {\n    if (param.isArray()) {\n      return new ArrayCoder(this.#getCoder(param.arrayChildren), param.arrayLength, param.name);\n    }\n    if (param.isTuple()) {\n      return new TupleCoder(param.components.map(c => this.#getCoder(c)), param.name);\n    }\n    switch (param.baseType) {\n      case \"address\":\n        return new AddressCoder(param.name);\n      case \"bool\":\n        return new BooleanCoder(param.name);\n      case \"string\":\n        return new StringCoder(param.name);\n      case \"bytes\":\n        return new BytesCoder(param.name);\n      case \"\":\n        return new NullCoder(param.name);\n    }\n    // u?int[0-9]*\n    let match = param.type.match(paramTypeNumber);\n    if (match) {\n      let size = parseInt(match[2] || \"256\");\n      assertArgument(size !== 0 && size <= 256 && size % 8 === 0, \"invalid \" + match[1] + \" bit length\", \"param\", param);\n      return new NumberCoder(size / 8, match[1] === \"int\", param.name);\n    }\n    // bytes[0-9]+\n    match = param.type.match(paramTypeBytes);\n    if (match) {\n      let size = parseInt(match[1]);\n      assertArgument(size !== 0 && size <= 32, \"invalid bytes length\", \"param\", param);\n      return new FixedBytesCoder(size, param.name);\n    }\n    assertArgument(false, \"invalid type\", \"type\", param.type);\n  }\n  /**\n   *  Get the default values for the given %%types%%.\n   *\n   *  For example, a ``uint`` is by default ``0`` and ``bool``\n   *  is by default ``false``.\n   */\n  getDefaultValue(types) {\n    const coders = types.map(type => this.#getCoder(ParamType.from(type)));\n    const coder = new TupleCoder(coders, \"_\");\n    return coder.defaultValue();\n  }\n  /**\n   *  Encode the %%values%% as the %%types%% into ABI data.\n   *\n   *  @returns DataHexstring\n   */\n  encode(types, values) {\n    assertArgumentCount(values.length, types.length, \"types/values length mismatch\");\n    const coders = types.map(type => this.#getCoder(ParamType.from(type)));\n    const coder = new TupleCoder(coders, \"_\");\n    const writer = new Writer();\n    coder.encode(writer, values);\n    return writer.data;\n  }\n  /**\n   *  Decode the ABI %%data%% as the %%types%% into values.\n   *\n   *  If %%loose%% decoding is enabled, then strict padding is\n   *  not enforced. Some older versions of Solidity incorrectly\n   *  padded event data emitted from ``external`` functions.\n   */\n  decode(types, data, loose) {\n    const coders = types.map(type => this.#getCoder(ParamType.from(type)));\n    const coder = new TupleCoder(coders, \"_\");\n    return coder.decode(new Reader(data, loose, defaultMaxInflation));\n  }\n  static _setDefaultMaxInflation(value) {\n    assertArgument(typeof value === \"number\" && Number.isInteger(value), \"invalid defaultMaxInflation factor\", \"value\", value);\n    defaultMaxInflation = value;\n  }\n  /**\n   *  Returns the shared singleton instance of a default [[AbiCoder]].\n   *\n   *  On the first call, the instance is created internally.\n   */\n  static defaultAbiCoder() {\n    if (defaultCoder == null) {\n      defaultCoder = new AbiCoder();\n    }\n    return defaultCoder;\n  }\n  /**\n   *  Returns an ethers-compatible [[CallExceptionError]] Error for the given\n   *  result %%data%% for the [[CallExceptionAction]] %%action%% against\n   *  the Transaction %%tx%%.\n   */\n  static getBuiltinCallException(action, tx, data) {\n    return getBuiltinCallException(action, tx, data, AbiCoder.defaultAbiCoder());\n  }\n}", "map": {"version": 3, "names": ["assertArgumentCount", "assertArgument", "Reader", "Writer", "AddressCoder", "ArrayCoder", "BooleanCoder", "BytesCoder", "FixedBytesCoder", "NullCoder", "NumberCoder", "StringCoder", "TupleCoder", "ParamType", "get<PERSON><PERSON><PERSON>", "getBytes", "hexlify", "makeError", "PanicReasons", "Map", "set", "paramTypeBytes", "RegExp", "paramTypeNumber", "defaultCoder", "defaultMaxInflation", "getBuiltinCallException", "action", "tx", "data", "abiCoder", "message", "reason", "invocation", "revert", "bytes", "length", "slice", "decode", "signature", "name", "args", "JSON", "stringify", "error", "code", "Number", "get", "transaction", "to", "from", "AbiCoder", "getCoder", "#getCoder", "param", "isArray", "array<PERSON><PERSON><PERSON>n", "array<PERSON>ength", "isTuple", "components", "map", "c", "baseType", "match", "type", "size", "parseInt", "getDefaultValue", "types", "coders", "coder", "defaultValue", "encode", "values", "writer", "loose", "_setDefaultMaxInflation", "value", "isInteger", "defaultAbiCoder"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\abi-coder.ts"], "sourcesContent": ["/**\n *  When sending values to or receiving values from a [[Contract]], the\n *  data is generally encoded using the [ABI standard](link-solc-abi).\n *\n *  The AbiCoder provides a utility to encode values to ABI data and\n *  decode values from ABI data.\n *\n *  Most of the time, developers should favour the [[Contract]] class,\n *  which further abstracts a lot of the finer details of ABI data.\n *\n *  @_section api/abi/abi-coder:ABI Encoding\n */\n\n// See: https://github.com/ethereum/wiki/wiki/Ethereum-Contract-ABI\n\nimport { assertArgumentCount, assertArgument } from \"../utils/index.js\";\n\nimport { Coder, Reader, Result, Writer } from \"./coders/abstract-coder.js\";\nimport { AddressCoder } from \"./coders/address.js\";\nimport { ArrayCoder } from \"./coders/array.js\";\nimport { BooleanCoder } from \"./coders/boolean.js\";\nimport { BytesCoder } from \"./coders/bytes.js\";\nimport { FixedBytesCoder } from \"./coders/fixed-bytes.js\";\nimport { NullCoder } from \"./coders/null.js\";\nimport { NumberCoder } from \"./coders/number.js\";\nimport { StringCoder } from \"./coders/string.js\";\nimport { TupleCoder } from \"./coders/tuple.js\";\nimport { ParamType } from \"./fragments.js\";\n\nimport { getAddress } from \"../address/index.js\";\nimport { getBytes, hexlify, makeError } from \"../utils/index.js\";\n\nimport type {\n    BytesLike,\n    CallExceptionAction, CallExceptionError, CallExceptionTransaction\n} from \"../utils/index.js\";\n\n// https://docs.soliditylang.org/en/v0.8.17/control-structures.html\nconst PanicReasons: Map<number, string> = new Map();\nPanicReasons.set(0x00, \"GENERIC_PANIC\");\nPanicReasons.set(0x01, \"ASSERT_FALSE\");\nPanicReasons.set(0x11, \"OVERFLOW\");\nPanicReasons.set(0x12, \"DIVIDE_BY_ZERO\");\nPanicReasons.set(0x21, \"ENUM_RANGE_ERROR\");\nPanicReasons.set(0x22, \"BAD_STORAGE_DATA\");\nPanicReasons.set(0x31, \"STACK_UNDERFLOW\");\nPanicReasons.set(0x32, \"ARRAY_RANGE_ERROR\");\nPanicReasons.set(0x41, \"OUT_OF_MEMORY\");\nPanicReasons.set(0x51, \"UNINITIALIZED_FUNCTION_CALL\");\n\nconst paramTypeBytes = new RegExp(/^bytes([0-9]*)$/);\nconst paramTypeNumber = new RegExp(/^(u?int)([0-9]*)$/);\n\n\nlet defaultCoder: null | AbiCoder = null;\nlet defaultMaxInflation = 1024;\n\nfunction getBuiltinCallException(action: CallExceptionAction, tx: { to?: null | string, from?: null | string, data?: string }, data: null | BytesLike, abiCoder: AbiCoder): CallExceptionError {\n    let message = \"missing revert data\";\n\n    let reason: null | string = null;\n    const invocation = null;\n    let revert: null | { signature: string, name: string, args: Array<any> } = null;\n\n    if (data) {\n        message = \"execution reverted\";\n\n        const bytes = getBytes(data);\n        data = hexlify(data);\n\n        if (bytes.length === 0) {\n            message += \" (no data present; likely require(false) occurred\";\n            reason = \"require(false)\";\n\n        } else if (bytes.length % 32 !== 4) {\n            message += \" (could not decode reason; invalid data length)\";\n\n        } else if (hexlify(bytes.slice(0, 4)) === \"0x08c379a0\") {\n            // Error(string)\n            try {\n                reason = abiCoder.decode([ \"string\" ], bytes.slice(4))[0]\n                revert = {\n                    signature: \"Error(string)\",\n                    name: \"Error\",\n                    args: [ reason ]\n                };\n                message += `: ${ JSON.stringify(reason) }`;\n\n            } catch (error) {\n                message += \" (could not decode reason; invalid string data)\";\n            }\n\n        } else if (hexlify(bytes.slice(0, 4)) === \"0x4e487b71\") {\n            // Panic(uint256)\n            try {\n                const code = Number(abiCoder.decode([ \"uint256\" ], bytes.slice(4))[0]);\n                revert = {\n                    signature: \"Panic(uint256)\",\n                    name: \"Panic\",\n                    args: [ code ]\n                };\n                reason = `Panic due to ${ PanicReasons.get(code) || \"UNKNOWN\" }(${ code })`;\n                message += `: ${ reason }`;\n            } catch (error) {\n                message += \" (could not decode panic code)\";\n            }\n        } else {\n            message += \" (unknown custom error)\";\n        }\n    }\n\n    const transaction: CallExceptionTransaction = {\n        to: (tx.to ? getAddress(tx.to): null),\n        data: (tx.data || \"0x\")\n    };\n    if (tx.from) { transaction.from = getAddress(tx.from); }\n\n    return makeError(message, \"CALL_EXCEPTION\", {\n        action, data, reason, transaction, invocation, revert\n    });\n}\n\n/**\n *  The **AbiCoder** is a low-level class responsible for encoding JavaScript\n *  values into binary data and decoding binary data into JavaScript values.\n */\nexport class AbiCoder {\n\n    #getCoder(param: ParamType): Coder {\n        if (param.isArray()) {\n            return new ArrayCoder(this.#getCoder(param.arrayChildren), param.arrayLength, param.name);\n        }\n\n        if (param.isTuple()) {\n            return new TupleCoder(param.components.map((c) => this.#getCoder(c)), param.name);\n        }\n\n        switch (param.baseType) {\n            case \"address\":\n                return new AddressCoder(param.name);\n            case \"bool\":\n                return new BooleanCoder(param.name);\n            case \"string\":\n                return new StringCoder(param.name);\n            case \"bytes\":\n                return new BytesCoder(param.name);\n            case \"\":\n                return new NullCoder(param.name);\n        }\n\n        // u?int[0-9]*\n        let match = param.type.match(paramTypeNumber);\n        if (match) {\n            let size = parseInt(match[2] || \"256\");\n            assertArgument(size !== 0 && size <= 256 && (size % 8) === 0,\n                \"invalid \" + match[1] + \" bit length\", \"param\", param);\n            return new NumberCoder(size / 8, (match[1] === \"int\"), param.name);\n        }\n\n        // bytes[0-9]+\n        match = param.type.match(paramTypeBytes);\n        if (match) {\n            let size = parseInt(match[1]);\n            assertArgument(size !== 0 && size <= 32, \"invalid bytes length\", \"param\", param);\n            return new FixedBytesCoder(size, param.name);\n        }\n\n        assertArgument(false, \"invalid type\", \"type\", param.type);\n    }\n\n    /**\n     *  Get the default values for the given %%types%%.\n     *\n     *  For example, a ``uint`` is by default ``0`` and ``bool``\n     *  is by default ``false``.\n     */\n    getDefaultValue(types: ReadonlyArray<string | ParamType>): Result {\n        const coders: Array<Coder> = types.map((type) => this.#getCoder(ParamType.from(type)));\n        const coder = new TupleCoder(coders, \"_\");\n        return coder.defaultValue();\n    }\n\n    /**\n     *  Encode the %%values%% as the %%types%% into ABI data.\n     *\n     *  @returns DataHexstring\n     */\n    encode(types: ReadonlyArray<string | ParamType>, values: ReadonlyArray<any>): string {\n        assertArgumentCount(values.length, types.length, \"types/values length mismatch\");\n\n        const coders = types.map((type) => this.#getCoder(ParamType.from(type)));\n        const coder = (new TupleCoder(coders, \"_\"));\n\n        const writer = new Writer();\n        coder.encode(writer, values);\n        return writer.data;\n    }\n\n    /**\n     *  Decode the ABI %%data%% as the %%types%% into values.\n     *\n     *  If %%loose%% decoding is enabled, then strict padding is\n     *  not enforced. Some older versions of Solidity incorrectly\n     *  padded event data emitted from ``external`` functions.\n     */\n    decode(types: ReadonlyArray<string | ParamType>, data: BytesLike, loose?: boolean): Result {\n        const coders: Array<Coder> = types.map((type) => this.#getCoder(ParamType.from(type)));\n        const coder = new TupleCoder(coders, \"_\");\n        return coder.decode(new Reader(data, loose, defaultMaxInflation));\n    }\n\n    static _setDefaultMaxInflation(value: number): void {\n        assertArgument(typeof(value) === \"number\" && Number.isInteger(value), \"invalid defaultMaxInflation factor\", \"value\", value);\n        defaultMaxInflation = value;\n    }\n\n    /**\n     *  Returns the shared singleton instance of a default [[AbiCoder]].\n     *\n     *  On the first call, the instance is created internally.\n     */\n    static defaultAbiCoder(): AbiCoder {\n        if (defaultCoder == null) {\n            defaultCoder = new AbiCoder();\n        }\n        return defaultCoder;\n    }\n\n    /**\n     *  Returns an ethers-compatible [[CallExceptionError]] Error for the given\n     *  result %%data%% for the [[CallExceptionAction]] %%action%% against\n     *  the Transaction %%tx%%.\n     */\n    static getBuiltinCallException(action: CallExceptionAction, tx: { to?: null | string, from?: null | string, data?: string }, data: null | BytesLike): CallExceptionError {\n        return getBuiltinCallException(action, tx, data, AbiCoder.defaultAbiCoder());\n    }\n}\n"], "mappings": "AAAA;;;;;;;;;;;;AAaA;AAEA,SAASA,mBAAmB,EAAEC,cAAc,QAAQ,mBAAmB;AAEvE,SAAgBC,MAAM,EAAUC,MAAM,QAAQ,4BAA4B;AAC1E,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,SAAS,QAAQ,gBAAgB;AAE1C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,QAAQ,mBAAmB;AAOhE;AACA,MAAMC,YAAY,GAAwB,IAAIC,GAAG,EAAE;AACnDD,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC;AACvCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,cAAc,CAAC;AACtCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC;AAClCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAAC;AACxCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC;AAC1CF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC;AAC1CF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,iBAAiB,CAAC;AACzCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC;AAC3CF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,eAAe,CAAC;AACvCF,YAAY,CAACE,GAAG,CAAC,IAAI,EAAE,6BAA6B,CAAC;AAErD,MAAMC,cAAc,GAAG,IAAIC,MAAM,CAAC,iBAAiB,CAAC;AACpD,MAAMC,eAAe,GAAG,IAAID,MAAM,CAAC,mBAAmB,CAAC;AAGvD,IAAIE,YAAY,GAAoB,IAAI;AACxC,IAAIC,mBAAmB,GAAG,IAAI;AAE9B,SAASC,uBAAuBA,CAACC,MAA2B,EAAEC,EAA+D,EAAEC,IAAsB,EAAEC,QAAkB;EACrK,IAAIC,OAAO,GAAG,qBAAqB;EAEnC,IAAIC,MAAM,GAAkB,IAAI;EAChC,MAAMC,UAAU,GAAG,IAAI;EACvB,IAAIC,MAAM,GAAiE,IAAI;EAE/E,IAAIL,IAAI,EAAE;IACNE,OAAO,GAAG,oBAAoB;IAE9B,MAAMI,KAAK,GAAGpB,QAAQ,CAACc,IAAI,CAAC;IAC5BA,IAAI,GAAGb,OAAO,CAACa,IAAI,CAAC;IAEpB,IAAIM,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACpBL,OAAO,IAAI,mDAAmD;MAC9DC,MAAM,GAAG,gBAAgB;KAE5B,MAAM,IAAIG,KAAK,CAACC,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE;MAChCL,OAAO,IAAI,iDAAiD;KAE/D,MAAM,IAAIf,OAAO,CAACmB,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;MACpD;MACA,IAAI;QACAL,MAAM,GAAGF,QAAQ,CAACQ,MAAM,CAAC,CAAE,QAAQ,CAAE,EAAEH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzDH,MAAM,GAAG;UACLK,SAAS,EAAE,eAAe;UAC1BC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,CAAET,MAAM;SACjB;QACDD,OAAO,IAAI,KAAMW,IAAI,CAACC,SAAS,CAACX,MAAM,CAAE,EAAE;OAE7C,CAAC,OAAOY,KAAK,EAAE;QACZb,OAAO,IAAI,iDAAiD;;KAGnE,MAAM,IAAIf,OAAO,CAACmB,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,YAAY,EAAE;MACpD;MACA,IAAI;QACA,MAAMQ,IAAI,GAAGC,MAAM,CAAChB,QAAQ,CAACQ,MAAM,CAAC,CAAE,SAAS,CAAE,EAAEH,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtEH,MAAM,GAAG;UACLK,SAAS,EAAE,gBAAgB;UAC3BC,IAAI,EAAE,OAAO;UACbC,IAAI,EAAE,CAAEI,IAAI;SACf;QACDb,MAAM,GAAG,gBAAiBd,YAAY,CAAC6B,GAAG,CAACF,IAAI,CAAC,IAAI,SAAU,IAAKA,IAAK,GAAG;QAC3Ed,OAAO,IAAI,KAAMC,MAAO,EAAE;OAC7B,CAAC,OAAOY,KAAK,EAAE;QACZb,OAAO,IAAI,gCAAgC;;KAElD,MAAM;MACHA,OAAO,IAAI,yBAAyB;;;EAI5C,MAAMiB,WAAW,GAA6B;IAC1CC,EAAE,EAAGrB,EAAE,CAACqB,EAAE,GAAGnC,UAAU,CAACc,EAAE,CAACqB,EAAE,CAAC,GAAE,IAAK;IACrCpB,IAAI,EAAGD,EAAE,CAACC,IAAI,IAAI;GACrB;EACD,IAAID,EAAE,CAACsB,IAAI,EAAE;IAAEF,WAAW,CAACE,IAAI,GAAGpC,UAAU,CAACc,EAAE,CAACsB,IAAI,CAAC;;EAErD,OAAOjC,SAAS,CAACc,OAAO,EAAE,gBAAgB,EAAE;IACxCJ,MAAM;IAAEE,IAAI;IAAEG,MAAM;IAAEgB,WAAW;IAAEf,UAAU;IAAEC;GAClD,CAAC;AACN;AAEA;;;;AAIA,OAAM,MAAOiB,QAAQ;EAEjB,CAAAC,QAASC,CAACC,KAAgB;IACtB,IAAIA,KAAK,CAACC,OAAO,EAAE,EAAE;MACjB,OAAO,IAAIlD,UAAU,CAAC,IAAI,CAAC,CAAA+C,QAAS,CAACE,KAAK,CAACE,aAAa,CAAC,EAAEF,KAAK,CAACG,WAAW,EAAEH,KAAK,CAACd,IAAI,CAAC;;IAG7F,IAAIc,KAAK,CAACI,OAAO,EAAE,EAAE;MACjB,OAAO,IAAI9C,UAAU,CAAC0C,KAAK,CAACK,UAAU,CAACC,GAAG,CAAEC,CAAC,IAAK,IAAI,CAAC,CAAAT,QAAS,CAACS,CAAC,CAAC,CAAC,EAAEP,KAAK,CAACd,IAAI,CAAC;;IAGrF,QAAQc,KAAK,CAACQ,QAAQ;MAClB,KAAK,SAAS;QACV,OAAO,IAAI1D,YAAY,CAACkD,KAAK,CAACd,IAAI,CAAC;MACvC,KAAK,MAAM;QACP,OAAO,IAAIlC,YAAY,CAACgD,KAAK,CAACd,IAAI,CAAC;MACvC,KAAK,QAAQ;QACT,OAAO,IAAI7B,WAAW,CAAC2C,KAAK,CAACd,IAAI,CAAC;MACtC,KAAK,OAAO;QACR,OAAO,IAAIjC,UAAU,CAAC+C,KAAK,CAACd,IAAI,CAAC;MACrC,KAAK,EAAE;QACH,OAAO,IAAI/B,SAAS,CAAC6C,KAAK,CAACd,IAAI,CAAC;;IAGxC;IACA,IAAIuB,KAAK,GAAGT,KAAK,CAACU,IAAI,CAACD,KAAK,CAACxC,eAAe,CAAC;IAC7C,IAAIwC,KAAK,EAAE;MACP,IAAIE,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;MACtC9D,cAAc,CAACgE,IAAI,KAAK,CAAC,IAAIA,IAAI,IAAI,GAAG,IAAKA,IAAI,GAAG,CAAC,KAAM,CAAC,EACxD,UAAU,GAAGF,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,EAAE,OAAO,EAAET,KAAK,CAAC;MAC1D,OAAO,IAAI5C,WAAW,CAACuD,IAAI,GAAG,CAAC,EAAGF,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,EAAGT,KAAK,CAACd,IAAI,CAAC;;IAGtE;IACAuB,KAAK,GAAGT,KAAK,CAACU,IAAI,CAACD,KAAK,CAAC1C,cAAc,CAAC;IACxC,IAAI0C,KAAK,EAAE;MACP,IAAIE,IAAI,GAAGC,QAAQ,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;MAC7B9D,cAAc,CAACgE,IAAI,KAAK,CAAC,IAAIA,IAAI,IAAI,EAAE,EAAE,sBAAsB,EAAE,OAAO,EAAEX,KAAK,CAAC;MAChF,OAAO,IAAI9C,eAAe,CAACyD,IAAI,EAAEX,KAAK,CAACd,IAAI,CAAC;;IAGhDvC,cAAc,CAAC,KAAK,EAAE,cAAc,EAAE,MAAM,EAAEqD,KAAK,CAACU,IAAI,CAAC;EAC7D;EAEA;;;;;;EAMAG,eAAeA,CAACC,KAAwC;IACpD,MAAMC,MAAM,GAAiBD,KAAK,CAACR,GAAG,CAAEI,IAAI,IAAK,IAAI,CAAC,CAAAZ,QAAS,CAACvC,SAAS,CAACqC,IAAI,CAACc,IAAI,CAAC,CAAC,CAAC;IACtF,MAAMM,KAAK,GAAG,IAAI1D,UAAU,CAACyD,MAAM,EAAE,GAAG,CAAC;IACzC,OAAOC,KAAK,CAACC,YAAY,EAAE;EAC/B;EAEA;;;;;EAKAC,MAAMA,CAACJ,KAAwC,EAAEK,MAA0B;IACvEzE,mBAAmB,CAACyE,MAAM,CAACrC,MAAM,EAAEgC,KAAK,CAAChC,MAAM,EAAE,8BAA8B,CAAC;IAEhF,MAAMiC,MAAM,GAAGD,KAAK,CAACR,GAAG,CAAEI,IAAI,IAAK,IAAI,CAAC,CAAAZ,QAAS,CAACvC,SAAS,CAACqC,IAAI,CAACc,IAAI,CAAC,CAAC,CAAC;IACxE,MAAMM,KAAK,GAAI,IAAI1D,UAAU,CAACyD,MAAM,EAAE,GAAG,CAAE;IAE3C,MAAMK,MAAM,GAAG,IAAIvE,MAAM,EAAE;IAC3BmE,KAAK,CAACE,MAAM,CAACE,MAAM,EAAED,MAAM,CAAC;IAC5B,OAAOC,MAAM,CAAC7C,IAAI;EACtB;EAEA;;;;;;;EAOAS,MAAMA,CAAC8B,KAAwC,EAAEvC,IAAe,EAAE8C,KAAe;IAC7E,MAAMN,MAAM,GAAiBD,KAAK,CAACR,GAAG,CAAEI,IAAI,IAAK,IAAI,CAAC,CAAAZ,QAAS,CAACvC,SAAS,CAACqC,IAAI,CAACc,IAAI,CAAC,CAAC,CAAC;IACtF,MAAMM,KAAK,GAAG,IAAI1D,UAAU,CAACyD,MAAM,EAAE,GAAG,CAAC;IACzC,OAAOC,KAAK,CAAChC,MAAM,CAAC,IAAIpC,MAAM,CAAC2B,IAAI,EAAE8C,KAAK,EAAElD,mBAAmB,CAAC,CAAC;EACrE;EAEA,OAAOmD,uBAAuBA,CAACC,KAAa;IACxC5E,cAAc,CAAC,OAAO4E,KAAM,KAAK,QAAQ,IAAI/B,MAAM,CAACgC,SAAS,CAACD,KAAK,CAAC,EAAE,oCAAoC,EAAE,OAAO,EAAEA,KAAK,CAAC;IAC3HpD,mBAAmB,GAAGoD,KAAK;EAC/B;EAEA;;;;;EAKA,OAAOE,eAAeA,CAAA;IAClB,IAAIvD,YAAY,IAAI,IAAI,EAAE;MACtBA,YAAY,GAAG,IAAI2B,QAAQ,EAAE;;IAEjC,OAAO3B,YAAY;EACvB;EAEA;;;;;EAKA,OAAOE,uBAAuBA,CAACC,MAA2B,EAAEC,EAA+D,EAAEC,IAAsB;IAC/I,OAAOH,uBAAuB,CAACC,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAEsB,QAAQ,CAAC4B,eAAe,EAAE,CAAC;EAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}