{"ast": null, "code": "/**\n *  @_ignore\n */\nimport { getAddress, getCreateAddress } from \"../address/index.js\";\nimport { Signature } from \"../crypto/index.js\";\nimport { accessListify } from \"../transaction/index.js\";\nimport { getBigInt, getNumber, hexlify, isHexString, zeroPadValue, assert, assertArgument } from \"../utils/index.js\";\nconst BN_0 = BigInt(0);\nexport function allowNull(format, nullValue) {\n  return function (value) {\n    if (value == null) {\n      return nullValue;\n    }\n    return format(value);\n  };\n}\nexport function arrayOf(format, allowNull) {\n  return array => {\n    if (allowNull && array == null) {\n      return null;\n    }\n    if (!Array.isArray(array)) {\n      throw new Error(\"not an array\");\n    }\n    return array.map(i => format(i));\n  };\n}\n// Requires an object which matches a fleet of other formatters\n// Any FormatFunc may return `undefined` to have the value omitted\n// from the result object. Calls preserve `this`.\nexport function object(format, altNames) {\n  return value => {\n    const result = {};\n    for (const key in format) {\n      let srcKey = key;\n      if (altNames && key in altNames && !(srcKey in value)) {\n        for (const altKey of altNames[key]) {\n          if (altKey in value) {\n            srcKey = altKey;\n            break;\n          }\n        }\n      }\n      try {\n        const nv = format[key](value[srcKey]);\n        if (nv !== undefined) {\n          result[key] = nv;\n        }\n      } catch (error) {\n        const message = error instanceof Error ? error.message : \"not-an-error\";\n        assert(false, `invalid value for value.${key} (${message})`, \"BAD_DATA\", {\n          value\n        });\n      }\n    }\n    return result;\n  };\n}\nexport function formatBoolean(value) {\n  switch (value) {\n    case true:\n    case \"true\":\n      return true;\n    case false:\n    case \"false\":\n      return false;\n  }\n  assertArgument(false, `invalid boolean; ${JSON.stringify(value)}`, \"value\", value);\n}\nexport function formatData(value) {\n  assertArgument(isHexString(value, true), \"invalid data\", \"value\", value);\n  return value;\n}\nexport function formatHash(value) {\n  assertArgument(isHexString(value, 32), \"invalid hash\", \"value\", value);\n  return value;\n}\nexport function formatUint256(value) {\n  if (!isHexString(value)) {\n    throw new Error(\"invalid uint256\");\n  }\n  return zeroPadValue(value, 32);\n}\nconst _formatLog = object({\n  address: getAddress,\n  blockHash: formatHash,\n  blockNumber: getNumber,\n  data: formatData,\n  index: getNumber,\n  removed: allowNull(formatBoolean, false),\n  topics: arrayOf(formatHash),\n  transactionHash: formatHash,\n  transactionIndex: getNumber\n}, {\n  index: [\"logIndex\"]\n});\nexport function formatLog(value) {\n  return _formatLog(value);\n}\nconst _formatBlock = object({\n  hash: allowNull(formatHash),\n  parentHash: formatHash,\n  parentBeaconBlockRoot: allowNull(formatHash, null),\n  number: getNumber,\n  timestamp: getNumber,\n  nonce: allowNull(formatData),\n  difficulty: getBigInt,\n  gasLimit: getBigInt,\n  gasUsed: getBigInt,\n  stateRoot: allowNull(formatHash, null),\n  receiptsRoot: allowNull(formatHash, null),\n  blobGasUsed: allowNull(getBigInt, null),\n  excessBlobGas: allowNull(getBigInt, null),\n  miner: allowNull(getAddress),\n  prevRandao: allowNull(formatHash, null),\n  extraData: formatData,\n  baseFeePerGas: allowNull(getBigInt)\n}, {\n  prevRandao: [\"mixHash\"]\n});\nexport function formatBlock(value) {\n  const result = _formatBlock(value);\n  result.transactions = value.transactions.map(tx => {\n    if (typeof tx === \"string\") {\n      return tx;\n    }\n    return formatTransactionResponse(tx);\n  });\n  return result;\n}\nconst _formatReceiptLog = object({\n  transactionIndex: getNumber,\n  blockNumber: getNumber,\n  transactionHash: formatHash,\n  address: getAddress,\n  topics: arrayOf(formatHash),\n  data: formatData,\n  index: getNumber,\n  blockHash: formatHash\n}, {\n  index: [\"logIndex\"]\n});\nexport function formatReceiptLog(value) {\n  return _formatReceiptLog(value);\n}\nconst _formatTransactionReceipt = object({\n  to: allowNull(getAddress, null),\n  from: allowNull(getAddress, null),\n  contractAddress: allowNull(getAddress, null),\n  // should be allowNull(hash), but broken-EIP-658 support is handled in receipt\n  index: getNumber,\n  root: allowNull(hexlify),\n  gasUsed: getBigInt,\n  blobGasUsed: allowNull(getBigInt, null),\n  logsBloom: allowNull(formatData),\n  blockHash: formatHash,\n  hash: formatHash,\n  logs: arrayOf(formatReceiptLog),\n  blockNumber: getNumber,\n  //confirmations: allowNull(getNumber, null),\n  cumulativeGasUsed: getBigInt,\n  effectiveGasPrice: allowNull(getBigInt),\n  blobGasPrice: allowNull(getBigInt, null),\n  status: allowNull(getNumber),\n  type: allowNull(getNumber, 0)\n}, {\n  effectiveGasPrice: [\"gasPrice\"],\n  hash: [\"transactionHash\"],\n  index: [\"transactionIndex\"]\n});\nexport function formatTransactionReceipt(value) {\n  return _formatTransactionReceipt(value);\n}\nexport function formatTransactionResponse(value) {\n  // Some clients (TestRPC) do strange things like return 0x0 for the\n  // 0 address; correct this to be a real address\n  if (value.to && getBigInt(value.to) === BN_0) {\n    value.to = \"0x0000000000000000000000000000000000000000\";\n  }\n  const result = object({\n    hash: formatHash,\n    // Some nodes do not return this, usually test nodes (like Ganache)\n    index: allowNull(getNumber, undefined),\n    type: value => {\n      if (value === \"0x\" || value == null) {\n        return 0;\n      }\n      return getNumber(value);\n    },\n    accessList: allowNull(accessListify, null),\n    blobVersionedHashes: allowNull(arrayOf(formatHash, true), null),\n    authorizationList: allowNull(arrayOf(v => {\n      let sig;\n      if (v.signature) {\n        sig = v.signature;\n      } else {\n        let yParity = v.yParity;\n        if (yParity === \"0x1b\") {\n          yParity = 0;\n        } else if (yParity === \"0x1c\") {\n          yParity = 1;\n        }\n        sig = Object.assign({}, v, {\n          yParity\n        });\n      }\n      return {\n        address: getAddress(v.address),\n        chainId: getBigInt(v.chainId),\n        nonce: getBigInt(v.nonce),\n        signature: Signature.from(sig)\n      };\n    }, false), null),\n    blockHash: allowNull(formatHash, null),\n    blockNumber: allowNull(getNumber, null),\n    transactionIndex: allowNull(getNumber, null),\n    from: getAddress,\n    // either (gasPrice) or (maxPriorityFeePerGas + maxFeePerGas) must be set\n    gasPrice: allowNull(getBigInt),\n    maxPriorityFeePerGas: allowNull(getBigInt),\n    maxFeePerGas: allowNull(getBigInt),\n    maxFeePerBlobGas: allowNull(getBigInt, null),\n    gasLimit: getBigInt,\n    to: allowNull(getAddress, null),\n    value: getBigInt,\n    nonce: getNumber,\n    data: formatData,\n    creates: allowNull(getAddress, null),\n    chainId: allowNull(getBigInt, null)\n  }, {\n    data: [\"input\"],\n    gasLimit: [\"gas\"],\n    index: [\"transactionIndex\"]\n  })(value);\n  // If to and creates are empty, populate the creates from the value\n  if (result.to == null && result.creates == null) {\n    result.creates = getCreateAddress(result);\n  }\n  // @TODO: Check fee data\n  // Add an access list to supported transaction types\n  if ((value.type === 1 || value.type === 2) && value.accessList == null) {\n    result.accessList = [];\n  }\n  // Compute the signature\n  if (value.signature) {\n    result.signature = Signature.from(value.signature);\n  } else {\n    result.signature = Signature.from(value);\n  }\n  // Some backends omit ChainId on legacy transactions, but we can compute it\n  if (result.chainId == null) {\n    const chainId = result.signature.legacyChainId;\n    if (chainId != null) {\n      result.chainId = chainId;\n    }\n  }\n  // @TODO: check chainID\n  /*\n  if (value.chainId != null) {\n      let chainId = value.chainId;\n       if (isHexString(chainId)) {\n          chainId = BigNumber.from(chainId).toNumber();\n      }\n       result.chainId = chainId;\n   } else {\n      let chainId = value.networkId;\n       // geth-etc returns chainId\n      if (chainId == null && result.v == null) {\n          chainId = value.chainId;\n      }\n       if (isHexString(chainId)) {\n          chainId = BigNumber.from(chainId).toNumber();\n      }\n       if (typeof(chainId) !== \"number\" && result.v != null) {\n          chainId = (result.v - 35) / 2;\n          if (chainId < 0) { chainId = 0; }\n          chainId = parseInt(chainId);\n      }\n       if (typeof(chainId) !== \"number\") { chainId = 0; }\n       result.chainId = chainId;\n  }\n  */\n  // 0x0000... should actually be null\n  if (result.blockHash && getBigInt(result.blockHash) === BN_0) {\n    result.blockHash = null;\n  }\n  return result;\n}", "map": {"version": 3, "names": ["get<PERSON><PERSON><PERSON>", "getCreateAddress", "Signature", "accessListify", "getBigInt", "getNumber", "hexlify", "isHexString", "zeroPadValue", "assert", "assertArgument", "BN_0", "BigInt", "allowNull", "format", "nullValue", "value", "arrayOf", "array", "Array", "isArray", "Error", "map", "i", "object", "altNames", "result", "key", "srcKey", "altKey", "nv", "undefined", "error", "message", "formatBoolean", "JSON", "stringify", "formatData", "formatHash", "formatUint256", "_formatLog", "address", "blockHash", "blockNumber", "data", "index", "removed", "topics", "transactionHash", "transactionIndex", "formatLog", "_formatBlock", "hash", "parentHash", "parentBeaconBlockRoot", "number", "timestamp", "nonce", "difficulty", "gasLimit", "gasUsed", "stateRoot", "receiptsRoot", "blobGasUsed", "excessBlobGas", "miner", "prevRandao", "extraData", "baseFeePerGas", "formatBlock", "transactions", "tx", "formatTransactionResponse", "_formatReceiptLog", "formatReceiptLog", "_formatTransactionReceipt", "to", "from", "contractAddress", "root", "logsBloom", "logs", "cumulativeGasUsed", "effectiveGasPrice", "blobGasPrice", "status", "type", "formatTransactionReceipt", "accessList", "blobVersionedHashes", "authorizationList", "v", "sig", "signature", "yParity", "Object", "assign", "chainId", "gasPrice", "maxPriorityFeePerGas", "maxFeePer<PERSON>as", "maxFeePerBlobGas", "creates", "legacyChainId"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\format.ts"], "sourcesContent": ["/**\n *  @_ignore\n */\nimport { getAddress, getCreateAddress } from \"../address/index.js\";\nimport { Signature } from \"../crypto/index.js\"\nimport { accessListify } from \"../transaction/index.js\";\nimport {\n    getBigInt, getNumber, hexlify, isHexString, zeroPadValue,\n    assert, assertArgument\n} from \"../utils/index.js\";\n\nimport type { SignatureLike } from \"../crypto/index.js\"\nimport type {\n    BlockParams, LogParams,\n    TransactionReceiptParams, TransactionResponseParams,\n} from \"./formatting.js\";\n\n\nconst BN_0 = BigInt(0);\n\nexport type FormatFunc = (value: any) => any;\n\nexport function allowNull(format: FormatFunc, nullValue?: any): FormatFunc {\n    return (function(value: any) {\n        if (value == null) { return nullValue; }\n        return format(value);\n    });\n}\n\nexport function arrayOf(format: FormatFunc, allowNull?: boolean): FormatFunc {\n    return ((array: any) => {\n        if (allowNull && array == null) { return null; }\n        if (!Array.isArray(array)) { throw new Error(\"not an array\"); }\n        return array.map((i) => format(i));\n    });\n}\n\n// Requires an object which matches a fleet of other formatters\n// Any FormatFunc may return `undefined` to have the value omitted\n// from the result object. Calls preserve `this`.\nexport function object(format: Record<string, FormatFunc>, altNames?: Record<string, Array<string>>): FormatFunc {\n    return ((value: any) => {\n        const result: any = { };\n        for (const key in format) {\n            let srcKey = key;\n            if (altNames && key in altNames && !(srcKey in value)) {\n                for (const altKey of altNames[key]) {\n                    if (altKey in value) {\n                        srcKey = altKey;\n                        break;\n                    }\n                }\n            }\n\n            try {\n                const nv = format[key](value[srcKey]);\n                if (nv !== undefined) { result[key] = nv; }\n            } catch (error) {\n                const message = (error instanceof Error) ? error.message: \"not-an-error\";\n                assert(false, `invalid value for value.${ key } (${ message })`, \"BAD_DATA\", { value })\n            }\n        }\n        return result;\n    });\n}\n\nexport function formatBoolean(value: any): boolean {\n    switch (value) {\n        case true: case \"true\":\n            return true;\n        case false: case \"false\":\n            return false;\n    }\n    assertArgument(false, `invalid boolean; ${ JSON.stringify(value) }`, \"value\", value);\n}\n\nexport function formatData(value: string): string {\n    assertArgument(isHexString(value, true), \"invalid data\", \"value\", value);\n    return value;\n}\n\nexport function formatHash(value: any): string {\n    assertArgument(isHexString(value, 32), \"invalid hash\", \"value\", value);\n    return value;\n}\n\nexport function formatUint256(value: any): string {\n    if (!isHexString(value)) {\n        throw new Error(\"invalid uint256\");\n    }\n    return zeroPadValue(value, 32);\n}\n\nconst _formatLog = object({\n    address: getAddress,\n    blockHash: formatHash,\n    blockNumber: getNumber,\n    data: formatData,\n    index: getNumber,\n    removed: allowNull(formatBoolean, false),\n    topics: arrayOf(formatHash),\n    transactionHash: formatHash,\n    transactionIndex: getNumber,\n}, {\n    index: [ \"logIndex\" ]\n});\n\nexport function formatLog(value: any): LogParams {\n    return _formatLog(value);\n}\n\nconst _formatBlock = object({\n    hash: allowNull(formatHash),\n    parentHash: formatHash,\n    parentBeaconBlockRoot: allowNull(formatHash, null),\n\n    number: getNumber,\n\n    timestamp: getNumber,\n    nonce: allowNull(formatData),\n    difficulty: getBigInt,\n\n    gasLimit: getBigInt,\n    gasUsed: getBigInt,\n\n    stateRoot: allowNull(formatHash, null),\n    receiptsRoot: allowNull(formatHash, null),\n\n    blobGasUsed: allowNull(getBigInt, null),\n    excessBlobGas: allowNull(getBigInt, null),\n\n    miner: allowNull(getAddress),\n    prevRandao: allowNull(formatHash, null),\n    extraData: formatData,\n\n    baseFeePerGas: allowNull(getBigInt)\n}, {\n    prevRandao: [ \"mixHash\" ]\n});\n\nexport function formatBlock(value: any): BlockParams {\n    const result = _formatBlock(value);\n    result.transactions = value.transactions.map((tx: string | TransactionResponseParams) => {\n        if (typeof(tx) === \"string\") { return tx; }\n        return formatTransactionResponse(tx);\n    });\n    return result;\n}\n\nconst _formatReceiptLog = object({\n    transactionIndex: getNumber,\n    blockNumber: getNumber,\n    transactionHash: formatHash,\n    address: getAddress,\n    topics: arrayOf(formatHash),\n    data: formatData,\n    index: getNumber,\n    blockHash: formatHash,\n}, {\n    index: [ \"logIndex\" ]\n});\n\nexport function formatReceiptLog(value: any): LogParams {\n    return _formatReceiptLog(value);\n}\n\nconst _formatTransactionReceipt = object({\n    to: allowNull(getAddress, null),\n    from: allowNull(getAddress, null),\n    contractAddress: allowNull(getAddress, null),\n    // should be allowNull(hash), but broken-EIP-658 support is handled in receipt\n    index: getNumber,\n    root: allowNull(hexlify),\n    gasUsed: getBigInt,\n    blobGasUsed: allowNull(getBigInt, null),\n    logsBloom: allowNull(formatData),\n    blockHash: formatHash,\n    hash: formatHash,\n    logs: arrayOf(formatReceiptLog),\n    blockNumber: getNumber,\n    //confirmations: allowNull(getNumber, null),\n    cumulativeGasUsed: getBigInt,\n    effectiveGasPrice: allowNull(getBigInt),\n    blobGasPrice: allowNull(getBigInt, null),\n    status: allowNull(getNumber),\n    type: allowNull(getNumber, 0)\n}, {\n    effectiveGasPrice: [ \"gasPrice\" ],\n    hash: [ \"transactionHash\" ],\n    index: [ \"transactionIndex\" ],\n});\n\nexport function formatTransactionReceipt(value: any): TransactionReceiptParams {\n    return _formatTransactionReceipt(value);\n}\n\nexport function formatTransactionResponse(value: any): TransactionResponseParams {\n\n    // Some clients (TestRPC) do strange things like return 0x0 for the\n    // 0 address; correct this to be a real address\n    if (value.to && getBigInt(value.to) === BN_0) {\n        value.to = \"0x0000000000000000000000000000000000000000\";\n    }\n\n    const result = object({\n        hash: formatHash,\n\n        // Some nodes do not return this, usually test nodes (like Ganache)\n        index: allowNull(getNumber, undefined),\n\n        type: (value: any) => {\n            if (value === \"0x\" || value == null) { return 0; }\n            return getNumber(value);\n        },\n        accessList: allowNull(accessListify, null),\n        blobVersionedHashes: allowNull(arrayOf(formatHash, true), null),\n\n        authorizationList: allowNull(arrayOf((v: any) => {\n            let sig: SignatureLike;\n            if (v.signature) {\n                sig = v.signature;\n\n            } else {\n                let yParity = v.yParity;\n                if (yParity === \"0x1b\") {\n                    yParity = 0;\n                } else if (yParity === \"0x1c\") {\n                    yParity = 1;\n                }\n                sig = Object.assign({ }, v, { yParity });\n            }\n\n            return {\n                address: getAddress(v.address),\n                chainId: getBigInt(v.chainId),\n                nonce: getBigInt(v.nonce),\n                signature: Signature.from(sig)\n            };\n        }, false), null),\n\n        blockHash: allowNull(formatHash, null),\n        blockNumber: allowNull(getNumber, null),\n        transactionIndex: allowNull(getNumber, null),\n\n        from: getAddress,\n\n        // either (gasPrice) or (maxPriorityFeePerGas + maxFeePerGas) must be set\n        gasPrice: allowNull(getBigInt),\n        maxPriorityFeePerGas: allowNull(getBigInt),\n        maxFeePerGas: allowNull(getBigInt),\n        maxFeePerBlobGas: allowNull(getBigInt, null),\n\n        gasLimit: getBigInt,\n        to: allowNull(getAddress, null),\n        value: getBigInt,\n        nonce: getNumber,\n        data: formatData,\n\n        creates: allowNull(getAddress, null),\n\n        chainId: allowNull(getBigInt, null)\n    }, {\n        data: [ \"input\" ],\n        gasLimit: [ \"gas\" ],\n        index: [ \"transactionIndex\" ]\n    })(value);\n\n    // If to and creates are empty, populate the creates from the value\n    if (result.to == null && result.creates == null) {\n        result.creates = getCreateAddress(result);\n    }\n\n    // @TODO: Check fee data\n\n    // Add an access list to supported transaction types\n    if ((value.type === 1 || value.type === 2) && value.accessList == null) {\n        result.accessList = [ ];\n    }\n\n    // Compute the signature\n    if (value.signature) {\n        result.signature = Signature.from(value.signature);\n    } else {\n        result.signature = Signature.from(value);\n    }\n\n    // Some backends omit ChainId on legacy transactions, but we can compute it\n    if (result.chainId == null) {\n        const chainId = result.signature.legacyChainId;\n        if (chainId != null) { result.chainId = chainId; }\n    }\n\n\n    // @TODO: check chainID\n    /*\n    if (value.chainId != null) {\n        let chainId = value.chainId;\n\n        if (isHexString(chainId)) {\n            chainId = BigNumber.from(chainId).toNumber();\n        }\n\n        result.chainId = chainId;\n\n    } else {\n        let chainId = value.networkId;\n\n        // geth-etc returns chainId\n        if (chainId == null && result.v == null) {\n            chainId = value.chainId;\n        }\n\n        if (isHexString(chainId)) {\n            chainId = BigNumber.from(chainId).toNumber();\n        }\n\n        if (typeof(chainId) !== \"number\" && result.v != null) {\n            chainId = (result.v - 35) / 2;\n            if (chainId < 0) { chainId = 0; }\n            chainId = parseInt(chainId);\n        }\n\n        if (typeof(chainId) !== \"number\") { chainId = 0; }\n\n        result.chainId = chainId;\n    }\n    */\n\n    // 0x0000... should actually be null\n    if (result.blockHash && getBigInt(result.blockHash) === BN_0) {\n        result.blockHash = null;\n    }\n\n    return result;\n}\n"], "mappings": "AAAA;;;AAGA,SAASA,UAAU,EAAEC,gBAAgB,QAAQ,qBAAqB;AAClE,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SACIC,SAAS,EAAEC,SAAS,EAAEC,OAAO,EAAEC,WAAW,EAAEC,YAAY,EACxDC,MAAM,EAAEC,cAAc,QACnB,mBAAmB;AAS1B,MAAMC,IAAI,GAAGC,MAAM,CAAC,CAAC,CAAC;AAItB,OAAM,SAAUC,SAASA,CAACC,MAAkB,EAAEC,SAAe;EACzD,OAAQ,UAASC,KAAU;IACvB,IAAIA,KAAK,IAAI,IAAI,EAAE;MAAE,OAAOD,SAAS;;IACrC,OAAOD,MAAM,CAACE,KAAK,CAAC;EACxB,CAAC;AACL;AAEA,OAAM,SAAUC,OAAOA,CAACH,MAAkB,EAAED,SAAmB;EAC3D,OAASK,KAAU,IAAI;IACnB,IAAIL,SAAS,IAAIK,KAAK,IAAI,IAAI,EAAE;MAAE,OAAO,IAAI;;IAC7C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MAAE,MAAM,IAAIG,KAAK,CAAC,cAAc,CAAC;;IAC5D,OAAOH,KAAK,CAACI,GAAG,CAAEC,CAAC,IAAKT,MAAM,CAACS,CAAC,CAAC,CAAC;EACtC,CAAC;AACL;AAEA;AACA;AACA;AACA,OAAM,SAAUC,MAAMA,CAACV,MAAkC,EAAEW,QAAwC;EAC/F,OAAST,KAAU,IAAI;IACnB,MAAMU,MAAM,GAAQ,EAAG;IACvB,KAAK,MAAMC,GAAG,IAAIb,MAAM,EAAE;MACtB,IAAIc,MAAM,GAAGD,GAAG;MAChB,IAAIF,QAAQ,IAAIE,GAAG,IAAIF,QAAQ,IAAI,EAAEG,MAAM,IAAIZ,KAAK,CAAC,EAAE;QACnD,KAAK,MAAMa,MAAM,IAAIJ,QAAQ,CAACE,GAAG,CAAC,EAAE;UAChC,IAAIE,MAAM,IAAIb,KAAK,EAAE;YACjBY,MAAM,GAAGC,MAAM;YACf;;;;MAKZ,IAAI;QACA,MAAMC,EAAE,GAAGhB,MAAM,CAACa,GAAG,CAAC,CAACX,KAAK,CAACY,MAAM,CAAC,CAAC;QACrC,IAAIE,EAAE,KAAKC,SAAS,EAAE;UAAEL,MAAM,CAACC,GAAG,CAAC,GAAGG,EAAE;;OAC3C,CAAC,OAAOE,KAAK,EAAE;QACZ,MAAMC,OAAO,GAAID,KAAK,YAAYX,KAAK,GAAIW,KAAK,CAACC,OAAO,GAAE,cAAc;QACxExB,MAAM,CAAC,KAAK,EAAE,2BAA4BkB,GAAI,KAAMM,OAAQ,GAAG,EAAE,UAAU,EAAE;UAAEjB;QAAK,CAAE,CAAC;;;IAG/F,OAAOU,MAAM;EACjB,CAAC;AACL;AAEA,OAAM,SAAUQ,aAAaA,CAAClB,KAAU;EACpC,QAAQA,KAAK;IACT,KAAK,IAAI;IAAE,KAAK,MAAM;MAClB,OAAO,IAAI;IACf,KAAK,KAAK;IAAE,KAAK,OAAO;MACpB,OAAO,KAAK;;EAEpBN,cAAc,CAAC,KAAK,EAAE,oBAAqByB,IAAI,CAACC,SAAS,CAACpB,KAAK,CAAE,EAAE,EAAE,OAAO,EAAEA,KAAK,CAAC;AACxF;AAEA,OAAM,SAAUqB,UAAUA,CAACrB,KAAa;EACpCN,cAAc,CAACH,WAAW,CAACS,KAAK,EAAE,IAAI,CAAC,EAAE,cAAc,EAAE,OAAO,EAAEA,KAAK,CAAC;EACxE,OAAOA,KAAK;AAChB;AAEA,OAAM,SAAUsB,UAAUA,CAACtB,KAAU;EACjCN,cAAc,CAACH,WAAW,CAACS,KAAK,EAAE,EAAE,CAAC,EAAE,cAAc,EAAE,OAAO,EAAEA,KAAK,CAAC;EACtE,OAAOA,KAAK;AAChB;AAEA,OAAM,SAAUuB,aAAaA,CAACvB,KAAU;EACpC,IAAI,CAACT,WAAW,CAACS,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIK,KAAK,CAAC,iBAAiB,CAAC;;EAEtC,OAAOb,YAAY,CAACQ,KAAK,EAAE,EAAE,CAAC;AAClC;AAEA,MAAMwB,UAAU,GAAGhB,MAAM,CAAC;EACtBiB,OAAO,EAAEzC,UAAU;EACnB0C,SAAS,EAAEJ,UAAU;EACrBK,WAAW,EAAEtC,SAAS;EACtBuC,IAAI,EAAEP,UAAU;EAChBQ,KAAK,EAAExC,SAAS;EAChByC,OAAO,EAAEjC,SAAS,CAACqB,aAAa,EAAE,KAAK,CAAC;EACxCa,MAAM,EAAE9B,OAAO,CAACqB,UAAU,CAAC;EAC3BU,eAAe,EAAEV,UAAU;EAC3BW,gBAAgB,EAAE5C;CACrB,EAAE;EACCwC,KAAK,EAAE,CAAE,UAAU;CACtB,CAAC;AAEF,OAAM,SAAUK,SAASA,CAAClC,KAAU;EAChC,OAAOwB,UAAU,CAACxB,KAAK,CAAC;AAC5B;AAEA,MAAMmC,YAAY,GAAG3B,MAAM,CAAC;EACxB4B,IAAI,EAAEvC,SAAS,CAACyB,UAAU,CAAC;EAC3Be,UAAU,EAAEf,UAAU;EACtBgB,qBAAqB,EAAEzC,SAAS,CAACyB,UAAU,EAAE,IAAI,CAAC;EAElDiB,MAAM,EAAElD,SAAS;EAEjBmD,SAAS,EAAEnD,SAAS;EACpBoD,KAAK,EAAE5C,SAAS,CAACwB,UAAU,CAAC;EAC5BqB,UAAU,EAAEtD,SAAS;EAErBuD,QAAQ,EAAEvD,SAAS;EACnBwD,OAAO,EAAExD,SAAS;EAElByD,SAAS,EAAEhD,SAAS,CAACyB,UAAU,EAAE,IAAI,CAAC;EACtCwB,YAAY,EAAEjD,SAAS,CAACyB,UAAU,EAAE,IAAI,CAAC;EAEzCyB,WAAW,EAAElD,SAAS,CAACT,SAAS,EAAE,IAAI,CAAC;EACvC4D,aAAa,EAAEnD,SAAS,CAACT,SAAS,EAAE,IAAI,CAAC;EAEzC6D,KAAK,EAAEpD,SAAS,CAACb,UAAU,CAAC;EAC5BkE,UAAU,EAAErD,SAAS,CAACyB,UAAU,EAAE,IAAI,CAAC;EACvC6B,SAAS,EAAE9B,UAAU;EAErB+B,aAAa,EAAEvD,SAAS,CAACT,SAAS;CACrC,EAAE;EACC8D,UAAU,EAAE,CAAE,SAAS;CAC1B,CAAC;AAEF,OAAM,SAAUG,WAAWA,CAACrD,KAAU;EAClC,MAAMU,MAAM,GAAGyB,YAAY,CAACnC,KAAK,CAAC;EAClCU,MAAM,CAAC4C,YAAY,GAAGtD,KAAK,CAACsD,YAAY,CAAChD,GAAG,CAAEiD,EAAsC,IAAI;IACpF,IAAI,OAAOA,EAAG,KAAK,QAAQ,EAAE;MAAE,OAAOA,EAAE;;IACxC,OAAOC,yBAAyB,CAACD,EAAE,CAAC;EACxC,CAAC,CAAC;EACF,OAAO7C,MAAM;AACjB;AAEA,MAAM+C,iBAAiB,GAAGjD,MAAM,CAAC;EAC7ByB,gBAAgB,EAAE5C,SAAS;EAC3BsC,WAAW,EAAEtC,SAAS;EACtB2C,eAAe,EAAEV,UAAU;EAC3BG,OAAO,EAAEzC,UAAU;EACnB+C,MAAM,EAAE9B,OAAO,CAACqB,UAAU,CAAC;EAC3BM,IAAI,EAAEP,UAAU;EAChBQ,KAAK,EAAExC,SAAS;EAChBqC,SAAS,EAAEJ;CACd,EAAE;EACCO,KAAK,EAAE,CAAE,UAAU;CACtB,CAAC;AAEF,OAAM,SAAU6B,gBAAgBA,CAAC1D,KAAU;EACvC,OAAOyD,iBAAiB,CAACzD,KAAK,CAAC;AACnC;AAEA,MAAM2D,yBAAyB,GAAGnD,MAAM,CAAC;EACrCoD,EAAE,EAAE/D,SAAS,CAACb,UAAU,EAAE,IAAI,CAAC;EAC/B6E,IAAI,EAAEhE,SAAS,CAACb,UAAU,EAAE,IAAI,CAAC;EACjC8E,eAAe,EAAEjE,SAAS,CAACb,UAAU,EAAE,IAAI,CAAC;EAC5C;EACA6C,KAAK,EAAExC,SAAS;EAChB0E,IAAI,EAAElE,SAAS,CAACP,OAAO,CAAC;EACxBsD,OAAO,EAAExD,SAAS;EAClB2D,WAAW,EAAElD,SAAS,CAACT,SAAS,EAAE,IAAI,CAAC;EACvC4E,SAAS,EAAEnE,SAAS,CAACwB,UAAU,CAAC;EAChCK,SAAS,EAAEJ,UAAU;EACrBc,IAAI,EAAEd,UAAU;EAChB2C,IAAI,EAAEhE,OAAO,CAACyD,gBAAgB,CAAC;EAC/B/B,WAAW,EAAEtC,SAAS;EACtB;EACA6E,iBAAiB,EAAE9E,SAAS;EAC5B+E,iBAAiB,EAAEtE,SAAS,CAACT,SAAS,CAAC;EACvCgF,YAAY,EAAEvE,SAAS,CAACT,SAAS,EAAE,IAAI,CAAC;EACxCiF,MAAM,EAAExE,SAAS,CAACR,SAAS,CAAC;EAC5BiF,IAAI,EAAEzE,SAAS,CAACR,SAAS,EAAE,CAAC;CAC/B,EAAE;EACC8E,iBAAiB,EAAE,CAAE,UAAU,CAAE;EACjC/B,IAAI,EAAE,CAAE,iBAAiB,CAAE;EAC3BP,KAAK,EAAE,CAAE,kBAAkB;CAC9B,CAAC;AAEF,OAAM,SAAU0C,wBAAwBA,CAACvE,KAAU;EAC/C,OAAO2D,yBAAyB,CAAC3D,KAAK,CAAC;AAC3C;AAEA,OAAM,SAAUwD,yBAAyBA,CAACxD,KAAU;EAEhD;EACA;EACA,IAAIA,KAAK,CAAC4D,EAAE,IAAIxE,SAAS,CAACY,KAAK,CAAC4D,EAAE,CAAC,KAAKjE,IAAI,EAAE;IAC1CK,KAAK,CAAC4D,EAAE,GAAG,4CAA4C;;EAG3D,MAAMlD,MAAM,GAAGF,MAAM,CAAC;IAClB4B,IAAI,EAAEd,UAAU;IAEhB;IACAO,KAAK,EAAEhC,SAAS,CAACR,SAAS,EAAE0B,SAAS,CAAC;IAEtCuD,IAAI,EAAGtE,KAAU,IAAI;MACjB,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,IAAI,IAAI,EAAE;QAAE,OAAO,CAAC;;MAC/C,OAAOX,SAAS,CAACW,KAAK,CAAC;IAC3B,CAAC;IACDwE,UAAU,EAAE3E,SAAS,CAACV,aAAa,EAAE,IAAI,CAAC;IAC1CsF,mBAAmB,EAAE5E,SAAS,CAACI,OAAO,CAACqB,UAAU,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;IAE/DoD,iBAAiB,EAAE7E,SAAS,CAACI,OAAO,CAAE0E,CAAM,IAAI;MAC5C,IAAIC,GAAkB;MACtB,IAAID,CAAC,CAACE,SAAS,EAAE;QACbD,GAAG,GAAGD,CAAC,CAACE,SAAS;OAEpB,MAAM;QACH,IAAIC,OAAO,GAAGH,CAAC,CAACG,OAAO;QACvB,IAAIA,OAAO,KAAK,MAAM,EAAE;UACpBA,OAAO,GAAG,CAAC;SACd,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC3BA,OAAO,GAAG,CAAC;;QAEfF,GAAG,GAAGG,MAAM,CAACC,MAAM,CAAC,EAAG,EAAEL,CAAC,EAAE;UAAEG;QAAO,CAAE,CAAC;;MAG5C,OAAO;QACHrD,OAAO,EAAEzC,UAAU,CAAC2F,CAAC,CAAClD,OAAO,CAAC;QAC9BwD,OAAO,EAAE7F,SAAS,CAACuF,CAAC,CAACM,OAAO,CAAC;QAC7BxC,KAAK,EAAErD,SAAS,CAACuF,CAAC,CAAClC,KAAK,CAAC;QACzBoC,SAAS,EAAE3F,SAAS,CAAC2E,IAAI,CAACe,GAAG;OAChC;IACL,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;IAEhBlD,SAAS,EAAE7B,SAAS,CAACyB,UAAU,EAAE,IAAI,CAAC;IACtCK,WAAW,EAAE9B,SAAS,CAACR,SAAS,EAAE,IAAI,CAAC;IACvC4C,gBAAgB,EAAEpC,SAAS,CAACR,SAAS,EAAE,IAAI,CAAC;IAE5CwE,IAAI,EAAE7E,UAAU;IAEhB;IACAkG,QAAQ,EAAErF,SAAS,CAACT,SAAS,CAAC;IAC9B+F,oBAAoB,EAAEtF,SAAS,CAACT,SAAS,CAAC;IAC1CgG,YAAY,EAAEvF,SAAS,CAACT,SAAS,CAAC;IAClCiG,gBAAgB,EAAExF,SAAS,CAACT,SAAS,EAAE,IAAI,CAAC;IAE5CuD,QAAQ,EAAEvD,SAAS;IACnBwE,EAAE,EAAE/D,SAAS,CAACb,UAAU,EAAE,IAAI,CAAC;IAC/BgB,KAAK,EAAEZ,SAAS;IAChBqD,KAAK,EAAEpD,SAAS;IAChBuC,IAAI,EAAEP,UAAU;IAEhBiE,OAAO,EAAEzF,SAAS,CAACb,UAAU,EAAE,IAAI,CAAC;IAEpCiG,OAAO,EAAEpF,SAAS,CAACT,SAAS,EAAE,IAAI;GACrC,EAAE;IACCwC,IAAI,EAAE,CAAE,OAAO,CAAE;IACjBe,QAAQ,EAAE,CAAE,KAAK,CAAE;IACnBd,KAAK,EAAE,CAAE,kBAAkB;GAC9B,CAAC,CAAC7B,KAAK,CAAC;EAET;EACA,IAAIU,MAAM,CAACkD,EAAE,IAAI,IAAI,IAAIlD,MAAM,CAAC4E,OAAO,IAAI,IAAI,EAAE;IAC7C5E,MAAM,CAAC4E,OAAO,GAAGrG,gBAAgB,CAACyB,MAAM,CAAC;;EAG7C;EAEA;EACA,IAAI,CAACV,KAAK,CAACsE,IAAI,KAAK,CAAC,IAAItE,KAAK,CAACsE,IAAI,KAAK,CAAC,KAAKtE,KAAK,CAACwE,UAAU,IAAI,IAAI,EAAE;IACpE9D,MAAM,CAAC8D,UAAU,GAAG,EAAG;;EAG3B;EACA,IAAIxE,KAAK,CAAC6E,SAAS,EAAE;IACjBnE,MAAM,CAACmE,SAAS,GAAG3F,SAAS,CAAC2E,IAAI,CAAC7D,KAAK,CAAC6E,SAAS,CAAC;GACrD,MAAM;IACHnE,MAAM,CAACmE,SAAS,GAAG3F,SAAS,CAAC2E,IAAI,CAAC7D,KAAK,CAAC;;EAG5C;EACA,IAAIU,MAAM,CAACuE,OAAO,IAAI,IAAI,EAAE;IACxB,MAAMA,OAAO,GAAGvE,MAAM,CAACmE,SAAS,CAACU,aAAa;IAC9C,IAAIN,OAAO,IAAI,IAAI,EAAE;MAAEvE,MAAM,CAACuE,OAAO,GAAGA,OAAO;;;EAInD;EACA;;;;;;;;;;;;;;;;;;;;;;;;;EAkCA;EACA,IAAIvE,MAAM,CAACgB,SAAS,IAAItC,SAAS,CAACsB,MAAM,CAACgB,SAAS,CAAC,KAAK/B,IAAI,EAAE;IAC1De,MAAM,CAACgB,SAAS,GAAG,IAAI;;EAG3B,OAAOhB,MAAM;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}