{"ast": null, "code": "import { defineProperties } from \"../../utils/properties.js\";\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\nimport { pack, unpack } from \"./array.js\";\n/**\n *  @_ignore\n */\nexport class TupleCoder extends Coder {\n  coders;\n  constructor(coders, localName) {\n    let dynamic = false;\n    const types = [];\n    coders.forEach(coder => {\n      if (coder.dynamic) {\n        dynamic = true;\n      }\n      types.push(coder.type);\n    });\n    const type = \"tuple(\" + types.join(\",\") + \")\";\n    super(\"tuple\", type, localName, dynamic);\n    defineProperties(this, {\n      coders: Object.freeze(coders.slice())\n    });\n  }\n  defaultValue() {\n    const values = [];\n    this.coders.forEach(coder => {\n      values.push(coder.defaultValue());\n    });\n    // We only output named properties for uniquely named coders\n    const uniqueNames = this.coders.reduce((accum, coder) => {\n      const name = coder.localName;\n      if (name) {\n        if (!accum[name]) {\n          accum[name] = 0;\n        }\n        accum[name]++;\n      }\n      return accum;\n    }, {});\n    // Add named values\n    this.coders.forEach((coder, index) => {\n      let name = coder.localName;\n      if (!name || uniqueNames[name] !== 1) {\n        return;\n      }\n      if (name === \"length\") {\n        name = \"_length\";\n      }\n      if (values[name] != null) {\n        return;\n      }\n      values[name] = values[index];\n    });\n    return Object.freeze(values);\n  }\n  encode(writer, _value) {\n    const value = Typed.dereference(_value, \"tuple\");\n    return pack(writer, this.coders, value);\n  }\n  decode(reader) {\n    return unpack(reader, this.coders);\n  }\n}", "map": {"version": 3, "names": ["defineProperties", "Typed", "Coder", "pack", "unpack", "TupleCoder", "coders", "constructor", "localName", "dynamic", "types", "for<PERSON>ach", "coder", "push", "type", "join", "Object", "freeze", "slice", "defaultValue", "values", "uniqueNames", "reduce", "accum", "name", "index", "encode", "writer", "_value", "value", "dereference", "decode", "reader"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\abi\\coders\\tuple.ts"], "sourcesContent": ["import { defineProperties } from \"../../utils/properties.js\";\n\nimport { Typed } from \"../typed.js\";\nimport { Coder } from \"./abstract-coder.js\";\n\nimport { pack, unpack } from \"./array.js\";\n\nimport type { <PERSON>, Writer } from \"./abstract-coder.js\";\n\n/**\n *  @_ignore\n */\nexport class TupleCoder extends Coder {\n    readonly coders!: ReadonlyArray<Coder>;\n\n    constructor(coders: Array<Coder>, localName: string) {\n        let dynamic = false;\n        const types: Array<string> = [];\n        coders.forEach((coder) => {\n            if (coder.dynamic) { dynamic = true; }\n            types.push(coder.type);\n        });\n        const type = (\"tuple(\" + types.join(\",\") + \")\");\n\n        super(\"tuple\", type, localName, dynamic);\n        defineProperties<TupleCoder>(this, { coders: Object.freeze(coders.slice()) });\n    }\n\n    defaultValue(): any {\n        const values: any = [ ];\n        this.coders.forEach((coder) => {\n            values.push(coder.defaultValue());\n        });\n\n        // We only output named properties for uniquely named coders\n        const uniqueNames = this.coders.reduce((accum, coder) => {\n            const name = coder.localName;\n            if (name) {\n                if (!accum[name]) { accum[name] = 0; }\n                accum[name]++;\n            }\n            return accum;\n        }, <{ [ name: string ]: number }>{ });\n\n        // Add named values\n        this.coders.forEach((coder: Coder, index: number) => {\n            let name = coder.localName;\n            if (!name || uniqueNames[name] !== 1) { return; }\n\n            if (name === \"length\") { name = \"_length\"; }\n\n            if (values[name] != null) { return; }\n\n            values[name] = values[index];\n        });\n\n        return Object.freeze(values);\n    }\n\n    encode(writer: Writer, _value: Array<any> | { [ name: string ]: any } | Typed): number {\n        const value = Typed.dereference(_value, \"tuple\");\n        return pack(writer, this.coders, value);\n    }\n\n    decode(reader: Reader): any {\n        return unpack(reader, this.coders);\n    }\n}\n\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,KAAK,QAAQ,qBAAqB;AAE3C,SAASC,IAAI,EAAEC,MAAM,QAAQ,YAAY;AAIzC;;;AAGA,OAAM,MAAOC,UAAW,SAAQH,KAAK;EACxBI,MAAM;EAEfC,YAAYD,MAAoB,EAAEE,SAAiB;IAC/C,IAAIC,OAAO,GAAG,KAAK;IACnB,MAAMC,KAAK,GAAkB,EAAE;IAC/BJ,MAAM,CAACK,OAAO,CAAEC,KAAK,IAAI;MACrB,IAAIA,KAAK,CAACH,OAAO,EAAE;QAAEA,OAAO,GAAG,IAAI;;MACnCC,KAAK,CAACG,IAAI,CAACD,KAAK,CAACE,IAAI,CAAC;IAC1B,CAAC,CAAC;IACF,MAAMA,IAAI,GAAI,QAAQ,GAAGJ,KAAK,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,GAAI;IAE/C,KAAK,CAAC,OAAO,EAAED,IAAI,EAAEN,SAAS,EAAEC,OAAO,CAAC;IACxCT,gBAAgB,CAAa,IAAI,EAAE;MAAEM,MAAM,EAAEU,MAAM,CAACC,MAAM,CAACX,MAAM,CAACY,KAAK,EAAE;IAAC,CAAE,CAAC;EACjF;EAEAC,YAAYA,CAAA;IACR,MAAMC,MAAM,GAAQ,EAAG;IACvB,IAAI,CAACd,MAAM,CAACK,OAAO,CAAEC,KAAK,IAAI;MAC1BQ,MAAM,CAACP,IAAI,CAACD,KAAK,CAACO,YAAY,EAAE,CAAC;IACrC,CAAC,CAAC;IAEF;IACA,MAAME,WAAW,GAAG,IAAI,CAACf,MAAM,CAACgB,MAAM,CAAC,CAACC,KAAK,EAAEX,KAAK,KAAI;MACpD,MAAMY,IAAI,GAAGZ,KAAK,CAACJ,SAAS;MAC5B,IAAIgB,IAAI,EAAE;QACN,IAAI,CAACD,KAAK,CAACC,IAAI,CAAC,EAAE;UAAED,KAAK,CAACC,IAAI,CAAC,GAAG,CAAC;;QACnCD,KAAK,CAACC,IAAI,CAAC,EAAE;;MAEjB,OAAOD,KAAK;IAChB,CAAC,EAAgC,EAAG,CAAC;IAErC;IACA,IAAI,CAACjB,MAAM,CAACK,OAAO,CAAC,CAACC,KAAY,EAAEa,KAAa,KAAI;MAChD,IAAID,IAAI,GAAGZ,KAAK,CAACJ,SAAS;MAC1B,IAAI,CAACgB,IAAI,IAAIH,WAAW,CAACG,IAAI,CAAC,KAAK,CAAC,EAAE;QAAE;;MAExC,IAAIA,IAAI,KAAK,QAAQ,EAAE;QAAEA,IAAI,GAAG,SAAS;;MAEzC,IAAIJ,MAAM,CAACI,IAAI,CAAC,IAAI,IAAI,EAAE;QAAE;;MAE5BJ,MAAM,CAACI,IAAI,CAAC,GAAGJ,MAAM,CAACK,KAAK,CAAC;IAChC,CAAC,CAAC;IAEF,OAAOT,MAAM,CAACC,MAAM,CAACG,MAAM,CAAC;EAChC;EAEAM,MAAMA,CAACC,MAAc,EAAEC,MAAsD;IACzE,MAAMC,KAAK,GAAG5B,KAAK,CAAC6B,WAAW,CAACF,MAAM,EAAE,OAAO,CAAC;IAChD,OAAOzB,IAAI,CAACwB,MAAM,EAAE,IAAI,CAACrB,MAAM,EAAEuB,KAAK,CAAC;EAC3C;EAEAE,MAAMA,CAACC,MAAc;IACjB,OAAO5B,MAAM,CAAC4B,MAAM,EAAE,IAAI,CAAC1B,MAAM,CAAC;EACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}