{"ast": null, "code": "/**\n *  When interacting with Ethereum, it is necessary to use a private\n *  key authenticate actions by signing a payload.\n *\n *  Wallets are the simplest way to expose the concept of an\n *  //Externally Owner Account// (EOA) as it wraps a private key\n *  and supports high-level methods to sign common types of interaction\n *  and send transactions.\n *\n *  The class most developers will want to use is [[Wallet]], which\n *  can load a private key directly or from any common wallet format.\n *\n *  The [[HDNodeWallet]] can be used when it is necessary to access\n *  low-level details of how an HD wallets are derived, exported\n *  or imported.\n *\n *  @_section: api/wallet:Wallets  [about-wallets]\n */\nexport { BaseWallet } from \"./base-wallet.js\";\nexport { defaultPath, getAccountPath, getIndexedAccountPath, HDNodeWallet, HDNodeVoidWallet } from \"./hdwallet.js\";\nexport { isCrowdsale<PERSON>son, decryptCrowdsaleJson } from \"./json-crowdsale.js\";\nexport { isKeystoreJson, decryptKeystoreJsonSync, decryptKeystoreJson, encryptKeystoreJson, encryptKeystoreJsonSync } from \"./json-keystore.js\";\nexport { Mnemonic } from \"./mnemonic.js\";\nexport { Wallet } from \"./wallet.js\";", "map": {"version": 3, "names": ["BaseWallet", "defaultPath", "getAccountPath", "getIndexedAccountPath", "HDNodeWallet", "HDNodeVoidWallet", "isCrowdsaleJson", "decryptCrowdsale<PERSON>son", "isKeystoreJson", "decryptKeystoreJsonSync", "decryptKeystoreJson", "encryptKeystoreJson", "encryptKeystoreJsonSync", "Mnemonic", "Wallet"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\index.ts"], "sourcesContent": ["/**\n *  When interacting with Ethereum, it is necessary to use a private\n *  key authenticate actions by signing a payload.\n *\n *  Wallets are the simplest way to expose the concept of an\n *  //Externally Owner Account// (EOA) as it wraps a private key\n *  and supports high-level methods to sign common types of interaction\n *  and send transactions.\n *\n *  The class most developers will want to use is [[Wallet]], which\n *  can load a private key directly or from any common wallet format.\n *\n *  The [[HDNodeWallet]] can be used when it is necessary to access\n *  low-level details of how an HD wallets are derived, exported\n *  or imported.\n *\n *  @_section: api/wallet:Wallets  [about-wallets]\n */\n\nexport { BaseWallet } from \"./base-wallet.js\";\n\nexport {\n    defaultPath,\n\n    getAccountPath, getIndexedAccountPath,\n\n    HDNodeWallet,\n    HDNodeVoidWallet,\n} from \"./hdwallet.js\";\n\nexport { isCrowdsaleJson, decryptCrowdsaleJson } from \"./json-crowdsale.js\";\n\nexport {\n    isKeystoreJson,\n    decryptKeystoreJsonSync, decryptKeystoreJson,\n    encryptKeystore<PERSON>son, encryptKeystoreJsonSync\n} from \"./json-keystore.js\";\n\nexport { Mnemonic } from \"./mnemonic.js\";\n\nexport { Wallet } from \"./wallet.js\";\n\n\nexport type { CrowdsaleAccount } from \"./json-crowdsale.js\";\nexport type {\n    KeystoreAccount, EncryptOptions\n} from \"./json-keystore.js\"\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;AAmBA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SACIC,WAAW,EAEXC,cAAc,EAAEC,qBAAqB,EAErCC,YAAY,EACZC,gBAAgB,QACb,eAAe;AAEtB,SAASC,eAAe,EAAEC,oBAAoB,QAAQ,qBAAqB;AAE3E,SACIC,cAAc,EACdC,uBAAuB,EAAEC,mBAAmB,EAC5CC,mBAAmB,EAAEC,uBAAuB,QACzC,oBAAoB;AAE3B,SAASC,QAAQ,QAAQ,eAAe;AAExC,SAASC,MAAM,QAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}