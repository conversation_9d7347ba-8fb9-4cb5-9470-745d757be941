{"ast": null, "code": "/**\n *  [[link-blockscout]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Ethereum Classic (``classic``)\n *  - Arbitrum (``arbitrum``)\n *  - Base (``base``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - Gnosis (``xdai``)\n *  - Optimism (``optimism``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *\n *  @_subsection: api/providers/thirdparty:Blockscout  [providers-blockscout]\n */\nimport { assertArgument, defineProperties, FetchRequest, isHexString } from \"../utils/index.js\";\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\nfunction getUrl(name) {\n  switch (name) {\n    case \"mainnet\":\n      return \"https:/\\/eth.blockscout.com/api/eth-rpc\";\n    case \"sepolia\":\n      return \"https:/\\/eth-sepolia.blockscout.com/api/eth-rpc\";\n    case \"holesky\":\n      return \"https:/\\/eth-holesky.blockscout.com/api/eth-rpc\";\n    case \"classic\":\n      return \"https:/\\/etc.blockscout.com/api/eth-rpc\";\n    case \"arbitrum\":\n      return \"https:/\\/arbitrum.blockscout.com/api/eth-rpc\";\n    case \"base\":\n      return \"https:/\\/base.blockscout.com/api/eth-rpc\";\n    case \"base-sepolia\":\n      return \"https:/\\/base-sepolia.blockscout.com/api/eth-rpc\";\n    case \"matic\":\n      return \"https:/\\/polygon.blockscout.com/api/eth-rpc\";\n    case \"optimism\":\n      return \"https:/\\/optimism.blockscout.com/api/eth-rpc\";\n    case \"optimism-sepolia\":\n      return \"https:/\\/optimism-sepolia.blockscout.com/api/eth-rpc\";\n    case \"xdai\":\n      return \"https:/\\/gnosis.blockscout.com/api/eth-rpc\";\n  }\n  assertArgument(false, \"unsupported network\", \"network\", name);\n}\n/**\n *  The **BlockscoutProvider** connects to the [[link-blockscout]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-blockscout).\n */\nexport class BlockscoutProvider extends JsonRpcProvider {\n  /**\n   *  The API key.\n   */\n  apiKey;\n  /**\n   *  Creates a new **BlockscoutProvider**.\n   */\n  constructor(_network, apiKey) {\n    if (_network == null) {\n      _network = \"mainnet\";\n    }\n    const network = Network.from(_network);\n    if (apiKey == null) {\n      apiKey = null;\n    }\n    const request = BlockscoutProvider.getRequest(network);\n    super(request, network, {\n      staticNetwork: network\n    });\n    defineProperties(this, {\n      apiKey\n    });\n  }\n  _getProvider(chainId) {\n    try {\n      return new BlockscoutProvider(chainId, this.apiKey);\n    } catch (error) {}\n    return super._getProvider(chainId);\n  }\n  isCommunityResource() {\n    return this.apiKey === null;\n  }\n  getRpcRequest(req) {\n    // Blockscout enforces the TAG argument for estimateGas\n    const resp = super.getRpcRequest(req);\n    if (resp && resp.method === \"eth_estimateGas\" && resp.args.length == 1) {\n      resp.args = resp.args.slice();\n      resp.args.push(\"latest\");\n    }\n    return resp;\n  }\n  getRpcError(payload, _error) {\n    const error = _error ? _error.error : null;\n    // Blockscout currently drops the VM result and replaces it with a\n    // human-readable string, so we need to make it machine-readable.\n    if (error && error.code === -32015 && !isHexString(error.data || \"\", true)) {\n      const panicCodes = {\n        \"assert(false)\": \"01\",\n        \"arithmetic underflow or overflow\": \"11\",\n        \"division or modulo by zero\": \"12\",\n        \"out-of-bounds array access; popping on an empty array\": \"31\",\n        \"out-of-bounds access of an array or bytesN\": \"32\"\n      };\n      let panicCode = \"\";\n      if (error.message === \"VM execution error.\") {\n        // eth_call passes this message\n        panicCode = panicCodes[error.data] || \"\";\n      } else if (panicCodes[error.message || \"\"]) {\n        panicCode = panicCodes[error.message || \"\"];\n      }\n      if (panicCode) {\n        error.message += ` (reverted: ${error.data})`;\n        error.data = \"0x4e487b7100000000000000000000000000000000000000000000000000000000000000\" + panicCode;\n      }\n    } else if (error && error.code === -32000) {\n      if (error.message === \"wrong transaction nonce\") {\n        error.message += \" (nonce too low)\";\n      }\n    }\n    return super.getRpcError(payload, _error);\n  }\n  /**\n   *  Returns a prepared request for connecting to %%network%%\n   *  with %%apiKey%%.\n   */\n  static getRequest(network) {\n    const request = new FetchRequest(getUrl(network.name));\n    request.allowGzip = true;\n    return request;\n  }\n}", "map": {"version": 3, "names": ["assertArgument", "defineProperties", "FetchRequest", "isHexString", "Network", "JsonRpcProvider", "getUrl", "name", "BlockscoutProvider", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "_network", "network", "from", "request", "getRequest", "staticNetwork", "_get<PERSON><PERSON><PERSON>", "chainId", "error", "isCommunityResource", "getRpcRequest", "req", "resp", "method", "args", "length", "slice", "push", "getRpcError", "payload", "_error", "code", "data", "panicCodes", "panicCode", "message", "allowGzip"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\providers\\provider-blockscout.ts"], "sourcesContent": ["\n/**\n *  [[link-blockscout]] provides a third-party service for connecting to\n *  various blockchains over JSON-RPC.\n *\n *  **Supported Networks**\n *\n *  - Ethereum Mainnet (``mainnet``)\n *  - Sepolia Testnet (``sepolia``)\n *  - Holesky Testnet (``holesky``)\n *  - Ethereum Classic (``classic``)\n *  - Arbitrum (``arbitrum``)\n *  - Base (``base``)\n *  - Base Sepolia Testnet (``base-sepolia``)\n *  - Gnosis (``xdai``)\n *  - Optimism (``optimism``)\n *  - Optimism Sepolia Testnet (``optimism-sepolia``)\n *  - Polygon (``matic``)\n *\n *  @_subsection: api/providers/thirdparty:Blockscout  [providers-blockscout]\n */\nimport {\n    assertArgument, defineProperties, FetchRequest, isHexString\n} from \"../utils/index.js\";\n\nimport { Network } from \"./network.js\";\nimport { JsonRpcProvider } from \"./provider-jsonrpc.js\";\n\nimport type { AbstractProvider, PerformActionRequest } from \"./abstract-provider.js\";\nimport type { CommunityResourcable } from \"./community.js\";\nimport type { Networkish } from \"./network.js\";\nimport type { JsonRpcPayload, JsonRpcError } from \"./provider-jsonrpc.js\";\n\n\nfunction getUrl(name: string): string {\n    switch(name) {\n        case \"mainnet\":\n            return \"https:/\\/eth.blockscout.com/api/eth-rpc\";\n        case \"sepolia\":\n            return \"https:/\\/eth-sepolia.blockscout.com/api/eth-rpc\";\n        case \"holesky\":\n            return \"https:/\\/eth-holesky.blockscout.com/api/eth-rpc\";\n\n        case \"classic\":\n            return \"https:/\\/etc.blockscout.com/api/eth-rpc\";\n\n        case \"arbitrum\":\n            return \"https:/\\/arbitrum.blockscout.com/api/eth-rpc\";\n\n        case \"base\":\n            return \"https:/\\/base.blockscout.com/api/eth-rpc\";\n        case \"base-sepolia\":\n            return \"https:/\\/base-sepolia.blockscout.com/api/eth-rpc\";\n\n        case \"matic\":\n            return \"https:/\\/polygon.blockscout.com/api/eth-rpc\";\n\n        case \"optimism\":\n            return \"https:/\\/optimism.blockscout.com/api/eth-rpc\";\n        case \"optimism-sepolia\":\n            return \"https:/\\/optimism-sepolia.blockscout.com/api/eth-rpc\";\n\n        case \"xdai\":\n            return \"https:/\\/gnosis.blockscout.com/api/eth-rpc\";\n    }\n\n    assertArgument(false, \"unsupported network\", \"network\", name);\n}\n\n\n/**\n *  The **BlockscoutProvider** connects to the [[link-blockscout]]\n *  JSON-RPC end-points.\n *\n *  By default, a highly-throttled API key is used, which is\n *  appropriate for quick prototypes and simple scripts. To\n *  gain access to an increased rate-limit, it is highly\n *  recommended to [sign up here](link-blockscout).\n */\nexport class BlockscoutProvider extends JsonRpcProvider implements CommunityResourcable {\n    /**\n     *  The API key.\n     */\n    readonly apiKey!: null | string;\n\n    /**\n     *  Creates a new **BlockscoutProvider**.\n     */\n    constructor(_network?: Networkish, apiKey?: null | string) {\n        if (_network == null) { _network = \"mainnet\"; }\n        const network = Network.from(_network);\n\n        if (apiKey == null) { apiKey = null; }\n\n        const request = BlockscoutProvider.getRequest(network);\n        super(request, network, { staticNetwork: network });\n\n        defineProperties<BlockscoutProvider>(this, { apiKey });\n    }\n\n    _getProvider(chainId: number): AbstractProvider {\n        try {\n            return new BlockscoutProvider(chainId, this.apiKey);\n        } catch (error) { }\n        return super._getProvider(chainId);\n    }\n\n    isCommunityResource(): boolean {\n        return (this.apiKey === null);\n    }\n\n    getRpcRequest(req: PerformActionRequest): null | { method: string, args: Array<any> } {\n        // Blockscout enforces the TAG argument for estimateGas\n        const resp = super.getRpcRequest(req);\n        if (resp && resp.method === \"eth_estimateGas\" && resp.args.length == 1) {\n            resp.args = resp.args.slice();\n            resp.args.push(\"latest\");\n        }\n        return resp;\n    }\n\n    getRpcError(payload: JsonRpcPayload, _error: JsonRpcError): Error {\n        const error = _error ? _error.error: null;\n\n        // Blockscout currently drops the VM result and replaces it with a\n        // human-readable string, so we need to make it machine-readable.\n        if (error && error.code === -32015 && !isHexString(error.data || \"\", true)) {\n            const panicCodes = <Record<string, string>>{\n                \"assert(false)\": \"01\",\n                \"arithmetic underflow or overflow\": \"11\",\n                \"division or modulo by zero\": \"12\",\n                \"out-of-bounds array access; popping on an empty array\": \"31\",\n                \"out-of-bounds access of an array or bytesN\": \"32\"\n            };\n\n            let panicCode = \"\";\n            if (error.message === \"VM execution error.\") {\n                // eth_call passes this message\n                panicCode = panicCodes[error.data] || \"\";\n            } else if (panicCodes[error.message || \"\"]) {\n                panicCode = panicCodes[error.message || \"\"];\n            }\n\n            if (panicCode) {\n                error.message += ` (reverted: ${ error.data })`;\n                error.data = \"0x4e487b7100000000000000000000000000000000000000000000000000000000000000\" + panicCode;\n            }\n\n        } else if (error && error.code === -32000) {\n            if (error.message === \"wrong transaction nonce\") {\n                error.message += \" (nonce too low)\";\n            }\n        }\n\n        return super.getRpcError(payload, _error);\n    }\n\n    /**\n     *  Returns a prepared request for connecting to %%network%%\n     *  with %%apiKey%%.\n     */\n    static getRequest(network: Network): FetchRequest {\n        const request = new FetchRequest(getUrl(network.name));\n        request.allowGzip = true;\n        return request;\n    }\n}\n"], "mappings": "AACA;;;;;;;;;;;;;;;;;;;;AAoBA,SACIA,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,WAAW,QACxD,mBAAmB;AAE1B,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,eAAe,QAAQ,uBAAuB;AAQvD,SAASC,MAAMA,CAACC,IAAY;EACxB,QAAOA,IAAI;IACP,KAAK,SAAS;MACV,OAAO,yCAAyC;IACpD,KAAK,SAAS;MACV,OAAO,iDAAiD;IAC5D,KAAK,SAAS;MACV,OAAO,iDAAiD;IAE5D,KAAK,SAAS;MACV,OAAO,yCAAyC;IAEpD,KAAK,UAAU;MACX,OAAO,8CAA8C;IAEzD,KAAK,MAAM;MACP,OAAO,0CAA0C;IACrD,KAAK,cAAc;MACf,OAAO,kDAAkD;IAE7D,KAAK,OAAO;MACR,OAAO,6CAA6C;IAExD,KAAK,UAAU;MACX,OAAO,8CAA8C;IACzD,KAAK,kBAAkB;MACnB,OAAO,sDAAsD;IAEjE,KAAK,MAAM;MACP,OAAO,4CAA4C;;EAG3DP,cAAc,CAAC,KAAK,EAAE,qBAAqB,EAAE,SAAS,EAAEO,IAAI,CAAC;AACjE;AAGA;;;;;;;;;AASA,OAAM,MAAOC,kBAAmB,SAAQH,eAAe;EACnD;;;EAGSI,MAAM;EAEf;;;EAGAC,YAAYC,QAAqB,EAAEF,MAAsB;IACrD,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,SAAS;;IAC5C,MAAMC,OAAO,GAAGR,OAAO,CAACS,IAAI,CAACF,QAAQ,CAAC;IAEtC,IAAIF,MAAM,IAAI,IAAI,EAAE;MAAEA,MAAM,GAAG,IAAI;;IAEnC,MAAMK,OAAO,GAAGN,kBAAkB,CAACO,UAAU,CAACH,OAAO,CAAC;IACtD,KAAK,CAACE,OAAO,EAAEF,OAAO,EAAE;MAAEI,aAAa,EAAEJ;IAAO,CAAE,CAAC;IAEnDX,gBAAgB,CAAqB,IAAI,EAAE;MAAEQ;IAAM,CAAE,CAAC;EAC1D;EAEAQ,YAAYA,CAACC,OAAe;IACxB,IAAI;MACA,OAAO,IAAIV,kBAAkB,CAACU,OAAO,EAAE,IAAI,CAACT,MAAM,CAAC;KACtD,CAAC,OAAOU,KAAK,EAAE;IAChB,OAAO,KAAK,CAACF,YAAY,CAACC,OAAO,CAAC;EACtC;EAEAE,mBAAmBA,CAAA;IACf,OAAQ,IAAI,CAACX,MAAM,KAAK,IAAI;EAChC;EAEAY,aAAaA,CAACC,GAAyB;IACnC;IACA,MAAMC,IAAI,GAAG,KAAK,CAACF,aAAa,CAACC,GAAG,CAAC;IACrC,IAAIC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,iBAAiB,IAAID,IAAI,CAACE,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE;MACpEH,IAAI,CAACE,IAAI,GAAGF,IAAI,CAACE,IAAI,CAACE,KAAK,EAAE;MAC7BJ,IAAI,CAACE,IAAI,CAACG,IAAI,CAAC,QAAQ,CAAC;;IAE5B,OAAOL,IAAI;EACf;EAEAM,WAAWA,CAACC,OAAuB,EAAEC,MAAoB;IACrD,MAAMZ,KAAK,GAAGY,MAAM,GAAGA,MAAM,CAACZ,KAAK,GAAE,IAAI;IAEzC;IACA;IACA,IAAIA,KAAK,IAAIA,KAAK,CAACa,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC7B,WAAW,CAACgB,KAAK,CAACc,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE;MACxE,MAAMC,UAAU,GAA2B;QACvC,eAAe,EAAE,IAAI;QACrB,kCAAkC,EAAE,IAAI;QACxC,4BAA4B,EAAE,IAAI;QAClC,uDAAuD,EAAE,IAAI;QAC7D,4CAA4C,EAAE;OACjD;MAED,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIhB,KAAK,CAACiB,OAAO,KAAK,qBAAqB,EAAE;QACzC;QACAD,SAAS,GAAGD,UAAU,CAACf,KAAK,CAACc,IAAI,CAAC,IAAI,EAAE;OAC3C,MAAM,IAAIC,UAAU,CAACf,KAAK,CAACiB,OAAO,IAAI,EAAE,CAAC,EAAE;QACxCD,SAAS,GAAGD,UAAU,CAACf,KAAK,CAACiB,OAAO,IAAI,EAAE,CAAC;;MAG/C,IAAID,SAAS,EAAE;QACXhB,KAAK,CAACiB,OAAO,IAAI,eAAgBjB,KAAK,CAACc,IAAK,GAAG;QAC/Cd,KAAK,CAACc,IAAI,GAAG,0EAA0E,GAAGE,SAAS;;KAG1G,MAAM,IAAIhB,KAAK,IAAIA,KAAK,CAACa,IAAI,KAAK,CAAC,KAAK,EAAE;MACvC,IAAIb,KAAK,CAACiB,OAAO,KAAK,yBAAyB,EAAE;QAC7CjB,KAAK,CAACiB,OAAO,IAAI,kBAAkB;;;IAI3C,OAAO,KAAK,CAACP,WAAW,CAACC,OAAO,EAAEC,MAAM,CAAC;EAC7C;EAEA;;;;EAIA,OAAOhB,UAAUA,CAACH,OAAgB;IAC9B,MAAME,OAAO,GAAG,IAAIZ,YAAY,CAACI,MAAM,CAACM,OAAO,CAACL,IAAI,CAAC,CAAC;IACtDO,OAAO,CAACuB,SAAS,GAAG,IAAI;IACxB,OAAOvB,OAAO;EAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}