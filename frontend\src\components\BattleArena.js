import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HeartIcon, 
  BoltIcon, 
  ShieldCheckIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';

const BattleArena = ({ playerHeroes, enemyHeroes, battleLog, onBattleEnd, isActive = false }) => {
  const [currentLogIndex, setCurrentLogIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [heroStates, setHeroStates] = useState({});

  useEffect(() => {
    if (battleLog && battleLog.length > 0) {
      initializeHeroStates();
    }
  }, [battleLog, playerHeroes, enemyHeroes]);

  useEffect(() => {
    let interval;
    if (isPlaying && currentLogIndex < battleLog.length) {
      interval = setInterval(() => {
        processNextLogEntry();
      }, 1500);
    } else if (currentLogIndex >= battleLog.length && onBattleEnd) {
      onBattleEnd();
    }

    return () => clearInterval(interval);
  }, [isPlaying, currentLogIndex, battleLog]);

  const initializeHeroStates = () => {
    const states = {};
    
    playerHeroes.forEach(hero => {
      states[hero.id] = {
        ...hero,
        currentHp: hero.stats.hp,
        maxHp: hero.stats.hp,
        isAlive: true,
        isPlayer: true,
        animation: null
      };
    });

    enemyHeroes.forEach(enemy => {
      states[enemy.id] = {
        ...enemy,
        currentHp: enemy.stats?.hp || enemy.hp,
        maxHp: enemy.stats?.hp || enemy.hp,
        isAlive: true,
        isPlayer: false,
        animation: null
      };
    });

    setHeroStates(states);
  };

  const processNextLogEntry = () => {
    if (currentLogIndex >= battleLog.length) return;

    const logEntry = battleLog[currentLogIndex];
    
    if (logEntry.attacker && logEntry.target && logEntry.damage) {
      // Find heroes by name
      const targetHero = Object.values(heroStates).find(h => h.name === logEntry.target);
      
      if (targetHero) {
        setHeroStates(prev => ({
          ...prev,
          [targetHero.id]: {
            ...prev[targetHero.id],
            currentHp: Math.max(0, logEntry.targetHp || prev[targetHero.id].currentHp - logEntry.damage),
            animation: 'damage',
            isAlive: (logEntry.targetHp || prev[targetHero.id].currentHp - logEntry.damage) > 0
          }
        }));

        // Clear animation after delay
        setTimeout(() => {
          setHeroStates(prev => ({
            ...prev,
            [targetHero.id]: {
              ...prev[targetHero.id],
              animation: null
            }
          }));
        }, 500);
      }
    }

    setCurrentLogIndex(prev => prev + 1);
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
  };

  const resetBattle = () => {
    setCurrentLogIndex(0);
    setIsPlaying(false);
    initializeHeroStates();
  };

  const getHpPercentage = (hero) => {
    if (!hero.maxHp) return 100;
    return (hero.currentHp / hero.maxHp) * 100;
  };

  const getHpColor = (percentage) => {
    if (percentage > 60) return 'bg-green-500';
    if (percentage > 30) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="battle-arena min-h-96 p-6">
      {/* Battle Controls */}
      <div className="flex justify-center mb-6 space-x-4">
        <button
          onClick={togglePlayback}
          className="game-button flex items-center space-x-2"
          disabled={!battleLog || battleLog.length === 0}
        >
          {isPlaying ? <PauseIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
          <span>{isPlaying ? 'Pause' : 'Play'}</span>
        </button>
        <button
          onClick={resetBattle}
          className="game-button-secondary"
          disabled={!battleLog || battleLog.length === 0}
        >
          Reset
        </button>
      </div>

      {/* Battle Field */}
      <div className="grid grid-cols-2 gap-8 mb-6">
        {/* Player Team */}
        <div>
          <h3 className="text-lg font-bold text-blue-400 mb-4 text-center">Your Heroes</h3>
          <div className="space-y-3">
            {playerHeroes.map(hero => {
              const heroState = heroStates[hero.id] || hero;
              const hpPercentage = getHpPercentage(heroState);
              
              return (
                <motion.div
                  key={hero.id}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    heroState.isAlive 
                      ? 'border-blue-500 bg-blue-900/20' 
                      : 'border-gray-600 bg-gray-900/50 opacity-50'
                  } ${heroState.animation === 'damage' ? 'shake' : ''}`}
                  animate={heroState.animation === 'damage' ? { x: [-5, 5, -5, 0] } : {}}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{hero.name}</span>
                    <span className="text-sm text-blue-400 capitalize">{hero.type}</span>
                  </div>
                  
                  {/* HP Bar */}
                  <div className="mb-2">
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>HP</span>
                      <span>{heroState.currentHp}/{heroState.maxHp}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full ${getHpColor(hpPercentage)}`}
                        initial={{ width: '100%' }}
                        animate={{ width: `${hpPercentage}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <BoltIcon className="w-3 h-3 text-orange-400" />
                      <span className="text-gray-300">{hero.stats.atk}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ShieldCheckIcon className="w-3 h-3 text-blue-400" />
                      <span className="text-gray-300">{hero.stats.def}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-green-400">SPD</span>
                      <span className="text-gray-300">{hero.stats.spd}</span>
                    </div>
                  </div>

                  {/* Damage Animation */}
                  <AnimatePresence>
                    {heroState.animation === 'damage' && (
                      <motion.div
                        className="absolute floating-damage"
                        initial={{ opacity: 1, y: 0 }}
                        animate={{ opacity: 0, y: -30 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 1 }}
                      >
                        -{battleLog[currentLogIndex - 1]?.damage || 0}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })}
          </div>
        </div>

        {/* Enemy Team */}
        <div>
          <h3 className="text-lg font-bold text-red-400 mb-4 text-center">Enemies</h3>
          <div className="space-y-3">
            {enemyHeroes.map(enemy => {
              const enemyState = heroStates[enemy.id] || enemy;
              const hpPercentage = getHpPercentage(enemyState);
              
              return (
                <motion.div
                  key={enemy.id}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    enemyState.isAlive 
                      ? 'border-red-500 bg-red-900/20' 
                      : 'border-gray-600 bg-gray-900/50 opacity-50'
                  } ${enemyState.animation === 'damage' ? 'shake' : ''}`}
                  animate={enemyState.animation === 'damage' ? { x: [-5, 5, -5, 0] } : {}}
                  transition={{ duration: 0.5 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-white">{enemy.name}</span>
                    <span className="text-sm text-red-400">Level {enemy.level}</span>
                  </div>
                  
                  {/* HP Bar */}
                  <div className="mb-2">
                    <div className="flex justify-between text-xs text-gray-400 mb-1">
                      <span>HP</span>
                      <span>{enemyState.currentHp}/{enemyState.maxHp}</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <motion.div
                        className={`h-2 rounded-full ${getHpColor(hpPercentage)}`}
                        initial={{ width: '100%' }}
                        animate={{ width: `${hpPercentage}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <BoltIcon className="w-3 h-3 text-orange-400" />
                      <span className="text-gray-300">{enemy.stats?.atk || enemy.atk}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ShieldCheckIcon className="w-3 h-3 text-blue-400" />
                      <span className="text-gray-300">{enemy.stats?.def || enemy.def}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="text-green-400">SPD</span>
                      <span className="text-gray-300">{enemy.stats?.spd || enemy.spd}</span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Battle Log */}
      <div className="game-card p-4">
        <h4 className="font-medium text-white mb-3">Battle Log</h4>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {battleLog && battleLog.slice(0, currentLogIndex + 1).map((entry, index) => (
            <div
              key={index}
              className={`text-sm p-2 rounded ${
                index === currentLogIndex ? 'bg-primary-900/30 border border-primary-700' : 'bg-dark-700/30'
              }`}
            >
              {entry.event ? (
                <span className="text-yellow-400">{entry.event}</span>
              ) : (
                <span className="text-gray-300">
                  <span className="text-blue-400">{entry.attacker}</span> attacks{' '}
                  <span className="text-red-400">{entry.target}</span> for{' '}
                  <span className="text-orange-400">{entry.damage}</span> damage
                </span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Progress Bar */}
      {battleLog && battleLog.length > 0 && (
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-400 mb-1">
            <span>Battle Progress</span>
            <span>{currentLogIndex}/{battleLog.length}</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentLogIndex / battleLog.length) * 100}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default BattleArena;
