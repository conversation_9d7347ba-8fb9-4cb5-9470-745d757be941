const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("StakingPool", function () {
  let cqtToken, stakingPool;
  let owner, playToEarn, ecosystem, team, marketing, staking, liquidity;
  let user1, user2;

  beforeEach(async function () {
    [owner, playToEarn, ecosystem, team, marketing, staking, liquidity, user1, user2] = await ethers.getSigners();

    // Deploy CQTToken
    const CQTToken = await ethers.getContractFactory("CQTToken");
    cqtToken = await CQTToken.deploy(
      playToEarn.address,
      ecosystem.address,
      team.address,
      marketing.address,
      staking.address,
      liquidity.address
    );
    await cqtToken.waitForDeployment();

    // Deploy StakingPool
    const StakingPool = await ethers.getContractFactory("StakingPool");
    stakingPool = await StakingPool.deploy(await cqtToken.getAddress());
    await stakingPool.waitForDeployment();

    // Transfer some tokens to staking pool for rewards
    const stakingRewards = ethers.parseEther("1000000");
    await cqtToken.transfer(await stakingPool.getAddress(), stakingRewards);

    // Give users some tokens to stake
    const userTokens = ethers.parseEther("10000");
    await cqtToken.transfer(user1.address, userTokens);
    await cqtToken.transfer(user2.address, userTokens);
  });

  describe("Staking", function () {
    it("Should allow users to stake tokens", async function () {
      const stakeAmount = ethers.parseEther("1000");
      
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
      
      const stakeInfo = await stakingPool.getStakeInfo(user1.address);
      expect(stakeInfo[0]).to.equal(stakeAmount); // amount
      expect(stakeInfo[4]).to.be.true; // isActive
      expect(stakeInfo[5]).to.be.true; // xpBoost (for 1000+ CQT)
      
      expect(await stakingPool.totalStaked()).to.equal(stakeAmount);
    });

    it("Should not allow staking below minimum amount", async function () {
      const stakeAmount = ethers.parseEther("50"); // Below 100 CQT minimum
      
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      
      await expect(
        stakingPool.connect(user1).stake(stakeAmount)
      ).to.be.revertedWith("Amount below minimum stake");
    });

    it("Should not allow staking without sufficient balance", async function () {
      const stakeAmount = ethers.parseEther("20000"); // More than user has
      
      await expect(
        stakingPool.connect(user1).stake(stakeAmount)
      ).to.be.revertedWith("Insufficient balance");
    });

    it("Should activate XP boost for stakes >= 1000 CQT", async function () {
      const stakeAmount = ethers.parseEther("1000");
      
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
      
      expect(await stakingPool.hasStakingBoost(user1.address)).to.be.true;
    });

    it("Should not activate XP boost for stakes < 1000 CQT", async function () {
      const stakeAmount = ethers.parseEther("500");
      
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
      
      expect(await stakingPool.hasStakingBoost(user1.address)).to.be.false;
    });

    it("Should allow additional staking to existing stake", async function () {
      const firstStake = ethers.parseEther("500");
      const secondStake = ethers.parseEther("600");
      
      // First stake
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), firstStake);
      await stakingPool.connect(user1).stake(firstStake);
      
      // Second stake
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), secondStake);
      await stakingPool.connect(user1).stake(secondStake);
      
      const stakeInfo = await stakingPool.getStakeInfo(user1.address);
      expect(stakeInfo[0]).to.equal(firstStake + secondStake);
      expect(stakeInfo[5]).to.be.true; // XP boost should now be active
    });
  });

  describe("Unstaking", function () {
    beforeEach(async function () {
      const stakeAmount = ethers.parseEther("1000");
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
    });

    it("Should not allow unstaking before lock period", async function () {
      await expect(
        stakingPool.connect(user1).unstake()
      ).to.be.revertedWith("Stake is still locked");
    });

    it("Should allow unstaking after lock period", async function () {
      // Fast forward time by 7 days
      await time.increase(7 * 24 * 60 * 60);
      
      const initialBalance = await cqtToken.balanceOf(user1.address);
      const stakeAmount = ethers.parseEther("1000");
      
      await stakingPool.connect(user1).unstake();
      
      const finalBalance = await cqtToken.balanceOf(user1.address);
      expect(finalBalance - initialBalance).to.equal(stakeAmount);
      
      const stakeInfo = await stakingPool.getStakeInfo(user1.address);
      expect(stakeInfo[4]).to.be.false; // isActive should be false
      expect(await stakingPool.hasStakingBoost(user1.address)).to.be.false;
    });

    it("Should allow emergency unstaking without rewards", async function () {
      const initialBalance = await cqtToken.balanceOf(user1.address);
      const stakeAmount = ethers.parseEther("1000");
      
      await stakingPool.connect(user1).emergencyUnstake();
      
      const finalBalance = await cqtToken.balanceOf(user1.address);
      expect(finalBalance - initialBalance).to.equal(stakeAmount);
      
      const stakeInfo = await stakingPool.getStakeInfo(user1.address);
      expect(stakeInfo[4]).to.be.false; // isActive should be false
    });
  });

  describe("Rewards", function () {
    beforeEach(async function () {
      const stakeAmount = ethers.parseEther("1000");
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
    });

    it("Should calculate pending rewards correctly", async function () {
      // Fast forward time by 1 day
      await time.increase(24 * 60 * 60);
      
      const pendingRewards = await stakingPool.calculatePendingRewards(user1.address);
      expect(pendingRewards).to.be.gt(0);
      
      // Rewards should be approximately 12% APY
      // For 1000 CQT staked for 1 day: (1000 * 0.12) / 365 ≈ 0.328 CQT
      const expectedDailyReward = ethers.parseEther("1000") * BigInt(1200) / BigInt(10000) / BigInt(365);
      expect(pendingRewards).to.be.closeTo(expectedDailyReward, ethers.parseEther("0.01"));
    });

    it("Should allow claiming rewards", async function () {
      // Fast forward time by 1 day
      await time.increase(24 * 60 * 60);
      
      const initialBalance = await cqtToken.balanceOf(user1.address);
      const pendingRewards = await stakingPool.calculatePendingRewards(user1.address);
      
      await stakingPool.connect(user1).claimRewards();
      
      const finalBalance = await cqtToken.balanceOf(user1.address);
      expect(finalBalance - initialBalance).to.be.closeTo(pendingRewards, ethers.parseEther("0.001"));
    });

    it("Should reset pending rewards after claiming", async function () {
      // Fast forward time by 1 day
      await time.increase(24 * 60 * 60);
      
      await stakingPool.connect(user1).claimRewards();
      
      const pendingRewards = await stakingPool.calculatePendingRewards(user1.address);
      expect(pendingRewards).to.be.lt(ethers.parseEther("0.001")); // Should be very small
    });

    it("Should accumulate rewards over time", async function () {
      // Fast forward time by 1 day
      await time.increase(24 * 60 * 60);
      const rewards1Day = await stakingPool.calculatePendingRewards(user1.address);
      
      // Fast forward another day
      await time.increase(24 * 60 * 60);
      const rewards2Days = await stakingPool.calculatePendingRewards(user1.address);
      
      expect(rewards2Days).to.be.gt(rewards1Day);
      expect(rewards2Days).to.be.closeTo(rewards1Day * BigInt(2), ethers.parseEther("0.01"));
    });
  });

  describe("Pool Statistics", function () {
    it("Should track total staked correctly", async function () {
      const stake1 = ethers.parseEther("1000");
      const stake2 = ethers.parseEther("500");
      
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stake1);
      await stakingPool.connect(user1).stake(stake1);
      
      await cqtToken.connect(user2).approve(await stakingPool.getAddress(), stake2);
      await stakingPool.connect(user2).stake(stake2);
      
      expect(await stakingPool.totalStaked()).to.equal(stake1 + stake2);
    });

    it("Should return correct pool statistics", async function () {
      const stakeAmount = ethers.parseEther("1000");
      await cqtToken.connect(user1).approve(await stakingPool.getAddress(), stakeAmount);
      await stakingPool.connect(user1).stake(stakeAmount);
      
      const poolStats = await stakingPool.getPoolStats();
      expect(poolStats[0]).to.equal(stakeAmount); // totalStaked
      expect(poolStats[2]).to.equal(1200); // currentAPY (12%)
    });
  });

  describe("Configuration", function () {
    it("Should allow owner to update reward rate", async function () {
      const newRate = 1500; // 15% APY
      
      await stakingPool.updateRewardRate(newRate);
      expect(await stakingPool.annualRewardRate()).to.equal(newRate);
    });

    it("Should not allow setting reward rate too high", async function () {
      const tooHighRate = 6000; // 60% APY
      
      await expect(
        stakingPool.updateRewardRate(tooHighRate)
      ).to.be.revertedWith("Rate too high");
    });

    it("Should not allow non-owner to update reward rate", async function () {
      await expect(
        stakingPool.connect(user1).updateRewardRate(1500)
      ).to.be.revertedWithCustomError(stakingPool, "OwnableUnauthorizedAccount");
    });
  });
});
