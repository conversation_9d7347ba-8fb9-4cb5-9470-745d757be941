{"ast": null, "code": "/**\n *  @_subsection: api/wallet:JSON Wallets  [json-wallets]\n */\nimport { CBC, pkcs7Strip } from \"aes-js\";\nimport { getAddress } from \"../address/index.js\";\nimport { pbkdf2 } from \"../crypto/index.js\";\nimport { id } from \"../hash/index.js\";\nimport { getBytes, assertArgument } from \"../utils/index.js\";\nimport { getPassword, looseArrayify, spelunk } from \"./utils.js\";\n/**\n *  Returns true if %%json%% is a valid JSON Crowdsale wallet.\n */\nexport function isCrowdsaleJson(json) {\n  try {\n    const data = JSON.parse(json);\n    if (data.encseed) {\n      return true;\n    }\n  } catch (error) {}\n  return false;\n}\n// See: https://github.com/ethereum/pyethsaletool\n/**\n *  Before Ethereum launched, it was necessary to create a wallet\n *  format for backers to use, which would be used to receive ether\n *  as a reward for contributing to the project.\n *\n *  The [[link-crowdsale]] format is now obsolete, but it is still\n *  useful to support and the additional code is fairly trivial as\n *  all the primitives required are used through core portions of\n *  the library.\n */\nexport function decryptCrowdsaleJson(json, _password) {\n  const data = JSON.parse(json);\n  const password = getPassword(_password);\n  // Ethereum Address\n  const address = getAddress(spelunk(data, \"ethaddr:string!\"));\n  // Encrypted Seed\n  const encseed = looseArrayify(spelunk(data, \"encseed:string!\"));\n  assertArgument(encseed && encseed.length % 16 === 0, \"invalid encseed\", \"json\", json);\n  const key = getBytes(pbkdf2(password, password, 2000, 32, \"sha256\")).slice(0, 16);\n  const iv = encseed.slice(0, 16);\n  const encryptedSeed = encseed.slice(16);\n  // Decrypt the seed\n  const aesCbc = new CBC(key, iv);\n  const seed = pkcs7Strip(getBytes(aesCbc.decrypt(encryptedSeed)));\n  // This wallet format is weird... Convert the binary encoded hex to a string.\n  let seedHex = \"\";\n  for (let i = 0; i < seed.length; i++) {\n    seedHex += String.fromCharCode(seed[i]);\n  }\n  return {\n    address,\n    privateKey: id(seedHex)\n  };\n}", "map": {"version": 3, "names": ["CBC", "pkcs7Strip", "get<PERSON><PERSON><PERSON>", "pbkdf2", "id", "getBytes", "assertArgument", "getPassword", "looseArrayify", "spelunk", "isCrowdsaleJson", "json", "data", "JSON", "parse", "encseed", "error", "decryptCrowdsale<PERSON>son", "_password", "password", "address", "length", "key", "slice", "iv", "encryptedSeed", "aesCbc", "seed", "decrypt", "seedHex", "i", "String", "fromCharCode", "privateKey"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\json-crowdsale.ts"], "sourcesContent": ["/**\n *  @_subsection: api/wallet:JSON Wallets  [json-wallets]\n */\n\nimport { CBC, pkcs7Strip } from \"aes-js\";\n\nimport { getAddress } from \"../address/index.js\";\nimport { pbkdf2 } from \"../crypto/index.js\";\nimport { id } from \"../hash/index.js\";\nimport { getBytes, assertArgument } from \"../utils/index.js\";\n\nimport { getPassword, looseArrayify, spelunk } from \"./utils.js\";\n\n\n/**\n *  The data stored within a JSON Crowdsale wallet is fairly\n *  minimal.\n */\nexport type CrowdsaleAccount = {\n    privateKey: string;\n    address: string;\n}\n\n/**\n *  Returns true if %%json%% is a valid JSON Crowdsale wallet.\n */\nexport function isCrowdsaleJson(json: string): boolean {\n    try {\n        const data = JSON.parse(json);\n        if (data.encseed) { return true; }\n    } catch (error) { }\n    return false;\n}\n\n// See: https://github.com/ethereum/pyethsaletool\n\n/**\n *  Before Ethereum launched, it was necessary to create a wallet\n *  format for backers to use, which would be used to receive ether\n *  as a reward for contributing to the project.\n *\n *  The [[link-crowdsale]] format is now obsolete, but it is still\n *  useful to support and the additional code is fairly trivial as\n *  all the primitives required are used through core portions of\n *  the library.\n */\nexport function decryptCrowdsaleJson(json: string, _password: string | Uint8Array): CrowdsaleAccount {\n    const data = JSON.parse(json);\n    const password = getPassword(_password);\n\n    // Ethereum Address\n    const address = getAddress(spelunk(data, \"ethaddr:string!\"));\n\n    // Encrypted Seed\n    const encseed = looseArrayify(spelunk(data, \"encseed:string!\"));\n    assertArgument(encseed && (encseed.length % 16) === 0, \"invalid encseed\", \"json\", json);\n\n    const key = getBytes(pbkdf2(password, password, 2000, 32, \"sha256\")).slice(0, 16);\n\n    const iv = encseed.slice(0, 16);\n    const encryptedSeed = encseed.slice(16);\n\n    // Decrypt the seed\n    const aesCbc = new CBC(key, iv);\n    const seed = pkcs7Strip(getBytes(aesCbc.decrypt(encryptedSeed)));\n\n    // This wallet format is weird... Convert the binary encoded hex to a string.\n    let seedHex = \"\";\n    for (let i = 0; i < seed.length; i++) {\n        seedHex += String.fromCharCode(seed[i]);\n    }\n\n    return { address, privateKey: id(seedHex) };\n}\n"], "mappings": "AAAA;;;AAIA,SAASA,GAAG,EAAEC,UAAU,QAAQ,QAAQ;AAExC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,EAAE,QAAQ,kBAAkB;AACrC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,mBAAmB;AAE5D,SAASC,WAAW,EAAEC,aAAa,EAAEC,OAAO,QAAQ,YAAY;AAYhE;;;AAGA,OAAM,SAAUC,eAAeA,CAACC,IAAY;EACxC,IAAI;IACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IAC7B,IAAIC,IAAI,CAACG,OAAO,EAAE;MAAE,OAAO,IAAI;;GAClC,CAAC,OAAOC,KAAK,EAAE;EAChB,OAAO,KAAK;AAChB;AAEA;AAEA;;;;;;;;;;AAUA,OAAM,SAAUC,oBAAoBA,CAACN,IAAY,EAAEO,SAA8B;EAC7E,MAAMN,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;EAC7B,MAAMQ,QAAQ,GAAGZ,WAAW,CAACW,SAAS,CAAC;EAEvC;EACA,MAAME,OAAO,GAAGlB,UAAU,CAACO,OAAO,CAACG,IAAI,EAAE,iBAAiB,CAAC,CAAC;EAE5D;EACA,MAAMG,OAAO,GAAGP,aAAa,CAACC,OAAO,CAACG,IAAI,EAAE,iBAAiB,CAAC,CAAC;EAC/DN,cAAc,CAACS,OAAO,IAAKA,OAAO,CAACM,MAAM,GAAG,EAAE,KAAM,CAAC,EAAE,iBAAiB,EAAE,MAAM,EAAEV,IAAI,CAAC;EAEvF,MAAMW,GAAG,GAAGjB,QAAQ,CAACF,MAAM,CAACgB,QAAQ,EAAEA,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EAEjF,MAAMC,EAAE,GAAGT,OAAO,CAACQ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;EAC/B,MAAME,aAAa,GAAGV,OAAO,CAACQ,KAAK,CAAC,EAAE,CAAC;EAEvC;EACA,MAAMG,MAAM,GAAG,IAAI1B,GAAG,CAACsB,GAAG,EAAEE,EAAE,CAAC;EAC/B,MAAMG,IAAI,GAAG1B,UAAU,CAACI,QAAQ,CAACqB,MAAM,CAACE,OAAO,CAACH,aAAa,CAAC,CAAC,CAAC;EAEhE;EACA,IAAII,OAAO,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACN,MAAM,EAAES,CAAC,EAAE,EAAE;IAClCD,OAAO,IAAIE,MAAM,CAACC,YAAY,CAACL,IAAI,CAACG,CAAC,CAAC,CAAC;;EAG3C,OAAO;IAAEV,OAAO;IAAEa,UAAU,EAAE7B,EAAE,CAACyB,OAAO;EAAC,CAAE;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}