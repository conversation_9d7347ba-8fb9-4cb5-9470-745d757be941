{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\STG\\\\extra\\\\CryptoQuest\\\\frontend\\\\src\\\\components\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'md',\n  text = 'Loading...',\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n  const textSizes = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex flex-col items-center justify-center ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: `${sizeClasses[size]} border-2 border-primary-200 border-t-primary-600 rounded-full`,\n      animate: {\n        rotate: 360\n      },\n      transition: {\n        duration: 1,\n        repeat: Infinity,\n        ease: 'linear'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this), text && /*#__PURE__*/_jsxDEV(motion.p, {\n      className: `${textSizes[size]} text-dark-300 mt-2`,\n      initial: {\n        opacity: 0.5\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        duration: 1,\n        repeat: Infinity,\n        repeatType: 'reverse'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "motion", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "text", "className", "sizeClasses", "sm", "md", "lg", "xl", "textSizes", "children", "div", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "p", "initial", "opacity", "repeatType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/src/components/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\n\nconst LoadingSpinner = ({ size = 'md', text = 'Loading...', className = '' }) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n\n  const textSizes = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <motion.div\n        className={`${sizeClasses[size]} border-2 border-primary-200 border-t-primary-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n      />\n      {text && (\n        <motion.p\n          className={`${textSizes[size]} text-dark-300 mt-2`}\n          initial={{ opacity: 0.5 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1, repeat: Infinity, repeatType: 'reverse' }}\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGA,CAAC;EAAEC,IAAI,GAAG,IAAI;EAAEC,IAAI,GAAG,YAAY;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAC/E,MAAMC,WAAW,GAAG;IAClBC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE;EACN,CAAC;EAED,MAAMC,SAAS,GAAG;IAChBJ,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,WAAW;IACfC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE;EACN,CAAC;EAED,oBACET,OAAA;IAAKI,SAAS,EAAE,6CAA6CA,SAAS,EAAG;IAAAO,QAAA,gBACvEX,OAAA,CAACF,MAAM,CAACc,GAAG;MACTR,SAAS,EAAE,GAAGC,WAAW,CAACH,IAAI,CAAC,gEAAiE;MAChGW,OAAO,EAAE;QAAEC,MAAM,EAAE;MAAI,CAAE;MACzBC,UAAU,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAEC,QAAQ;QAAEC,IAAI,EAAE;MAAS;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC,EACDpB,IAAI,iBACHH,OAAA,CAACF,MAAM,CAAC0B,CAAC;MACPpB,SAAS,EAAE,GAAGM,SAAS,CAACR,IAAI,CAAC,qBAAsB;MACnDuB,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC1Bb,OAAO,EAAE;QAAEa,OAAO,EAAE;MAAE,CAAE;MACxBX,UAAU,EAAE;QAAEC,QAAQ,EAAE,CAAC;QAAEC,MAAM,EAAEC,QAAQ;QAAES,UAAU,EAAE;MAAU,CAAE;MAAAhB,QAAA,EAEpER;IAAI;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACK,EAAA,GAlCI3B,cAAc;AAoCpB,eAAeA,cAAc;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}