{"ast": null, "code": "import { ripemd160 as noble_ripemd160 } from \"@noble/hashes/ripemd160\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\nlet locked = false;\nconst _ripemd160 = function (data) {\n  return noble_ripemd160(data);\n};\nlet __ripemd160 = _ripemd160;\n/**\n *  Compute the cryptographic RIPEMD-160 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    ripemd160(\"0x\")\n *    //_result:\n *\n *    ripemd160(\"0x1337\")\n *    //_result:\n *\n *    ripemd160(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n */\nexport function ripemd160(_data) {\n  const data = getBytes(_data, \"data\");\n  return hexlify(__ripemd160(data));\n}\nripemd160._ = _ripemd160;\nripemd160.lock = function () {\n  locked = true;\n};\nripemd160.register = function (func) {\n  if (locked) {\n    throw new TypeError(\"ripemd160 is locked\");\n  }\n  __ripemd160 = func;\n};\nObject.freeze(ripemd160);", "map": {"version": 3, "names": ["ripemd160", "noble_ripemd160", "getBytes", "hexlify", "locked", "_ripemd160", "data", "__ripemd160", "_data", "_", "lock", "register", "func", "TypeError", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\ripemd160.ts"], "sourcesContent": ["import { ripemd160 as noble_ripemd160 } from \"@noble/hashes/ripemd160\";\n\nimport { getBytes,  hexlify } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n\nlet locked = false;\n\nconst _ripemd160 = function(data: Uint8Array): Uint8Array {\n    return noble_ripemd160(data);\n}\n\nlet __ripemd160: (data: Uint8Array) => BytesLike = _ripemd160;\n\n/**\n *  Compute the cryptographic RIPEMD-160 hash of %%data%%.\n *\n *  @_docloc: api/crypto:Hash Functions\n *  @returns DataHexstring\n *\n *  @example:\n *    ripemd160(\"0x\")\n *    //_result:\n *\n *    ripemd160(\"0x1337\")\n *    //_result:\n *\n *    ripemd160(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n */\nexport function ripemd160(_data: BytesLike): string {\n    const data = getBytes(_data, \"data\");\n    return hexlify(__ripemd160(data));\n}\nripemd160._ = _ripemd160;\nripemd160.lock = function(): void { locked = true; }\nripemd160.register = function(func: (data: Uint8Array) => BytesLike) {\n    if (locked) { throw new TypeError(\"ripemd160 is locked\"); }\n    __ripemd160 = func;\n}\nObject.freeze(ripemd160);\n"], "mappings": "AAAA,SAASA,SAAS,IAAIC,eAAe,QAAQ,yBAAyB;AAEtE,SAASC,QAAQ,EAAGC,OAAO,QAAQ,mBAAmB;AAKtD,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,UAAU,GAAG,SAAAA,CAASC,IAAgB;EACxC,OAAOL,eAAe,CAACK,IAAI,CAAC;AAChC,CAAC;AAED,IAAIC,WAAW,GAAoCF,UAAU;AAE7D;;;;;;;;;;;;;;;;;AAiBA,OAAM,SAAUL,SAASA,CAACQ,KAAgB;EACtC,MAAMF,IAAI,GAAGJ,QAAQ,CAACM,KAAK,EAAE,MAAM,CAAC;EACpC,OAAOL,OAAO,CAACI,WAAW,CAACD,IAAI,CAAC,CAAC;AACrC;AACAN,SAAS,CAACS,CAAC,GAAGJ,UAAU;AACxBL,SAAS,CAACU,IAAI,GAAG;EAAmBN,MAAM,GAAG,IAAI;AAAE,CAAC;AACpDJ,SAAS,CAACW,QAAQ,GAAG,UAASC,IAAqC;EAC/D,IAAIR,MAAM,EAAE;IAAE,MAAM,IAAIS,SAAS,CAAC,qBAAqB,CAAC;;EACxDN,WAAW,GAAGK,IAAI;AACtB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACf,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}