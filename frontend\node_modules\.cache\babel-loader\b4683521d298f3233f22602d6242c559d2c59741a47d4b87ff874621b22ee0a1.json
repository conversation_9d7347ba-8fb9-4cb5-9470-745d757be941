{"ast": null, "code": "import { frame, cancelFrame } from '../frameloop/frame.mjs';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n  const start = performance.now();\n  const checkElapsed = ({\n    timestamp\n  }) => {\n    const elapsed = timestamp - start;\n    if (elapsed >= timeout) {\n      cancelFrame(checkElapsed);\n      callback(elapsed - timeout);\n    }\n  };\n  frame.read(checkElapsed, true);\n  return () => cancelFrame(checkElapsed);\n}\nexport { delay };", "map": {"version": 3, "names": ["frame", "cancelFrame", "delay", "callback", "timeout", "start", "performance", "now", "checkElapsed", "timestamp", "elapsed", "read"], "sources": ["C:/Users/<USER>/Desktop/STG/extra/CryptoQuest/frontend/node_modules/framer-motion/dist/es/utils/delay.mjs"], "sourcesContent": ["import { frame, cancelFrame } from '../frameloop/frame.mjs';\n\n/**\n * Timeout defined in ms\n */\nfunction delay(callback, timeout) {\n    const start = performance.now();\n    const checkElapsed = ({ timestamp }) => {\n        const elapsed = timestamp - start;\n        if (elapsed >= timeout) {\n            cancelFrame(checkElapsed);\n            callback(elapsed - timeout);\n        }\n    };\n    frame.read(checkElapsed, true);\n    return () => cancelFrame(checkElapsed);\n}\n\nexport { delay };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,QAAQ,wBAAwB;;AAE3D;AACA;AACA;AACA,SAASC,KAAKA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC9B,MAAMC,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;EAC/B,MAAMC,YAAY,GAAGA,CAAC;IAAEC;EAAU,CAAC,KAAK;IACpC,MAAMC,OAAO,GAAGD,SAAS,GAAGJ,KAAK;IACjC,IAAIK,OAAO,IAAIN,OAAO,EAAE;MACpBH,WAAW,CAACO,YAAY,CAAC;MACzBL,QAAQ,CAACO,OAAO,GAAGN,OAAO,CAAC;IAC/B;EACJ,CAAC;EACDJ,KAAK,CAACW,IAAI,CAACH,YAAY,EAAE,IAAI,CAAC;EAC9B,OAAO,MAAMP,WAAW,CAACO,YAAY,CAAC;AAC1C;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}