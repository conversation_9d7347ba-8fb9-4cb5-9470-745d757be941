{"ast": null, "code": "/**\n *  Cryptographic hashing functions\n *\n *  @_subsection: api/crypto:Hash Functions [about-crypto-hashing]\n */\nimport { keccak_256 } from \"@noble/hashes/sha3\";\nimport { getBytes, hexlify } from \"../utils/index.js\";\nlet locked = false;\nconst _keccak256 = function (data) {\n  return keccak_256(data);\n};\nlet __keccak256 = _keccak256;\n/**\n *  Compute the cryptographic KECCAK256 hash of %%data%%.\n *\n *  The %%data%% **must** be a data representation, to compute the\n *  hash of UTF-8 data use the [[id]] function.\n *\n *  @returns DataHexstring\n *  @example:\n *    keccak256(\"0x\")\n *    //_result:\n *\n *    keccak256(\"0x1337\")\n *    //_result:\n *\n *    keccak256(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n *    // Strings are assumed to be DataHexString, otherwise it will\n *    // throw. To hash UTF-8 data, see the note above.\n *    keccak256(\"Hello World\")\n *    //_error:\n */\nexport function keccak256(_data) {\n  const data = getBytes(_data, \"data\");\n  return hexlify(__keccak256(data));\n}\nkeccak256._ = _keccak256;\nkeccak256.lock = function () {\n  locked = true;\n};\nkeccak256.register = function (func) {\n  if (locked) {\n    throw new TypeError(\"keccak256 is locked\");\n  }\n  __keccak256 = func;\n};\nObject.freeze(keccak256);", "map": {"version": 3, "names": ["keccak_256", "getBytes", "hexlify", "locked", "_keccak256", "data", "__keccak256", "keccak256", "_data", "_", "lock", "register", "func", "TypeError", "Object", "freeze"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\crypto\\keccak.ts"], "sourcesContent": ["/**\n *  Cryptographic hashing functions\n *\n *  @_subsection: api/crypto:Hash Functions [about-crypto-hashing]\n */\n\nimport { keccak_256 } from \"@noble/hashes/sha3\";\n\nimport { getBytes, hexlify } from \"../utils/index.js\";\n\nimport type { BytesLike } from \"../utils/index.js\";\n\n\nlet locked = false;\n\nconst _keccak256 = function(data: Uint8Array): Uint8Array {\n    return keccak_256(data);\n}\n\nlet __keccak256: (data: Uint8Array) => BytesLike = _keccak256;\n\n/**\n *  Compute the cryptographic KECCAK256 hash of %%data%%.\n *\n *  The %%data%% **must** be a data representation, to compute the\n *  hash of UTF-8 data use the [[id]] function.\n *\n *  @returns DataHexstring\n *  @example:\n *    keccak256(\"0x\")\n *    //_result:\n *\n *    keccak256(\"0x1337\")\n *    //_result:\n *\n *    keccak256(new Uint8Array([ 0x13, 0x37 ]))\n *    //_result:\n *\n *    // Strings are assumed to be DataHexString, otherwise it will\n *    // throw. To hash UTF-8 data, see the note above.\n *    keccak256(\"Hello World\")\n *    //_error:\n */\nexport function keccak256(_data: BytesLike): string {\n    const data = getBytes(_data, \"data\");\n    return hexlify(__keccak256(data));\n}\nkeccak256._ = _keccak256;\nkeccak256.lock = function(): void { locked = true; }\nkeccak256.register = function(func: (data: Uint8Array) => BytesLike) {\n    if (locked) { throw new TypeError(\"keccak256 is locked\"); }\n    __keccak256 = func;\n}\nObject.freeze(keccak256);\n"], "mappings": "AAAA;;;;;AAMA,SAASA,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,QAAQ,EAAEC,OAAO,QAAQ,mBAAmB;AAKrD,IAAIC,MAAM,GAAG,KAAK;AAElB,MAAMC,UAAU,GAAG,SAAAA,CAASC,IAAgB;EACxC,OAAOL,UAAU,CAACK,IAAI,CAAC;AAC3B,CAAC;AAED,IAAIC,WAAW,GAAoCF,UAAU;AAE7D;;;;;;;;;;;;;;;;;;;;;;AAsBA,OAAM,SAAUG,SAASA,CAACC,KAAgB;EACtC,MAAMH,IAAI,GAAGJ,QAAQ,CAACO,KAAK,EAAE,MAAM,CAAC;EACpC,OAAON,OAAO,CAACI,WAAW,CAACD,IAAI,CAAC,CAAC;AACrC;AACAE,SAAS,CAACE,CAAC,GAAGL,UAAU;AACxBG,SAAS,CAACG,IAAI,GAAG;EAAmBP,MAAM,GAAG,IAAI;AAAE,CAAC;AACpDI,SAAS,CAACI,QAAQ,GAAG,UAASC,IAAqC;EAC/D,IAAIT,MAAM,EAAE;IAAE,MAAM,IAAIU,SAAS,CAAC,qBAAqB,CAAC;;EACxDP,WAAW,GAAGM,IAAI;AACtB,CAAC;AACDE,MAAM,CAACC,MAAM,CAACR,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}