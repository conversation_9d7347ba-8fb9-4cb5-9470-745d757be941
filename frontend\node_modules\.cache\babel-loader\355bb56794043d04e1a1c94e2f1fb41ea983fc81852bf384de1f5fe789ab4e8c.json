{"ast": null, "code": "/**\n *  Explain HD Wallets..\n *\n *  @_subsection: api/wallet:HD Wallets  [hd-wallets]\n */\nimport { computeHmac, randomBytes, ripemd160, Signing<PERSON>ey, sha256 } from \"../crypto/index.js\";\nimport { VoidSigner } from \"../providers/index.js\";\nimport { computeAddress } from \"../transaction/index.js\";\nimport { concat, dataSlice, decodeBase58, defineProperties, encodeBase58, getBytes, hexlify, isBytesLike, getNumber, toBeArray, toBigInt, toBeHex, assertPrivate, assert, assertArgument } from \"../utils/index.js\";\nimport { LangEn } from \"../wordlists/lang-en.js\";\nimport { BaseWallet } from \"./base-wallet.js\";\nimport { Mnemonic } from \"./mnemonic.js\";\nimport { encryptKeystoreJson, encryptKeystoreJsonSync } from \"./json-keystore.js\";\n/**\n *  The default derivation path for Ethereum HD Nodes. (i.e. ``\"m/44'/60'/0'/0/0\"``)\n */\nexport const defaultPath = \"m/44'/60'/0'/0/0\";\n// \"Bitcoin seed\"\nconst MasterSecret = new Uint8Array([66, 105, 116, 99, 111, 105, 110, 32, 115, 101, 101, 100]);\nconst HardenedBit = 0x80000000;\nconst N = BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\");\nconst Nibbles = \"0123456789abcdef\";\nfunction zpad(value, length) {\n  let result = \"\";\n  while (value) {\n    result = Nibbles[value % 16] + result;\n    value = Math.trunc(value / 16);\n  }\n  while (result.length < length * 2) {\n    result = \"0\" + result;\n  }\n  return \"0x\" + result;\n}\nfunction encodeBase58Check(_value) {\n  const value = getBytes(_value);\n  const check = dataSlice(sha256(sha256(value)), 0, 4);\n  const bytes = concat([value, check]);\n  return encodeBase58(bytes);\n}\nconst _guard = {};\nfunction ser_I(index, chainCode, publicKey, privateKey) {\n  const data = new Uint8Array(37);\n  if (index & HardenedBit) {\n    assert(privateKey != null, \"cannot derive child of neutered node\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"deriveChild\"\n    });\n    // Data = 0x00 || ser_256(k_par)\n    data.set(getBytes(privateKey), 1);\n  } else {\n    // Data = ser_p(point(k_par))\n    data.set(getBytes(publicKey));\n  }\n  // Data += ser_32(i)\n  for (let i = 24; i >= 0; i -= 8) {\n    data[33 + (i >> 3)] = index >> 24 - i & 0xff;\n  }\n  const I = getBytes(computeHmac(\"sha512\", chainCode, data));\n  return {\n    IL: I.slice(0, 32),\n    IR: I.slice(32)\n  };\n}\nfunction derivePath(node, path) {\n  const components = path.split(\"/\");\n  assertArgument(components.length > 0, \"invalid path\", \"path\", path);\n  if (components[0] === \"m\") {\n    assertArgument(node.depth === 0, `cannot derive root path (i.e. path starting with \"m/\") for a node at non-zero depth ${node.depth}`, \"path\", path);\n    components.shift();\n  }\n  let result = node;\n  for (let i = 0; i < components.length; i++) {\n    const component = components[i];\n    if (component.match(/^[0-9]+'$/)) {\n      const index = parseInt(component.substring(0, component.length - 1));\n      assertArgument(index < HardenedBit, \"invalid path index\", `path[${i}]`, component);\n      result = result.deriveChild(HardenedBit + index);\n    } else if (component.match(/^[0-9]+$/)) {\n      const index = parseInt(component);\n      assertArgument(index < HardenedBit, \"invalid path index\", `path[${i}]`, component);\n      result = result.deriveChild(index);\n    } else {\n      assertArgument(false, \"invalid path component\", `path[${i}]`, component);\n    }\n  }\n  return result;\n}\n/**\n *  An **HDNodeWallet** is a [[Signer]] backed by the private key derived\n *  from an HD Node using the [[link-bip-32]] stantard.\n *\n *  An HD Node forms a hierarchal structure with each HD Node having a\n *  private key and the ability to derive child HD Nodes, defined by\n *  a path indicating the index of each child.\n */\nexport class HDNodeWallet extends BaseWallet {\n  /**\n   *  The compressed public key.\n   */\n  publicKey;\n  /**\n   *  The fingerprint.\n   *\n   *  A fingerprint allows quick qay to detect parent and child nodes,\n   *  but developers should be prepared to deal with collisions as it\n   *  is only 4 bytes.\n   */\n  fingerprint;\n  /**\n   *  The parent fingerprint.\n   */\n  parentFingerprint;\n  /**\n   *  The mnemonic used to create this HD Node, if available.\n   *\n   *  Sources such as extended keys do not encode the mnemonic, in\n   *  which case this will be ``null``.\n   */\n  mnemonic;\n  /**\n   *  The chaincode, which is effectively a public key used\n   *  to derive children.\n   */\n  chainCode;\n  /**\n   *  The derivation path of this wallet.\n   *\n   *  Since extended keys do not provide full path details, this\n   *  may be ``null``, if instantiated from a source that does not\n   *  encode it.\n   */\n  path;\n  /**\n   *  The child index of this wallet. Values over ``2 *\\* 31`` indicate\n   *  the node is hardened.\n   */\n  index;\n  /**\n   *  The depth of this wallet, which is the number of components\n   *  in its path.\n   */\n  depth;\n  /**\n   *  @private\n   */\n  constructor(guard, signingKey, parentFingerprint, chainCode, path, index, depth, mnemonic, provider) {\n    super(signingKey, provider);\n    assertPrivate(guard, _guard, \"HDNodeWallet\");\n    defineProperties(this, {\n      publicKey: signingKey.compressedPublicKey\n    });\n    const fingerprint = dataSlice(ripemd160(sha256(this.publicKey)), 0, 4);\n    defineProperties(this, {\n      parentFingerprint,\n      fingerprint,\n      chainCode,\n      path,\n      index,\n      depth\n    });\n    defineProperties(this, {\n      mnemonic\n    });\n  }\n  connect(provider) {\n    return new HDNodeWallet(_guard, this.signingKey, this.parentFingerprint, this.chainCode, this.path, this.index, this.depth, this.mnemonic, provider);\n  }\n  #account() {\n    const account = {\n      address: this.address,\n      privateKey: this.privateKey\n    };\n    const m = this.mnemonic;\n    if (this.path && m && m.wordlist.locale === \"en\" && m.password === \"\") {\n      account.mnemonic = {\n        path: this.path,\n        locale: \"en\",\n        entropy: m.entropy\n      };\n    }\n    return account;\n  }\n  /**\n   *  Resolves to a [JSON Keystore Wallet](json-wallets) encrypted with\n   *  %%password%%.\n   *\n   *  If %%progressCallback%% is specified, it will receive periodic\n   *  updates as the encryption process progreses.\n   */\n  async encrypt(password, progressCallback) {\n    return await encryptKeystoreJson(this.#account(), password, {\n      progressCallback\n    });\n  }\n  /**\n   *  Returns a [JSON Keystore Wallet](json-wallets) encryped with\n   *  %%password%%.\n   *\n   *  It is preferred to use the [async version](encrypt) instead,\n   *  which allows a [[ProgressCallback]] to keep the user informed.\n   *\n   *  This method will block the event loop (freezing all UI) until\n   *  it is complete, which may be a non-trivial duration.\n   */\n  encryptSync(password) {\n    return encryptKeystoreJsonSync(this.#account(), password);\n  }\n  /**\n   *  The extended key.\n   *\n   *  This key will begin with the prefix ``xpriv`` and can be used to\n   *  reconstruct this HD Node to derive its children.\n   */\n  get extendedKey() {\n    // We only support the mainnet values for now, but if anyone needs\n    // testnet values, let me know. I believe current sentiment is that\n    // we should always use mainnet, and use BIP-44 to derive the network\n    //   - Mainnet: public=0x0488B21E, private=0x0488ADE4\n    //   - Testnet: public=0x043587CF, private=0x04358394\n    assert(this.depth < 256, \"Depth too deep\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"extendedKey\"\n    });\n    return encodeBase58Check(concat([\"0x0488ADE4\", zpad(this.depth, 1), this.parentFingerprint, zpad(this.index, 4), this.chainCode, concat([\"0x00\", this.privateKey])]));\n  }\n  /**\n   *  Returns true if this wallet has a path, providing a Type Guard\n   *  that the path is non-null.\n   */\n  hasPath() {\n    return this.path != null;\n  }\n  /**\n   *  Returns a neutered HD Node, which removes the private details\n   *  of an HD Node.\n   *\n   *  A neutered node has no private key, but can be used to derive\n   *  child addresses and other public data about the HD Node.\n   */\n  neuter() {\n    return new HDNodeVoidWallet(_guard, this.address, this.publicKey, this.parentFingerprint, this.chainCode, this.path, this.index, this.depth, this.provider);\n  }\n  /**\n   *  Return the child for %%index%%.\n   */\n  deriveChild(_index) {\n    const index = getNumber(_index, \"index\");\n    assertArgument(index <= 0xffffffff, \"invalid index\", \"index\", index);\n    // Base path\n    let path = this.path;\n    if (path) {\n      path += \"/\" + (index & ~HardenedBit);\n      if (index & HardenedBit) {\n        path += \"'\";\n      }\n    }\n    const {\n      IR,\n      IL\n    } = ser_I(index, this.chainCode, this.publicKey, this.privateKey);\n    const ki = new SigningKey(toBeHex((toBigInt(IL) + BigInt(this.privateKey)) % N, 32));\n    return new HDNodeWallet(_guard, ki, this.fingerprint, hexlify(IR), path, index, this.depth + 1, this.mnemonic, this.provider);\n  }\n  /**\n   *  Return the HDNode for %%path%% from this node.\n   */\n  derivePath(path) {\n    return derivePath(this, path);\n  }\n  static #fromSeed(_seed, mnemonic) {\n    assertArgument(isBytesLike(_seed), \"invalid seed\", \"seed\", \"[REDACTED]\");\n    const seed = getBytes(_seed, \"seed\");\n    assertArgument(seed.length >= 16 && seed.length <= 64, \"invalid seed\", \"seed\", \"[REDACTED]\");\n    const I = getBytes(computeHmac(\"sha512\", MasterSecret, seed));\n    const signingKey = new SigningKey(hexlify(I.slice(0, 32)));\n    return new HDNodeWallet(_guard, signingKey, \"0x00000000\", hexlify(I.slice(32)), \"m\", 0, 0, mnemonic, null);\n  }\n  /**\n   *  Creates a new HD Node from %%extendedKey%%.\n   *\n   *  If the %%extendedKey%% will either have a prefix or ``xpub`` or\n   *  ``xpriv``, returning a neutered HD Node ([[HDNodeVoidWallet]])\n   *  or full HD Node ([[HDNodeWallet) respectively.\n   */\n  static fromExtendedKey(extendedKey) {\n    const bytes = toBeArray(decodeBase58(extendedKey)); // @TODO: redact\n    assertArgument(bytes.length === 82 || encodeBase58Check(bytes.slice(0, 78)) === extendedKey, \"invalid extended key\", \"extendedKey\", \"[ REDACTED ]\");\n    const depth = bytes[4];\n    const parentFingerprint = hexlify(bytes.slice(5, 9));\n    const index = parseInt(hexlify(bytes.slice(9, 13)).substring(2), 16);\n    const chainCode = hexlify(bytes.slice(13, 45));\n    const key = bytes.slice(45, 78);\n    switch (hexlify(bytes.slice(0, 4))) {\n      // Public Key\n      case \"0x0488b21e\":\n      case \"0x043587cf\":\n        {\n          const publicKey = hexlify(key);\n          return new HDNodeVoidWallet(_guard, computeAddress(publicKey), publicKey, parentFingerprint, chainCode, null, index, depth, null);\n        }\n      // Private Key\n      case \"0x0488ade4\":\n      case \"0x04358394 \":\n        if (key[0] !== 0) {\n          break;\n        }\n        return new HDNodeWallet(_guard, new SigningKey(key.slice(1)), parentFingerprint, chainCode, null, index, depth, null, null);\n    }\n    assertArgument(false, \"invalid extended key prefix\", \"extendedKey\", \"[ REDACTED ]\");\n  }\n  /**\n   *  Creates a new random HDNode.\n   */\n  static createRandom(password, path, wordlist) {\n    if (password == null) {\n      password = \"\";\n    }\n    if (path == null) {\n      path = defaultPath;\n    }\n    if (wordlist == null) {\n      wordlist = LangEn.wordlist();\n    }\n    const mnemonic = Mnemonic.fromEntropy(randomBytes(16), password, wordlist);\n    return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n  }\n  /**\n   *  Create an HD Node from %%mnemonic%%.\n   */\n  static fromMnemonic(mnemonic, path) {\n    if (!path) {\n      path = defaultPath;\n    }\n    return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n  }\n  /**\n   *  Creates an HD Node from a mnemonic %%phrase%%.\n   */\n  static fromPhrase(phrase, password, path, wordlist) {\n    if (password == null) {\n      password = \"\";\n    }\n    if (path == null) {\n      path = defaultPath;\n    }\n    if (wordlist == null) {\n      wordlist = LangEn.wordlist();\n    }\n    const mnemonic = Mnemonic.fromPhrase(phrase, password, wordlist);\n    return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n  }\n  /**\n   *  Creates an HD Node from a %%seed%%.\n   */\n  static fromSeed(seed) {\n    return HDNodeWallet.#fromSeed(seed, null);\n  }\n}\n/**\n *  A **HDNodeVoidWallet** cannot sign, but provides access to\n *  the children nodes of a [[link-bip-32]] HD wallet addresses.\n *\n *  The can be created by using an extended ``xpub`` key to\n *  [[HDNodeWallet_fromExtendedKey]] or by\n *  [nuetering](HDNodeWallet-neuter) a [[HDNodeWallet]].\n */\nexport class HDNodeVoidWallet extends VoidSigner {\n  /**\n   *  The compressed public key.\n   */\n  publicKey;\n  /**\n   *  The fingerprint.\n   *\n   *  A fingerprint allows quick qay to detect parent and child nodes,\n   *  but developers should be prepared to deal with collisions as it\n   *  is only 4 bytes.\n   */\n  fingerprint;\n  /**\n   *  The parent node fingerprint.\n   */\n  parentFingerprint;\n  /**\n   *  The chaincode, which is effectively a public key used\n   *  to derive children.\n   */\n  chainCode;\n  /**\n   *  The derivation path of this wallet.\n   *\n   *  Since extended keys do not provider full path details, this\n   *  may be ``null``, if instantiated from a source that does not\n   *  enocde it.\n   */\n  path;\n  /**\n   *  The child index of this wallet. Values over ``2 *\\* 31`` indicate\n   *  the node is hardened.\n   */\n  index;\n  /**\n   *  The depth of this wallet, which is the number of components\n   *  in its path.\n   */\n  depth;\n  /**\n   *  @private\n   */\n  constructor(guard, address, publicKey, parentFingerprint, chainCode, path, index, depth, provider) {\n    super(address, provider);\n    assertPrivate(guard, _guard, \"HDNodeVoidWallet\");\n    defineProperties(this, {\n      publicKey\n    });\n    const fingerprint = dataSlice(ripemd160(sha256(publicKey)), 0, 4);\n    defineProperties(this, {\n      publicKey,\n      fingerprint,\n      parentFingerprint,\n      chainCode,\n      path,\n      index,\n      depth\n    });\n  }\n  connect(provider) {\n    return new HDNodeVoidWallet(_guard, this.address, this.publicKey, this.parentFingerprint, this.chainCode, this.path, this.index, this.depth, provider);\n  }\n  /**\n   *  The extended key.\n   *\n   *  This key will begin with the prefix ``xpub`` and can be used to\n   *  reconstruct this neutered key to derive its children addresses.\n   */\n  get extendedKey() {\n    // We only support the mainnet values for now, but if anyone needs\n    // testnet values, let me know. I believe current sentiment is that\n    // we should always use mainnet, and use BIP-44 to derive the network\n    //   - Mainnet: public=0x0488B21E, private=0x0488ADE4\n    //   - Testnet: public=0x043587CF, private=0x04358394\n    assert(this.depth < 256, \"Depth too deep\", \"UNSUPPORTED_OPERATION\", {\n      operation: \"extendedKey\"\n    });\n    return encodeBase58Check(concat([\"0x0488B21E\", zpad(this.depth, 1), this.parentFingerprint, zpad(this.index, 4), this.chainCode, this.publicKey]));\n  }\n  /**\n   *  Returns true if this wallet has a path, providing a Type Guard\n   *  that the path is non-null.\n   */\n  hasPath() {\n    return this.path != null;\n  }\n  /**\n   *  Return the child for %%index%%.\n   */\n  deriveChild(_index) {\n    const index = getNumber(_index, \"index\");\n    assertArgument(index <= 0xffffffff, \"invalid index\", \"index\", index);\n    // Base path\n    let path = this.path;\n    if (path) {\n      path += \"/\" + (index & ~HardenedBit);\n      if (index & HardenedBit) {\n        path += \"'\";\n      }\n    }\n    const {\n      IR,\n      IL\n    } = ser_I(index, this.chainCode, this.publicKey, null);\n    const Ki = SigningKey.addPoints(IL, this.publicKey, true);\n    const address = computeAddress(Ki);\n    return new HDNodeVoidWallet(_guard, address, Ki, this.fingerprint, hexlify(IR), path, index, this.depth + 1, this.provider);\n  }\n  /**\n   *  Return the signer for %%path%% from this node.\n   */\n  derivePath(path) {\n    return derivePath(this, path);\n  }\n}\n/*\nexport class HDNodeWalletManager {\n    #root: HDNodeWallet;\n\n    constructor(phrase: string, password?: null | string, path?: null | string, locale?: null | Wordlist) {\n        if (password == null) { password = \"\"; }\n        if (path == null) { path = \"m/44'/60'/0'/0\"; }\n        if (locale == null) { locale = LangEn.wordlist(); }\n        this.#root = HDNodeWallet.fromPhrase(phrase, password, path, locale);\n    }\n\n    getSigner(index?: number): HDNodeWallet {\n        return this.#root.deriveChild((index == null) ? 0: index);\n    }\n}\n*/\n/**\n *  Returns the [[link-bip-32]] path for the account at %%index%%.\n *\n *  This is the pattern used by wallets like Ledger.\n *\n *  There is also an [alternate pattern](getIndexedAccountPath) used by\n *  some software.\n */\nexport function getAccountPath(_index) {\n  const index = getNumber(_index, \"index\");\n  assertArgument(index >= 0 && index < HardenedBit, \"invalid account index\", \"index\", index);\n  return `m/44'/60'/${index}'/0/0`;\n}\n/**\n *  Returns the path using an alternative pattern for deriving accounts,\n *  at %%index%%.\n *\n *  This derivation path uses the //index// component rather than the\n *  //account// component to derive sequential accounts.\n *\n *  This is the pattern used by wallets like MetaMask.\n */\nexport function getIndexedAccountPath(_index) {\n  const index = getNumber(_index, \"index\");\n  assertArgument(index >= 0 && index < HardenedBit, \"invalid account index\", \"index\", index);\n  return `m/44'/60'/0'/0/${index}`;\n}", "map": {"version": 3, "names": ["computeHmac", "randomBytes", "ripemd160", "SigningKey", "sha256", "<PERSON>oid<PERSON><PERSON><PERSON>", "computeAddress", "concat", "dataSlice", "decodeBase58", "defineProperties", "encodeBase58", "getBytes", "hexlify", "isBytesLike", "getNumber", "toBeArray", "toBigInt", "toBeHex", "assertPrivate", "assert", "assertArgument", "LangEn", "BaseWallet", "Mnemonic", "encryptKeystoreJson", "encryptKeystoreJsonSync", "defaultPath", "<PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "HardenedBit", "N", "BigInt", "Nibbles", "zpad", "value", "length", "result", "Math", "trunc", "encodeBase58Check", "_value", "check", "bytes", "_guard", "ser_I", "index", "chainCode", "public<PERSON>ey", "privateKey", "data", "operation", "set", "i", "I", "IL", "slice", "IR", "derivePath", "node", "path", "components", "split", "depth", "shift", "component", "match", "parseInt", "substring", "<PERSON><PERSON><PERSON><PERSON>", "HDNodeWallet", "fingerprint", "parentFingerprint", "mnemonic", "constructor", "guard", "<PERSON><PERSON><PERSON>", "provider", "compressedPublicKey", "connect", "account", "#account", "address", "m", "wordlist", "locale", "password", "entropy", "encrypt", "progressCallback", "encryptSync", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "neuter", "HDNodeVoidWallet", "_index", "ki", "fromSeed", "#fromSeed", "_seed", "seed", "fromExtendedKey", "key", "createRandom", "fromEntropy", "computeSeed", "fromMnemonic", "fromPhrase", "phrase", "<PERSON>", "addPoints", "getAccountPath", "getIndexedAccountPath"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wallet\\hdwallet.ts"], "sourcesContent": ["/**\n *  Explain HD Wallets..\n *\n *  @_subsection: api/wallet:HD Wallets  [hd-wallets]\n */\nimport { computeHmac, randomBytes, ripemd160, Signing<PERSON><PERSON>, sha256 } from \"../crypto/index.js\";\nimport { VoidSigner } from \"../providers/index.js\";\nimport { computeAddress } from \"../transaction/index.js\";\nimport {\n    concat, dataSlice, decodeBase58, defineProperties, encodeBase58,\n    getBytes, hexlify, isBytesLike,\n    getNumber, toBeArray, toBigInt, toBeHex,\n    assertPrivate, assert, assertArgument\n} from \"../utils/index.js\";\nimport { LangEn } from \"../wordlists/lang-en.js\";\n\nimport { BaseWallet } from \"./base-wallet.js\";\nimport { Mnemonic } from \"./mnemonic.js\";\nimport {\n    encryptKeystoreJson, encryptKeystoreJsonSync,\n} from \"./json-keystore.js\";\n\nimport type { ProgressCallback } from \"../crypto/index.js\";\nimport type { Provider } from \"../providers/index.js\";\nimport type { BytesLike, Numeric } from \"../utils/index.js\";\nimport type { Wordlist } from \"../wordlists/index.js\";\n\nimport type { KeystoreAccount } from \"./json-keystore.js\";\n\n/**\n *  The default derivation path for Ethereum HD Nodes. (i.e. ``\"m/44'/60'/0'/0/0\"``)\n */\nexport const defaultPath: string = \"m/44'/60'/0'/0/0\";\n\n\n// \"Bitcoin seed\"\nconst MasterSecret = new Uint8Array([ 66, 105, 116, 99, 111, 105, 110, 32, 115, 101, 101, 100 ]);\n\nconst HardenedBit = 0x80000000;\n\nconst N = BigInt(\"0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141\");\n\nconst Nibbles = \"0123456789abcdef\";\nfunction zpad(value: number, length: number): string {\n    let result = \"\";\n    while (value) {\n        result = Nibbles[value % 16] + result;\n        value = Math.trunc(value / 16);\n    }\n    while (result.length < length * 2) { result = \"0\" + result; }\n    return \"0x\" + result;\n}\n\nfunction encodeBase58Check(_value: BytesLike): string {\n    const value = getBytes(_value);\n    const check = dataSlice(sha256(sha256(value)), 0, 4);\n    const bytes = concat([ value, check ]);\n    return encodeBase58(bytes);\n}\n\nconst _guard = { };\n\nfunction ser_I(index: number, chainCode: string, publicKey: string, privateKey: null | string): { IL: Uint8Array, IR: Uint8Array } {\n    const data = new Uint8Array(37);\n\n    if (index & HardenedBit) {\n        assert(privateKey != null, \"cannot derive child of neutered node\", \"UNSUPPORTED_OPERATION\", {\n            operation: \"deriveChild\"\n        });\n\n        // Data = 0x00 || ser_256(k_par)\n        data.set(getBytes(privateKey), 1);\n\n    } else {\n        // Data = ser_p(point(k_par))\n        data.set(getBytes(publicKey));\n    }\n\n    // Data += ser_32(i)\n    for (let i = 24; i >= 0; i -= 8) { data[33 + (i >> 3)] = ((index >> (24 - i)) & 0xff); }\n    const I = getBytes(computeHmac(\"sha512\", chainCode, data));\n\n    return { IL: I.slice(0, 32), IR: I.slice(32) };\n}\n\ntype HDNodeLike<T> = { depth: number, deriveChild: (i: number) => T };\nfunction derivePath<T extends HDNodeLike<T>>(node: T, path: string): T {\n    const components = path.split(\"/\");\n\n    assertArgument(components.length > 0, \"invalid path\", \"path\", path);\n\n    if (components[0] === \"m\") {\n        assertArgument(node.depth === 0, `cannot derive root path (i.e. path starting with \"m/\") for a node at non-zero depth ${ node.depth }`, \"path\", path);\n        components.shift();\n    }\n\n    let result: T = node;\n    for (let i = 0; i < components.length; i++) {\n        const component = components[i];\n\n        if (component.match(/^[0-9]+'$/)) {\n            const index = parseInt(component.substring(0, component.length - 1));\n            assertArgument(index < HardenedBit, \"invalid path index\", `path[${ i }]`, component);\n            result = result.deriveChild(HardenedBit + index);\n\n        } else if (component.match(/^[0-9]+$/)) {\n            const index = parseInt(component);\n            assertArgument(index < HardenedBit, \"invalid path index\", `path[${ i }]`, component);\n            result = result.deriveChild(index);\n\n        } else {\n            assertArgument(false, \"invalid path component\", `path[${ i }]`, component);\n        }\n    }\n\n    return result;\n}\n\n/**\n *  An **HDNodeWallet** is a [[Signer]] backed by the private key derived\n *  from an HD Node using the [[link-bip-32]] stantard.\n *\n *  An HD Node forms a hierarchal structure with each HD Node having a\n *  private key and the ability to derive child HD Nodes, defined by\n *  a path indicating the index of each child.\n */\nexport class HDNodeWallet extends BaseWallet {\n    /**\n     *  The compressed public key.\n     */\n    readonly publicKey!: string;\n\n    /**\n     *  The fingerprint.\n     *\n     *  A fingerprint allows quick qay to detect parent and child nodes,\n     *  but developers should be prepared to deal with collisions as it\n     *  is only 4 bytes.\n     */\n    readonly fingerprint!: string;\n\n    /**\n     *  The parent fingerprint.\n     */\n    readonly parentFingerprint!: string;\n\n    /**\n     *  The mnemonic used to create this HD Node, if available.\n     *\n     *  Sources such as extended keys do not encode the mnemonic, in\n     *  which case this will be ``null``.\n     */\n    readonly mnemonic!: null | Mnemonic;\n\n    /**\n     *  The chaincode, which is effectively a public key used\n     *  to derive children.\n     */\n    readonly chainCode!: string;\n\n    /**\n     *  The derivation path of this wallet.\n     *\n     *  Since extended keys do not provide full path details, this\n     *  may be ``null``, if instantiated from a source that does not\n     *  encode it.\n     */\n    readonly path!: null | string;\n\n    /**\n     *  The child index of this wallet. Values over ``2 *\\* 31`` indicate\n     *  the node is hardened.\n     */\n    readonly index!: number;\n\n    /**\n     *  The depth of this wallet, which is the number of components\n     *  in its path.\n     */\n    readonly depth!: number;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, signingKey: SigningKey, parentFingerprint: string, chainCode: string, path: null | string, index: number, depth: number, mnemonic: null | Mnemonic, provider: null | Provider) {\n        super(signingKey, provider);\n        assertPrivate(guard, _guard, \"HDNodeWallet\");\n\n        defineProperties<HDNodeWallet>(this, { publicKey: signingKey.compressedPublicKey });\n\n        const fingerprint = dataSlice(ripemd160(sha256(this.publicKey)), 0, 4);\n        defineProperties<HDNodeWallet>(this, {\n            parentFingerprint, fingerprint,\n            chainCode, path, index, depth\n        });\n\n        defineProperties<HDNodeWallet>(this, { mnemonic });\n    }\n\n    connect(provider: null | Provider): HDNodeWallet {\n        return new HDNodeWallet(_guard, this.signingKey, this.parentFingerprint,\n            this.chainCode, this.path, this.index, this.depth, this.mnemonic, provider);\n    }\n\n    #account(): KeystoreAccount {\n        const account: KeystoreAccount = { address: this.address, privateKey: this.privateKey };\n        const m = this.mnemonic;\n        if (this.path && m && m.wordlist.locale === \"en\" && m.password === \"\") {\n            account.mnemonic = {\n                path: this.path,\n                locale: \"en\",\n                entropy: m.entropy\n            };\n        }\n\n        return account;\n    }\n\n    /**\n     *  Resolves to a [JSON Keystore Wallet](json-wallets) encrypted with\n     *  %%password%%.\n     *\n     *  If %%progressCallback%% is specified, it will receive periodic\n     *  updates as the encryption process progreses.\n     */\n    async encrypt(password: Uint8Array | string, progressCallback?: ProgressCallback): Promise<string> {\n        return await encryptKeystoreJson(this.#account(), password, { progressCallback });\n    }\n\n    /**\n     *  Returns a [JSON Keystore Wallet](json-wallets) encryped with\n     *  %%password%%.\n     *\n     *  It is preferred to use the [async version](encrypt) instead,\n     *  which allows a [[ProgressCallback]] to keep the user informed.\n     *\n     *  This method will block the event loop (freezing all UI) until\n     *  it is complete, which may be a non-trivial duration.\n     */\n    encryptSync(password: Uint8Array | string): string {\n        return encryptKeystoreJsonSync(this.#account(), password);\n    }\n\n    /**\n     *  The extended key.\n     *\n     *  This key will begin with the prefix ``xpriv`` and can be used to\n     *  reconstruct this HD Node to derive its children.\n     */\n    get extendedKey(): string {\n        // We only support the mainnet values for now, but if anyone needs\n        // testnet values, let me know. I believe current sentiment is that\n        // we should always use mainnet, and use BIP-44 to derive the network\n        //   - Mainnet: public=0x0488B21E, private=0x0488ADE4\n        //   - Testnet: public=0x043587CF, private=0x04358394\n\n        assert(this.depth < 256, \"Depth too deep\", \"UNSUPPORTED_OPERATION\", { operation: \"extendedKey\" });\n\n        return encodeBase58Check(concat([\n            \"0x0488ADE4\", zpad(this.depth, 1), this.parentFingerprint,\n            zpad(this.index, 4), this.chainCode,\n            concat([ \"0x00\", this.privateKey ])\n        ]));\n    }\n\n    /**\n     *  Returns true if this wallet has a path, providing a Type Guard\n     *  that the path is non-null.\n     */\n    hasPath(): this is { path: string } { return (this.path != null); }\n\n    /**\n     *  Returns a neutered HD Node, which removes the private details\n     *  of an HD Node.\n     *\n     *  A neutered node has no private key, but can be used to derive\n     *  child addresses and other public data about the HD Node.\n     */\n    neuter(): HDNodeVoidWallet {\n        return new HDNodeVoidWallet(_guard, this.address, this.publicKey,\n            this.parentFingerprint, this.chainCode, this.path, this.index,\n            this.depth, this.provider);\n    }\n\n    /**\n     *  Return the child for %%index%%.\n     */\n    deriveChild(_index: Numeric): HDNodeWallet {\n        const index = getNumber(_index, \"index\");\n        assertArgument(index <= 0xffffffff, \"invalid index\", \"index\", index);\n\n        // Base path\n        let path = this.path;\n        if (path) {\n            path += \"/\" + (index & ~HardenedBit);\n            if (index & HardenedBit) { path += \"'\"; }\n        }\n\n        const { IR, IL } = ser_I(index, this.chainCode, this.publicKey, this.privateKey);\n        const ki = new SigningKey(toBeHex((toBigInt(IL) + BigInt(this.privateKey)) % N, 32));\n\n        return new HDNodeWallet(_guard, ki, this.fingerprint, hexlify(IR),\n            path, index, this.depth + 1, this.mnemonic, this.provider);\n\n    }\n\n    /**\n     *  Return the HDNode for %%path%% from this node.\n     */\n    derivePath(path: string): HDNodeWallet {\n        return derivePath<HDNodeWallet>(this, path);\n    }\n\n    static #fromSeed(_seed: BytesLike, mnemonic: null | Mnemonic): HDNodeWallet {\n        assertArgument(isBytesLike(_seed), \"invalid seed\", \"seed\", \"[REDACTED]\");\n\n        const seed = getBytes(_seed, \"seed\");\n        assertArgument(seed.length >= 16 && seed.length <= 64 , \"invalid seed\", \"seed\", \"[REDACTED]\");\n\n        const I = getBytes(computeHmac(\"sha512\", MasterSecret, seed));\n        const signingKey = new SigningKey(hexlify(I.slice(0, 32)));\n\n        return new HDNodeWallet(_guard, signingKey, \"0x00000000\", hexlify(I.slice(32)),\n            \"m\", 0, 0, mnemonic, null);\n    }\n\n    /**\n     *  Creates a new HD Node from %%extendedKey%%.\n     *\n     *  If the %%extendedKey%% will either have a prefix or ``xpub`` or\n     *  ``xpriv``, returning a neutered HD Node ([[HDNodeVoidWallet]])\n     *  or full HD Node ([[HDNodeWallet) respectively.\n     */\n    static fromExtendedKey(extendedKey: string): HDNodeWallet | HDNodeVoidWallet {\n        const bytes = toBeArray(decodeBase58(extendedKey)); // @TODO: redact\n\n        assertArgument(bytes.length === 82 || encodeBase58Check(bytes.slice(0, 78)) === extendedKey,\n            \"invalid extended key\", \"extendedKey\", \"[ REDACTED ]\");\n\n        const depth = bytes[4];\n        const parentFingerprint = hexlify(bytes.slice(5, 9));\n        const index = parseInt(hexlify(bytes.slice(9, 13)).substring(2), 16);\n        const chainCode = hexlify(bytes.slice(13, 45));\n        const key = bytes.slice(45, 78);\n\n        switch (hexlify(bytes.slice(0, 4))) {\n            // Public Key\n            case \"0x0488b21e\": case \"0x043587cf\": {\n                const publicKey = hexlify(key);\n                return new HDNodeVoidWallet(_guard, computeAddress(publicKey), publicKey,\n                    parentFingerprint, chainCode, null, index, depth, null);\n            }\n\n            // Private Key\n            case \"0x0488ade4\": case \"0x04358394 \":\n                if (key[0] !== 0) { break; }\n                return new HDNodeWallet(_guard, new SigningKey(key.slice(1)),\n                    parentFingerprint, chainCode, null, index, depth, null, null);\n        }\n\n\n        assertArgument(false, \"invalid extended key prefix\", \"extendedKey\", \"[ REDACTED ]\");\n    }\n\n    /**\n     *  Creates a new random HDNode.\n     */\n    static createRandom(password?: string, path?: string, wordlist?: Wordlist): HDNodeWallet {\n        if (password == null) { password = \"\"; }\n        if (path == null) { path = defaultPath; }\n        if (wordlist == null) { wordlist = LangEn.wordlist(); }\n        const mnemonic = Mnemonic.fromEntropy(randomBytes(16), password, wordlist)\n        return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n    }\n\n    /**\n     *  Create an HD Node from %%mnemonic%%.\n     */\n    static fromMnemonic(mnemonic: Mnemonic, path?: string): HDNodeWallet {\n        if (!path) { path = defaultPath; }\n        return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n    }\n\n    /**\n     *  Creates an HD Node from a mnemonic %%phrase%%.\n     */\n    static fromPhrase(phrase: string, password?: string, path?: string, wordlist?: Wordlist): HDNodeWallet {\n        if (password == null) { password = \"\"; }\n        if (path == null) { path = defaultPath; }\n        if (wordlist == null) { wordlist = LangEn.wordlist(); }\n        const mnemonic = Mnemonic.fromPhrase(phrase, password, wordlist)\n        return HDNodeWallet.#fromSeed(mnemonic.computeSeed(), mnemonic).derivePath(path);\n    }\n\n    /**\n     *  Creates an HD Node from a %%seed%%.\n     */\n    static fromSeed(seed: BytesLike): HDNodeWallet {\n        return HDNodeWallet.#fromSeed(seed, null);\n    }\n}\n\n/**\n *  A **HDNodeVoidWallet** cannot sign, but provides access to\n *  the children nodes of a [[link-bip-32]] HD wallet addresses.\n *\n *  The can be created by using an extended ``xpub`` key to\n *  [[HDNodeWallet_fromExtendedKey]] or by \n *  [nuetering](HDNodeWallet-neuter) a [[HDNodeWallet]].\n */\nexport class HDNodeVoidWallet extends VoidSigner {\n    /**\n     *  The compressed public key.\n     */\n    readonly publicKey!: string;\n\n    /**\n     *  The fingerprint.\n     *\n     *  A fingerprint allows quick qay to detect parent and child nodes,\n     *  but developers should be prepared to deal with collisions as it\n     *  is only 4 bytes.\n     */\n    readonly fingerprint!: string;\n\n    /**\n     *  The parent node fingerprint.\n     */\n    readonly parentFingerprint!: string;\n\n    /**\n     *  The chaincode, which is effectively a public key used\n     *  to derive children.\n     */\n    readonly chainCode!: string;\n\n    /**\n     *  The derivation path of this wallet.\n     *\n     *  Since extended keys do not provider full path details, this\n     *  may be ``null``, if instantiated from a source that does not\n     *  enocde it.\n     */\n    readonly path!: null | string;\n\n    /**\n     *  The child index of this wallet. Values over ``2 *\\* 31`` indicate\n     *  the node is hardened.\n     */\n    readonly index!: number;\n\n    /**\n     *  The depth of this wallet, which is the number of components\n     *  in its path.\n     */\n    readonly depth!: number;\n\n    /**\n     *  @private\n     */\n    constructor(guard: any, address: string, publicKey: string, parentFingerprint: string, chainCode: string, path: null | string, index: number, depth: number, provider: null | Provider) {\n        super(address, provider);\n        assertPrivate(guard, _guard, \"HDNodeVoidWallet\");\n\n        defineProperties<HDNodeVoidWallet>(this, { publicKey });\n\n        const fingerprint = dataSlice(ripemd160(sha256(publicKey)), 0, 4);\n        defineProperties<HDNodeVoidWallet>(this, {\n            publicKey, fingerprint, parentFingerprint, chainCode, path, index, depth\n        });\n    }\n\n    connect(provider: null | Provider): HDNodeVoidWallet {\n        return new HDNodeVoidWallet(_guard, this.address, this.publicKey,\n            this.parentFingerprint, this.chainCode, this.path, this.index, this.depth, provider);\n    }\n\n    /**\n     *  The extended key.\n     *\n     *  This key will begin with the prefix ``xpub`` and can be used to\n     *  reconstruct this neutered key to derive its children addresses.\n     */\n    get extendedKey(): string {\n        // We only support the mainnet values for now, but if anyone needs\n        // testnet values, let me know. I believe current sentiment is that\n        // we should always use mainnet, and use BIP-44 to derive the network\n        //   - Mainnet: public=0x0488B21E, private=0x0488ADE4\n        //   - Testnet: public=0x043587CF, private=0x04358394\n\n        assert(this.depth < 256, \"Depth too deep\", \"UNSUPPORTED_OPERATION\", { operation: \"extendedKey\" });\n\n        return encodeBase58Check(concat([\n            \"0x0488B21E\",\n            zpad(this.depth, 1),\n            this.parentFingerprint,\n            zpad(this.index, 4),\n            this.chainCode,\n            this.publicKey,\n        ]));\n    }\n\n    /**\n     *  Returns true if this wallet has a path, providing a Type Guard\n     *  that the path is non-null.\n     */\n    hasPath(): this is { path: string } { return (this.path != null); }\n\n    /**\n     *  Return the child for %%index%%.\n     */\n    deriveChild(_index: Numeric): HDNodeVoidWallet {\n        const index = getNumber(_index, \"index\");\n        assertArgument(index <= 0xffffffff, \"invalid index\", \"index\", index);\n\n        // Base path\n        let path = this.path;\n        if (path) {\n            path += \"/\" + (index & ~HardenedBit);\n            if (index & HardenedBit) { path += \"'\"; }\n        }\n\n        const { IR, IL } = ser_I(index, this.chainCode, this.publicKey, null);\n        const Ki = SigningKey.addPoints(IL, this.publicKey, true);\n\n        const address = computeAddress(Ki);\n\n        return new HDNodeVoidWallet(_guard, address, Ki, this.fingerprint, hexlify(IR),\n            path, index, this.depth + 1, this.provider);\n\n    }\n\n    /**\n     *  Return the signer for %%path%% from this node.\n     */\n    derivePath(path: string): HDNodeVoidWallet {\n        return derivePath<HDNodeVoidWallet>(this, path);\n    }\n}\n\n/*\nexport class HDNodeWalletManager {\n    #root: HDNodeWallet;\n\n    constructor(phrase: string, password?: null | string, path?: null | string, locale?: null | Wordlist) {\n        if (password == null) { password = \"\"; }\n        if (path == null) { path = \"m/44'/60'/0'/0\"; }\n        if (locale == null) { locale = LangEn.wordlist(); }\n        this.#root = HDNodeWallet.fromPhrase(phrase, password, path, locale);\n    }\n\n    getSigner(index?: number): HDNodeWallet {\n        return this.#root.deriveChild((index == null) ? 0: index);\n    }\n}\n*/\n\n/**\n *  Returns the [[link-bip-32]] path for the account at %%index%%.\n *\n *  This is the pattern used by wallets like Ledger.\n *\n *  There is also an [alternate pattern](getIndexedAccountPath) used by\n *  some software.\n */\nexport function getAccountPath(_index: Numeric): string {\n    const index = getNumber(_index, \"index\");\n    assertArgument(index >= 0 && index < HardenedBit, \"invalid account index\", \"index\", index);\n    return `m/44'/60'/${ index }'/0/0`;\n}\n\n/**\n *  Returns the path using an alternative pattern for deriving accounts,\n *  at %%index%%.\n *\n *  This derivation path uses the //index// component rather than the\n *  //account// component to derive sequential accounts.\n *\n *  This is the pattern used by wallets like MetaMask.\n */\nexport function getIndexedAccountPath(_index: Numeric): string {\n    const index = getNumber(_index, \"index\");\n    assertArgument(index >= 0 && index < HardenedBit, \"invalid account index\", \"index\", index);\n    return `m/44'/60'/0'/0/${ index}`;\n}\n\n"], "mappings": "AAAA;;;;;AAKA,SAASA,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,oBAAoB;AAC5F,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SACIC,MAAM,EAAEC,SAAS,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAC/DC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAC9BC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EACvCC,aAAa,EAAEC,MAAM,EAAEC,cAAc,QAClC,mBAAmB;AAC1B,SAASC,MAAM,QAAQ,yBAAyB;AAEhD,SAASC,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SACIC,mBAAmB,EAAEC,uBAAuB,QACzC,oBAAoB;AAS3B;;;AAGA,OAAO,MAAMC,WAAW,GAAW,kBAAkB;AAGrD;AACA,MAAMC,YAAY,GAAG,IAAIC,UAAU,CAAC,CAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE,CAAC;AAEhG,MAAMC,WAAW,GAAG,UAAU;AAE9B,MAAMC,CAAC,GAAGC,MAAM,CAAC,oEAAoE,CAAC;AAEtF,MAAMC,OAAO,GAAG,kBAAkB;AAClC,SAASC,IAAIA,CAACC,KAAa,EAAEC,MAAc;EACvC,IAAIC,MAAM,GAAG,EAAE;EACf,OAAOF,KAAK,EAAE;IACVE,MAAM,GAAGJ,OAAO,CAACE,KAAK,GAAG,EAAE,CAAC,GAAGE,MAAM;IACrCF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,GAAG,EAAE,CAAC;;EAElC,OAAOE,MAAM,CAACD,MAAM,GAAGA,MAAM,GAAG,CAAC,EAAE;IAAEC,MAAM,GAAG,GAAG,GAAGA,MAAM;;EAC1D,OAAO,IAAI,GAAGA,MAAM;AACxB;AAEA,SAASG,iBAAiBA,CAACC,MAAiB;EACxC,MAAMN,KAAK,GAAGvB,QAAQ,CAAC6B,MAAM,CAAC;EAC9B,MAAMC,KAAK,GAAGlC,SAAS,CAACJ,MAAM,CAACA,MAAM,CAAC+B,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACpD,MAAMQ,KAAK,GAAGpC,MAAM,CAAC,CAAE4B,KAAK,EAAEO,KAAK,CAAE,CAAC;EACtC,OAAO/B,YAAY,CAACgC,KAAK,CAAC;AAC9B;AAEA,MAAMC,MAAM,GAAG,EAAG;AAElB,SAASC,KAAKA,CAACC,KAAa,EAAEC,SAAiB,EAAEC,SAAiB,EAAEC,UAAyB;EACzF,MAAMC,IAAI,GAAG,IAAIrB,UAAU,CAAC,EAAE,CAAC;EAE/B,IAAIiB,KAAK,GAAGhB,WAAW,EAAE;IACrBV,MAAM,CAAC6B,UAAU,IAAI,IAAI,EAAE,sCAAsC,EAAE,uBAAuB,EAAE;MACxFE,SAAS,EAAE;KACd,CAAC;IAEF;IACAD,IAAI,CAACE,GAAG,CAACxC,QAAQ,CAACqC,UAAU,CAAC,EAAE,CAAC,CAAC;GAEpC,MAAM;IACH;IACAC,IAAI,CAACE,GAAG,CAACxC,QAAQ,CAACoC,SAAS,CAAC,CAAC;;EAGjC;EACA,KAAK,IAAIK,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAAEH,IAAI,CAAC,EAAE,IAAIG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAKP,KAAK,IAAK,EAAE,GAAGO,CAAE,GAAI,IAAK;;EACrF,MAAMC,CAAC,GAAG1C,QAAQ,CAACZ,WAAW,CAAC,QAAQ,EAAE+C,SAAS,EAAEG,IAAI,CAAC,CAAC;EAE1D,OAAO;IAAEK,EAAE,EAAED,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAEH,CAAC,CAACE,KAAK,CAAC,EAAE;EAAC,CAAE;AAClD;AAGA,SAASE,UAAUA,CAA0BC,IAAO,EAAEC,IAAY;EAC9D,MAAMC,UAAU,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAElCzC,cAAc,CAACwC,UAAU,CAACzB,MAAM,GAAG,CAAC,EAAE,cAAc,EAAE,MAAM,EAAEwB,IAAI,CAAC;EAEnE,IAAIC,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvBxC,cAAc,CAACsC,IAAI,CAACI,KAAK,KAAK,CAAC,EAAE,uFAAwFJ,IAAI,CAACI,KAAM,EAAE,EAAE,MAAM,EAAEH,IAAI,CAAC;IACrJC,UAAU,CAACG,KAAK,EAAE;;EAGtB,IAAI3B,MAAM,GAAMsB,IAAI;EACpB,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,UAAU,CAACzB,MAAM,EAAEiB,CAAC,EAAE,EAAE;IACxC,MAAMY,SAAS,GAAGJ,UAAU,CAACR,CAAC,CAAC;IAE/B,IAAIY,SAAS,CAACC,KAAK,CAAC,WAAW,CAAC,EAAE;MAC9B,MAAMpB,KAAK,GAAGqB,QAAQ,CAACF,SAAS,CAACG,SAAS,CAAC,CAAC,EAAEH,SAAS,CAAC7B,MAAM,GAAG,CAAC,CAAC,CAAC;MACpEf,cAAc,CAACyB,KAAK,GAAGhB,WAAW,EAAE,oBAAoB,EAAE,QAASuB,CAAE,GAAG,EAAEY,SAAS,CAAC;MACpF5B,MAAM,GAAGA,MAAM,CAACgC,WAAW,CAACvC,WAAW,GAAGgB,KAAK,CAAC;KAEnD,MAAM,IAAImB,SAAS,CAACC,KAAK,CAAC,UAAU,CAAC,EAAE;MACpC,MAAMpB,KAAK,GAAGqB,QAAQ,CAACF,SAAS,CAAC;MACjC5C,cAAc,CAACyB,KAAK,GAAGhB,WAAW,EAAE,oBAAoB,EAAE,QAASuB,CAAE,GAAG,EAAEY,SAAS,CAAC;MACpF5B,MAAM,GAAGA,MAAM,CAACgC,WAAW,CAACvB,KAAK,CAAC;KAErC,MAAM;MACHzB,cAAc,CAAC,KAAK,EAAE,wBAAwB,EAAE,QAASgC,CAAE,GAAG,EAAEY,SAAS,CAAC;;;EAIlF,OAAO5B,MAAM;AACjB;AAEA;;;;;;;;AAQA,OAAM,MAAOiC,YAAa,SAAQ/C,UAAU;EACxC;;;EAGSyB,SAAS;EAElB;;;;;;;EAOSuB,WAAW;EAEpB;;;EAGSC,iBAAiB;EAE1B;;;;;;EAMSC,QAAQ;EAEjB;;;;EAIS1B,SAAS;EAElB;;;;;;;EAOSa,IAAI;EAEb;;;;EAISd,KAAK;EAEd;;;;EAISiB,KAAK;EAEd;;;EAGAW,YAAYC,KAAU,EAAEC,UAAsB,EAAEJ,iBAAyB,EAAEzB,SAAiB,EAAEa,IAAmB,EAAEd,KAAa,EAAEiB,KAAa,EAAEU,QAAyB,EAAEI,QAAyB;IACjM,KAAK,CAACD,UAAU,EAAEC,QAAQ,CAAC;IAC3B1D,aAAa,CAACwD,KAAK,EAAE/B,MAAM,EAAE,cAAc,CAAC;IAE5ClC,gBAAgB,CAAe,IAAI,EAAE;MAAEsC,SAAS,EAAE4B,UAAU,CAACE;IAAmB,CAAE,CAAC;IAEnF,MAAMP,WAAW,GAAG/D,SAAS,CAACN,SAAS,CAACE,MAAM,CAAC,IAAI,CAAC4C,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACtEtC,gBAAgB,CAAe,IAAI,EAAE;MACjC8D,iBAAiB;MAAED,WAAW;MAC9BxB,SAAS;MAAEa,IAAI;MAAEd,KAAK;MAAEiB;KAC3B,CAAC;IAEFrD,gBAAgB,CAAe,IAAI,EAAE;MAAE+D;IAAQ,CAAE,CAAC;EACtD;EAEAM,OAAOA,CAACF,QAAyB;IAC7B,OAAO,IAAIP,YAAY,CAAC1B,MAAM,EAAE,IAAI,CAACgC,UAAU,EAAE,IAAI,CAACJ,iBAAiB,EACnE,IAAI,CAACzB,SAAS,EAAE,IAAI,CAACa,IAAI,EAAE,IAAI,CAACd,KAAK,EAAE,IAAI,CAACiB,KAAK,EAAE,IAAI,CAACU,QAAQ,EAAEI,QAAQ,CAAC;EACnF;EAEA,CAAAG,OAAQC,CAAA;IACJ,MAAMD,OAAO,GAAoB;MAAEE,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEjC,UAAU,EAAE,IAAI,CAACA;IAAU,CAAE;IACvF,MAAMkC,CAAC,GAAG,IAAI,CAACV,QAAQ;IACvB,IAAI,IAAI,CAACb,IAAI,IAAIuB,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAACC,MAAM,KAAK,IAAI,IAAIF,CAAC,CAACG,QAAQ,KAAK,EAAE,EAAE;MACnEN,OAAO,CAACP,QAAQ,GAAG;QACfb,IAAI,EAAE,IAAI,CAACA,IAAI;QACfyB,MAAM,EAAE,IAAI;QACZE,OAAO,EAAEJ,CAAC,CAACI;OACd;;IAGL,OAAOP,OAAO;EAClB;EAEA;;;;;;;EAOA,MAAMQ,OAAOA,CAACF,QAA6B,EAAEG,gBAAmC;IAC5E,OAAO,MAAMhE,mBAAmB,CAAC,IAAI,CAAC,CAAAuD,OAAQ,EAAE,EAAEM,QAAQ,EAAE;MAAEG;IAAgB,CAAE,CAAC;EACrF;EAEA;;;;;;;;;;EAUAC,WAAWA,CAACJ,QAA6B;IACrC,OAAO5D,uBAAuB,CAAC,IAAI,CAAC,CAAAsD,OAAQ,EAAE,EAAEM,QAAQ,CAAC;EAC7D;EAEA;;;;;;EAMA,IAAIK,WAAWA,CAAA;IACX;IACA;IACA;IACA;IACA;IAEAvE,MAAM,CAAC,IAAI,CAAC2C,KAAK,GAAG,GAAG,EAAE,gBAAgB,EAAE,uBAAuB,EAAE;MAAEZ,SAAS,EAAE;IAAa,CAAE,CAAC;IAEjG,OAAOX,iBAAiB,CAACjC,MAAM,CAAC,CAC5B,YAAY,EAAE2B,IAAI,CAAC,IAAI,CAAC6B,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAACS,iBAAiB,EACzDtC,IAAI,CAAC,IAAI,CAACY,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAACC,SAAS,EACnCxC,MAAM,CAAC,CAAE,MAAM,EAAE,IAAI,CAAC0C,UAAU,CAAE,CAAC,CACtC,CAAC,CAAC;EACP;EAEA;;;;EAIA2C,OAAOA,CAAA;IAA+B,OAAQ,IAAI,CAAChC,IAAI,IAAI,IAAI;EAAG;EAElE;;;;;;;EAOAiC,MAAMA,CAAA;IACF,OAAO,IAAIC,gBAAgB,CAAClD,MAAM,EAAE,IAAI,CAACsC,OAAO,EAAE,IAAI,CAAClC,SAAS,EAC5D,IAAI,CAACwB,iBAAiB,EAAE,IAAI,CAACzB,SAAS,EAAE,IAAI,CAACa,IAAI,EAAE,IAAI,CAACd,KAAK,EAC7D,IAAI,CAACiB,KAAK,EAAE,IAAI,CAACc,QAAQ,CAAC;EAClC;EAEA;;;EAGAR,WAAWA,CAAC0B,MAAe;IACvB,MAAMjD,KAAK,GAAG/B,SAAS,CAACgF,MAAM,EAAE,OAAO,CAAC;IACxC1E,cAAc,CAACyB,KAAK,IAAI,UAAU,EAAE,eAAe,EAAE,OAAO,EAAEA,KAAK,CAAC;IAEpE;IACA,IAAIc,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIA,IAAI,EAAE;MACNA,IAAI,IAAI,GAAG,IAAId,KAAK,GAAG,CAAChB,WAAW,CAAC;MACpC,IAAIgB,KAAK,GAAGhB,WAAW,EAAE;QAAE8B,IAAI,IAAI,GAAG;;;IAG1C,MAAM;MAAEH,EAAE;MAAEF;IAAE,CAAE,GAAGV,KAAK,CAACC,KAAK,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,UAAU,CAAC;IAChF,MAAM+C,EAAE,GAAG,IAAI7F,UAAU,CAACe,OAAO,CAAC,CAACD,QAAQ,CAACsC,EAAE,CAAC,GAAGvB,MAAM,CAAC,IAAI,CAACiB,UAAU,CAAC,IAAIlB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEpF,OAAO,IAAIuC,YAAY,CAAC1B,MAAM,EAAEoD,EAAE,EAAE,IAAI,CAACzB,WAAW,EAAE1D,OAAO,CAAC4C,EAAE,CAAC,EAC7DG,IAAI,EAAEd,KAAK,EAAE,IAAI,CAACiB,KAAK,GAAG,CAAC,EAAE,IAAI,CAACU,QAAQ,EAAE,IAAI,CAACI,QAAQ,CAAC;EAElE;EAEA;;;EAGAnB,UAAUA,CAACE,IAAY;IACnB,OAAOF,UAAU,CAAe,IAAI,EAAEE,IAAI,CAAC;EAC/C;EAEA,OAAO,CAAAqC,QAASC,CAACC,KAAgB,EAAE1B,QAAyB;IACxDpD,cAAc,CAACP,WAAW,CAACqF,KAAK,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC;IAExE,MAAMC,IAAI,GAAGxF,QAAQ,CAACuF,KAAK,EAAE,MAAM,CAAC;IACpC9E,cAAc,CAAC+E,IAAI,CAAChE,MAAM,IAAI,EAAE,IAAIgE,IAAI,CAAChE,MAAM,IAAI,EAAE,EAAG,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC;IAE7F,MAAMkB,CAAC,GAAG1C,QAAQ,CAACZ,WAAW,CAAC,QAAQ,EAAE4B,YAAY,EAAEwE,IAAI,CAAC,CAAC;IAC7D,MAAMxB,UAAU,GAAG,IAAIzE,UAAU,CAACU,OAAO,CAACyC,CAAC,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAE1D,OAAO,IAAIc,YAAY,CAAC1B,MAAM,EAAEgC,UAAU,EAAE,YAAY,EAAE/D,OAAO,CAACyC,CAAC,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC,EAC1E,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEiB,QAAQ,EAAE,IAAI,CAAC;EAClC;EAEA;;;;;;;EAOA,OAAO4B,eAAeA,CAACV,WAAmB;IACtC,MAAMhD,KAAK,GAAG3B,SAAS,CAACP,YAAY,CAACkF,WAAW,CAAC,CAAC,CAAC,CAAC;IAEpDtE,cAAc,CAACsB,KAAK,CAACP,MAAM,KAAK,EAAE,IAAII,iBAAiB,CAACG,KAAK,CAACa,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAKmC,WAAW,EACvF,sBAAsB,EAAE,aAAa,EAAE,cAAc,CAAC;IAE1D,MAAM5B,KAAK,GAAGpB,KAAK,CAAC,CAAC,CAAC;IACtB,MAAM6B,iBAAiB,GAAG3D,OAAO,CAAC8B,KAAK,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,MAAMV,KAAK,GAAGqB,QAAQ,CAACtD,OAAO,CAAC8B,KAAK,CAACa,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAACY,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpE,MAAMrB,SAAS,GAAGlC,OAAO,CAAC8B,KAAK,CAACa,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC9C,MAAM8C,GAAG,GAAG3D,KAAK,CAACa,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;IAE/B,QAAQ3C,OAAO,CAAC8B,KAAK,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9B;MACA,KAAK,YAAY;MAAE,KAAK,YAAY;QAAE;UAClC,MAAMR,SAAS,GAAGnC,OAAO,CAACyF,GAAG,CAAC;UAC9B,OAAO,IAAIR,gBAAgB,CAAClD,MAAM,EAAEtC,cAAc,CAAC0C,SAAS,CAAC,EAAEA,SAAS,EACpEwB,iBAAiB,EAAEzB,SAAS,EAAE,IAAI,EAAED,KAAK,EAAEiB,KAAK,EAAE,IAAI,CAAC;;MAG/D;MACA,KAAK,YAAY;MAAE,KAAK,aAAa;QACjC,IAAIuC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;UAAE;;QACpB,OAAO,IAAIhC,YAAY,CAAC1B,MAAM,EAAE,IAAIzC,UAAU,CAACmG,GAAG,CAAC9C,KAAK,CAAC,CAAC,CAAC,CAAC,EACxDgB,iBAAiB,EAAEzB,SAAS,EAAE,IAAI,EAAED,KAAK,EAAEiB,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;;IAIzE1C,cAAc,CAAC,KAAK,EAAE,6BAA6B,EAAE,aAAa,EAAE,cAAc,CAAC;EACvF;EAEA;;;EAGA,OAAOkF,YAAYA,CAACjB,QAAiB,EAAE1B,IAAa,EAAEwB,QAAmB;IACrE,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,EAAE;;IACrC,IAAI1B,IAAI,IAAI,IAAI,EAAE;MAAEA,IAAI,GAAGjC,WAAW;;IACtC,IAAIyD,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG9D,MAAM,CAAC8D,QAAQ,EAAE;;IACpD,MAAMX,QAAQ,GAAGjD,QAAQ,CAACgF,WAAW,CAACvG,WAAW,CAAC,EAAE,CAAC,EAAEqF,QAAQ,EAAEF,QAAQ,CAAC;IAC1E,OAAOd,YAAY,CAAC,CAAA2B,QAAS,CAACxB,QAAQ,CAACgC,WAAW,EAAE,EAAEhC,QAAQ,CAAC,CAACf,UAAU,CAACE,IAAI,CAAC;EACpF;EAEA;;;EAGA,OAAO8C,YAAYA,CAACjC,QAAkB,EAAEb,IAAa;IACjD,IAAI,CAACA,IAAI,EAAE;MAAEA,IAAI,GAAGjC,WAAW;;IAC/B,OAAO2C,YAAY,CAAC,CAAA2B,QAAS,CAACxB,QAAQ,CAACgC,WAAW,EAAE,EAAEhC,QAAQ,CAAC,CAACf,UAAU,CAACE,IAAI,CAAC;EACpF;EAEA;;;EAGA,OAAO+C,UAAUA,CAACC,MAAc,EAAEtB,QAAiB,EAAE1B,IAAa,EAAEwB,QAAmB;IACnF,IAAIE,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG,EAAE;;IACrC,IAAI1B,IAAI,IAAI,IAAI,EAAE;MAAEA,IAAI,GAAGjC,WAAW;;IACtC,IAAIyD,QAAQ,IAAI,IAAI,EAAE;MAAEA,QAAQ,GAAG9D,MAAM,CAAC8D,QAAQ,EAAE;;IACpD,MAAMX,QAAQ,GAAGjD,QAAQ,CAACmF,UAAU,CAACC,MAAM,EAAEtB,QAAQ,EAAEF,QAAQ,CAAC;IAChE,OAAOd,YAAY,CAAC,CAAA2B,QAAS,CAACxB,QAAQ,CAACgC,WAAW,EAAE,EAAEhC,QAAQ,CAAC,CAACf,UAAU,CAACE,IAAI,CAAC;EACpF;EAEA;;;EAGA,OAAOqC,QAAQA,CAACG,IAAe;IAC3B,OAAO9B,YAAY,CAAC,CAAA2B,QAAS,CAACG,IAAI,EAAE,IAAI,CAAC;EAC7C;;AAGJ;;;;;;;;AAQA,OAAM,MAAON,gBAAiB,SAAQzF,UAAU;EAC5C;;;EAGS2C,SAAS;EAElB;;;;;;;EAOSuB,WAAW;EAEpB;;;EAGSC,iBAAiB;EAE1B;;;;EAISzB,SAAS;EAElB;;;;;;;EAOSa,IAAI;EAEb;;;;EAISd,KAAK;EAEd;;;;EAISiB,KAAK;EAEd;;;EAGAW,YAAYC,KAAU,EAAEO,OAAe,EAAElC,SAAiB,EAAEwB,iBAAyB,EAAEzB,SAAiB,EAAEa,IAAmB,EAAEd,KAAa,EAAEiB,KAAa,EAAEc,QAAyB;IAClL,KAAK,CAACK,OAAO,EAAEL,QAAQ,CAAC;IACxB1D,aAAa,CAACwD,KAAK,EAAE/B,MAAM,EAAE,kBAAkB,CAAC;IAEhDlC,gBAAgB,CAAmB,IAAI,EAAE;MAAEsC;IAAS,CAAE,CAAC;IAEvD,MAAMuB,WAAW,GAAG/D,SAAS,CAACN,SAAS,CAACE,MAAM,CAAC4C,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACjEtC,gBAAgB,CAAmB,IAAI,EAAE;MACrCsC,SAAS;MAAEuB,WAAW;MAAEC,iBAAiB;MAAEzB,SAAS;MAAEa,IAAI;MAAEd,KAAK;MAAEiB;KACtE,CAAC;EACN;EAEAgB,OAAOA,CAACF,QAAyB;IAC7B,OAAO,IAAIiB,gBAAgB,CAAClD,MAAM,EAAE,IAAI,CAACsC,OAAO,EAAE,IAAI,CAAClC,SAAS,EAC5D,IAAI,CAACwB,iBAAiB,EAAE,IAAI,CAACzB,SAAS,EAAE,IAAI,CAACa,IAAI,EAAE,IAAI,CAACd,KAAK,EAAE,IAAI,CAACiB,KAAK,EAAEc,QAAQ,CAAC;EAC5F;EAEA;;;;;;EAMA,IAAIc,WAAWA,CAAA;IACX;IACA;IACA;IACA;IACA;IAEAvE,MAAM,CAAC,IAAI,CAAC2C,KAAK,GAAG,GAAG,EAAE,gBAAgB,EAAE,uBAAuB,EAAE;MAAEZ,SAAS,EAAE;IAAa,CAAE,CAAC;IAEjG,OAAOX,iBAAiB,CAACjC,MAAM,CAAC,CAC5B,YAAY,EACZ2B,IAAI,CAAC,IAAI,CAAC6B,KAAK,EAAE,CAAC,CAAC,EACnB,IAAI,CAACS,iBAAiB,EACtBtC,IAAI,CAAC,IAAI,CAACY,KAAK,EAAE,CAAC,CAAC,EACnB,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,SAAS,CACjB,CAAC,CAAC;EACP;EAEA;;;;EAIA4C,OAAOA,CAAA;IAA+B,OAAQ,IAAI,CAAChC,IAAI,IAAI,IAAI;EAAG;EAElE;;;EAGAS,WAAWA,CAAC0B,MAAe;IACvB,MAAMjD,KAAK,GAAG/B,SAAS,CAACgF,MAAM,EAAE,OAAO,CAAC;IACxC1E,cAAc,CAACyB,KAAK,IAAI,UAAU,EAAE,eAAe,EAAE,OAAO,EAAEA,KAAK,CAAC;IAEpE;IACA,IAAIc,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIA,IAAI,EAAE;MACNA,IAAI,IAAI,GAAG,IAAId,KAAK,GAAG,CAAChB,WAAW,CAAC;MACpC,IAAIgB,KAAK,GAAGhB,WAAW,EAAE;QAAE8B,IAAI,IAAI,GAAG;;;IAG1C,MAAM;MAAEH,EAAE;MAAEF;IAAE,CAAE,GAAGV,KAAK,CAACC,KAAK,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAAC;IACrE,MAAM6D,EAAE,GAAG1G,UAAU,CAAC2G,SAAS,CAACvD,EAAE,EAAE,IAAI,CAACP,SAAS,EAAE,IAAI,CAAC;IAEzD,MAAMkC,OAAO,GAAG5E,cAAc,CAACuG,EAAE,CAAC;IAElC,OAAO,IAAIf,gBAAgB,CAAClD,MAAM,EAAEsC,OAAO,EAAE2B,EAAE,EAAE,IAAI,CAACtC,WAAW,EAAE1D,OAAO,CAAC4C,EAAE,CAAC,EAC1EG,IAAI,EAAEd,KAAK,EAAE,IAAI,CAACiB,KAAK,GAAG,CAAC,EAAE,IAAI,CAACc,QAAQ,CAAC;EAEnD;EAEA;;;EAGAnB,UAAUA,CAACE,IAAY;IACnB,OAAOF,UAAU,CAAmB,IAAI,EAAEE,IAAI,CAAC;EACnD;;AAGJ;;;;;;;;;;;;;;;;AAiBA;;;;;;;;AAQA,OAAM,SAAUmD,cAAcA,CAAChB,MAAe;EAC1C,MAAMjD,KAAK,GAAG/B,SAAS,CAACgF,MAAM,EAAE,OAAO,CAAC;EACxC1E,cAAc,CAACyB,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGhB,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAEgB,KAAK,CAAC;EAC1F,OAAO,aAAcA,KAAM,OAAO;AACtC;AAEA;;;;;;;;;AASA,OAAM,SAAUkE,qBAAqBA,CAACjB,MAAe;EACjD,MAAMjD,KAAK,GAAG/B,SAAS,CAACgF,MAAM,EAAE,OAAO,CAAC;EACxC1E,cAAc,CAACyB,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGhB,WAAW,EAAE,uBAAuB,EAAE,OAAO,EAAEgB,KAAK,CAAC;EAC1F,OAAO,kBAAmBA,KAAK,EAAE;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}