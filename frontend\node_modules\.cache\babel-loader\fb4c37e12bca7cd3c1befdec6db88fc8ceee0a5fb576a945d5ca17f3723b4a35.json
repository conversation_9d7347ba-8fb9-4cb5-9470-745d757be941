{"ast": null, "code": "import { assertArgument } from \"../utils/index.js\";\nconst subsChrs = \" !#$%&'()*+,-./<=>?@[]^_`{|}~\";\nconst Word = /^[a-z]*$/i;\nfunction unfold(words, sep) {\n  let initial = 97;\n  return words.reduce((accum, word) => {\n    if (word === sep) {\n      initial++;\n    } else if (word.match(Word)) {\n      accum.push(String.fromCharCode(initial) + word);\n    } else {\n      initial = 97;\n      accum.push(word);\n    }\n    return accum;\n  }, []);\n}\n/**\n *  @_ignore\n */\nexport function decode(data, subs) {\n  // Replace all the substitutions with their expanded form\n  for (let i = subsChrs.length - 1; i >= 0; i--) {\n    data = data.split(subsChrs[i]).join(subs.substring(2 * i, 2 * i + 2));\n  }\n  // Get all tle clumps; each suffix, first-increment and second-increment\n  const clumps = [];\n  const leftover = data.replace(/(:|([0-9])|([A-Z][a-z]*))/g, (all, item, semi, word) => {\n    if (semi) {\n      for (let i = parseInt(semi); i >= 0; i--) {\n        clumps.push(\";\");\n      }\n    } else {\n      clumps.push(item.toLowerCase());\n    }\n    return \"\";\n  });\n  /* c8 ignore start */\n  if (leftover) {\n    throw new Error(`leftovers: ${JSON.stringify(leftover)}`);\n  }\n  /* c8 ignore stop */\n  return unfold(unfold(clumps, \";\"), \":\");\n}\n/**\n *  @_ignore\n */\nexport function decodeOwl(data) {\n  assertArgument(data[0] === \"0\", \"unsupported auwl data\", \"data\", data);\n  return decode(data.substring(1 + 2 * subsChrs.length), data.substring(1, 1 + 2 * subsChrs.length));\n}", "map": {"version": 3, "names": ["assertArgument", "subsChrs", "Word", "unfold", "words", "sep", "initial", "reduce", "accum", "word", "match", "push", "String", "fromCharCode", "decode", "data", "subs", "i", "length", "split", "join", "substring", "clumps", "leftover", "replace", "all", "item", "semi", "parseInt", "toLowerCase", "Error", "JSON", "stringify", "decodeOwl"], "sources": ["C:\\Users\\<USER>\\Desktop\\STG\\extra\\CryptoQuest\\frontend\\node_modules\\ethers\\src.ts\\wordlists\\decode-owl.ts"], "sourcesContent": ["import { assertArgument } from \"../utils/index.js\";\n\n\nconst subsChrs = \" !#$%&'()*+,-./<=>?@[]^_`{|}~\";\nconst Word = /^[a-z]*$/i;\n\nfunction unfold(words: Array<string>, sep: string): Array<string> {\n    let initial = 97;\n    return words.reduce((accum, word) => {\n        if (word === sep) {\n            initial++;\n        } else if (word.match(Word)) {\n            accum.push(String.fromCharCode(initial) + word);\n        } else {\n            initial = 97;\n            accum.push(word);\n        }\n        return accum;\n    }, <Array<string>>[]);\n}\n\n/**\n *  @_ignore\n */\nexport function decode(data: string, subs: string): Array<string> {\n\n    // Replace all the substitutions with their expanded form\n    for (let i = subsChrs.length - 1; i >= 0; i--) {\n        data = data.split(subsChrs[i]).join(subs.substring(2 * i, 2 * i + 2));\n    }\n\n    // Get all tle clumps; each suffix, first-increment and second-increment\n    const clumps: Array<string> = [ ];\n    const leftover = data.replace(/(:|([0-9])|([A-Z][a-z]*))/g, (all, item, semi, word) => {\n        if (semi) {\n            for (let i = parseInt(semi); i >= 0; i--) { clumps.push(\";\"); }\n        } else {\n            clumps.push(item.toLowerCase());\n        }\n        return \"\";\n    });\n    /* c8 ignore start */\n    if (leftover) { throw new Error(`leftovers: ${ JSON.stringify(leftover) }`); }\n    /* c8 ignore stop */\n\n    return unfold(unfold(clumps, \";\"), \":\");\n}\n\n/**\n *  @_ignore\n */\nexport function decodeOwl(data: string): Array<string> {\n    assertArgument(data[0] === \"0\", \"unsupported auwl data\", \"data\", data);\n\n    return decode(\n        data.substring(1 + 2 * subsChrs.length),\n        data.substring(1, 1 + 2 * subsChrs.length));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,mBAAmB;AAGlD,MAAMC,QAAQ,GAAG,+BAA+B;AAChD,MAAMC,IAAI,GAAG,WAAW;AAExB,SAASC,MAAMA,CAACC,KAAoB,EAAEC,GAAW;EAC7C,IAAIC,OAAO,GAAG,EAAE;EAChB,OAAOF,KAAK,CAACG,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAI;IAChC,IAAIA,IAAI,KAAKJ,GAAG,EAAE;MACdC,OAAO,EAAE;KACZ,MAAM,IAAIG,IAAI,CAACC,KAAK,CAACR,IAAI,CAAC,EAAE;MACzBM,KAAK,CAACG,IAAI,CAACC,MAAM,CAACC,YAAY,CAACP,OAAO,CAAC,GAAGG,IAAI,CAAC;KAClD,MAAM;MACHH,OAAO,GAAG,EAAE;MACZE,KAAK,CAACG,IAAI,CAACF,IAAI,CAAC;;IAEpB,OAAOD,KAAK;EAChB,CAAC,EAAiB,EAAE,CAAC;AACzB;AAEA;;;AAGA,OAAM,SAAUM,MAAMA,CAACC,IAAY,EAAEC,IAAY;EAE7C;EACA,KAAK,IAAIC,CAAC,GAAGhB,QAAQ,CAACiB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3CF,IAAI,GAAGA,IAAI,CAACI,KAAK,CAAClB,QAAQ,CAACgB,CAAC,CAAC,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACK,SAAS,CAAC,CAAC,GAAGJ,CAAC,EAAE,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,CAAC;;EAGzE;EACA,MAAMK,MAAM,GAAkB,EAAG;EACjC,MAAMC,QAAQ,GAAGR,IAAI,CAACS,OAAO,CAAC,4BAA4B,EAAE,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAElB,IAAI,KAAI;IAClF,IAAIkB,IAAI,EAAE;MACN,KAAK,IAAIV,CAAC,GAAGW,QAAQ,CAACD,IAAI,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAAEK,MAAM,CAACX,IAAI,CAAC,GAAG,CAAC;;KAC/D,MAAM;MACHW,MAAM,CAACX,IAAI,CAACe,IAAI,CAACG,WAAW,EAAE,CAAC;;IAEnC,OAAO,EAAE;EACb,CAAC,CAAC;EACF;EACA,IAAIN,QAAQ,EAAE;IAAE,MAAM,IAAIO,KAAK,CAAC,cAAeC,IAAI,CAACC,SAAS,CAACT,QAAQ,CAAE,EAAE,CAAC;;EAC3E;EAEA,OAAOpB,MAAM,CAACA,MAAM,CAACmB,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;AAC3C;AAEA;;;AAGA,OAAM,SAAUW,SAASA,CAAClB,IAAY;EAClCf,cAAc,CAACe,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,uBAAuB,EAAE,MAAM,EAAEA,IAAI,CAAC;EAEtE,OAAOD,MAAM,CACTC,IAAI,CAACM,SAAS,CAAC,CAAC,GAAG,CAAC,GAAGpB,QAAQ,CAACiB,MAAM,CAAC,EACvCH,IAAI,CAACM,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,GAAGpB,QAAQ,CAACiB,MAAM,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}